-- 重新添加软参配置

-- 删除软参
DELETE FROM `dfs_business_config` WHERE `full_path_code` in ('production.productMaterialsList.listConfig','production.productMaterialsList.listConfig.listDisplayFormat','production.workerMaterialsList.listConfig','production.workerMaterialsList.listConfig.listDisplayFormat');
DELETE FROM `dfs_business_config_value` WHERE `config_full_path_code` in ('production.productMaterialsList.listConfig','production.productMaterialsList.listConfig.listDisplayFormat','production.workerMaterialsList.listConfig','production.workerMaterialsList.listConfig.listDisplayFormat');

-- 生产订单用料清单列表展示形式配置
INSERT INTO `dfs_business_config` VALUES (null, 'listConfig', '生产订单用料清单列表配置', 'production.productMaterialsList.listConfig', 'production', NULL, 'yelinkoncall', 'yelinkoncall', now(), now(), '');
INSERT INTO `dfs_business_config` VALUES (null, 'listDisplayFormat', '列表展示形式', 'production.productMaterialsList.listConfig.listDisplayFormat', 'production.productMaterialsList.listConfig', NULL, 'yelinkoncall', 'yelinkoncall', now(), now(), '');
INSERT INTO `dfs_business_config_value` VALUES (null, 'materialOrOrder', '按单/物料', 'production.productMaterialsList.listConfig.listDisplayFormat.materialOrOrder', 'production.productMaterialsList.listConfig.listDisplayFormat', 'select', 'api', '/api/common/model/material/order/list', 'get', NULL, 'code,name', NULL, '\"material\"', NULL);
-- 生产工单用料清单列表展示形式配置
INSERT INTO `dfs_business_config` VALUES (null, 'listConfig', '生产工单用料清单列表配置', 'production.workerMaterialsList.listConfig', 'production', NULL, 'yelinkoncall', 'yelinkoncall', now(), now(), '');
INSERT INTO `dfs_business_config` VALUES (null, 'listDisplayFormat', '列表展示形式', 'production.workerMaterialsList.listConfig.listDisplayFormat', 'production.workerMaterialsList.listConfig', NULL, 'yelinkoncall', 'yelinkoncall', now(), now(), '');
INSERT INTO `dfs_business_config_value` VALUES (null, 'materialOrOrder', '按单/物料', 'production.workerMaterialsList.listConfig.listDisplayFormat.materialOrOrder', 'production.workerMaterialsList.listConfig.listDisplayFormat', 'select', 'api', '/api/common/model/material/order/list', 'get', NULL, 'code,name', NULL, '\"material\"', NULL);

-- 添加表字段默认值
call proc_modify_column(
        'dfs_form_field_module_config',
        'parent_module_code',
        'ALTER TABLE `dfs_form_field_module_config` CHANGE COLUMN `parent_module_code` `parent_module_code`  varchar(255) DEFAULT ''base'' COMMENT ''父模块编码''');
call proc_modify_column(
        'dfs_form_field_module_config',
        'parent_module_name',
        'ALTER TABLE `dfs_form_field_module_config` CHANGE COLUMN `parent_module_name` `parent_module_name`  varchar(255) DEFAULT ''基础字段'' COMMENT ''父模块名称''');