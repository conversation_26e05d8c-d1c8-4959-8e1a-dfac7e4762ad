-- 补全指标组缺少两个指标
INSERT INTO `dfs_target_dict`(`target_name`, `target_cnname`, `target_type`, `model_type`, `model_code`, `alarm_config`, `is_threshold_setting`, `unit`, `create_time`, `update_time`, `data_type`, `is_inner_target`, `default_frequency`, `default_frequency_unit`) VALUES ('qualityProductOrderDefectDaily', '生产订单不良每日质量', '1', 'baseTargetGroupObject', 'qualityTarget', NULL, NULL, '1', NULL, NULL, NULL, 1, 5, 'm');
INSERT INTO `dfs_target_dict`(`target_name`, `target_cnname`, `target_type`, `model_type`, `model_code`, `alarm_config`, `is_threshold_setting`, `unit`, `create_time`, `update_time`, `data_type`, `is_inner_target`, `default_frequency`, `default_frequency_unit`) VALUES ('qualityDefectDistributionDaily', '不良分布每日质量', '1', 'baseTargetGroupObject', 'qualityTarget', NULL, NULL, '1', NULL, NULL, NULL, 1, 5, 'm');
INSERT INTO `dfs_target_method`(`target_name`, `model_type`, `method_cname`, `method_name`, `has_frequency`, `multiple_choice`) VALUES ('qualityProductOrderDefectDaily', 'baseTargetGroupObject', '系统统计', 'qualityProductOrderDefectDaily', 1, NULL);
INSERT INTO `dfs_target_method`(`target_name`, `model_type`, `method_cname`, `method_name`, `has_frequency`, `multiple_choice`) VALUES ('qualityDefectDistributionDaily', 'baseTargetGroupObject', '系统统计', 'qualityDefectDistributionDaily', 1, NULL);

INSERT INTO `dfs_target_model_table_relate`(`target_name`, `model_id`, `table_schema`, `table_name`) VALUES ('qualityProductOrderDefectDaily', (SELECT id FROM `dfs_model` WHERE `code` = 'qualityTarget'), 'dfs_metrics', 'dfs_metrics_quality_product_order_defect_daily');
INSERT INTO `dfs_target_model_table_relate`(`target_name`, `model_id`, `table_schema`, `table_name`) VALUES ('qualityDefectDistributionDaily', (SELECT id FROM `dfs_model` WHERE `code` = 'qualityTarget'), 'dfs_metrics', 'dfs_metrics_quality_defect_distribution_daily');