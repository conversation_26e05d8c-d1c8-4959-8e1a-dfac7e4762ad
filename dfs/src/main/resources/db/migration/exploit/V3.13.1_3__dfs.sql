-- ================================================此脚本专为表单需求设定，如需写脚本，请移步到3.13.1.1=======================================================
-- ================================================此脚本专为表单需求设定，如需写脚本，请移步到3.13.1.1=======================================================
-- ================================================此脚本专为表单需求设定，如需写脚本，请移步到3.13.1.1=======================================================
-- ================================================此脚本专为表单需求设定，如需写脚本，请移步到3.13.1.1=======================================================
-- ================================================此脚本专为表单需求设定，如需写脚本，请移步到3.13.1.1=======================================================

drop table IF EXISTS dfs_form_field_module_config;

CREATE TABLE IF NOT EXISTS `dfs_form_field_module_config` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `full_path_code` varchar(255) NOT NULL COMMENT '配置的全路径编码',
    `field_name_full_path_code` varchar(255) DEFAULT NULL COMMENT '字段命名全路径编号',
    `module_code` varchar(255) NOT NULL COMMENT '模块编码',
    `module_name` varchar(255) DEFAULT '' COMMENT '模块名称',
    `have_save_button` tinyint(1) DEFAULT 1 COMMENT '是否有保存按钮',
    `have_add_button` tinyint(1) DEFAULT 0 COMMENT '是否有添加按钮',
    PRIMARY KEY (`id`) USING BTREE,
    KEY `full_path_code_module` (`full_path_code`, `module_code`) USING BTREE,
    KEY `field_name_full_path_code` (`field_name_full_path_code`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='表单字段模块配置表';

call proc_add_column(
        'dfs_form_field_config',
        'module_code',
        'ALTER TABLE `dfs_form_field_config` ADD COLUMN `module_code` varchar(100) NULL DEFAULT ''baseField'' COMMENT ''所属模块编码''');
call proc_add_column(
        'dfs_form_field_rule_config',
        'module_code',
        'ALTER TABLE `dfs_form_field_rule_config` ADD COLUMN `module_code` varchar(100) NULL DEFAULT ''baseField'' COMMENT ''所属模块编码''');
call proc_add_column(
        'dfs_form_field_rule_config',
        'is_dynamic_add',
        'ALTER TABLE `dfs_form_field_rule_config` ADD COLUMN `is_dynamic_add` tinyint(1) NULL DEFAULT 0 COMMENT ''是否是动态添加''');

call proc_modify_column(
        'dfs_form_field_config',
        'type_code',
        'ALTER TABLE `dfs_form_field_config` MODIFY COLUMN `type_code` varchar(100) NOT NULL COMMENT ''类型''');

DROP PROCEDURE if EXISTS proc_drop_column_index;
delimiter $$
CREATE PROCEDURE `proc_drop_column_index`(in var_table_name varchar(64), in var_index_name varchar(64))
top:begin

    -- 表不存在则直接返回
    set @p_tablenum='';
    set @sqlstr1=concat('select count(table_name)into @p_tablenum from information_schema.tables where table_schema=database() and table_name=\'',var_table_name,'\' limit 1;');
    prepare stmt1 from @sqlstr1;
    execute stmt1;
    deallocate prepare stmt1;
    if(@p_tablenum<1)then
        leave top;
    end if;

    -- 字段且索引存在才删除索引
    set @str=concat(' drop index `',var_index_name,'` on `',var_table_name,'`;');
    set @cnt = '';
    select count(*) into @cnt from information_schema.statistics where table_schema=database() and table_name=var_table_name and index_name=var_index_name;
    if (@cnt > 0) then
        PREPARE stmt FROM @str;
        EXECUTE stmt;
    end if;

end $$
delimiter ;

-- 重新添加索引
call proc_drop_column_index('dfs_form_field_config','unique_idx');
call proc_add_unique_index('dfs_form_field_config','full_path_code,field_code,type_code,module_code','unique_idx');

DROP PROCEDURE if EXISTS `proc_add_form_field_module`;
delimiter $$
CREATE PROCEDURE `proc_add_form_field_module`(in var_full_path_code varchar(512),in var_field_code varchar(255),in var_field_name varchar(255),in var_module_code varchar(255))
top:begin
    INSERT INTO `dfs_form_field_config`(`id`, `full_path_code`, `field_code`, `field_name`, `type_code`, `type_name`, `module_code`) VALUES (NULL, var_full_path_code, var_field_code, var_field_code, 'originalField', '字段编码', var_module_code);
    INSERT INTO `dfs_form_field_config`(`id`, `full_path_code`, `field_code`, `field_name`, `type_code`, `type_name`, `module_code`) VALUES (NULL, var_full_path_code, var_field_code, var_field_name, 'sysField', '系统字段', var_module_code);
    INSERT INTO `dfs_form_field_config`(`id`, `full_path_code`, `field_code`, `field_name`, `type_code`, `type_name`, `module_code`) VALUES (NULL, var_full_path_code, var_field_code, '', 'customField', '自定义字段', var_module_code);
end $$
delimiter ;

DROP PROCEDURE if EXISTS `proc_add_form_field`;
delimiter $$
CREATE PROCEDURE `proc_add_form_field`(in var_full_path_code varchar(512),in var_field_code varchar(255),in var_field_name varchar(255))
top:begin
    call proc_add_form_field_module(var_full_path_code, var_field_code, var_field_name, 'baseField');
end $$
delimiter ;

delete from dfs_form_field_config where module_code = 'batchField';
delete from dfs_form_field_rule_config where module_code = 'batchField';

insert into dfs_form_field_module_config(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `have_save_button`, `have_add_button`)
select DISTINCT full_path_code, field_name_full_path_code, 'baseField', '基础字段', 1, 0 from dfs_form_field_rule_config;

-- 物料扩展字段
INSERT INTO dfs_form_field_module_config (full_path_code, module_code, module_name, have_save_button, have_add_button) VALUES('productOrder.detail', 'materialExtendField', '物料扩展字段', 0, 0);
INSERT INTO dfs_form_field_module_config (full_path_code, module_code, module_name, have_save_button, have_add_button) VALUES('productOrder.editByCancel', 'materialExtendField', '物料扩展字段', 0, 0);
INSERT INTO dfs_form_field_module_config (full_path_code, module_code, module_name, have_save_button, have_add_button) VALUES('productOrder.editByClosed', 'materialExtendField', '物料扩展字段', 0, 0);
INSERT INTO dfs_form_field_module_config (full_path_code, module_code, module_name, have_save_button, have_add_button) VALUES('productOrder.editByCreate', 'materialExtendField', '物料扩展字段', 0, 0);
INSERT INTO dfs_form_field_module_config (full_path_code, module_code, module_name, have_save_button, have_add_button) VALUES('productOrder.editByFinish', 'materialExtendField', '物料扩展字段', 0, 0);
INSERT INTO dfs_form_field_module_config (full_path_code, module_code, module_name, have_save_button, have_add_button) VALUES('productOrder.editByRelease', 'materialExtendField', '物料扩展字段', 0, 0);
INSERT INTO dfs_form_field_module_config (full_path_code, module_code, module_name, have_save_button, have_add_button) VALUES('productOrder.list', 'materialExtendField', '物料扩展字段', 0, 0);
INSERT INTO dfs_form_field_module_config (full_path_code, module_code, module_name, have_save_button, have_add_button) VALUES('productOrderMaterialList.detail', 'materialExtendField', '物料扩展字段', 0, 0);
INSERT INTO dfs_form_field_module_config (full_path_code, module_code, module_name, have_save_button, have_add_button) VALUES('productOrderMaterialList.editByCancel', 'materialExtendField', '物料扩展字段', 0, 0);
INSERT INTO dfs_form_field_module_config (full_path_code, module_code, module_name, have_save_button, have_add_button) VALUES('productOrderMaterialList.editByClosed', 'materialExtendField', '物料扩展字段', 0, 0);
INSERT INTO dfs_form_field_module_config (full_path_code, module_code, module_name, have_save_button, have_add_button) VALUES('productOrderMaterialList.editByCreate', 'materialExtendField', '物料扩展字段', 0, 0);
INSERT INTO dfs_form_field_module_config (full_path_code, module_code, module_name, have_save_button, have_add_button) VALUES('productOrderMaterialList.editByFinish', 'materialExtendField', '物料扩展字段', 0, 0);
INSERT INTO dfs_form_field_module_config (full_path_code, module_code, module_name, have_save_button, have_add_button) VALUES('productOrderMaterialList.editByRelease', 'materialExtendField', '物料扩展字段', 0, 0);
INSERT INTO dfs_form_field_module_config (full_path_code, module_code, module_name, have_save_button, have_add_button) VALUES('productOrderMaterialList.list.material', 'materialExtendField', '物料扩展字段', 0, 0);
INSERT INTO dfs_form_field_module_config (full_path_code, module_code, module_name, have_save_button, have_add_button) VALUES('purchaseOrder.detail', 'materialExtendField', '物料扩展字段', 0, 0);
INSERT INTO dfs_form_field_module_config (full_path_code, module_code, module_name, have_save_button, have_add_button) VALUES('purchaseOrder.editByCancel', 'materialExtendField', '物料扩展字段', 0, 0);
INSERT INTO dfs_form_field_module_config (full_path_code, module_code, module_name, have_save_button, have_add_button) VALUES('purchaseOrder.editByClosed', 'materialExtendField', '物料扩展字段', 0, 0);
INSERT INTO dfs_form_field_module_config (full_path_code, module_code, module_name, have_save_button, have_add_button) VALUES('purchaseOrder.editByCreate', 'materialExtendField', '物料扩展字段', 0, 0);
INSERT INTO dfs_form_field_module_config (full_path_code, module_code, module_name, have_save_button, have_add_button) VALUES('purchaseOrder.editByFinish', 'materialExtendField', '物料扩展字段', 0, 0);
INSERT INTO dfs_form_field_module_config (full_path_code, module_code, module_name, have_save_button, have_add_button) VALUES('purchaseOrder.editByRelease', 'materialExtendField', '物料扩展字段', 0, 0);
INSERT INTO dfs_form_field_module_config (full_path_code, module_code, module_name, have_save_button, have_add_button) VALUES('purchaseOrder.list.material', 'materialExtendField', '物料扩展字段', 0, 0);
INSERT INTO dfs_form_field_module_config (full_path_code, module_code, module_name, have_save_button, have_add_button) VALUES('purchaseReceiptOrder.detail', 'materialExtendField', '物料扩展字段', 0, 0);
INSERT INTO dfs_form_field_module_config (full_path_code, module_code, module_name, have_save_button, have_add_button) VALUES('purchaseReceiptOrder.editByCancel', 'materialExtendField', '物料扩展字段', 0, 0);
INSERT INTO dfs_form_field_module_config (full_path_code, module_code, module_name, have_save_button, have_add_button) VALUES('purchaseReceiptOrder.editByClosed', 'materialExtendField', '物料扩展字段', 0, 0);
INSERT INTO dfs_form_field_module_config (full_path_code, module_code, module_name, have_save_button, have_add_button) VALUES('purchaseReceiptOrder.editByCreate', 'materialExtendField', '物料扩展字段', 0, 0);
INSERT INTO dfs_form_field_module_config (full_path_code, module_code, module_name, have_save_button, have_add_button) VALUES('purchaseReceiptOrder.editByFinish', 'materialExtendField', '物料扩展字段', 0, 0);
INSERT INTO dfs_form_field_module_config (full_path_code, module_code, module_name, have_save_button, have_add_button) VALUES('purchaseReceiptOrder.editByRelease', 'materialExtendField', '物料扩展字段', 0, 0);
INSERT INTO dfs_form_field_module_config (full_path_code, module_code, module_name, have_save_button, have_add_button) VALUES('purchaseReceiptOrder.list.material', 'materialExtendField', '物料扩展字段', 0, 0);
INSERT INTO dfs_form_field_module_config (full_path_code, module_code, module_name, have_save_button, have_add_button) VALUES('purchaseReceiptOrder.list.order', 'materialExtendField', '物料扩展字段', 0, 0);
INSERT INTO dfs_form_field_module_config (full_path_code, module_code, module_name, have_save_button, have_add_button) VALUES('purchaseRequestOrder.detail', 'materialExtendField', '物料扩展字段', 0, 0);
INSERT INTO dfs_form_field_module_config (full_path_code, module_code, module_name, have_save_button, have_add_button) VALUES('purchaseRequestOrder.editByCancel', 'materialExtendField', '物料扩展字段', 0, 0);
INSERT INTO dfs_form_field_module_config (full_path_code, module_code, module_name, have_save_button, have_add_button) VALUES('purchaseRequestOrder.editByClosed', 'materialExtendField', '物料扩展字段', 0, 0);
INSERT INTO dfs_form_field_module_config (full_path_code, module_code, module_name, have_save_button, have_add_button) VALUES('purchaseRequestOrder.editByCreate', 'materialExtendField', '物料扩展字段', 0, 0);
INSERT INTO dfs_form_field_module_config (full_path_code, module_code, module_name, have_save_button, have_add_button) VALUES('purchaseRequestOrder.editByFinish', 'materialExtendField', '物料扩展字段', 0, 0);
INSERT INTO dfs_form_field_module_config (full_path_code, module_code, module_name, have_save_button, have_add_button) VALUES('purchaseRequestOrder.editByRelease', 'materialExtendField', '物料扩展字段', 0, 0);
INSERT INTO dfs_form_field_module_config (full_path_code, module_code, module_name, have_save_button, have_add_button) VALUES('purchaseRequestOrder.list', 'materialExtendField', '物料扩展字段', 0, 0);
INSERT INTO dfs_form_field_module_config (full_path_code, module_code, module_name, have_save_button, have_add_button) VALUES('purchaseReturn.detail', 'materialExtendField', '物料扩展字段', 0, 0);
INSERT INTO dfs_form_field_module_config (full_path_code, module_code, module_name, have_save_button, have_add_button) VALUES('purchaseReturn.editByCancel', 'materialExtendField', '物料扩展字段', 0, 0);
INSERT INTO dfs_form_field_module_config (full_path_code, module_code, module_name, have_save_button, have_add_button) VALUES('purchaseReturn.editByClosed', 'materialExtendField', '物料扩展字段', 0, 0);
INSERT INTO dfs_form_field_module_config (full_path_code, module_code, module_name, have_save_button, have_add_button) VALUES('purchaseReturn.editByCreate', 'materialExtendField', '物料扩展字段', 0, 0);
INSERT INTO dfs_form_field_module_config (full_path_code, module_code, module_name, have_save_button, have_add_button) VALUES('purchaseReturn.editByFinish', 'materialExtendField', '物料扩展字段', 0, 0);
INSERT INTO dfs_form_field_module_config (full_path_code, module_code, module_name, have_save_button, have_add_button) VALUES('purchaseReturn.editByRelease', 'materialExtendField', '物料扩展字段', 0, 0);
INSERT INTO dfs_form_field_module_config (full_path_code, module_code, module_name, have_save_button, have_add_button) VALUES('purchaseReturn.list.material', 'materialExtendField', '物料扩展字段', 0, 0);
INSERT INTO dfs_form_field_module_config (full_path_code, module_code, module_name, have_save_button, have_add_button) VALUES('saleOrder.detail', 'materialExtendField', '物料扩展字段', 0, 0);
INSERT INTO dfs_form_field_module_config (full_path_code, module_code, module_name, have_save_button, have_add_button) VALUES('saleOrder.editByCancel', 'materialExtendField', '物料扩展字段', 0, 0);
INSERT INTO dfs_form_field_module_config (full_path_code, module_code, module_name, have_save_button, have_add_button) VALUES('saleOrder.editByClosed', 'materialExtendField', '物料扩展字段', 0, 0);
INSERT INTO dfs_form_field_module_config (full_path_code, module_code, module_name, have_save_button, have_add_button) VALUES('saleOrder.editByCreate', 'materialExtendField', '物料扩展字段', 0, 0);
INSERT INTO dfs_form_field_module_config (full_path_code, module_code, module_name, have_save_button, have_add_button) VALUES('saleOrder.editByFinish', 'materialExtendField', '物料扩展字段', 0, 0);
INSERT INTO dfs_form_field_module_config (full_path_code, module_code, module_name, have_save_button, have_add_button) VALUES('saleOrder.editByRelease', 'materialExtendField', '物料扩展字段', 0, 0);
INSERT INTO dfs_form_field_module_config (full_path_code, module_code, module_name, have_save_button, have_add_button) VALUES('saleOrder.list.material', 'materialExtendField', '物料扩展字段', 0, 0);
INSERT INTO dfs_form_field_module_config (full_path_code, module_code, module_name, have_save_button, have_add_button) VALUES('saleReturn.detail', 'materialExtendField', '物料扩展字段', 0, 0);
INSERT INTO dfs_form_field_module_config (full_path_code, module_code, module_name, have_save_button, have_add_button) VALUES('saleReturn.editByCancel', 'materialExtendField', '物料扩展字段', 0, 0);
INSERT INTO dfs_form_field_module_config (full_path_code, module_code, module_name, have_save_button, have_add_button) VALUES('saleReturn.editByClosed', 'materialExtendField', '物料扩展字段', 0, 0);
INSERT INTO dfs_form_field_module_config (full_path_code, module_code, module_name, have_save_button, have_add_button) VALUES('saleReturn.editByCreate', 'materialExtendField', '物料扩展字段', 0, 0);
INSERT INTO dfs_form_field_module_config (full_path_code, module_code, module_name, have_save_button, have_add_button) VALUES('saleReturn.editByFinish', 'materialExtendField', '物料扩展字段', 0, 0);
INSERT INTO dfs_form_field_module_config (full_path_code, module_code, module_name, have_save_button, have_add_button) VALUES('saleReturn.editByRelease', 'materialExtendField', '物料扩展字段', 0, 0);
INSERT INTO dfs_form_field_module_config (full_path_code, module_code, module_name, have_save_button, have_add_button) VALUES('saleReturn.list.material', 'materialExtendField', '物料扩展字段', 0, 0);
INSERT INTO dfs_form_field_module_config (full_path_code, module_code, module_name, have_save_button, have_add_button) VALUES('subcontractOrder.detail', 'materialExtendField', '物料扩展字段', 0, 0);
INSERT INTO dfs_form_field_module_config (full_path_code, module_code, module_name, have_save_button, have_add_button) VALUES('subcontractOrder.editByCancel', 'materialExtendField', '物料扩展字段', 0, 0);
INSERT INTO dfs_form_field_module_config (full_path_code, module_code, module_name, have_save_button, have_add_button) VALUES('subcontractOrder.editByClosed', 'materialExtendField', '物料扩展字段', 0, 0);
INSERT INTO dfs_form_field_module_config (full_path_code, module_code, module_name, have_save_button, have_add_button) VALUES('subcontractOrder.editByCreate', 'materialExtendField', '物料扩展字段', 0, 0);
INSERT INTO dfs_form_field_module_config (full_path_code, module_code, module_name, have_save_button, have_add_button) VALUES('subcontractOrder.editByFinish', 'materialExtendField', '物料扩展字段', 0, 0);
INSERT INTO dfs_form_field_module_config (full_path_code, module_code, module_name, have_save_button, have_add_button) VALUES('subcontractOrder.editByRelease', 'materialExtendField', '物料扩展字段', 0, 0);
INSERT INTO dfs_form_field_module_config (full_path_code, module_code, module_name, have_save_button, have_add_button) VALUES('subcontractOrder.list.material', 'materialExtendField', '物料扩展字段', 0, 0);
INSERT INTO dfs_form_field_module_config (full_path_code, module_code, module_name, have_save_button, have_add_button) VALUES('subcontractReceipt.editByCancel', 'materialExtendField', '物料扩展字段', 0, 0);
INSERT INTO dfs_form_field_module_config (full_path_code, module_code, module_name, have_save_button, have_add_button) VALUES('subcontractReceipt.editByClosed', 'materialExtendField', '物料扩展字段', 0, 0);
INSERT INTO dfs_form_field_module_config (full_path_code, module_code, module_name, have_save_button, have_add_button) VALUES('subcontractReceipt.editByCreate', 'materialExtendField', '物料扩展字段', 0, 0);
INSERT INTO dfs_form_field_module_config (full_path_code, module_code, module_name, have_save_button, have_add_button) VALUES('subcontractReceipt.editByFinish', 'materialExtendField', '物料扩展字段', 0, 0);
INSERT INTO dfs_form_field_module_config (full_path_code, module_code, module_name, have_save_button, have_add_button) VALUES('subcontractReceipt.editByRelease', 'materialExtendField', '物料扩展字段', 0, 0);
INSERT INTO dfs_form_field_module_config (full_path_code, module_code, module_name, have_save_button, have_add_button) VALUES('subcontractReceipt.list', 'materialExtendField', '物料扩展字段', 0, 0);
INSERT INTO dfs_form_field_module_config (full_path_code, module_code, module_name, have_save_button, have_add_button) VALUES('supplierMaterial.edit', 'materialExtendField', '物料扩展字段', 0, 0);
INSERT INTO dfs_form_field_module_config (full_path_code, module_code, module_name, have_save_button, have_add_button) VALUES('supplierMaterial.list', 'materialExtendField', '物料扩展字段', 0, 0);
INSERT INTO dfs_form_field_module_config (full_path_code, module_code, module_name, have_save_button, have_add_button) VALUES('workOrder.detail', 'materialExtendField', '物料扩展字段', 0, 0);
INSERT INTO dfs_form_field_module_config (full_path_code, module_code, module_name, have_save_button, have_add_button) VALUES('workOrder.editByCancel', 'materialExtendField', '物料扩展字段', 0, 0);
INSERT INTO dfs_form_field_module_config (full_path_code, module_code, module_name, have_save_button, have_add_button) VALUES('workOrder.editByClosed', 'materialExtendField', '物料扩展字段', 0, 0);
INSERT INTO dfs_form_field_module_config (full_path_code, module_code, module_name, have_save_button, have_add_button) VALUES('workOrder.editByCreate', 'materialExtendField', '物料扩展字段', 0, 0);
INSERT INTO dfs_form_field_module_config (full_path_code, module_code, module_name, have_save_button, have_add_button) VALUES('workOrder.editByFinish', 'materialExtendField', '物料扩展字段', 0, 0);
INSERT INTO dfs_form_field_module_config (full_path_code, module_code, module_name, have_save_button, have_add_button) VALUES('workOrder.editByHangUp', 'materialExtendField', '物料扩展字段', 0, 0);
INSERT INTO dfs_form_field_module_config (full_path_code, module_code, module_name, have_save_button, have_add_button) VALUES('workOrder.editByInvestment', 'materialExtendField', '物料扩展字段', 0, 0);
INSERT INTO dfs_form_field_module_config (full_path_code, module_code, module_name, have_save_button, have_add_button) VALUES('workOrder.editByRelease', 'materialExtendField', '物料扩展字段', 0, 0);
INSERT INTO dfs_form_field_module_config (full_path_code, module_code, module_name, have_save_button, have_add_button) VALUES('workOrder.list', 'materialExtendField', '物料扩展字段', 0, 0);
INSERT INTO dfs_form_field_module_config (full_path_code, module_code, module_name, have_save_button, have_add_button) VALUES('workOrderMaterialList.detail', 'materialExtendField', '物料扩展字段', 0, 0);
INSERT INTO dfs_form_field_module_config (full_path_code, module_code, module_name, have_save_button, have_add_button) VALUES('workOrderMaterialList.editByCancel', 'materialExtendField', '物料扩展字段', 0, 0);
INSERT INTO dfs_form_field_module_config (full_path_code, module_code, module_name, have_save_button, have_add_button) VALUES('workOrderMaterialList.editByClosed', 'materialExtendField', '物料扩展字段', 0, 0);
INSERT INTO dfs_form_field_module_config (full_path_code, module_code, module_name, have_save_button, have_add_button) VALUES('workOrderMaterialList.editByCreate', 'materialExtendField', '物料扩展字段', 0, 0);
INSERT INTO dfs_form_field_module_config (full_path_code, module_code, module_name, have_save_button, have_add_button) VALUES('workOrderMaterialList.editByFinish', 'materialExtendField', '物料扩展字段', 0, 0);
INSERT INTO dfs_form_field_module_config (full_path_code, module_code, module_name, have_save_button, have_add_button) VALUES('workOrderMaterialList.editByRelease', 'materialExtendField', '物料扩展字段', 0, 0);
INSERT INTO dfs_form_field_module_config (full_path_code, module_code, module_name, have_save_button, have_add_button) VALUES('workOrderMaterialList.list.material', 'materialExtendField', '物料扩展字段', 0, 0);
INSERT INTO dfs_form_field_module_config (full_path_code, module_code, module_name, have_save_button, have_add_button) VALUES('workOrderReport.list', 'materialExtendField', '物料扩展字段', 0, 0);
INSERT INTO dfs_form_field_module_config (full_path_code, module_code, module_name, have_save_button, have_add_button) VALUES('workScheduleProduction.scheduledList', 'materialExtendField', '物料扩展字段', 0, 0);
INSERT INTO dfs_form_field_module_config (full_path_code, module_code, module_name, have_save_button, have_add_button) VALUES('workScheduleProduction.toBeScheduledList', 'materialExtendField', '物料扩展字段', 0, 0);

-- 工单批次
INSERT INTO dfs_form_field_module_config (full_path_code, module_code, module_name, have_save_button, have_add_button) VALUES ('workOrder.editByCreate', 'batchField', '批次字段', 1, 1);
INSERT INTO dfs_form_field_module_config (full_path_code, module_code, module_name, have_save_button, have_add_button) VALUES ('workOrder.editByRelease', 'batchField', '批次字段', 1, 1);
INSERT INTO dfs_form_field_module_config (full_path_code, module_code, module_name, have_save_button, have_add_button) VALUES ('workOrder.editByInvestment', 'batchField', '批次字段', 1, 1);
INSERT INTO dfs_form_field_module_config (full_path_code, module_code, module_name, have_save_button, have_add_button) VALUES ('workOrder.editByHangUp', 'batchField', '批次字段', 1, 1);
INSERT INTO dfs_form_field_module_config (full_path_code, module_code, module_name, have_save_button, have_add_button) VALUES ('workOrder.editByFinish', 'batchField', '批次字段', 1, 1);
INSERT INTO dfs_form_field_module_config (full_path_code, module_code, module_name, have_save_button, have_add_button) VALUES ('workOrder.editByClosed', 'batchField', '批次字段', 1, 1);
INSERT INTO dfs_form_field_module_config (full_path_code, module_code, module_name, have_save_button, have_add_button) VALUES ('workOrder.editByCancel', 'batchField', '批次字段', 1, 1);
INSERT INTO dfs_form_field_module_config (full_path_code, module_code, module_name, have_save_button, have_add_button) VALUES ('workOrder.detail', 'batchField', '批次字段', 1, 1);
-- 订单批次
INSERT INTO dfs_form_field_module_config (full_path_code, module_code, module_name, have_save_button, have_add_button) VALUES ('productOrder.editByCreate', 'batchField', '批次字段', 1, 1);
INSERT INTO dfs_form_field_module_config (full_path_code, module_code, module_name, have_save_button, have_add_button) VALUES ('productOrder.editByRelease', 'batchField', '批次字段', 1, 1);
INSERT INTO dfs_form_field_module_config (full_path_code, module_code, module_name, have_save_button, have_add_button) VALUES ('productOrder.editByFinish', 'batchField', '批次字段', 1, 1);
INSERT INTO dfs_form_field_module_config (full_path_code, module_code, module_name, have_save_button, have_add_button) VALUES ('productOrder.editByClosed', 'batchField', '批次字段', 1, 1);
INSERT INTO dfs_form_field_module_config (full_path_code, module_code, module_name, have_save_button, have_add_button) VALUES ('productOrder.editByCancel', 'batchField', '批次字段', 1, 1);
INSERT INTO dfs_form_field_module_config (full_path_code, module_code, module_name, have_save_button, have_add_button) VALUES ('productOrder.detail', 'batchField', '批次字段', 1, 1);
-- 采购批次
INSERT INTO dfs_form_field_module_config (full_path_code, module_code, module_name, have_save_button, have_add_button) VALUES ('purchaseReceiptOrder.editByCreate', 'batchField', '批次字段', 1, 1);
INSERT INTO dfs_form_field_module_config (full_path_code, module_code, module_name, have_save_button, have_add_button) VALUES ('purchaseReceiptOrder.editByRelease', 'batchField', '批次字段', 1, 1);
INSERT INTO dfs_form_field_module_config (full_path_code, module_code, module_name, have_save_button, have_add_button) VALUES ('purchaseReceiptOrder.editByFinish', 'batchField', '批次字段', 1, 1);
INSERT INTO dfs_form_field_module_config (full_path_code, module_code, module_name, have_save_button, have_add_button) VALUES ('purchaseReceiptOrder.editByClosed', 'batchField', '批次字段', 1, 1);
INSERT INTO dfs_form_field_module_config (full_path_code, module_code, module_name, have_save_button, have_add_button) VALUES ('purchaseReceiptOrder.editByCancel', 'batchField', '批次字段', 1, 1);
INSERT INTO dfs_form_field_module_config (full_path_code, module_code, module_name, have_save_button, have_add_button) VALUES ('purchaseReceiptOrder.detail', 'batchField', '批次字段', 1, 1);

INSERT INTO dfs_form_field_module_config (full_path_code, module_code, module_name, have_save_button, have_add_button) VALUES ('purchaseReturn.editByCreate', 'batchField', '批次字段', 1, 1);
INSERT INTO dfs_form_field_module_config (full_path_code, module_code, module_name, have_save_button, have_add_button) VALUES ('purchaseReturn.editByRelease', 'batchField', '批次字段', 1, 1);
INSERT INTO dfs_form_field_module_config (full_path_code, module_code, module_name, have_save_button, have_add_button) VALUES ('purchaseReturn.editByFinish', 'batchField', '批次字段', 1, 1);
INSERT INTO dfs_form_field_module_config (full_path_code, module_code, module_name, have_save_button, have_add_button) VALUES ('purchaseReturn.editByClosed', 'batchField', '批次字段', 1, 1);
INSERT INTO dfs_form_field_module_config (full_path_code, module_code, module_name, have_save_button, have_add_button) VALUES ('purchaseReturn.editByCancel', 'batchField', '批次字段', 1, 1);
INSERT INTO dfs_form_field_module_config (full_path_code, module_code, module_name, have_save_button, have_add_button) VALUES ('purchaseReturn.detail', 'batchField', '批次字段', 1, 1);

update dfs_form_field_module_config m set field_name_full_path_code = (select field_name_full_path_code from dfs_form_field_rule_config where full_path_code = m.full_path_code limit 1);


-- 批次字段
call proc_add_form_field_module("workOrder.edit", "barCode", "批次号", "batchField");
call proc_add_form_field_module("workOrder.edit", "stateName", "状态", "batchField");
call proc_add_form_field_module("workOrder.edit", "count", "计划数量", "batchField");
call proc_add_form_field_module("workOrder.edit", "finishCount", "生产数量", "batchField");
call proc_add_form_field_module("workOrder.edit", "unqualified", "不良数量", "batchField");
call proc_add_form_field_module("workOrder.edit", "receiptQuantity", "入库数量", "batchField");
call proc_add_form_field_module("workOrder.edit", "createByNickName", "创建人", "batchField");
call proc_add_form_field_module("workOrder.edit", "createTime", "创建时间", "batchField");
call proc_add_form_field_module("workOrder.edit", "remarks", "备注", "batchField");

call proc_add_form_field_module("workOrder.detail", "barCode", "批次号", "batchField");
call proc_add_form_field_module("workOrder.detail", "stateName", "状态", "batchField");
call proc_add_form_field_module("workOrder.detail", "count", "计划数量", "batchField");
call proc_add_form_field_module("workOrder.detail", "finishCount", "生产数量", "batchField");
call proc_add_form_field_module("workOrder.detail", "unqualified", "不良数量", "batchField");
call proc_add_form_field_module("workOrder.detail", "receiptQuantity", "入库数量", "batchField");
call proc_add_form_field_module("workOrder.detail", "createByNickName", "创建人", "batchField");
call proc_add_form_field_module("workOrder.detail", "createTime", "创建时间", "batchField");
call proc_add_form_field_module("workOrder.detail", "remarks", "备注", "batchField");

INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/order-model/production-workorder', 'workOrder.editByCreate', 'workOrder.edit', 'barCode', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/order-model/production-workorder', 'workOrder.editByCreate', 'workOrder.edit', 'stateName', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/order-model/production-workorder', 'workOrder.editByCreate', 'workOrder.edit', 'count', 1, 1, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/order-model/production-workorder', 'workOrder.editByCreate', 'workOrder.edit', 'finishCount', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/order-model/production-workorder', 'workOrder.editByCreate', 'workOrder.edit', 'unqualified', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/order-model/production-workorder', 'workOrder.editByCreate', 'workOrder.edit', 'receiptQuantity', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/order-model/production-workorder', 'workOrder.editByCreate', 'workOrder.edit', 'createByNickName', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/order-model/production-workorder', 'workOrder.editByCreate', 'workOrder.edit', 'createTime', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/order-model/production-workorder', 'workOrder.editByCreate', 'workOrder.edit', 'remarks', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);

INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/order-model/production-workorder', 'workOrder.editByRelease', 'workOrder.edit', 'barCode', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/order-model/production-workorder', 'workOrder.editByRelease', 'workOrder.edit', 'stateName', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/order-model/production-workorder', 'workOrder.editByRelease', 'workOrder.edit', 'count', 1, 1, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/order-model/production-workorder', 'workOrder.editByRelease', 'workOrder.edit', 'finishCount', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/order-model/production-workorder', 'workOrder.editByRelease', 'workOrder.edit', 'unqualified', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/order-model/production-workorder', 'workOrder.editByRelease', 'workOrder.edit', 'receiptQuantity', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/order-model/production-workorder', 'workOrder.editByRelease', 'workOrder.edit', 'createByNickName', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/order-model/production-workorder', 'workOrder.editByRelease', 'workOrder.edit', 'createTime', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/order-model/production-workorder', 'workOrder.editByRelease', 'workOrder.edit', 'remarks', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);

INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/order-model/production-workorder', 'workOrder.editByInvestment', 'workOrder.edit', 'barCode', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/order-model/production-workorder', 'workOrder.editByInvestment', 'workOrder.edit', 'stateName', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/order-model/production-workorder', 'workOrder.editByInvestment', 'workOrder.edit', 'count', 1, 1, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/order-model/production-workorder', 'workOrder.editByInvestment', 'workOrder.edit', 'finishCount', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/order-model/production-workorder', 'workOrder.editByInvestment', 'workOrder.edit', 'unqualified', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/order-model/production-workorder', 'workOrder.editByInvestment', 'workOrder.edit', 'receiptQuantity', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/order-model/production-workorder', 'workOrder.editByInvestment', 'workOrder.edit', 'createByNickName', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/order-model/production-workorder', 'workOrder.editByInvestment', 'workOrder.edit', 'createTime', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/order-model/production-workorder', 'workOrder.editByInvestment', 'workOrder.edit', 'remarks', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);

INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/order-model/production-workorder', 'workOrder.editByHangUp', 'workOrder.edit', 'barCode', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/order-model/production-workorder', 'workOrder.editByHangUp', 'workOrder.edit', 'stateName', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/order-model/production-workorder', 'workOrder.editByHangUp', 'workOrder.edit', 'count', 1, 1, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/order-model/production-workorder', 'workOrder.editByHangUp', 'workOrder.edit', 'finishCount', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/order-model/production-workorder', 'workOrder.editByHangUp', 'workOrder.edit', 'unqualified', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/order-model/production-workorder', 'workOrder.editByHangUp', 'workOrder.edit', 'receiptQuantity', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/order-model/production-workorder', 'workOrder.editByHangUp', 'workOrder.edit', 'createByNickName', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/order-model/production-workorder', 'workOrder.editByHangUp', 'workOrder.edit', 'createTime', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/order-model/production-workorder', 'workOrder.editByHangUp', 'workOrder.edit', 'remarks', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);

INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/order-model/production-workorder', 'workOrder.editByFinish', 'workOrder.edit', 'barCode', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/order-model/production-workorder', 'workOrder.editByFinish', 'workOrder.edit', 'stateName', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/order-model/production-workorder', 'workOrder.editByFinish', 'workOrder.edit', 'count', 1, 1, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/order-model/production-workorder', 'workOrder.editByFinish', 'workOrder.edit', 'finishCount', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/order-model/production-workorder', 'workOrder.editByFinish', 'workOrder.edit', 'unqualified', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/order-model/production-workorder', 'workOrder.editByFinish', 'workOrder.edit', 'receiptQuantity', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/order-model/production-workorder', 'workOrder.editByFinish', 'workOrder.edit', 'createByNickName', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/order-model/production-workorder', 'workOrder.editByFinish', 'workOrder.edit', 'createTime', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/order-model/production-workorder', 'workOrder.editByFinish', 'workOrder.edit', 'remarks', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);

INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/order-model/production-workorder', 'workOrder.editByClosed', 'workOrder.edit', 'barCode', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/order-model/production-workorder', 'workOrder.editByClosed', 'workOrder.edit', 'stateName', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/order-model/production-workorder', 'workOrder.editByClosed', 'workOrder.edit', 'count', 1, 1, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/order-model/production-workorder', 'workOrder.editByClosed', 'workOrder.edit', 'finishCount', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/order-model/production-workorder', 'workOrder.editByClosed', 'workOrder.edit', 'unqualified', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/order-model/production-workorder', 'workOrder.editByClosed', 'workOrder.edit', 'receiptQuantity', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/order-model/production-workorder', 'workOrder.editByClosed', 'workOrder.edit', 'createByNickName', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/order-model/production-workorder', 'workOrder.editByClosed', 'workOrder.edit', 'createTime', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/order-model/production-workorder', 'workOrder.editByClosed', 'workOrder.edit', 'remarks', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);

INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/order-model/production-workorder', 'workOrder.editByCancel', 'workOrder.edit', 'barCode', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/order-model/production-workorder', 'workOrder.editByCancel', 'workOrder.edit', 'stateName', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/order-model/production-workorder', 'workOrder.editByCancel', 'workOrder.edit', 'count', 1, 1, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/order-model/production-workorder', 'workOrder.editByCancel', 'workOrder.edit', 'finishCount', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/order-model/production-workorder', 'workOrder.editByCancel', 'workOrder.edit', 'unqualified', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/order-model/production-workorder', 'workOrder.editByCancel', 'workOrder.edit', 'receiptQuantity', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/order-model/production-workorder', 'workOrder.editByCancel', 'workOrder.edit', 'createByNickName', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/order-model/production-workorder', 'workOrder.editByCancel', 'workOrder.edit', 'createTime', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/order-model/production-workorder', 'workOrder.editByCancel', 'workOrder.edit', 'remarks', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);

INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/order-model/production-workorder', 'workOrder.detail', 'workOrder.detail', 'barCode', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/order-model/production-workorder', 'workOrder.detail', 'workOrder.detail', 'stateName', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/order-model/production-workorder', 'workOrder.detail', 'workOrder.detail', 'count', 1, 1, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/order-model/production-workorder', 'workOrder.detail', 'workOrder.detail', 'finishCount', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/order-model/production-workorder', 'workOrder.detail', 'workOrder.detail', 'unqualified', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/order-model/production-workorder', 'workOrder.detail', 'workOrder.detail', 'receiptQuantity', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/order-model/production-workorder', 'workOrder.detail', 'workOrder.detail', 'createByNickName', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/order-model/production-workorder', 'workOrder.detail', 'workOrder.detail', 'createTime', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/order-model/production-workorder', 'workOrder.detail', 'workOrder.detail', 'remarks', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);

-- 订单批次
call proc_add_form_field_module("productOrder.edit", "barCode", "批次号", "batchField");
call proc_add_form_field_module("productOrder.edit", "stateName", "状态", "batchField");
call proc_add_form_field_module("productOrder.edit", "count", "计划数量", "batchField");
call proc_add_form_field_module("productOrder.edit", "finishCount", "生产数量", "batchField");
call proc_add_form_field_module("productOrder.edit", "unqualified", "不良数量", "batchField");
call proc_add_form_field_module("productOrder.edit", "receiptQuantity", "入库数量", "batchField");
call proc_add_form_field_module("productOrder.edit", "createByNickName", "创建人", "batchField");
call proc_add_form_field_module("productOrder.edit", "createTime", "创建时间", "batchField");
call proc_add_form_field_module("productOrder.edit", "remarks", "备注", "batchField");

call proc_add_form_field_module("productOrder.detail", "barCode", "批次号", "batchField");
call proc_add_form_field_module("productOrder.detail", "stateName", "状态", "batchField");
call proc_add_form_field_module("productOrder.detail", "count", "计划数量", "batchField");
call proc_add_form_field_module("productOrder.detail", "finishCount", "生产数量", "batchField");
call proc_add_form_field_module("productOrder.detail", "unqualified", "不良数量", "batchField");
call proc_add_form_field_module("productOrder.detail", "receiptQuantity", "入库数量", "batchField");
call proc_add_form_field_module("productOrder.detail", "createByNickName", "创建人", "batchField");
call proc_add_form_field_module("productOrder.detail", "createTime", "创建时间", "batchField");
call proc_add_form_field_module("productOrder.detail", "remarks", "备注", "batchField");

INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/order-model/product-order', 'productOrder.editByCreate', 'productOrder.edit', 'barCode', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/order-model/product-order', 'productOrder.editByCreate', 'productOrder.edit', 'stateName', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/order-model/product-order', 'productOrder.editByCreate', 'productOrder.edit', 'count', 1, 1, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/order-model/product-order', 'productOrder.editByCreate', 'productOrder.edit', 'finishCount', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/order-model/product-order', 'productOrder.editByCreate', 'productOrder.edit', 'unqualified', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/order-model/product-order', 'productOrder.editByCreate', 'productOrder.edit', 'receiptQuantity', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/order-model/product-order', 'productOrder.editByCreate', 'productOrder.edit', 'createByNickName', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/order-model/product-order', 'productOrder.editByCreate', 'productOrder.edit', 'createTime', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/order-model/product-order', 'productOrder.editByCreate', 'productOrder.edit', 'remarks', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);

INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/order-model/product-order', 'productOrder.editByRelease', 'productOrder.edit', 'barCode', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/order-model/product-order', 'productOrder.editByRelease', 'productOrder.edit', 'stateName', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/order-model/product-order', 'productOrder.editByRelease', 'productOrder.edit', 'count', 1, 1, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/order-model/product-order', 'productOrder.editByRelease', 'productOrder.edit', 'finishCount', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/order-model/product-order', 'productOrder.editByRelease', 'productOrder.edit', 'unqualified', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/order-model/product-order', 'productOrder.editByRelease', 'productOrder.edit', 'receiptQuantity', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/order-model/product-order', 'productOrder.editByRelease', 'productOrder.edit', 'createByNickName', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/order-model/product-order', 'productOrder.editByRelease', 'productOrder.edit', 'createTime', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/order-model/product-order', 'productOrder.editByRelease', 'productOrder.edit', 'remarks', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);

INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/order-model/product-order', 'productOrder.editByFinish', 'productOrder.edit', 'barCode', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/order-model/product-order', 'productOrder.editByFinish', 'productOrder.edit', 'stateName', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/order-model/product-order', 'productOrder.editByFinish', 'productOrder.edit', 'count', 1, 1, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/order-model/product-order', 'productOrder.editByFinish', 'productOrder.edit', 'finishCount', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/order-model/product-order', 'productOrder.editByFinish', 'productOrder.edit', 'unqualified', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/order-model/product-order', 'productOrder.editByFinish', 'productOrder.edit', 'receiptQuantity', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/order-model/product-order', 'productOrder.editByFinish', 'productOrder.edit', 'createByNickName', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/order-model/product-order', 'productOrder.editByFinish', 'productOrder.edit', 'createTime', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/order-model/product-order', 'productOrder.editByFinish', 'productOrder.edit', 'remarks', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);

INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/order-model/product-order', 'productOrder.editByClosed', 'productOrder.edit', 'barCode', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/order-model/product-order', 'productOrder.editByClosed', 'productOrder.edit', 'stateName', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/order-model/product-order', 'productOrder.editByClosed', 'productOrder.edit', 'count', 1, 1, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/order-model/product-order', 'productOrder.editByClosed', 'productOrder.edit', 'finishCount', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/order-model/product-order', 'productOrder.editByClosed', 'productOrder.edit', 'unqualified', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/order-model/product-order', 'productOrder.editByClosed', 'productOrder.edit', 'receiptQuantity', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/order-model/product-order', 'productOrder.editByClosed', 'productOrder.edit', 'createByNickName', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/order-model/product-order', 'productOrder.editByClosed', 'productOrder.edit', 'createTime', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/order-model/product-order', 'productOrder.editByClosed', 'productOrder.edit', 'remarks', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);

INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/order-model/product-order', 'productOrder.editByCancel', 'productOrder.edit', 'barCode', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/order-model/product-order', 'productOrder.editByCancel', 'productOrder.edit', 'stateName', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/order-model/product-order', 'productOrder.editByCancel', 'productOrder.edit', 'count', 1, 1, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/order-model/product-order', 'productOrder.editByCancel', 'productOrder.edit', 'finishCount', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/order-model/product-order', 'productOrder.editByCancel', 'productOrder.edit', 'unqualified', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/order-model/product-order', 'productOrder.editByCancel', 'productOrder.edit', 'receiptQuantity', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/order-model/product-order', 'productOrder.editByCancel', 'productOrder.edit', 'createByNickName', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/order-model/product-order', 'productOrder.editByCancel', 'productOrder.edit', 'createTime', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/order-model/product-order', 'productOrder.editByCancel', 'productOrder.edit', 'remarks', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);

INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/order-model/product-order', 'productOrder.detail', 'productOrder.detail', 'barCode', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/order-model/product-order', 'productOrder.detail', 'productOrder.detail', 'stateName', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/order-model/product-order', 'productOrder.detail', 'productOrder.detail', 'count', 1, 1, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/order-model/product-order', 'productOrder.detail', 'productOrder.detail', 'finishCount', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/order-model/product-order', 'productOrder.detail', 'productOrder.detail', 'unqualified', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/order-model/product-order', 'productOrder.detail', 'productOrder.detail', 'receiptQuantity', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/order-model/product-order', 'productOrder.detail', 'productOrder.detail', 'createByNickName', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/order-model/product-order', 'productOrder.detail', 'productOrder.detail', 'createTime', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/order-model/product-order', 'productOrder.detail', 'productOrder.detail', 'remarks', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);

-- 采购收料批次
call proc_add_form_field_module("purchaseReceiptOrder.edit", "barCode", "批次号", "batchField");
call proc_add_form_field_module("purchaseReceiptOrder.edit", "count", "收料数量", "batchField");
call proc_add_form_field_module("purchaseReceiptOrder.edit", "relateInspectOrder", "检验单号", "batchField");
call proc_add_form_field_module("purchaseReceiptOrder.edit", "qualifiedQuantity", "合格数", "batchField");
call proc_add_form_field_module("purchaseReceiptOrder.edit", "unqualifiedQuantity", "不合格数", "batchField");
call proc_add_form_field_module("purchaseReceiptOrder.edit", "rejectedQty", "拒收数量", "batchField");
call proc_add_form_field_module("purchaseReceiptOrder.edit", "warehouseStateName", "入库状态", "batchField");
call proc_add_form_field_module("purchaseReceiptOrder.edit", "concessionAcceptanceQuantity", "让步接收数量", "batchField");
call proc_add_form_field_module("purchaseReceiptOrder.edit", "returnAmount", "退料数量", "batchField");
call proc_add_form_field_module("purchaseReceiptOrder.edit", "industrialWasteQuantity", "工废数量", "batchField");
call proc_add_form_field_module("purchaseReceiptOrder.edit", "materialWasteQuantity", "料废数量", "batchField");
call proc_add_form_field_module("purchaseReceiptOrder.edit", "rejectedReason", "拒收理由", "batchField");
call proc_add_form_field_module("purchaseReceiptOrder.edit", "receiveStateName", "收料状态", "batchField");
call proc_add_form_field_module("purchaseReceiptOrder.edit", "receiveTime", "收料时间", "batchField");
call proc_add_form_field_module("purchaseReceiptOrder.edit", "inspectStateName", "检验状态", "batchField");
call proc_add_form_field_module("purchaseReceiptOrder.edit", "createByNickName", "创建人", "batchField");
call proc_add_form_field_module("purchaseReceiptOrder.edit", "createTime", "创建时间", "batchField");

call proc_add_form_field_module("purchaseReceiptOrder.detail", "barCode", "批次号", "batchField");
call proc_add_form_field_module("purchaseReceiptOrder.detail", "count", "收料数量", "batchField");
call proc_add_form_field_module("purchaseReceiptOrder.detail", "relateInspectOrder", "检验单号", "batchField");
call proc_add_form_field_module("purchaseReceiptOrder.detail", "qualifiedQuantity", "合格数", "batchField");
call proc_add_form_field_module("purchaseReceiptOrder.detail", "unqualifiedQuantity", "不合格数", "batchField");
call proc_add_form_field_module("purchaseReceiptOrder.detail", "rejectedQty", "拒收数量", "batchField");
call proc_add_form_field_module("purchaseReceiptOrder.detail", "warehouseStateName", "入库状态", "batchField");
call proc_add_form_field_module("purchaseReceiptOrder.detail", "concessionAcceptanceQuantity", "让步接收数量", "batchField");
call proc_add_form_field_module("purchaseReceiptOrder.detail", "returnAmount", "退料数量", "batchField");
call proc_add_form_field_module("purchaseReceiptOrder.detail", "industrialWasteQuantity", "工废数量", "batchField");
call proc_add_form_field_module("purchaseReceiptOrder.detail", "materialWasteQuantity", "料废数量", "batchField");
call proc_add_form_field_module("purchaseReceiptOrder.detail", "rejectedReason", "拒收理由", "batchField");
call proc_add_form_field_module("purchaseReceiptOrder.detail", "receiveStateName", "收料状态", "batchField");
call proc_add_form_field_module("purchaseReceiptOrder.detail", "receiveTime", "收料时间", "batchField");
call proc_add_form_field_module("purchaseReceiptOrder.detail", "inspectStateName", "检验状态", "batchField");
call proc_add_form_field_module("purchaseReceiptOrder.detail", "createByNickName", "创建人", "batchField");
call proc_add_form_field_module("purchaseReceiptOrder.detail", "createTime", "创建时间", "batchField");

INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByCreate', 'purchaseReceiptOrder.edit', 'barCode', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByCreate', 'purchaseReceiptOrder.edit', 'count', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByCreate', 'purchaseReceiptOrder.edit', 'relateInspectOrder', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByCreate', 'purchaseReceiptOrder.edit', 'qualifiedQuantity', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByCreate', 'purchaseReceiptOrder.edit', 'unqualifiedQuantity', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByCreate', 'purchaseReceiptOrder.edit', 'rejectedQty', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByCreate', 'purchaseReceiptOrder.edit', 'warehouseStateName', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByCreate', 'purchaseReceiptOrder.edit', 'concessionAcceptanceQuantity', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByCreate', 'purchaseReceiptOrder.edit', 'returnAmount', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByCreate', 'purchaseReceiptOrder.edit', 'industrialWasteQuantity', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByCreate', 'purchaseReceiptOrder.edit', 'materialWasteQuantity', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByCreate', 'purchaseReceiptOrder.edit', 'rejectedReason', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByCreate', 'purchaseReceiptOrder.edit', 'receiveStateName', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByCreate', 'purchaseReceiptOrder.edit', 'receiveTime', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByCreate', 'purchaseReceiptOrder.edit', 'inspectStateName', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByCreate', 'purchaseReceiptOrder.edit', 'createByNickName', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByCreate', 'purchaseReceiptOrder.edit', 'createTime', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);

INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByRelease', 'purchaseReceiptOrder.edit', 'barCode', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByRelease', 'purchaseReceiptOrder.edit', 'count', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByRelease', 'purchaseReceiptOrder.edit', 'relateInspectOrder', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByRelease', 'purchaseReceiptOrder.edit', 'qualifiedQuantity', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByRelease', 'purchaseReceiptOrder.edit', 'unqualifiedQuantity', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByRelease', 'purchaseReceiptOrder.edit', 'rejectedQty', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByRelease', 'purchaseReceiptOrder.edit', 'warehouseStateName', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByRelease', 'purchaseReceiptOrder.edit', 'concessionAcceptanceQuantity', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByRelease', 'purchaseReceiptOrder.edit', 'returnAmount', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByRelease', 'purchaseReceiptOrder.edit', 'industrialWasteQuantity', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByRelease', 'purchaseReceiptOrder.edit', 'materialWasteQuantity', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByRelease', 'purchaseReceiptOrder.edit', 'rejectedReason', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByRelease', 'purchaseReceiptOrder.edit', 'receiveStateName', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByRelease', 'purchaseReceiptOrder.edit', 'receiveTime', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByRelease', 'purchaseReceiptOrder.edit', 'inspectStateName', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByRelease', 'purchaseReceiptOrder.edit', 'createByNickName', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByRelease', 'purchaseReceiptOrder.edit', 'createTime', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);

INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByFinish', 'purchaseReceiptOrder.edit', 'barCode', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByFinish', 'purchaseReceiptOrder.edit', 'count', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByFinish', 'purchaseReceiptOrder.edit', 'relateInspectOrder', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByFinish', 'purchaseReceiptOrder.edit', 'qualifiedQuantity', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByFinish', 'purchaseReceiptOrder.edit', 'unqualifiedQuantity', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByFinish', 'purchaseReceiptOrder.edit', 'rejectedQty', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByFinish', 'purchaseReceiptOrder.edit', 'warehouseStateName', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByFinish', 'purchaseReceiptOrder.edit', 'concessionAcceptanceQuantity', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByFinish', 'purchaseReceiptOrder.edit', 'returnAmount', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByFinish', 'purchaseReceiptOrder.edit', 'industrialWasteQuantity', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByFinish', 'purchaseReceiptOrder.edit', 'materialWasteQuantity', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByFinish', 'purchaseReceiptOrder.edit', 'rejectedReason', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByFinish', 'purchaseReceiptOrder.edit', 'receiveStateName', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByFinish', 'purchaseReceiptOrder.edit', 'receiveTime', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByFinish', 'purchaseReceiptOrder.edit', 'inspectStateName', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByFinish', 'purchaseReceiptOrder.edit', 'createByNickName', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByFinish', 'purchaseReceiptOrder.edit', 'createTime', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);

INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByClosed', 'purchaseReceiptOrder.edit', 'barCode', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByClosed', 'purchaseReceiptOrder.edit', 'count', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByClosed', 'purchaseReceiptOrder.edit', 'relateInspectOrder', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByClosed', 'purchaseReceiptOrder.edit', 'qualifiedQuantity', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByClosed', 'purchaseReceiptOrder.edit', 'unqualifiedQuantity', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByClosed', 'purchaseReceiptOrder.edit', 'rejectedQty', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByClosed', 'purchaseReceiptOrder.edit', 'warehouseStateName', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByClosed', 'purchaseReceiptOrder.edit', 'concessionAcceptanceQuantity', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByClosed', 'purchaseReceiptOrder.edit', 'returnAmount', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByClosed', 'purchaseReceiptOrder.edit', 'industrialWasteQuantity', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByClosed', 'purchaseReceiptOrder.edit', 'materialWasteQuantity', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByClosed', 'purchaseReceiptOrder.edit', 'rejectedReason', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByClosed', 'purchaseReceiptOrder.edit', 'receiveStateName', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByClosed', 'purchaseReceiptOrder.edit', 'receiveTime', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByClosed', 'purchaseReceiptOrder.edit', 'inspectStateName', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByClosed', 'purchaseReceiptOrder.edit', 'createByNickName', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByClosed', 'purchaseReceiptOrder.edit', 'createTime', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);

INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByCancel', 'purchaseReceiptOrder.edit', 'barCode', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByCancel', 'purchaseReceiptOrder.edit', 'count', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByCancel', 'purchaseReceiptOrder.edit', 'relateInspectOrder', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByCancel', 'purchaseReceiptOrder.edit', 'qualifiedQuantity', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByCancel', 'purchaseReceiptOrder.edit', 'unqualifiedQuantity', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByCancel', 'purchaseReceiptOrder.edit', 'rejectedQty', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByCancel', 'purchaseReceiptOrder.edit', 'warehouseStateName', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByCancel', 'purchaseReceiptOrder.edit', 'concessionAcceptanceQuantity', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByCancel', 'purchaseReceiptOrder.edit', 'returnAmount', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByCancel', 'purchaseReceiptOrder.edit', 'industrialWasteQuantity', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByCancel', 'purchaseReceiptOrder.edit', 'materialWasteQuantity', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByCancel', 'purchaseReceiptOrder.edit', 'rejectedReason', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByCancel', 'purchaseReceiptOrder.edit', 'receiveStateName', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByCancel', 'purchaseReceiptOrder.edit', 'receiveTime', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByCancel', 'purchaseReceiptOrder.edit', 'inspectStateName', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByCancel', 'purchaseReceiptOrder.edit', 'createByNickName', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByCancel', 'purchaseReceiptOrder.edit', 'createTime', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);

INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.detail', 'purchaseReceiptOrder.detail', 'barCode', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.detail', 'purchaseReceiptOrder.detail', 'count', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.detail', 'purchaseReceiptOrder.detail', 'relateInspectOrder', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.detail', 'purchaseReceiptOrder.detail', 'qualifiedQuantity', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.detail', 'purchaseReceiptOrder.detail', 'unqualifiedQuantity', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.detail', 'purchaseReceiptOrder.detail', 'rejectedQty', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.detail', 'purchaseReceiptOrder.detail', 'warehouseStateName', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.detail', 'purchaseReceiptOrder.detail', 'concessionAcceptanceQuantity', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.detail', 'purchaseReceiptOrder.detail', 'returnAmount', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.detail', 'purchaseReceiptOrder.detail', 'industrialWasteQuantity', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.detail', 'purchaseReceiptOrder.detail', 'materialWasteQuantity', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.detail', 'purchaseReceiptOrder.detail', 'rejectedReason', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.detail', 'purchaseReceiptOrder.detail', 'receiveStateName', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.detail', 'purchaseReceiptOrder.detail', 'receiveTime', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.detail', 'purchaseReceiptOrder.detail', 'inspectStateName', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.detail', 'purchaseReceiptOrder.detail', 'createByNickName', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.detail', 'purchaseReceiptOrder.detail', 'createTime', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);

-- 采购退料批次
call proc_add_form_field_module("purchaseReturn.edit", "barCode", "批次号", "batchField");
call proc_add_form_field_module("purchaseReturn.edit", "batchAmount", "批次数量", "batchField");
call proc_add_form_field_module("purchaseReturn.edit", "returnAmount", "退料数量", "batchField");

call proc_add_form_field_module("purchaseReturn.detail", "barCode", "批次号", "batchField");
call proc_add_form_field_module("purchaseReturn.detail", "batchAmount", "批次数量", "batchField");
call proc_add_form_field_module("purchaseReturn.detail", "returnAmount", "退料数量", "batchField");

INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/supply-chain-collaboration/procurement-management/return-order', 'purchaseReturn.editByCreate', 'purchaseReturn.edit', 'barCode', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/supply-chain-collaboration/procurement-management/return-order', 'purchaseReturn.editByCreate', 'purchaseReturn.edit', 'batchAmount', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/supply-chain-collaboration/procurement-management/return-order', 'purchaseReturn.editByCreate', 'purchaseReturn.edit', 'returnAmount', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);

INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/supply-chain-collaboration/procurement-management/return-order', 'purchaseReturn.editByRelease', 'purchaseReturn.edit', 'barCode', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/supply-chain-collaboration/procurement-management/return-order', 'purchaseReturn.editByRelease', 'purchaseReturn.edit', 'batchAmount', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/supply-chain-collaboration/procurement-management/return-order', 'purchaseReturn.editByRelease', 'purchaseReturn.edit', 'returnAmount', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);

INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/supply-chain-collaboration/procurement-management/return-order', 'purchaseReturn.editByFinish', 'purchaseReturn.edit', 'barCode', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/supply-chain-collaboration/procurement-management/return-order', 'purchaseReturn.editByFinish', 'purchaseReturn.edit', 'batchAmount', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/supply-chain-collaboration/procurement-management/return-order', 'purchaseReturn.editByFinish', 'purchaseReturn.edit', 'returnAmount', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);

INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/supply-chain-collaboration/procurement-management/return-order', 'purchaseReturn.editByClosed', 'purchaseReturn.edit', 'barCode', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/supply-chain-collaboration/procurement-management/return-order', 'purchaseReturn.editByClosed', 'purchaseReturn.edit', 'batchAmount', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/supply-chain-collaboration/procurement-management/return-order', 'purchaseReturn.editByClosed', 'purchaseReturn.edit', 'returnAmount', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);

INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/supply-chain-collaboration/procurement-management/return-order', 'purchaseReturn.editByCancel', 'purchaseReturn.edit', 'barCode', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/supply-chain-collaboration/procurement-management/return-order', 'purchaseReturn.editByCancel', 'purchaseReturn.edit', 'batchAmount', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/supply-chain-collaboration/procurement-management/return-order', 'purchaseReturn.editByCancel', 'purchaseReturn.edit', 'returnAmount', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);

INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/supply-chain-collaboration/procurement-management/return-order', 'purchaseReturn.detail', 'purchaseReturn.detail', 'barCode', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/supply-chain-collaboration/procurement-management/return-order', 'purchaseReturn.detail', 'purchaseReturn.detail', 'batchAmount', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/supply-chain-collaboration/procurement-management/return-order', 'purchaseReturn.detail', 'purchaseReturn.detail', 'returnAmount', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);


-- ================================================此脚本专为表单需求设定，如需写脚本，请移步到3.13.1.1=======================================================
-- ================================================此脚本专为表单需求设定，如需写脚本，请移步到3.13.1.1=======================================================
-- ================================================此脚本专为表单需求设定，如需写脚本，请移步到3.13.1.1=======================================================
-- ================================================此脚本专为表单需求设定，如需写脚本，请移步到3.13.1.1=======================================================
-- ================================================此脚本专为表单需求设定，如需写脚本，请移步到3.13.1.1=======================================================
