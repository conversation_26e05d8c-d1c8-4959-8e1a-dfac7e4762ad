-- DD<PERSON>
call proc_add_column(
        'dfs_form_field_module_config',
        'parent_module_code',
        'ALTER TABLE `dfs_form_field_module_config` ADD COLUMN `parent_module_code` varchar(255) DEFAULT ''base'' COMMENT ''父模块编码'' after `module_name`;');
call proc_add_column(
        'dfs_form_field_module_config',
        'parent_module_name',
        'ALTER TABLE `dfs_form_field_module_config` ADD COLUMN `parent_module_name` varchar(255) DEFAULT ''基础字段'' COMMENT ''父模块名称'' after `parent_module_code`;');
call proc_add_column(
        'sys_users',
        'is_delete',
        'ALTER TABLE `sys_users` ADD COLUMN `is_delete` tinyint(4) DEFAULT 0 COMMENT ''是否软删除'';');
call proc_add_column(
        'dfs_form_field_rule_config',
        'sub_type',
        'ALTER TABLE `dfs_form_field_rule_config` ADD COLUMN `sub_type` varchar(255) DEFAULT NULL COMMENT ''子项类型'' after `option_values_type`;');
call proc_add_column(
        'dfs_material_config_field',
        'sub_type',
        'ALTER TABLE `dfs_material_config_field` ADD COLUMN `sub_type` varchar(255) DEFAULT NULL COMMENT ''子项类型'' after `option_values_type`;');
call proc_add_column(
        'dfs_material_type_config_field',
        'sub_type',
        'ALTER TABLE `dfs_material_type_config_field` ADD COLUMN `sub_type` varchar(255) DEFAULT NULL COMMENT ''子项类型'' after `option_values_type`;');
call proc_add_column(
        'dfs_order_category',
        'background_color',
        'ALTER TABLE `dfs_order_category` ADD COLUMN `background_color` varchar(100) DEFAULT ''#00A2E8'' COMMENT ''背景色'';');
call proc_add_column(
        'dfs_order_category',
        'font_color',
        'ALTER TABLE `dfs_order_category` ADD COLUMN `font_color` varchar(100) DEFAULT ''#000000'' COMMENT ''字体色'';');



INSERT INTO `sys_permissions` (`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`)
VALUES ('1090102410', '附件上传', 'sales.order:appendixUpload', NULL, NULL, NOW(), NULL, NOW(), 'enable', 'GET', '1090102', '2', '1', '0', '/supply-chain-collaboration/order-model/salesOrder', '1', NULL, '');
call init_new_role_permission('1090102410');

INSERT INTO `sys_permissions` (`id`, `name`, `path`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`)
VALUES ('1090205310', '回退', 'purchasing.order:rollback', NULL, NOW(), NULL, NOW(), 'enable', 'GET', '1090205', '2', '1', '0', '/supply-chain-collaboration/procurement-management/purchasing-list', '1', NULL, '');
call init_new_role_permission('1090205310');

-- 新增在线小程序清单表
CREATE TABLE IF NOT EXISTS `dfs_app_online` (
      `id` int(11) NOT NULL AUTO_INCREMENT,
      `permission_id` varchar(255) DEFAULT NULL COMMENT '权限id',
      `permission_path` varchar(255) DEFAULT NULL COMMENT '权限路径',
      `application_id` varchar(255) NOT NULL COMMENT '小程序id',
      `name` varchar(255) NOT NULL COMMENT '小程序名称',
      PRIMARY KEY (`id`),
      UNIQUE KEY `application_id` (`application_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='在线小程序清单';

-- DML
-- 新增删除用户的按钮权限
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`, `is_sys_data`) VALUES ('12102110', '删除', 'user:delete', NULL, NULL, NULL, NULL, '2024-12-09 15:26:02', 'enable', 'GET', '12102', 2, 1, 0, '/base-info/user', 1, NULL, '', 1);
call init_new_role_permission('12102110');

-- 将表单配置修改为【字段配置】
UPDATE `sys_route` SET `title` = '字段配置' WHERE `path` = '/system_settings/form-config/appFieldRuleConf';
UPDATE `sys_permissions` SET `name` = '字段配置' WHERE `path` = '/system_settings/form-config/appFieldRuleConf';
-- 表单配置菜单根据新整理的进行重新归纳
INSERT INTO `dfs_form_config`(`type`, `code`, `name`, `full_path_code`, `parent_full_path_code`, `description`, `create_by`, `update_by`, `create_time`, `update_time`, `is_inner`) VALUES ('web', 'dfs', 'DFS', 'dfs', '', NULL, 'admin', 'admin', '2025-01-16 16:12:21', '2025-01-16 16:12:21', 1);
INSERT INTO `dfs_form_config`(`type`, `code`, `name`, `full_path_code`, `parent_full_path_code`, `description`, `create_by`, `update_by`, `create_time`, `update_time`, `is_inner`) VALUES ('web', 'amsSale', '销售模块', 'amsSale', '', NULL, 'admin', 'admin', '2025-01-16 16:12:21', '2025-01-16 16:12:21', 1);
INSERT INTO `dfs_form_config`(`type`, `code`, `name`, `full_path_code`, `parent_full_path_code`, `description`, `create_by`, `update_by`, `create_time`, `update_time`, `is_inner`) VALUES ('web', 'amsProduct', '生产模块', 'amsProduct', '', NULL, 'admin', 'admin', '2025-01-16 16:12:21', '2025-01-16 16:12:21', 1);
INSERT INTO `dfs_form_config`(`type`, `code`, `name`, `full_path_code`, `parent_full_path_code`, `description`, `create_by`, `update_by`, `create_time`, `update_time`, `is_inner`) VALUES ('web', 'amsPurchase', '采购模块', 'amsPurchase', '', NULL, 'admin', 'admin', '2025-01-16 16:12:21', '2025-01-16 16:12:21', 1);
INSERT INTO `dfs_form_config`(`type`, `code`, `name`, `full_path_code`, `parent_full_path_code`, `description`, `create_by`, `update_by`, `create_time`, `update_time`, `is_inner`) VALUES ('web', 'amsSubcontract', '委外模块', 'amsSubcontract', '', NULL, 'admin', 'admin', '2025-01-16 16:12:21', '2025-01-16 16:12:21', 1);
DELETE FROM `dfs_form_config` WHERE `code` = 'qms';
INSERT INTO `dfs_form_config`(`type`, `code`, `name`, `full_path_code`, `parent_full_path_code`, `description`, `create_by`, `update_by`, `create_time`, `update_time`, `is_inner`) VALUES ('web', 'qms', '质量模块', 'qms', '', NULL, 'admin', 'admin', '2025-01-16 16:12:21', '2025-01-16 16:12:21', 1);
INSERT INTO `dfs_form_config`(`type`, `code`, `name`, `full_path_code`, `parent_full_path_code`, `description`, `create_by`, `update_by`, `create_time`, `update_time`, `is_inner`) VALUES ('web', 'wms', '仓库模块', 'wms', '', NULL, 'admin', 'admin', '2025-01-16 16:12:21', '2025-01-16 16:12:21', 1);
INSERT INTO `dfs_form_config`(`type`, `code`, `name`, `full_path_code`, `parent_full_path_code`, `description`, `create_by`, `update_by`, `create_time`, `update_time`, `is_inner`) VALUES ('web', 'aps', 'APS', 'aps', '', NULL, 'admin', 'admin', '2025-01-16 16:12:21', '2025-01-16 16:12:21', 1);
INSERT INTO `dfs_form_config`(`type`, `code`, `name`, `full_path_code`, `parent_full_path_code`, `description`, `create_by`, `update_by`, `create_time`, `update_time`, `is_inner`) VALUES ('web', 'tpm', 'TPM', 'tpm', '', NULL, 'admin', 'admin', '2025-01-16 16:12:21', '2025-01-16 16:12:21', 1);
UPDATE `dfs_form_config` SET `parent_full_path_code` = 'dfs' WHERE `full_path_code` = 'supplier';
UPDATE `dfs_form_config` SET `parent_full_path_code` = 'dfs' WHERE `full_path_code` = 'supplierMaterial';
UPDATE `dfs_form_config` SET `parent_full_path_code` = 'dfs' WHERE `full_path_code` = 'valuationConfig';
UPDATE `dfs_form_config` SET `parent_full_path_code` = 'dfs' WHERE `full_path_code` = 'material';
UPDATE `dfs_form_config` SET `parent_full_path_code` = 'dfs' WHERE `full_path_code` = 'workOrderReport';

UPDATE `dfs_form_config` SET `parent_full_path_code` = 'amsSale' WHERE `full_path_code` = 'saleOrder';
UPDATE `dfs_form_config` SET `parent_full_path_code` = 'amsSale' WHERE `full_path_code` = 'deliveryApplication';
UPDATE `dfs_form_config` SET `parent_full_path_code` = 'amsSale' WHERE `full_path_code` = 'saleReturn';

UPDATE `dfs_form_config` SET `parent_full_path_code` = 'amsProduct' WHERE `full_path_code` = 'productOrder';
UPDATE `dfs_form_config` SET `parent_full_path_code` = 'amsProduct' WHERE `full_path_code` = 'productOrderMaterialList';
UPDATE `dfs_form_config` SET `parent_full_path_code` = 'amsProduct' WHERE `full_path_code` = 'workOrder';
UPDATE `dfs_form_config` SET `parent_full_path_code` = 'amsProduct' WHERE `full_path_code` = 'workOrderMaterialList';

UPDATE `dfs_form_config` SET `parent_full_path_code` = 'amsPurchase' WHERE `full_path_code` = 'purchaseRequestOrder';
UPDATE `dfs_form_config` SET `parent_full_path_code` = 'amsPurchase' WHERE `full_path_code` = 'purchaseOrder';
UPDATE `dfs_form_config` SET `parent_full_path_code` = 'amsPurchase' WHERE `full_path_code` = 'purchaseReceiptOrder';
UPDATE `dfs_form_config` SET `parent_full_path_code` = 'amsPurchase' WHERE `full_path_code` = 'purchaseReturn';

UPDATE `dfs_form_config` SET `parent_full_path_code` = 'amsSubcontract' WHERE `full_path_code` = 'subcontractOrder';
UPDATE `dfs_form_config` SET `parent_full_path_code` = 'amsSubcontract' WHERE `full_path_code` = 'subcontractOrderMaterial';
UPDATE `dfs_form_config` SET `parent_full_path_code` = 'amsSubcontract' WHERE `full_path_code` = 'subcontractReceipt';
UPDATE `dfs_form_config` SET `parent_full_path_code` = 'amsSubcontract' WHERE `full_path_code` = 'subcontractDelivery';
UPDATE `dfs_form_config` SET `parent_full_path_code` = 'amsSubcontract' WHERE `full_path_code` = 'subcontractReturn';

UPDATE `dfs_form_config` SET `parent_full_path_code` = 'qms' WHERE `full_path_code` = 'qmsScheme';
UPDATE `dfs_form_config` SET `parent_full_path_code` = 'qms' WHERE `full_path_code` = 'qmsInsepct';
UPDATE `dfs_form_config` SET `parent_full_path_code` = 'qms' WHERE `full_path_code` = 'qmsIncomingInspect';
UPDATE `dfs_form_config` SET `parent_full_path_code` = 'qms' WHERE `full_path_code` = 'qmsProductionProcessInspect';
UPDATE `dfs_form_config` SET `parent_full_path_code` = 'qms' WHERE `full_path_code` = 'qmsProductionInspect';
UPDATE `dfs_form_config` SET `parent_full_path_code` = 'qms' WHERE `full_path_code` = 'qmsSaleInspect';
UPDATE `dfs_form_config` SET `parent_full_path_code` = 'qms' WHERE `full_path_code` = 'qmsOtherInspect';

UPDATE `dfs_form_config` SET `parent_full_path_code` = 'wms' WHERE `full_path_code` = 'salesIssueDoc';
UPDATE `dfs_form_config` SET `parent_full_path_code` = 'wms' WHERE `full_path_code` = 'workOrderComplete';

UPDATE `dfs_form_config` SET `parent_full_path_code` = 'aps' WHERE `full_path_code` = 'workScheduleProduction';

UPDATE `dfs_form_config` SET `parent_full_path_code` = 'tpm' WHERE `full_path_code` = 'deviceInspectionPlanItem';


-- 委外发料单、委外订单用料清单-单据类型默认值设置
INSERT INTO `dfs_order_type_config`(`code`, `name`, `related_category_code`, `description`, `is_default`, `create_by`, `update_by`, `create_time`, `update_time`, `is_inner`) VALUES ('subcontractDeliveryOrder', '委外发料单', '', NULL, 0, 'admin', 'admin', NOW(), NOW(), 1);
INSERT INTO `dfs_order_type_config`(`code`, `name`, `related_category_code`, `description`, `is_default`, `create_by`, `update_by`, `create_time`, `update_time`, `is_inner`) VALUES ('standardSubcontractDelivery', '标准发料', 'subcontractDeliveryOrder', NULL, 1, 'admin', 'admin', NOW(), NOW(), 1);
INSERT INTO `dfs_order_type_item`(`business_type_code`, `code`, `name`, `enable`, `sort`, `is_default`, `is_inner`, `description`, `create_by`, `update_by`, `create_time`, `update_time`) VALUES ('standardSubcontractDelivery', 'subcontractDelivery', '标准发料单', 1, 1, 0, 1, NULL, 'admin', 'admin', NOW(), NOW());
INSERT INTO `dfs_order_type_config`(`code`, `name`, `related_category_code`, `description`, `is_default`, `create_by`, `update_by`, `create_time`, `update_time`, `is_inner`) VALUES ('subcontractOrderMaterialList', '委外订单用料清单', '', NULL, 0, 'admin', 'admin', NOW(), NOW(), 1);
INSERT INTO `dfs_order_type_config`(`code`, `name`, `related_category_code`, `description`, `is_default`, `create_by`, `update_by`, `create_time`, `update_time`, `is_inner`) VALUES ('standardSubcontractMaterialList', '标准委外用料清单', 'subcontractOrderMaterialList', NULL, 1, 'admin', 'admin', NOW(), NOW(), 1);
INSERT INTO `dfs_order_type_item`(`business_type_code`, `code`, `name`, `enable`, `sort`, `is_default`, `is_inner`, `description`, `create_by`, `update_by`, `create_time`, `update_time`) VALUES ('standardSubcontractMaterialList', 'subcontractMaterialList', '标准委外用料清单', 1, 1, 0, 1, NULL, 'admin', 'admin', NOW(), NOW());

-- 采购收料单下推采购退料 - 按检验不良数量下推采购退料单
INSERT INTO `dfs_dict`(`code`, `name`, `type`, `value`) VALUES ('purchase.purchaseReceiptPushDownConfig.returnOrder.inspectDefectNum', '按检验不良数量下推采购退料单', 'purchase.purchaseReceiptPushDownConfig.returnOrder', 'show');
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'originalOrderStates', '源单据状态', 'purchase.purchaseReceiptPushDownConfig.returnOrder.inspectDefectNum.originalOrderStates', 'purchase.purchaseReceiptPushDownConfig.returnOrder.inspectDefectNum', 'select-multiple', 'api', '/ams/receipt/state', 'get', NULL, 'code,name', '[2,3]', '[2,3]', 'sourceConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'isSupportMaterialLineReversePushDown', '选择物料行', 'purchase.purchaseReceiptPushDownConfig.returnOrder.inspectDefectNum.isSupportMaterialLineReversePushDown', 'purchase.purchaseReceiptPushDownConfig.returnOrder.inspectDefectNum', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"开启\"},{\"value\":false,\"label\":\"关闭\"}]', 'true', 'sourceConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'description', '描述', 'purchase.purchaseReceiptPushDownConfig.returnOrder.inspectDefectNum.description', 'purchase.purchaseReceiptPushDownConfig.returnOrder.inspectDefectNum', 'input', 'input', NULL, NULL, NULL, NULL, NULL, NULL, 'sourceConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'targetOrderState', '目标单据状态', 'purchase.purchaseReceiptPushDownConfig.returnOrder.inspectDefectNum.targetOrderState', 'purchase.purchaseReceiptPushDownConfig.returnOrder.inspectDefectNum', 'select', 'api', '/ams/purchase/return/state', 'get', NULL, 'code,name', '[1,2]', '1', 'targetConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'defectNumPush', '按不良品数量下推', 'purchase.purchaseReceiptPushDownConfig.returnOrder.inspectDefectNum.defectNumPush', 'purchase.purchaseReceiptPushDownConfig.returnOrder.inspectDefectNum', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"是\"},{\"value\":false,\"label\":\"否\"}]', 'true', 'targetConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'pushDownAgain', '再次下推', 'purchase.purchaseReceiptPushDownConfig.returnOrder.inspectDefectNum.pushDownAgain', 'purchase.purchaseReceiptPushDownConfig.returnOrder.inspectDefectNum', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"支持\"},{\"value\":false,\"label\":\"不支持\"}]', 'true', 'targetConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'applicationId', 'applicationId', 'purchase.purchaseReceiptPushDownConfig.returnOrder.inspectDefectNum.applicationId', 'purchase.purchaseReceiptPushDownConfig.returnOrder.inspectDefectNum', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"new.yk.genieos.ams.scc.procurement-management.return-order\"', 'sysConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'url', '前端路由', 'purchase.purchaseReceiptPushDownConfig.returnOrder.inspectDefectNum.url', 'purchase.purchaseReceiptPushDownConfig.returnOrder.inspectDefectNum', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"/ams/supply-chain-collaboration/procurement-management/return-order?pushDownOrigin=purchaseReceipt\"', 'sysConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'sourceOrderType', '源单据类型编码', 'purchase.purchaseReceiptPushDownConfig.returnOrder.inspectDefectNum.sourceOrderType', 'purchase.purchaseReceiptPushDownConfig.returnOrder.inspectDefectNum', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"purchaseReceipt\"', 'sysConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'targetOrderType', '目标单据类型编码', 'purchase.purchaseReceiptPushDownConfig.returnOrder.inspectDefectNum.targetOrderType', 'purchase.purchaseReceiptPushDownConfig.returnOrder.inspectDefectNum', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"purchaseReturn\"', 'sysConf');
INSERT INTO `dfs_order_push_down_item`(`id`, `config_path`, `name`, `type`, `enable`, `is_inner`, `instance_type`, `create_by`, `update_by`, `create_time`, `update_time`, `is_show`)
VALUES (NULL, 'purchase.purchaseReceiptPushDownConfig.returnOrder',  '按检验不良数量下推采购退料单', 'RULE', 1, 1, 'purchase.purchaseReceiptPushDownConfig.returnOrder.inspectDefectNum', 'admin', 'admin', NOW(), NOW(), 1);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'originalOrderStates', '源单据状态', 'purchase.purchaseReceiptPushDownConfig.returnOrder.inspectDefectNum.originalOrderStates', 'purchase.purchaseReceiptPushDownConfig.returnOrder.inspectDefectNum', 'select-multiple', 'api', '/ams/receipt/state', 'get', NULL, 'code,name', '[2,3]', '[2,3]', 'sourceConf');
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'isSupportMaterialLineReversePushDown', '选择物料行', 'purchase.purchaseReceiptPushDownConfig.returnOrder.inspectDefectNum.isSupportMaterialLineReversePushDown', 'purchase.purchaseReceiptPushDownConfig.returnOrder.inspectDefectNum', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"开启\"},{\"value\":false,\"label\":\"关闭\"}]', 'true', 'sourceConf');
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'description', '描述', 'purchase.purchaseReceiptPushDownConfig.returnOrder.inspectDefectNum.description', 'purchase.purchaseReceiptPushDownConfig.returnOrder.inspectDefectNum', 'input', 'input', NULL, NULL, NULL, NULL, NULL, NULL, 'sourceConf');
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'targetOrderState', '目标单据状态', 'purchase.purchaseReceiptPushDownConfig.returnOrder.inspectDefectNum.targetOrderState', 'purchase.purchaseReceiptPushDownConfig.returnOrder.inspectDefectNum', 'select', 'api', '/ams/purchase/return/state', 'get', NULL, 'code,name', '[1,2]', '1', 'targetConf');
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'defectNumPush', '按不良品数量下推', 'purchase.purchaseReceiptPushDownConfig.returnOrder.inspectDefectNum.defectNumPush', 'purchase.purchaseReceiptPushDownConfig.returnOrder.inspectDefectNum', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"是\"},{\"value\":false,\"label\":\"否\"}]', 'true', 'targetConf');
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'pushDownAgain', '再次下推', 'purchase.purchaseReceiptPushDownConfig.returnOrder.inspectDefectNum.pushDownAgain', 'purchase.purchaseReceiptPushDownConfig.returnOrder.inspectDefectNum', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"支持\"},{\"value\":false,\"label\":\"不支持\"}]', 'true', 'targetConf');
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'applicationId', 'applicationId', 'purchase.purchaseReceiptPushDownConfig.returnOrder.inspectDefectNum.applicationId', 'purchase.purchaseReceiptPushDownConfig.returnOrder.inspectDefectNum', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"new.yk.genieos.ams.scc.procurement-management.return-order\"', 'sysConf');
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'url', '前端路由', 'purchase.purchaseReceiptPushDownConfig.returnOrder.inspectDefectNum.url', 'purchase.purchaseReceiptPushDownConfig.returnOrder.inspectDefectNum', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"/ams/supply-chain-collaboration/procurement-management/return-order?pushDownOrigin=purchaseReceipt\"', 'sysConf');
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'sourceOrderType', '源单据类型编码', 'purchase.purchaseReceiptPushDownConfig.returnOrder.inspectDefectNum.sourceOrderType', 'purchase.purchaseReceiptPushDownConfig.returnOrder.inspectDefectNum', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"purchaseReceipt\"', 'sysConf');
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'targetOrderType', '目标单据类型编码', 'purchase.purchaseReceiptPushDownConfig.returnOrder.inspectDefectNum.targetOrderType', 'purchase.purchaseReceiptPushDownConfig.returnOrder.inspectDefectNum', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"purchaseReturn\"', 'sysConf');
-- 关联绑定itemId
UPDATE `dfs_order_push_down_config_value` a,`dfs_order_push_down_item` b
SET a.`item_id` = b.`id`
WHERE a.`config_full_path_code` = b.`instance_type` and a.`item_id` is null and b.`instance_type` = 'purchase.purchaseReceiptPushDownConfig.returnOrder.inspectDefectNum';




-- 新增下推配置（生产订单按订单日计划下推生产工单）
INSERT INTO `dfs_dict`(`code`, `name`, `type`, `value`) VALUES ('production.productOrderPushDownConfig.workOrder.productOrderDaily', '按订单日计划下推', 'production.productOrderPushDownConfig.workOrder', 'show');
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'originalOrderStates', '源单据状态', 'production.productOrderPushDownConfig.workOrder.productOrderDaily.originalOrderStates', 'production.productOrderPushDownConfig.workOrder.productOrderDaily', 'select-multiple', 'api', '/ams/product/orders/all/state', 'get', NULL, 'code,name', '[2,3]', '[2,3]', 'sourceConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'isSupportMaterialLineReversePushDown', '选择物料行', 'production.productOrderPushDownConfig.workOrder.productOrderDaily.isSupportMaterialLineReversePushDown', 'production.productOrderPushDownConfig.workOrder.productOrderDaily', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"开启\"},{\"value\":false,\"label\":\"关闭\"}]', 'true', 'sourceConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'description', '描述', 'production.productOrderPushDownConfig.workOrder.productOrderDaily.description', 'production.productOrderPushDownConfig.workOrder.productOrderDaily', 'input', 'input', NULL, NULL, NULL, NULL, NULL, NULL, 'sourceConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'targetOrderState', '目标单据状态', 'production.productOrderPushDownConfig.workOrder.productOrderDaily.targetOrderState', 'production.productOrderPushDownConfig.workOrder.productOrderDaily', 'select', 'api', '/api/workorders/all/state', 'get', NULL, 'code,name', '[1,2]', '1', 'targetConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES (NULL, 'enableAutoFillPlanTime', '自动填写计划时间', 'production.productOrderPushDownConfig.workOrder.productOrderDaily.enableAutoFillPlanTime', 'production.productOrderPushDownConfig.workOrder.productOrderDaily', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"开启\"},{\"value\":false,\"label\":\"关闭\"}]', 'true', NULL, 'targetConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES (NULL, 'isFilterWorkOrder', '是否过滤数量为0的生产工单', 'production.productOrderPushDownConfig.workOrder.productOrderDaily.isFilterWorkOrder', 'production.productOrderPushDownConfig.workOrder.productOrderDaily', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"开启\"},{\"value\":false,\"label\":\"关闭\"}]', 'true', NULL, 'targetConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES (NULL, 'defaultCraft', '是否支持默认工艺', 'production.productOrderPushDownConfig.workOrder.productOrderDaily.defaultCraft', 'production.productOrderPushDownConfig.workOrder.productOrderDaily', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"是\"},{\"value\":false,\"label\":\"否\"}]', 'true', NULL, 'targetConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES (NULL, 'craftSplitType', '工艺拆分方式', 'production.productOrderPushDownConfig.workOrder.productOrderDaily.craftSplitType', 'production.productOrderPushDownConfig.workOrder.productOrderDaily', 'select', 'api', '/api/config/push/down/craft/split', 'get', NULL, 'code,name', NULL, '\"procedure\"', NULL, 'targetConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES (NULL, 'materialChoose', '工序物料选择', 'production.productOrderPushDownConfig.workOrder.productOrderDaily.materialChoose', 'production.productOrderPushDownConfig.workOrder.productOrderDaily', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":\"finishProduct\",\"label\":\"成品\"},{\"value\":\"procedureMaterial\",\"label\":\"工序物料\"}]', '\"finishProduct\"', NULL, 'targetConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES (NULL, 'isShowFinishProductCraft', '绑定成品工艺', 'production.productOrderPushDownConfig.workOrder.productOrderDaily.isShowFinishProductCraft', 'production.productOrderPushDownConfig.workOrder.productOrderDaily', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"开启\"},{\"value\":false,\"label\":\"关闭\"}]', 'true', NULL, 'targetConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES (NULL, 'isSupportNoCraft', '无工艺下推', 'production.productOrderPushDownConfig.workOrder.productOrderDaily.isSupportNoCraft', 'production.productOrderPushDownConfig.workOrder.productOrderDaily', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"支持\"},{\"value\":false,\"label\":\"不支持\"}]', 'false', NULL, 'targetConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'pushDownAgain', '再次下推', 'production.productOrderPushDownConfig.workOrder.productOrderDaily.pushDownAgain', 'production.productOrderPushDownConfig.workOrder.productOrderDaily', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"支持\"},{\"value\":false,\"label\":\"不支持\"}]', 'true', 'targetConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'applicationId', 'applicationId', 'production.productOrderPushDownConfig.workOrder.productOrderDaily.applicationId', 'production.productOrderPushDownConfig.workOrder.productOrderDaily', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"new.yk.genieos.dfs.order-model.production-workorder\"', 'sysConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'url', '前端路由', 'production.productOrderPushDownConfig.workOrder.productOrderDaily.url', 'production.productOrderPushDownConfig.workOrder.productOrderDaily', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"/dfs/workorder-model/production-workorder?pushDownOrigin=productOrder\"', 'sysConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'sourceOrderType', '源单据类型编码', 'production.productOrderPushDownConfig.workOrder.productOrderDaily.sourceOrderType', 'production.productOrderPushDownConfig.workOrder.productOrderDaily', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"productOrder\"', 'sysConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'targetOrderType', '目标单据类型编码', 'production.productOrderPushDownConfig.workOrder.productOrderDaily.targetOrderType', 'production.productOrderPushDownConfig.workOrder.productOrderDaily', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"workOrder\"', 'sysConf');
INSERT INTO `dfs_order_push_down_item`(`id`, `config_path`, `name`, `type`, `enable`, `is_inner`, `instance_type`, `create_by`, `update_by`, `create_time`, `update_time`, `is_show`)
VALUES (NULL, 'production.productOrderPushDownConfig.workOrder',  '按订单日计划下推', 'RULE', 1, 1, 'production.productOrderPushDownConfig.workOrder.productOrderDaily', 'admin', 'admin', NOW(), NOW(), 1);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'originalOrderStates', '源单据状态', 'production.productOrderPushDownConfig.workOrder.productOrderDaily.originalOrderStates', 'production.productOrderPushDownConfig.workOrder.productOrderDaily', 'select-multiple', 'api', '/ams/product/orders/all/state', 'get', NULL, 'code,name', '[2,3]', '[2,3]', 'sourceConf');
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'isSupportMaterialLineReversePushDown', '反选物料行', 'production.productOrderPushDownConfig.workOrder.productOrderDaily.isSupportMaterialLineReversePushDown', 'production.productOrderPushDownConfig.workOrder.productOrderDaily', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"开启\"},{\"value\":false,\"label\":\"关闭\"}]', 'true', 'sourceConf');
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'description', '描述', 'production.productOrderPushDownConfig.workOrder.productOrderDaily.description', 'production.productOrderPushDownConfig.workOrder.productOrderDaily', 'input', 'input', NULL, NULL, NULL, NULL, NULL, NULL, 'sourceConf');
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'targetOrderState', '目标单据状态', 'production.productOrderPushDownConfig.workOrder.productOrderDaily.targetOrderState', 'production.productOrderPushDownConfig.workOrder.productOrderDaily', 'select', 'api', '/api/workorders/all/state', 'get', NULL, 'code,name', '[1,2]', '1', 'targetConf');
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES (NULL, 'enableAutoFillPlanTime', '自动填写计划时间', 'production.productOrderPushDownConfig.workOrder.productOrderDaily.enableAutoFillPlanTime', 'production.productOrderPushDownConfig.workOrder.productOrderDaily', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"开启\"},{\"value\":false,\"label\":\"关闭\"}]', 'true', NULL, 'targetConf');
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES (NULL, 'isFilterWorkOrder', '是否过滤数量为0的生产工单', 'production.productOrderPushDownConfig.workOrder.productOrderDaily.isFilterWorkOrder', 'production.productOrderPushDownConfig.workOrder.productOrderDaily', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"开启\"},{\"value\":false,\"label\":\"关闭\"}]', 'true', NULL, 'targetConf');
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES (NULL, 'defaultCraft', '是否支持默认工艺', 'production.productOrderPushDownConfig.workOrder.productOrderDaily.defaultCraft', 'production.productOrderPushDownConfig.workOrder.productOrderDaily', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"是\"},{\"value\":false,\"label\":\"否\"}]', 'true', NULL, 'targetConf');
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES (NULL, 'craftSplitType', '工艺拆分方式', 'production.productOrderPushDownConfig.workOrder.productOrderDaily.craftSplitType', 'production.productOrderPushDownConfig.workOrder.productOrderDaily', 'select', 'api', '/api/config/push/down/craft/split', 'get', NULL, 'code,name', NULL, '\"procedure\"', NULL, 'targetConf');
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES (NULL, 'materialChoose', '工序物料选择', 'production.productOrderPushDownConfig.workOrder.productOrderDaily.materialChoose', 'production.productOrderPushDownConfig.workOrder.productOrderDaily', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":\"finishProduct\",\"label\":\"成品\"},{\"value\":\"procedureMaterial\",\"label\":\"工序物料\"}]', '\"finishProduct\"', NULL, 'targetConf');
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES (NULL, 'isShowFinishProductCraft', '绑定成品工艺', 'production.productOrderPushDownConfig.workOrder.productOrderDaily.isShowFinishProductCraft', 'production.productOrderPushDownConfig.workOrder.productOrderDaily', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"开启\"},{\"value\":false,\"label\":\"关闭\"}]', 'true', NULL, 'targetConf');
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES (NULL, 'isSupportNoCraft', '无工艺下推', 'production.productOrderPushDownConfig.workOrder.productOrderDaily.isSupportNoCraft', 'production.productOrderPushDownConfig.workOrder.productOrderDaily', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"支持\"},{\"value\":false,\"label\":\"不支持\"}]', 'false', NULL, 'targetConf');
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'pushDownAgain', '再次下推', 'production.productOrderPushDownConfig.workOrder.productOrderDaily.pushDownAgain', 'production.productOrderPushDownConfig.workOrder.productOrderDaily', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"支持\"},{\"value\":false,\"label\":\"不支持\"}]', 'true', 'targetConf');
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'applicationId', 'applicationId', 'production.productOrderPushDownConfig.workOrder.productOrderDaily.applicationId', 'production.productOrderPushDownConfig.workOrder.productOrderDaily', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"new.yk.genieos.dfs.order-model.production-workorder\"', 'sysConf');
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'url', '前端路由', 'production.productOrderPushDownConfig.workOrder.productOrderDaily.url', 'production.productOrderPushDownConfig.workOrder.productOrderDaily', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"/dfs/workorder-model/production-workorder?pushDownOrigin=productOrder\"', 'sysConf');
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'sourceOrderType', '源单据类型编码', 'production.productOrderPushDownConfig.workOrder.productOrderDaily.sourceOrderType', 'production.productOrderPushDownConfig.workOrder.productOrderDaily', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"productOrder\"', 'sysConf');
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'targetOrderType', '目标单据类型编码', 'production.productOrderPushDownConfig.workOrder.productOrderDaily.targetOrderType', 'production.productOrderPushDownConfig.workOrder.productOrderDaily', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"workOrder\"', 'sysConf');
-- 关联绑定itemId
UPDATE `dfs_order_push_down_config_value` a,`dfs_order_push_down_item` b
SET a.`item_id` = b.`id`
WHERE a.`config_full_path_code` = b.`instance_type` and a.`item_id` is null and b.`instance_type` = 'production.productOrderPushDownConfig.workOrder.productOrderDaily';


call proc_add_column(
        'dfs_bom',
        'release_time',
        'ALTER TABLE `dfs_bom` ADD COLUMN `release_time` datetime NULL COMMENT ''生效时间''');

call proc_add_column(
        'dfs_material',
        'release_time',
        'ALTER TABLE `dfs_material` ADD COLUMN `release_time` datetime NULL COMMENT ''生效时间''');

call proc_add_column(
        'dfs_craft',
        'release_time',
        'ALTER TABLE `dfs_craft` ADD COLUMN `release_time` datetime NULL COMMENT ''生效时间''');

call proc_add_column(
        'dfs_procedure',
        'release_time',
        'ALTER TABLE `dfs_procedure` ADD COLUMN `release_time` datetime NULL COMMENT ''生效时间''');

-- 物料条码动态tab页配置
delete from  dfs_business_config where full_path_code = 'codeCenter.dynamicTabPageConfig';
delete from  dfs_business_config_value where config_full_path_code = 'codeCenter.dynamicTabPageConfig';
INSERT INTO `dfs_business_config` VALUES (null, 'dynamicTabPageConfig', '物料条码动态tab页配置', 'codeCenter.dynamicTabPageConfig', 'codeCenter', '配置内容：格式为json数组，示例：[{"tabPageCode": "test", "tabPageName": "测试", "webAddress": "/genieos/subapps/children/ams/test.vue"}]。tabPageCode为动态tab页编码，tabPageName为动态tab页名称，webAddress为前端代码地址。', 'yelinkoncall', 'yelinkoncall', now(), now(), '');
INSERT INTO `dfs_business_config_value` VALUES (null, 'configContent', '配置内容', 'codeCenter.dynamicTabPageConfig.configContent', 'codeCenter.dynamicTabPageConfig', 'json', 'input', NULL, NULL, NULL, NULL, NULL, '[]', NULL);

-- 任务日志追溯
CREATE TABLE IF NOT EXISTS `dfs_task_log` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `task_id` int(11) NOT NULL COMMENT '任务类型',
    `operate_type` varchar(255) NOT NULL COMMENT '操作类型',
    `detail` text COMMENT '操作明细',
    `operator` varchar(255) DEFAULT NULL COMMENT '操作人',
    `operate_time` datetime DEFAULT NULL COMMENT '操作时间',
    PRIMARY KEY (`id`),
    KEY `task_id` (`task_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='任务日志追溯';

INSERT INTO `dfs_app_online`(`permission_id`, `permission_path`, `application_id`, `name`) VALUES
('/documents-config/order-type', '/documents-config/order-type', 'new.yk.genieos.dfs.doc.ordertype', '单据类型'),
('/documents-config/return-order', '/documents-config/return-order', 'new.yk.genieos.dfs.documents-config.return-order', '单据回退'),
('/task-center/task-config', '/task-center/task-config', 'new.yk.dfs.task-center.task-config', '任务配置'),
('10003', '/order-model/product-order', 'new.yk.genieos.ams.order-model.product-order', '生产订单'),
('10006', '/order-model/production-materials', 'new.yk.genieos.ams.order-model.production-materials', '生产订单用料清单'),
('10010', '/order-model/saleReport', 'com.yk.genieos.ams.statementCenter.salesReport', '销售看板'),
('10012', '/order-model/workOrder-materials', 'new.yk.genieos.dfs.order-model.workOrder-materials', '生产工单用料清单'),
('10013', '/order-model/work-schedule-production', 'new.yk.genieos.aps.order-model.work-schedule-production', '智能排产'),
('10014', '/order-model/package-order', 'new.yk.genieos.dfs.order-model.package-order', '包装工单'),
('10101', '/workorder-model/production-workorder', 'new.yk.genieos.dfs.order-model.production-workorder', '生产工单'),
('10104', '/workorder-model/product-report/index', 'new.yk.genieos.dfs.workorder-model.product-report.index', '生产看板'),
('10109', '/workorder-model/production-records', 'new.yk.genieos.dfs.workorder-model.production-records', '生产工单报工记录'),
('10111', '/workorder-model/workorderAttendanceRecords', 'new.yk.genieos.dfs.workorder-model.workorderAttendanceRecords', '生产上工记录'),
('10113', '/workorder-model/feed-to-prevent-errors', 'new.yk.genieos.dfs.workorder-model.feed-to-prevent-errors', '上料防错'),
('10201', '/trace/order', 'new.yk.genieos.ams.trace.order', '生产订单追溯'),
('10202', '/trace/work-order', 'new.yk.genieos.ams.trace.work-order', '工单追溯'),
('10203', '/trace/package-record', 'new.yk.genieos.ams.trace.package-record', '包装记录'),
('10205', '/trace/single-product', 'new.yk.genieos.ams.trace.single-product', '单品追溯'),
('10206', '/trace/order-transparency', 'new.yk.genieos.ams.trace', '销售订单透明'),
('10207', '/trace/saleOrder-trace', 'new.yk.genieos.ams.trace.saleOrder-trace', '销售订单追溯'),
('10208', '/trace/feed-record', 'new.yk.genieos.ams.trace.feed-record', '物料追溯'),
('10210', '/trace/valuation-cal', 'new.yk.genieos.ams.trace.valuation-cal', '工资计算'),
('10302', '/equipment-management/warranty', 'new.yk.genieos.tpm.equipment-management.warranty', '维修工单'),
('10303', '/equipment-management/alarmToOrder', 'new.yk.genieos.tpm.equipment-management.alarmToOrder', '告警转工单'),
('10304', '/equipment-management/deviceInspection/index', 'new.yk.genieos.tpm.equipment-management.deviceInspection.index', '设备巡检计划'),
('10305', '/equipment-management/deviceMaintenance/index', 'new.yk.genieos.tpm.equipment-management.deviceMaintenance.index', '设备保养计划'),
('10307', '/equipment-management/equip-checked/equipCheckProject', 'new.yk.genieos.tpm.equipment-management.ec.equipCheckProject', '设备检查项目'),
('10308', '/equipment-management/equip-checked/equipCheckPlan', 'new.yk.genieos.tpm.equipment-management.ec.equipCheckPlan', '设备检查方案'),
('10309', '/equipment-management/deviceDetection/index', 'new.yk.genieos.tpm.equipment-management.deviceDetection.index', '点检计划'),
('10310', '/equipment-management/maintainGroup', 'new.yk.genieos.tpm.equipment-management.maintainGroup', '维保班组'),
('10408', '/qualities-manage/productInspection/project', 'new.yk.genieos.qms.qualities-manage.productInspection.project', '产品检验项目'),
('10409', '/qualities-manage/productInspection/plan', 'new.yk.genieos.qms.qualities-manage.productInspection.plan', '产品检验方案'),
('10410', '/qualities-manage/productInspection', 'new.yk.genieos.qms.qualities-manage.productInspection', '产品检验'),
('10412', '/qualities-manage/qualityReport', 'new.yk.genieos.dfs.qualities-manage.qualityReport', '质量看板'),
('10602', '/smt-management/material-station', 'new.yk.genieos.smt.smt-management.material-station', '物料站位表'),
('10603', '/smt-management/order-station', 'new.yk.genieos.smt.smt-management.order-station', '工单站位表'),
('10604', '/smt-management/feederManage', 'new.yk.genieos.smt.smt-management.feederManage', 'Feeder管理'),
('10606', '/smt-management/aging-management', 'new.yk.genieos.dfs.smt-management.aging-management', '老化管理'),
('10607', '/smt-management/scrap-material-management', 'new.yk.genieos.dfs.smt-management.scrap-material-management', '废料管理'),
('10609', '/smt-management/bosaManagement', 'new.yk.genieos.smt.smt-management.bosaManagement', 'BOSA管理'),
('10613', '/smt-management/combine-management', 'new.yk.genieos.smt.smt-management.combine-management', '拼码管理'),
('10701', '/alarm/list', 'new.yk.genieos.dfs.alarm.list', '告警列表'),
('10702', '/alarm/config', 'new.yk.genieos.dfs.alarm.config', '告警设置'),
('10703', '/alarm/historyAlarm', 'new.yk.genieos.dfs.alarm.historyAlarm', '历史告警'),
('10705', '/alarm/alarmIDDefine', 'new.yk.genieos.dfs.alarm.alarmIDDefine', '告警定义'),
('10706', '/alarm/event-definition', 'new.yk.genieos.dfs.alarm.event-definition', '事件定义'),
('10707', '/alarm/event-record', 'new.yk.genieos.dfs.alarm.event-record', '事件记录'),
('10801', '/product-management/supplies', 'new.yk.genieos.dfs.product-management.supplies', '物料'),
('10802', '/product-management/bom', 'new.yk.genieos.dfs.product-management.bom', 'BOM'),
('10803', '/product-management/technology', 'new.yk.genieos.dfs.product-management.technology', '工艺'),
('10804', '/product-management/procedure', 'new.yk.genieos.dfs.product-management.procedure', '工序定义'),
('10806', '/product-management/replace-scheme', 'new.yk.genieos/dfs.product-management.replace-scheme', '替代方案'),
('10807', '/product-management/auxiliary-attr', 'new.yk.genieos.dfs.product-management.auxiliary-attr', '物料特征参数'),
('10808', '/product-management/bomTemplate', 'new.yk.genieos.dfs.product-management.bomTemplate', 'BOM模板'),
('10809', '/product-management/technologyTemplate', 'new.yk.genieos/dfs.product-management.technologyTemplate', '工艺模板'),
('10810', '/packageManagement/scheme', 'new.yk.genieos.dfs.packageManagement.scheme', '包装方案'),
('1090101', '/supply-chain-collaboration/order-model/clientFile', 'new.yk.genieos.dfs.scc.order-model.clientFile', '客户档案'),
('1090102', '/supply-chain-collaboration/order-model/salesOrder', 'new.yk.genieos.ams.order-model.salesOrder', '销售订单'),
('1090103', '/supply-chain-collaboration/order-model/shipment_application', 'new.yk.genieos.dfs.scc.order-model.shipment_application', '销售出货单'),
('1090104', '/supply-chain-collaboration/order-model/deliver_goods', 'new.yk.genieos.dfs.supply-chain-collaboration.order-model.dg', '销售发货'),
('1090105', '/supply-chain-collaboration/order-model/customer-material-list', 'new.yk.genieos.dfs.scc.order-model.customer-material-list', '客户物料清单'),
('1090201', '/supply-chain-collaboration/procurement-management/supplier-profile', 'new.yk.genieos.dfs.scc.procurement-management.supplier-profile', '供应商档案'),
('1090202', '/supply-chain-collaboration/procurement-management/supplyList', 'new.yk.genieos.dfs.scc.procurement-management.supplyList', '供应商物料'),
('1090203', '/supply-chain-collaboration/procurement-management/materialAnalysis', 'new.yk.genieos.ams.scc.procurement-management.materialAnalysis', '物料需求分析'),
('1090204', '/supply-chain-collaboration/procurement-management/purchasing-demand', 'new.yk.genieos.ams.scc.procurement-management.purchasing-demand', '采购需求单'),
('1090205', '/supply-chain-collaboration/procurement-management/purchasing-list', 'new.yk.genieos.ams.scc.procurement-management.purchasing-list', '采购订单'),
('1090206', '/supply-chain-collaboration/procurement-management/delivery-order', 'new.yk.genieos.ams.scc.procurement-management.delivery-order', '采购收料单'),
('1090208', '/supply-chain-collaboration/procurement-management/purchase-material-return-application', 'new.yk.genieos.ams.scc.procurement-management.pmr-application', '采购退料申请'),
('1090209', '/supply-chain-collaboration/procurement-management/return-order', 'new.yk.genieos.ams.scc.procurement-management.return-order', '采购退料'),
('1090310', '/warehousingDistribution/purchaseOutOfStorage', 'com.yk.genieos.wms.warehousingDistribution.purchaseOutOfStorage', '采购出入库'),
('1090311', '/warehousingDistribution/productInOrOut', 'com.yk.genieos.wms.warehousingDistribution.productInOrOut', '生产出入库'),
('1090312', '/warehousingDistribution/baseManagement', 'com.yk.genieos.wms.warehousingDistribution.baseManagement', '仓库基础管理'),
('1090314', '/warehousingDistribution/salesIssueReceipt', 'com.yk.genieos.wms.warehousingDistribution.salesIssueReceipt', '销售出入库'),
('1090315', '/warehousingDistribution/subcontracting', 'new.yk.genieos.ams.scc.osm.outsourcingOrder', '委外订单'),
('1090316', '/warehousingDistribution/cougnyManage', 'com.yk.genieos.wms.warehousingDistribution.cougnyManage', '库内管理'),
('1090317', '/warehousingDistribution/mixReceive', 'com.yk.genieos.wms.warehousingDistribution.mixReceive', '杂收杂发'),
('1090319', '/warehousingDistribution/warehouseConfigSystem', 'com.yk.genieos.wms.warehousingDistribution.warehouseConfigSystem', '仓储配置中心'),
('1090402', '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'new.yk.genieos.ams.scc.osm.outsourcingReceiptOrder', '委外订单收货单'),
('1090403', '/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder', 'new.yk.genieos.ams.scc.osm.outsourcingMaterialOrder', '委外订单用料清单'),
('1090404', '/supply-chain-collaboration/outsourcingManagement/outsourcingIssueMaterial', 'new.yk.genieos.ams.scc.osm.outsourcingIssueMaterial', '委外发料单'),
('1090405', '/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'new.yk.genieos.ams.scc.osm.outsourcingReturnOrder', '委外订单退货单'),
('11001', '/energy-consumption-management/energy-information', 'new.yk.genieos.dfs.ec-management.energy-information', '能源信息'),
('11002', '/energy-consumption-management/time-period-management', 'new.yk.genieos.dfs.ec-management.time-period-management', '用电时段管理'),
('11301', '/factory-model/companyInfo/index', 'new.yk.genieos.dfs.factory-model.companyInfo.index', '公司信息'),
('11302', '/factory-model/product-model/index', 'new.yk.genieos.dfs.factory-model.product-model.index', '工厂模型'),
('11303', '/factory-model/factory-area', 'new.yk.genieos.dfs.factory-model.factory-area', '厂区'),
('11304', '/factory-model/workshop', 'new.yk.genieos.dfs.factory-model.workshop', '车间'),
('11305', '/factory-model/product-line', 'new.yk.genieos.dfs.factory-model.product-line', '制造单元'),
('11306', '/factory-model/station', 'new.yk.genieos.dfs.factory-model.station', '工位'),
('11307', '/factory-model/team', 'new.yk.genieos.dfs.factory-model.team', '班组'),
('11308', '/factory-model/work-center', 'new.yk.genieos.dfs.factory-model.work-center', '工作中心'),
('11309', '/factory-model/business-unit', 'new.yk.genieos/dfs.factory-model.business-unit', '业务单元'),
('11401', '/production-config/workCalendar/worksCalendar', 'new.yk.genieos.dfs.production-config.workCalendar.worksCalendar', '工作日历'),
('11402', '/production-config/flight-config', 'new.yk.genieos.dfs.production-config.flight-config', '班次配置'),
('11404', '/production-config/capacityList', 'new.yk.genieos.dfs.production-config.capacityList', '产能列表'),
('11405', '/production-config/valuation-config', 'new.yk.genieos.dfs.production-config.valuation-config', '工资配置'),
('11501', '/target/factoryTarget', 'new.yk.genieos.dfs.target.factoryTarget', '工厂指标'),
('11601', '/equipment/device', 'new.yk.genieos.dfs.equipment-management.device', '设备台账'),
('11602', '/equipment/test-equipment', 'new.yk.genieos.dfs.equipment.test-equipment', '采集设备'),
('11603', '/equipment/station-machine', 'new.yk.genieos.dfs.equipment.station-machine', '终端设备'),
('11605', '/equipment/screen-management', 'new.yk.genieos.dfs.scr.settings', '大屏管理'),
('11606', '/equipment/equipment-model', 'new.yk.genieos.dfs.equipment.equipment-model', '设备类型'),
('11608', '/equipment/technology-device', 'new.yk.genieos.dfs.equipment.technology-device', '工装'),
('11701', '/avg/point/list', 'new.yk.genieos.dfs.avg.dispath.index', '点位配置'),
('11702', '/avg/dispath/index', 'new.yk.genieos.dfs.avg.point.list', '线路配置'),
('11801', '/material-config/material-type-config', 'new.yk.genieos.dfs.material-config.material-type-config', '物料类型配置'),
('11803', '/material-config/unit-config', 'new.yk.genieos.dfs.material-config.unit-config', '单位配置'),
('11804', '/material-config/extend-config', 'new.yk.genieos.dfs.material-config.extend-config', '物料扩展字段'),
('11805', '/material-config/material-property', 'new.yk.genieos.dfs.material-config.material-property', '条码扩展字段'),
('119', '/label-manage/labelRules', 'new.yk.genieos.dfs.label-manage.labelRules', '标签管理'),
('12001', '/documents-config/num-rule', 'new.yk.genieos.dfs.documents-config.num-rule', '编码规则'),
('12002', '/documents-config/approveConfig', 'new.yk.genieos.dfs.documents-config.approveConfig', '审批配置'),
('12003', '/documents-config/clause', 'new.yk.genieos.dfs.documents-config.clause', '条款管理'),
('12005', '/documents-config/scan-rule', 'new.yk.genieos.dfs.documents-config.scan-rule', '扫码规则'),
('12007', '/documents-config/decode-rule', 'new.yk.genieos.dfs.documents-config.decode-rule', '解码规则'),
('12101', '/base-info/organization', 'new.yk.genieos.dfs.base-info.organization', '组织机构'),
('12102', '/base-info/user', 'new.yk.genieos.dfs.base-info.user', '用户管理'),
('12103', '/base-info/role-permission', 'new.yk.genieos.dfs.base-info.role-permission', '角色业务权限'),
('12104', '/base-info/post-manager', 'new.yk.genieos.dfs.base-info.post-manager', '岗位管理'),
('12205', '/system_settings/whiteCardSetting', 'new.yk.genieos.dfs.system_settings.whiteCardSetting', '白牌设置'),
('12207', '/system_settings/circulation-notice-config', 'new.yk.genieos.dfs.system_settings.circulation-notice-config', '消息通知配置'),
('12208', '/system_settings/BusinessConfiguration', 'new.yk.genieos.dfs.system_settings.BusinessConfiguration', '软件参数配置'),
('12211030', '/system_settings/form-config/appFieldRuleConf', 'new.yk.genieos.dfs.system_settings.form-config.appFieldRuleConf', '字段配置'),
('12230', '/system_settings/demo-data-config', 'new.yk.genieos.dfs.system_settings.demo-data-config', '演示数据配置'),
('123', '/system/operation', 'new.yk.genieos.dfs.system.operation', '操作日志'),
('1280010001', '/projectManagement/projectDefinition', 'new.yk.genieos.pms.projectManagement.projectDefinition', '项目定义'),
('12801', '/projectManagement/nodeDefinition', 'new.yk.genieos.ams.projectManagement.nodeDefinition', '节点定义'),
('12802', '/projectManagement/nodeConfigure', 'new.yk.genieos.ams.projectManagement.nodeConfigure', '项目节点配置'),
('12803', '/projectManagement/projectSelect', 'new.yk.genieos.ams.projectManagement.projectSelect', '项目查询'),
('13003', '/statementCenter/salesReport', 'new.yk.genieos.ams.statementCenter.salesReport', '销售看板'),
('13004', '/statementCenter/reportManage', 'new.yk.genieos.ams.statementCenter.reportManage', '报表管理'),
('13006', '/statementCenter/salaryBoard', 'new.yk.genieos.ams.statementCenter.salaryBoard', '工资看板'),
('13101', '/identification-center/material-identification', 'new.yk.genieos.ams.identification-center.materialidentification', '物料条码'),
('13201', '/api/transform/apiList', 'new.yk.genieos.dfs.api.transform.apiList', 'API重定向'),
('301101001', '/contract-management/sale-contract', 'com.yk.genieos.pms.contract-management.sale-contract', '销售合同'),
('301101002', '/contract-management/purchase-contract', 'com.yk.genieos.pms.contract-management.purchase-contract', '采购合同'),
('aps10002', '/order-model/auxiliary_plan', 'new.yk.genieos.aps.order-model.auxiliary_plan', '辅助计划'),
('assignment-model.01', '/assignment-model/home-workorder', 'new.yk.genieos.dfs.assignment-model.home-workorder', '作业工单'),
('assignment-model.02', '/assignment-model/workorder-records', 'new.yk.genieos.dfs.assignment-model.workorder-records', '作业工单报工记录'),
('battery.10601', '/battery-management/cellDataManagement', 'new.yk.genieos.dfs.battery-management.cellDataManagement', '电芯数据管理'),
('dfs10209', '/trace/device-trace', 'new.yk.genieos.ams.trace.device-trace', '设备追溯'),
('dfs10414', '/qualities-manage/processInspectRecord', 'new.yk.genieos.dfs.qualities-manage.processInspectRecord', '工序检验记录'),
('dfs12212', '/system_settings/pageRedirect', 'new.yk.genieos.dfs.system_settings.pageRedirect', '页面重定向'),
('dfs12213', '/system_settings/routeConf', 'new.yk.genieos.dfs.system_settings.routeConf', '菜单配置'),
('dfs12214', '/system_settings/orderPushDownConf', 'new.yk.genieos.dfs.system_settings.orderPushDownConf', '单据下推配置'),
('facility-maintain.01', '/facility-maintain/reworkQuality/define', 'new.yk.genieos.dfs.facility-maintain.reworkQuality.define', '维修定义'),
('facility-maintain.02', '/facility-maintain/reworkQuality', 'new.yk.genieos.dfs.facility-maintain.reworkQuality', '维修记录'),
('facility-maintain.03', '/facility-maintain/reworkQuality/maintainPlan/project', 'new.yk.genieos.dfs.facility-maintain.reworkQuality.mP.project', '维修方案'),
('facility-quality.01', '/facility-quality/processQuality/qualityDefinition', 'new.yk.genieos.dfs.fq.processQuality.qualityDefinition', '不良定义'),
('facility-quality.02', '/facility-quality/processQuality/qualityRecord', 'new.yk.genieos.dfs.facility-quality.processQuality.qualityRecord', '质检记录'),
('pis10420', '/inspection-manage/process/item', 'new.yk.genieos.pis.inspection-manage.process.item', '工序巡检项目'),
('pis10421', '/inspection-manage/process/scheme', 'new.yk.genieos.pis.inspection-manage.process.scheme', '工序巡检方案'),
('qms-product-inspection1000140', '/inspection-manage/qms-product-inspection/productionProcess', 'new.yk.genieos.pis.inspection-manage.process.task', '生产巡检任务'),
('pms1280010002', '/projectManagement/projectStageDefinition', 'new.yk.genieos.pms.projectManagement.projectStageDefinition', '项目阶段定义'),
('pms1280010003', '/projectManagement/projectStageTemplate', 'new.yk.genieos.pms.projectManagement.projectStageTemplate', '项目阶段模板'),
('pms1280010004', '/projectManagement/taskTemplate', 'new.yk.genieos.pms.projectManagement.taskTemplate', '任务模板'),
('qms-aftersale-inspection100010', '/inspection-manage/aftersale-inspection/defectBound', 'new.yk.genieos.aftersale.im.aftersale-inspection.defectBound', '缺陷绑定'),
('qms-product-inspection1000130', '/inspection-manage/qms-product-inspection/incomingInspect', 'new.yk..inspection-manage.qms-product-inspection.incomingInspect', '来料检验单'),
('qms-product-inspection1000140', '/inspection-manage/qms-product-inspection/productionProcess', 'new.yk. genieos.qms3.0.qms-product-inspection.productionProcess', '生产巡检单'),
('qms-product-inspection1000150', '/inspection-manage/qms-product-inspection/productionInspect', 'new.yk. genieos.qms3.0.qms-product-inspection.productionInspect', '生产检验单'),
('qms-product-inspection1000160', '/inspection-manage/qms-product-inspection/saleInspect', 'new.yk. genieos.qms3.0.qms-product-inspection.saleInspect', '销售检验单'),
('qms-product-inspection1000170', '/inspection-manage/qms-product-inspection/otherInspect', 'new.yk. genieos.qms3.0.qms-product-inspection.otherInspect', '其他检验单'),
('qms-product-inspection100030', '/inspection-manage/qms-product-inspection/project', 'new.yk.genieos.qms3.0.im.qms-product-inspection.project', '检验项目'),
('qms-product-inspection100040', '/inspection-manage/qms-product-inspection/samplePlane', 'new.yk.genieos.qms3.0.inspection-manage.qpi.samplePlane', '抽样方案'),
('qms-product-inspection100050', '/inspection-manage/qms-product-inspection/scheme', 'new.yk.genieos.qms3.0.inspection-manage.qpi.scheme', '检验方案'),
('qms-product-inspection100060', '/inspection-manage/qms-product-inspection/defect', 'new.yk.genieos.qms3.0.inspection-manage.qpi.defect', '缺陷定义'),
('qms-product-inspection100080', '/inspection-manage/qms-product-inspection/defectReason', 'new.yk.genieos.qms3.0.inspection-manage.qpi.defectReason', '缺陷原因'),
('qms-product-inspection100090', '/inspection-manage/qms-product-inspection/defectResult', 'new.yk.genieos.qms3.0.inspection-manage.qpi.defectResult', '缺陷后果'),
('qms-product-inspection100110', '/inspection-manage/qms-product-inspection/productionQualityReport', 'qms-product-inspection100110', '质量报表');


-- 生产订单导出_查看日志
INSERT INTO sys_permissions(id, name, `path`, description, create_by, create_time, update_by, update_time, status, `method`, parent_id, `type`, is_web, is_back_stage, parent_path, is_enable)
VALUES('10003500', '导出_查看日志', 'product.order:export:log', NULL, NULL, NOW(), NULL, NOW(), 'enable', 'GET', '10003', 2, 1, 0, '/order-model/product-order', 1);
call init_new_role_permission('10003500');

-- 新增两个精制接口
INSERT INTO `dfs_config_open_api`(`service`, `model`, `interface_code`, `interface_name`, `http_method`, `path`) VALUES ('jingzhi', 'jingzhi', 'bindRoleApp', '角色绑定小程序', 'POST', '/jingzhi/role/app/map');
INSERT INTO `dfs_config_open_api`(`service`, `model`, `interface_code`, `interface_name`, `http_method`, `path`) VALUES ('jingzhi', 'jingzhi', 'unbindRoleApp', '角色解绑小程序', 'DELETE', '/jingzhi/role/app/map');
INSERT INTO `dfs_config_open_api`(`service`, `model`, `interface_code`, `interface_name`, `http_method`, `path`) VALUES ('jingzhi', 'jingzhi', 'getAllRoleBindApps', '获取所有角色对应的在线小程序', 'GET', '/jingzhi/role/onlineApp/all');
INSERT INTO `dfs_config_open_api`(`service`, `model`, `interface_code`, `interface_name`, `http_method`, `path`) VALUES ('jingzhi', 'jingzhi', 'getRolesByApplicationId', '获取小程序的角色信息', 'GET', '/jingzhi/app/v2/roles');


-- 任务中心角色配置
INSERT INTO dfs_task_role_config (order_category, role_id) SELECT 'saleOrder', id from sys_roles WHERE name in ('管理员','系统运维','生产','经营');
INSERT INTO dfs_task_role_config (order_category, role_id) SELECT 'deliveryApplication', id from sys_roles WHERE name in ('管理员','系统运维','生产','经营');
INSERT INTO dfs_task_role_config (order_category, role_id) SELECT 'saleReturnOrder', id from sys_roles WHERE name in ('管理员','系统运维','生产','经营');
INSERT INTO dfs_task_role_config (order_category, role_id) SELECT 'productOrder', id from sys_roles WHERE name in ('管理员','系统运维','生产','经营');
INSERT INTO dfs_task_role_config (order_category, role_id) SELECT 'productMaterialsList', id from sys_roles WHERE name in ('管理员','系统运维','生产','经营');
INSERT INTO dfs_task_role_config (order_category, role_id) SELECT 'purchaseRequest', id from sys_roles WHERE name in ('管理员','系统运维','生产','经营');
INSERT INTO dfs_task_role_config (order_category, role_id) SELECT 'purchaseOrder', id from sys_roles WHERE name in ('管理员','系统运维','生产','经营');
INSERT INTO dfs_task_role_config (order_category, role_id) SELECT 'purchaseReceipt', id from sys_roles WHERE name in ('管理员','系统运维','生产','经营');
INSERT INTO dfs_task_role_config (order_category, role_id) SELECT 'returnOrder', id from sys_roles WHERE name in ('管理员','系统运维','生产','经营');
INSERT INTO dfs_task_role_config (order_category, role_id) SELECT 'subcontractOrder', id from sys_roles WHERE name in ('管理员','系统运维','生产','经营');
INSERT INTO dfs_task_role_config (order_category, role_id) SELECT 'inspectOrder', id from sys_roles WHERE name in ('管理员','系统运维','生产','经营');
INSERT INTO dfs_task_role_config (order_category, role_id) SELECT 'salesIssueDoc', id from sys_roles WHERE name in ('管理员','系统运维','生产','经营');
INSERT INTO dfs_task_role_config (order_category, role_id) SELECT 'salesReturnReceiptDoc', id from sys_roles WHERE name in ('管理员','系统运维','生产','经营');
INSERT INTO dfs_task_role_config (order_category, role_id) SELECT 'workOrderComplete', id from sys_roles WHERE name in ('管理员','系统运维','生产','经营');
INSERT INTO dfs_task_role_config (order_category, role_id) SELECT 'outputPickingProduct', id from sys_roles WHERE name in ('管理员','系统运维','生产','经营');
INSERT INTO dfs_task_role_config (order_category, role_id) SELECT 'workOrderSupplement', id from sys_roles WHERE name in ('管理员','系统运维','生产','经营');
INSERT INTO dfs_task_role_config (order_category, role_id) SELECT 'applicationReturn', id from sys_roles WHERE name in ('管理员','系统运维','生产','经营');
INSERT INTO dfs_task_role_config (order_category, role_id) SELECT 'purchaseIn', id from sys_roles WHERE name in ('管理员','系统运维','生产','经营');
INSERT INTO dfs_task_role_config (order_category, role_id) SELECT 'purchaseReturnOut', id from sys_roles WHERE name in ('管理员','系统运维','生产','经营');
INSERT INTO dfs_task_role_config (order_category, role_id) SELECT 'subcontractProductOutbound', id from sys_roles WHERE name in ('管理员','系统运维','生产','经营');
INSERT INTO dfs_task_role_config (order_category, role_id) SELECT 'subcontractSupplementaryFood', id from sys_roles WHERE name in ('管理员','系统运维','生产','经营');
INSERT INTO dfs_task_role_config (order_category, role_id) SELECT 'subcontractReturnReceipt', id from sys_roles WHERE name in ('管理员','系统运维','生产','经营');
INSERT INTO dfs_task_role_config (order_category, role_id) SELECT 'transferOrder', id from sys_roles WHERE name in ('管理员','系统运维','生产','经营');
INSERT INTO dfs_task_role_config (order_category, role_id) SELECT 'stockOtherAppForm', id from sys_roles WHERE name in ('管理员','系统运维','生产','经营');
INSERT INTO dfs_task_role_config (order_category, role_id) SELECT 'subcontractReceiptOrder', id from sys_roles WHERE name in ('管理员','系统运维','生产','经营');
INSERT INTO dfs_task_role_config (order_category, role_id) SELECT 'subcontractDeliveryOrder', id from sys_roles WHERE name in ('管理员','系统运维','生产','经营');
INSERT INTO dfs_task_role_config (order_category, role_id) SELECT 'subcontractMaterialsList', id from sys_roles WHERE name in ('管理员','系统运维','生产','经营');
INSERT INTO dfs_task_role_config (order_category, role_id) SELECT 'subcontractReturnOrder', id from sys_roles WHERE name in ('管理员','系统运维','生产','经营');

-- 表单配置的option_values字段无用，设置为空，避免影响到前端渲染
UPDATE `dfs_form_field_rule_config` SET `option_values` = null;

-- 任务地图更新颜色
UPDATE `dfs_order_category` SET `background_color` = '#D9E8FC' WHERE `order_category` in ('purchaseRequest','purchaseOrder','purchaseReceipt','returnOrder', 'purchaseIn', 'purchaseReturnOut', 'inspectOrder');
UPDATE `dfs_order_category` SET `background_color` = '#BBD5DE' WHERE `order_category` in ('subcontractOrder','subcontractProductOutbound','subcontractSupplementaryFood','subcontractReturnReceipt','subcontractReceiptOrder','subcontractDeliveryOrder','subcontractMaterialsList','subcontractReturnOrder');
UPDATE `dfs_order_category` SET `background_color` = '#FEDCC3' WHERE `order_category` in ('saleOrder','deliveryApplication','saleReturnOrder','salesIssueDoc','salesReturnReceiptDoc','transferOrder','stockOtherAppForm');
UPDATE `dfs_order_category` SET `background_color` = '#D8E9D4' WHERE `order_category` in ('productOrder','productMaterialsList','workOrder','workOrderMaterialList','workOrderComplete','outputPickingProduct','workOrderSupplement','applicationReturn');

-- 生产订单表单配置排程状态改成计划
UPDATE `dfs_form_field_config` SET `field_name` = '计划'  WHERE `full_path_code` = 'productOrder.list' and `field_code` = 'schedulingStatusName'  and  type_code = 'sysField';
UPDATE `dfs_form_field_config` SET `field_name` = '计划'   WHERE `full_path_code` = 'productOrder.edit' and `field_code` = 'schedulingStatusName'  and  type_code = 'sysField';
UPDATE `dfs_form_field_config` SET `field_name` = '计划'  WHERE `full_path_code` = 'productOrder.detail' and `field_code` = 'schedulingStatusName'  and  type_code = 'sysField';

-- 禁用审批配置菜单
update sys_permissions set is_enable = 0 where name = '审批配置';
