-- 由于路由大改动，下推路由需要变更
UPDATE `dfs_order_push_down_config_value` SET `value` = REPLACE(`value`, '/dfs/order-model/production-workorder','/dfs/workorder-model/production-workorder') WHERE `value_full_path_code` in ('saleOrder.pushDownConfig.workOrder.craftPush.url-2','production.productOrderPushDownConfig.workOrder.craftPush.url');
UPDATE `dfs_order_push_down_config_value_dict` SET `value` = REPLACE(`value`, '/dfs/order-model/production-workorder','/dfs/workorder-model/production-workorder') WHERE `value_full_path_code` in ('saleOrder.pushDownConfig.workOrder.craftPush.url-2','production.productOrderPushDownConfig.workOrder.craftPush.url');

INSERT INTO sys_permissions(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`, `is_sys_data`)
 VALUES ('12209070', '初始化', 'init.config:init', NULL, NULL, NULL, NULL, '2024-10-24 09:25:13', 'enable', 'GET', '12209', 2, 1, 0, '/product-management/auxiliary-attr', 1, NULL, '', 1);
call init_new_role_permission('12209070');

-- 本厂规格标识修改
update dfs_config_label_info set placeholder = '\$\{factoryStandard\}' where code = 'factoryStandard';

-- 下推配置菜单不是仓库的单据错误归到到了wms，现做变更处理
update `dfs_order_push_down_config` set service_name = '' where `full_path_code` in ('saleOrder.pushDownConfig.saleReturnOrder', 'purchase.purchaseReceiptPushDownConfig.returnOrder');