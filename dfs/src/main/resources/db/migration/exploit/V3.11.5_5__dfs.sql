-- 下推菜单新增字段
call proc_add_column(
        'dfs_order_push_down_config',
        'service_name',
        'ALTER TABLE `dfs_order_push_down_config` ADD COLUMN `service_name` varchar(255) DEFAULT '''' COMMENT ''服务名称''');

-- 删除无用的下推配置项
DELETE FROM `dfs_dict` WHERE `type` in ('purchase.purchaseOrderPushDownConfig.supplierBom', 'purchase.purchaseReceiptPushDownConfig.incomingInspection');
DELETE FROM `dfs_order_push_down_config` WHERE `full_path_code` in ('purchase.purchaseOrderPushDownConfig.supplierBom', 'purchase.purchaseReceiptPushDownConfig.incomingInspection');
DELETE FROM `dfs_order_push_down_item` WHERE `instance_type` in ('production.workOrderPushDownConfig.takeOutApplication.jumpPage', 'purchase.purchaseOrderPushDownConfig.supplierBom.jumpPage', 'purchase.purchaseReceiptPushDownConfig.incomingInspection.jumpPage');
DELETE FROM `dfs_order_push_down_config_value` WHERE `config_full_path_code` in ('purchase.purchaseOrderPushDownConfig.supplierBom.jumpPage', 'purchase.purchaseReceiptPushDownConfig.incomingInspection.jumpPage');
DELETE FROM `dfs_order_push_down_config_value_dict` WHERE `config_full_path_code` in ('purchase.purchaseOrderPushDownConfig.supplierBom.jumpPage', 'purchase.purchaseReceiptPushDownConfig.incomingInspection.jumpPage');

-- 下推配置默认开启,除了自动下推
UPDATE `dfs_order_push_down_item` SET `enable` = 1;
UPDATE `dfs_order_push_down_item` SET `enable` = 0  WHERE `instance_type` in ('production.productOrderPushDownConfig.workOrder.craftRouteAuto','saleOrder.pushDownConfig.productOrder.bomAuto');
-- 将第三方相关的下推数据，服务名设置为wms，质量服务名设置为qms
UPDATE `dfs_order_push_down_config` SET `service_name` = 'wms'
WHERE `full_path_code` in ('production.productMaterialsListPushDownConfig.outputPickingProduct','production.productMaterialsListPushDownConfig.workOrderSupplement','production.productMaterialsListPushDownConfig.transferOrder','production.productOrderPushDownConfig.productionIn','production.workOrderPushDownConfig.productStockInAndOut','purchase.purchaseOrderPushDownConfig.purchaseIn','purchase.purchaseReceiptPushDownConfig.requestStockInAndOut','purchase.purchaseReceiptPushDownConfig.returnOrder','purchase.purchaseReturnApplicationPushDownConfig.purchaseReturnOut','saleOrder.pushDownConfig.saleInAndOut','saleOrder.pushDownConfig.saleReturnOrder','production.takeOutApplicationPushDownConfig','production.workOrderMaterialsListPushDownConfig.takeOutOutbound','production.takeOutApplicationPushDownConfig.takeOutOutbound');
UPDATE `dfs_order_push_down_config` SET `service_name` = 'qms'
WHERE `full_path_code` in ('purchase.purchaseReceiptPushDownConfig.inspectOrder');

