-- 质检新增4个扩展字段
call proc_add_column(
        'dfs_record_work_order_unqualified',
        'record_work_order_unqualified_extend_one',
        'ALTER TABLE `dfs_record_work_order_unqualified` ADD COLUMN `record_work_order_unqualified_extend_one` varchar(255) DEFAULT NULL COMMENT ''质检扩展字段1''');
call proc_add_column(
        'dfs_record_work_order_unqualified',
        'record_work_order_unqualified_extend_two',
        'ALTER TABLE `dfs_record_work_order_unqualified` ADD COLUMN `record_work_order_unqualified_extend_two` varchar(255) DEFAULT NULL COMMENT ''质检扩展字段2''');
call proc_add_column(
        'dfs_record_work_order_unqualified',
        'record_work_order_unqualified_extend_three',
        'ALTER TABLE `dfs_record_work_order_unqualified` ADD COLUMN `record_work_order_unqualified_extend_three` varchar(255) DEFAULT NULL COMMENT ''质检扩展字段3''');
call proc_add_column(
        'dfs_record_work_order_unqualified',
        'record_work_order_unqualified_extend_four',
        'ALTER TABLE `dfs_record_work_order_unqualified` ADD COLUMN `record_work_order_unqualified_extend_four` varchar(255) DEFAULT NULL COMMENT ''质检扩展字段4''');
