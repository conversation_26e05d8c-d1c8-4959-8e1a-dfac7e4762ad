-- DDL

-- 工序定义相关
CREATE TABLE if NOT EXISTS `dfs_procedure_def_material` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `procedure_code` varchar(255) NOT NULL COMMENT '工序编码',
    `material_code` varchar(100) DEFAULT NULL COMMENT '物料编码',
    `material_name` text COMMENT '物料名称',
    `number` double(15,7) DEFAULT NULL COMMENT '用量',
    `unit` varchar(50) DEFAULT NULL COMMENT '单位',
    `extend` text COMMENT '扩展字段',
    `data_source` varchar(50) DEFAULT 'bomMaterial' COMMENT '数据来源',
    PRIMARY KEY (`id`) USING BTREE,
    KEY `material_code` (`material_code`),
    KEY `procedure_code` (`procedure_code`) USING BTREE
    ) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='工序定义用料';

CREATE TABLE if NOT EXISTS `dfs_procedure_def_device_type` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `procedure_code` varchar(255) NOT NULL COMMENT '工序编码',
    `device_model_id` int(11) DEFAULT NULL COMMENT '设备类型id',
    `device_type_name` varchar(50) DEFAULT NULL COMMENT '设备类型名称',
    `formula_num` varchar(100) DEFAULT NULL COMMENT '配方号',
    `formula_file_url` varchar(255) DEFAULT NULL COMMENT '配方文件url',
    `formula_file_name` varchar(255) DEFAULT NULL COMMENT '配方文件名称',
    `theoretical_speed` double(11,3) DEFAULT NULL COMMENT '理论生产速度',
    `unit` varchar(255) DEFAULT NULL COMMENT '单位',
    `number` int(11) DEFAULT '0' COMMENT '用量',
    PRIMARY KEY (`id`) USING BTREE,
    KEY `procedure_code` (`procedure_code`) USING BTREE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='工序定义设备类型关联表';

CREATE TABLE if NOT EXISTS `dfs_procedure_def_relation_work_hours` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `procedure_code` varchar(255) NOT NULL COMMENT '工序编码',
    `preparation_time` double(20,2) DEFAULT '0.00' COMMENT '准备工时',
    `processing_hours` double(20,2) DEFAULT '0.00' COMMENT '加工工时',
    `degree_of_difficulty` double(20,2) DEFAULT '1.00' COMMENT '难度系数',
    `default_time` double(20,2) DEFAULT '0.00' COMMENT '缺省工時',
    `is_by_work` tinyint(1) DEFAULT '0' COMMENT '是否按工時計算0-否，1-是',
    `preparation_time_unit` varchar(255) DEFAULT NULL COMMENT '准备工时单位',
    `processing_hours_unit` varchar(255) DEFAULT NULL COMMENT '加工工时单位',
    `default_time_unit` varchar(255) DEFAULT NULL COMMENT '缺省工时单位',
    PRIMARY KEY (`id`),
    KEY `procedure_code` (`procedure_code`) USING BTREE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='工序定义工时关联表';

CREATE TABLE if NOT EXISTS `dfs_procedure_def_file` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `procedure_code` varchar(255) NOT NULL COMMENT '工序编码',
    `file_url` varchar(255) DEFAULT NULL COMMENT '附件url',
    `name` varchar(255) DEFAULT NULL COMMENT '附件名称',
    `craft_id` int(11) DEFAULT NULL COMMENT '关联的工艺id',
    `create_by` varchar(255) DEFAULT NULL COMMENT '创建人',
    `update_by` varchar(255) DEFAULT NULL COMMENT '修改人',
    `create_time` datetime DEFAULT NULL COMMENT '创建时间',
    `update_time` datetime DEFAULT NULL COMMENT '修改时间',
    `editor` varchar(255) DEFAULT NULL COMMENT '编制人',
    PRIMARY KEY (`id`) USING BTREE,
    KEY `procedure_code` (`procedure_code`) USING BTREE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='工序定义附件';

CREATE TABLE if NOT EXISTS `dfs_procedure_def_inspection_controller_config` (
    `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '自增ID',
    `procedure_code` varchar(255) NOT NULL COMMENT '工序编码',
    `inspect_type_code` int(11) DEFAULT NULL COMMENT '检验类型编号',
    `inspect_type_name` varchar(100) DEFAULT NULL COMMENT '检验类型名称',
    `inspect_scheme_name` varchar(255) DEFAULT NULL COMMENT '检验方案名称（按逗号分隔）',
    `inspect_method_code` varchar(255) DEFAULT NULL COMMENT '检验方式编号（按逗号分隔）',
    `inspect_trigger_condition_code` varchar(750) DEFAULT NULL COMMENT '检验触发条件编号（按逗号分隔）',
    PRIMARY KEY (`id`) USING BTREE,
    KEY `procedure_code` (`procedure_code`) USING BTREE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='工序定义的检验控制表';


CREATE TABLE if NOT EXISTS `dfs_procedure_def_controller_config` (
    `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '自增ID',
    `procedure_code` varchar(255) NOT NULL COMMENT '工序编码',
    `increase_production_volume` double(20,2) DEFAULT '0.00' COMMENT '生产提前量',
    `standard_circulation_duration` double(20,2) DEFAULT '0.00' COMMENT '标准流转时长',
    `jump_station_check` tinyint(4) DEFAULT '0' COMMENT '跳站检查',
    `reform_check` tinyint(4) DEFAULT '0' COMMENT '重做检查',
    `material_check` tinyint(4) DEFAULT '0' COMMENT '物料检查',
    `production_check` tinyint(4) DEFAULT '0' COMMENT '投产检查',
    `craft_procedure_check` tinyint(4) DEFAULT '0' COMMENT '工艺工序检查',
    `update_by` varchar(50) DEFAULT NULL COMMENT '更新人',
    `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
    `update_time` datetime DEFAULT NULL COMMENT '更新时间',
    `create_time` datetime DEFAULT NULL COMMENT '创建时间',
    `flow_timeout_threshold_configuration` double(20,2) DEFAULT '0.00' COMMENT '流转超时阈值配置',
    `production_timeout_threshold_configuration` double(20,2) DEFAULT '0.00' COMMENT '生产超时阈值配置',
    `flow_timeout_threshold_configuration_unit_type` varchar(255) DEFAULT 'fixedValue' COMMENT '流转超时阈值配置单位类型(百分比/固定值)',
    `production_timeout_threshold_configuration_unit` varchar(255) DEFAULT 'fixedValue' COMMENT '生产超时阈值配置单位类型(百分比/固定值)',
    `max_fail_count` int(11) DEFAULT NULL COMMENT '最大失败次数',
    `conversion_factor` double(11,2) DEFAULT '1.00' COMMENT '换算系数',
    `last_value_check` tinyint(1) DEFAULT '0' COMMENT '工序互检',
    `signature_check` tinyint(1) DEFAULT '0' COMMENT '签名确认：0否，1是',
    `input_check` tinyint(1) DEFAULT '0' COMMENT '投入数检查',
    PRIMARY KEY (`id`) USING BTREE,
    KEY `procedure_code` (`procedure_code`) USING BTREE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='工序定义控制配置表';

CREATE TABLE if NOT EXISTS `dfs_procedure_def_process_assembly` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `procedure_code` varchar(255) NOT NULL COMMENT '工序编码',
    `process_assembly_code` varchar(100) DEFAULT NULL COMMENT '工装编号',
    `process_assembly_name` varchar(100) DEFAULT NULL COMMENT '工装名称',
    `process_assembly_type` varchar(100) DEFAULT NULL COMMENT '工装类型',
    `number` double(15,7) DEFAULT NULL COMMENT '工装用量',
    `unit` varchar(50) DEFAULT NULL COMMENT '单位',
    `remark` varchar(255) DEFAULT NULL COMMENT '备注',
    `update_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '更新人',
    `create_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '创建人',
    `update_time` datetime DEFAULT NULL COMMENT '更新时间',
    `create_time` datetime DEFAULT NULL COMMENT '创建时间',
    PRIMARY KEY (`id`),
    KEY `procedure_code` (`procedure_code`) USING BTREE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='工序定义工装配置表';

CREATE TABLE if NOT EXISTS `dfs_procedure_def_defect_scheme` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `procedure_code` varchar(255) NOT NULL COMMENT '工序编码',
    `scheme_id` int(11) DEFAULT NULL COMMENT '关联质检表ID',
    `scheme_name` varchar(255) DEFAULT NULL COMMENT '质检方案',
    `create_by` varchar(255) DEFAULT NULL COMMENT '创建人',
    `update_by` varchar(255) DEFAULT NULL COMMENT '修改人',
    `create_time` datetime DEFAULT NULL COMMENT '创建时间',
    `update_time` datetime DEFAULT NULL COMMENT '修改时间',
    PRIMARY KEY (`id`),
    KEY `procedure_code` (`procedure_code`) USING BTREE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='工序定义质检方案表';

CREATE TABLE if NOT EXISTS `dfs_procedure_def_maintain_scheme` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `procedure_code` varchar(255) NOT NULL COMMENT '工序编码',
    `maintain_id` int(11) DEFAULT NULL COMMENT '关联维修表ID',
    `maintain_name` varchar(255) DEFAULT NULL COMMENT '维修方案',
    `create_by` varchar(255) DEFAULT NULL COMMENT '创建人',
    `update_by` varchar(255) DEFAULT NULL COMMENT '修改人',
    `create_time` datetime DEFAULT NULL COMMENT '创建时间',
    `update_time` datetime DEFAULT NULL COMMENT '修改时间',
    PRIMARY KEY (`id`),
    KEY `procedure_code` (`procedure_code`) USING BTREE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='工序定义维修信息表';

CREATE TABLE if NOT EXISTS `dfs_procedure_def_post` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `procedure_code` varchar(255) NOT NULL COMMENT '工序编码',
    `post_id` int(11) DEFAULT NULL COMMENT '岗位表ID',
    `post_code` varchar(100) DEFAULT NULL COMMENT '岗位编号',
    `post_name` varchar(100) DEFAULT NULL COMMENT '岗位名称',
    `post_level` varchar(100) DEFAULT NULL COMMENT '岗位级别',
    `number` int(11) DEFAULT NULL COMMENT '数量',
    `unit` varchar(50) DEFAULT NULL COMMENT '单位',
    `remark` varchar(255) DEFAULT NULL COMMENT '备注',
    `update_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '更新人',
    `create_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '创建人',
    `update_time` datetime DEFAULT NULL COMMENT '更新时间',
    `create_time` datetime DEFAULT NULL COMMENT '创建时间',
    PRIMARY KEY (`id`),
    KEY `procedure_code` (`procedure_code`) USING BTREE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='工序定义人力表';

-- 新增审批通知类型
INSERT INTO `dfs_config_notice_level_relation` (`code`, `name`, `configurable_placeholder`, `third_party_conf_placeholder`, `parent_code`) VALUES ('craftApprovalNotice', '审批工艺通知', '{工艺编号},{工艺名称},{物料编号},{物料名称},{审批时间},{审批状态}', NULL, 'orderApprovalNotice');
INSERT INTO `dfs_config_notice_level_relation` (`code`, `name`, `configurable_placeholder`, `third_party_conf_placeholder`, `parent_code`) VALUES ('bomApprovalNotice', '审批bom通知', '{bom编号},{物料编号},{审批时间},{审批状态}', NULL, 'orderApprovalNotice');
INSERT INTO `dfs_config_notice_level_relation` (`code`, `name`, `configurable_placeholder`, `third_party_conf_placeholder`, `parent_code`) VALUES ('materialApprovalNotice', '审批物料通知', '{物料编号},{物料名称},{审批时间},{审批状态}', NULL, 'orderApprovalNotice');



-- 工单排产表单配置
INSERT INTO `dfs_form_config`(`id`, `type`, `is_name_tree`, `code`, `name`, `full_path_code`, `parent_full_path_code`, `description`, `create_by`, `update_by`, `create_time`, `update_time`) VALUES (NULL, 'web', NULL, 'workScheduleProduction', '工单排产', 'workScheduleProduction', '', NULL, 'admin', 'admin', NOW(), NOW());
INSERT INTO `dfs_form_config`(`id`, `type`, `is_name_tree`, `code`, `name`, `full_path_code`, `parent_full_path_code`, `description`, `create_by`, `update_by`, `create_time`, `update_time`) VALUES (NULL, 'web', NULL, 'toBeScheduledList', '待排区列表', 'workScheduleProduction.toBeScheduledList', 'workScheduleProduction', NULL, 'admin', 'admin', NOW(), NOW());
INSERT INTO `dfs_form_config`(`id`, `type`, `is_name_tree`, `code`, `name`, `full_path_code`, `parent_full_path_code`, `description`, `create_by`, `update_by`, `create_time`, `update_time`) VALUES (NULL, 'web', NULL, 'scheduledList', '已排区列表', 'workScheduleProduction.scheduledList', 'workScheduleProduction', NULL, 'admin', 'admin', NOW(), NOW());
-- 工单排产相关字段配置
call proc_add_form_field("workScheduleProduction.toBeScheduledList", "workOrderName", "工单名称");
call proc_add_form_field("workScheduleProduction.toBeScheduledList", "workOrderNumber", "工单编号");
call proc_add_form_field("workScheduleProduction.toBeScheduledList", "productOrderNumber", "关联生产订单");
call proc_add_form_field("workScheduleProduction.toBeScheduledList", "saleOrderNumber", "关联销售订单");
call proc_add_form_field("workScheduleProduction.toBeScheduledList", "stateName", "工单状态");
call proc_add_form_field("workScheduleProduction.toBeScheduledList", "customerCode", "客户编码");
call proc_add_form_field("workScheduleProduction.toBeScheduledList", "customerName", "客户名称");
call proc_add_form_field("workScheduleProduction.toBeScheduledList", "materialCode", "物料编码");
call proc_add_form_field("workScheduleProduction.toBeScheduledList", "materialName", "物料名称");
call proc_add_form_field("workScheduleProduction.toBeScheduledList", "materialStandard", "物料规格");
call proc_add_form_field("workScheduleProduction.toBeScheduledList", "comp", "单位");
call proc_add_form_field("workScheduleProduction.toBeScheduledList", "planQuantity", "计划生产数量");
call proc_add_form_field("workScheduleProduction.toBeScheduledList", "procedureName", "工序名称");
call proc_add_form_field("workScheduleProduction.toBeScheduledList", "workCenterName", "工作中心");
call proc_add_form_field("workScheduleProduction.toBeScheduledList", "schedulingStateName", "排产状态");
call proc_add_form_field("workScheduleProduction.toBeScheduledList", "planTheoryHour", "计划理论工时");
call proc_add_form_field("workScheduleProduction.toBeScheduledList", "planProductEndTime", "订单计划时间");
call proc_add_form_field("workScheduleProduction.toBeScheduledList", "startDate", "计划开始时间");
call proc_add_form_field("workScheduleProduction.toBeScheduledList", "endDate", "计划完成时间");
call proc_add_form_field("workScheduleProduction.toBeScheduledList", "requireGoodsDate", "交货时间");
call proc_add_form_field("workScheduleProduction.toBeScheduledList", "orderDate", "下单时间");
call proc_add_form_field("workScheduleProduction.toBeScheduledList", "remark", "备注");
call proc_add_form_field("workScheduleProduction.toBeScheduledList", "planDays", "生产计划天数");
call proc_add_form_field("workScheduleProduction.toBeScheduledList", "workOrderExtendFieldOne", "工单拓展字段1");
call proc_add_form_field("workScheduleProduction.toBeScheduledList", "workOrderExtendFieldTwo", "工单拓展字段2");
call proc_add_form_field("workScheduleProduction.toBeScheduledList", "workOrderExtendFieldThree", "工单拓展字段3");
call proc_add_form_field("workScheduleProduction.toBeScheduledList", "workOrderExtendFieldFour", "工单拓展字段4");
call proc_add_form_field("workScheduleProduction.toBeScheduledList", "workOrderExtendFieldFive", "工单拓展字段5");
call proc_add_form_field("workScheduleProduction.toBeScheduledList", "workOrderExtendFieldSix", "工单拓展字段6");
call proc_add_form_field("workScheduleProduction.toBeScheduledList", "workOrderExtendFieldSeven", "工单拓展字段7");
call proc_add_form_field("workScheduleProduction.toBeScheduledList", "workOrderExtendFieldEight", "工单拓展字段8");
call proc_add_form_field("workScheduleProduction.toBeScheduledList", "workOrderExtendFieldNine", "工单拓展字段9");
call proc_add_form_field("workScheduleProduction.toBeScheduledList", "workOrderExtendFieldTen", "工单拓展字段10");
call proc_add_form_field("workScheduleProduction.toBeScheduledList", "workOrderMaterialExtendFieldOne", "工单物料拓展字段1");
call proc_add_form_field("workScheduleProduction.toBeScheduledList", "workOrderMaterialExtendFieldTwo", "工单物料拓展字段2");
call proc_add_form_field("workScheduleProduction.toBeScheduledList", "workOrderMaterialExtendFieldThree", "工单物料拓展字段3");
call proc_add_form_field("workScheduleProduction.toBeScheduledList", "workOrderMaterialExtendFieldFour", "工单物料拓展字段4");
call proc_add_form_field("workScheduleProduction.toBeScheduledList", "workOrderMaterialExtendFieldFive", "工单物料拓展字段5");
call proc_add_form_field("workScheduleProduction.toBeScheduledList", "workOrderMaterialExtendFieldSix", "工单物料拓展字段6");
call proc_add_form_field("workScheduleProduction.toBeScheduledList", "workOrderMaterialExtendFieldSeven", "工单物料拓展字段7");
call proc_add_form_field("workScheduleProduction.toBeScheduledList", "workOrderMaterialExtendFieldEight", "工单物料拓展字段8");
call proc_add_form_field("workScheduleProduction.toBeScheduledList", "workOrderMaterialExtendFieldNine", "工单物料拓展字段9");
call proc_add_form_field("workScheduleProduction.toBeScheduledList", "workOrderMaterialExtendFieldTen", "工单物料拓展字段10");

call proc_add_form_field("workScheduleProduction.scheduledList", "sep", "执行顺序");
call proc_add_form_field("workScheduleProduction.scheduledList", "workOrderName", "工单名称");
call proc_add_form_field("workScheduleProduction.scheduledList", "workOrderNumber", "工单编号");
call proc_add_form_field("workScheduleProduction.scheduledList", "productOrderNumber", "关联生产订单");
call proc_add_form_field("workScheduleProduction.scheduledList", "saleOrderNumber", "关联销售订单");
call proc_add_form_field("workScheduleProduction.scheduledList", "stateName", "工单状态");
call proc_add_form_field("workScheduleProduction.scheduledList", "customerCode", "客户编码");
call proc_add_form_field("workScheduleProduction.scheduledList", "customerName", "客户名称");
call proc_add_form_field("workScheduleProduction.scheduledList", "materialCode", "物料编码");
call proc_add_form_field("workScheduleProduction.scheduledList", "materialName", "物料名称");
call proc_add_form_field("workScheduleProduction.scheduledList", "materialStandard", "物料规格");
call proc_add_form_field("workScheduleProduction.scheduledList", "comp", "单位");
call proc_add_form_field("workScheduleProduction.scheduledList", "planQuantity", "计划生产数量");
call proc_add_form_field("workScheduleProduction.scheduledList", "procedureName", "工序名称");
call proc_add_form_field("workScheduleProduction.scheduledList", "workCenterName", "工作中心");
call proc_add_form_field("workScheduleProduction.scheduledList", "schedulingStateName", "排产状态");
call proc_add_form_field("workScheduleProduction.scheduledList", "planTheoryHour", "计划理论工时");
call proc_add_form_field("workScheduleProduction.scheduledList", "planProductEndTime", "订单计划时间");
call proc_add_form_field("workScheduleProduction.scheduledList", "startDate", "计划开始时间");
call proc_add_form_field("workScheduleProduction.scheduledList", "endDate", "计划完成时间");
call proc_add_form_field("workScheduleProduction.scheduledList", "requireGoodsDate", "交货时间");
call proc_add_form_field("workScheduleProduction.scheduledList", "orderDate", "下单时间");
call proc_add_form_field("workScheduleProduction.scheduledList", "remark", "备注");
call proc_add_form_field("workScheduleProduction.scheduledList", "workOrderExtendFieldOne", "工单拓展字段1");
call proc_add_form_field("workScheduleProduction.scheduledList", "workOrderExtendFieldTwo", "工单拓展字段2");
call proc_add_form_field("workScheduleProduction.scheduledList", "workOrderExtendFieldThree", "工单拓展字段3");
call proc_add_form_field("workScheduleProduction.scheduledList", "workOrderExtendFieldFour", "工单拓展字段4");
call proc_add_form_field("workScheduleProduction.scheduledList", "workOrderExtendFieldFive", "工单拓展字段5");
call proc_add_form_field("workScheduleProduction.scheduledList", "workOrderExtendFieldSix", "工单拓展字段6");
call proc_add_form_field("workScheduleProduction.scheduledList", "workOrderExtendFieldSeven", "工单拓展字段7");
call proc_add_form_field("workScheduleProduction.scheduledList", "workOrderExtendFieldEight", "工单拓展字段8");
call proc_add_form_field("workScheduleProduction.scheduledList", "workOrderExtendFieldNine", "工单拓展字段9");
call proc_add_form_field("workScheduleProduction.scheduledList", "workOrderExtendFieldTen", "工单拓展字段10");
call proc_add_form_field("workScheduleProduction.scheduledList", "workOrderMaterialExtendFieldOne", "工单物料拓展字段1");
call proc_add_form_field("workScheduleProduction.scheduledList", "workOrderMaterialExtendFieldTwo", "工单物料拓展字段2");
call proc_add_form_field("workScheduleProduction.scheduledList", "workOrderMaterialExtendFieldThree", "工单物料拓展字段3");
call proc_add_form_field("workScheduleProduction.scheduledList", "workOrderMaterialExtendFieldFour", "工单物料拓展字段4");
call proc_add_form_field("workScheduleProduction.scheduledList", "workOrderMaterialExtendFieldFive", "工单物料拓展字段5");
call proc_add_form_field("workScheduleProduction.scheduledList", "workOrderMaterialExtendFieldSix", "工单物料拓展字段6");
call proc_add_form_field("workScheduleProduction.scheduledList", "workOrderMaterialExtendFieldSeven", "工单物料拓展字段7");
call proc_add_form_field("workScheduleProduction.scheduledList", "workOrderMaterialExtendFieldEight", "工单物料拓展字段8");
call proc_add_form_field("workScheduleProduction.scheduledList", "workOrderMaterialExtendFieldNine", "工单物料拓展字段9");
call proc_add_form_field("workScheduleProduction.scheduledList", "workOrderMaterialExtendFieldTen", "工单物料拓展字段10");


INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/work-schedule-production', 'workScheduleProduction.toBeScheduledList', "workScheduleProduction.toBeScheduledList", "workOrderName", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/work-schedule-production', 'workScheduleProduction.toBeScheduledList', "workScheduleProduction.toBeScheduledList", "workOrderNumber", 1, 0, 0, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/work-schedule-production', 'workScheduleProduction.toBeScheduledList', "workScheduleProduction.toBeScheduledList", "productOrderNumber", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/work-schedule-production', 'workScheduleProduction.toBeScheduledList', "workScheduleProduction.toBeScheduledList", "saleOrderNumber", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/work-schedule-production', 'workScheduleProduction.toBeScheduledList', "workScheduleProduction.toBeScheduledList", "stateName", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/work-schedule-production', 'workScheduleProduction.toBeScheduledList', "workScheduleProduction.toBeScheduledList", "customerCode", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/work-schedule-production', 'workScheduleProduction.toBeScheduledList', "workScheduleProduction.toBeScheduledList", "customerName", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/work-schedule-production', 'workScheduleProduction.toBeScheduledList', "workScheduleProduction.toBeScheduledList", "materialCode", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/work-schedule-production', 'workScheduleProduction.toBeScheduledList', "workScheduleProduction.toBeScheduledList", "materialName", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/work-schedule-production', 'workScheduleProduction.toBeScheduledList', "workScheduleProduction.toBeScheduledList", "materialStandard", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/work-schedule-production', 'workScheduleProduction.toBeScheduledList', "workScheduleProduction.toBeScheduledList", "comp", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/work-schedule-production', 'workScheduleProduction.toBeScheduledList', "workScheduleProduction.toBeScheduledList", "planQuantity", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/work-schedule-production', 'workScheduleProduction.toBeScheduledList', "workScheduleProduction.toBeScheduledList", "procedureName", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/work-schedule-production', 'workScheduleProduction.toBeScheduledList', "workScheduleProduction.toBeScheduledList", "workCenterName", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/work-schedule-production', 'workScheduleProduction.toBeScheduledList', "workScheduleProduction.toBeScheduledList", "schedulingStateName", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/work-schedule-production', 'workScheduleProduction.toBeScheduledList', "workScheduleProduction.toBeScheduledList", "planTheoryHour", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/work-schedule-production', 'workScheduleProduction.toBeScheduledList', "workScheduleProduction.toBeScheduledList", "planProductEndTime", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/work-schedule-production', 'workScheduleProduction.toBeScheduledList', "workScheduleProduction.toBeScheduledList", "startDate", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/work-schedule-production', 'workScheduleProduction.toBeScheduledList', "workScheduleProduction.toBeScheduledList", "endDate", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/work-schedule-production', 'workScheduleProduction.toBeScheduledList', "workScheduleProduction.toBeScheduledList", "requireGoodsDate", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/work-schedule-production', 'workScheduleProduction.toBeScheduledList', "workScheduleProduction.toBeScheduledList", "orderDate", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/work-schedule-production', 'workScheduleProduction.toBeScheduledList', "workScheduleProduction.toBeScheduledList", "remark", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/work-schedule-production', 'workScheduleProduction.toBeScheduledList', "workScheduleProduction.toBeScheduledList", "planDays", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/work-schedule-production', 'workScheduleProduction.toBeScheduledList', "workScheduleProduction.toBeScheduledList", "workOrderExtendFieldOne", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/work-schedule-production', 'workScheduleProduction.toBeScheduledList', "workScheduleProduction.toBeScheduledList", "workOrderExtendFieldTwo", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/work-schedule-production', 'workScheduleProduction.toBeScheduledList', "workScheduleProduction.toBeScheduledList", "workOrderExtendFieldThree", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/work-schedule-production', 'workScheduleProduction.toBeScheduledList', "workScheduleProduction.toBeScheduledList", "workOrderExtendFieldFour", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/work-schedule-production', 'workScheduleProduction.toBeScheduledList', "workScheduleProduction.toBeScheduledList", "workOrderExtendFieldFive", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/work-schedule-production', 'workScheduleProduction.toBeScheduledList', "workScheduleProduction.toBeScheduledList", "workOrderExtendFieldSix", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/work-schedule-production', 'workScheduleProduction.toBeScheduledList', "workScheduleProduction.toBeScheduledList", "workOrderExtendFieldSeven", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/work-schedule-production', 'workScheduleProduction.toBeScheduledList', "workScheduleProduction.toBeScheduledList", "workOrderExtendFieldEight", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/work-schedule-production', 'workScheduleProduction.toBeScheduledList', "workScheduleProduction.toBeScheduledList", "workOrderExtendFieldNine", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/work-schedule-production', 'workScheduleProduction.toBeScheduledList', "workScheduleProduction.toBeScheduledList", "workOrderExtendFieldTen", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/work-schedule-production', 'workScheduleProduction.toBeScheduledList', "workScheduleProduction.toBeScheduledList", "workOrderMaterialExtendFieldOne", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/work-schedule-production', 'workScheduleProduction.toBeScheduledList', "workScheduleProduction.toBeScheduledList", "workOrderMaterialExtendFieldTwo", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/work-schedule-production', 'workScheduleProduction.toBeScheduledList', "workScheduleProduction.toBeScheduledList", "workOrderMaterialExtendFieldThree", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/work-schedule-production', 'workScheduleProduction.toBeScheduledList', "workScheduleProduction.toBeScheduledList", "workOrderMaterialExtendFieldFour", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/work-schedule-production', 'workScheduleProduction.toBeScheduledList', "workScheduleProduction.toBeScheduledList", "workOrderMaterialExtendFieldFive", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/work-schedule-production', 'workScheduleProduction.toBeScheduledList', "workScheduleProduction.toBeScheduledList", "workOrderMaterialExtendFieldSix", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/work-schedule-production', 'workScheduleProduction.toBeScheduledList', "workScheduleProduction.toBeScheduledList", "workOrderMaterialExtendFieldSeven", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/work-schedule-production', 'workScheduleProduction.toBeScheduledList', "workScheduleProduction.toBeScheduledList", "workOrderMaterialExtendFieldEight", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/work-schedule-production', 'workScheduleProduction.toBeScheduledList', "workScheduleProduction.toBeScheduledList", "workOrderMaterialExtendFieldNine", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/work-schedule-production', 'workScheduleProduction.toBeScheduledList', "workScheduleProduction.toBeScheduledList", "workOrderMaterialExtendFieldTen", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);

INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/work-schedule-production', 'workScheduleProduction.scheduledList', "workScheduleProduction.scheduledList", "sep", 1, 0, 0, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/work-schedule-production', 'workScheduleProduction.scheduledList', "workScheduleProduction.scheduledList", "workOrderName", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/work-schedule-production', 'workScheduleProduction.scheduledList', "workScheduleProduction.scheduledList", "workOrderNumber", 1, 0, 0, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/work-schedule-production', 'workScheduleProduction.scheduledList', "workScheduleProduction.scheduledList", "productOrderNumber", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/work-schedule-production', 'workScheduleProduction.scheduledList', "workScheduleProduction.scheduledList", "saleOrderNumber", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/work-schedule-production', 'workScheduleProduction.scheduledList', "workScheduleProduction.scheduledList", "stateName", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/work-schedule-production', 'workScheduleProduction.scheduledList', "workScheduleProduction.scheduledList", "customerCode", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/work-schedule-production', 'workScheduleProduction.scheduledList', "workScheduleProduction.scheduledList", "customerName", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/work-schedule-production', 'workScheduleProduction.scheduledList', "workScheduleProduction.scheduledList", "materialCode", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/work-schedule-production', 'workScheduleProduction.scheduledList', "workScheduleProduction.scheduledList", "materialName", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/work-schedule-production', 'workScheduleProduction.scheduledList', "workScheduleProduction.scheduledList", "materialStandard", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/work-schedule-production', 'workScheduleProduction.scheduledList', "workScheduleProduction.scheduledList", "comp", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/work-schedule-production', 'workScheduleProduction.scheduledList', "workScheduleProduction.scheduledList", "planQuantity", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/work-schedule-production', 'workScheduleProduction.scheduledList', "workScheduleProduction.scheduledList", "procedureName", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/work-schedule-production', 'workScheduleProduction.scheduledList', "workScheduleProduction.scheduledList", "workCenterName", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/work-schedule-production', 'workScheduleProduction.scheduledList', "workScheduleProduction.scheduledList", "schedulingStateName", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/work-schedule-production', 'workScheduleProduction.scheduledList', "workScheduleProduction.scheduledList", "planTheoryHour", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/work-schedule-production', 'workScheduleProduction.scheduledList', "workScheduleProduction.scheduledList", "planProductEndTime", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/work-schedule-production', 'workScheduleProduction.scheduledList', "workScheduleProduction.scheduledList", "startDate", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/work-schedule-production', 'workScheduleProduction.scheduledList', "workScheduleProduction.scheduledList", "endDate", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/work-schedule-production', 'workScheduleProduction.scheduledList', "workScheduleProduction.scheduledList", "requireGoodsDate", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/work-schedule-production', 'workScheduleProduction.scheduledList', "workScheduleProduction.scheduledList", "orderDate", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/work-schedule-production', 'workScheduleProduction.scheduledList', "workScheduleProduction.scheduledList", "remark", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/work-schedule-production', 'workScheduleProduction.scheduledList', "workScheduleProduction.scheduledList", "workOrderExtendFieldOne", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/work-schedule-production', 'workScheduleProduction.scheduledList', "workScheduleProduction.scheduledList", "workOrderExtendFieldTwo", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/work-schedule-production', 'workScheduleProduction.scheduledList', "workScheduleProduction.scheduledList", "workOrderExtendFieldThree", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/work-schedule-production', 'workScheduleProduction.scheduledList', "workScheduleProduction.scheduledList", "workOrderExtendFieldFour", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/work-schedule-production', 'workScheduleProduction.scheduledList', "workScheduleProduction.scheduledList", "workOrderExtendFieldFive", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/work-schedule-production', 'workScheduleProduction.scheduledList', "workScheduleProduction.scheduledList", "workOrderExtendFieldSix", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/work-schedule-production', 'workScheduleProduction.scheduledList', "workScheduleProduction.scheduledList", "workOrderExtendFieldSeven", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/work-schedule-production', 'workScheduleProduction.scheduledList', "workScheduleProduction.scheduledList", "workOrderExtendFieldEight", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/work-schedule-production', 'workScheduleProduction.scheduledList', "workScheduleProduction.scheduledList", "workOrderExtendFieldNine", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/work-schedule-production', 'workScheduleProduction.scheduledList', "workScheduleProduction.scheduledList", "workOrderExtendFieldTen", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/work-schedule-production', 'workScheduleProduction.scheduledList', "workScheduleProduction.scheduledList", "workOrderMaterialExtendFieldOne", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/work-schedule-production', 'workScheduleProduction.scheduledList', "workScheduleProduction.scheduledList", "workOrderMaterialExtendFieldTwo", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/work-schedule-production', 'workScheduleProduction.scheduledList', "workScheduleProduction.scheduledList", "workOrderMaterialExtendFieldThree", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/work-schedule-production', 'workScheduleProduction.scheduledList', "workScheduleProduction.scheduledList", "workOrderMaterialExtendFieldFour", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/work-schedule-production', 'workScheduleProduction.scheduledList', "workScheduleProduction.scheduledList", "workOrderMaterialExtendFieldFive", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/work-schedule-production', 'workScheduleProduction.scheduledList', "workScheduleProduction.scheduledList", "workOrderMaterialExtendFieldSix", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/work-schedule-production', 'workScheduleProduction.scheduledList', "workScheduleProduction.scheduledList", "workOrderMaterialExtendFieldSeven", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/work-schedule-production', 'workScheduleProduction.scheduledList', "workScheduleProduction.scheduledList", "workOrderMaterialExtendFieldEight", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/work-schedule-production', 'workScheduleProduction.scheduledList', "workScheduleProduction.scheduledList", "workOrderMaterialExtendFieldNine", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/work-schedule-production', 'workScheduleProduction.scheduledList', "workScheduleProduction.scheduledList", "workOrderMaterialExtendFieldTen", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);

-- 采购收料-标签打印权限
INSERT INTO `sys_permissions` (`id`, `name`, `path`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`)
VALUES ('1090206160', '标签打印', 'delivery.order:labelPrint', NULL, NOW(), NULL, NOW(), 'enable', 'GET', '1090206', '2', '1', '0', '/supply-chain-collaboration/procurement-management/delivery-order', '1', NULL, '');
call init_new_role_permission('1090206160');
-- 采购收料单标签打印增加批次关联检验单号
INSERT INTO `dfs_config_label_info`(`code`, `name`, `placeholder`, `is_sys_field`) VALUES ('relateInspectOrder', '批次关联检验单号', '\$\{relateInspectOrder\}', 1);
INSERT INTO `dfs_config_label_type_info_relate`(`label_type_code`, `label_info_code`) VALUES ('purchase', 'relateInspectOrder');