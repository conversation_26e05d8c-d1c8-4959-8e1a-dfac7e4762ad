-- 删除冗余的表单配置
DELETE FROM `dfs_form_field_config` WHERE `full_path_code`='workOrderReport.detail';
DELETE FROM `dfs_form_field_rule_config` WHERE `full_path_code`='workOrderReport.detail';
DELETE FROM `dfs_form_config` WHERE `full_path_code`='workOrderReport.detail';

-- 销售出货单 物料扩展字段
INSERT INTO dfs_form_field_module_config (id, full_path_code, field_name_full_path_code, module_code, module_name, have_save_button, have_add_button) VALUES(null, 'deliveryApplication.detail', 'deliveryApplication.detail', 'materialExtendField', '物料扩展字段', 0, 0);
INSERT INTO dfs_form_field_module_config (id, full_path_code, field_name_full_path_code, module_code, module_name, have_save_button, have_add_button) VALUES(null, 'deliveryApplication.editByCancel', 'deliveryApplication.edit', 'materialExtendField', '物料扩展字段', 0, 0);
INSERT INTO dfs_form_field_module_config (id, full_path_code, field_name_full_path_code, module_code, module_name, have_save_button, have_add_button) VALUES(null, 'deliveryApplication.editByClosed', 'deliveryApplication.edit', 'materialExtendField', '物料扩展字段', 0, 0);
INSERT INTO dfs_form_field_module_config (id, full_path_code, field_name_full_path_code, module_code, module_name, have_save_button, have_add_button) VALUES(null, 'deliveryApplication.editByCreate', 'deliveryApplication.edit', 'materialExtendField', '物料扩展字段', 0, 0);
INSERT INTO dfs_form_field_module_config (id, full_path_code, field_name_full_path_code, module_code, module_name, have_save_button, have_add_button) VALUES(null, 'deliveryApplication.editByFinish', 'deliveryApplication.edit', 'materialExtendField', '物料扩展字段', 0, 0);
INSERT INTO dfs_form_field_module_config (id, full_path_code, field_name_full_path_code, module_code, module_name, have_save_button, have_add_button) VALUES(null, 'deliveryApplication.editByRelease', 'deliveryApplication.edit', 'materialExtendField', '物料扩展字段', 0, 0);
INSERT INTO dfs_form_field_module_config (id, full_path_code, field_name_full_path_code, module_code, module_name, have_save_button, have_add_button) VALUES(null, 'deliveryApplication.list.material', 'deliveryApplication.list.material', 'materialExtendField', '物料扩展字段', 0, 0);

