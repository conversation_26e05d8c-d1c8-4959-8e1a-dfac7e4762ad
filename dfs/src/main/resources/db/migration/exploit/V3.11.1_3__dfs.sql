-- ================================================此脚本专为条码相关sql，数据较大，如需写脚本，请移步到3.11.1.1=======================================================
-- ================================================此脚本专为条码相关sql，数据较，请移步到3.11.1.1=======================================================
-- ================================================此脚本专为条码相关sql，数据较，请移步到3.11.1.1=======================================================
-- ================================================此脚本专为条码相关sql，数据较，请移步到3.11.1.1=======================================================
-- ================================================此脚本专为条码相关sql，数据较，请移步到3.11.1.1=======================================================

-- 删除冗余数据
DELETE
FROM `dfs_code_report`
WHERE `id` NOT IN (
    SELECT id
    FROM (
             SELECT MIN(id) as id
             FROM `dfs_code_report`
             GROUP BY `relation_number`, `product_flow_code`, `version`
         ) as temp
);

-- 删除现有的索引
ALTER TABLE `dfs_code_report`
    DROP INDEX `relation_number`;

-- 添加唯一索引
ALTER TABLE `dfs_code_report`
    ADD UNIQUE INDEX `unique_relation_number` (`relation_number`, `product_flow_code`, `version`);


-- ================================================此脚本专为条码相关sql，数据较，请移步到3.11.1.1=======================================================
-- ================================================此脚本专为条码相关sql，数据较，请移步到3.11.1.1=======================================================
-- ================================================此脚本专为条码相关sql，数据较，请移步到3.11.1.1=======================================================
-- ================================================此脚本专为条码相关sql，数据较，请移步到3.11.1.1=======================================================
-- ================================================此脚本专为条码相关sql，数据较，请移步到3.11.1.1=======================================================