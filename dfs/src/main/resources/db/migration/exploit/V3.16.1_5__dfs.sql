-- DDL
CREATE TABLE IF NOT EXISTS `dfs_rule_type_config`
(
    `id`               bigint                                  NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `type`             varchar(100) COLLATE utf8mb4_general_ci NOT NULL COMMENT '编号规则类型编码',
    `module_name`      varchar(100) COLLATE utf8mb4_general_ci NOT NULL COMMENT '模块名称',
    `parent_type_code` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '父级类型编码',
    `create_time`      datetime                                DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`      datetime                                DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `remark`           varchar(512) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '备注',
    `type_code`        varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '类型编码(1、用于判断编码规则是否加1使用2、用来解释历史类型编码type所代表的含义)',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_type` (`type`) COMMENT '类型编码唯一索引'
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='编码规则类型配置表';
