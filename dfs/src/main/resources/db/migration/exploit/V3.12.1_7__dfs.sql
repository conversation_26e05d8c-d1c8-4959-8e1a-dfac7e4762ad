-- ================================================此脚本专为权限需求设定，如需写脚本，请移步到3.12.1.1=======================================================
-- ================================================此脚本专为权限需求设定，如需写脚本，请移步到3.12.1.1=======================================================
-- ================================================此脚本专为权限需求设定，如需写脚本，请移步到3.12.1.1=======================================================
DROP PROCEDURE if EXISTS `_inner_role_permission_add`;
delimiter $$
CREATE DEFINER=`root`@`%` PROCEDURE `_inner_role_permission_add`(in permissionPath varchar(255), in roleId varchar(255))
BEGIN
	DECLARE done2 int DEFAULT TRUE;
	DECLARE currentPathId varchar(100);
	-- 定义游标， 这里找出当前路径下的祖孙三代
	DECLARE cur2 CURSOR FOR SELECT parent_id from sys_permissions  WHERE `path` = permissionPath UNION SELECT id from sys_permissions  WHERE `parent_path` = permissionPath OR `path` = permissionPath;
    -- 声明当游标遍历完后将标志变量置为某个值
    DECLARE CONTINUE HANDLER FOR NOT FOUND SET done2 = FALSE;

	-- 打开游标, 处理子权限
    OPEN cur2;
    WHILE done2 do
        -- 将游标中的值赋值给变量，注意：变量名不要与sql返回的列名相同，变量顺序要和sql结果列的顺序一致
        FETCH cur2 INTO currentPathId;
        -- 游标还未结束
        IF (done2) THEN
            -- 执行业务逻辑
            SET @num = (select count(1) from `sys_role_permission` where role_id = roleId and permission_id = currentPathId);
            -- 角色权限不存在才新增
            IF (@num = 0) THEN
                INSERT into sys_role_permission(`role_id`, `permission_id`) VALUES(roleId, currentPathId);
            END IF;
        END IF;
    END WHILE;

END $$
delimiter;

-- 角色权限批量插入：permissionPath-权限路径，roleNameArr-角色名称列表(按,分隔)
DROP PROCEDURE if EXISTS role_permission_add;
delimiter $$
CREATE DEFINER=`root`@`%` PROCEDURE `role_permission_add`(in permissionPath varchar(255), in roleNameArr varchar(255))
BEGIN

	DECLARE done1 int DEFAULT TRUE;
	DECLARE roleId varchar(100);
    -- 定义游标, 这里找出角色名称列表(按,分隔)的所有id
	DECLARE cur1 CURSOR FOR SELECT id from sys_roles WHERE  FIND_IN_SET(`name`,REPLACE(roleNameArr,' ',''));
    -- 声明当游标遍历完后将标志变量置为某个值
    DECLARE CONTINUE HANDLER FOR NOT FOUND SET done1 = FALSE;
        -- 打开游标,
    OPEN cur1;
    WHILE done1 do
        -- 将游标中的值赋值给变量，注意：变量名不要与sql返回的列名相同，变量顺序要和sql结果列的顺序一致
        FETCH cur1 INTO roleId;
        -- 游标还未结束
        IF (done1) THEN
            -- 执行业务逻辑
            call `_inner_role_permission_add`(permissionPath, roleId);
        END IF;
    END WHILE;

END $$
delimiter;

-- 刷新权限的父路径(有些是遗失的)
DROP PROCEDURE if EXISTS refresh_permission_parent_path;
delimiter $$
CREATE DEFINER=`root`@`%` PROCEDURE `refresh_permission_parent_path`()
BEGIN
	DECLARE done int DEFAULT TRUE;
	DECLARE paerntId varchar(100);
	DECLARE selfId varchar(100);
    -- 定义游标, 这里找出非根节点且parent_path未null的权限
	DECLARE cur CURSOR FOR select id,parent_id from sys_permissions WHERE parent_path is null and type!= 0 ;
    -- 声明当游标遍历完后将标志变量置为某个值
    DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = FALSE;
    -- 打开游标,
    OPEN cur;
    WHILE done do
        -- 将游标中的值赋值给变量，注意：变量名不要与sql返回的列名相同，变量顺序要和sql结果列的顺序一致
        FETCH cur INTO selfId, paerntId;
        -- 游标还未结束
        IF (done) THEN
            -- 执行业务逻辑
            SET @parentPath = (SELECT path from sys_permissions WHERE id = paerntId);
            update sys_permissions set parent_path = @parentPath WHERE id = selfId;
        END IF;
    END WHILE;

END $$
delimiter;

call refresh_permission_parent_path();
