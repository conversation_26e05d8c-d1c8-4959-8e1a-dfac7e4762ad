
-- 补充销售订单的下载/打印单据权限
INSERT INTO `sys_permissions` (`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`)
VALUES ('1090102370', '下载/打印单据(下载默认打印模板)', 'sales.order:downDefaultTemplate', NULL, NULL, NOW(), NULL, NOW(), 'enable', 'GET', '1090102', '2', '1', '0', '/supply-chain-collaboration/order-model/salesOrder', '1', NULL, '');
INSERT INTO `sys_permissions` (`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`)
VALUES ('1090102380', '下载/打印单据(上传/下载自定义打印模板)', 'sales.order:downUploadPrintTemplate', NULL, NULL, NOW(), NULL, NOW(), 'enable', 'POST', '1090102', '2', '1', '0', '/supply-chain-collaboration/order-model/salesOrder', '1', NULL, '');
INSERT INTO `sys_permissions` (`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`)
VALUES ('1090102390', '下载/打印单据(下载/打印pdf)', 'sales.order:downPrintPdf', NULL, NULL, NOW(), NULL, NOW(), 'enable', 'GET', '1090102', '2', '1', '0', '/supply-chain-collaboration/order-model/salesOrder', '1', NULL, '');
INSERT INTO `sys_permissions` (`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`)
VALUES ('1090102400', '下载/打印单据(导出Excel)', 'sales.order:excel', NULL, NULL, NOW(), NULL, NOW(), 'enable', 'GET', '1090102', '2', '1', '0', '/supply-chain-collaboration/order-model/salesOrder', '1', NULL, '');
call init_new_role_permission('1090102370');
call init_new_role_permission('1090102380');
call init_new_role_permission('1090102390');
call init_new_role_permission('1090102400');

-- 补充采购需求单的下载/打印单据权限
INSERT INTO `sys_permissions` (`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`)
VALUES ('1090204170', '下载/打印单据(下载默认打印模板)', 'purchasing.demand:downDefaultTemplate', NULL, NULL, NOW(), NULL, NOW(), 'enable', 'GET', '1090204', '2', '1', '0', '/supply-chain-collaboration/procurement-management/purchasing-demand', '1', NULL, '');
INSERT INTO `sys_permissions` (`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`)
VALUES ('1090204180', '下载/打印单据(上传/下载自定义打印模板)', 'purchasing.demand:downUploadPrintTemplate', NULL, NULL, NOW(), NULL, NOW(), 'enable', 'POST', '1090204', '2', '1', '0', '/supply-chain-collaboration/procurement-management/purchasing-demand', '1', NULL, '');
INSERT INTO `sys_permissions` (`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`)
VALUES ('1090204190', '下载/打印单据(下载/打印pdf)', 'purchasing.demand:downPrintPdf', NULL, NULL, NOW(), NULL, NOW(), 'enable', 'GET', '1090204', '2', '1', '0', '/supply-chain-collaboration/procurement-management/purchasing-demand', '1', NULL, '');
INSERT INTO `sys_permissions` (`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`)
VALUES ('1090204200', '下载/打印单据(导出Excel)', 'purchasing.demand:excel', NULL, NULL, NOW(), NULL, NOW(), 'enable', 'GET', '1090204', '2', '1', '0', '/supply-chain-collaboration/procurement-management/purchasing-demand', '1', NULL, '');
call init_new_role_permission('1090204170');
call init_new_role_permission('1090204180');
call init_new_role_permission('1090204190');
call init_new_role_permission('1090204200');

-- 补充生产作业-生产看板-生产记录-工单记录权限
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `create_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`)
VALUES ('10104420', '工单记录_列配置_列表', 'workOrderRecord:columnConfigList', NOW(), 'enable', 'GET', '10104', 2, 1, 0, '/workorder-model/product-report/index', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `create_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`)
VALUES ('10104430', '工单记录_导出_下载默认模板', 'workOrderRecord:export:defaultTemplate', NOW(), 'enable', 'GET', '10104', 2, 1, 0, '/workorder-model/product-report/index', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `create_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`)
VALUES ('10104440', '工单记录_导出_上传下载自定义导出模板', 'workOrderRecord:export:uploadTemplate', NOW(), 'enable', 'GET', '10104', 2, 1, 0, '/workorder-model/product-report/index', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `create_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`)
VALUES ('10104450', '工单记录_导出_导出Excel', 'workOrderRecord:export:excel', NOW(), 'enable', 'GET', '10104', 2, 1, 0, '/workorder-model/product-report/index', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `create_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`)
VALUES ('10104460', '工单记录_导出_查看日志', 'workOrderRecord:export:log', NOW(), 'enable', 'GET', '10104', 2, 1, 0, '/workorder-model/product-report/index', 1);
call init_new_role_permission('10104420');
call init_new_role_permission('10104430');
call init_new_role_permission('10104440');
call init_new_role_permission('10104450');
call init_new_role_permission('10104460');

-- 补充报表中心-生产看板-生产记录-工单记录权限
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `create_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`)
VALUES ('13001780', '工单记录_列配置_列表', 'center:workOrderRecord:columnConfigList', NOW(), 'enable', 'GET', '13001', 2, 1, 0, '/statementCenter/production', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `create_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`)
VALUES ('13001781', '工单记录_导出_下载默认模板', 'center:workOrderRecord:export:defaultTemplate', NOW(), 'enable', 'GET', '13001', 2, 1, 0, '/statementCenter/production', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `create_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`)
VALUES ('13001782', '工单记录_导出_上传下载自定义导出模板', 'center:workOrderRecord:export:uploadTemplate', NOW(), 'enable', 'GET', '13001', 2, 1, 0, '/statementCenter/production', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `create_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`)
VALUES ('13001783', '工单记录_导出_导出Excel', 'center:workOrderRecord:export:excel', NOW(), 'enable', 'GET', '13001', 2, 1, 0, '/statementCenter/production', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `create_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`)
VALUES ('13001784', '工单记录_导出_查看日志', 'center:workOrderRecord:export:log', NOW(), 'enable', 'GET', '13001', 2, 1, 0, '/statementCenter/production', 1);
call init_new_role_permission('13001780');
call init_new_role_permission('13001781');
call init_new_role_permission('13001782');
call init_new_role_permission('13001783');
call init_new_role_permission('13001784');


-- 补充报表中心-销售看板-订单状况-销售订单导出权限
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `create_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`)
VALUES ('13003300', '销售订单_导出_下载默认模板', 'center:saleOrder:export:defaultTemplate', NOW(), 'enable', 'GET', '13003', 2, 1, 0, '/statementCenter/salesReport', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `create_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`)
VALUES ('13003310', '销售订单_导出_上传下载自定义导出模板', 'center:saleOrder:export:uploadTemplate', NOW(), 'enable', 'GET', '13003', 2, 1, 0, '/statementCenter/salesReport', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `create_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`)
VALUES ('13003320', '销售订单_导出_导出Excel', 'center:saleOrder:export:excel', NOW(), 'enable', 'GET', '13003', 2, 1, 0, '/statementCenter/salesReport', 1);
call init_new_role_permission('13003300');
call init_new_role_permission('13003310');
call init_new_role_permission('13003320');


