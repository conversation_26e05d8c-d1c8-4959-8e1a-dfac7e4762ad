-- ================================================此脚本专为编码规则设定，如需写脚本，请移步到3.17.1.1=======================================================
-- ================================================此脚本专为编码规则设定，如需写脚本，请移步到3.17.1.1=======================================================
-- ================================================此脚本专为编码规则设定，如需写脚本，请移步到3.17.1.1=======================================================
-- ================================================此脚本专为编码规则设定，如需写脚本，请移步到3.17.1.1=======================================================
-- ================================================此脚本专为编码规则设定，如需写脚本，请移步到3.17.1.1=======================================================


CREATE TABLE IF NOT EXISTS`dfs_rule_auto_increment_config`
(
    `id`                    bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `code`                  varchar(50) NOT NULL COMMENT '编码',
    `name`                  varchar(50) NOT NULL COMMENT '名称',
    `allow_rule_types`      text COMMENT '允许的规则类型列表，逗号分隔',
    `need_other_rule_codes` varchar(255)         DEFAULT NULL COMMENT '归一时需要的其他组成信息编码，逗号分隔',
    `create_time`           datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`           datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_code` (`code`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='编码规则自增序号配置表';

CREATE TABLE IF NOT EXISTS`dfs_rule_prefix_config`
(
    `id`               bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `code`             int(11) NOT NULL COMMENT '编码',
    `type`             varchar(50) NOT NULL COMMENT '名称',
    `impl_name`        varchar(50)          DEFAULT NULL COMMENT '实现方法名',
    `input`            tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否输入 1：手动输入 0：不需要手动输入',
    `allow_rule_types` text COMMENT '允许的规则类型列表，逗号分隔',
    `create_time`      datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`      datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_code` (`code`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='编码规则前缀配置表';

CREATE TABLE IF NOT EXISTS`dfs_rule_prefix_extend`
(
    `id`          bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `prefix_code` int(11) NOT NULL COMMENT '关联的前缀编码',
    `pattern`     varchar(50) NOT NULL COMMENT '模式',
    `value`       varchar(255)         DEFAULT NULL COMMENT '值',
    `create_time` datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY           `idx_prefix_code` (`prefix_code`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='编码规则扩展配置表';

CREATE TABLE IF NOT EXISTS`dfs_rule_type_config`
(
    `id`          bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `type`        int(11) NOT NULL COMMENT '类型编码',
    `module_name` varchar(100) NOT NULL COMMENT '模块名称',
    `parent_type_code` varchar(100) COMMENT '父级类型编码',
    `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_type` (`type`) COMMENT '类型编码唯一索引'
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='编码规则类型配置表';

-- 创建编码规则父级类型表
CREATE TABLE IF NOT EXISTS`dfs_rule_type_parent_config`
(
    `id`          bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `type`        varchar(100) NOT NULL COMMENT '类型编码',
    `name`        varchar(100) NOT NULL COMMENT '模块名称',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_type` (`type`) COMMENT '类型编码唯一索引'
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='编码规则父级类型表';

INSERT INTO `dfs_rule_auto_increment_config`(`code`, `name`, `allow_rule_types`, `need_other_rule_codes`, `create_time`, `update_time`) VALUES ('noCross', '不归一', '1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,95,96,97,98,100,89,90,91,92,93,94,101,500,501,502,503,504', NULL, '2025-02-17 14:50:18', '2025-02-17 14:50:18');
INSERT INTO `dfs_rule_auto_increment_config`(`code`, `name`, `allow_rule_types`, `need_other_rule_codes`, `create_time`, `update_time`) VALUES ('day', '跨天归1', '1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,95,96,97,98,100,89,90,91,92,93,94,101,500,501,502,503,504', '2', '2025-02-17 14:50:18', '2025-02-17 14:50:18');
INSERT INTO `dfs_rule_auto_increment_config`(`code`, `name`, `allow_rule_types`, `need_other_rule_codes`, `create_time`, `update_time`) VALUES ('month', '跨月归1', '1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,95,96,97,98,100,89,90,91,92,93,94,101,500,501,502,503,504', '2', '2025-02-17 14:50:18', '2025-02-17 14:50:18');
INSERT INTO `dfs_rule_auto_increment_config`(`code`, `name`, `allow_rule_types`, `need_other_rule_codes`, `create_time`, `update_time`) VALUES ('year', '跨年归1', '1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,95,96,97,98,100,89,90,91,92,93,94,101,500,501,502,503,504', '2', '2025-02-17 14:50:18', '2025-02-17 14:50:18');
INSERT INTO `dfs_rule_auto_increment_config`(`code`, `name`, `allow_rule_types`, `need_other_rule_codes`, `create_time`, `update_time`) VALUES ('workOrder', '按生产工单归1', '5,12,14,61,97,100', '3', '2025-02-17 14:50:18', '2025-02-17 14:50:18');
INSERT INTO `dfs_rule_auto_increment_config`(`code`, `name`, `allow_rule_types`, `need_other_rule_codes`, `create_time`, `update_time`) VALUES ('productOrder', '按生产订单归1', '57,80', '20', '2025-02-17 14:50:18', '2025-02-17 14:50:18');
INSERT INTO `dfs_rule_auto_increment_config`(`code`, `name`, `allow_rule_types`, `need_other_rule_codes`, `create_time`, `update_time`) VALUES ('productOrderOnlyPushDown', '按生产订单归1(仅下推有效)', '2', '21', '2025-02-17 14:50:18', '2025-02-17 14:50:18');
INSERT INTO `dfs_rule_auto_increment_config`(`code`, `name`, `allow_rule_types`, `need_other_rule_codes`, `create_time`, `update_time`) VALUES ('saleOrderOnlyPushDown', '按销售订单归1(仅下推有效)', '2,10', '22', '2025-02-17 14:50:18', '2025-02-17 14:50:18');
INSERT INTO `dfs_rule_auto_increment_config`(`code`, `name`, `allow_rule_types`, `need_other_rule_codes`, `create_time`, `update_time`) VALUES ('lineModel', '按制造单元类型归1', '5,12,14', '9', '2025-02-17 14:50:18', '2025-02-17 14:50:18');
INSERT INTO `dfs_rule_auto_increment_config`(`code`, `name`, `allow_rule_types`, `need_other_rule_codes`, `create_time`, `update_time`) VALUES ('line', '按制造单元归1', '5,12,14', '10', '2025-02-17 14:50:18', '2025-02-17 14:50:18');
INSERT INTO `dfs_rule_auto_increment_config`(`code`, `name`, `allow_rule_types`, `need_other_rule_codes`, `create_time`, `update_time`) VALUES ('supplier', '按供应商编码归1', '21,22,11', '12', '2025-02-17 14:50:18', '2025-02-17 14:50:18');
INSERT INTO `dfs_rule_auto_increment_config`(`code`, `name`, `allow_rule_types`, `need_other_rule_codes`, `create_time`, `update_time`) VALUES ('purchase', '按采购订单归1', '21,22,11', '13', '2025-02-17 14:50:18', '2025-02-17 14:50:18');
INSERT INTO `dfs_rule_auto_increment_config`(`code`, `name`, `allow_rule_types`, `need_other_rule_codes`, `create_time`, `update_time`) VALUES ('purchaseReceipt', '按采购收货归1', '11', '15', '2025-02-17 14:50:18', '2025-02-17 14:50:18');
INSERT INTO `dfs_rule_auto_increment_config`(`code`, `name`, `allow_rule_types`, `need_other_rule_codes`, `create_time`, `update_time`) VALUES ('purchaseBatch', '按采购批次归1', '22', '14', '2025-02-17 14:50:18', '2025-02-17 14:50:18');
INSERT INTO `dfs_rule_auto_increment_config`(`code`, `name`, `allow_rule_types`, `need_other_rule_codes`, `create_time`, `update_time`) VALUES ('workOrderPackage', '按生产工单+包装层级归1', '61', '3', '2025-02-17 14:50:18', '2025-02-17 14:50:18');
INSERT INTO `dfs_rule_auto_increment_config`(`code`, `name`, `allow_rule_types`, `need_other_rule_codes`, `create_time`, `update_time`) VALUES ('departmentCode', '按部门编码归1', '3,78', '23', '2025-02-17 14:50:18', '2025-02-17 14:50:18');
INSERT INTO `dfs_rule_auto_increment_config`(`code`, `name`, `allow_rule_types`, `need_other_rule_codes`, `create_time`, `update_time`) VALUES ('customerCode', '按客户编码归1', '1', '7', '2025-02-17 14:50:18', '2025-02-17 14:50:18');
INSERT INTO `dfs_rule_auto_increment_config`(`code`, `name`, `allow_rule_types`, `need_other_rule_codes`, `create_time`, `update_time`) VALUES ('saleOrderTypeCode', '按订单类型编码归1', '1', '24', '2025-02-17 14:50:18', '2025-02-17 14:50:18');
INSERT INTO `dfs_rule_auto_increment_config`(`code`, `name`, `allow_rule_types`, `need_other_rule_codes`, `create_time`, `update_time`) VALUES ('workOrderOnlySplit', '按生产工单归1(仅排产拆分有效)', '85', '25', '2025-02-17 14:50:18', '2025-02-17 14:50:18');
INSERT INTO `dfs_rule_auto_increment_config`(`code`, `name`, `allow_rule_types`, `need_other_rule_codes`, `create_time`, `update_time`) VALUES ('packageOrder', '按包装工单归1', '87', '26', '2025-02-17 14:50:18', '2025-02-17 14:50:18');
INSERT INTO `dfs_rule_auto_increment_config`(`code`, `name`, `allow_rule_types`, `need_other_rule_codes`, `create_time`, `update_time`) VALUES ('packageOrderLevel', '按包装工单_包装层级归1', '87', '26', '2025-02-17 14:50:18', '2025-02-17 14:50:18');
INSERT INTO `dfs_rule_auto_increment_config`(`code`, `name`, `allow_rule_types`, `need_other_rule_codes`, `create_time`, `update_time`) VALUES ('materialTypeCode', '按物料类型编码归1', '82', '28', '2025-02-17 14:50:18', '2025-02-17 14:50:18');
INSERT INTO `dfs_rule_auto_increment_config`(`code`, `name`, `allow_rule_types`, `need_other_rule_codes`, `create_time`, `update_time`) VALUES ('materialCode', '按物料编码归1', '4,26', '6', '2025-02-17 14:50:18', '2025-02-17 14:50:18');
INSERT INTO `dfs_rule_auto_increment_config`(`code`, `name`, `allow_rule_types`, `need_other_rule_codes`, `create_time`, `update_time`) VALUES ('workOrderNumberLineCode', '按生产工单号+制造单元编码归1', '5', '3', '2025-02-17 14:50:18', '2025-02-17 14:50:18');

INSERT INTO `dfs_rule_prefix_config`(`code`, `type`, `impl_name`, `input`, `allow_rule_types`, `create_time`, `update_time`) VALUES (1, '人工输入', NULL, 1, '1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,95,96,97,98,100,89,90,91,92,93,94,101,500,501,502,503,504', '2025-02-17 14:51:01', '2025-02-17 14:51:01');
INSERT INTO `dfs_rule_prefix_config`(`code`, `type`, `impl_name`, `input`, `allow_rule_types`, `create_time`, `update_time`) VALUES (2, '当前日期', 'formatCurrentDate', 0, '1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,95,96,97,98,100,89,90,91,92,93,94,101,500,501,502,503,504', '2025-02-17 14:51:01', '2025-02-17 14:51:01');
INSERT INTO `dfs_rule_prefix_config`(`code`, `type`, `impl_name`, `input`, `allow_rule_types`, `create_time`, `update_time`) VALUES (4, '自动生成序号', NULL, 0, '1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,95,96,97,98,100,89,90,91,92,93,94,101,500,501,502,503,504', '2025-02-17 14:51:01', '2025-02-17 14:51:01');
INSERT INTO `dfs_rule_prefix_config`(`code`, `type`, `impl_name`, `input`, `allow_rule_types`, `create_time`, `update_time`) VALUES (5, '固定信息', NULL, 0, '1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,95,96,97,98,100,89,90,91,92,93,94,101,500,501,502,503,504', '2025-02-17 14:51:01', '2025-02-17 14:51:01');
INSERT INTO `dfs_rule_prefix_config`(`code`, `type`, `impl_name`, `input`, `allow_rule_types`, `create_time`, `update_time`) VALUES (3, '生产工单号', NULL, 1, '5,12,14,61,97,100', '2025-02-17 14:51:01', '2025-02-17 14:51:01');
INSERT INTO `dfs_rule_prefix_config`(`code`, `type`, `impl_name`, `input`, `allow_rule_types`, `create_time`, `update_time`) VALUES (6, '物料编码', NULL, 1, '5,12,14,21,22,43,11,57,61,4,26,62', '2025-02-17 14:51:01', '2025-02-17 14:51:01');
INSERT INTO `dfs_rule_prefix_config`(`code`, `type`, `impl_name`, `input`, `allow_rule_types`, `create_time`, `update_time`) VALUES (7, '客户编码', NULL, 1, '1', '2025-02-17 14:51:01', '2025-02-17 14:51:01');
INSERT INTO `dfs_rule_prefix_config`(`code`, `type`, `impl_name`, `input`, `allow_rule_types`, `create_time`, `update_time`) VALUES (9, '制造单元类型编号', NULL, 1, '5,12,14', '2025-02-17 14:51:01', '2025-02-17 14:51:01');
INSERT INTO `dfs_rule_prefix_config`(`code`, `type`, `impl_name`, `input`, `allow_rule_types`, `create_time`, `update_time`) VALUES (10, '制造单元编号', NULL, 1, '5,12,14', '2025-02-17 14:51:01', '2025-02-17 14:51:01');
INSERT INTO `dfs_rule_prefix_config`(`code`, `type`, `impl_name`, `input`, `allow_rule_types`, `create_time`, `update_time`) VALUES (12, '供应商编码', NULL, 1, '21,22,43,11,62', '2025-02-17 14:51:01', '2025-02-17 14:51:01');
INSERT INTO `dfs_rule_prefix_config`(`code`, `type`, `impl_name`, `input`, `allow_rule_types`, `create_time`, `update_time`) VALUES (13, '采购订单号', NULL, 1, '11,21,22', '2025-02-17 14:51:01', '2025-02-17 14:51:01');
INSERT INTO `dfs_rule_prefix_config`(`code`, `type`, `impl_name`, `input`, `allow_rule_types`, `create_time`, `update_time`) VALUES (14, '采购批次号', NULL, 1, '22', '2025-02-17 14:51:01', '2025-02-17 14:51:01');
INSERT INTO `dfs_rule_prefix_config`(`code`, `type`, `impl_name`, `input`, `allow_rule_types`, `create_time`, `update_time`) VALUES (15, '采购收货号', NULL, 1, '24,25,11', '2025-02-17 14:51:01', '2025-02-17 14:51:01');
INSERT INTO `dfs_rule_prefix_config`(`code`, `type`, `impl_name`, `input`, `allow_rule_types`, `create_time`, `update_time`) VALUES (16, '下单日期', 'formatCurrentDate', 1, '11', '2025-02-17 14:51:01', '2025-02-17 14:51:01');
INSERT INTO `dfs_rule_prefix_config`(`code`, `type`, `impl_name`, `input`, `allow_rule_types`, `create_time`, `update_time`) VALUES (17, '交货日期', 'formatCurrentDate', 1, '11', '2025-02-17 14:51:01', '2025-02-17 14:51:01');
INSERT INTO `dfs_rule_prefix_config`(`code`, `type`, `impl_name`, `input`, `allow_rule_types`, `create_time`, `update_time`) VALUES (18, '按校验次数自增序号', NULL, 1, '5', '2025-02-17 14:51:01', '2025-02-17 14:51:01');
INSERT INTO `dfs_rule_prefix_config`(`code`, `type`, `impl_name`, `input`, `allow_rule_types`, `create_time`, `update_time`) VALUES (19, '委外订单号', NULL, 1, '43', '2025-02-17 14:51:01', '2025-02-17 14:51:01');
INSERT INTO `dfs_rule_prefix_config`(`code`, `type`, `impl_name`, `input`, `allow_rule_types`, `create_time`, `update_time`) VALUES (20, '生产订单号', NULL, 1, '57,80', '2025-02-17 14:51:01', '2025-02-17 14:51:01');
INSERT INTO `dfs_rule_prefix_config`(`code`, `type`, `impl_name`, `input`, `allow_rule_types`, `create_time`, `update_time`) VALUES (21, '生产订单号(仅下推有效)', NULL, 1, '2', '2025-02-17 14:51:01', '2025-02-17 14:51:01');
INSERT INTO `dfs_rule_prefix_config`(`code`, `type`, `impl_name`, `input`, `allow_rule_types`, `create_time`, `update_time`) VALUES (22, '销售订单号(仅下推有效)', NULL, 1, '2,10', '2025-02-17 14:51:01', '2025-02-17 14:51:01');
INSERT INTO `dfs_rule_prefix_config`(`code`, `type`, `impl_name`, `input`, `allow_rule_types`, `create_time`, `update_time`) VALUES (23, '部门编码', NULL, 1, '3,78', '2025-02-17 14:51:01', '2025-02-17 14:51:01');
INSERT INTO `dfs_rule_prefix_config`(`code`, `type`, `impl_name`, `input`, `allow_rule_types`, `create_time`, `update_time`) VALUES (24, '订单类型编码', NULL, 1, '1', '2025-02-17 14:51:01', '2025-02-17 14:51:01');
INSERT INTO `dfs_rule_prefix_config`(`code`, `type`, `impl_name`, `input`, `allow_rule_types`, `create_time`, `update_time`) VALUES (25, '原生产工单号(仅排产拆分有效)', NULL, 1, '85', '2025-02-17 14:51:01', '2025-02-17 14:51:01');
INSERT INTO `dfs_rule_prefix_config`(`code`, `type`, `impl_name`, `input`, `allow_rule_types`, `create_time`, `update_time`) VALUES (26, '包装工单编号', NULL, 1, '87', '2025-02-17 14:51:01', '2025-02-17 14:51:01');
INSERT INTO `dfs_rule_prefix_config`(`code`, `type`, `impl_name`, `input`, `allow_rule_types`, `create_time`, `update_time`) VALUES (27, '包装工单层级', NULL, 1, '87', '2025-02-17 14:51:01', '2025-02-17 14:51:01');
INSERT INTO `dfs_rule_prefix_config`(`code`, `type`, `impl_name`, `input`, `allow_rule_types`, `create_time`, `update_time`) VALUES (28, '物料类型编码', NULL, 1, '82', '2025-02-17 14:51:01', '2025-02-17 14:51:01');
INSERT INTO `dfs_rule_prefix_config`(`code`, `type`, `impl_name`, `input`, `allow_rule_types`, `create_time`, `update_time`) VALUES (29, '档位', NULL, 1, '97', '2025-02-17 14:51:01', '2025-02-17 14:51:01');
INSERT INTO `dfs_rule_prefix_config`(`code`, `type`, `impl_name`, `input`, `allow_rule_types`, `create_time`, `update_time`) VALUES (30, '不良类型编码', NULL, 1, '102', '2025-02-17 14:51:01', '2025-02-17 14:51:01');
INSERT INTO `dfs_rule_prefix_config`(`code`, `type`, `impl_name`, `input`, `allow_rule_types`, `create_time`, `update_time`) VALUES (31, '维修类型编码', NULL, 1, '103', '2025-02-17 14:51:01', '2025-02-17 14:51:01');

INSERT INTO `dfs_rule_prefix_extend`(`prefix_code`, `pattern`, `value`, `create_time`, `update_time`) VALUES (2, 'yyyy', NULL, '2025-02-17 14:51:01', '2025-02-17 14:51:01');
INSERT INTO `dfs_rule_prefix_extend`(`prefix_code`, `pattern`, `value`, `create_time`, `update_time`) VALUES (2, 'MM', NULL, '2025-02-17 14:51:01', '2025-02-17 14:51:01');
INSERT INTO `dfs_rule_prefix_extend`(`prefix_code`, `pattern`, `value`, `create_time`, `update_time`) VALUES (2, 'yyyyMM', NULL, '2025-02-17 14:51:01', '2025-02-17 14:51:01');
INSERT INTO `dfs_rule_prefix_extend`(`prefix_code`, `pattern`, `value`, `create_time`, `update_time`) VALUES (2, 'yyMM', NULL, '2025-02-17 14:51:01', '2025-02-17 14:51:01');
INSERT INTO `dfs_rule_prefix_extend`(`prefix_code`, `pattern`, `value`, `create_time`, `update_time`) VALUES (2, 'yMM', NULL, '2025-02-17 14:51:01', '2025-02-17 14:51:01');
INSERT INTO `dfs_rule_prefix_extend`(`prefix_code`, `pattern`, `value`, `create_time`, `update_time`) VALUES (2, 'yyyyMMdd', NULL, '2025-02-17 14:51:01', '2025-02-17 14:51:01');
INSERT INTO `dfs_rule_prefix_extend`(`prefix_code`, `pattern`, `value`, `create_time`, `update_time`) VALUES (2, 'yyyy/MM/dd', NULL, '2025-02-17 14:51:01', '2025-02-17 14:51:01');
INSERT INTO `dfs_rule_prefix_extend`(`prefix_code`, `pattern`, `value`, `create_time`, `update_time`) VALUES (2, 'yyMMdd', NULL, '2025-02-17 14:51:01', '2025-02-17 14:51:01');
INSERT INTO `dfs_rule_prefix_extend`(`prefix_code`, `pattern`, `value`, `create_time`, `update_time`) VALUES (2, 'HH', NULL, '2025-02-17 14:51:01', '2025-02-17 14:51:01');
INSERT INTO `dfs_rule_prefix_extend`(`prefix_code`, `pattern`, `value`, `create_time`, `update_time`) VALUES (2, 'yyyyMMddHHmmss', NULL, '2025-02-17 14:51:01', '2025-02-17 14:51:01');
INSERT INTO `dfs_rule_prefix_extend`(`prefix_code`, `pattern`, `value`, `create_time`, `update_time`) VALUES (2, 'yy', NULL, '2025-02-17 14:51:01', '2025-02-17 14:51:01');
INSERT INTO `dfs_rule_prefix_extend`(`prefix_code`, `pattern`, `value`, `create_time`, `update_time`) VALUES (2, 'dd', NULL, '2025-02-17 14:51:01', '2025-02-17 14:51:01');
INSERT INTO `dfs_rule_prefix_extend`(`prefix_code`, `pattern`, `value`, `create_time`, `update_time`) VALUES (2, 'yyyyMMddHHmm', NULL, '2025-02-17 14:51:01', '2025-02-17 14:51:01');
INSERT INTO `dfs_rule_prefix_extend`(`prefix_code`, `pattern`, `value`, `create_time`, `update_time`) VALUES (2, '自定义格式', NULL, '2025-02-17 14:51:01', '2025-02-17 14:51:01');
INSERT INTO `dfs_rule_prefix_extend`(`prefix_code`, `pattern`, `value`, `create_time`, `update_time`) VALUES (16, 'yyyy', NULL, '2025-02-17 14:51:01', '2025-02-17 14:51:01');
INSERT INTO `dfs_rule_prefix_extend`(`prefix_code`, `pattern`, `value`, `create_time`, `update_time`) VALUES (16, 'MM', NULL, '2025-02-17 14:51:01', '2025-02-17 14:51:01');
INSERT INTO `dfs_rule_prefix_extend`(`prefix_code`, `pattern`, `value`, `create_time`, `update_time`) VALUES (16, 'yyyyMM', NULL, '2025-02-17 14:51:01', '2025-02-17 14:51:01');
INSERT INTO `dfs_rule_prefix_extend`(`prefix_code`, `pattern`, `value`, `create_time`, `update_time`) VALUES (16, 'yyMM', NULL, '2025-02-17 14:51:01', '2025-02-17 14:51:01');
INSERT INTO `dfs_rule_prefix_extend`(`prefix_code`, `pattern`, `value`, `create_time`, `update_time`) VALUES (16, 'yMM', NULL, '2025-02-17 14:51:01', '2025-02-17 14:51:01');
INSERT INTO `dfs_rule_prefix_extend`(`prefix_code`, `pattern`, `value`, `create_time`, `update_time`) VALUES (16, 'yyyyMMdd', NULL, '2025-02-17 14:51:01', '2025-02-17 14:51:01');
INSERT INTO `dfs_rule_prefix_extend`(`prefix_code`, `pattern`, `value`, `create_time`, `update_time`) VALUES (16, 'yyyy/MM/dd', NULL, '2025-02-17 14:51:01', '2025-02-17 14:51:01');
INSERT INTO `dfs_rule_prefix_extend`(`prefix_code`, `pattern`, `value`, `create_time`, `update_time`) VALUES (16, 'yyMMdd', NULL, '2025-02-17 14:51:01', '2025-02-17 14:51:01');
INSERT INTO `dfs_rule_prefix_extend`(`prefix_code`, `pattern`, `value`, `create_time`, `update_time`) VALUES (16, 'HH', NULL, '2025-02-17 14:51:01', '2025-02-17 14:51:01');
INSERT INTO `dfs_rule_prefix_extend`(`prefix_code`, `pattern`, `value`, `create_time`, `update_time`) VALUES (16, 'yyyyMMddHHmmss', NULL, '2025-02-17 14:51:01', '2025-02-17 14:51:01');
INSERT INTO `dfs_rule_prefix_extend`(`prefix_code`, `pattern`, `value`, `create_time`, `update_time`) VALUES (16, 'yyyyMMddHHmm', NULL, '2025-02-17 14:51:01', '2025-02-17 14:51:01');
INSERT INTO `dfs_rule_prefix_extend`(`prefix_code`, `pattern`, `value`, `create_time`, `update_time`) VALUES (17, 'yyyy', NULL, '2025-02-17 14:51:01', '2025-02-17 14:51:01');
INSERT INTO `dfs_rule_prefix_extend`(`prefix_code`, `pattern`, `value`, `create_time`, `update_time`) VALUES (17, 'MM', NULL, '2025-02-17 14:51:01', '2025-02-17 14:51:01');
INSERT INTO `dfs_rule_prefix_extend`(`prefix_code`, `pattern`, `value`, `create_time`, `update_time`) VALUES (17, 'yyyyMM', NULL, '2025-02-17 14:51:01', '2025-02-17 14:51:01');
INSERT INTO `dfs_rule_prefix_extend`(`prefix_code`, `pattern`, `value`, `create_time`, `update_time`) VALUES (17, 'yyMM', NULL, '2025-02-17 14:51:01', '2025-02-17 14:51:01');
INSERT INTO `dfs_rule_prefix_extend`(`prefix_code`, `pattern`, `value`, `create_time`, `update_time`) VALUES (17, 'yMM', NULL, '2025-02-17 14:51:01', '2025-02-17 14:51:01');
INSERT INTO `dfs_rule_prefix_extend`(`prefix_code`, `pattern`, `value`, `create_time`, `update_time`) VALUES (17, 'yyyyMMdd', NULL, '2025-02-17 14:51:01', '2025-02-17 14:51:01');
INSERT INTO `dfs_rule_prefix_extend`(`prefix_code`, `pattern`, `value`, `create_time`, `update_time`) VALUES (17, 'yyyy/MM/dd', NULL, '2025-02-17 14:51:01', '2025-02-17 14:51:01');
INSERT INTO `dfs_rule_prefix_extend`(`prefix_code`, `pattern`, `value`, `create_time`, `update_time`) VALUES (17, 'yyMMdd', NULL, '2025-02-17 14:51:01', '2025-02-17 14:51:01');
INSERT INTO `dfs_rule_prefix_extend`(`prefix_code`, `pattern`, `value`, `create_time`, `update_time`) VALUES (17, 'HH', NULL, '2025-02-17 14:51:01', '2025-02-17 14:51:01');
INSERT INTO `dfs_rule_prefix_extend`(`prefix_code`, `pattern`, `value`, `create_time`, `update_time`) VALUES (17, 'yyyyMMddHHmmss', NULL, '2025-02-17 14:51:01', '2025-02-17 14:51:01');
INSERT INTO `dfs_rule_prefix_extend`(`prefix_code`, `pattern`, `value`, `create_time`, `update_time`) VALUES (17, 'yyyyMMddHHmm', NULL, '2025-02-17 14:51:01', '2025-02-17 14:51:01');


INSERT INTO `dfs_rule_type_config`(`type`, `module_name`, `create_time`, `update_time`) VALUES (1, '销售订单-单据编号', '2025-02-17 14:50:35', '2025-02-17 14:50:35');
INSERT INTO `dfs_rule_type_config`(`type`, `module_name`, `create_time`, `update_time`) VALUES (16, '销售出货单-单据编号', '2025-02-17 14:50:35', '2025-02-17 14:50:35');
INSERT INTO `dfs_rule_type_config`(`type`, `module_name`, `create_time`, `update_time`) VALUES (17, '销售发货-单据编号', '2025-02-17 14:50:35', '2025-02-17 14:50:35');
INSERT INTO `dfs_rule_type_config`(`type`, `module_name`, `create_time`, `update_time`) VALUES (10, '生产订单-单据编号', '2025-02-17 14:50:35', '2025-02-17 14:50:35');
INSERT INTO `dfs_rule_type_config`(`type`, `module_name`, `create_time`, `update_time`) VALUES (34, '生产订单-生产订单用料清单', '2025-02-17 14:50:35', '2025-02-17 14:50:35');
INSERT INTO `dfs_rule_type_config`(`type`, `module_name`, `create_time`, `update_time`) VALUES (57, '生产订单-订单流水码', '2025-02-17 14:50:35', '2025-02-17 14:50:35');
INSERT INTO `dfs_rule_type_config`(`type`, `module_name`, `create_time`, `update_time`) VALUES (80, '生产订单-订单批次号', '2025-02-17 14:50:35', '2025-02-17 14:50:35');
INSERT INTO `dfs_rule_type_config`(`type`, `module_name`, `create_time`, `update_time`) VALUES (2, '生产工单-单据编号', '2025-02-17 14:50:35', '2025-02-17 14:50:35');
INSERT INTO `dfs_rule_type_config`(`type`, `module_name`, `create_time`, `update_time`) VALUES (5, '生产工单-生产批次号', '2025-02-17 14:50:35', '2025-02-17 14:50:35');
INSERT INTO `dfs_rule_type_config`(`type`, `module_name`, `create_time`, `update_time`) VALUES (12, '生产工单-生产流水码', '2025-02-17 14:50:35', '2025-02-17 14:50:35');
INSERT INTO `dfs_rule_type_config`(`type`, `module_name`, `create_time`, `update_time`) VALUES (14, '生产工单-生产成品码', '2025-02-17 14:50:35', '2025-02-17 14:50:35');
INSERT INTO `dfs_rule_type_config`(`type`, `module_name`, `create_time`, `update_time`) VALUES (61, '生产工单-包装成品码', '2025-02-17 14:50:35', '2025-02-17 14:50:35');
INSERT INTO `dfs_rule_type_config`(`type`, `module_name`, `create_time`, `update_time`) VALUES (77, '生产工单-生产工单用料清单', '2025-02-17 14:50:35', '2025-02-17 14:50:35');
INSERT INTO `dfs_rule_type_config`(`type`, `module_name`, `create_time`, `update_time`) VALUES (85, '生产工单-单据编号(排产拆分)', '2025-02-17 14:50:35', '2025-02-17 14:50:35');
INSERT INTO `dfs_rule_type_config`(`type`, `module_name`, `create_time`, `update_time`) VALUES (18, '领料申请-单据编号', '2025-02-17 14:50:35', '2025-02-17 14:50:35');
INSERT INTO `dfs_rule_type_config`(`type`, `module_name`, `create_time`, `update_time`) VALUES (9, '作业工单-单据编号', '2025-02-17 14:50:35', '2025-02-17 14:50:35');
INSERT INTO `dfs_rule_type_config`(`type`, `module_name`, `create_time`, `update_time`) VALUES (19, '采购需求-单据编号', '2025-02-17 14:50:35', '2025-02-17 14:50:35');
INSERT INTO `dfs_rule_type_config`(`type`, `module_name`, `create_time`, `update_time`) VALUES (20, '采购订单-单据编号', '2025-02-17 14:50:35', '2025-02-17 14:50:35');
INSERT INTO `dfs_rule_type_config`(`type`, `module_name`, `create_time`, `update_time`) VALUES (21, '采购订单-采购批次号', '2025-02-17 14:50:35', '2025-02-17 14:50:35');
INSERT INTO `dfs_rule_type_config`(`type`, `module_name`, `create_time`, `update_time`) VALUES (22, '采购订单-采购单品码', '2025-02-17 14:50:35', '2025-02-17 14:50:35');
INSERT INTO `dfs_rule_type_config`(`type`, `module_name`, `create_time`, `update_time`) VALUES (23, '采购收料-单据编号', '2025-02-17 14:50:35', '2025-02-17 14:50:35');
INSERT INTO `dfs_rule_type_config`(`type`, `module_name`, `create_time`, `update_time`) VALUES (11, '采购收料-采购批次号', '2025-02-17 14:50:35', '2025-02-17 14:50:35');
INSERT INTO `dfs_rule_type_config`(`type`, `module_name`, `create_time`, `update_time`) VALUES (33, '采购退料-申请单号', '2025-02-17 14:50:35', '2025-02-17 14:50:35');
INSERT INTO `dfs_rule_type_config`(`type`, `module_name`, `create_time`, `update_time`) VALUES (24, '来料检验-单据编号', '2025-02-17 14:50:35', '2025-02-17 14:50:35');
INSERT INTO `dfs_rule_type_config`(`type`, `module_name`, `create_time`, `update_time`) VALUES (25, '来料检验-采购批次号(拆批)', '2025-02-17 14:50:35', '2025-02-17 14:50:35');
INSERT INTO `dfs_rule_type_config`(`type`, `module_name`, `create_time`, `update_time`) VALUES (82, '物料编号', '2025-02-17 14:50:35', '2025-02-17 14:50:35');
INSERT INTO `dfs_rule_type_config`(`type`, `module_name`, `create_time`, `update_time`) VALUES (4, 'BOM编号', '2025-02-17 14:50:35', '2025-02-17 14:50:35');
INSERT INTO `dfs_rule_type_config`(`type`, `module_name`, `create_time`, `update_time`) VALUES (26, '工艺编号', '2025-02-17 14:50:35', '2025-02-17 14:50:35');
INSERT INTO `dfs_rule_type_config`(`type`, `module_name`, `create_time`, `update_time`) VALUES (56, '替代方案编号', '2025-02-17 14:50:35', '2025-02-17 14:50:35');
INSERT INTO `dfs_rule_type_config`(`type`, `module_name`, `create_time`, `update_time`) VALUES (58, '特征参数编号', '2025-02-17 14:50:35', '2025-02-17 14:50:35');
INSERT INTO `dfs_rule_type_config`(`type`, `module_name`, `create_time`, `update_time`) VALUES (98, '工序编号', '2025-02-17 14:50:35', '2025-02-17 14:50:35');
INSERT INTO `dfs_rule_type_config`(`type`, `module_name`, `create_time`, `update_time`) VALUES (27, '入库记录-单据编号', '2025-02-17 14:50:35', '2025-02-17 14:50:35');
INSERT INTO `dfs_rule_type_config`(`type`, `module_name`, `create_time`, `update_time`) VALUES (35, '入库记录-采购入库', '2025-02-17 14:50:35', '2025-02-17 14:50:35');
INSERT INTO `dfs_rule_type_config`(`type`, `module_name`, `create_time`, `update_time`) VALUES (36, '入库记录-生产入库', '2025-02-17 14:50:35', '2025-02-17 14:50:35');
INSERT INTO `dfs_rule_type_config`(`type`, `module_name`, `create_time`, `update_time`) VALUES (37, '入库记录-生产退料', '2025-02-17 14:50:35', '2025-02-17 14:50:35');
INSERT INTO `dfs_rule_type_config`(`type`, `module_name`, `create_time`, `update_time`) VALUES (6, '出库记录-单据编号', '2025-02-17 14:50:35', '2025-02-17 14:50:35');
INSERT INTO `dfs_rule_type_config`(`type`, `module_name`, `create_time`, `update_time`) VALUES (38, '出库记录-采购退料出库', '2025-02-17 14:50:35', '2025-02-17 14:50:35');
INSERT INTO `dfs_rule_type_config`(`type`, `module_name`, `create_time`, `update_time`) VALUES (39, '出库记录-生产领料出库', '2025-02-17 14:50:35', '2025-02-17 14:50:35');
INSERT INTO `dfs_rule_type_config`(`type`, `module_name`, `create_time`, `update_time`) VALUES (40, '出库记录-生产补料出库', '2025-02-17 14:50:35', '2025-02-17 14:50:35');
INSERT INTO `dfs_rule_type_config`(`type`, `module_name`, `create_time`, `update_time`) VALUES (28, '仓库调拨-单据编号', '2025-02-17 14:50:35', '2025-02-17 14:50:35');
INSERT INTO `dfs_rule_type_config`(`type`, `module_name`, `create_time`, `update_time`) VALUES (29, '盘点任务单-单据编号', '2025-02-17 14:50:35', '2025-02-17 14:50:35');
INSERT INTO `dfs_rule_type_config`(`type`, `module_name`, `create_time`, `update_time`) VALUES (62, '仓库-批次号', '2025-02-17 14:50:35', '2025-02-17 14:50:35');
INSERT INTO `dfs_rule_type_config`(`type`, `module_name`, `create_time`, `update_time`) VALUES (63, '移位单-单据编号', '2025-02-17 14:50:35', '2025-02-17 14:50:35');
INSERT INTO `dfs_rule_type_config`(`type`, `module_name`, `create_time`, `update_time`) VALUES (64, '调整单-单据编号', '2025-02-17 14:50:35', '2025-02-17 14:50:35');
INSERT INTO `dfs_rule_type_config`(`type`, `module_name`, `create_time`, `update_time`) VALUES (65, '盘点计划单-单据编号', '2025-02-17 14:50:35', '2025-02-17 14:50:35');
INSERT INTO `dfs_rule_type_config`(`type`, `module_name`, `create_time`, `update_time`) VALUES (66, '上架记录-单据编号', '2025-02-17 14:50:35', '2025-02-17 14:50:35');
INSERT INTO `dfs_rule_type_config`(`type`, `module_name`, `create_time`, `update_time`) VALUES (42, '仓储配送-货架定义', '2025-02-17 14:50:35', '2025-02-17 14:50:35');
INSERT INTO `dfs_rule_type_config`(`type`, `module_name`, `create_time`, `update_time`) VALUES (46, '其他出库申请单-单据编号', '2025-02-17 14:50:35', '2025-02-17 14:50:35');
INSERT INTO `dfs_rule_type_config`(`type`, `module_name`, `create_time`, `update_time`) VALUES (47, '其他入库申请单-单据编号', '2025-02-17 14:50:35', '2025-02-17 14:50:35');
INSERT INTO `dfs_rule_type_config`(`type`, `module_name`, `create_time`, `update_time`) VALUES (48, '其他入库-单据编号', '2025-02-17 14:50:35', '2025-02-17 14:50:35');
INSERT INTO `dfs_rule_type_config`(`type`, `module_name`, `create_time`, `update_time`) VALUES (49, '其他出库-单据编号', '2025-02-17 14:50:35', '2025-02-17 14:50:35');
INSERT INTO `dfs_rule_type_config`(`type`, `module_name`, `create_time`, `update_time`) VALUES (50, '销售出库单-单据编号', '2025-02-17 14:50:35', '2025-02-17 14:50:35');
INSERT INTO `dfs_rule_type_config`(`type`, `module_name`, `create_time`, `update_time`) VALUES (51, '销售退料入库单-单据编号', '2025-02-17 14:50:35', '2025-02-17 14:50:35');
INSERT INTO `dfs_rule_type_config`(`type`, `module_name`, `create_time`, `update_time`) VALUES (52, '委外订单入库单-单据编号', '2025-02-17 14:50:35', '2025-02-17 14:50:35');
INSERT INTO `dfs_rule_type_config`(`type`, `module_name`, `create_time`, `update_time`) VALUES (53, '委外领料出库单-单据编号', '2025-02-17 14:50:35', '2025-02-17 14:50:35');
INSERT INTO `dfs_rule_type_config`(`type`, `module_name`, `create_time`, `update_time`) VALUES (54, '委外补料出库单-单据编号', '2025-02-17 14:50:35', '2025-02-17 14:50:35');
INSERT INTO `dfs_rule_type_config`(`type`, `module_name`, `create_time`, `update_time`) VALUES (55, '委外退料入库单-单据编号', '2025-02-17 14:50:35', '2025-02-17 14:50:35');
INSERT INTO `dfs_rule_type_config`(`type`, `module_name`, `create_time`, `update_time`) VALUES (13, '质检报工-质检号', '2025-02-17 14:50:35', '2025-02-17 14:50:35');
INSERT INTO `dfs_rule_type_config`(`type`, `module_name`, `create_time`, `update_time`) VALUES (15, '产品检测-送检单号', '2025-02-17 14:50:35', '2025-02-17 14:50:35');
INSERT INTO `dfs_rule_type_config`(`type`, `module_name`, `create_time`, `update_time`) VALUES (30, '产品检测-质检单号', '2025-02-17 14:50:35', '2025-02-17 14:50:35');
INSERT INTO `dfs_rule_type_config`(`type`, `module_name`, `create_time`, `update_time`) VALUES (31, '产品检测-报告单号', '2025-02-17 14:50:35', '2025-02-17 14:50:35');
INSERT INTO `dfs_rule_type_config`(`type`, `module_name`, `create_time`, `update_time`) VALUES (32, '生产任务-单据编号', '2025-02-17 14:50:35', '2025-02-17 14:50:35');
INSERT INTO `dfs_rule_type_config`(`type`, `module_name`, `create_time`, `update_time`) VALUES (78, '客户档案-客户编码', '2025-02-17 14:50:35', '2025-02-17 14:50:35');
INSERT INTO `dfs_rule_type_config`(`type`, `module_name`, `create_time`, `update_time`) VALUES (3, '供应商档案-供应商编码', '2025-02-17 14:50:35', '2025-02-17 14:50:35');
INSERT INTO `dfs_rule_type_config`(`type`, `module_name`, `create_time`, `update_time`) VALUES (7, '炉号信息-电炉炉次号', '2025-02-17 14:50:35', '2025-02-17 14:50:35');
INSERT INTO `dfs_rule_type_config`(`type`, `module_name`, `create_time`, `update_time`) VALUES (8, '炉号信息-精炼炉炉次号', '2025-02-17 14:50:35', '2025-02-17 14:50:35');
INSERT INTO `dfs_rule_type_config`(`type`, `module_name`, `create_time`, `update_time`) VALUES (41, '委外订单-单据编号', '2025-02-17 14:50:35', '2025-02-17 14:50:35');
INSERT INTO `dfs_rule_type_config`(`type`, `module_name`, `create_time`, `update_time`) VALUES (45, '委外订单物料清单-单据编号', '2025-02-17 14:50:35', '2025-02-17 14:50:35');
INSERT INTO `dfs_rule_type_config`(`type`, `module_name`, `create_time`, `update_time`) VALUES (44, '委外订单收货单-单据编号', '2025-02-17 14:50:35', '2025-02-17 14:50:35');
INSERT INTO `dfs_rule_type_config`(`type`, `module_name`, `create_time`, `update_time`) VALUES (83, '委外发料单-单据编号', '2025-02-17 14:50:35', '2025-02-17 14:50:35');
INSERT INTO `dfs_rule_type_config`(`type`, `module_name`, `create_time`, `update_time`) VALUES (84, '委外退货单-单据编号', '2025-02-17 14:50:35', '2025-02-17 14:50:35');
INSERT INTO `dfs_rule_type_config`(`type`, `module_name`, `create_time`, `update_time`) VALUES (43, '委外订单收货单-收货批次号', '2025-02-17 14:50:35', '2025-02-17 14:50:35');
INSERT INTO `dfs_rule_type_config`(`type`, `module_name`, `create_time`, `update_time`) VALUES (81, '生产领料-申请单号', '2025-02-17 14:50:35', '2025-02-17 14:50:35');
INSERT INTO `dfs_rule_type_config`(`type`, `module_name`, `create_time`, `update_time`) VALUES (60, '包装方案-单据编号', '2025-02-17 14:50:35', '2025-02-17 14:50:35');
INSERT INTO `dfs_rule_type_config`(`type`, `module_name`, `create_time`, `update_time`) VALUES (67, 'SMT拼版-组合码', '2025-02-17 14:50:35', '2025-02-17 14:50:35');
INSERT INTO `dfs_rule_type_config`(`type`, `module_name`, `create_time`, `update_time`) VALUES (70, '巡检计划编号', '2025-02-17 14:50:35', '2025-02-17 14:50:35');
INSERT INTO `dfs_rule_type_config`(`type`, `module_name`, `create_time`, `update_time`) VALUES (71, '巡检任务编号', '2025-02-17 14:50:35', '2025-02-17 14:50:35');
INSERT INTO `dfs_rule_type_config`(`type`, `module_name`, `create_time`, `update_time`) VALUES (72, '点检计划编号', '2025-02-17 14:50:35', '2025-02-17 14:50:35');
INSERT INTO `dfs_rule_type_config`(`type`, `module_name`, `create_time`, `update_time`) VALUES (73, '点检任务编号', '2025-02-17 14:50:35', '2025-02-17 14:50:35');
INSERT INTO `dfs_rule_type_config`(`type`, `module_name`, `create_time`, `update_time`) VALUES (74, '保养计划编号', '2025-02-17 14:50:35', '2025-02-17 14:50:35');
INSERT INTO `dfs_rule_type_config`(`type`, `module_name`, `create_time`, `update_time`) VALUES (75, '保养任务编号', '2025-02-17 14:50:35', '2025-02-17 14:50:35');
INSERT INTO `dfs_rule_type_config`(`type`, `module_name`, `create_time`, `update_time`) VALUES (76, '光猫发货单-单据编号', '2025-02-17 14:50:35', '2025-02-17 14:50:35');
INSERT INTO `dfs_rule_type_config`(`type`, `module_name`, `create_time`, `update_time`) VALUES (79, '装箱发货单-单据编号', '2025-02-17 14:50:35', '2025-02-17 14:50:35');
INSERT INTO `dfs_rule_type_config`(`type`, `module_name`, `create_time`, `update_time`) VALUES (86, '包装工单-单据编号', '2025-02-17 14:50:35', '2025-02-17 14:50:35');
INSERT INTO `dfs_rule_type_config`(`type`, `module_name`, `create_time`, `update_time`) VALUES (87, '包装工单-包装码', '2025-02-17 14:50:35', '2025-02-17 14:50:35');
INSERT INTO `dfs_rule_type_config`(`type`, `module_name`, `create_time`, `update_time`) VALUES (88, '不良定义-不良类型编号', '2025-02-17 14:50:35', '2025-02-17 14:50:35');
INSERT INTO `dfs_rule_type_config`(`type`, `module_name`, `create_time`, `update_time`) VALUES (102, '不良定义-不良代号', '2025-02-17 14:50:35', '2025-02-17 14:50:35');
INSERT INTO `dfs_rule_type_config`(`type`, `module_name`, `create_time`, `update_time`) VALUES (103, '维修定义-维修代号', '2025-02-17 14:50:35', '2025-02-17 14:50:35');
INSERT INTO `dfs_rule_type_config`(`type`, `module_name`, `create_time`, `update_time`) VALUES (95, '销售退货单', '2025-02-17 14:50:35', '2025-02-17 14:50:35');
INSERT INTO `dfs_rule_type_config`(`type`, `module_name`, `create_time`, `update_time`) VALUES (96, '销售退货单批次', '2025-02-17 14:50:35', '2025-02-17 14:50:35');
INSERT INTO `dfs_rule_type_config`(`type`, `module_name`, `create_time`, `update_time`) VALUES (97, '电池条码', '2025-02-17 14:50:35', '2025-02-17 14:50:35');
INSERT INTO `dfs_rule_type_config`(`type`, `module_name`, `create_time`, `update_time`) VALUES (100, '生产工单-流转卡号', '2025-02-17 14:50:35', '2025-02-17 14:50:35');
INSERT INTO `dfs_rule_type_config`(`type`, `module_name`, `create_time`, `update_time`) VALUES (89, '质量管理-检验项目组', '2025-02-17 14:50:35', '2025-02-17 14:50:35');
INSERT INTO `dfs_rule_type_config`(`type`, `module_name`, `create_time`, `update_time`) VALUES (90, '质量管理-检验项目', '2025-02-17 14:50:35', '2025-02-17 14:50:35');
INSERT INTO `dfs_rule_type_config`(`type`, `module_name`, `create_time`, `update_time`) VALUES (91, '检验单-单据编号', '2025-02-17 14:50:35', '2025-02-17 14:50:35');
INSERT INTO `dfs_rule_type_config`(`type`, `module_name`, `create_time`, `update_time`) VALUES (92, '质量管理-缺陷定义', '2025-02-17 14:50:35', '2025-02-17 14:50:35');
INSERT INTO `dfs_rule_type_config`(`type`, `module_name`, `create_time`, `update_time`) VALUES (93, '质量管理-抽样方案', '2025-02-17 14:50:35', '2025-02-17 14:50:35');
INSERT INTO `dfs_rule_type_config`(`type`, `module_name`, `create_time`, `update_time`) VALUES (94, '质量管理-检验方案', '2025-02-17 14:50:35', '2025-02-17 14:50:35');
INSERT INTO `dfs_rule_type_config`(`type`, `module_name`, `create_time`, `update_time`) VALUES (101, '质量管理-巡检-巡检计划', '2025-02-17 14:50:35', '2025-02-17 14:50:35');
INSERT INTO `dfs_rule_type_config`(`type`, `module_name`, `create_time`, `update_time`) VALUES (500, '质量管理-来料检验单', '2025-02-17 14:50:35', '2025-02-17 14:50:35');
INSERT INTO `dfs_rule_type_config`(`type`, `module_name`, `create_time`, `update_time`) VALUES (501, '质量管理-生产检验单', '2025-02-17 14:50:35', '2025-02-17 14:50:35');
INSERT INTO `dfs_rule_type_config`(`type`, `module_name`, `create_time`, `update_time`) VALUES (502, '质量管理-生产巡检单', '2025-02-17 14:50:35', '2025-02-17 14:50:35');
INSERT INTO `dfs_rule_type_config`(`type`, `module_name`, `create_time`, `update_time`) VALUES (503, '质量管理-销售检验单', '2025-02-17 14:50:35', '2025-02-17 14:50:35');
INSERT INTO `dfs_rule_type_config`(`type`, `module_name`, `create_time`, `update_time`) VALUES (504, '质量管理-其他检验单', '2025-02-17 14:50:35', '2025-02-17 14:50:35');
INSERT INTO `dfs_rule_type_config`(`type`, `module_name`, `create_time`, `update_time`) VALUES (59, '采购退料-单据编号', '2025-02-17 14:50:35', '2025-02-17 14:50:35');
INSERT INTO `dfs_rule_type_config`(`type`, `module_name`, `create_time`, `update_time`) VALUES (104, '维修工单-单据编号', '2025-02-17 14:50:35', '2025-02-17 14:50:35');

-- 初始化编码规则父级类型数据
INSERT INTO `dfs_rule_type_parent_config` (`type`, `name`) VALUES ('SALE', '销售管理');
INSERT INTO `dfs_rule_type_parent_config` (`type`, `name`) VALUES ('PRODUCTION', '生产管理');
INSERT INTO `dfs_rule_type_parent_config` (`type`, `name`) VALUES ('PURCHASE', '采购管理');
INSERT INTO `dfs_rule_type_parent_config` (`type`, `name`) VALUES ('WAREHOUSE', '仓储管理');
INSERT INTO `dfs_rule_type_parent_config` (`type`, `name`) VALUES ('QUALITY', '质量管理');
INSERT INTO `dfs_rule_type_parent_config` (`type`, `name`) VALUES ('BASIC', '基础资料');
INSERT INTO `dfs_rule_type_parent_config` (`type`, `name`) VALUES ('OUTSOURCE', '委外管理');
INSERT INTO `dfs_rule_type_parent_config` (`type`, `name`) VALUES ('EQUIPMENT', '设备管理');
INSERT INTO `dfs_rule_type_parent_config` (`type`, `name`) VALUES ('PACKAGE', '包装管理');
INSERT INTO `dfs_rule_type_parent_config` (`type`, `name`) VALUES ('REPAIR', '维修管理');
INSERT INTO `dfs_rule_type_parent_config` (`type`, `name`) VALUES ('OTHER', '其他');

-- 更新父级类型编码
-- 销售相关
UPDATE `dfs_rule_type_config` SET `parent_type_code` = 'SALE' WHERE `type` IN (1, 16, 17, 95, 96);
-- 生产相关
UPDATE `dfs_rule_type_config` SET `parent_type_code` = 'PRODUCTION' WHERE `type` IN (2, 5, 10, 12, 14, 32, 34, 57, 61, 77, 80, 85, 100,18,9,81,67,97);
-- 采购相关
UPDATE `dfs_rule_type_config` SET `parent_type_code` = 'PURCHASE' WHERE `type` IN (19, 20, 21, 22, 23, 11, 33, 59);
-- 仓储相关
UPDATE `dfs_rule_type_config` SET `parent_type_code` = 'WAREHOUSE' WHERE `type` IN (27, 28, 29, 35, 36, 37, 6, 38, 39, 40, 42, 46, 47, 48, 49, 50, 51, 62, 63, 64, 65, 66);
-- 质量相关
UPDATE `dfs_rule_type_config` SET `parent_type_code` = 'QUALITY' WHERE `type` IN (13, 15, 24, 25, 30, 31, 88, 89, 90, 91, 92, 93, 94, 101, 102, 500, 501, 502, 503, 504);
-- 基础资料相关
UPDATE `dfs_rule_type_config` SET `parent_type_code` = 'BASIC' WHERE `type` IN (3, 4, 26, 78, 82, 98,56,58);
-- 委外相关
UPDATE `dfs_rule_type_config` SET `parent_type_code` = 'OUTSOURCE' WHERE `type` IN (41, 43, 44, 45, 52, 53, 54, 55, 83, 84);
-- 设备相关
UPDATE `dfs_rule_type_config` SET `parent_type_code` = 'EQUIPMENT' WHERE `type` IN (70, 71, 72, 73, 74, 75);
-- 包装相关
UPDATE `dfs_rule_type_config` SET `parent_type_code` = 'PACKAGE' WHERE `type` IN (60, 76, 79, 86, 87);
-- 维修相关
UPDATE `dfs_rule_type_config` SET `parent_type_code` = 'REPAIR' WHERE `type` IN (103, 104);
UPDATE `dfs_rule_type_config` SET `parent_type_code` = 'OTHER' WHERE `type` IN (7, 8);


-- 编码规则组成信息默认不能输入
UPDATE `dfs_rule_prefix_config` SET `input` = 0 WHERE `code` in (3,6,7,9,10,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31);

-- 删除原记录，新增编码规则(有些现网没有采购退料-单据编号，保险措施为全删除，添加新的编码规则)
DELETE FROM `dfs_config_rule` WHERE `rule_type` = '59';
INSERT INTO `dfs_config_rule`(`rule_type`, `rule_type_name`) VALUES (59, '采购退料-单据编号');
UPDATE `dfs_config_number_rules` SET `prefix_detail` = REPLACE(`prefix_detail`,'\"example\":\"WXGD\"','\"example\":\"CGTL\"') WHERE `type` = 59;
-- 删除无用表
DROP TABLE `dfs_config_rule`;

-- 更新编码规则存储过程
DELIMITER $$
DROP PROCEDURE IF EXISTS init_number_rules;
CREATE DEFINER=`root`@`%` PROCEDURE `init_number_rules`(in var_id int(11), in var_rule_name varchar(255))
top:
BEGIN
    -- 定义变量
    DECLARE type_id int;
    DECLARE uuid_val CHAR(36);
    DECLARE prefix VARCHAR(500);
    DECLARE suffix VARCHAR(500);
    DECLARE nowTime TIMESTAMP;

    -- 获取当前时间
    SET nowTime = NOW();
    -- 生成UUID
    SELECT UUID() INTO uuid_val;
    -- 设置编码规则JSON模板
    SET prefix = '[{\"code\":2,\"name\":\"当前日期\",\"rule\":\"yyyyMMdd\",\"initValue\":1,\"example\":\"20240813\"},{\"code\":4,\"name\":\"自动生成序号\",\"rule\":\"3\",\"autoIncrementConfigureType\":\"day\",\"uuid\":\"';
    SET suffix = '\",\"initValue\":1,\"example\":\"001\"}]';

    -- 查询是否存在指定的规则类型
    SELECT type INTO type_id FROM dfs_rule_type_config WHERE type = var_id;

    -- 如果存在，则结束存储过程,避免重复刷脚本导致脏数据生成
    IF type_id IS NOT NULL THEN
        LEAVE top;
    END IF;

    -- 1. 插入规则类型配置(没填则默认归属于'其他')
    INSERT INTO `dfs_rule_type_config`(`type`, `module_name`, `create_time`, `update_time`, `parent_type_code`) VALUES (var_id, var_rule_name, nowTime, nowTime, 'OTHER');

    -- 2. 更新前缀配置的allow_rule_types字段
    UPDATE `dfs_rule_prefix_config` SET `allow_rule_types` = CASE
     WHEN `allow_rule_types` IS NULL THEN var_id
     WHEN FIND_IN_SET(var_id, `allow_rule_types`) = 0
         THEN CONCAT(`allow_rule_types`, ',', var_id)
     ELSE `allow_rule_types`
    END,
    `update_time` = nowTime WHERE `code` IN (1, 2, 4, 5);

    -- 3. 更新自增配置的allow_rule_types字段
    UPDATE `dfs_rule_auto_increment_config` SET `allow_rule_types` = CASE
     WHEN `allow_rule_types` IS NULL THEN var_id
     WHEN FIND_IN_SET(var_id, `allow_rule_types`) = 0
         THEN CONCAT(`allow_rule_types`, ',', var_id)
     ELSE `allow_rule_types`
    END,
    `update_time` = nowTime WHERE `code` IN ('day', 'month', 'year', 'noCross');

    -- 4. 插入编码规则实例
    INSERT INTO `dfs_config_number_rules`(`type`, `name`, `prefix_detail`, `create_by`, `update_by`, `create_time`, `update_time`, `is_default`)
    VALUES (var_id, '编码规则', CONCAT(prefix, uuid_val, suffix), 'admin', NULL, nowTime, NULL, 1);

END $$
DELIMITER ;

-- 新增存储过程适应新添加的编码规则
DELIMITER $$
CREATE DEFINER=`root`@`%` PROCEDURE `init_number_rules_module`(in var_id int(11), in var_rule_name varchar(255), in var_parent_type_code varchar(255))
top:
BEGIN
    -- 定义变量
    DECLARE type_id int;
    DECLARE uuid_val CHAR(36);
    DECLARE prefix VARCHAR(500);
    DECLARE suffix VARCHAR(500);
    DECLARE nowTime TIMESTAMP;

    -- 获取当前时间
    SET nowTime = NOW();
    -- 生成UUID
    SELECT UUID() INTO uuid_val;
    -- 设置编码规则JSON模板
    SET prefix = '[{\"code\":2,\"name\":\"当前日期\",\"rule\":\"yyyyMMdd\",\"initValue\":1,\"example\":\"20240813\"},{\"code\":4,\"name\":\"自动生成序号\",\"rule\":\"3\",\"autoIncrementConfigureType\":\"day\",\"uuid\":\"';
    SET suffix = '\",\"initValue\":1,\"example\":\"001\"}]';

    -- 查询是否存在指定的规则类型
    SELECT type INTO type_id FROM dfs_rule_type_config WHERE type = var_id;

    -- 如果存在，则结束存储过程,避免重复刷脚本导致脏数据生成
    IF type_id IS NOT NULL THEN
            LEAVE top;
    END IF;

    -- 1. 插入规则类型配置(没填则默认归属于'其他')
    INSERT INTO `dfs_rule_type_config`(`type`, `module_name`, `create_time`, `update_time`, `parent_type_code`) VALUES (var_id, var_rule_name, nowTime, nowTime, var_parent_type_code);

    -- 2. 更新前缀配置的allow_rule_types字段
    UPDATE `dfs_rule_prefix_config` SET `allow_rule_types` = CASE
         WHEN `allow_rule_types` IS NULL THEN var_id
         WHEN FIND_IN_SET(var_id, `allow_rule_types`) = 0
             THEN CONCAT(`allow_rule_types`, ',', var_id)
         ELSE `allow_rule_types`
    END,
    `update_time` = nowTime WHERE `code` IN (1, 2, 4, 5);

    -- 3. 更新自增配置的allow_rule_types字段
    UPDATE `dfs_rule_auto_increment_config` SET `allow_rule_types` = CASE
         WHEN `allow_rule_types` IS NULL THEN var_id
         WHEN FIND_IN_SET(var_id, `allow_rule_types`) = 0
             THEN CONCAT(`allow_rule_types`, ',', var_id)
         ELSE `allow_rule_types`
    END,
    `update_time` = nowTime WHERE `code` IN ('day', 'month', 'year', 'noCross');

    -- 4. 插入编码规则实例
    INSERT INTO `dfs_config_number_rules`(`type`, `name`, `prefix_detail`, `create_by`, `update_by`, `create_time`, `update_time`, `is_default`)
    VALUES (var_id, '编码规则', CONCAT(prefix, uuid_val, suffix), 'admin', NULL, nowTime, NULL, 1);

END $$
DELIMITER ;

call init_number_rules_module(104, '维修工单-单据编号', 'REPAIR');

-- ================================================此脚本专为编码规则设定，如需写脚本，请移步到3.17.1.1=======================================================
-- ================================================此脚本专为编码规则设定，如需写脚本，请移步到3.17.1.1=======================================================
-- ================================================此脚本专为编码规则设定，如需写脚本，请移步到3.17.1.1=======================================================
-- ================================================此脚本专为编码规则设定，如需写脚本，请移步到3.17.1.1=======================================================
-- ================================================此脚本专为编码规则设定，如需写脚本，请移步到3.17.1.1=======================================================
