-- 指标的
call `dfs_metrics`.`proc_modify_column`(
        'dfs_metrics_sale_order_material_daily',
        'customer_code',
        'ALTER TABLE `dfs_metrics_sale_order_material_daily` DROP COLUMN `customer_code`');

call `dfs_metrics`.`proc_modify_column`(
        'dfs_metrics_sale_order_material_daily',
        'customer_name',
        'ALTER TABLE `dfs_metrics_sale_order_material_daily` DROP COLUMN `customer_name`');

call `dfs_metrics`.`proc_modify_column`(
        'dfs_metrics_sale_order_material_daily',
        'salesman_code',
        'ALTER TABLE `dfs_metrics_sale_order_material_daily` DROP COLUMN `salesman_code`');

call `dfs_metrics`.`proc_modify_column`(
        'dfs_metrics_sale_order_material_daily',
        'salesman_name',
        'ALTER TABLE `dfs_metrics_sale_order_material_daily` DROP COLUMN `salesman_name`');

DROP TABLE IF EXISTS `dfs_metrics`.`dfs_metrics_sale_order`;

-- 自定义指标组对象视图脚本变更
UPDATE `dfs_target_model` SET `script` = 'select vdrl.report_date,vdwo.line_name ,vdm.`code`, vdm.`name`, vdm.`standard`,vdm.`drawing_number`,
vdrl.`work_order`,vdwo.product_order_number ,vdwo.`sale_order_number`,vdrl.`finish_count`,vdrl.unqualified,
vdrl.user_nickname ,vdrl.operator_name,vdrl.shift_type ,vdrl.device_code,vdrl.device_id
from dfs_metrics.v_dfs_report_line vdrl,dfs_metrics.v_dfs_work_order vdwo,dfs_metrics.v_dfs_material vdm
where vdrl.work_order = vdwo.work_order_number
and vdwo.material_code = vdm.code' WHERE `target_name` = 'discreteProcessingProductionSchedule';
UPDATE `dfs_target_model` SET `script` = 'SELECT aaa.record_date,aaa.work_order_number,aaa.defect_count_sum,aaa.defect_1,aaa.defect_2,aaa.defect_3,aaa.defect_4,aaa.defect_5,aaa.defect_6,aaa.defect_7,aaa.defect_8,aaa.defect_9,aaa.defect_10,vdm.name,vdm.standard,vdm.code,vdm.drawing_number,vdwo.product_order_number  FROM
(SELECT DATE(time) record_date ,work_order_number,sum(defect_count) as defect_count_sum,
case when rank=1 then defect_name end as defect_1,
case when rank=2 then defect_name end as defect_2,
case when rank=3 then defect_name end as defect_3,
case when rank=4 then defect_name end as defect_4,
case when rank=5 then defect_name end as defect_5,
case when rank=6 then defect_name end as defect_6,
case when rank=7 then defect_name end as defect_7,
case when rank=8 then defect_name end as defect_8,
case when rank=9 then defect_name end as defect_9,
case when rank=10 then defect_name end as defect_10
from dfs_metrics.dfs_metrics_top_work_order dmtwo
group by record_date ,work_order_number
)aaa, dfs_metrics.v_dfs_work_order vdwo,dfs_metrics.v_dfs_material vdm
where aaa.work_order_number = vdwo.work_order_number and vdwo.material_code = vdm.code' WHERE `target_name` = 'discreteProcessingQualityStatistics';
UPDATE `dfs_target_model` SET `script` = 'SELECT device_name,fname,line_name,material_name,nick_name,procedure_name,product_order_number,COUNT(id) as quantity,record_date,record_hour,relation_number from
(SELECT ee.*,su.nick_name from
(SELECT cc.*,dd.device_name from
(SELECT bb.*,df.fname from
(SELECT aa.*,dcp.procedure_name from
(SELECT date(dpfcr.report_time) record_date,HOUR(dpfcr.report_time) record_hour,dpfcr.report_by,dpfcr.material_name,dpfcr.relation_number,dpfcr.produre_id ,dwo.line_name ,dpfcr.fac_id ,dpfcr.device_id ,dpfcr.id,dpfcr.product_order_number
from dfs.dfs_product_flow_code_record dpfcr
left join dfs_work_order dwo on dpfcr.relation_number = dwo.work_order_number) aa
left join dfs_craft_procedure dcp on aa.produre_id = dcp.id)bb
left join dfs_facilities df on bb.fac_id = df.fid)cc
left join dfs_device dd on cc.device_id = dd.device_id )ee
left join sys_users su on ee.report_by = su.user_name) ff
group by record_date,record_hour,nick_name,relation_number,procedure_name' WHERE `target_name` = 'discreteProcessingPersonnelSalaryStatistics';
UPDATE `dfs_target_model` SET `script` = 'SELECT dmso.sale_order_number,dmso.customer_name,dmso.customer_code,dmso.salesman_name,dmso.material_name,dmso.material_code,vasom.customer_material_code ,vasom.customer_material_name ,vasom.order_date ,vasom.require_goods_date ,vasom.shipment_status,vasom.process_status,dmso.sales_quantity,dmso.plan_quantity,dmso.produce_quantity,dmso.applied_shipment_quantity,dmso.applied_quantity,dmso.delay_quantity ,vasom.return_quantity FROM dfs_metrics.dfs_metrics_sale_order dmso, dfs_metrics.v_ams_sale_order_material vasom WHERE dmso.sale_order_number = vasom.sale_order_number and dmso.material_code =vasom.material_code' WHERE `target_name` = 'assemblyLineSalesSchedule';
UPDATE `dfs_target_model` SET `script` = 'SELECT vdwo.sale_order_number ,vdwo.product_order_number ,dmwod.work_order_number,dmwod.plan_quantity,dmwod.produce_quantity,dmwod.inspection_quantity,dmwod.unqualified_quantity,dmwod.inbound_quantity,dmwod.direct_access_quantity,dmwod.start_work_time,dmwod.working_people_quantity,dmwod.actual_working_hour,dmwod.theory_working_hour,dmwod.qualified_rate,dmwod.product_efficiency_rate,dmwod.achievements,dmwod.repair_quantity,dmwod.repair_qualified_quantity,dmwod.repair_quantity_rate,dmwod.record_date,dmwod.time,dmwod.unqualified_record_quantity,dmwod.unqualified_record_item_quantity from dfs_metrics.dfs_metrics_work_order_daily dmwod , dfs_metrics.v_dfs_work_order vdwo
where dmwod.work_order_number = vdwo.work_order_number' WHERE `target_name` = 'assemblyLineProductionSchedule';
