-- 业务配置新增字段
call proc_add_column(
        'dfs_business_config',
        'service_name',
        'ALTER TABLE `dfs_business_config` ADD COLUMN `service_name` varchar(255) DEFAULT '''' COMMENT ''服务名称''');

-- 将第三方相关的下推数据，服务名设置为wms
UPDATE `dfs_business_config` SET `service_name` = 'wms'
WHERE `full_path_code` in ('subcontract.subcontractMaterialsListPushDownConfig.subcontractOutputPicking.jumpPage','subcontract.subcontractMaterialsListPushDownConfig.subcontractOutputPicking','production.productMaterialsListPushDownConfig.outputPickingProduct','production.productMaterialsListPushDownConfig.workOrderSupplement','production.productMaterialsListPushDownConfig.transferOrder','production.productMaterialsListPushDownConfig.outputPickingProduct.jumpPage','production.productMaterialsListPushDownConfig.workOrderSupplement.jumpPage','production.productMaterialsListPushDownConfig.transferOrder.jumpPage','purchase.purchaseReturnApplicationPushDownConfig.purchaseReturnOut','purchase.purchaseReturnApplicationPushDownConfig.purchaseReturnOut.jumpPage','delivery.deliveryApplicationPushDownConfig.saleInAndOut','delivery.deliveryApplicationPushDownConfig.saleInAndOut.jumpPage','delivery.deliveryApplicationPushDownConfig.transferOrder','delivery.deliveryApplicationPushDownConfig.transferOrder.jumpPage','purchase.purchaseReceiptPushDownConfig.requestStockInAndOut','purchase.purchaseReceiptPushDownConfig.requestStockInAndOut.autoPushDownPurchaseIn','warehouse','purchaseInAndOut','subcontractInAndOut','saleInAndOut','otherInAndOut','warehouse.warehouseViewPermissionConfig','warehouse.warehouseViewPermissionConfig.accountPermission','warehouse.warehouseViewPermissionConfig.batchPermission','warehouse.warehouseViewPermissionConfig.warehouseBillPermission','purchaseInAndOut.purchaseInPushDownConfig','purchaseInAndOut.purchaseInPushDownConfig.purchaseReturnOut','purchaseInAndOut.purchaseInPushDownConfig.purchaseReturnOut.jumpPage','subcontractInAndOut.subcontractProductOutboundPushDownConfig','subcontractInAndOut.subcontractSupplementaryFoodPushDownConfig','subcontractInAndOut.subcontractProductOutboundPushDownConfig.subcontractReturnReceipt','subcontractInAndOut.subcontractSupplementaryFoodPushDownConfig.subcontractReturnReceipt','subcontractInAndOut.subcontractProductOutboundPushDownConfig.subcontractReturnReceipt.jumpPage','subcontractInAndOut.subcontractSupplementaryFoodPushDownConfig.subcontractReturnReceipt.jumpPage','saleInAndOut.saleOutPushDownConfig','saleInAndOut.saleOutPushDownConfig.salesReturnReceiptDoc','saleInAndOut.saleOutPushDownConfig.salesReturnReceiptDoc.jumpPage','otherInAndOut.stockOtherAppFormPushDownConfig','otherInAndOut.stockOtherAppFormPushDownConfig.stockAllocation','otherInAndOut.stockOtherAppFormPushDownConfig.stockAllocation.jumpPage','purchase.purchaseReturnApplicationPushDownConfig.purchaseReturnOut','purchase.purchaseReturnApplicationPushDownConfig.purchaseReturnOut.jumpPage','subcontract.subcontractMaterialsListPushDownConfig.subcontractSupplement','subcontract.subcontractMaterialsListPushDownConfig.subcontractSupplement.jumpPage','subcontract.subcontractMaterialsListPushDownConfig.transferOrder','subcontract.subcontractMaterialsListPushDownConfig.transferOrder.jumpPage','subcontract.subcontractReceiptOrderPushDownConfig.subcontractIn','subcontract.subcontractReceiptOrderPushDownConfig.subcontractIn.jumpPage','subcontract.subcontractReceiptOrderPushDownConfig.subcontractReturnMaterialsList','subcontract.subcontractReceiptOrderPushDownConfig.subcontractReturnMaterialsList.jumpPage','subcontract.subcontractOrderPushDownConfig.subcontractProductOutbound','subcontract.subcontractOrderPushDownConfig.subcontractProductOutbound.jumpPage');





