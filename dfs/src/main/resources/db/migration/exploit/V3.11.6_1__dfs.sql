-- DDL

-- DML

-- 业务配置-采购收料小程序配置-自动下推入库单配置
INSERT INTO `dfs_business_config_value` (`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`)
VALUES (null, 'targetOrderState', '目标单据状态', 'purchase.receiptOrderAppConfig.autoPushPurchaseIn.targetOrderState', 'purchase.receiptOrderAppConfig.autoPushPurchaseIn', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":0,\"label\":\"创建\"},{\"value\":1,\"label\":\"生效\"}]', '1', NULL);

call proc_add_form_field("workOrderReport.list", "reportFieldOneName", "报工扩展字段1");
call proc_add_form_field("workOrderReport.list", "reportFieldTwoName", "报工扩展字段2");
call proc_add_form_field("workOrderReport.list", "reportFieldThreeName", "报工扩展字段3");
call proc_add_form_field("workOrderReport.list", "reportFieldFourName", "报工扩展字段4");
call proc_add_form_field("workOrderReport.list", "reportFieldFiveName", "报工扩展字段5");

INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/workorder-model/production-records', 'workOrderReport.list', 'workOrderReport.list', 'reportFieldOneName', 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/workorder-model/production-records', 'workOrderReport.list', 'workOrderReport.list', 'reportFieldTwoName', 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/workorder-model/production-records', 'workOrderReport.list', 'workOrderReport.list', 'reportFieldThreeName', 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/workorder-model/production-records', 'workOrderReport.list', 'workOrderReport.list', 'reportFieldFourName', 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/workorder-model/production-records', 'workOrderReport.list', 'workOrderReport.list', 'reportFieldFiveName', 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);

-- 采购订单表单配置,列表-按单,增加创建时间字段
call proc_add_form_field("purchaseOrder.list.order", "createTime", "创建时间");
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-list', 'purchaseOrder.list.order', 'purchaseOrder.list.order', 'createTime', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);

-- 新增业务配置 : 工艺参数的参数值通过业务配置来判断是否必填
INSERT INTO `dfs_business_config`(`id`, `code`, `name`, `full_path_code`, `parent_full_path_code`, `description`, `create_by`, `update_by`, `create_time`, `update_time`) VALUES (NULL, 'requiredConf', '必填项配置', 'design.craftConfig.requiredConf', 'design.craftConfig', '此配置用于工艺相关数据的必填项校验设置', 'yelinkoncall', 'yelinkoncall', '2024-01-18 06:14:57', '2024-01-18 06:14:57');
INSERT INTO `dfs_business_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'craftProcessParameterValueRequired', '工序-工艺参数参数值', 'design.craftConfig.requiredConf.craftProcessParameterValueRequired', 'design.craftConfig.requiredConf', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"必填\"},{\"value\":false,\"label\":\"非必填\"}]', 'false', NULL);

call init_number_rules(101, '质量管理-巡检-巡检计划');

-- tpm保存并生效 权限
INSERT INTO `sys_permissions` (`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`, `is_sys_data`) VALUES ('10307080', '保存并生效', 'equip.check.project:addAndReleased', NULL, NULL, NULL, NULL, '2024-11-01 17:13:51', 'enable', 'POST', '10307', 2, 1, 0, '/equipment-management/equip-checked/equipCheckProject', 1, NULL, '', 1);
INSERT INTO `sys_permissions` (`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`, `is_sys_data`) VALUES ('10308090', '保存并生效', 'equip.check.plan:addAndReleased', NULL, NULL, NULL, NULL, '2024-11-01 17:13:51', 'enable', 'GET', '10308', 2, 1, 0, '/equipment-management/equip-checked/equipCheckPlan', 1, NULL, '', 1);


-- 清除工序质检方案/维修方案 的脏数据
DELETE FROM `dfs_procedure_def_maintain_scheme` WHERE maintain_id is null;
DELETE FROM  `dfs_procedure_maintain_scheme` WHERE maintain_id is null;
DELETE FROM `dfs_procedure_def_defect_scheme` WHERE scheme_id is null;
DELETE FROM  `dfs_procedure_defect_scheme` WHERE scheme_id is null;