-- ================================================此脚本专为审批需求设定，如需写脚本，请移步到3.17.1.1=======================================================
-- ================================================此脚本专为审批需求设定，如需写脚本，请移步到3.17.1.1=======================================================
-- ================================================此脚本专为审批需求设定，如需写脚本，请移步到3.17.1.1=======================================================
-- ================================================此脚本专为审批需求设定，如需写脚本，请移步到3.17.1.1=======================================================
-- ================================================此脚本专为审批需求设定，如需写脚本，请移步到3.17.1.1=======================================================

DROP TABLE IF EXISTS `dfs_approve_docking_config`;
CREATE TABLE IF NOT EXISTS `dfs_approve_docking_config` (
    `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'id',
    `docking_code` varchar(255) DEFAULT NULL COMMENT '对接编码',
    `corp_id` varchar(255) DEFAULT NULL COMMENT '企业ID',
    `agent_id` varchar(255) DEFAULT NULL COMMENT '应用ID',
    `agent_secret` varchar(255) DEFAULT NULL COMMENT '应用密钥',
    `url` varchar(255) DEFAULT NULL COMMENT '接收消息服务器url',
    `token` varchar(255) DEFAULT NULL COMMENT '接收消息服务器token',
    `encoding_aes_key` varchar(255) DEFAULT NULL COMMENT '接收消息服务器EncodingAESKey',
    `whitelist_ip`text DEFAULT NULL COMMENT '白名单ip',
    `update_by` varchar(50) DEFAULT NULL COMMENT '更新人',
    `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
    `update_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `create_time` datetime DEFAULT NULL COMMENT '创建时间',
    `approve_callback_state` tinyint(1) DEFAULT 0 COMMENT '审批回调状态',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='审批对接配置表';

DROP TABLE IF EXISTS `dfs_approve_node_config`;
CREATE TABLE IF NOT EXISTS `dfs_approve_node_config` (
    `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'id',
    `code` varchar(100) DEFAULT NULL COMMENT '节点编码',
    `name` varchar(255) DEFAULT NULL COMMENT '节点名称',
    `full_path_code` varchar(750) DEFAULT NULL COMMENT '节点的全路径编码，父级编码.子级编码',
    `parent_full_path_code` varchar(750) DEFAULT '' COMMENT '父级节点的全路径编码',
    `is_approve` int(11) DEFAULT NULL COMMENT '是否需要审批',
    `service_name` varchar(255) DEFAULT '' COMMENT '服务名称',
    `create_by` varchar(255) DEFAULT NULL COMMENT '创建人',
    `update_by` varchar(255) DEFAULT NULL COMMENT '修改人',
    `create_time` datetime DEFAULT NULL COMMENT '创建时间',
    `update_time` datetime DEFAULT NULL COMMENT '修改时间',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `full_path_code` (`full_path_code`) USING BTREE,
    KEY `parent_full_path_code` (`parent_full_path_code`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='审批节点配置表';

DROP TABLE IF EXISTS `dfs_approve_node_module`;
CREATE TABLE IF NOT EXISTS `dfs_approve_node_module` (
    `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'id',
    `full_path_code` varchar(750) DEFAULT NULL COMMENT '节点的全路径编码，父级编码.子级编码',
    `module_code` varchar(255) NOT NULL COMMENT '模块编码',
    `module_name` varchar(255) DEFAULT '' COMMENT '模块名称',
    `value_source` varchar(255) DEFAULT NULL COMMENT '字段取值来源，数组时必填',
    `seq` int(11) DEFAULT 1 COMMENT '序号',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `full_path_code` (`full_path_code`, `seq`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='审批节点模块表';

DROP TABLE IF EXISTS `dfs_approve_node_field`;
CREATE TABLE IF NOT EXISTS `dfs_approve_node_field` (
    `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'id',
    `full_path_code` varchar(750) DEFAULT NULL COMMENT '节点的全路径编码，父级编码.子级编码',
    `module_code` varchar(255) NOT NULL COMMENT '模块编码',
    `field_code` varchar(750) NOT NULL COMMENT '业务字段编码',
    `field_name` varchar(255) DEFAULT '' COMMENT '业务字段名称',
    PRIMARY KEY (`id`) USING BTREE,
    KEY `full_path_code` (`full_path_code`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='审批节点字段表';

DROP TABLE IF EXISTS `dfs_approve_template`;
CREATE TABLE IF NOT EXISTS `dfs_approve_template` (
    `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'id',
    `full_path_code` varchar(750) DEFAULT NULL COMMENT '节点的全路径编码，父级编码.子级编码',
    `name` varchar(255) DEFAULT NULL COMMENT '模板名称',
    `outer_template_id` varchar(255) DEFAULT NULL COMMENT '外部系统模板ID',
    `create_by` varchar(255) DEFAULT NULL COMMENT '创建人',
    `update_by` varchar(255) DEFAULT NULL COMMENT '修改人',
    `create_time` datetime DEFAULT NULL COMMENT '创建时间',
    `update_time` datetime DEFAULT NULL COMMENT '修改时间',
    PRIMARY KEY (`id`) USING BTREE,
    KEY `full_path_code` (`full_path_code`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='审批模板表';

DROP TABLE IF EXISTS `dfs_approve_template_control`;
CREATE TABLE IF NOT EXISTS `dfs_approve_template_control` (
    `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'id',
    `full_path_code` varchar(750) DEFAULT NULL COMMENT '节点的全路径编码，父级编码.子级编码',
    `template_id` int(11) DEFAULT NULL COMMENT '模板ID',
    `control` varchar(255) DEFAULT NULL COMMENT '控件类型',
    `control_two` varchar(255) DEFAULT '' COMMENT '控件类型2',
    `outer_control_id` varchar(255) DEFAULT NULL COMMENT '外部系统控件ID',
    `name` varchar(255) DEFAULT NULL COMMENT '控件名称',
    `description` varchar(255) DEFAULT NULL COMMENT '控件说明',
    `is_require` tinyint(1) DEFAULT 0 COMMENT '是否必填',
    `is_print` tinyint(1) DEFAULT 1 COMMENT '是否参与打印',
    `parent_outer_control_id` varchar(255) DEFAULT '' COMMENT '父外部系统控件ID',
    `seq` int(11) DEFAULT 1 COMMENT '序号',
    `option_values` text COMMENT '可选值选项',
    `module_code` varchar(255) DEFAULT NULL COMMENT '模块编码',
    `field_code` varchar(750) DEFAULT NULL COMMENT '业务字段编码',
    `is_outer_add` tinyint(1) DEFAULT 0 COMMENT '是否是外部系统添加',
    PRIMARY KEY (`id`) USING BTREE,
    KEY `full_path_code` (`full_path_code`) USING BTREE,
    KEY `template_outer_control` (`template_id`, `outer_control_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='审批模板控件表';

DROP TABLE IF EXISTS `dfs_approve_control_field`;
CREATE TABLE IF NOT EXISTS `dfs_approve_control_field` (
    `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'id',
    `full_path_code` varchar(750) DEFAULT NULL COMMENT '节点的全路径编码，父级编码.子级编码',
    `template_id` int(11) DEFAULT NULL COMMENT '模板ID',
    `control_id` int(11) DEFAULT NULL COMMENT '控件ID',
    `module_code` varchar(255) DEFAULT NULL COMMENT '模块编码',
    `field_code` varchar(750) DEFAULT NULL COMMENT '业务字段编码',
    PRIMARY KEY (`id`) USING BTREE,
    KEY `full_path_code` (`full_path_code`) USING BTREE,
    KEY `template_id` (`template_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='审批控件业务字段关联表';

DROP TABLE IF EXISTS `dfs_approve_record`;
CREATE TABLE IF NOT EXISTS `dfs_approve_record` (
    `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'id',
    `full_path_code` varchar(750) DEFAULT NULL COMMENT '节点的全路径编码，父级编码.子级编码',
	`order_number` varchar(255) DEFAULT NULL COMMENT '单据编号',
	`outer_approve_number` varchar(255) DEFAULT NULL COMMENT '外部审批编号',
    `approve_type` int(11) DEFAULT NULL COMMENT '审批类型',
    `approver` varchar(255) DEFAULT NULL COMMENT '审批人',
	`approval_status` int(11) DEFAULT NULL COMMENT '审批状态',
	`approval_suggestion` varchar(255) DEFAULT NULL COMMENT '审批建议',
	`approval_time` datetime DEFAULT NULL COMMENT '审批时间',
	`approval_timestamp` varchar(50) DEFAULT NULL COMMENT '审批时间戳',
	`update_by` varchar(50) DEFAULT NULL COMMENT '更新人',
    `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
    `update_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `create_time` datetime DEFAULT NULL COMMENT '创建时间',
    PRIMARY KEY (`id`) USING BTREE,
    KEY `full_path_code` (`full_path_code`) USING BTREE,
    KEY `order_number` (`order_number`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='审批记录表';

call proc_add_column(
        'dfs_work_order',
        'outer_approve_number',
        'ALTER TABLE `dfs_work_order` ADD COLUMN `outer_approve_number` varchar(255) DEFAULT NULL COMMENT ''外部审批编号'';');


truncate table dfs_approve_docking_config;
INSERT INTO `dfs_approve_docking_config`(`id`, `docking_code`, `corp_id`, `agent_id`, `agent_secret`, `url`, `token`, `encoding_aes_key`, `whitelist_ip`, `update_by`, `create_by`, `update_time`, `create_time`, `approve_callback_state`) VALUES (1, 'wechat', '', '', '', '/api/wechat/feedback', 'wechatToken', '5QVSyFkf5IvZrlcV5WVxhLGMWZGMzj5SDmsWDQqYGBL', '*************', 'admin', NULL, '2025-02-11 11:19:47', NULL, 0);

truncate table dfs_approve_node_config;
INSERT INTO `dfs_approve_node_config`(`code`, `name`, `full_path_code`, `parent_full_path_code`, `is_approve`)
select code, name, code, '', null from dfs_approve_config ;
INSERT INTO `dfs_approve_node_config`(`code`, `name`, `full_path_code`, `parent_full_path_code`, `is_approve`)
select 'releasedApprove', '生效审批', CONCAT(code,'.','releasedApprove'), code, is_approve from dfs_approve_config;
-- 送检单特殊处理
update dfs_approve_node_config set is_approve = (select released_control from dfs_approve_config where code = 'inspectionSheet') where full_path_code = 'inspectionSheet.releasedApprove';
INSERT INTO `dfs_approve_node_config`(`code`, `name`, `full_path_code`, `parent_full_path_code`, `is_approve`)
select 'finishApprove', '完成审批', CONCAT(code,'.','finishApprove'), code, finish_control from dfs_approve_config where code = 'inspectionSheet';

truncate table dfs_approve_node_module;
INSERT INTO `dfs_approve_node_module`(`full_path_code`, `module_code`, `module_name`, `seq`) VALUES ('workOrder.releasedApprove', 'baseField', '基础信息', 1);
INSERT INTO `dfs_approve_node_module`(`full_path_code`, `module_code`, `module_name`, `seq`) VALUES ('workOrder.releasedApprove', 'materialField', '物料信息', 2);

truncate table dfs_approve_node_field;
INSERT INTO `dfs_approve_node_field`(`full_path_code`, `module_code`, `field_code`, `field_name`) VALUES ('workOrder.releasedApprove', 'baseField', 'workOrderNumber','工单号');
INSERT INTO `dfs_approve_node_field`(`full_path_code`, `module_code`, `field_code`, `field_name`) VALUES ('workOrder.releasedApprove', 'baseField', 'workOrderName','工单名称');
INSERT INTO `dfs_approve_node_field`(`full_path_code`, `module_code`, `field_code`, `field_name`) VALUES ('workOrder.releasedApprove', 'baseField', 'stateName','状态');
INSERT INTO `dfs_approve_node_field`(`full_path_code`, `module_code`, `field_code`, `field_name`) VALUES ('workOrder.releasedApprove', 'baseField', 'assignmentStateName','派工状态');
INSERT INTO `dfs_approve_node_field`(`full_path_code`, `module_code`, `field_code`, `field_name`) VALUES ('workOrder.releasedApprove', 'baseField', 'productOrderNumber','生产订单号');
INSERT INTO `dfs_approve_node_field`(`full_path_code`, `module_code`, `field_code`, `field_name`) VALUES ('workOrder.releasedApprove', 'baseField', 'pickingStateName','领料状态');
INSERT INTO `dfs_approve_node_field`(`full_path_code`, `module_code`, `field_code`, `field_name`) VALUES ('workOrder.releasedApprove', 'baseField', 'investCheckResultName','投产检查结果');
INSERT INTO `dfs_approve_node_field`(`full_path_code`, `module_code`, `field_code`, `field_name`) VALUES ('workOrder.releasedApprove', 'materialField', 'priority','优先级');
INSERT INTO `dfs_approve_node_field`(`full_path_code`, `module_code`, `field_code`, `field_name`) VALUES ('workOrder.releasedApprove', 'materialField', 'materialFields.code','物料编码');
INSERT INTO `dfs_approve_node_field`(`full_path_code`, `module_code`, `field_code`, `field_name`) VALUES ('workOrder.releasedApprove', 'materialField', 'materialFields.name','物料名称');
INSERT INTO `dfs_approve_node_field`(`full_path_code`, `module_code`, `field_code`, `field_name`) VALUES ('workOrder.releasedApprove', 'materialField', 'materialFields.standard','物料规格');
INSERT INTO `dfs_approve_node_field`(`full_path_code`, `module_code`, `field_code`, `field_name`) VALUES ('workOrder.releasedApprove', 'materialField', 'planQuantity','计划数量');
INSERT INTO `dfs_approve_node_field`(`full_path_code`, `module_code`, `field_code`, `field_name`) VALUES ('workOrder.releasedApprove', 'materialField', 'finishCount','完成数量');
INSERT INTO `dfs_approve_node_field`(`full_path_code`, `module_code`, `field_code`, `field_name`) VALUES ('workOrder.releasedApprove', 'materialField', 'plannedBatches','计划批数');
INSERT INTO `dfs_approve_node_field`(`full_path_code`, `module_code`, `field_code`, `field_name`) VALUES ('workOrder.releasedApprove', 'materialField', 'actualBatches','实际批数');
INSERT INTO `dfs_approve_node_field`(`full_path_code`, `module_code`, `field_code`, `field_name`) VALUES ('workOrder.releasedApprove', 'materialField', 'plansPerBatch','每批计划数');
INSERT INTO `dfs_approve_node_field`(`full_path_code`, `module_code`, `field_code`, `field_name`) VALUES ('workOrder.releasedApprove', 'materialField', 'passRate','合格率');
INSERT INTO `dfs_approve_node_field`(`full_path_code`, `module_code`, `field_code`, `field_name`) VALUES ('workOrder.releasedApprove', 'materialField', 'unqualified','不合格数量');
INSERT INTO `dfs_approve_node_field`(`full_path_code`, `module_code`, `field_code`, `field_name`) VALUES ('workOrder.releasedApprove', 'materialField', 'inventoryQuantity','入库数量');
INSERT INTO `dfs_approve_node_field`(`full_path_code`, `module_code`, `field_code`, `field_name`) VALUES ('workOrder.releasedApprove', 'baseField', 'customerCode','客户编码');
INSERT INTO `dfs_approve_node_field`(`full_path_code`, `module_code`, `field_code`, `field_name`) VALUES ('workOrder.releasedApprove', 'baseField', 'customerName','客户名称');
INSERT INTO `dfs_approve_node_field`(`full_path_code`, `module_code`, `field_code`, `field_name`) VALUES ('workOrder.releasedApprove', 'materialField', 'progress','完成率');
INSERT INTO `dfs_approve_node_field`(`full_path_code`, `module_code`, `field_code`, `field_name`) VALUES ('workOrder.releasedApprove', 'materialField', 'materialFields.haveBom','BOM');
INSERT INTO `dfs_approve_node_field`(`full_path_code`, `module_code`, `field_code`, `field_name`) VALUES ('workOrder.releasedApprove', 'materialField', 'materialFields.haveCraft','工单工艺');
INSERT INTO `dfs_approve_node_field`(`full_path_code`, `module_code`, `field_code`, `field_name`) VALUES ('workOrder.releasedApprove', 'materialField', 'comp','单位');
INSERT INTO `dfs_approve_node_field`(`full_path_code`, `module_code`, `field_code`, `field_name`) VALUES ('workOrder.releasedApprove', 'materialField', 'procedureName','工序名称');
INSERT INTO `dfs_approve_node_field`(`full_path_code`, `module_code`, `field_code`, `field_name`) VALUES ('workOrder.releasedApprove', 'materialField', 'lineName','制造单元名称');
INSERT INTO `dfs_approve_node_field`(`full_path_code`, `module_code`, `field_code`, `field_name`) VALUES ('workOrder.releasedApprove', 'materialField', 'workCenterName','工作中心');
INSERT INTO `dfs_approve_node_field`(`full_path_code`, `module_code`, `field_code`, `field_name`) VALUES ('workOrder.releasedApprove', 'materialField', 'teamName','班组名称');
INSERT INTO `dfs_approve_node_field`(`full_path_code`, `module_code`, `field_code`, `field_name`) VALUES ('workOrder.releasedApprove', 'materialField', 'deviceName','设备名称');
INSERT INTO `dfs_approve_node_field`(`full_path_code`, `module_code`, `field_code`, `field_name`) VALUES ('workOrder.releasedApprove', 'baseField', 'saleOrderNumber','销售订单');
INSERT INTO `dfs_approve_node_field`(`full_path_code`, `module_code`, `field_code`, `field_name`) VALUES ('workOrder.releasedApprove', 'baseField', 'projectDefineName','项目名称');
INSERT INTO `dfs_approve_node_field`(`full_path_code`, `module_code`, `field_code`, `field_name`) VALUES ('workOrder.releasedApprove', 'baseField', 'contractName','合同名称');
INSERT INTO `dfs_approve_node_field`(`full_path_code`, `module_code`, `field_code`, `field_name`) VALUES ('workOrder.releasedApprove', 'baseField', 'createDate','创建时间');
INSERT INTO `dfs_approve_node_field`(`full_path_code`, `module_code`, `field_code`, `field_name`) VALUES ('workOrder.releasedApprove', 'baseField', 'updateDate','更新时间');
INSERT INTO `dfs_approve_node_field`(`full_path_code`, `module_code`, `field_code`, `field_name`) VALUES ('workOrder.releasedApprove', 'materialField', 'startDate','计划开始时间');
INSERT INTO `dfs_approve_node_field`(`full_path_code`, `module_code`, `field_code`, `field_name`) VALUES ('workOrder.releasedApprove', 'materialField', 'endDate','计划完成时间');
INSERT INTO `dfs_approve_node_field`(`full_path_code`, `module_code`, `field_code`, `field_name`) VALUES ('workOrder.releasedApprove', 'materialField', 'actualStartDate','实际开始时间');
INSERT INTO `dfs_approve_node_field`(`full_path_code`, `module_code`, `field_code`, `field_name`) VALUES ('workOrder.releasedApprove', 'materialField', 'actualEndDate','实际完成时间');
INSERT INTO `dfs_approve_node_field`(`full_path_code`, `module_code`, `field_code`, `field_name`) VALUES ('workOrder.releasedApprove', 'materialField', 'materialFields.skuEntity.skuName','特征参数');
INSERT INTO `dfs_approve_node_field`(`full_path_code`, `module_code`, `field_code`, `field_name`) VALUES ('workOrder.releasedApprove', 'materialField', 'inStockCount','流转数量');
INSERT INTO `dfs_approve_node_field`(`full_path_code`, `module_code`, `field_code`, `field_name`) VALUES ('workOrder.releasedApprove', 'materialField', 'actualWorkingHours','生产时长');
INSERT INTO `dfs_approve_node_field`(`full_path_code`, `module_code`, `field_code`, `field_name`) VALUES ('workOrder.releasedApprove', 'materialField', 'circulationDuration','流转时长');
INSERT INTO `dfs_approve_node_field`(`full_path_code`, `module_code`, `field_code`, `field_name`) VALUES ('workOrder.releasedApprove', 'materialField', 'executionStatusName','工单执行');
INSERT INTO `dfs_approve_node_field`(`full_path_code`, `module_code`, `field_code`, `field_name`) VALUES ('workOrder.releasedApprove', 'baseField', 'workOrderExtendFieldOneName','工单扩展字段1');
INSERT INTO `dfs_approve_node_field`(`full_path_code`, `module_code`, `field_code`, `field_name`) VALUES ('workOrder.releasedApprove', 'baseField', 'workOrderExtendFieldTwoName','工单扩展字段2');
INSERT INTO `dfs_approve_node_field`(`full_path_code`, `module_code`, `field_code`, `field_name`) VALUES ('workOrder.releasedApprove', 'baseField', 'workOrderExtendFieldThreeName','工单扩展字段3');
INSERT INTO `dfs_approve_node_field`(`full_path_code`, `module_code`, `field_code`, `field_name`) VALUES ('workOrder.releasedApprove', 'baseField', 'workOrderExtendFieldFourName','工单扩展字段4');
INSERT INTO `dfs_approve_node_field`(`full_path_code`, `module_code`, `field_code`, `field_name`) VALUES ('workOrder.releasedApprove', 'baseField', 'workOrderExtendFieldFiveName','工单扩展字段5');
INSERT INTO `dfs_approve_node_field`(`full_path_code`, `module_code`, `field_code`, `field_name`) VALUES ('workOrder.releasedApprove', 'baseField', 'workOrderExtendFieldSixName','工单扩展字段6');
INSERT INTO `dfs_approve_node_field`(`full_path_code`, `module_code`, `field_code`, `field_name`) VALUES ('workOrder.releasedApprove', 'baseField', 'workOrderExtendFieldSevenName','工单扩展字段7');
INSERT INTO `dfs_approve_node_field`(`full_path_code`, `module_code`, `field_code`, `field_name`) VALUES ('workOrder.releasedApprove', 'baseField', 'workOrderExtendFieldEightName','工单扩展字段8');
INSERT INTO `dfs_approve_node_field`(`full_path_code`, `module_code`, `field_code`, `field_name`) VALUES ('workOrder.releasedApprove', 'baseField', 'workOrderExtendFieldNineName','工单扩展字段9');
INSERT INTO `dfs_approve_node_field`(`full_path_code`, `module_code`, `field_code`, `field_name`) VALUES ('workOrder.releasedApprove', 'baseField', 'workOrderExtendFieldTenName','工单扩展字段10');
INSERT INTO `dfs_approve_node_field`(`full_path_code`, `module_code`, `field_code`, `field_name`) VALUES ('workOrder.releasedApprove', 'materialField', 'workOrderMaterialExtendFieldOneName','工单物料扩展字段1');
INSERT INTO `dfs_approve_node_field`(`full_path_code`, `module_code`, `field_code`, `field_name`) VALUES ('workOrder.releasedApprove', 'materialField', 'workOrderMaterialExtendFieldTwoName','工单物料扩展字段2');
INSERT INTO `dfs_approve_node_field`(`full_path_code`, `module_code`, `field_code`, `field_name`) VALUES ('workOrder.releasedApprove', 'materialField', 'workOrderMaterialExtendFieldThreeName','工单物料扩展字段3');
INSERT INTO `dfs_approve_node_field`(`full_path_code`, `module_code`, `field_code`, `field_name`) VALUES ('workOrder.releasedApprove', 'materialField', 'workOrderMaterialExtendFieldFourName','工单物料扩展字段4');
INSERT INTO `dfs_approve_node_field`(`full_path_code`, `module_code`, `field_code`, `field_name`) VALUES ('workOrder.releasedApprove', 'materialField', 'workOrderMaterialExtendFieldFiveName','工单物料扩展字段5');
INSERT INTO `dfs_approve_node_field`(`full_path_code`, `module_code`, `field_code`, `field_name`) VALUES ('workOrder.releasedApprove', 'materialField', 'workOrderMaterialExtendFieldSixName','工单物料扩展字段6');
INSERT INTO `dfs_approve_node_field`(`full_path_code`, `module_code`, `field_code`, `field_name`) VALUES ('workOrder.releasedApprove', 'materialField', 'workOrderMaterialExtendFieldSevenName','工单物料扩展字段7');
INSERT INTO `dfs_approve_node_field`(`full_path_code`, `module_code`, `field_code`, `field_name`) VALUES ('workOrder.releasedApprove', 'materialField', 'workOrderMaterialExtendFieldEightName','工单物料扩展字段8');
INSERT INTO `dfs_approve_node_field`(`full_path_code`, `module_code`, `field_code`, `field_name`) VALUES ('workOrder.releasedApprove', 'materialField', 'workOrderMaterialExtendFieldNineName','工单物料扩展字段9');
INSERT INTO `dfs_approve_node_field`(`full_path_code`, `module_code`, `field_code`, `field_name`) VALUES ('workOrder.releasedApprove', 'materialField', 'workOrderMaterialExtendFieldTenName','工单物料扩展字段10');
INSERT INTO `dfs_approve_node_field`(`full_path_code`, `module_code`, `field_code`, `field_name`) VALUES ('workOrder.releasedApprove', 'baseField', 'investCheckResultDetail','投产检查结果明细');
INSERT INTO `dfs_approve_node_field`(`full_path_code`, `module_code`, `field_code`, `field_name`) VALUES ('workOrder.releasedApprove', 'materialField', 'theoryHour','单件理论工时');
INSERT INTO `dfs_approve_node_field`(`full_path_code`, `module_code`, `field_code`, `field_name`) VALUES ('workOrder.releasedApprove', 'materialField', 'produceTheoryHour','产出理论工时');
INSERT INTO `dfs_approve_node_field`(`full_path_code`, `module_code`, `field_code`, `field_name`) VALUES ('workOrder.releasedApprove', 'materialField', 'planTheoryHour','计划理论工时');
INSERT INTO `dfs_approve_node_field`(`full_path_code`, `module_code`, `field_code`, `field_name`) VALUES ('workOrder.releasedApprove', 'materialField', 'customerMaterialCode','客户物料编码');
INSERT INTO `dfs_approve_node_field`(`full_path_code`, `module_code`, `field_code`, `field_name`) VALUES ('workOrder.releasedApprove', 'materialField', 'customerMaterialName','客户物料名称');
INSERT INTO `dfs_approve_node_field`(`full_path_code`, `module_code`, `field_code`, `field_name`) VALUES ('workOrder.releasedApprove', 'materialField', 'customerSpecification','客户物料规格');
INSERT INTO `dfs_approve_node_field`(`full_path_code`, `module_code`, `field_code`, `field_name`) VALUES ('workOrder.releasedApprove', 'baseField', 'remark','备注');
INSERT INTO `dfs_approve_node_field`(`full_path_code`, `module_code`, `field_code`, `field_name`) VALUES ('workOrder.releasedApprove', 'baseField', 'createName','创建人');
INSERT INTO `dfs_approve_node_field`(`full_path_code`, `module_code`, `field_code`, `field_name`) VALUES ('workOrder.releasedApprove', 'baseField', 'updateByName','更新人');
INSERT INTO `dfs_approve_node_field`(`full_path_code`, `module_code`, `field_code`, `field_name`) VALUES ('workOrder.releasedApprove', 'baseField', 'businessTypeName','业务类型');
INSERT INTO `dfs_approve_node_field`(`full_path_code`, `module_code`, `field_code`, `field_name`) VALUES ('workOrder.releasedApprove', 'baseField', 'orderTypeName','单据类型');

-- 创建待审批改为待提交
update dfs_work_order set approval_status = 0 where approval_status = 1 and state = 1;


-- ================================================此脚本专为审批需求设定，如需写脚本，请移步到3.17.1.1=======================================================
-- ================================================此脚本专为审批需求设定，如需写脚本，请移步到3.17.1.1=======================================================
-- ================================================此脚本专为审批需求设定，如需写脚本，请移步到3.17.1.1=======================================================
-- ================================================此脚本专为审批需求设定，如需写脚本，请移步到3.17.1.1=======================================================
-- ================================================此脚本专为审批需求设定，如需写脚本，请移步到3.17.1.1=======================================================
