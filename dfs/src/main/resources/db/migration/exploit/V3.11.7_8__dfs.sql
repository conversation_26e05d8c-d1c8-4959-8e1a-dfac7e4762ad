
-- 补充销售订单-导出查看日志 权限
INSERT INTO `sys_permissions` (`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`, `is_sys_data`)
VALUES ('1090102260', '导出_查看日志', 'sales.order:export:log', NULL, NULL, NOW(), NULL, NOW(), 'enable', 'GET', '1090102', '2', '1', '0', '/supply-chain-collaboration/order-model/salesOrder', '1', NULL, '', '1');
call init_new_role_permission('1090102260');
