-- 表单模块新增排序字段
call proc_add_column(
        'dfs_form_field_module_config',
        'sort',
        'ALTER TABLE `dfs_form_field_module_config` ADD COLUMN `sort` int(11) NULL DEFAULT 1 COMMENT ''排序''');

-- 基础字段顺序为1
update `dfs_form_field_module_config` set sort = 1 where `module_code` = 'baseField';
-- 物料基础字段顺序为2
update `dfs_form_field_module_config` set sort = 2 where `module_code` = 'materialBaseField';
-- 物料扩展字段顺序为3
update `dfs_form_field_module_config` set sort = 3 where `module_code` = 'materialExtendField';
-- 批次字段顺序为4
update `dfs_form_field_module_config` set sort = 4 where `module_code` = 'batchField';


-- 任务配置的权限
call route_add_l2('/task-center', '/task-center/task-config', '任务配置');
update sys_route set module_name = 'dfs'WHERE path = '/task-center/task-config';

-- 全局地图新增委外下推的所有单据
UPDATE `dfs_order_push_down_config` SET `type_code` = 'subcontractOrder' WHERE `full_path_code` = 'subcontract.subcontractOrderPushDownConfig';
UPDATE `dfs_order_push_down_config` SET `type_code` = 'subcontractReceiptOrder' WHERE `full_path_code` = 'subcontract.subcontractOrderPushDownConfig.subcontractReceiptOrder';
UPDATE `dfs_order_push_down_config` SET `type_code` = 'subcontractProductOutbound' WHERE `full_path_code` = 'subcontract.subcontractOrderPushDownConfig.subcontractProductOutbound';
UPDATE `dfs_order_push_down_config` SET `type_code` = 'subcontractDeliveryOrder' WHERE `full_path_code` = 'subcontract.subcontractOrderPushDownConfig.subcontractDeliveryOrder';
UPDATE `dfs_order_push_down_config` SET `type_code` = 'subcontractMaterialsList' WHERE `full_path_code` = 'subcontract.subcontractOrderPushDownConfig.subcontractOrderMaterial';
UPDATE `dfs_order_push_down_config` SET `type_code` = 'subcontractSupplementaryFood' WHERE `full_path_code` = 'subcontract.subcontractMaterialsListPushDownConfig.subcontractSupplyOutbound';
UPDATE `dfs_order_push_down_config` SET `type_code` = 'subcontractReceiptOrder' WHERE `full_path_code` = 'subcontract.subcontractReceiptOrderPushDownConfig';
UPDATE `dfs_order_push_down_config` SET `type_code` = 'subcontractReturnOrder' WHERE `full_path_code` = 'subcontract.subcontractReceiptOrderPushDownConfig.subcontractReturnOrder';
UPDATE `dfs_order_push_down_config` SET `type_code` = 'subcontractReturnReceipt' WHERE `full_path_code` = 'subcontract.subcontractReceiptOrderPushDownConfig.subcontractIn';
UPDATE `dfs_order_push_down_config` SET `type_code` = 'subcontractMaterialsList' WHERE `full_path_code` = 'subcontract.subcontractMaterialsListPushDownConfig';
UPDATE `dfs_order_push_down_config` SET `type_code` = 'subcontractProductOutbound' WHERE `full_path_code` = 'subcontract.subcontractMaterialsListPushDownConfig.subcontractProductOutbound';

INSERT INTO `dfs_order_category`(`order_category`, `order_name`, `application_id`, `detail_path`, `request_prefix`, `is_show`, `is_sys_data`, `location_x`, `location_y`) VALUES ('subcontractReceiptOrder', '委外收货单', 'new.yk.genieos.ams.scc.osm.outsourcingReceiptOrder', '', NULL, 1, 1, -377.8758, 246.9343);
INSERT INTO `dfs_order_category`(`order_category`, `order_name`, `application_id`, `detail_path`, `request_prefix`, `is_show`, `is_sys_data`, `location_x`, `location_y`) VALUES ('subcontractDeliveryOrder', '委外发料单', 'new.yk.genieos.ams.scc.osm.outsourcingIssueMaterial', '', NULL, 1, 1, -382.1615, 125.5058);
INSERT INTO `dfs_order_category`(`order_category`, `order_name`, `application_id`, `detail_path`, `request_prefix`, `is_show`, `is_sys_data`, `location_x`, `location_y`) VALUES ('subcontractMaterialsList', '委外订单用料清单', 'new.yk.genieos.ams.scc.osm.outsourcingMaterialOrder', '', NULL, 1, 1, -380.7329, 6.9343);
INSERT INTO `dfs_order_category`(`order_category`, `order_name`, `application_id`, `detail_path`, `request_prefix`, `is_show`, `is_sys_data`, `location_x`, `location_y`) VALUES ('subcontractReturnOrder', '委外退货单', 'new.yk.genieos.ams.scc.osm.outsourcingReturnOrder', '', NULL, 1, 1, -380.7329, 6.9343);
INSERT INTO `dfs_order_category`(`order_category`, `order_name`, `application_id`, `detail_path`, `request_prefix`, `is_show`, `is_sys_data`, `location_x`, `location_y`) VALUES ('subcontractReturnReceipt', '委外入库单', 'com.luotu.wms.subcontracting.order', '', NULL, 1, 1, -380.7329, 6.9343);

INSERT INTO `dfs_order_relation`(`upstream_service`, `upstream_order_category`, `upstream_order_name`, `downstream_service`, `downstream_order_category`, `downstream_order_name`, `is_sys_data`) VALUES ('ams', 'subcontractOrder', '委外订单', 'ams', 'subcontractReceiptOrder', '委外收货单', 1);
INSERT INTO `dfs_order_relation`(`upstream_service`, `upstream_order_category`, `upstream_order_name`, `downstream_service`, `downstream_order_category`, `downstream_order_name`, `is_sys_data`) VALUES ('ams', 'subcontractOrder', '委外订单', 'wms', 'subcontractProductOutbound', '委外领料出库单', 1);
INSERT INTO `dfs_order_relation`(`upstream_service`, `upstream_order_category`, `upstream_order_name`, `downstream_service`, `downstream_order_category`, `downstream_order_name`, `is_sys_data`) VALUES ('ams', 'subcontractOrder', '委外订单', 'ams', 'subcontractDeliveryOrder', '委外发料单', 1);
INSERT INTO `dfs_order_relation`(`upstream_service`, `upstream_order_category`, `upstream_order_name`, `downstream_service`, `downstream_order_category`, `downstream_order_name`, `is_sys_data`) VALUES ('ams', 'subcontractOrder', '委外订单', 'ams', 'subcontractMaterialsList', '委外订单用料清单', 1);
INSERT INTO `dfs_order_relation`(`upstream_service`, `upstream_order_category`, `upstream_order_name`, `downstream_service`, `downstream_order_category`, `downstream_order_name`, `is_sys_data`) VALUES ('ams', 'subcontractReceiptOrder', '委外收货单', 'ams', 'subcontractReturnOrder', '委外退货单', 1);
INSERT INTO `dfs_order_relation`(`upstream_service`, `upstream_order_category`, `upstream_order_name`, `downstream_service`, `downstream_order_category`, `downstream_order_name`, `is_sys_data`) VALUES ('ams', 'subcontractReceiptOrder', '委外收货单', 'wms', 'subcontractReturnReceipt', '委外入库单', 1);
INSERT INTO `dfs_order_relation`(`upstream_service`, `upstream_order_category`, `upstream_order_name`, `downstream_service`, `downstream_order_category`, `downstream_order_name`, `is_sys_data`) VALUES ('ams', 'subcontractMaterialsList', '委外订单用料清单', 'wms', 'subcontractProductOutbound', '委外领料出库单', 1);
INSERT INTO `dfs_order_relation`(`upstream_service`, `upstream_order_category`, `upstream_order_name`, `downstream_service`, `downstream_order_category`, `downstream_order_name`, `is_sys_data`) VALUES ('ams', 'subcontractMaterialsList', '委外订单用料清单', 'wms', 'subcontractSupplementaryFood', '委外补料出库单', 1);
-- 重置全局地图位置
TRUNCATE TABLE `dfs_order_category`;
INSERT INTO `dfs_order_category`(`order_category`, `order_name`, `application_id`, `detail_path`, `request_prefix`, `is_show`, `is_sys_data`, `location_x`, `location_y`) VALUES ('saleOrder', '销售订单', 'new.yk.genieos.ams.order-model.salesOrder', '', NULL, 1, 1, -234.8885, -205.3533);
INSERT INTO `dfs_order_category`(`order_category`, `order_name`, `application_id`, `detail_path`, `request_prefix`, `is_show`, `is_sys_data`, `location_x`, `location_y`) VALUES ('deliveryApplication', '销售出货单', 'new.yk.genieos.dfs.scc.order-model.shipment_application', '', NULL, 1, 1, 175.6515, -476.3259);
INSERT INTO `dfs_order_category`(`order_category`, `order_name`, `application_id`, `detail_path`, `request_prefix`, `is_show`, `is_sys_data`, `location_x`, `location_y`) VALUES ('saleReturnOrder', '销售退货单', 'new.yk.genieos.ams.scc.order-model.sales_returns', '', NULL, 1, 1, 509.5061, -474.9312);
INSERT INTO `dfs_order_category`(`order_category`, `order_name`, `application_id`, `detail_path`, `request_prefix`, `is_show`, `is_sys_data`, `location_x`, `location_y`) VALUES ('productOrder', '生产订单', 'new.yk.genieos.ams.order-model.product-order', '', NULL, 1, 1, 52.7595, 9.5508);
INSERT INTO `dfs_order_category`(`order_category`, `order_name`, `application_id`, `detail_path`, `request_prefix`, `is_show`, `is_sys_data`, `location_x`, `location_y`) VALUES ('productMaterialsList', '生产订单用料清单', 'new.yk.genieos.ams.order-model.production-materials', '', NULL, 1, 1, 54.3339, 150.5949);
INSERT INTO `dfs_order_category`(`order_category`, `order_name`, `application_id`, `detail_path`, `request_prefix`, `is_show`, `is_sys_data`, `location_x`, `location_y`) VALUES ('purchaseRequest', '采购需求单', 'new.yk.genieos.ams.scc.procurement-management.purchasing-demand', '', NULL, 1, 1, -778.6925, -308.2027);
INSERT INTO `dfs_order_category`(`order_category`, `order_name`, `application_id`, `detail_path`, `request_prefix`, `is_show`, `is_sys_data`, `location_x`, `location_y`) VALUES ('purchaseOrder', '采购订单', 'new.yk.genieos.ams.scc.procurement-management.purchasing-list', '', NULL, 1, 1, -780.3226, -171.8491);
INSERT INTO `dfs_order_category`(`order_category`, `order_name`, `application_id`, `detail_path`, `request_prefix`, `is_show`, `is_sys_data`, `location_x`, `location_y`) VALUES ('purchaseReceipt', '采购收料', 'new.yk.genieos.ams.scc.procurement-management.delivery-order', '', NULL, 1, 1, -926.6448, -172.3809);
INSERT INTO `dfs_order_category`(`order_category`, `order_name`, `application_id`, `detail_path`, `request_prefix`, `is_show`, `is_sys_data`, `location_x`, `location_y`) VALUES ('returnOrder', '采购退料', 'new.yk.genieos.ams.scc.procurement-management.return-order', '', NULL, 1, 1, -785.2590, 28.7123);
INSERT INTO `dfs_order_category`(`order_category`, `order_name`, `application_id`, `detail_path`, `request_prefix`, `is_show`, `is_sys_data`, `location_x`, `location_y`) VALUES ('subcontractOrder', '委外订单', 'new.yk.genieos.ams.scc.osm.outsourcingOrder', '', NULL, 1, 1, -345.4962, 106.7684);
INSERT INTO `dfs_order_category`(`order_category`, `order_name`, `application_id`, `detail_path`, `request_prefix`, `is_show`, `is_sys_data`, `location_x`, `location_y`) VALUES ('workOrder', '生产工单', 'new.yk.genieos.dfs.order-model.production-workorder', '', NULL, 1, 1, 249.2012, 69.9647);
INSERT INTO `dfs_order_category`(`order_category`, `order_name`, `application_id`, `detail_path`, `request_prefix`, `is_show`, `is_sys_data`, `location_x`, `location_y`) VALUES ('workOrderMaterialList', '生产工单用料清单', 'new.yk.genieos.dfs.order-model.workOrder-materials', '', NULL, 1, 1, 251.5576, 240.0012);
INSERT INTO `dfs_order_category`(`order_category`, `order_name`, `application_id`, `detail_path`, `request_prefix`, `is_show`, `is_sys_data`, `location_x`, `location_y`) VALUES ('inspectOrder', '检验单', 'new.yk.genieos.qms3.0.inspection-manage.qpi.productInspection', '', NULL, 1, 1, -1068.7589, -172.4997);
INSERT INTO `dfs_order_category`(`order_category`, `order_name`, `application_id`, `detail_path`, `request_prefix`, `is_show`, `is_sys_data`, `location_x`, `location_y`) VALUES ('salesIssueDoc', '销售出库单', 'com.luotu.wms.salesIssueReceipt.salesIssueDoc', '', NULL, 1, 1, 178.4281, -643.9753);
INSERT INTO `dfs_order_category`(`order_category`, `order_name`, `application_id`, `detail_path`, `request_prefix`, `is_show`, `is_sys_data`, `location_x`, `location_y`) VALUES ('salesReturnReceiptDoc', '销售退货入库单', 'com.luotu.wms.salesIssueReceipt.salesMaterialReturnReceiptDoc', '', NULL, 1, 1, 340.3953, -471.7731);
INSERT INTO `dfs_order_category`(`order_category`, `order_name`, `application_id`, `detail_path`, `request_prefix`, `is_show`, `is_sys_data`, `location_x`, `location_y`) VALUES ('workOrderComplete', '生产入库单', 'com.luotu.wms.productInOrOut.workOrderComplete', '', NULL, 1, 1, 621.5897, 9.7373);
INSERT INTO `dfs_order_category`(`order_category`, `order_name`, `application_id`, `detail_path`, `request_prefix`, `is_show`, `is_sys_data`, `location_x`, `location_y`) VALUES ('outputPickingProduct', '生产领料出库单', 'com.luotu.wms.productInOrOut.workOrderTakeOut', '', NULL, 1, 1, 621.7077, 101.0304);
INSERT INTO `dfs_order_category`(`order_category`, `order_name`, `application_id`, `detail_path`, `request_prefix`, `is_show`, `is_sys_data`, `location_x`, `location_y`) VALUES ('workOrderSupplement', '生产补料出库单', 'com.luotu.wms.productInOrOut.workOrderSupplement', '', NULL, 1, 1, 619.5237, 188.3622);
INSERT INTO `dfs_order_category`(`order_category`, `order_name`, `application_id`, `detail_path`, `request_prefix`, `is_show`, `is_sys_data`, `location_x`, `location_y`) VALUES ('applicationReturn', '生产退料入库单', 'com.luotu.wms.productInOrOut.applicationReturn', '', NULL, 1, 1, 618.7387, 285.1258);
INSERT INTO `dfs_order_category`(`order_category`, `order_name`, `application_id`, `detail_path`, `request_prefix`, `is_show`, `is_sys_data`, `location_x`, `location_y`) VALUES ('purchaseIn', '采购入库单', 'com.luotu.wms.purchaseOutOfStorage.purchasePutInStorage', '', NULL, 1, 1, -928.2548, 23.0314);
INSERT INTO `dfs_order_category`(`order_category`, `order_name`, `application_id`, `detail_path`, `request_prefix`, `is_show`, `is_sys_data`, `location_x`, `location_y`) VALUES ('purchaseReturnOut', '采购退料出库单', 'com.luotu.wms.purchaseOutOfStorage.purchaseReturnOutOfStorage', '', NULL, 1, 1, -928.1065, 174.1497);
INSERT INTO `dfs_order_category`(`order_category`, `order_name`, `application_id`, `detail_path`, `request_prefix`, `is_show`, `is_sys_data`, `location_x`, `location_y`) VALUES ('subcontractProductOutbound', '委外领料出库单', 'com.luotu.wms.subcontracting.outstoreMaterail', '', NULL, 1, 1, -467.3020, 374.8782);
INSERT INTO `dfs_order_category`(`order_category`, `order_name`, `application_id`, `detail_path`, `request_prefix`, `is_show`, `is_sys_data`, `location_x`, `location_y`) VALUES ('subcontractSupplementaryFood', '委外补料出库单', 'com.luotu.wms.subcontracting.repairMaterail', '', NULL, 1, 1, -606.7775, 376.5122);
INSERT INTO `dfs_order_category`(`order_category`, `order_name`, `application_id`, `detail_path`, `request_prefix`, `is_show`, `is_sys_data`, `location_x`, `location_y`) VALUES ('subcontractReturnReceipt', '委外退料入库单', 'com.luotu.wms.subcontracting.refundMaterail', '', NULL, 1, 1, -463.3204, 514.8947);
INSERT INTO `dfs_order_category`(`order_category`, `order_name`, `application_id`, `detail_path`, `request_prefix`, `is_show`, `is_sys_data`, `location_x`, `location_y`) VALUES ('transferOrder', '调拨单', 'com.luotu.wms.cougnyManage.allocation', '', NULL, 1, 1, 571.6902, -255.6328);
INSERT INTO `dfs_order_category`(`order_category`, `order_name`, `application_id`, `detail_path`, `request_prefix`, `is_show`, `is_sys_data`, `location_x`, `location_y`) VALUES ('stockOtherAppForm', '其他出库申请单', 'com.luotu.wms.mixReceive.issueRequisition', '', NULL, 1, 1, 729.2671, -257.3514);
INSERT INTO `dfs_order_category`(`order_category`, `order_name`, `application_id`, `detail_path`, `request_prefix`, `is_show`, `is_sys_data`, `location_x`, `location_y`) VALUES ('subcontractReceiptOrder', '委外收货单', 'new.yk.genieos.ams.scc.osm.outsourcingReceiptOrder', '', NULL, 1, 1, -247.8758, 248.1843);
INSERT INTO `dfs_order_category`(`order_category`, `order_name`, `application_id`, `detail_path`, `request_prefix`, `is_show`, `is_sys_data`, `location_x`, `location_y`) VALUES ('subcontractDeliveryOrder', '委外发料单', 'new.yk.genieos.ams.scc.osm.outsourcingIssueMaterial', '', NULL, 1, 1, -611.0901, 223.7201);
INSERT INTO `dfs_order_category`(`order_category`, `order_name`, `application_id`, `detail_path`, `request_prefix`, `is_show`, `is_sys_data`, `location_x`, `location_y`) VALUES ('subcontractMaterialsList', '委外订单用料清单', 'new.yk.genieos.ams.scc.osm.outsourcingMaterialOrder', '', NULL, 1, 1, -466.0900, 226.3986);
INSERT INTO `dfs_order_category`(`order_category`, `order_name`, `application_id`, `detail_path`, `request_prefix`, `is_show`, `is_sys_data`, `location_x`, `location_y`) VALUES ('subcontractReturnOrder', '委外退货单', 'new.yk.genieos.ams.scc.osm.outsourcingReturnOrder', '', NULL, 1, 1, -248.4114, 390.6842);

-- 任务配置新增两个按钮权限
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`, `is_sys_data`) VALUES ('/task-center/task-config/overallChart', '全局地图', 'task.config:overallChart', NULL, NULL, NULL, NULL, '2024-12-09 15:26:02', 'enable', 'GET', '/task-center/task-config', 2, 1, 0, '/task-center/task-config', 1, NULL, '', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`, `is_sys_data`) VALUES ('/task-center/task-config/update', '更新', 'task.config:update', NULL, NULL, NULL, NULL, '2024-12-09 15:26:02', 'enable', 'GET', '/task-center/task-config', 2, 1, 0, '/task-center/task-config', 1, NULL, '', 1);
call init_new_role_permission('/task-center/task-config/overallChart');
call init_new_role_permission('/task-center/task-config/update');