-- DDL
-- 表单配置新增字段
call proc_add_column(
        'dfs_form_config',
        'module',
        'ALTER TABLE `dfs_form_config` ADD COLUMN `module` varchar(255) NULL COMMENT ''所属模块（edit--编辑  detail--详情）''');
call proc_add_column(
        'dfs_form_config',
        'is_inner',
        'ALTER TABLE `dfs_form_config` ADD COLUMN `is_inner` tinyint(4) NULL DEFAULT 0 COMMENT ''是否为内部数据''');

-- DML
-- 工单批量删除权限
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`, `is_sys_data`) VALUES ('10005680', '批量删除', 'production.workorder.appendix:batchDelete', NULL, NULL, NULL, NULL, '2024-10-16 10:42:58', 'enable', 'GET', '10005', 2, 1, 0, '/order-model/production-workorder', 1, NULL, '', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`, `is_sys_data`) VALUES ('10101620', '批量删除', 'production.workorder.appendix:batchDelete', NULL, NULL, NULL, NULL, '2024-10-16 10:42:58', 'enable', 'GET', '10101', 2, 1, 0, '/workorder-model/production-workorder', 1, NULL, '', 1);
call init_new_role_permission('10005680');
call init_new_role_permission('10101620');

-- 表单配置的(状态)改为编辑页，详情改成详情页，列表改成列表页
UPDATE `dfs_form_config` SET `name` = REPLACE (`name`, '(状态)', '编辑页');
UPDATE `dfs_form_config` SET `name` = REPLACE (`name`, '列表', '列表页');
UPDATE `dfs_form_config` SET `name` = REPLACE (`name`, '详情', '详情页');

-- 工艺工序新增时，默认值自动添加支持业务配置
INSERT INTO `dfs_business_config` (`code`, `name`, `full_path_code`, `parent_full_path_code`, `description`, `create_by`, `update_by`, `create_time`, `update_time`) VALUES ('defaultProcedureConfig', '默认工序配置', 'design.craftConfig.defaultProcedureConfig', 'design.craftConfig', '工艺各工序，创建时是否默认带出工序定义配置对应的项', 'yelinkoncall', 'admin', '2023-09-08 06:14:57', '2024-10-14 15:10:12');
INSERT INTO `dfs_business_config_value` (`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES ('needExportModule', '需要带出默认值的模块', 'design.craftConfig.defaultProcedureConfig.needExportModule', 'design.craftConfig.defaultProcedureConfig', 'select-multiple', 'table', NULL, NULL, NULL, NULL, '[{\"value\":\"craftProcedureMaterialUsed\",\"label\":\"工序用料\"},{\"value\":\"craftProcedureDevice\",\"label\":\"工序设备\"},{\"value\":\"craftProcedureParameter\",\"label\":\"工艺参数\"},{\"value\":\"craftProcedureWorkHour\",\"label\":\"工序工时\"},{\"value\":\"craftProcedureFile\",\"label\":\"工序附件\"},{\"value\":\"craftProcedureInspect\",\"label\":\"工序检验项\"},{\"value\":\"craftProcedureInspectScheme\",\"label\":\"工序检验方案\"},{\"value\":\"craftProcedureController\",\"label\":\"工序控制\"},{\"value\":\"craftProcedureAssemble\",\"label\":\"工序工装\"},{\"value\":\"craftProcedurePost\",\"label\":\"工序人力\"},{\"value\":\"craftProcedureDefect\",\"label\":\"工序质检\"},{\"value\":\"craftProcedureMaintain\",\"label\":\"工序维修\"}]', '[\"craftProcedureDevice\",\"craftProcedureMaterialUsed\",\"craftProcedureWorkHour\",\"craftProcedureParameter\",\"craftProcedureFile\",\"craftProcedureInspect\",\"craftProcedureInspectScheme\",\"craftProcedureController\",\"craftProcedureAssemble\",\"craftProcedurePost\",\"craftProcedureDefect\",\"craftProcedureMaintain\"]', NULL);

-- 删除订单报工、工单报工时无用的表单字段配置（shiftType）
DELETE FROM `dfs_form_field_config` WHERE `full_path_code` in ('orderReportApp.addReport', 'orderReportApp.editReport') AND `field_code` in ('shiftType');
DELETE FROM `dfs_form_field_rule_config` where `full_path_code` in ('orderReportApp.addReport', 'orderReportApp.editReport') AND `field_code` in ('shiftType');

-- 订单报工、工单报工的拓展字段允许配置选项值
UPDATE `dfs_form_field_rule_config` SET `input_type` = 'input', `input_gray` = 0, `value_gray` = 0 WHERE `field_code` = 'reportFieldOne' AND `field_name_full_path_code` in ('orderReportApp.addReport', 'orderReportApp.editReport', 'workOrderReport.addReport', 'workOrderReport.editReport');
UPDATE `dfs_form_field_rule_config` SET `input_type` = 'input', `input_gray` = 0, `value_gray` = 0 WHERE `field_code` = 'reportFieldTwo' AND `field_name_full_path_code` in ('orderReportApp.addReport', 'orderReportApp.editReport', 'workOrderReport.addReport', 'workOrderReport.editReport');
UPDATE `dfs_form_field_rule_config` SET `input_type` = 'input', `input_gray` = 0, `value_gray` = 0 WHERE `field_code` = 'reportFieldThree' AND `field_name_full_path_code` in ('orderReportApp.addReport', 'orderReportApp.editReport', 'workOrderReport.addReport', 'workOrderReport.editReport');
UPDATE `dfs_form_field_rule_config` SET `input_type` = 'input', `input_gray` = 0, `value_gray` = 0 WHERE `field_code` = 'reportFieldFour' AND `field_name_full_path_code` in ('orderReportApp.addReport', 'orderReportApp.editReport', 'workOrderReport.addReport', 'workOrderReport.editReport');
UPDATE `dfs_form_field_rule_config` SET `input_type` = 'input', `input_gray` = 0, `value_gray` = 0 WHERE `field_code` = 'reportFieldFive' AND `field_name_full_path_code` in ('orderReportApp.addReport', 'orderReportApp.editReport', 'workOrderReport.addReport', 'workOrderReport.editReport');

-- 修改单据下推菜单名称
UPDATE `dfs_order_push_down_item` SET `name` = '下推生产订单用料清单' WHERE `name` = '下推生产订单用量清单';

-- 修改API配置的请求方式
UPDATE `dfs_api_transform` SET `request_method` = 'POST' WHERE `code` = 'getCustomerList';

-- 优化下推配置
UPDATE `dfs_order_push_down_config_value` SET `value` = '[3,4,5]' WHERE `value_full_path_code` = 'production.workOrderPushDownConfig.productStockInAndOut.jumpPageProductionIn.originalOrderStates';
UPDATE `dfs_order_push_down_config_value_dict` SET `value` = '[3,4,5]' WHERE `value_full_path_code` = 'production.workOrderPushDownConfig.productStockInAndOut.jumpPageProductionIn.originalOrderStates';
