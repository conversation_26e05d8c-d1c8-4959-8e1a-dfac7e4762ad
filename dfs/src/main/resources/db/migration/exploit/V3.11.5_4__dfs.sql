-- 下推路由变更
UPDATE `dfs_order_push_down_config_value` SET `value` = '\"com.yk.genieos.dfs.qualities-manage\"' WHERE `value_code` = 'applicationId' AND `value_full_path_code` = 'purchase.purchaseOrderPushDownConfig.supplierBom.jumpPage.applicationId';
UPDATE `dfs_order_push_down_config_value` SET `value` = '\"/qms/qualities-manage/productInspection?pushDownOrigin=purchaseOrder\"' WHERE `value_code` = 'url' AND `value_full_path_code` = 'purchase.purchaseOrderPushDownConfig.supplierBom.jumpPage.url';
UPDATE `dfs_order_push_down_config_value_dict` SET `value` = '\"com.yk.genieos.dfs.qualities-manage\"' WHERE `value_code` = 'applicationId' AND `value_full_path_code` = 'purchase.purchaseOrderPushDownConfig.supplierBom.jumpPage.applicationId';
UPDATE `dfs_order_push_down_config_value_dict` SET `value` = '\"/qms/qualities-manage/productInspection?pushDownOrigin=purchaseOrder\"' WHERE `value_code` = 'url' AND `value_full_path_code` = 'purchase.purchaseOrderPushDownConfig.supplierBom.jumpPage.url';


