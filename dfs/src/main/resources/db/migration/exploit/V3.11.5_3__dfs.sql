

-- 销售订单生效状态特征参数表单配置修改
update  dfs_form_field_rule_config  set  is_edit = 0 where full_path_code = 'saleOrder.editByRelease'   and  field_code = 'auxiliaryAttr';

-- 更新编码规则术语
UPDATE `dfs_config_rule` SET `rule_type_name` = REPLACE (`rule_type_name`, '-单据编号', '编号') WHERE `rule_type_name` in ('物料-单据编号', 'BOM-单据编号', '工艺-单据编号', '替代方案-单据编号', '特征参数-单据编号');

-- 工单列表的表单配置新增 创建人和更新人字段
call proc_add_form_field("workOrder.list", "createName", "创建人");
call proc_add_form_field("workOrder.list", "updateByName", "更新人");

INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES (NULL, '/order-model/production-workorder', 'workOrder.list', 'workOrder.list', 'createName', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, '[]', NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES (NULL, '/order-model/production-workorder', 'workOrder.list', 'workOrder.list', 'updateByName', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, '[]', NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);

-- 销售订单按物料查询时物料数量应该叫销售数量
update `dfs_form_field_config` SET `field_name` = '销售数量' where `full_path_code` = 'saleOrder.list.material' AND `field_name` = '物料数量';

-- 默认关闭自动下推的下推规则
UPDATE `dfs_order_push_down_item` SET `enable` = 0 WHERE `instance_type` in ('production.productOrderPushDownConfig.workOrder.craftRouteAuto', 'saleOrder.pushDownConfig.productOrder.bomAuto');

call proc_add_form_field("saleOrder.list.material", "remark", "订单备注");

-- 重命名产能取值优先级名称
UPDATE `dfs_dict` SET `name` = '生产基本单元 + 物料 > 生产基本单元 > 物料 > 工厂产能' WHERE `code` = 'logicOne';
UPDATE `dfs_dict` SET `name` = '生产基本单元 + 物料 > 物料 > 生产基本单元 > 工厂产能' WHERE `code` = 'logicTwo';

-- 订单报工小程序的表单配置中的工序任务，字段名变更
update `dfs_form_field_config` SET `field_name` = '生产订单号' where `full_path_code` = 'orderReportApp.processTasks' AND `field_name` = '生产工单号';

UPDATE `dfs_target_model` SET `script` = 'SELECT vasom.sale_order_number,vaso.customer_name,vaso.customer_code,vaso.salesman_name,dmsom.material_name,dmsom.material_code,
vasom.customer_material_code ,vasom.customer_material_name,vasom.order_date ,vasom.require_goods_date,vasom.shipment_status,vasom.process_status,
dmsom.sales_quantity,dmsom.plan_quantity,dmsom.produce_quantity,dmsom.applied_shipment_quantity,dmsom.applied_quantity,dmsom.delay_quantity,vasom.return_quantity
FROM dfs_metrics.dfs_metrics_sale_order_material dmsom, dfs_metrics.v_ams_sale_order_material vasom, dfs_metrics.v_ams_sale_order vaso
WHERE dmsom.sale_order_number = vasom.sale_order_number and dmsom.material_code =vasom.material_code AND vasom.sale_order_number = vaso.sale_order_number' WHERE `target_name` = 'assemblyLineSalesSchedule';

-- 刷新字段映射表
DELETE FROM `dfs_field_mapping`;
INSERT INTO `dfs_field_mapping`(`id`, `upstream_service`, `upstream_order_type`, `upstream_field_code`, `downstream_service`, `downstream_order_type`, `downstream_field_code`) VALUES (NULL, 'ams', 'saleOrder', 'saleOrderExtendFieldOne', 'ams', 'productOrder', 'productOrderExtendFieldOne');
INSERT INTO `dfs_field_mapping`(`id`, `upstream_service`, `upstream_order_type`, `upstream_field_code`, `downstream_service`, `downstream_order_type`, `downstream_field_code`) VALUES (NULL, 'ams', 'saleOrder', 'saleOrderExtendFieldTwo', 'ams', 'productOrder', 'productOrderExtendFieldTwo');
INSERT INTO `dfs_field_mapping`(`id`, `upstream_service`, `upstream_order_type`, `upstream_field_code`, `downstream_service`, `downstream_order_type`, `downstream_field_code`) VALUES (NULL, 'ams', 'saleOrder', 'saleOrderExtendFieldThree', 'ams', 'productOrder', 'productOrderExtendFieldThree');
INSERT INTO `dfs_field_mapping`(`id`, `upstream_service`, `upstream_order_type`, `upstream_field_code`, `downstream_service`, `downstream_order_type`, `downstream_field_code`) VALUES (NULL, 'ams', 'saleOrder', 'saleOrderExtendFieldFour', 'ams', 'productOrder', 'productOrderExtendFieldFour');
INSERT INTO `dfs_field_mapping`(`id`, `upstream_service`, `upstream_order_type`, `upstream_field_code`, `downstream_service`, `downstream_order_type`, `downstream_field_code`) VALUES (NULL, 'ams', 'saleOrder', 'saleOrderExtendFieldFive', 'ams', 'productOrder', 'productOrderExtendFieldFive');
INSERT INTO `dfs_field_mapping`(`id`, `upstream_service`, `upstream_order_type`, `upstream_field_code`, `downstream_service`, `downstream_order_type`, `downstream_field_code`) VALUES (NULL, 'ams', 'saleOrder', 'saleOrderExtendFieldSix', 'ams', 'productOrder', 'productOrderExtendFieldSix');
INSERT INTO `dfs_field_mapping`(`id`, `upstream_service`, `upstream_order_type`, `upstream_field_code`, `downstream_service`, `downstream_order_type`, `downstream_field_code`) VALUES (NULL, 'ams', 'saleOrder', 'saleOrderExtendFieldSeven', 'ams', 'productOrder', 'productOrderExtendFieldSeven');
INSERT INTO `dfs_field_mapping`(`id`, `upstream_service`, `upstream_order_type`, `upstream_field_code`, `downstream_service`, `downstream_order_type`, `downstream_field_code`) VALUES (NULL, 'ams', 'saleOrder', 'saleOrderExtendFieldEight', 'ams', 'productOrder', 'productOrderExtendFieldEight');
INSERT INTO `dfs_field_mapping`(`id`, `upstream_service`, `upstream_order_type`, `upstream_field_code`, `downstream_service`, `downstream_order_type`, `downstream_field_code`) VALUES (NULL, 'ams', 'saleOrder', 'saleOrderExtendFieldNine', 'ams', 'productOrder', 'productOrderExtendFieldNine');
INSERT INTO `dfs_field_mapping`(`id`, `upstream_service`, `upstream_order_type`, `upstream_field_code`, `downstream_service`, `downstream_order_type`, `downstream_field_code`) VALUES (NULL, 'ams', 'saleOrder', 'saleOrderExtendFieldTen', 'ams', 'productOrder', 'productOrderExtendFieldTen');
INSERT INTO `dfs_field_mapping`(`id`, `upstream_service`, `upstream_order_type`, `upstream_field_code`, `downstream_service`, `downstream_order_type`, `downstream_field_code`) VALUES (NULL, 'ams', 'saleOrder', 'saleOrderMaterialExtendFieldOne', 'ams', 'productOrder', 'productOrderMaterialExtendFieldOne');
INSERT INTO `dfs_field_mapping`(`id`, `upstream_service`, `upstream_order_type`, `upstream_field_code`, `downstream_service`, `downstream_order_type`, `downstream_field_code`) VALUES (NULL, 'ams', 'saleOrder', 'saleOrderMaterialExtendFieldTwo', 'ams', 'productOrder', 'productOrderMaterialExtendFieldTwo');
INSERT INTO `dfs_field_mapping`(`id`, `upstream_service`, `upstream_order_type`, `upstream_field_code`, `downstream_service`, `downstream_order_type`, `downstream_field_code`) VALUES (NULL, 'ams', 'saleOrder', 'saleOrderMaterialExtendFieldThree', 'ams', 'productOrder', 'productOrderMaterialExtendFieldThree');
INSERT INTO `dfs_field_mapping`(`id`, `upstream_service`, `upstream_order_type`, `upstream_field_code`, `downstream_service`, `downstream_order_type`, `downstream_field_code`) VALUES (NULL, 'ams', 'saleOrder', 'saleOrderMaterialExtendFieldFour', 'ams', 'productOrder', 'productOrderMaterialExtendFieldFour');
INSERT INTO `dfs_field_mapping`(`id`, `upstream_service`, `upstream_order_type`, `upstream_field_code`, `downstream_service`, `downstream_order_type`, `downstream_field_code`) VALUES (NULL, 'ams', 'saleOrder', 'saleOrderMaterialExtendFieldFive', 'ams', 'productOrder', 'productOrderMaterialExtendFieldFive');
INSERT INTO `dfs_field_mapping`(`id`, `upstream_service`, `upstream_order_type`, `upstream_field_code`, `downstream_service`, `downstream_order_type`, `downstream_field_code`) VALUES (NULL, 'ams', 'saleOrder', 'saleOrderMaterialExtendFieldSix', 'ams', 'productOrder', 'productOrderMaterialExtendFieldSix');
INSERT INTO `dfs_field_mapping`(`id`, `upstream_service`, `upstream_order_type`, `upstream_field_code`, `downstream_service`, `downstream_order_type`, `downstream_field_code`) VALUES (NULL, 'ams', 'saleOrder', 'saleOrderMaterialExtendFieldSeven', 'ams', 'productOrder', 'productOrderMaterialExtendFieldSeven');
INSERT INTO `dfs_field_mapping`(`id`, `upstream_service`, `upstream_order_type`, `upstream_field_code`, `downstream_service`, `downstream_order_type`, `downstream_field_code`) VALUES (NULL, 'ams', 'saleOrder', 'saleOrderMaterialExtendFieldEight', 'ams', 'productOrder', 'productOrderMaterialExtendFieldEight');
INSERT INTO `dfs_field_mapping`(`id`, `upstream_service`, `upstream_order_type`, `upstream_field_code`, `downstream_service`, `downstream_order_type`, `downstream_field_code`) VALUES (NULL, 'ams', 'saleOrder', 'saleOrderMaterialExtendFieldNine', 'ams', 'productOrder', 'productOrderMaterialExtendFieldNine');
INSERT INTO `dfs_field_mapping`(`id`, `upstream_service`, `upstream_order_type`, `upstream_field_code`, `downstream_service`, `downstream_order_type`, `downstream_field_code`) VALUES (NULL, 'ams', 'saleOrder', 'saleOrderMaterialExtendFieldTen', 'ams', 'productOrder', 'productOrderMaterialExtendFieldTen');
INSERT INTO `dfs_field_mapping`(`id`, `upstream_service`, `upstream_order_type`, `upstream_field_code`, `downstream_service`, `downstream_order_type`, `downstream_field_code`) VALUES (NULL, 'ams', 'productOrder', 'productOrderExtendFieldOne', 'dfs', 'workOrder', 'workOrderExtendFieldOne');
INSERT INTO `dfs_field_mapping`(`id`, `upstream_service`, `upstream_order_type`, `upstream_field_code`, `downstream_service`, `downstream_order_type`, `downstream_field_code`) VALUES (NULL, 'ams', 'productOrder', 'productOrderExtendFieldTwo', 'dfs', 'workOrder', 'workOrderExtendFieldTwo');
INSERT INTO `dfs_field_mapping`(`id`, `upstream_service`, `upstream_order_type`, `upstream_field_code`, `downstream_service`, `downstream_order_type`, `downstream_field_code`) VALUES (NULL, 'ams', 'productOrder', 'productOrderExtendFieldThree', 'dfs', 'workOrder', 'workOrderExtendFieldThree');
INSERT INTO `dfs_field_mapping`(`id`, `upstream_service`, `upstream_order_type`, `upstream_field_code`, `downstream_service`, `downstream_order_type`, `downstream_field_code`) VALUES (NULL, 'ams', 'productOrder', 'productOrderExtendFieldFour', 'dfs', 'workOrder', 'workOrderExtendFieldFour');
INSERT INTO `dfs_field_mapping`(`id`, `upstream_service`, `upstream_order_type`, `upstream_field_code`, `downstream_service`, `downstream_order_type`, `downstream_field_code`) VALUES (NULL, 'ams', 'productOrder', 'productOrderExtendFieldFive', 'dfs', 'workOrder', 'workOrderExtendFieldFive');
INSERT INTO `dfs_field_mapping`(`id`, `upstream_service`, `upstream_order_type`, `upstream_field_code`, `downstream_service`, `downstream_order_type`, `downstream_field_code`) VALUES (NULL, 'ams', 'productOrder', 'productOrderExtendFieldSix', 'dfs', 'workOrder', 'workOrderExtendFieldSix');
INSERT INTO `dfs_field_mapping`(`id`, `upstream_service`, `upstream_order_type`, `upstream_field_code`, `downstream_service`, `downstream_order_type`, `downstream_field_code`) VALUES (NULL, 'ams', 'productOrder', 'productOrderExtendFieldSeven', 'dfs', 'workOrder', 'workOrderExtendFieldSeven');
INSERT INTO `dfs_field_mapping`(`id`, `upstream_service`, `upstream_order_type`, `upstream_field_code`, `downstream_service`, `downstream_order_type`, `downstream_field_code`) VALUES (NULL, 'ams', 'productOrder', 'productOrderExtendFieldEight', 'dfs', 'workOrder', 'workOrderExtendFieldEight');
INSERT INTO `dfs_field_mapping`(`id`, `upstream_service`, `upstream_order_type`, `upstream_field_code`, `downstream_service`, `downstream_order_type`, `downstream_field_code`) VALUES (NULL, 'ams', 'productOrder', 'productOrderExtendFieldNine', 'dfs', 'workOrder', 'workOrderExtendFieldNine');
INSERT INTO `dfs_field_mapping`(`id`, `upstream_service`, `upstream_order_type`, `upstream_field_code`, `downstream_service`, `downstream_order_type`, `downstream_field_code`) VALUES (NULL, 'ams', 'productOrder', 'productOrderExtendFieldTen', 'dfs', 'workOrder', 'workOrderExtendFieldTen');
INSERT INTO `dfs_field_mapping`(`id`, `upstream_service`, `upstream_order_type`, `upstream_field_code`, `downstream_service`, `downstream_order_type`, `downstream_field_code`) VALUES (NULL, 'ams', 'productOrder', 'productOrderMaterialExtendFieldOne', 'dfs', 'workOrder', 'workOrderMaterialExtendFieldOne');
INSERT INTO `dfs_field_mapping`(`id`, `upstream_service`, `upstream_order_type`, `upstream_field_code`, `downstream_service`, `downstream_order_type`, `downstream_field_code`) VALUES (NULL, 'ams', 'productOrder', 'productOrderMaterialExtendFieldTwo', 'dfs', 'workOrder', 'workOrderMaterialExtendFieldTwo');
INSERT INTO `dfs_field_mapping`(`id`, `upstream_service`, `upstream_order_type`, `upstream_field_code`, `downstream_service`, `downstream_order_type`, `downstream_field_code`) VALUES (NULL, 'ams', 'productOrder', 'productOrderMaterialExtendFieldThree', 'dfs', 'workOrder', 'workOrderMaterialExtendFieldThree');
INSERT INTO `dfs_field_mapping`(`id`, `upstream_service`, `upstream_order_type`, `upstream_field_code`, `downstream_service`, `downstream_order_type`, `downstream_field_code`) VALUES (NULL, 'ams', 'productOrder', 'productOrderMaterialExtendFieldFour', 'dfs', 'workOrder', 'workOrderMaterialExtendFieldFour');
INSERT INTO `dfs_field_mapping`(`id`, `upstream_service`, `upstream_order_type`, `upstream_field_code`, `downstream_service`, `downstream_order_type`, `downstream_field_code`) VALUES (NULL, 'ams', 'productOrder', 'productOrderMaterialExtendFieldFive', 'dfs', 'workOrder', 'workOrderMaterialExtendFieldFive');
INSERT INTO `dfs_field_mapping`(`id`, `upstream_service`, `upstream_order_type`, `upstream_field_code`, `downstream_service`, `downstream_order_type`, `downstream_field_code`) VALUES (NULL, 'ams', 'productOrder', 'productOrderMaterialExtendFieldSix', 'dfs', 'workOrder', 'workOrderMaterialExtendFieldSix');
INSERT INTO `dfs_field_mapping`(`id`, `upstream_service`, `upstream_order_type`, `upstream_field_code`, `downstream_service`, `downstream_order_type`, `downstream_field_code`) VALUES (NULL, 'ams', 'productOrder', 'productOrderMaterialExtendFieldSeven', 'dfs', 'workOrder', 'workOrderMaterialExtendFieldSeven');
INSERT INTO `dfs_field_mapping`(`id`, `upstream_service`, `upstream_order_type`, `upstream_field_code`, `downstream_service`, `downstream_order_type`, `downstream_field_code`) VALUES (NULL, 'ams', 'productOrder', 'productOrderMaterialExtendFieldEight', 'dfs', 'workOrder', 'workOrderMaterialExtendFieldEight');
INSERT INTO `dfs_field_mapping`(`id`, `upstream_service`, `upstream_order_type`, `upstream_field_code`, `downstream_service`, `downstream_order_type`, `downstream_field_code`) VALUES (NULL, 'ams', 'productOrder', 'productOrderMaterialExtendFieldNine', 'dfs', 'workOrder', 'workOrderMaterialExtendFieldNine');
INSERT INTO `dfs_field_mapping`(`id`, `upstream_service`, `upstream_order_type`, `upstream_field_code`, `downstream_service`, `downstream_order_type`, `downstream_field_code`) VALUES (NULL, 'ams', 'productOrder', 'productOrderMaterialExtendFieldTen', 'dfs', 'workOrder', 'workOrderMaterialExtendFieldTen');
INSERT INTO `dfs_field_mapping`(`id`, `upstream_service`, `upstream_order_type`, `upstream_field_code`, `downstream_service`, `downstream_order_type`, `downstream_field_code`) VALUES (NULL, 'ams', 'saleOrder', 'saleOrderExtendFieldOne', 'dfs', 'workOrder', 'workOrderExtendFieldOne');
INSERT INTO `dfs_field_mapping`(`id`, `upstream_service`, `upstream_order_type`, `upstream_field_code`, `downstream_service`, `downstream_order_type`, `downstream_field_code`) VALUES (NULL, 'ams', 'saleOrder', 'saleOrderExtendFieldTwo', 'dfs', 'workOrder', 'workOrderExtendFieldTwo');
INSERT INTO `dfs_field_mapping`(`id`, `upstream_service`, `upstream_order_type`, `upstream_field_code`, `downstream_service`, `downstream_order_type`, `downstream_field_code`) VALUES (NULL, 'ams', 'saleOrder', 'saleOrderExtendFieldThree', 'dfs', 'workOrder', 'workOrderExtendFieldThree');
INSERT INTO `dfs_field_mapping`(`id`, `upstream_service`, `upstream_order_type`, `upstream_field_code`, `downstream_service`, `downstream_order_type`, `downstream_field_code`) VALUES (NULL, 'ams', 'saleOrder', 'saleOrderExtendFieldFour', 'dfs', 'workOrder', 'workOrderExtendFieldFour');
INSERT INTO `dfs_field_mapping`(`id`, `upstream_service`, `upstream_order_type`, `upstream_field_code`, `downstream_service`, `downstream_order_type`, `downstream_field_code`) VALUES (NULL, 'ams', 'saleOrder', 'saleOrderExtendFieldFive', 'dfs', 'workOrder', 'workOrderExtendFieldFive');
INSERT INTO `dfs_field_mapping`(`id`, `upstream_service`, `upstream_order_type`, `upstream_field_code`, `downstream_service`, `downstream_order_type`, `downstream_field_code`) VALUES (NULL, 'ams', 'saleOrder', 'saleOrderExtendFieldSix', 'dfs', 'workOrder', 'workOrderExtendFieldSix');
INSERT INTO `dfs_field_mapping`(`id`, `upstream_service`, `upstream_order_type`, `upstream_field_code`, `downstream_service`, `downstream_order_type`, `downstream_field_code`) VALUES (NULL, 'ams', 'saleOrder', 'saleOrderExtendFieldSeven', 'dfs', 'workOrder', 'workOrderExtendFieldSeven');
INSERT INTO `dfs_field_mapping`(`id`, `upstream_service`, `upstream_order_type`, `upstream_field_code`, `downstream_service`, `downstream_order_type`, `downstream_field_code`) VALUES (NULL, 'ams', 'saleOrder', 'saleOrderExtendFieldEight', 'dfs', 'workOrder', 'workOrderExtendFieldEight');
INSERT INTO `dfs_field_mapping`(`id`, `upstream_service`, `upstream_order_type`, `upstream_field_code`, `downstream_service`, `downstream_order_type`, `downstream_field_code`) VALUES (NULL, 'ams', 'saleOrder', 'saleOrderExtendFieldNine', 'dfs', 'workOrder', 'workOrderExtendFieldNine');
INSERT INTO `dfs_field_mapping`(`id`, `upstream_service`, `upstream_order_type`, `upstream_field_code`, `downstream_service`, `downstream_order_type`, `downstream_field_code`) VALUES (NULL, 'ams', 'saleOrder', 'saleOrderExtendFieldTen', 'dfs', 'workOrder', 'workOrderExtendFieldTen');
INSERT INTO `dfs_field_mapping`(`id`, `upstream_service`, `upstream_order_type`, `upstream_field_code`, `downstream_service`, `downstream_order_type`, `downstream_field_code`) VALUES (NULL, 'ams', 'saleOrder', 'saleOrderMaterialExtendFieldOne', 'dfs', 'workOrder', 'workOrderMaterialExtendFieldOne');
INSERT INTO `dfs_field_mapping`(`id`, `upstream_service`, `upstream_order_type`, `upstream_field_code`, `downstream_service`, `downstream_order_type`, `downstream_field_code`) VALUES (NULL, 'ams', 'saleOrder', 'saleOrderMaterialExtendFieldTwo', 'dfs', 'workOrder', 'workOrderMaterialExtendFieldTwo');
INSERT INTO `dfs_field_mapping`(`id`, `upstream_service`, `upstream_order_type`, `upstream_field_code`, `downstream_service`, `downstream_order_type`, `downstream_field_code`) VALUES (NULL, 'ams', 'saleOrder', 'saleOrderMaterialExtendFieldThree', 'dfs', 'workOrder', 'workOrderMaterialExtendFieldThree');
INSERT INTO `dfs_field_mapping`(`id`, `upstream_service`, `upstream_order_type`, `upstream_field_code`, `downstream_service`, `downstream_order_type`, `downstream_field_code`) VALUES (NULL, 'ams', 'saleOrder', 'saleOrderMaterialExtendFieldFour', 'dfs', 'workOrder', 'workOrderMaterialExtendFieldFour');
INSERT INTO `dfs_field_mapping`(`id`, `upstream_service`, `upstream_order_type`, `upstream_field_code`, `downstream_service`, `downstream_order_type`, `downstream_field_code`) VALUES (NULL, 'ams', 'saleOrder', 'saleOrderMaterialExtendFieldFive', 'dfs', 'workOrder', 'workOrderMaterialExtendFieldFive');
INSERT INTO `dfs_field_mapping`(`id`, `upstream_service`, `upstream_order_type`, `upstream_field_code`, `downstream_service`, `downstream_order_type`, `downstream_field_code`) VALUES (NULL, 'ams', 'saleOrder', 'saleOrderMaterialExtendFieldSix', 'dfs', 'workOrder', 'workOrderMaterialExtendFieldSix');
INSERT INTO `dfs_field_mapping`(`id`, `upstream_service`, `upstream_order_type`, `upstream_field_code`, `downstream_service`, `downstream_order_type`, `downstream_field_code`) VALUES (NULL, 'ams', 'saleOrder', 'saleOrderMaterialExtendFieldSeven', 'dfs', 'workOrder', 'workOrderMaterialExtendFieldSeven');
INSERT INTO `dfs_field_mapping`(`id`, `upstream_service`, `upstream_order_type`, `upstream_field_code`, `downstream_service`, `downstream_order_type`, `downstream_field_code`) VALUES (NULL, 'ams', 'saleOrder', 'saleOrderMaterialExtendFieldEight', 'dfs', 'workOrder', 'workOrderMaterialExtendFieldEight');
INSERT INTO `dfs_field_mapping`(`id`, `upstream_service`, `upstream_order_type`, `upstream_field_code`, `downstream_service`, `downstream_order_type`, `downstream_field_code`) VALUES (NULL, 'ams', 'saleOrder', 'saleOrderMaterialExtendFieldNine', 'dfs', 'workOrder', 'workOrderMaterialExtendFieldNine');
INSERT INTO `dfs_field_mapping`(`id`, `upstream_service`, `upstream_order_type`, `upstream_field_code`, `downstream_service`, `downstream_order_type`, `downstream_field_code`) VALUES (NULL, 'ams', 'saleOrder', 'saleOrderMaterialExtendFieldTen', 'dfs', 'workOrder', 'workOrderMaterialExtendFieldTen');
