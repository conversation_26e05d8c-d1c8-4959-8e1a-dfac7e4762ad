-- ================================================此脚本专为指标需求设定，如需写脚本，请移步到3.12.1.1=======================================================
-- ================================================此脚本专为指标需求设定，如需写脚本，请移步到3.12.1.1=======================================================
-- ================================================此脚本专为指标需求设定，如需写脚本，请移步到3.12.1.1=======================================================
-- 工单
call `dfs_metrics`.`proc_add_column`(
        'dfs_metrics_work_order',
        'material_code',
        'ALTER TABLE `dfs_metrics_work_order` ADD COLUMN `material_code` varchar(255) DEFAULT NULL COMMENT ''物料编码''');
call `dfs_metrics`.`proc_add_column`(
        'dfs_metrics_work_order',
        'material_name',
        'ALTER TABLE `dfs_metrics_work_order` ADD COLUMN `material_name` varchar(255) DEFAULT NULL COMMENT ''物料名称''');

call `dfs_metrics`.`proc_add_column`(
        'dfs_metrics_work_order_daily',
        'material_code',
        'ALTER TABLE `dfs_metrics_work_order_daily` ADD COLUMN `material_code` varchar(255) DEFAULT NULL COMMENT ''物料编码''');
call `dfs_metrics`.`proc_add_column`(
        'dfs_metrics_work_order_daily',
        'material_name',
        'ALTER TABLE `dfs_metrics_work_order_daily` ADD COLUMN `material_name` varchar(255) DEFAULT NULL COMMENT ''物料名称''');

call `dfs_metrics`.`proc_add_column`(
        'dfs_metrics_work_order_hourly',
        'material_code',
        'ALTER TABLE `dfs_metrics_work_order_hourly` ADD COLUMN `material_code` varchar(255) DEFAULT NULL COMMENT ''物料编码''');
call `dfs_metrics`.`proc_add_column`(
        'dfs_metrics_work_order_hourly',
        'material_name',
        'ALTER TABLE `dfs_metrics_work_order_hourly` ADD COLUMN `material_name` varchar(255) DEFAULT NULL COMMENT ''物料名称''');

call `dfs_metrics`.`proc_add_column`(
        'dfs_metrics_work_order_10min',
        'material_code',
        'ALTER TABLE `dfs_metrics_work_order_10min` ADD COLUMN `material_code` varchar(255) DEFAULT NULL COMMENT ''物料编码''');
call `dfs_metrics`.`proc_add_column`(
        'dfs_metrics_work_order_10min',
        'material_name',
        'ALTER TABLE `dfs_metrics_work_order_10min` ADD COLUMN `material_name` varchar(255) DEFAULT NULL COMMENT ''物料名称''');

call `dfs_metrics`.`proc_add_column`(
        'dfs_metrics_work_order_procedure',
        'material_code',
        'ALTER TABLE `dfs_metrics_work_order_procedure` ADD COLUMN `material_code` varchar(255) DEFAULT NULL COMMENT ''物料编码''');
call `dfs_metrics`.`proc_add_column`(
        'dfs_metrics_work_order_procedure',
        'material_name',
        'ALTER TABLE `dfs_metrics_work_order_procedure` ADD COLUMN `material_name` varchar(255) DEFAULT NULL COMMENT ''物料名称''');


-- 工资
call `dfs_metrics`.`proc_add_column`(
        'dfs_metrics_valuation_cal',
        'work_center_name',
        'ALTER TABLE `dfs_metrics_valuation_cal` ADD COLUMN `work_center_name` varchar(255) DEFAULT NULL COMMENT ''工作中心名称''');
call `dfs_metrics`.`proc_add_column`(
        'dfs_metrics_valuation_cal',
        'line_name',
        'ALTER TABLE `dfs_metrics_valuation_cal` ADD COLUMN `line_name` varchar(255) DEFAULT NULL COMMENT ''产线名称''');
call `dfs_metrics`.`proc_add_column`(
        'dfs_metrics_valuation_cal',
        'device_name',
        'ALTER TABLE `dfs_metrics_valuation_cal` ADD COLUMN `device_name` varchar(255) DEFAULT NULL COMMENT ''设备名称''');
call `dfs_metrics`.`proc_add_column`(
        'dfs_metrics_valuation_cal',
        'team_name',
        'ALTER TABLE `dfs_metrics_valuation_cal` ADD COLUMN `team_name` varchar(255) DEFAULT NULL COMMENT ''班组名称''');



-- 质量
call `dfs_metrics`.`proc_add_column`(
        'dfs_metrics_quality_product_order_defect_daily',
        'defect_type_name',
        'ALTER TABLE `dfs_metrics_quality_product_order_defect_daily` ADD COLUMN `defect_type_name` varchar(255) DEFAULT NULL COMMENT ''不良类型名称''');
call `dfs_metrics`.`proc_add_column`(
        'dfs_metrics_quality_product_order_defect_daily',
        'material_name',
        'ALTER TABLE `dfs_metrics_quality_product_order_defect_daily` ADD COLUMN `material_name` varchar(255) DEFAULT NULL COMMENT ''物料名称''');

call `dfs_metrics`.`proc_add_column`(
        'dfs_metrics_quality_line_material_defect_daily',
        'defect_type_name',
        'ALTER TABLE `dfs_metrics_quality_line_material_defect_daily` ADD COLUMN `defect_type_name` varchar(255) DEFAULT NULL COMMENT ''不良类型名称''');




-- 订单
call `dfs_metrics`.`proc_add_column`(
        'dfs_metrics_product_order',
        'material_code',
        'ALTER TABLE `dfs_metrics_product_order` ADD COLUMN `material_code` varchar(255) DEFAULT NULL COMMENT ''物料编码''');
call `dfs_metrics`.`proc_add_column`(
        'dfs_metrics_product_order',
        'material_name',
        'ALTER TABLE `dfs_metrics_product_order` ADD COLUMN `material_name` varchar(255) DEFAULT NULL COMMENT ''物料名称''');
call `dfs_metrics`.`proc_modify_column`(
        'dfs_metrics_product_order',
        'product_order_id',
        'ALTER TABLE `dfs_metrics_product_order` MODIFY COLUMN `product_order_id` int(11) NOT NULL COMMENT ''生产订单id''');
call `dfs_metrics`.`proc_modify_column`(
        'dfs_metrics_product_order',
        'product_order_number',
        'ALTER TABLE `dfs_metrics_product_order` MODIFY COLUMN `product_order_number` varchar(255) NOT NULL COMMENT ''生产订单号''');

call `dfs_metrics`.`proc_add_column`(
        'dfs_metrics_product_order_daily',
        'material_code',
        'ALTER TABLE `dfs_metrics_product_order_daily` ADD COLUMN `material_code` varchar(255) DEFAULT NULL COMMENT ''物料编码''');
call `dfs_metrics`.`proc_add_column`(
        'dfs_metrics_product_order_daily',
        'material_name',
        'ALTER TABLE `dfs_metrics_product_order_daily` ADD COLUMN `material_name` varchar(255) DEFAULT NULL COMMENT ''物料名称''');
call `dfs_metrics`.`proc_modify_column`(
        'dfs_metrics_product_order_daily',
        'product_order_id',
        'ALTER TABLE `dfs_metrics_product_order_daily` MODIFY COLUMN `product_order_id` int(11) NOT NULL COMMENT ''生产订单id''');
call `dfs_metrics`.`proc_modify_column`(
        'dfs_metrics_product_order_daily',
        'product_order_number',
        'ALTER TABLE `dfs_metrics_product_order_daily` MODIFY COLUMN `product_order_number` varchar(255) NOT NULL COMMENT ''生产订单号''');

call `dfs_metrics`.`proc_add_column`(
        'dfs_metrics_product_order_monthly',
        'material_code',
        'ALTER TABLE `dfs_metrics_product_order_monthly` ADD COLUMN `material_code` varchar(255) DEFAULT NULL COMMENT ''物料编码''');
call `dfs_metrics`.`proc_add_column`(
        'dfs_metrics_product_order_monthly',
        'material_name',
        'ALTER TABLE `dfs_metrics_product_order_monthly` ADD COLUMN `material_name` varchar(255) DEFAULT NULL COMMENT ''物料名称''');
call `dfs_metrics`.`proc_modify_column`(
        'dfs_metrics_product_order_monthly',
        'product_order_id',
        'ALTER TABLE `dfs_metrics_product_order_monthly` MODIFY COLUMN `product_order_id` int(11) NOT NULL COMMENT ''生产订单id''');
call `dfs_metrics`.`proc_modify_column`(
        'dfs_metrics_product_order_monthly',
        'product_order_number',
        'ALTER TABLE `dfs_metrics_product_order_monthly` MODIFY COLUMN `product_order_number` varchar(255) NOT NULL COMMENT ''生产订单号''');



-- 物料
call `dfs_metrics`.`proc_add_column`(
        'dfs_metrics_material_trace_daily',
        'material_name',
        'ALTER TABLE `dfs_metrics_material_trace_daily` ADD COLUMN `material_name` varchar(255) DEFAULT NULL COMMENT ''物料名称''');
call `dfs_metrics`.`proc_add_column`(
        'dfs_metrics_material_trace_daily',
        'produce_material_name',
        'ALTER TABLE `dfs_metrics_material_trace_daily` ADD COLUMN `produce_material_name` varchar(255) DEFAULT NULL COMMENT ''产成物料名称''');
call `dfs_metrics`.`proc_add_column`(
        'dfs_metrics_material_trace_daily',
        'line_name',
        'ALTER TABLE `dfs_metrics_material_trace_daily` ADD COLUMN `line_name` varchar(255) DEFAULT NULL COMMENT ''产线名称''');

call `dfs_metrics`.`proc_add_column`(
        'dfs_metrics_end_material',
        'material_name',
        'ALTER TABLE `dfs_metrics_end_material` ADD COLUMN `material_name` varchar(255) DEFAULT NULL COMMENT ''物料名称''');

call `dfs_metrics`.`proc_add_column`(
        'dfs_metrics_end_material_daily',
        'material_name',
        'ALTER TABLE `dfs_metrics_end_material_daily` ADD COLUMN `material_name` varchar(255) DEFAULT NULL COMMENT ''物料名称''');



-- 产线
call `dfs_metrics`.`proc_add_column`(
        'dfs_metrics_line_material_defect_daily',
        'line_name',
        'ALTER TABLE `dfs_metrics_line_material_defect_daily` ADD COLUMN `line_name` varchar(255) DEFAULT NULL COMMENT ''产线名称''');

call `dfs_metrics`.`proc_modify_column`(
        'dfs_metrics_line_daily',
        'line_id',
        'ALTER TABLE `dfs_metrics_line_daily` MODIFY COLUMN `line_id` int(11) NOT NULL COMMENT ''产线id''');
call `dfs_metrics`.`proc_modify_column`(
        'dfs_metrics_line_daily',
        'line_name',
        'ALTER TABLE `dfs_metrics_line_daily` MODIFY COLUMN `line_name` varchar(255) DEFAULT NULL COMMENT ''产线名称''');

call `dfs_metrics`.`proc_modify_column`(
        'dfs_metrics_line_monthly',
        'line_id',
        'ALTER TABLE `dfs_metrics_line_monthly` MODIFY COLUMN `line_id` int(11) NOT NULL COMMENT ''产线id''');
call `dfs_metrics`.`proc_modify_column`(
        'dfs_metrics_line_monthly',
        'line_name',
        'ALTER TABLE `dfs_metrics_line_monthly` MODIFY COLUMN `line_name` varchar(255) DEFAULT NULL COMMENT ''产线名称''');


-- 来料
call `dfs_metrics`.`proc_add_column`(
        'dfs_metrics_incoming_inspection_daily',
        'material_name',
        'ALTER TABLE `dfs_metrics_incoming_inspection_daily` ADD COLUMN `material_name` varchar(255) DEFAULT NULL COMMENT ''物料名称''');
call `dfs_metrics`.`proc_add_column`(
        'dfs_metrics_incoming_inspection_daily',
        'supplier_name',
        'ALTER TABLE `dfs_metrics_incoming_inspection_daily` ADD COLUMN `supplier_name` varchar(255) DEFAULT NULL COMMENT ''供应商名称''');


-- 设备
call `dfs_metrics`.`proc_add_column`(
        'dfs_metrics_device_oee',
        'device_name',
        'ALTER TABLE `dfs_metrics_device_oee` ADD COLUMN `device_name` varchar(255) DEFAULT NULL COMMENT ''设备名称''');
call `dfs_metrics`.`proc_add_column`(
        'dfs_metrics_device_state_daily',
        'device_name',
        'ALTER TABLE `dfs_metrics_device_state_daily` ADD COLUMN `device_name` varchar(255) DEFAULT NULL COMMENT ''设备名称''');

-- 表单配置的拓展字段统一重命名为  扩展字段
UPDATE `dfs_form_field_config` SET `field_name` = REPLACE (`field_name`, '拓展字段', '扩展字段');
-- 更新表单菜单的所属模块
UPDATE `dfs_form_config` SET `module` = 'list' WHERE `full_path_code` in ('workOrder.list', 'saleOrder.list.order', 'saleOrder.list.material', 'productOrder.list', 'purchaseRequestOrder.list', 'purchaseOrder.list.order', 'purchaseOrder.list.material', 'purchaseReceiptOrder.list.order', 'purchaseReceiptOrder.list.material', 'subcontractOrder.list.order', 'subcontractOrder.list.material', 'productOrderMaterialList.list.order', 'productOrderMaterialList.list.material', 'workOrderMaterialList.list.order', 'workOrderMaterialList.list.material');
UPDATE `dfs_form_config` SET `module` = 'edit-create' WHERE `full_path_code` in ('workOrder.editByCreate', 'saleOrder.editByCreate', 'productOrder.editByCreate', 'purchaseRequestOrder.editByCreate', 'purchaseOrder.editByCreate', 'purchaseReceiptOrder.editByCreate', 'subcontractOrder.editByCreate', 'productOrderMaterialList.editByCreate', 'workOrderMaterialList.editByCreate');
UPDATE `dfs_form_config` SET `module` = 'edit-other' WHERE `full_path_code` in ('workOrder.editByRelease','workOrder.editByInvestment','workOrder.editByHangUp','workOrder.editByFinish','workOrder.editByClosed','workOrder.editByCancel','saleOrder.editByRelease','saleOrder.editByFinish','saleOrder.editByClosed','saleOrder.editByCancel','productOrder.editByRelease','productOrder.editByFinish','productOrder.editByClosed','productOrder.editByCancel','purchaseRequestOrder.editByRelease','purchaseRequestOrder.editByFinish','purchaseRequestOrder.editByClosed','purchaseRequestOrder.editByCancel','purchaseOrder.editByRelease','purchaseOrder.editByFinish','purchaseOrder.editByClosed','purchaseOrder.editByCancel','purchaseReceiptOrder.editByRelease','purchaseReceiptOrder.editByFinish','purchaseReceiptOrder.editByClosed','purchaseReceiptOrder.editByCancel','subcontractOrder.editByRelease','subcontractOrder.editByFinish','subcontractOrder.editByClosed','subcontractOrder.editByCancel','productOrderMaterialList.editByRelease','productOrderMaterialList.editByFinish','productOrderMaterialList.editByClosed','productOrderMaterialList.editByCancel','workOrderMaterialList.editByRelease','workOrderMaterialList.editByFinish','workOrderMaterialList.editByClosed','workOrderMaterialList.editByCancel');
UPDATE `dfs_form_config` SET `module` = 'detail' WHERE `full_path_code` like '%.detail';
