-- 优化内置的API接口，只返回必要的信息，防止用户错误配置表单导致接口超时
UPDATE `dfs_api_transform` SET `res_body_script` = 'function transform(data){
  var result = [];
  for (var i = 0; i < data.data.records.length; i++) {
   var saleOrderNumber = data.data.records[i].saleOrderNumber;
    if(saleOrderNumber == null) {
      continue;
    }
    result.push({
      value: saleOrderNumber,
      label: saleOrderNumber
    });
  }
  return result;
}' WHERE `code` = 'getSaleOrderList';

UPDATE `dfs_api_transform` SET `res_body_script` = 'function transform(data){
  var result = [];
  for (var i = 0; i < data.data.length; i++) {
    result.push({
      value: data.data[i].projectDefineCode,
      label: data.data[i].projectDefineName
    });
  }
  return result;
}' WHERE `code` = 'getProjectDefineList';

UPDATE `dfs_api_transform` SET `res_body_script` = 'function transform(data){
  var result = [];
  for (var i = 0; i < data.data.records.length; i++) {
    result.push({
      value: data.data.records[i].code,
      label: data.data.records[i].name
    });
  }
  return result;
}', `remark` = '生效态的物料' WHERE `code` = 'getMaterialList';
UPDATE `dfs_api_transform` SET `req_body_script` = 'function transform(data){
  return {};
}',`res_body_script` = 'function transform(data){
  var result = [];
  for (var i = 0; i < data.data.records.length; i++) {
    result.push({
      value: data.data.records[i].schemeCode,
      label: data.data.records[i].schemeName
    });
  }
  return result;
}' WHERE `code` = 'getPackageSchemeList';
UPDATE `dfs_api_transform` SET `res_body_script` = 'function transform(data) {
  var result = [];
  for (var i = 0; i < data.data.records.length; i++) {
    result.push({
      value: data.data.records[i].gcode,
      label: data.data.records[i].gname
    });
  }
  return result;
}' WHERE `code` = 'getGridList';

DELETE FROM `dfs_api_transform` WHERE `code` = 'getWorkOrderTypeList';
UPDATE `dfs_api_transform` SET `res_body_script` = 'function transform(data){
  var result = [];
  for (var i = 0; i < data.data.length; i++) {
    result.push({
      value: data.data[i].code,
      label: data.data[i].name
    });
  }
  return result;
}' WHERE `code` = 'getWorkOrderAssignmentStateList';
UPDATE `dfs_api_transform` SET `res_body_script` = 'function transform(data) {
  var result = [];
  for (var i = 0; i < data.data.records.length; i++) {
    result.push({
      value: data.data.records[i].customerCode,
      label: data.data.records[i].customerName
    });
  }
  return result;
}' WHERE `code` = 'getCustomerList';
UPDATE `dfs_api_transform` SET `res_body_script` = 'function transform(data) {
  var result = [];
  for (var i = 0; i < data.data.records.length; i++) {
    result.push({
      value: data.data.records[i].productOrderNumber,
      label: data.data.records[i].productOrderNumber
    });
  }
  return result;
}' WHERE `code` = 'getProductOrderList';




