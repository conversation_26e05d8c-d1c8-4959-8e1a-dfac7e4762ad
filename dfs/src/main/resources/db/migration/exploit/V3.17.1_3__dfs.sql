-- ================================================此脚本专为指标相关设定，如需写脚本，请移步到3.17.1.1=======================================================
-- ================================================此脚本专为指标相关设定，如需写脚本，请移步到3.17.1.1=======================================================
ALTER TABLE `dfs_metrics`.`dfs_metrics_end_material` COMMENT = '产成品-整体统计';
ALTER TABLE `dfs_metrics`.`dfs_metrics_end_material_daily` COMMENT = '产成品-每日统计';
ALTER TABLE `dfs_metrics`.`dfs_metrics_exception_classify_daily` COMMENT = '异常管理-每日统计';
ALTER TABLE `dfs_metrics`.`dfs_metrics_incoming_inspection_daily` COMMENT = '来料检验-每日统计';
ALTER TABLE `dfs_metrics`.`dfs_metrics_line_monthly` COMMENT = '产线-每月统计';
ALTER TABLE `dfs_metrics`.`dfs_metrics_line_daily` COMMENT = '产线-每日统计';
ALTER TABLE `dfs_metrics`.`dfs_metrics_line_material_defect_daily` COMMENT = '产线物料不良项-每日统计';
ALTER TABLE `dfs_metrics`.`dfs_metrics_maintain_line_daily` COMMENT = '维修产线-每日统计';
ALTER TABLE `dfs_metrics`.`dfs_metrics_maintain_team_daily` COMMENT = '维修班组-每日统计';
ALTER TABLE `dfs_metrics`.`dfs_metrics_material_trace_daily` COMMENT = '物料-每日统计';
ALTER TABLE `dfs_metrics`.`dfs_metrics_procedure_hourly` COMMENT = '工序-小时统计';
ALTER TABLE `dfs_metrics`.`dfs_metrics_product_order` COMMENT = '生产订单-整体统计';
ALTER TABLE `dfs_metrics`.`dfs_metrics_product_order_daily` COMMENT = '生产订单-每日统计';
ALTER TABLE `dfs_metrics`.`dfs_metrics_product_order_monthly` COMMENT = '生产订单-每月统计';
ALTER TABLE `dfs_metrics`.`dfs_metrics_product_order_procedure` COMMENT = '生产订单产线工序-整体统计';
ALTER TABLE `dfs_metrics`.`dfs_metrics_product_order_summary_monthly` COMMENT = '生产订单-汇总每月统计';
ALTER TABLE `dfs_metrics`.`dfs_metrics_quality_defect_distribution_daily` COMMENT = '不良分布-每日统计';
ALTER TABLE `dfs_metrics`.`dfs_metrics_quality_line_material_daily` COMMENT = '质量产线物料-每日统计';
ALTER TABLE `dfs_metrics`.`dfs_metrics_quality_line_material_defect_daily` COMMENT = '质量产线物料不良项-每日统计';
ALTER TABLE `dfs_metrics`.`dfs_metrics_quality_product_order_defect_daily` COMMENT = '质量生成订单物料不良项-每日统计';
ALTER TABLE `dfs_metrics`.`dfs_metrics_sale_order_material` COMMENT = '销售订单-整体统计';
ALTER TABLE `dfs_metrics`.`dfs_metrics_sale_order_material_daily` COMMENT = '销售订单-每日统计';
ALTER TABLE `dfs_metrics`.`dfs_metrics_sale_order_summary_monthly` COMMENT = '销售订单-汇总每月统计';
ALTER TABLE `dfs_metrics`.`dfs_metrics_staff_daily` COMMENT = '员工-每日统计';
ALTER TABLE `dfs_metrics`.`dfs_metrics_team_daily` COMMENT = '班组-每日统计';
ALTER TABLE `dfs_metrics`.`dfs_metrics_top_daily` COMMENT = 'Top排行-每日统计';
ALTER TABLE `dfs_metrics`.`dfs_metrics_top_work_order` COMMENT = 'Topg工单排行-整体统计';
ALTER TABLE `dfs_metrics`.`dfs_metrics_work_order` COMMENT = '工单-整体统计';
ALTER TABLE `dfs_metrics`.`dfs_metrics_work_order_daily` COMMENT = '工单-每日统计';
ALTER TABLE `dfs_metrics`.`dfs_metrics_work_order_hourly` COMMENT = '工单-小时统计';
ALTER TABLE `dfs_metrics`.`dfs_metrics_work_order_10min` COMMENT = '工单-10分钟统计';
ALTER TABLE `dfs_metrics`.`dfs_metrics_work_order_procedure` COMMENT = '工单工序-整体统计';

CREATE TABLE IF NOT EXISTS `dfs_metrics`.`dfs_metrics_increase_base_weekly` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `uni_code` varchar(255) NOT NULL COMMENT '唯一标识',
    `category` varchar(255) NOT NULL COMMENT '类型',
    `record_time` datetime NOT NULL COMMENT '所属周的日期（按周日为第一天算）',
    `week_of_year` int(11) NOT NULL COMMENT '今年第n周',
    `release_increase_count` int(11) NOT NULL COMMENT '生效增量',
    `time` datetime DEFAULT NULL COMMENT '最新统计时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uni_code` (`uni_code`) USING BTREE,
    KEY `category` (`category`),
    KEY `record_week` (`record_time`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='增量基础数据-每周统计';

CREATE TABLE IF NOT EXISTS `dfs_metrics`.`dfs_metrics_increase_order_weekly` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `uni_code` varchar(255) NOT NULL COMMENT '唯一标识',
    `category` varchar(255) NOT NULL COMMENT '单据大类',
    `record_time` datetime NOT NULL COMMENT '所属周的日期（按周日为第一天算）',
    `week_of_year` int(11) NOT NULL COMMENT '今年第n周',
    `create_increase_count` int(11) NOT NULL COMMENT '创建增量',
    `time` datetime DEFAULT NULL COMMENT '最新统计时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uni_code` (`uni_code`) USING BTREE,
    KEY `category` (`category`),
    KEY `record_week` (`record_time`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='增量单据-每周统计';

INSERT INTO `dfs_model` (`type`, `name`, `code`, `seq`, `pid`) VALUES ('baseTargetGroupObject', '工厂', 'areaTarget', 14, -1);

INSERT INTO `dfs_target_dict`(`target_name`, `target_cnname`, `target_type`, `model_type`, `model_code`, `alarm_config`, `is_threshold_setting`, `unit`, `create_time`, `update_time`, `data_type`, `is_inner_target`, `default_frequency`, `default_frequency_unit`) VALUES ('increaseBaseWeekly', '基础数据增量每周统计', '1', 'baseTargetGroupObject', 'areaTarget', NULL, NULL, '1', NULL, NULL, NULL, 1, 5, 'm');
INSERT INTO `dfs_target_method`(`target_name`, `model_type`, `method_cname`, `method_name`, `has_frequency`, `multiple_choice`) VALUES ('increaseBaseWeekly', 'baseTargetGroupObject', '系统统计', 'increaseBaseWeekly', 1, NULL);
INSERT INTO `dfs_target_model_table_relate`(`target_name`, `model_id`, `table_schema`, `table_name`) VALUES ('increaseBaseWeekly', (SELECT id FROM `dfs_model` WHERE `code` = 'areaTarget'), 'dfs_metrics', 'dfs_metrics_increase_base_weekly');

INSERT INTO `dfs_target_dict`(`target_name`, `target_cnname`, `target_type`, `model_type`, `model_code`, `alarm_config`, `is_threshold_setting`, `unit`, `create_time`, `update_time`, `data_type`, `is_inner_target`, `default_frequency`, `default_frequency_unit`) VALUES ('increaseOrderWeekly', '单据增量每周统计', '1', 'baseTargetGroupObject', 'areaTarget', NULL, NULL, '1', NULL, NULL, NULL, 1, 5, 'm');
INSERT INTO `dfs_target_method`(`target_name`, `model_type`, `method_cname`, `method_name`, `has_frequency`, `multiple_choice`) VALUES ('increaseOrderWeekly', 'baseTargetGroupObject', '系统统计', 'increaseOrderWeekly', 1, NULL);
INSERT INTO `dfs_target_model_table_relate`(`target_name`, `model_id`, `table_schema`, `table_name`) VALUES ('increaseOrderWeekly', (SELECT id FROM `dfs_model` WHERE `code` = 'areaTarget'), 'dfs_metrics', 'dfs_metrics_increase_order_weekly');

INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_increase_base_weekly', '基础数据增量每周统计', 'id', '主键', 'int', 11, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_increase_base_weekly', '基础数据增量每周统计', 'uni_code', '唯一标识', 'varchar', 255, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_increase_base_weekly', '基础数据增量每周统计', 'category', '类型', 'varchar', 255, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_increase_base_weekly', '基础数据增量每周统计', 'record_time', '所属周的日期（按周日为第一天算）', 'datetime', 255, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_increase_base_weekly', '基础数据增量每周统计', 'week_of_year', '今年第n周', 'int', 11, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_increase_base_weekly', '基础数据增量每周统计', 'release_increase_count', '生效增量', 'double', 11, 2, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_increase_base_weekly', '基础数据增量每周统计', 'time', '最新统计时间', 'datetime', 0, 0, NULL, NULL);

INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_increase_order_weekly', '单据增量每周统计', 'id', '主键', 'int', 11, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_increase_order_weekly', '单据增量每周统计', 'uni_code', '唯一标识', 'varchar', 255, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_increase_order_weekly', '单据增量每周统计', 'category', '单据大类', 'varchar', 255, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_increase_order_weekly', '单据增量每周统计', 'record_time', '所属周的日期（按周日为第一天算）', 'datetime', 255, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_increase_order_weekly', '单据增量每周统计', 'week_of_year', '今年第n周', 'int', 11, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_increase_order_weekly', '单据增量每周统计', 'create_increase_count', '创建增量', 'double', 11, 2, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_increase_order_weekly', '单据增量每周统计', 'time', '最新统计时间', 'datetime', 0, 0, NULL, NULL);
