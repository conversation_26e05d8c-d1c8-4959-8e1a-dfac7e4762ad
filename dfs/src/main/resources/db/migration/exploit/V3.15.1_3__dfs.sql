-- ================================================此脚本专为标签需求设定，如需写脚本，请移步到3.15.1.1=======================================================
-- ================================================此脚本专为标签需求设定，如需写脚本，请移步到3.15.1.1=======================================================
-- ================================================此脚本专为标签需求设定，如需写脚本，请移步到3.15.1.1=======================================================
-- ================================================此脚本专为标签需求设定，如需写脚本，请移步到3.15.1.1=======================================================
-- ================================================此脚本专为标签需求设定，如需写脚本，请移步到3.15.1.1=======================================================

-- DDL
call proc_add_column(
        'dfs_config_label_type',
        'relate_type',
        'ALTER TABLE `dfs_config_label_type` ADD COLUMN `relate_type` varchar(50) DEFAULT null COMMENT ''关联类型'';');

ALTER TABLE dfs_config_label_info DROP PRIMARY KEY;
call proc_add_column(
        'dfs_config_label_info',
        'id',
        'ALTER TABLE `dfs_config_label_info` ADD COLUMN `id` int(11) NOT NULL AUTO_INCREMENT PRIMARY KEY FIRST;');
call proc_add_column(
        'dfs_config_label_info',
        'module_code',
        'ALTER TABLE `dfs_config_label_info` ADD COLUMN `module_code` varchar(50) DEFAULT ''base'' COMMENT ''模块编码'';');
call proc_add_column(
        'dfs_config_label_info',
        'form_field_code',
        'ALTER TABLE `dfs_config_label_info` ADD COLUMN `form_field_code` varchar(255) DEFAULT '''' COMMENT ''表单字段编码'';');

call proc_add_column(
        'dfs_config_label_type_info_relate',
        'module_code',
        'ALTER TABLE `dfs_config_label_type_info_relate` ADD COLUMN `module_code` varchar(50) DEFAULT ''base'' COMMENT ''模块编码'';');

call proc_add_unique_index('dfs_config_label_info','code','code');
call proc_add_column_index('dfs_config_label_info','placeholder','placeholder');


CREATE TABLE IF NOT EXISTS `dfs_label_module_config` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `type_code` varchar(255) NOT NULL COMMENT '标签类型编号',
    `module_code` varchar(255) NOT NULL COMMENT '模块编码',
    `module_name` varchar(255) DEFAULT '' COMMENT '模块名称',
    `have_all_field` tinyint(1) DEFAULT 0 COMMENT '是否有模块所有字段',
    `form_full_path_code` varchar(255) NULL COMMENT '表单路径编码',
    PRIMARY KEY (`id`) USING BTREE,
    KEY `type_code` (`type_code`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='标签模块配置表';

-- DML

update dfs_config_label_type set relate_type = 'workOrder' where type_code in ('finished','finishedProductCode','productFlowCode','workOrderNumber');
update dfs_config_label_type set relate_type = 'productOrder' where type_code in ('orderProductCode','productBarCode','productOrderNumber');
update dfs_config_label_type set relate_type = 'purchaseOrder' where type_code in ('purchaseSingleProductCode');
update dfs_config_label_type set relate_type = 'purchaseReceipt' where type_code in ('purchase');

truncate table dfs_label_module_config;
INSERT INTO dfs_label_module_config (type_code, module_code, module_name, have_all_field, form_full_path_code)
select type_code, 'base', '基础', 0, null from dfs_config_label_type;
INSERT INTO dfs_label_module_config (type_code, module_code, module_name, have_all_field, form_full_path_code)
select type_code, 'materialExtendField', '物料扩展字段', 1, null from dfs_config_label_type where type_code not in ('device', 'userInfo');
INSERT INTO dfs_label_module_config (type_code, module_code, module_name, have_all_field, form_full_path_code)
select type_code, 'workOrder', '生产工单', 1, 'workOrder.list' from dfs_config_label_type where type_code in ('finished','finishedProductCode','productFlowCode','workOrderNumber');
INSERT INTO dfs_label_module_config (type_code, module_code, module_name, have_all_field, form_full_path_code)
select type_code, 'batchExtendField', '批次扩展字段', 1, 'workOrder.detail' from dfs_config_label_type where type_code in ('finished');
INSERT INTO dfs_label_module_config (type_code, module_code, module_name, have_all_field, form_full_path_code)
select type_code, 'productOrder', '生产订单', 1, 'productOrder.list' from dfs_config_label_type where type_code in ('orderProductCode','productBarCode','productOrderNumber','finished','finishedProductCode','productFlowCode','workOrderNumber');
INSERT INTO dfs_label_module_config (type_code, module_code, module_name, have_all_field, form_full_path_code)
select type_code, 'batchExtendField', '批次扩展字段', 1, 'productOrder.detail' from dfs_config_label_type where type_code in ('productBarCode');
INSERT INTO dfs_label_module_config (type_code, module_code, module_name, have_all_field, form_full_path_code)
select type_code, 'batchExtendField', '批次扩展字段', 1, 'purchaseReceiptOrder.detail' from dfs_config_label_type where type_code in ('purchase');
INSERT INTO dfs_label_module_config (type_code, module_code, module_name, have_all_field, form_full_path_code)
select type_code, 'purchaseOrder', '采购订单', 1, 'purchaseOrder.list.order' from dfs_config_label_type where type_code in ('purchase','purchaseSingleProductCode');

delete from dfs_config_label_info where module_code <> 'base';

-- INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code)
-- select CONCAT('workOrderEntity.', c.field_code), f.field_name, CONCAT('\$\{workOrderEntity.', c.field_code, '\\}'), 1, 'workOrder', c.field_code from dfs_form_field_rule_config c
-- left join dfs_form_field_config f on f.full_path_code = c.field_name_full_path_code and f.field_code = c.field_code and f.module_code = c.module_code
-- and f.full_path_code in ('workOrder.list') and f.module_code = 'baseField' and f.type_code = 'sysField'
-- where c.full_path_code in ('workOrder.list') and c.module_code = 'baseField';
--
-- INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code)
-- select CONCAT('productOrderEntity.', c.field_code), f.field_name, CONCAT('\$\{productOrderEntity.', c.field_code, '\\}'), 1, 'productOrder', c.field_code from dfs_form_field_rule_config c
-- left join dfs_form_field_config f on f.full_path_code = c.field_name_full_path_code and f.field_code = c.field_code and f.module_code = c.module_code
-- and f.full_path_code in ('productOrder.list') and f.module_code = 'baseField' and f.type_code = 'sysField'
-- where c.full_path_code in ('productOrder.list') and c.module_code = 'baseField';

-- INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code)
-- select CONCAT('purchaseEntity.', c.field_code), f.field_name, CONCAT('\$\{purchaseEntity.', c.field_code, '\\}'), 1, 'purchaseOrder', c.field_code from dfs_form_field_rule_config c
-- left join dfs_form_field_config f on f.full_path_code = c.field_name_full_path_code and f.field_code = c.field_code and f.module_code = c.module_code
-- and f.full_path_code in ('purchaseOrder.list.order') and f.module_code = 'baseField' and f.type_code = 'sysField'
-- where c.full_path_code in ('purchaseOrder.list.order') and c.module_code = 'baseField';

-- 生产工单字段
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('workOrderEntity.workOrderNumber', '工单号', '\$\{workOrderEntity.workOrderNumber\}', 1, 'workOrder', 'workOrderNumber');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('workOrderEntity.workOrderName', '工单名称', '\$\{workOrderEntity.workOrderName\}', 1, 'workOrder', 'workOrderName');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('workOrderEntity.stateName', '状态', '\$\{workOrderEntity.stateName\}', 1, 'workOrder', 'stateName');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('workOrderEntity.assignmentStateName', '派工状态', '\$\{workOrderEntity.assignmentStateName\}', 1, 'workOrder', 'assignmentStateName');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('workOrderEntity.productOrderNumber', '生产订单号', '\$\{workOrderEntity.productOrderNumber\}', 1, 'workOrder', 'productOrderNumber');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('workOrderEntity.pickingStateName', '领料状态', '\$\{workOrderEntity.pickingStateName\}', 1, 'workOrder', 'pickingStateName');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('workOrderEntity.investCheckResultName', '投产检查结果', '\$\{workOrderEntity.investCheckResultName\}', 1, 'workOrder', 'investCheckResultName');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('workOrderEntity.priority', '优先级', '\$\{workOrderEntity.priority\}', 1, 'workOrder', 'priority');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('workOrderEntity.materialFields.code', '物料编码', '\$\{workOrderEntity.materialFields.code\}', 1, 'workOrder', 'code');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('workOrderEntity.materialFields.name', '物料名称', '\$\{workOrderEntity.materialFields.name\}', 1, 'workOrder', 'name');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('workOrderEntity.materialFields.standard', '物料规格', '\$\{workOrderEntity.materialFields.standard\}', 1, 'workOrder', 'standard');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('workOrderEntity.planQuantity', '计划数量', '\$\{workOrderEntity.planQuantity\}', 1, 'workOrder', 'planQuantity');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('workOrderEntity.finishCount', '完成数量', '\$\{workOrderEntity.finishCount\}', 1, 'workOrder', 'finishCount');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('workOrderEntity.plannedBatches', '计划批数', '\$\{workOrderEntity.plannedBatches\}', 1, 'workOrder', 'plannedBatches');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('workOrderEntity.actualBatches', '实际批数', '\$\{workOrderEntity.actualBatches\}', 1, 'workOrder', 'actualBatches');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('workOrderEntity.plansPerBatch', '每批计划数', '\$\{workOrderEntity.plansPerBatch\}', 1, 'workOrder', 'plansPerBatch');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('workOrderEntity.passRate', '合格率', '\$\{workOrderEntity.passRate\}', 1, 'workOrder', 'passRate');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('workOrderEntity.unqualified', '不合格数量', '\$\{workOrderEntity.unqualified\}', 1, 'workOrder', 'unqualified');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('workOrderEntity.inventoryQuantity', '入库数量', '\$\{workOrderEntity.inventoryQuantity\}', 1, 'workOrder', 'inventoryQuantity');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('workOrderEntity.customerCode', '客户编码', '\$\{workOrderEntity.customerCode\}', 1, 'workOrder', 'customerCode');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('workOrderEntity.customerName', '客户名称', '\$\{workOrderEntity.customerName\}', 1, 'workOrder', 'customerName');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('workOrderEntity.progress', '完成率', '\$\{workOrderEntity.progress\}', 1, 'workOrder', 'progress');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('workOrderEntity.materialFields.haveBom', 'BOM', '\$\{workOrderEntity.materialFields.haveBom\}', 1, 'workOrder', 'haveBom');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('workOrderEntity.materialFields.haveCraft', '工单工艺', '\$\{workOrderEntity.materialFields.haveCraft\}', 1, 'workOrder', 'haveCraft');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('workOrderEntity.comp', '单位', '\$\{workOrderEntity.comp\}', 1, 'workOrder', 'comp');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('workOrderEntity.procedureName', '工序名称', '\$\{workOrderEntity.procedureName\}', 1, 'workOrder', 'procedureName');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('workOrderEntity.lineName', '制造单元名称', '\$\{workOrderEntity.lineName\}', 1, 'workOrder', 'lineName');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('workOrderEntity.workCenterName', '工作中心', '\$\{workOrderEntity.workCenterName\}', 1, 'workOrder', 'workCenterName');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('workOrderEntity.teamName', '班组名称', '\$\{workOrderEntity.teamName\}', 1, 'workOrder', 'teamName');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('workOrderEntity.deviceName', '设备名称', '\$\{workOrderEntity.deviceName\}', 1, 'workOrder', 'deviceName');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('workOrderEntity.saleOrderNumber', '销售订单', '\$\{workOrderEntity.saleOrderNumber\}', 1, 'workOrder', 'saleOrderNumber');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('workOrderEntity.projectDefineName', '项目名称', '\$\{workOrderEntity.projectDefineName\}', 1, 'workOrder', 'projectDefineName');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('workOrderEntity.contractName', '合同名称', '\$\{workOrderEntity.contractName\}', 1, 'workOrder', 'contractName');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('workOrderEntity.createDate', '创建时间', '\$\{workOrderEntity.createDate\}', 1, 'workOrder', 'createDate');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('workOrderEntity.updateDate', '更新时间', '\$\{workOrderEntity.updateDate\}', 1, 'workOrder', 'updateDate');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('workOrderEntity.startDate', '计划开始时间', '\$\{workOrderEntity.startDate\}', 1, 'workOrder', 'startDate');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('workOrderEntity.endDate', '计划完成时间', '\$\{workOrderEntity.endDate\}', 1, 'workOrder', 'endDate');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('workOrderEntity.actualStartDate', '实际开始时间', '\$\{workOrderEntity.actualStartDate\}', 1, 'workOrder', 'actualStartDate');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('workOrderEntity.actualEndDate', '实际完成时间', '\$\{workOrderEntity.actualEndDate\}', 1, 'workOrder', 'actualEndDate');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('workOrderEntity.materialFields.skuEntity.skuName', '特征参数', '\$\{workOrderEntity.materialFields.skuEntity.skuName\}', 1, 'workOrder', 'auxiliaryAttr');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('workOrderEntity.inStockCount', '流转数量', '\$\{workOrderEntity.inStockCount\}', 1, 'workOrder', 'inStockCount');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('workOrderEntity.actualWorkingHours', '生产时长', '\$\{workOrderEntity.actualWorkingHours\}', 1, 'workOrder', 'actualWorkingHours');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('workOrderEntity.circulationDuration', '流转时长', '\$\{workOrderEntity.circulationDuration\}', 1, 'workOrder', 'circulationDuration');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('workOrderEntity.executionStatusName', '工单执行', '\$\{workOrderEntity.executionStatusName\}', 1, 'workOrder', 'executionStatusName');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('workOrderEntity.workOrderExtendFieldOneName', '工单扩展字段1', '\$\{workOrderEntity.workOrderExtendFieldOneName\}', 1, 'workOrder', 'workOrderExtendFieldOneName');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('workOrderEntity.workOrderExtendFieldTwoName', '工单扩展字段2', '\$\{workOrderEntity.workOrderExtendFieldTwoName\}', 1, 'workOrder', 'workOrderExtendFieldTwoName');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('workOrderEntity.workOrderExtendFieldThreeName', '工单扩展字段3', '\$\{workOrderEntity.workOrderExtendFieldThreeName\}', 1, 'workOrder', 'workOrderExtendFieldThreeName');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('workOrderEntity.workOrderExtendFieldFourName', '工单扩展字段4', '\$\{workOrderEntity.workOrderExtendFieldFourName\}', 1, 'workOrder', 'workOrderExtendFieldFourName');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('workOrderEntity.workOrderExtendFieldFiveName', '工单扩展字段5', '\$\{workOrderEntity.workOrderExtendFieldFiveName\}', 1, 'workOrder', 'workOrderExtendFieldFiveName');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('workOrderEntity.workOrderExtendFieldSixName', '工单扩展字段6', '\$\{workOrderEntity.workOrderExtendFieldSixName\}', 1, 'workOrder', 'workOrderExtendFieldSixName');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('workOrderEntity.workOrderExtendFieldSevenName', '工单扩展字段7', '\$\{workOrderEntity.workOrderExtendFieldSevenName\}', 1, 'workOrder', 'workOrderExtendFieldSevenName');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('workOrderEntity.workOrderExtendFieldEightName', '工单扩展字段8', '\$\{workOrderEntity.workOrderExtendFieldEightName\}', 1, 'workOrder', 'workOrderExtendFieldEightName');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('workOrderEntity.workOrderExtendFieldNineName', '工单扩展字段9', '\$\{workOrderEntity.workOrderExtendFieldNineName\}', 1, 'workOrder', 'workOrderExtendFieldNineName');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('workOrderEntity.workOrderExtendFieldTenName', '工单扩展字段10', '\$\{workOrderEntity.workOrderExtendFieldTenName\}', 1, 'workOrder', 'workOrderExtendFieldTenName');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('workOrderEntity.workOrderMaterialExtendFieldOneName', '工单物料扩展字段1', '\$\{workOrderEntity.workOrderMaterialExtendFieldOneName\}', 1, 'workOrder', 'workOrderMaterialExtendFieldOneName');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('workOrderEntity.workOrderMaterialExtendFieldTwoName', '工单物料扩展字段2', '\$\{workOrderEntity.workOrderMaterialExtendFieldTwoName\}', 1, 'workOrder', 'workOrderMaterialExtendFieldTwoName');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('workOrderEntity.workOrderMaterialExtendFieldThreeName', '工单物料扩展字段3', '\$\{workOrderEntity.workOrderMaterialExtendFieldThreeName\}', 1, 'workOrder', 'workOrderMaterialExtendFieldThreeName');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('workOrderEntity.workOrderMaterialExtendFieldFourName', '工单物料扩展字段4', '\$\{workOrderEntity.workOrderMaterialExtendFieldFourName\}', 1, 'workOrder', 'workOrderMaterialExtendFieldFourName');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('workOrderEntity.workOrderMaterialExtendFieldFiveName', '工单物料扩展字段5', '\$\{workOrderEntity.workOrderMaterialExtendFieldFiveName\}', 1, 'workOrder', 'workOrderMaterialExtendFieldFiveName');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('workOrderEntity.workOrderMaterialExtendFieldSixName', '工单物料扩展字段6', '\$\{workOrderEntity.workOrderMaterialExtendFieldSixName\}', 1, 'workOrder', 'workOrderMaterialExtendFieldSixName');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('workOrderEntity.workOrderMaterialExtendFieldSevenName', '工单物料扩展字段7', '\$\{workOrderEntity.workOrderMaterialExtendFieldSevenName\}', 1, 'workOrder', 'workOrderMaterialExtendFieldSevenName');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('workOrderEntity.workOrderMaterialExtendFieldEightName', '工单物料扩展字段8', '\$\{workOrderEntity.workOrderMaterialExtendFieldEightName\}', 1, 'workOrder', 'workOrderMaterialExtendFieldEightName');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('workOrderEntity.workOrderMaterialExtendFieldNineName', '工单物料扩展字段9', '\$\{workOrderEntity.workOrderMaterialExtendFieldNineName\}', 1, 'workOrder', 'workOrderMaterialExtendFieldNineName');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('workOrderEntity.workOrderMaterialExtendFieldTenName', '工单物料扩展字段10', '\$\{workOrderEntity.workOrderMaterialExtendFieldTenName\}', 1, 'workOrder', 'workOrderMaterialExtendFieldTenName');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('workOrderEntity.investCheckResultDetail', '投产检查结果明细', '\$\{workOrderEntity.investCheckResultDetail\}', 1, 'workOrder', 'investCheckResultDetail');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('workOrderEntity.theoryHour', '单件理论工时', '\$\{workOrderEntity.theoryHour\}', 1, 'workOrder', 'theoryHour');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('workOrderEntity.produceTheoryHour', '产出理论工时', '\$\{workOrderEntity.produceTheoryHour\}', 1, 'workOrder', 'produceTheoryHour');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('workOrderEntity.planTheoryHour', '计划理论工时', '\$\{workOrderEntity.planTheoryHour\}', 1, 'workOrder', 'planTheoryHour');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('workOrderEntity.customerMaterialCode', '客户物料编码', '\$\{workOrderEntity.customerMaterialCode\}', 1, 'workOrder', 'customerMaterialCode');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('workOrderEntity.customerMaterialName', '客户物料名称', '\$\{workOrderEntity.customerMaterialName\}', 1, 'workOrder', 'customerMaterialName');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('workOrderEntity.customerSpecification', '客户物料规格', '\$\{workOrderEntity.customerSpecification\}', 1, 'workOrder', 'customerSpecification');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('workOrderEntity.remark', '备注', '\$\{workOrderEntity.remark\}', 1, 'workOrder', 'remark');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('workOrderEntity.createName', '创建人', '\$\{workOrderEntity.createName\}', 1, 'workOrder', 'createName');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('workOrderEntity.updateByName', '更新人', '\$\{workOrderEntity.updateByName\}', 1, 'workOrder', 'updateByName');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('workOrderEntity.businessTypeName', '业务类型', '\$\{workOrderEntity.businessTypeName\}', 1, 'workOrder', 'businessTypeName');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('workOrderEntity.orderTypeName', '单据类型', '\$\{workOrderEntity.orderTypeName\}', 1, 'workOrder', 'orderTypeName');

-- 生产订单字段
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('productOrderEntity.productOrderNumber', '生产订单号', '\$\{productOrderEntity.productOrderNumber\}', 1, 'productOrder', 'orderNumber');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('productOrderEntity.productOrderMaterial.pickingStateName', '领料状态', '\$\{productOrderEntity.productOrderMaterial.pickingStateName\}', 1, 'productOrder', 'pickingStateName');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('productOrderEntity.stateName', '状态', '\$\{productOrderEntity.stateName\}', 1, 'productOrder', 'state');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('productOrderEntity.approvalStatusName', '审批状态', '\$\{productOrderEntity.approvalStatusName\}', 1, 'productOrder', 'approvalStatus');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('productOrderEntity.saleOrderCode', '销售订单号', '\$\{productOrderEntity.saleOrderCode\}', 1, 'productOrder', 'saleOrderCode');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('productOrderEntity.productOrderMaterial.priority', '优先级', '\$\{productOrderEntity.productOrderMaterial.priority\}', 1, 'productOrder', 'priority');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('productOrderEntity.relatedRootOrderNum', '根生产订单', '\$\{productOrderEntity.relatedRootOrderNum\}', 1, 'productOrder', 'relatedRootOrderNum');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('productOrderEntity.relatedOrderNum', '上级生产订单', '\$\{productOrderEntity.relatedOrderNum\}', 1, 'productOrder', 'relatedOrderNum');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('productOrderEntity.productOrderMaterial.materialFields.code', '物料编码', '\$\{productOrderEntity.productOrderMaterial.materialFields.code\}', 1, 'productOrder', 'code');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('productOrderEntity.productOrderMaterial.materialFields.name', '物料名称', '\$\{productOrderEntity.productOrderMaterial.materialFields.name\}', 1, 'productOrder', 'name');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('productOrderEntity.productOrderMaterial.procedureProcessDetail', '工序进度详情', '\$\{productOrderEntity.productOrderMaterial.procedureProcessDetail\}', 1, 'productOrder', 'procedureProcessDetail');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('productOrderEntity.productOrderMaterial.procedureProcess', '工序进度', '\$\{productOrderEntity.productOrderMaterial.procedureProcess\}', 1, 'productOrder', 'procedureProcess');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('productOrderEntity.productOrderMaterial.scheduledProductionQuantity', '排产数量', '\$\{productOrderEntity.productOrderMaterial.scheduledProductionQuantity\}', 1, 'productOrder', 'scheduledProductionQuantity');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('productOrderEntity.productOrderMaterial.materialFields.skuEntity.skuName', '特征参数', '\$\{productOrderEntity.productOrderMaterial.materialFields.skuEntity.skuName\}', 1, 'productOrder', 'auxiliaryAttr');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('productOrderEntity.productOrderMaterial.planQuantity', '计划数量', '\$\{productOrderEntity.productOrderMaterial.planQuantity\}', 1, 'productOrder', 'planQuantity');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('productOrderEntity.productOrderMaterial.finishCount', '完成数量', '\$\{productOrderEntity.productOrderMaterial.finishCount\}', 1, 'productOrder', 'finishCount');
-- INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('productOrderEntity.identifierList', '单据标识', '\$\{productOrderEntity.identifierList\}', 1, 'productOrder', 'identifierList');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('productOrderEntity.productOrderMaterial.unqualifiedCount', '不合格数', '\$\{productOrderEntity.productOrderMaterial.unqualifiedCount\}', 1, 'productOrder', 'unqualifiedCount');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('productOrderEntity.productOrderMaterial.plannedBatches', '计划批数', '\$\{productOrderEntity.productOrderMaterial.plannedBatches\}', 1, 'productOrder', 'plannedBatches');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('productOrderEntity.productOrderMaterial.plansPerBatch', '每批计划数', '\$\{productOrderEntity.productOrderMaterial.plansPerBatch\}', 1, 'productOrder', 'plansPerBatch');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('productOrderEntity.productOrderMaterial.actualBatches', '实际批数', '\$\{productOrderEntity.productOrderMaterial.actualBatches\}', 1, 'productOrder', 'actualBatches');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('productOrderEntity.productOrderMaterial.passRate', '合格率', '\$\{productOrderEntity.productOrderMaterial.passRate\}', 1, 'productOrder', 'passRate');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('productOrderEntity.productOrderMaterial.progress', '完成率', '\$\{productOrderEntity.productOrderMaterial.progress\}', 1, 'productOrder', 'progress');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('productOrderEntity.createTime', '创建时间', '\$\{productOrderEntity.createTime\}', 1, 'productOrder', 'createTime');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('productOrderEntity.productOrderMaterial.planProductStartTime', '计划生产开始时间', '\$\{productOrderEntity.productOrderMaterial.planProductStartTime\}', 1, 'productOrder', 'planProductStartTime');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('productOrderEntity.productOrderMaterial.planProductEndTime', '计划生产完成时间', '\$\{productOrderEntity.productOrderMaterial.planProductEndTime\}', 1, 'productOrder', 'planProductEndTime');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('productOrderEntity.productOrderMaterial.gridName', '车间名称', '\$\{productOrderEntity.productOrderMaterial.gridName\}', 1, 'productOrder', 'gridName');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('productOrderEntity.customerCode', '客户编码', '\$\{productOrderEntity.customerCode\}', 1, 'productOrder', 'customerCode');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('productOrderEntity.customerName', '客户名称', '\$\{productOrderEntity.customerName\}', 1, 'productOrder', 'customerName');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('productOrderEntity.productOrderMaterial.orderDate', '下单日期', '\$\{productOrderEntity.productOrderMaterial.orderDate\}', 1, 'productOrder', 'orderDate');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('productOrderEntity.productOrderMaterial.salesQuantity', '销售数量', '\$\{productOrderEntity.productOrderMaterial.salesQuantity\}', 1, 'productOrder', 'salesQuantity');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('productOrderEntity.productOrderMaterial.materialFields.haveBom', 'BOM', '\$\{productOrderEntity.productOrderMaterial.materialFields.haveBom\}', 1, 'productOrder', 'haveBom');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('productOrderEntity.productOrderMaterial.materialFields.haveCraft', '工单工艺', '\$\{productOrderEntity.productOrderMaterial.materialFields.haveCraft\}', 1, 'productOrder', 'haveCraft');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('productOrderEntity.productOrderMaterial.mergedStateName', '合单状态', '\$\{productOrderEntity.productOrderMaterial.mergedStateName\}', 1, 'productOrder', 'mergedStateName');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('productOrderEntity.productOrderMaterial.mergeOrderNumber', '合单单号', '\$\{productOrderEntity.productOrderMaterial.mergeOrderNumber\}', 1, 'productOrder', 'mergeOrderNumber');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('productOrderEntity.projectDefineName', '项目名称', '\$\{productOrderEntity.projectDefineName\}', 1, 'productOrder', 'projectDefineName');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('productOrderEntity.contractName', '合同名称', '\$\{productOrderEntity.contractName\}', 1, 'productOrder', 'contractName');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('productOrderEntity.productOrderMaterial.requireGoodsDate', '要货日期', '\$\{productOrderEntity.productOrderMaterial.requireGoodsDate\}', 1, 'productOrder', 'requireGoodsDate');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('productOrderEntity.actualApproverName', '实际审批人', '\$\{productOrderEntity.actualApproverName\}', 1, 'productOrder', 'actualApproverName');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('productOrderEntity.approvalTime', '审批时间', '\$\{productOrderEntity.approvalTime\}', 1, 'productOrder', 'approvalTime');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('productOrderEntity.createName', '创建人', '\$\{productOrderEntity.createName\}', 1, 'productOrder', 'createName');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('productOrderEntity.updateName', '更新人', '\$\{productOrderEntity.updateName\}', 1, 'productOrder', 'updateName');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('productOrderEntity.updateTime', '更新时间', '\$\{productOrderEntity.updateTime\}', 1, 'productOrder', 'updateTime');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('productOrderEntity.importTime', '导入时间', '\$\{productOrderEntity.importTime\}', 1, 'productOrder', 'importTime');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('productOrderEntity.yield', '成品率', '\$\{productOrderEntity.yield\}', 1, 'productOrder', 'yield');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('productOrderEntity.productOrderMaterial.actualProductStartTime', '实际开始时间', '\$\{productOrderEntity.productOrderMaterial.actualProductStartTime\}', 1, 'productOrder', 'actualProductStartTime');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('productOrderEntity.productOrderMaterial.actualProductEndTime', '实际完成时间', '\$\{productOrderEntity.productOrderMaterial.actualProductEndTime\}', 1, 'productOrder', 'actualProductEndTime');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('productOrderEntity.productOrderMaterial.schedulingStatusName', '排产状态', '\$\{productOrderEntity.productOrderMaterial.schedulingStatusName\}', 1, 'productOrder', 'schedulingStatusName');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('productOrderEntity.remark', '备注', '\$\{productOrderEntity.remark\}', 1, 'productOrder', 'orderRemark');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('productOrderEntity.productOrderMaterial.remark', '行备注', '\$\{productOrderEntity.productOrderMaterial.remark\}', 1, 'productOrder', 'materialRemark');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('productOrderEntity.productOrderMaterial.processStatusName', '生产状态', '\$\{productOrderEntity.productOrderMaterial.processStatusName\}', 1, 'productOrder', 'processStatusName');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('productOrderEntity.productOrderExtendFieldOneName', '生产订单扩展字段1', '\$\{productOrderEntity.productOrderExtendFieldOneName\}', 1, 'productOrder', 'productOrderExtendFieldOneName');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('productOrderEntity.productOrderExtendFieldTwoName', '生产订单扩展字段2', '\$\{productOrderEntity.productOrderExtendFieldTwoName\}', 1, 'productOrder', 'productOrderExtendFieldTwoName');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('productOrderEntity.productOrderExtendFieldThreeName', '生产订单扩展字段3', '\$\{productOrderEntity.productOrderExtendFieldThreeName\}', 1, 'productOrder', 'productOrderExtendFieldThreeName');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('productOrderEntity.productOrderExtendFieldFourName', '生产订单扩展字段4', '\$\{productOrderEntity.productOrderExtendFieldFourName\}', 1, 'productOrder', 'productOrderExtendFieldFourName');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('productOrderEntity.productOrderExtendFieldFiveName', '生产订单扩展字段5', '\$\{productOrderEntity.productOrderExtendFieldFiveName\}', 1, 'productOrder', 'productOrderExtendFieldFiveName');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('productOrderEntity.productOrderExtendFieldSixName', '生产订单扩展字段6', '\$\{productOrderEntity.productOrderExtendFieldSixName\}', 1, 'productOrder', 'productOrderExtendFieldSixName');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('productOrderEntity.productOrderExtendFieldSevenName', '生产订单扩展字段7', '\$\{productOrderEntity.productOrderExtendFieldSevenName\}', 1, 'productOrder', 'productOrderExtendFieldSevenName');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('productOrderEntity.productOrderExtendFieldEightName', '生产订单扩展字段8', '\$\{productOrderEntity.productOrderExtendFieldEightName\}', 1, 'productOrder', 'productOrderExtendFieldEightName');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('productOrderEntity.productOrderExtendFieldNineName', '生产订单扩展字段9', '\$\{productOrderEntity.productOrderExtendFieldNineName\}', 1, 'productOrder', 'productOrderExtendFieldNineName');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('productOrderEntity.productOrderExtendFieldTenName', '生产订单扩展字段10', '\$\{productOrderEntity.productOrderExtendFieldTenName\}', 1, 'productOrder', 'productOrderExtendFieldTenName');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('productOrderEntity.productOrderMaterial.productOrderMaterialExtendFieldOneName', '生产订单物料扩展字段1', '\$\{productOrderEntity.productOrderMaterial.productOrderMaterialExtendFieldOneName\}', 1, 'productOrder', 'productOrderMaterialExtendFieldOneName');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('productOrderEntity.productOrderMaterial.productOrderMaterialExtendFieldTwoName', '生产订单物料扩展字段2', '\$\{productOrderEntity.productOrderMaterial.productOrderMaterialExtendFieldTwoName\}', 1, 'productOrder', 'productOrderMaterialExtendFieldTwoName');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('productOrderEntity.productOrderMaterial.productOrderMaterialExtendFieldThreeName', '生产订单物料扩展字段3', '\$\{productOrderEntity.productOrderMaterial.productOrderMaterialExtendFieldThreeName\}', 1, 'productOrder', 'productOrderMaterialExtendFieldThreeName');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('productOrderEntity.productOrderMaterial.productOrderMaterialExtendFieldFourName', '生产订单物料扩展字段4', '\$\{productOrderEntity.productOrderMaterial.productOrderMaterialExtendFieldFourName\}', 1, 'productOrder', 'productOrderMaterialExtendFieldFourName');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('productOrderEntity.productOrderMaterial.productOrderMaterialExtendFieldFiveName', '生产订单物料扩展字段5', '\$\{productOrderEntity.productOrderMaterial.productOrderMaterialExtendFieldFiveName\}', 1, 'productOrder', 'productOrderMaterialExtendFieldFiveName');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('productOrderEntity.productOrderMaterial.productOrderMaterialExtendFieldSixName', '生产订单物料扩展字段6', '\$\{productOrderEntity.productOrderMaterial.productOrderMaterialExtendFieldSixName\}', 1, 'productOrder', 'productOrderMaterialExtendFieldSixName');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('productOrderEntity.productOrderMaterial.productOrderMaterialExtendFieldSevenName', '生产订单物料扩展字段7', '\$\{productOrderEntity.productOrderMaterial.productOrderMaterialExtendFieldSevenName\}', 1, 'productOrder', 'productOrderMaterialExtendFieldSevenName');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('productOrderEntity.productOrderMaterial.productOrderMaterialExtendFieldEightName', '生产订单物料扩展字段8', '\$\{productOrderEntity.productOrderMaterial.productOrderMaterialExtendFieldEightName\}', 1, 'productOrder', 'productOrderMaterialExtendFieldEightName');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('productOrderEntity.productOrderMaterial.productOrderMaterialExtendFieldNineName', '生产订单物料扩展字段9', '\$\{productOrderEntity.productOrderMaterial.productOrderMaterialExtendFieldNineName\}', 1, 'productOrder', 'productOrderMaterialExtendFieldNineName');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('productOrderEntity.productOrderMaterial.productOrderMaterialExtendFieldTenName', '生产订单物料扩展字段10', '\$\{productOrderEntity.productOrderMaterial.productOrderMaterialExtendFieldTenName\}', 1, 'productOrder', 'productOrderMaterialExtendFieldTenName');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('productOrderEntity.productOrderMaterial.planTheoryHour', '计划理论工时', '\$\{productOrderEntity.productOrderMaterial.planTheoryHour\}', 1, 'productOrder', 'planTheoryHour');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('productOrderEntity.productOrderMaterial.customerMaterialCode', '客户物料编码', '\$\{productOrderEntity.productOrderMaterial.customerMaterialCode\}', 1, 'productOrder', 'customerMaterialCode');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('productOrderEntity.productOrderMaterial.customerMaterialName', '客户物料名称', '\$\{productOrderEntity.productOrderMaterial.customerMaterialName\}', 1, 'productOrder', 'customerMaterialName');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('productOrderEntity.productOrderMaterial.customerSpecification', '客户物料规格', '\$\{productOrderEntity.productOrderMaterial.customerSpecification\}', 1, 'productOrder', 'customerSpecification');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('productOrderEntity.orderTypeName', '单据类型', '\$\{productOrderEntity.orderTypeName\}', 1, 'productOrder', 'orderTypeName');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('productOrderEntity.typeName', '业务类型', '\$\{productOrderEntity.typeName\}', 1, 'productOrder', 'typeName');

-- 采购订单
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('purchaseEntity.purchaseCode', '采购订单号', '\$\{purchaseEntity.purchaseCode\}', 1, 'purchaseOrder', 'purchaseCode');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('purchaseEntity.stateName', '状态', '\$\{purchaseEntity.stateName\}', 1, 'purchaseOrder', 'state');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('purchaseEntity.purchaseTypeName', '单据类型', '\$\{purchaseEntity.purchaseTypeName\}', 1, 'purchaseOrder', 'purchaseType');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('purchaseEntity.relatedTypeName', '关联单据类型', '\$\{purchaseEntity.relatedTypeName\}', 1, 'purchaseOrder', 'relatedTypeName');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('purchaseEntity.requestCode', '关联单据编号', '\$\{purchaseEntity.requestCode\}', 1, 'purchaseOrder', 'requestCode');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('purchaseEntity.supplierCode', '供应商编码', '\$\{purchaseEntity.supplierCode\}', 1, 'purchaseOrder', 'supplierCode');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('purchaseEntity.supplierName', '供应商名称', '\$\{purchaseEntity.supplierName\}', 1, 'purchaseOrder', 'supplierName');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('purchaseEntity.supplierPhone', '供应商联系方式', '\$\{purchaseEntity.supplierPhone\}', 1, 'purchaseOrder', 'supplierPhone');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('purchaseEntity.supplierAddr', '供应商地址', '\$\{purchaseEntity.supplierAddr\}', 1, 'purchaseOrder', 'supplierAddr');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('purchaseEntity.projectName', '项目备注', '\$\{purchaseEntity.projectName\}', 1, 'purchaseOrder', 'projectName');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('purchaseEntity.planTime', '计划采购日期', '\$\{purchaseEntity.planTime\}', 1, 'purchaseOrder', 'planTime');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('purchaseEntity.buyerNickname', '采购员', '\$\{purchaseEntity.buyerNickname\}', 1, 'purchaseOrder', 'buyerNickname');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('purchaseEntity.buyerPhone', '采购员联系方式', '\$\{purchaseEntity.buyerPhone\}', 1, 'purchaseOrder', 'buyerPhone');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('purchaseEntity.approverName', '审批人', '\$\{purchaseEntity.approverName\}', 1, 'purchaseOrder', 'approverName');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('purchaseEntity.approvalStatusName', '审批状态', '\$\{purchaseEntity.approvalStatusName\}', 1, 'purchaseOrder', 'approvalStatus');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('purchaseEntity.approvalTime', '审批时间', '\$\{purchaseEntity.approvalTime\}', 1, 'purchaseOrder', 'approvalTime');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('purchaseEntity.actualApproverName', '实际审批人', '\$\{purchaseEntity.actualApproverName\}', 1, 'purchaseOrder', 'actualApproverName');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('purchaseEntity.remark', '备注', '\$\{purchaseEntity.remark\}', 1, 'purchaseOrder', 'remark');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('purchaseEntity.createTime', '创建时间', '\$\{purchaseEntity.createTime\}', 1, 'purchaseOrder', 'createTime');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('purchaseEntity.purchaseExtendFieldOne', '采购订单扩展字段1', '\$\{purchaseEntity.purchaseExtendFieldOne\}', 1, 'purchaseOrder', 'purchaseExtendFieldOne');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('purchaseEntity.purchaseExtendFieldTwo', '采购订单扩展字段2', '\$\{purchaseEntity.purchaseExtendFieldTwo\}', 1, 'purchaseOrder', 'purchaseExtendFieldTwo');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('purchaseEntity.purchaseExtendFieldThree', '采购订单扩展字段3', '\$\{purchaseEntity.purchaseExtendFieldThree\}', 1, 'purchaseOrder', 'purchaseExtendFieldThree');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('purchaseEntity.purchaseExtendFieldFour', '采购订单扩展字段4', '\$\{purchaseEntity.purchaseExtendFieldFour\}', 1, 'purchaseOrder', 'purchaseExtendFieldFour');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('purchaseEntity.purchaseExtendFieldFive', '采购订单扩展字段5', '\$\{purchaseEntity.purchaseExtendFieldFive\}', 1, 'purchaseOrder', 'purchaseExtendFieldFive');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('purchaseEntity.purchaseExtendFieldSix', '采购订单扩展字段6', '\$\{purchaseEntity.purchaseExtendFieldSix\}', 1, 'purchaseOrder', 'purchaseExtendFieldSix');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('purchaseEntity.purchaseExtendFieldSeven', '采购订单扩展字段7', '\$\{purchaseEntity.purchaseExtendFieldSeven\}', 1, 'purchaseOrder', 'purchaseExtendFieldSeven');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('purchaseEntity.purchaseExtendFieldEight', '采购订单扩展字段8', '\$\{purchaseEntity.purchaseExtendFieldEight\}', 1, 'purchaseOrder', 'purchaseExtendFieldEight');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('purchaseEntity.purchaseExtendFieldNine', '采购订单扩展字段9', '\$\{purchaseEntity.purchaseExtendFieldNine\}', 1, 'purchaseOrder', 'purchaseExtendFieldNine');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('purchaseEntity.purchaseExtendFieldTen', '采购订单扩展字段10', '\$\{purchaseEntity.purchaseExtendFieldTen\}', 1, 'purchaseOrder', 'purchaseExtendFieldTen');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('purchaseEntity.businessTypeName', '业务类型', '\$\{purchaseEntity.businessTypeName\}', 1, 'purchaseOrder', 'businessTypeName');

-- ================================================此脚本专为标签需求设定，如需写脚本，请移步到3.15.1.1=======================================================
-- ================================================此脚本专为标签需求设定，如需写脚本，请移步到3.15.1.1=======================================================
-- ================================================此脚本专为标签需求设定，如需写脚本，请移步到3.15.1.1=======================================================
-- ================================================此脚本专为标签需求设定，如需写脚本，请移步到3.15.1.1=======================================================
-- ================================================此脚本专为标签需求设定，如需写脚本，请移步到3.15.1.1=======================================================
