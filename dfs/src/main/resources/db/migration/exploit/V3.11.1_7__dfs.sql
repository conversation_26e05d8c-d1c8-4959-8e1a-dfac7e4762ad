-- 华东电工 设备台账扩展字段
call proc_add_column(
        'dfs_device',
        'device_extend_one',
        'ALTER TABLE `dfs_device` ADD COLUMN `device_extend_one` varchar(512) DEFAULT NULL COMMENT ''扩展字段1''');
call proc_add_column(
        'dfs_device',
        'device_extend_two',
        'ALTER TABLE `dfs_device` ADD COLUMN `device_extend_two` varchar(512) DEFAULT NULL COMMENT ''扩展字段2''');
call proc_add_column(
        'dfs_device',
        'device_extend_three',
        'ALTER TABLE `dfs_device` ADD COLUMN `device_extend_three` varchar(512) DEFAULT NULL COMMENT ''扩展字段3''');
call proc_add_column(
        'dfs_device',
        'device_extend_four',
        'ALTER TABLE `dfs_device` ADD COLUMN `device_extend_four` varchar(512) DEFAULT NULL COMMENT ''扩展字段4''');
call proc_add_column(
        'dfs_device',
        'device_extend_five',
        'ALTER TABLE `dfs_device` ADD COLUMN `device_extend_five` varchar(512) DEFAULT NULL COMMENT ''扩展字段5''');
call proc_add_column(
        'dfs_device',
        'device_extend_six',
        'ALTER TABLE `dfs_device` ADD COLUMN `device_extend_six` varchar(512) DEFAULT NULL COMMENT ''扩展字段6''');
call proc_add_column(
        'dfs_device',
        'device_extend_seven',
        'ALTER TABLE `dfs_device` ADD COLUMN `device_extend_seven` varchar(512) DEFAULT NULL COMMENT ''扩展字段7''');
call proc_add_column(
        'dfs_device',
        'device_extend_eight',
        'ALTER TABLE `dfs_device` ADD COLUMN `device_extend_eight` varchar(512) DEFAULT NULL COMMENT ''扩展字段8''');
call proc_add_column(
        'dfs_device',
        'device_extend_nine',
        'ALTER TABLE `dfs_device` ADD COLUMN `device_extend_nine` varchar(512) DEFAULT NULL COMMENT ''扩展字段9''');
call proc_add_column(
        'dfs_device',
        'device_extend_ten',
        'ALTER TABLE `dfs_device` ADD COLUMN `device_extend_ten` varchar(512) DEFAULT NULL COMMENT ''扩展字段10''');