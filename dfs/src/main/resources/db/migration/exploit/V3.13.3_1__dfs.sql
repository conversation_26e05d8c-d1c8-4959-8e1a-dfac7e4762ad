-- 版本变更记录表
CREATE TABLE IF NOT EXISTS `dfs_version_change_record`
(
    `id`              int(11) NOT NULL AUTO_INCREMENT,
    `relate_type`     varchar(50) NOT NULL COMMENT '模型类型枚举：bom, craft',
    `relate_id`       int(11) NOT NULL COMMENT '关联的id',
    `first_id`        int(11) DEFAULT NULL COMMENT '最初版的id',
    `link_id`         text COMMENT '历史id链: {id_ver_1}..._{id_ver_n-1}_{id_ver_n}',
    `version_current` varchar(255)         DEFAULT NULL COMMENT '当前版本号',
    `version_last`    varchar(255)         DEFAULT NULL COMMENT '上一个版本号',
    `editor`          varchar(255)         DEFAULT NULL COMMENT '操作者username',
    `change_time`     datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '变更时间',
    `change_count`    int(11) NOT NULL COMMENT '变更次数: 累加',
    `change_log`      text COMMENT '变更日志',
    PRIMARY KEY (`id`) USING BTREE,
    KEY               `idx_relate` (`relate_type`,`relate_id`),
    KEY               `idx_first` (`relate_type`,`first_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='版本变更记录';

-- 生产订单流程卡打印标识配置
INSERT INTO `dfs_business_config`( `code`, `name`, `full_path_code`, `parent_full_path_code`, `description`, `create_by`, `update_by`, `create_time`, `update_time`)
VALUES ('printFlowCard', '已打印流程卡', 'production.productOrderListConfig.stateIdentifier.printFlowCard', 'production.productOrderListConfig.stateIdentifier', '生产订单打印流程卡次数', 'yelinkoncall', 'yelinkoncall', '2023-07-06 04:28:17', '2023-07-06 04:28:17');

INSERT INTO  `dfs_business_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`)
VALUES ( 'enable', '是否启用(单选)', 'production.productOrderListConfig.stateIdentifier.printFlowCard.enable', 'production.productOrderListConfig.stateIdentifier.printFlowCard', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"是\"},{\"value\":false,\"label\":\"否\"}]', 'true');

INSERT INTO `dfs_business_config_value`( `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`)
VALUES ('colorIdentifier', '颜色标识', 'production.productOrderListConfig.stateIdentifier.printFlowCard.colorIdentifier', 'production.productOrderListConfig.stateIdentifier.printFlowCard', 'color', 'table', NULL, NULL, NULL, NULL, NULL, '"#4DFF00"');

INSERT INTO `dfs_business_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`)
VALUES ('textValue', '文本内容(4个汉字8个字母符之内)', 'production.productOrderListConfig.stateIdentifier.printFlowCard.textValue', 'production.productOrderListConfig.stateIdentifier.printFlowCard', 'textarea', 'input', NULL, NULL, NULL, NULL, NULL,  '"已打印流程卡"');

-- -- 生产工单用料清单主表新增关联bomId字段
-- call proc_add_column(
--         'dfs_work_order_material_list',
--         'bom_id',
--         'ALTER TABLE `dfs_work_order_material_list` ADD COLUMN `bom_id` int(11) NULL COMMENT ''关联的bomId'';');
-- call proc_add_column(
--         'dfs_work_order_material_list',
--         'bom_version_revision',
--         'ALTER TABLE `dfs_work_order_material_list` ADD COLUMN `bom_version_revision` int(11) NULL COMMENT ''关联的bom的即时修订次数'';');

call proc_add_column(
        'dfs_delivery_application',
        'customer_code',
        'ALTER TABLE `dfs_delivery_application` ADD COLUMN `customer_code`  varchar(255) NULL COMMENT ''客户编码'' AFTER `state`;');
call proc_add_column(
        'dfs_delivery_application',
        'customer_name',
        'ALTER TABLE `dfs_delivery_application` ADD COLUMN `customer_name`  varchar(255) NULL COMMENT ''客户名称'' AFTER `customer_code`;');

call proc_add_column(
        'dfs_delivery_application_material',
        'out_stock_quantity',
        'ALTER TABLE `dfs_delivery_application_material` ADD COLUMN `out_stock_quantity`  double(20,6) NULL DEFAULT 0 COMMENT ''已出库数'' AFTER `sales_quantity`;');
call proc_add_column(
        'dfs_delivery_application_material',
        'return_in_quantity',
        'ALTER TABLE `dfs_delivery_application_material` ADD COLUMN `return_in_quantity`  double(20,6) NULL DEFAULT 0 COMMENT ''退货入库数'' AFTER `out_stock_quantity`;');
call proc_add_column(
        'dfs_delivery_application_material',
        'customer_material_code',
        'ALTER TABLE `dfs_delivery_application_material` ADD COLUMN `customer_material_code`  varchar(255) NULL COMMENT ''客户物料编码'' AFTER `return_in_quantity`;');
call proc_add_column(
        'dfs_delivery_application_material',
        'customer_material_name',
        'ALTER TABLE `dfs_delivery_application_material` ADD COLUMN `customer_material_name`  varchar(255) NULL COMMENT ''客户物料名称'' AFTER `customer_material_code`;');
call proc_add_column(
        'dfs_delivery_application_material',
        'require_goods_date',
        'ALTER TABLE `dfs_delivery_application_material` ADD COLUMN `require_goods_date`  datetime NULL COMMENT ''要货日期'' AFTER `customer_material_name`;');



-- 指标： 生产工单每日
call `dfs_metrics`.`proc_add_column`(
        'dfs_metrics_work_order_daily',
        'flow_code_report_quantity',
        'ALTER TABLE `dfs_metrics_work_order_daily` ADD COLUMN `flow_code_report_quantity` double(11, 2) NULL DEFAULT NULL COMMENT ''流水码标记为产出的数量：用于计算直通率''');


-- DML
-- 工资配置批量编辑、批量删除
INSERT INTO dfs.sys_permissions (id, name, `path`, description, create_by, create_time, update_by, update_time, status, `method`, parent_id, `type`, is_web, is_back_stage, parent_path, is_enable, sort, service_name, is_sys_data) VALUES('11405070', '批量编辑', 'valuation.config:batchEdit', NULL, NULL, NULL, NULL, '2024-12-09 15:26:02', 'enable', 'POST', '11405', 2, 1, 1, '/production-config/valuation-config', 1, NULL, '', 1);
INSERT INTO dfs.sys_permissions (id, name, `path`, description, create_by, create_time, update_by, update_time, status, `method`, parent_id, `type`, is_web, is_back_stage, parent_path, is_enable, sort, service_name, is_sys_data) VALUES('11405080', '批量删除', 'valuation.config:batchDelete', NULL, NULL, NULL, NULL, '2024-12-09 15:26:02', 'enable', 'POST', '11405', 2, 1, 1, '/production-config/valuation-config', 1, NULL, '', 1);
call init_new_role_permission('11405070');
call init_new_role_permission('11405080');

-- 处理工单中工艺编码不为空，但是工艺id为空的脏数据
UPDATE `dfs_work_order` w, `dfs_craft` cr
SET w.`craft_id` = cr.`craft_id`
WHERE w.`craft_code` = cr.`craft_code`
AND w.`craft_id` IS NULL OR w.`craft_id` = '';

UPDATE `dfs_work_order` w, `dfs_craft` cr
SET w.`craft_code` = cr.`craft_code`
WHERE w.`craft_id` = cr.`craft_id`
  AND w.`craft_code` IS NULL OR w.`craft_code` = '';

-- 销售订单的工艺信息重命名为 物料工艺 ， 生产订单、生产工单的工艺信息重命名为 工单工艺
UPDATE `dfs_form_field_config` SET `field_name` = '物料工艺'
WHERE `full_path_code` in ('saleOrder.list.material', 'saleOrder.detail', 'saleOrder.editByCreate','saleOrder.editByRelease','saleOrder.editByFinish','saleOrder.editByClosed','saleOrder.editByCancel')
  AND `field_code` = 'haveCraft' AND `type_code` = 'sysField';

UPDATE `dfs_form_field_config` SET `field_name` = '工单工艺'
WHERE `full_path_code` in ('productOrder.list', 'productOrder.detail', 'workOrder.list','workOrder.detail', 'productOrder.editByCreate','productOrder.editByRelease','productOrder.editByFinish','productOrder.editByClosed','productOrder.editByCancel')
  AND `field_code` = 'haveCraft' AND `type_code` = 'sysField';

-- 版本变更记录针对BOM、工艺的历史数据，修订次数统一设置为1
INSERT INTO `dfs_version_change_record` (`relate_type`, `relate_id`, `first_id`, `version_current`, `editor`, `change_time`, `change_count`, `change_log`)
SELECT 'BOM', `id`, `id`, `version`, `create_by`, `create_time`, 1, 'BOM - 新增'
FROM `dfs_bom`;

UPDATE `dfs_craft` SET `create_time` = CURRENT_DATE WHERE `create_time` IS NULL;
UPDATE `dfs_craft` SET `create_by` = 'admin' WHERE `create_time` IS NULL;

INSERT INTO `dfs_version_change_record` (`relate_type`, `relate_id`, `first_id`, `version_current`, `editor`, `change_time`, `change_count`, `change_log`)
SELECT 'CRAFT', `craft_id`, `craft_id`, `craft_version`, `create_by`, `create_time`, 1, '工艺 - 新增'
FROM `dfs_craft`;

-- 新增软件参数配置
INSERT INTO `dfs_business_config`(`code`, `name`, `full_path_code`, `parent_full_path_code`, `description`, `create_by`, `update_by`, `create_time`, `update_time`, `service_name`) VALUES ('versionMng', '版本管理', 'design.bomConfig.versionMng', 'design.bomConfig', NULL, 'yelinkoncall', 'yelinkoncall', '2024-05-09 15:50:34', '2024-05-09 15:50:34', '');
INSERT INTO `dfs_business_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES ('enableReleaseCount', '同物料允许生效个数', 'design.bomConfig.versionMng.enableReleaseCount', 'design.bomConfig.versionMng', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":\"one\",\"label\":\"仅一个\"},{\"value\":\"more\",\"label\":\"多个\"}]', '\"one\"', NULL);


