-- 下推配置菜单不是仓库的单据错误归到到了wms，现坐变更处理
update `dfs_order_push_down_config` set service_name = null where `full_path_code` in ('saleOrder.pushDownConfig.saleReturnOrder', 'purchase.purchaseReceiptPushDownConfig.returnOrder');
-- 刷新下推配置的默认值
UPDATE `dfs_order_push_down_config_value_dict` SET `value` = '\"multiLevelBomNotFilter\"' WHERE `value_full_path_code` = 'saleOrder.pushDownConfig.subcontractOrder.bom.bomSplitTypes';
UPDATE `dfs_order_push_down_config_value_dict` SET `value` = '\"multiLevelBomNotFilter\"' WHERE `value_full_path_code` = 'production.productOrderPushDownConfig.subcontractOrder.bom.bomSplitTypes';
UPDATE `dfs_order_push_down_config_value_dict` SET `value` = '\"firstLevelBomNotFilter\"' WHERE `value_full_path_code` = 'saleOrder.pushDownConfig.productOrder.bomPush.bomSplitTypes';
UPDATE `dfs_order_push_down_config_value_dict` SET `value` = '\"firstLevelBomNotFilter\"' WHERE `value_full_path_code` = 'saleOrder.pushDownConfig.workOrder.craftPush.bomSplitTypes';
UPDATE `dfs_order_push_down_config_value_dict` SET `value` = '\"multiLevelBomNotFilter\"' WHERE `value_full_path_code` = 'production.workOrderPushDownConfig.materialList.bomPush.bomSplitType';
UPDATE `dfs_order_push_down_config_value_dict` SET `value` = '\"multiLevelBomNotFilter\"' WHERE `value_full_path_code` = 'production.workOrderPushDownConfig.materialList.bomPush.bomSplitType';
UPDATE `dfs_order_push_down_config_value_dict` SET `value` = '\"firstLevelBomNotFilter\"' WHERE `value_full_path_code` = 'saleOrder.pushDownConfig.subcontractOrder.craftRouteBatch.bomSplitTypes';
UPDATE `dfs_order_push_down_config_value_dict` SET `value` = '\"byMultiLevelBom\"' WHERE `value_full_path_code` = 'saleOrder.pushDownConfig.purchaseRequest.pushBatch.bomSplitType';
UPDATE `dfs_order_push_down_config_value_dict` SET `value` = 'false' WHERE `value_full_path_code` = 'saleOrder.pushDownConfig.purchaseOrder.pushBatch.mergeSampleSupplier';
UPDATE `dfs_order_push_down_config_value_dict` SET `value` = '[]' WHERE `value_full_path_code` = 'production.productOrderPushDownConfig.productMaterialsList.pushBatch.filterMaterialSorts';
UPDATE `dfs_order_push_down_config_value_dict` SET `value` = '[]' WHERE `value_full_path_code` = 'production.workOrderPushDownConfig.materialList.bomPush.filterMaterialSorts';

UPDATE `dfs_order_push_down_config_value` SET `value` = '\"multiLevelBomNotFilter\"' WHERE `value_full_path_code` = 'saleOrder.pushDownConfig.subcontractOrder.bom.bomSplitTypes';
UPDATE `dfs_order_push_down_config_value` SET `value` = '\"multiLevelBomNotFilter\"' WHERE `value_full_path_code` = 'production.productOrderPushDownConfig.subcontractOrder.bom.bomSplitTypes';
UPDATE `dfs_order_push_down_config_value` SET `value` = '\"firstLevelBomNotFilter\"' WHERE `value_full_path_code` = 'saleOrder.pushDownConfig.productOrder.bomPush.bomSplitTypes';
UPDATE `dfs_order_push_down_config_value` SET `value` = '\"firstLevelBomNotFilter\"' WHERE `value_full_path_code` = 'saleOrder.pushDownConfig.workOrder.craftPush.bomSplitTypes';
UPDATE `dfs_order_push_down_config_value` SET `value` = '\"multiLevelBomNotFilter\"' WHERE `value_full_path_code` = 'production.workOrderPushDownConfig.materialList.bomPush.bomSplitType';
UPDATE `dfs_order_push_down_config_value` SET `value` = '\"multiLevelBomNotFilter\"' WHERE `value_full_path_code` = 'production.workOrderPushDownConfig.materialList.bomPush.bomSplitType';
UPDATE `dfs_order_push_down_config_value` SET `value` = '\"firstLevelBomNotFilter\"' WHERE `value_full_path_code` = 'saleOrder.pushDownConfig.subcontractOrder.craftRouteBatch.bomSplitTypes';
UPDATE `dfs_order_push_down_config_value` SET `value` = '\"byMultiLevelBom\"' WHERE `value_full_path_code` = 'saleOrder.pushDownConfig.purchaseRequest.pushBatch.bomSplitType';
UPDATE `dfs_order_push_down_config_value` SET `value` = 'false' WHERE `value_full_path_code` = 'saleOrder.pushDownConfig.purchaseOrder.pushBatch.mergeSampleSupplier';
UPDATE `dfs_order_push_down_config_value` SET `value` = '[]' WHERE `value_full_path_code` = 'production.productOrderPushDownConfig.productMaterialsList.pushBatch.filterMaterialSorts';
UPDATE `dfs_order_push_down_config_value` SET `value` = '[]' WHERE `value_full_path_code` = 'production.workOrderPushDownConfig.materialList.bomPush.filterMaterialSorts';
