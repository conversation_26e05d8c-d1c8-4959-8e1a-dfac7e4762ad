-- ================================================此脚本专为表单需求设定，如需写脚本，请移步到********=======================================================
-- ================================================此脚本专为表单需求设定，如需写脚本，请移步到********=======================================================
-- ================================================此脚本专为表单需求设定，如需写脚本，请移步到********=======================================================
-- ================================================此脚本专为表单需求设定，如需写脚本，请移步到********=======================================================
-- ================================================此脚本专为表单需求设定，如需写脚本，请移步到********=======================================================

-- 生产工单报工记录路由
INSERT INTO `dfs_form_config`(`id`, `type`, `is_name_tree`, `code`, `name`, `full_path_code`, `parent_full_path_code`, `description`, `create_by`, `update_by`, `create_time`, `update_time`) VALUES (NULL, 'web', NULL, 'workOrderReport', '生产工单报工记录', 'workOrderReport', '', NULL, 'admin', 'admin', NOW(), NOW());
INSERT INTO `dfs_form_config`(`id`, `type`, `is_name_tree`, `code`, `name`, `full_path_code`, `parent_full_path_code`, `description`, `create_by`, `update_by`, `create_time`, `update_time`) VALUES (NULL, 'web', NULL, 'workOrderReportList', '生产工单报工记录列表', 'workOrderReport.list', 'workOrderReport', NULL, 'admin', 'admin', NOW(), NOW());
INSERT INTO `dfs_form_config`(`id`, `type`, `is_name_tree`, `code`, `name`, `full_path_code`, `parent_full_path_code`, `description`, `create_by`, `update_by`, `create_time`, `update_time`) VALUES (NULL, 'web', NULL, 'workOrderReportDetail', '生产工单报工记录详情', 'workOrderReport.detail', 'workOrderReport', NULL, 'admin', 'admin', NOW(), NOW());

-- 生产工单报工记录相关字段配置
call proc_add_form_field("workOrderReport.list", "workOrderNumber", "生产工单号");
call proc_add_form_field("workOrderReport.list", "state", "状态");
call proc_add_form_field("workOrderReport.list", "orderReportName", "报工类型");
call proc_add_form_field("workOrderReport.list", "processAssemblyList", "工装");
call proc_add_form_field("workOrderReport.list", "customerName", "客户名称");
call proc_add_form_field("workOrderReport.list", "materialCode", "产品编码");
call proc_add_form_field("workOrderReport.list", "materialName", "产品名称");
call proc_add_form_field("workOrderReport.list", "planQuantity", "计划数量");
call proc_add_form_field("workOrderReport.list", "lossQuantity", "损耗数量");
call proc_add_form_field("workOrderReport.list", "standardScaleFactor", "标准单重");
call proc_add_form_field("workOrderReport.list", "scaleFactor", "单重");
call proc_add_form_field("workOrderReport.list", "totalScale", "总重");
call proc_add_form_field("workOrderReport.list", "productStandard", "产品规格");
call proc_add_form_field("workOrderReport.list", "lineModelName", "制造单元类型");
call proc_add_form_field("workOrderReport.list", "lineName", "制造单元");
call proc_add_form_field("workOrderReport.list", "teamName", "班组");
call proc_add_form_field("workOrderReport.list", "counterPerReported", "计数器参考值");
call proc_add_form_field("workOrderReport.list", "counterReported", "计数器累计参考值");
call proc_add_form_field("workOrderReport.list", "reportTime", "报工开始时间");
call proc_add_form_field("workOrderReport.list", "reportEndTime", "报工结束时间");
call proc_add_form_field("workOrderReport.list", "deviceName", "设备");
call proc_add_form_field("workOrderReport.list", "workCenterTypeName", "生产基本单元类型");
call proc_add_form_field("workOrderReport.list", "workCenterName", "工作中心");
call proc_add_form_field("workOrderReport.list", "procedureName", "工序");
call proc_add_form_field("workOrderReport.list", "procedureAlias", "工序别名");
call proc_add_form_field("workOrderReport.list", "resourceTypeName", "关联资源类型");
call proc_add_form_field("workOrderReport.list", "startDate", "计划开始时间");
call proc_add_form_field("workOrderReport.list", "endDate", "计划完成时间");
call proc_add_form_field("workOrderReport.list", "actualStartDate", "实际开始时间");
call proc_add_form_field("workOrderReport.list", "actualEndDate", "实际完成时间");
call proc_add_form_field("workOrderReport.list", "qualityLevel", "质量等级");
call proc_add_form_field("workOrderReport.list", "isIncludeDefect", "完成数包含不良");
call proc_add_form_field("workOrderReport.list", "inputTotal", "投入数量");
call proc_add_form_field("workOrderReport.list", "finishCount", "完成数量");
call proc_add_form_field("workOrderReport.list", "unqualified", "不良数量");
call proc_add_form_field("workOrderReport.list", "plannedWorkingHours", "计划工时");
call proc_add_form_field("workOrderReport.list", "actualWorkingHours", "生产时长");
call proc_add_form_field("workOrderReport.list", "theoryHour", "单件理论工时");
call proc_add_form_field("workOrderReport.list", "produceTheoryHour", "产出理论工时");
call proc_add_form_field("workOrderReport.list", "circulationDuration", "流转时长");
call proc_add_form_field("workOrderReport.list", "orderNumber", "生产订单号");
call proc_add_form_field("workOrderReport.list", "saleOrderNumber", "销售订单号");
call proc_add_form_field("workOrderReport.list", "type", "上报方式");
call proc_add_form_field("workOrderReport.list", "batch", "批次");
call proc_add_form_field("workOrderReport.list", "batchRemark", "批次备注");
call proc_add_form_field("workOrderReport.list", "vehicleCode", "载具号");
call proc_add_form_field("workOrderReport.list", "shiftType", "班次");
call proc_add_form_field("workOrderReport.list", "finishCountTwo", "上报完成数");
call proc_add_form_field("workOrderReport.list", "crewSize", "人员数量");
call proc_add_form_field("workOrderReport.list", "unqualifiedTwo", "上报不良数");
call proc_add_form_field("workOrderReport.list", "defectDesc", "不良描述");
call proc_add_form_field("workOrderReport.list", "effectiveHours", "上报工时");
call proc_add_form_field("workOrderReport.list", "reportDate", "生产日期");
call proc_add_form_field("workOrderReport.list", "operator", "操作员");
call proc_add_form_field("workOrderReport.list", "userNickname", "上报人");
call proc_add_form_field("workOrderReport.list", "createTime", "上报时间");

call proc_add_form_field("workOrderReport.detail", "workOrderNumber", "生产工单号");
call proc_add_form_field("workOrderReport.detail", "craftProcedureName", "工序");
call proc_add_form_field("workOrderReport.detail", "endTime", "结束时间");
call proc_add_form_field("workOrderReport.detail", "operator", "操作员");
call proc_add_form_field("workOrderReport.detail", "finishCount", "完成数量");
call proc_add_form_field("workOrderReport.detail", "lineName", "制造单元");
call proc_add_form_field("workOrderReport.detail", "scaleFactor", "单重");
call proc_add_form_field("workOrderReport.detail", "totalScale", "总重");
call proc_add_form_field("workOrderReport.detail", "teamName", "班组");
call proc_add_form_field("workOrderReport.detail", "recordDate", "上报时间");
call proc_add_form_field("workOrderReport.detail", "deviceName", "设备");
call proc_add_form_field("workOrderReport.detail", "qualityLevel", "质量等级");
call proc_add_form_field("workOrderReport.detail", "unqualified", "不良数量");
call proc_add_form_field("workOrderReport.detail", "batch", "批次");
call proc_add_form_field("workOrderReport.detail", "batchRemark", "批次备注");
call proc_add_form_field("workOrderReport.detail", "shiftType", "班次");
call proc_add_form_field("workOrderReport.detail", "defectDesc", "不良描述");
call proc_add_form_field("workOrderReport.detail", "effectiveHours", "上报工时");
call proc_add_form_field("workOrderReport.detail", "userNickname", "上报人");
call proc_add_form_field("workOrderReport.detail", "createTime", "上报时间");

INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/workorder-model/production-records', 'workOrderReport.list', 'workOrderReport.list', 'workOrderNumber', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/workorder-model/production-records', 'workOrderReport.list', 'workOrderReport.list', 'state', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/workorder-model/production-records', 'workOrderReport.list', 'workOrderReport.list', 'orderReportName', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/workorder-model/production-records', 'workOrderReport.list', 'workOrderReport.list', 'processAssemblyList', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/workorder-model/production-records', 'workOrderReport.list', 'workOrderReport.list', 'customerName', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/workorder-model/production-records', 'workOrderReport.list', 'workOrderReport.list', 'materialCode', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/workorder-model/production-records', 'workOrderReport.list', 'workOrderReport.list', 'materialName', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/workorder-model/production-records', 'workOrderReport.list', 'workOrderReport.list', 'planQuantity', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/workorder-model/production-records', 'workOrderReport.list', 'workOrderReport.list', 'lossQuantity', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/workorder-model/production-records', 'workOrderReport.list', 'workOrderReport.list', 'standardScaleFactor', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/workorder-model/production-records', 'workOrderReport.list', 'workOrderReport.list', 'scaleFactor', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/workorder-model/production-records', 'workOrderReport.list', 'workOrderReport.list', 'totalScale', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/workorder-model/production-records', 'workOrderReport.list', 'workOrderReport.list', 'productStandard', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/workorder-model/production-records', 'workOrderReport.list', 'workOrderReport.list', 'lineModelName', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/workorder-model/production-records', 'workOrderReport.list', 'workOrderReport.list', 'lineName', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/workorder-model/production-records', 'workOrderReport.list', 'workOrderReport.list', 'teamName', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/workorder-model/production-records', 'workOrderReport.list', 'workOrderReport.list', 'counterPerReported', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/workorder-model/production-records', 'workOrderReport.list', 'workOrderReport.list', 'counterReported', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/workorder-model/production-records', 'workOrderReport.list', 'workOrderReport.list', 'reportTime', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/workorder-model/production-records', 'workOrderReport.list', 'workOrderReport.list', 'reportEndTime', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/workorder-model/production-records', 'workOrderReport.list', 'workOrderReport.list', 'deviceName', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/workorder-model/production-records', 'workOrderReport.list', 'workOrderReport.list', 'workCenterTypeName', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/workorder-model/production-records', 'workOrderReport.list', 'workOrderReport.list', 'workCenterName', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/workorder-model/production-records', 'workOrderReport.list', 'workOrderReport.list', 'procedureName', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/workorder-model/production-records', 'workOrderReport.list', 'workOrderReport.list', 'procedureAlias', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/workorder-model/production-records', 'workOrderReport.list', 'workOrderReport.list', 'resourceTypeName', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/workorder-model/production-records', 'workOrderReport.list', 'workOrderReport.list', 'startDate', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/workorder-model/production-records', 'workOrderReport.list', 'workOrderReport.list', 'endDate', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/workorder-model/production-records', 'workOrderReport.list', 'workOrderReport.list', 'actualStartDate', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/workorder-model/production-records', 'workOrderReport.list', 'workOrderReport.list', 'actualEndDate', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/workorder-model/production-records', 'workOrderReport.list', 'workOrderReport.list', 'qualityLevel', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/workorder-model/production-records', 'workOrderReport.list', 'workOrderReport.list', 'isIncludeDefect', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/workorder-model/production-records', 'workOrderReport.list', 'workOrderReport.list', 'inputTotal', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/workorder-model/production-records', 'workOrderReport.list', 'workOrderReport.list', 'finishCount', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/workorder-model/production-records', 'workOrderReport.list', 'workOrderReport.list', 'unqualified', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/workorder-model/production-records', 'workOrderReport.list', 'workOrderReport.list', 'plannedWorkingHours', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/workorder-model/production-records', 'workOrderReport.list', 'workOrderReport.list', 'actualWorkingHours', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/workorder-model/production-records', 'workOrderReport.list', 'workOrderReport.list', 'theoryHour', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/workorder-model/production-records', 'workOrderReport.list', 'workOrderReport.list', 'produceTheoryHour', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/workorder-model/production-records', 'workOrderReport.list', 'workOrderReport.list', 'circulationDuration', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/workorder-model/production-records', 'workOrderReport.list', 'workOrderReport.list', 'orderNumber', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/workorder-model/production-records', 'workOrderReport.list', 'workOrderReport.list', 'saleOrderNumber', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/workorder-model/production-records', 'workOrderReport.list', 'workOrderReport.list', 'type', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/workorder-model/production-records', 'workOrderReport.list', 'workOrderReport.list', 'batch', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/workorder-model/production-records', 'workOrderReport.list', 'workOrderReport.list', 'batchRemark', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/workorder-model/production-records', 'workOrderReport.list', 'workOrderReport.list', 'vehicleCode', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/workorder-model/production-records', 'workOrderReport.list', 'workOrderReport.list', 'shiftType', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/workorder-model/production-records', 'workOrderReport.list', 'workOrderReport.list', 'finishCountTwo', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/workorder-model/production-records', 'workOrderReport.list', 'workOrderReport.list', 'crewSize', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/workorder-model/production-records', 'workOrderReport.list', 'workOrderReport.list', 'unqualifiedTwo', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/workorder-model/production-records', 'workOrderReport.list', 'workOrderReport.list', 'defectDesc', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/workorder-model/production-records', 'workOrderReport.list', 'workOrderReport.list', 'effectiveHours', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/workorder-model/production-records', 'workOrderReport.list', 'workOrderReport.list', 'reportDate', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/workorder-model/production-records', 'workOrderReport.list', 'workOrderReport.list', 'operator', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/workorder-model/production-records', 'workOrderReport.list', 'workOrderReport.list', 'userNickname', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/workorder-model/production-records', 'workOrderReport.list', 'workOrderReport.list', 'createTime', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);

INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/workorder-model/production-records', 'workOrderReport.detail', 'workOrderReport.detail', 'workOrderNumber', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/workorder-model/production-records', 'workOrderReport.detail', 'workOrderReport.detail', 'craftProcedureName', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/workorder-model/production-records', 'workOrderReport.detail', 'workOrderReport.detail', 'endTime', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/workorder-model/production-records', 'workOrderReport.detail', 'workOrderReport.detail', 'operator', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/workorder-model/production-records', 'workOrderReport.detail', 'workOrderReport.detail', 'finishCount', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/workorder-model/production-records', 'workOrderReport.detail', 'workOrderReport.detail', 'lineName', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/workorder-model/production-records', 'workOrderReport.detail', 'workOrderReport.detail', 'scaleFactor', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/workorder-model/production-records', 'workOrderReport.detail', 'workOrderReport.detail', 'totalScale', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/workorder-model/production-records', 'workOrderReport.detail', 'workOrderReport.detail', 'teamName', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/workorder-model/production-records', 'workOrderReport.detail', 'workOrderReport.detail', 'recordDate', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/workorder-model/production-records', 'workOrderReport.detail', 'workOrderReport.detail', 'deviceName', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/workorder-model/production-records', 'workOrderReport.detail', 'workOrderReport.detail', 'qualityLevel', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/workorder-model/production-records', 'workOrderReport.detail', 'workOrderReport.detail', 'unqualified', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/workorder-model/production-records', 'workOrderReport.detail', 'workOrderReport.detail', 'batch', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/workorder-model/production-records', 'workOrderReport.detail', 'workOrderReport.detail', 'batchRemark', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/workorder-model/production-records', 'workOrderReport.detail', 'workOrderReport.detail', 'shiftType', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/workorder-model/production-records', 'workOrderReport.detail', 'workOrderReport.detail', 'defectDesc', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/workorder-model/production-records', 'workOrderReport.detail', 'workOrderReport.detail', 'effectiveHours', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/workorder-model/production-records', 'workOrderReport.detail', 'workOrderReport.detail', 'userNickname', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/workorder-model/production-records', 'workOrderReport.detail', 'workOrderReport.detail', 'createTime', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);



-- 刷新表单配置菜单的所属模块,默认为空，也就是以前的框架（第三方服务需要自行更新所属模块，以下脚本仅支持dfs、ams）
UPDATE `dfs_form_config` SET `module` = 'detail' WHERE `full_path_code` in ('workOrder.list', 'saleOrder.list.order', 'saleOrder.list.material', 'productOrder.list', 'material.list', 'purchaseRequestOrder.list', 'purchaseOrder.list.order', 'purchaseOrder.list.material', 'purchaseReceiptOrder.list.order', 'purchaseReceiptOrder.list.material', 'salesIssueDoc.list.material', 'salesIssueDoc.list.order', 'workOrderComplete.list.material', 'workOrderComplete.list.order', 'workScheduleProduction.toBeScheduledList', 'workScheduleProduction.scheduledList', 'subcontractOrder.list.order', 'subcontractOrder.list.material', 'workOrderReport.list', 'productOrderMaterialList.list.order', 'productOrderMaterialList.list.material');
UPDATE `dfs_form_config` SET `module` = 'detail' WHERE `full_path_code` like '%.detail';
UPDATE `dfs_form_config` SET `module` = 'detail' WHERE `full_path_code` in ('orderReportApp.list', 'orderReportApp.orderDetail', 'orderReportApp.orderDetail.productOrderDetail', 'orderReportApp.orderDetail.workOrderDetail', 'orderReportApp.productReportProductOrderDetail', 'orderReportApp.productReportWorkOrderDetail', 'workOrderReport.workCenterList', 'workOrderReport.lineList', 'workOrderReport.workOrderDetail.detail');

-- 将部分表单菜单设置为系统内部字段
UPDATE `dfs_form_config` SET `is_inner` = 1 WHERE `full_path_code` in ('orderReportApp','orderReportApp.list','orderReportApp.orderDetail','orderReportApp.orderDetail.productOrderDetail','orderReportApp.orderDetail.workOrderDetail','orderReportApp.processTasks','orderReportApp.productReportPage','orderReportApp.productReportProductOrderDetail','orderReportApp.productReportWorkOrderDetail','orderReportApp.addFirstInspect','orderReportApp.addProcessInspect','orderReportApp.addFinalInspect','orderReportApp.addReport','orderReportApp.editReport','orderReportApp.addInputCount','orderReportApp.editInputCount','orderReportApp.abnormalReport','orderReportApp.batchPrint','workOrderReportApp','workOrderReport.workCenterList','workOrderReport.lineList','workOrderReport.workOrderList','workOrderReport.workOrderDetail','workOrderReport.workOrderDetail.detail','workOrderReport.addReport','workOrderReport.editReport','workOrderReport.addInputCount','workOrderReport.editInputCount','workOrderReport.abnormalReport','workOrderReport.batchPrint','workOrder','workOrder.list','workOrder.edit','workOrder.editByCreate','workOrder.editByRelease','workOrder.editByInvestment','workOrder.editByHangUp','workOrder.editByFinish','workOrder.editByClosed','workOrder.editByCancel','workOrder.detail','saleOrder','saleOrder.list','saleOrder.list.order','saleOrder.list.material','saleOrder.edit','saleOrder.editByCreate','saleOrder.editByRelease','saleOrder.editByFinish','saleOrder.editByClosed','saleOrder.editByCancel','saleOrder.detail','productOrder','productOrder.list','productOrder.edit','productOrder.editByCreate','productOrder.editByRelease','productOrder.editByFinish','productOrder.editByClosed','productOrder.editByCancel','productOrder.detail','material','material.list','material.edit','material.editByCreate','material.editByRelease','material.editByStopUsing','material.editByAbandon','material.detail','purchaseRequestOrder','purchaseRequestOrder.list','purchaseRequestOrder.edit','purchaseRequestOrder.editByCreate','purchaseRequestOrder.editByRelease','purchaseRequestOrder.editByFinish','purchaseRequestOrder.editByClosed','purchaseRequestOrder.editByCancel','purchaseRequestOrder.detail','purchaseOrder','purchaseOrder.list','purchaseOrder.list.order','purchaseOrder.list.material','purchaseOrder.edit','purchaseOrder.editByCreate','purchaseOrder.editByRelease','purchaseOrder.editByFinish','purchaseOrder.editByClosed','purchaseOrder.editByCancel','purchaseOrder.detail','purchaseReceiptOrder','purchaseReceiptOrder.list','purchaseReceiptOrder.list.order','purchaseReceiptOrder.list.material','purchaseReceiptOrder.edit','purchaseReceiptOrder.editByCreate','purchaseReceiptOrder.editByRelease','purchaseReceiptOrder.editByFinish','purchaseReceiptOrder.editByClosed','purchaseReceiptOrder.editByCancel','purchaseReceiptOrder.detail','valuationConfig','valuationConfig.manualConfigAdd','valuationConfig.manualConfigEdit','valuationConfig.extendValuationReport','subcontractOrder.list','subcontractOrder.list.order','subcontractOrder.list.material','subcontractOrder.edit','subcontractOrder.editByCreate','subcontractOrder.editByRelease','subcontractOrder.editByFinish','subcontractOrder.editByClosed','subcontractOrder.editByCancel','subcontractOrder.detail','workScheduleProduction','workScheduleProduction.toBeScheduledList','workScheduleProduction.scheduledList','subcontractOrder','workOrderReport','workOrderReport.list','workOrderReport.detail','productOrderMaterialList','productOrderMaterialList.list','productOrderMaterialList.list.order','productOrderMaterialList.list.material','productOrderMaterialList.edit','productOrderMaterialList.editByCreate','productOrderMaterialList.editByRelease','productOrderMaterialList.editByFinish','productOrderMaterialList.editByClosed','productOrderMaterialList.editByCancel','productOrderMaterialList.detail');

-- ================================================此脚本专为表单需求设定，如需写脚本，请移步到********=======================================================
-- ================================================此脚本专为表单需求设定，如需写脚本，请移步到********=======================================================
-- ================================================此脚本专为表单需求设定，如需写脚本，请移步到********=======================================================
-- ================================================此脚本专为表单需求设定，如需写脚本，请移步到********=======================================================
-- ================================================此脚本专为表单需求设定，如需写脚本，请移步到********=======================================================
