truncate table `dfs_version_change_record`;
-- 版本变更记录针对BOM、工艺的历史数据，修订次数统一设置为0
INSERT INTO `dfs_version_change_record` (`relate_type`, `relate_id`, `first_id`, `version_current`, `editor`, `change_time`, `change_count`, `change_log`)
SELECT 'BOM', `id`, `id`, `version`, `create_by`, `create_time`, 0, 'BOM - 新增'
FROM `dfs_bom`;


INSERT INTO `dfs_version_change_record` (`relate_type`, `relate_id`, `first_id`, `version_current`, `editor`, `change_time`, `change_count`, `change_log`)
SELECT 'CRAFT', `craft_id`, `craft_id`, `craft_version`, `create_by`, `create_time`, 0, '工艺 - 新增'
FROM `dfs_craft`;

-- 修改物料换算系数为11个小数点，防止运行下面那条sql报错
call proc_modify_column(
        'dfs_material',
        'scale_factor',
        'ALTER TABLE `dfs_material` MODIFY COLUMN `scale_factor` double(20,9) DEFAULT 1 COMMENT ''换算系数''');

-- 刷新物料历史数据的换算系数
UPDATE `dfs_material` SET `scale_factor` = `unit_numerator` / `unit_denominator`
WHERE  `unit_numerator` != 0;

-- 表单配置--生产订单用料清单新增 bom编码、bom版本
call proc_add_form_field("productOrderMaterialList.list.material", "bomNum", "BOM编码");
call proc_add_form_field("productOrderMaterialList.list.material", "bomVersion", "BOM版本");
call proc_add_form_field("productOrderMaterialList.list.order", "bomNum", "BOM编码");
call proc_add_form_field("productOrderMaterialList.list.order", "bomVersion", "BOM版本");
call proc_add_form_field("productOrderMaterialList.edit", "bomNum", "BOM编码");
call proc_add_form_field("productOrderMaterialList.edit", "bomVersion", "BOM版本");
call proc_add_form_field("productOrderMaterialList.detail", "bomNum", "BOM编码");
call proc_add_form_field("productOrderMaterialList.detail", "bomVersion", "BOM版本");

INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/production-materials', 'productOrderMaterialList.list.order', 'productOrderMaterialList.list.order', 'bomNum', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/production-materials', 'productOrderMaterialList.list.order', 'productOrderMaterialList.list.order', 'bomVersion', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);

INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/production-materials', 'productOrderMaterialList.list.material', 'productOrderMaterialList.list.material', 'bomNum', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/production-materials', 'productOrderMaterialList.list.material', 'productOrderMaterialList.list.material', 'bomVersion', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);

INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/production-materials', 'productOrderMaterialList.editByCreate', 'productOrderMaterialList.edit', 'bomNum', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/production-materials', 'productOrderMaterialList.editByCreate', 'productOrderMaterialList.edit', 'bomVersion', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);

INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/production-materials', 'productOrderMaterialList.editByRelease', 'productOrderMaterialList.edit', 'bomNum', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/production-materials', 'productOrderMaterialList.editByRelease', 'productOrderMaterialList.edit', 'bomVersion', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);

INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/production-materials', 'productOrderMaterialList.editByFinish', 'productOrderMaterialList.edit', 'bomNum', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/production-materials', 'productOrderMaterialList.editByFinish', 'productOrderMaterialList.edit', 'bomVersion', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);

INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/production-materials', 'productOrderMaterialList.editByClosed', 'productOrderMaterialList.edit', 'bomNum', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/production-materials', 'productOrderMaterialList.editByClosed', 'productOrderMaterialList.edit', 'bomVersion', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);

INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/production-materials', 'productOrderMaterialList.editByCancel', 'productOrderMaterialList.edit', 'bomNum', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/production-materials', 'productOrderMaterialList.editByCancel', 'productOrderMaterialList.edit', 'bomVersion', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);

INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/production-materials', 'productOrderMaterialList.detail', 'productOrderMaterialList.detail', 'bomNum', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/production-materials', 'productOrderMaterialList.detail', 'productOrderMaterialList.detail', 'bomVersion', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);