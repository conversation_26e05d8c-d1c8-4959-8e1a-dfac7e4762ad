call proc_add_column(
        'dfs_report_line_procedure',
        'resource_device_code',
        'ALTER TABLE `dfs_report_line_procedure` ADD COLUMN `resource_device_code` varchar(100) DEFAULT NULL COMMENT ''关联资源-设备编号''');

call proc_add_column(
        'dfs_report_line_procedure',
        'resource_device_name',
        'ALTER TABLE `dfs_report_line_procedure` ADD COLUMN `resource_device_name` varchar(100) DEFAULT NULL COMMENT ''关联资源-设备名称''');


call proc_add_column(
        'dfs_report_line_procedure',
        'resource_team_code',
        'ALTER TABLE `dfs_report_line_procedure` ADD COLUMN `resource_team_code` varchar(100) DEFAULT NULL COMMENT ''关联资源-班组编码''');

call proc_add_column(
        'dfs_report_line_procedure',
        'resource_team_name',
        'ALTER TABLE `dfs_report_line_procedure` ADD COLUMN `resource_team_name` varchar(100) DEFAULT NULL COMMENT ''关联资源-班组名称''');


call proc_add_column(
        'dfs_report_line_procedure',
        'resource_line_code',
        'ALTER TABLE `dfs_report_line_procedure` ADD COLUMN `resource_line_code` varchar(100) DEFAULT NULL COMMENT ''关联资源-产线编码''');

call proc_add_column(
        'dfs_report_line_procedure',
        'resource_line_name',
        'ALTER TABLE `dfs_report_line_procedure` ADD COLUMN `resource_line_name` varchar(100) DEFAULT NULL COMMENT ''关联资源-产线名称''');
