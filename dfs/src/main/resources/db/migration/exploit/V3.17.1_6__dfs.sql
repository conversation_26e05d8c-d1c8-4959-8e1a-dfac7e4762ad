-- ================================================此脚本专为消息需求设定，如需写脚本，请移步到3.17.1.1=======================================================
-- ================================================此脚本专为消息需求设定，如需写脚本，请移步到3.17.1.1=======================================================
-- ================================================此脚本专为消息需求设定，如需写脚本，请移步到3.17.1.1=======================================================
-- ================================================此脚本专为消息需求设定，如需写脚本，请移步到3.17.1.1=======================================================
-- ================================================此脚本专为消息需求设定，如需写脚本，请移步到3.17.1.1=======================================================

DROP TABLE IF EXISTS `dfs_notice_field_info`;
CREATE TABLE IF NOT EXISTS `dfs_notice_field_info` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `code` varchar(255) NOT NULL COMMENT '字段编号',
  `name` varchar(255) DEFAULT NULL COMMENT '字段名称',
  `placeholder` varchar(255) DEFAULT NULL COMMENT '字段替代符',
  `is_sys_field` tinyint(4) DEFAULT '0' COMMENT '是否为系统字段',
  PRIMARY KEY (`id`),
  UNIQUE KEY `placeholder` (`placeholder`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='消息字段信息表';

DROP TABLE IF EXISTS `dfs_notice_type_placeholder`;
CREATE TABLE IF NOT EXISTS `dfs_notice_type_placeholder` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `notice_type_code` varchar(255) NOT NULL COMMENT '消息类型编号',
  `placeholder` varchar(255) DEFAULT NULL COMMENT '字段替代符',
  PRIMARY KEY (`id`),
  KEY `notice_type_code` (`notice_type_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='消息类型替代符关联表';



truncate table dfs_config_notice_level_relation;
INSERT INTO `dfs_config_notice_level_relation`(`code`, `name`, `configurable_placeholder`, `third_party_conf_placeholder`, `parent_code`) VALUES ('saleOrder', '销售订单', NULL, NULL, NULL);
INSERT INTO `dfs_config_notice_level_relation`(`code`, `name`, `configurable_placeholder`, `third_party_conf_placeholder`, `parent_code`) VALUES ('productOrder', '生产订单', NULL, NULL, NULL);
INSERT INTO `dfs_config_notice_level_relation`(`code`, `name`, `configurable_placeholder`, `third_party_conf_placeholder`, `parent_code`) VALUES ('workOrder', '生产工单', NULL, NULL, NULL);
INSERT INTO `dfs_config_notice_level_relation`(`code`, `name`, `configurable_placeholder`, `third_party_conf_placeholder`, `parent_code`) VALUES ('purchaseOrder', '采购订单', NULL, NULL, NULL);
INSERT INTO `dfs_config_notice_level_relation`(`code`, `name`, `configurable_placeholder`, `third_party_conf_placeholder`, `parent_code`) VALUES ('inspectionForm', '产品检验', NULL, NULL, NULL);
INSERT INTO `dfs_config_notice_level_relation`(`code`, `name`, `configurable_placeholder`, `third_party_conf_placeholder`, `parent_code`) VALUES ('craft', '工艺', NULL, NULL, NULL);
INSERT INTO `dfs_config_notice_level_relation`(`code`, `name`, `configurable_placeholder`, `third_party_conf_placeholder`, `parent_code`) VALUES ('bom', 'BOM', NULL, NULL, NULL);
INSERT INTO `dfs_config_notice_level_relation`(`code`, `name`, `configurable_placeholder`, `third_party_conf_placeholder`, `parent_code`) VALUES ('material', '物料', NULL, NULL, NULL);
INSERT INTO `dfs_config_notice_level_relation`(`code`, `name`, `configurable_placeholder`, `third_party_conf_placeholder`, `parent_code`) VALUES ('saleOrderAddNotice', '单据新增通知', NULL, NULL, 'saleOrder');
INSERT INTO `dfs_config_notice_level_relation`(`code`, `name`, `configurable_placeholder`, `third_party_conf_placeholder`, `parent_code`) VALUES ('productOrderAddNotice', '单据新增通知', NULL, NULL, 'productOrder');
INSERT INTO `dfs_config_notice_level_relation`(`code`, `name`, `configurable_placeholder`, `third_party_conf_placeholder`, `parent_code`) VALUES ('workOrderAddNotice', '单据新增通知', NULL, NULL, 'workOrder');
INSERT INTO `dfs_config_notice_level_relation`(`code`, `name`, `configurable_placeholder`, `third_party_conf_placeholder`, `parent_code`) VALUES ('purchaseOrderAddNotice', '单据新增通知', NULL, NULL, 'purchaseOrder');
INSERT INTO `dfs_config_notice_level_relation`(`code`, `name`, `configurable_placeholder`, `third_party_conf_placeholder`, `parent_code`) VALUES ('workOrderApprovalNotice', '单据审批通知', NULL, NULL, 'workOrder');
INSERT INTO `dfs_config_notice_level_relation`(`code`, `name`, `configurable_placeholder`, `third_party_conf_placeholder`, `parent_code`) VALUES ('approvePurchaseOrderNotice', '单据审批通知', NULL, NULL, 'purchaseOrder');
INSERT INTO `dfs_config_notice_level_relation`(`code`, `name`, `configurable_placeholder`, `third_party_conf_placeholder`, `parent_code`) VALUES ('saleOrderStateChangeNotice', '单据状态变更通知', NULL, NULL, 'saleOrder');
INSERT INTO `dfs_config_notice_level_relation`(`code`, `name`, `configurable_placeholder`, `third_party_conf_placeholder`, `parent_code`) VALUES ('productOrderStateChangeNotice', '单据状态变更通知', NULL, NULL, 'productOrder');
INSERT INTO `dfs_config_notice_level_relation`(`code`, `name`, `configurable_placeholder`, `third_party_conf_placeholder`, `parent_code`) VALUES ('workOrderStateChangeNotice', '单据状态变更通知', NULL, NULL, 'workOrder');
INSERT INTO `dfs_config_notice_level_relation`(`code`, `name`, `configurable_placeholder`, `third_party_conf_placeholder`, `parent_code`) VALUES ('purchaseOrderStateChangeNotice', '单据状态变更通知', NULL, NULL, 'purchaseOrder');
INSERT INTO `dfs_config_notice_level_relation`(`code`, `name`, `configurable_placeholder`, `third_party_conf_placeholder`, `parent_code`) VALUES ('taskInfoNotice', '任务消息通知', NULL, NULL, NULL);
INSERT INTO `dfs_config_notice_level_relation`(`code`, `name`, `configurable_placeholder`, `third_party_conf_placeholder`, `parent_code`) VALUES ('designNotice', '设计消息通知', NULL, NULL, 'taskInfoNotice');
INSERT INTO `dfs_config_notice_level_relation`(`code`, `name`, `configurable_placeholder`, `third_party_conf_placeholder`, `parent_code`) VALUES ('unusualInfoNotice', '异常消息通知', NULL, NULL, NULL);
INSERT INTO `dfs_config_notice_level_relation`(`code`, `name`, `configurable_placeholder`, `third_party_conf_placeholder`, `parent_code`) VALUES ('returnMessageNotice', '退货消息通知', NULL, NULL, 'unusualInfoNotice');
INSERT INTO `dfs_config_notice_level_relation`(`code`, `name`, `configurable_placeholder`, `third_party_conf_placeholder`, `parent_code`) VALUES ('productQuantityLowerThanSalesQuantityNotice', '生产数量低于销售数量通知', NULL, NULL, 'unusualInfoNotice');
INSERT INTO `dfs_config_notice_level_relation`(`code`, `name`, `configurable_placeholder`, `third_party_conf_placeholder`, `parent_code`) VALUES ('productTimeOut', '生产超时通知', NULL, NULL, 'unusualInfoNotice');
INSERT INTO `dfs_config_notice_level_relation`(`code`, `name`, `configurable_placeholder`, `third_party_conf_placeholder`, `parent_code`) VALUES ('alarmReportNotice', '告警异常上报通知', NULL, NULL, 'unusualInfoNotice');
INSERT INTO `dfs_config_notice_level_relation`(`code`, `name`, `configurable_placeholder`, `third_party_conf_placeholder`, `parent_code`) VALUES ('alarmDealNotice', '告警异常处理通知', NULL, NULL, 'unusualInfoNotice');
INSERT INTO `dfs_config_notice_level_relation`(`code`, `name`, `configurable_placeholder`, `third_party_conf_placeholder`, `parent_code`) VALUES ('productCirculationNotice', '生产流转通知', NULL, NULL, NULL);
INSERT INTO `dfs_config_notice_level_relation`(`code`, `name`, `configurable_placeholder`, `third_party_conf_placeholder`, `parent_code`) VALUES ('takeMaterial', '取料通知', NULL, NULL, 'productCirculationNotice');
INSERT INTO `dfs_config_notice_level_relation`(`code`, `name`, `configurable_placeholder`, `third_party_conf_placeholder`, `parent_code`) VALUES ('takeMaterialTimeOut', '取料超时通知', NULL, NULL, 'productCirculationNotice');
INSERT INTO `dfs_config_notice_level_relation`(`code`, `name`, `configurable_placeholder`, `third_party_conf_placeholder`, `parent_code`) VALUES ('sendMaterial', '送料通知', NULL, NULL, 'productCirculationNotice');
INSERT INTO `dfs_config_notice_level_relation`(`code`, `name`, `configurable_placeholder`, `third_party_conf_placeholder`, `parent_code`) VALUES ('sendMaterialTimeOut', '送料超时通知', NULL, NULL, 'productCirculationNotice');
INSERT INTO `dfs_config_notice_level_relation`(`code`, `name`, `configurable_placeholder`, `third_party_conf_placeholder`, `parent_code`) VALUES ('productCollaborationNotice', '生产协同通知', NULL, NULL, NULL);
INSERT INTO `dfs_config_notice_level_relation`(`code`, `name`, `configurable_placeholder`, `third_party_conf_placeholder`, `parent_code`) VALUES ('locationMachineNotice', '工位机消息通知', NULL, NULL, 'productCollaborationNotice');
INSERT INTO `dfs_config_notice_level_relation`(`code`, `name`, `configurable_placeholder`, `third_party_conf_placeholder`, `parent_code`) VALUES ('qualityInfoNotice', '质量消息通知', NULL, NULL, NULL);
INSERT INTO `dfs_config_notice_level_relation`(`code`, `name`, `configurable_placeholder`, `third_party_conf_placeholder`, `parent_code`) VALUES ('badNewsNotice', '不良消息通知', NULL, NULL, 'qualityInfoNotice');
INSERT INTO `dfs_config_notice_level_relation`(`code`, `name`, `configurable_placeholder`, `third_party_conf_placeholder`, `parent_code`) VALUES ('productPatrolNotice', '生产巡检通知', NULL, NULL, 'qualityInfoNotice');
INSERT INTO `dfs_config_notice_level_relation`(`code`, `name`, `configurable_placeholder`, `third_party_conf_placeholder`, `parent_code`) VALUES ('inspectionFormAddNotice', '单据新增通知', NULL, NULL, 'inspectionForm');
INSERT INTO `dfs_config_notice_level_relation`(`code`, `name`, `configurable_placeholder`, `third_party_conf_placeholder`, `parent_code`) VALUES ('defectNumThresholdNotice', '不良数量达到阈值通知', NULL, NULL, 'qualityInfoNotice');
INSERT INTO `dfs_config_notice_level_relation`(`code`, `name`, `configurable_placeholder`, `third_party_conf_placeholder`, `parent_code`) VALUES ('defectRateThresholdNotice', '不良率达到阈值通知', NULL, NULL, 'qualityInfoNotice');
INSERT INTO `dfs_config_notice_level_relation`(`code`, `name`, `configurable_placeholder`, `third_party_conf_placeholder`, `parent_code`) VALUES ('concreteDefectNumThresholdNotice', '具体不良超数量阈值通知', NULL, NULL, 'qualityInfoNotice');
INSERT INTO `dfs_config_notice_level_relation`(`code`, `name`, `configurable_placeholder`, `third_party_conf_placeholder`, `parent_code`) VALUES ('manualConcreteDefectNotice', '手动具体不良通知', NULL, NULL, 'qualityInfoNotice');
INSERT INTO `dfs_config_notice_level_relation`(`code`, `name`, `configurable_placeholder`, `third_party_conf_placeholder`, `parent_code`) VALUES ('iqcNotice', '生产呼叫通知', NULL, NULL, 'productCirculationNotice');
INSERT INTO `dfs_config_notice_level_relation`(`code`, `name`, `configurable_placeholder`, `third_party_conf_placeholder`, `parent_code`) VALUES ('inspectionFormStateChangeNotice', '单据状态变更通知', NULL, NULL, 'inspectionForm');
INSERT INTO `dfs_config_notice_level_relation`(`code`, `name`, `configurable_placeholder`, `third_party_conf_placeholder`, `parent_code`) VALUES ('inspectProcessChangeNotice', '检验阶段变更通知', NULL, NULL, 'qualityInfoNotice');
INSERT INTO `dfs_config_notice_level_relation`(`code`, `name`, `configurable_placeholder`, `third_party_conf_placeholder`, `parent_code`) VALUES ('projectNotice', '项目消息通知', NULL, NULL, NULL);
INSERT INTO `dfs_config_notice_level_relation`(`code`, `name`, `configurable_placeholder`, `third_party_conf_placeholder`, `parent_code`) VALUES ('taskAddNotice', '任务新增通知', NULL, NULL, 'projectNotice');
INSERT INTO `dfs_config_notice_level_relation`(`code`, `name`, `configurable_placeholder`, `third_party_conf_placeholder`, `parent_code`) VALUES ('taskUpdateNotice', '任务修改通知', NULL, NULL, 'projectNotice');
INSERT INTO `dfs_config_notice_level_relation`(`code`, `name`, `configurable_placeholder`, `third_party_conf_placeholder`, `parent_code`) VALUES ('taskTimeNotice', '任务到期通知', NULL, NULL, 'projectNotice');
INSERT INTO `dfs_config_notice_level_relation`(`code`, `name`, `configurable_placeholder`, `third_party_conf_placeholder`, `parent_code`) VALUES ('processAssemblyNotice', '工装消息通知', NULL, NULL, NULL);
INSERT INTO `dfs_config_notice_level_relation`(`code`, `name`, `configurable_placeholder`, `third_party_conf_placeholder`, `parent_code`) VALUES ('processAssemblyExpirationNotice', '寿命到期通知', NULL, NULL, 'processAssemblyNotice');
INSERT INTO `dfs_config_notice_level_relation`(`code`, `name`, `configurable_placeholder`, `third_party_conf_placeholder`, `parent_code`) VALUES ('badInspectionNotice', '产品检验结果通知', NULL, NULL, 'qualityInfoNotice');
INSERT INTO `dfs_config_notice_level_relation`(`code`, `name`, `configurable_placeholder`, `third_party_conf_placeholder`, `parent_code`) VALUES ('craftApprovalNotice', '单据审批通知', NULL, NULL, 'craft');
INSERT INTO `dfs_config_notice_level_relation`(`code`, `name`, `configurable_placeholder`, `third_party_conf_placeholder`, `parent_code`) VALUES ('bomApprovalNotice', '单据审批通知', NULL, NULL, 'bom');
INSERT INTO `dfs_config_notice_level_relation`(`code`, `name`, `configurable_placeholder`, `third_party_conf_placeholder`, `parent_code`) VALUES ('materialApprovalNotice', '单据审批通知', NULL, NULL, 'material');

truncate table dfs_notice_field_info;
INSERT INTO `dfs_notice_field_info`(`id`, `code`, `name`, `placeholder`, `is_sys_field`) VALUES (1, 'procedureName', '工序名称', '{工序名称}', 1);
INSERT INTO `dfs_notice_field_info`(`id`, `code`, `name`, `placeholder`, `is_sys_field`) VALUES (2, 'preProcedureName', '前工序名称', '{前工序名称}', 1);
INSERT INTO `dfs_notice_field_info`(`id`, `code`, `name`, `placeholder`, `is_sys_field`) VALUES (3, 'backProcedureName', '后工序名称', '{后工序名称}', 1);
INSERT INTO `dfs_notice_field_info`(`id`, `code`, `name`, `placeholder`, `is_sys_field`) VALUES (4, 'workCenterName', '工作中心名称', '{工作中心名称}', 1);
INSERT INTO `dfs_notice_field_info`(`id`, `code`, `name`, `placeholder`, `is_sys_field`) VALUES (5, 'productionBasicUnitName', '基本生产单元名称', '{基本生产单元名称}', 1);
INSERT INTO `dfs_notice_field_info`(`id`, `code`, `name`, `placeholder`, `is_sys_field`) VALUES (6, 'lineName', '产线名称', '{产线名称}', 1);
INSERT INTO `dfs_notice_field_info`(`id`, `code`, `name`, `placeholder`, `is_sys_field`) VALUES (7, 'preLineName', '前产线名称', '{前产线名称}', 1);
INSERT INTO `dfs_notice_field_info`(`id`, `code`, `name`, `placeholder`, `is_sys_field`) VALUES (8, 'backLineName', '后产线名称', '{后产线名称}', 1);
INSERT INTO `dfs_notice_field_info`(`id`, `code`, `name`, `placeholder`, `is_sys_field`) VALUES (9, 'customerCode', '客户编码', '{客户编码}', 1);
INSERT INTO `dfs_notice_field_info`(`id`, `code`, `name`, `placeholder`, `is_sys_field`) VALUES (10, 'materialCode', '物料编号', '{物料编号}', 1);
INSERT INTO `dfs_notice_field_info`(`id`, `code`, `name`, `placeholder`, `is_sys_field`) VALUES (11, 'materialName', '物料名称', '{物料名称}', 1);
INSERT INTO `dfs_notice_field_info`(`id`, `code`, `name`, `placeholder`, `is_sys_field`) VALUES (12, 'orderQuantity', '下单数量', '{下单数量}', 1);
INSERT INTO `dfs_notice_field_info`(`id`, `code`, `name`, `placeholder`, `is_sys_field`) VALUES (13, 'orderDate', '下单时间', '{下单时间}', 1);
INSERT INTO `dfs_notice_field_info`(`id`, `code`, `name`, `placeholder`, `is_sys_field`) VALUES (14, 'deliveryDate', '交货日期', '{交货日期}', 1);
INSERT INTO `dfs_notice_field_info`(`id`, `code`, `name`, `placeholder`, `is_sys_field`) VALUES (15, 'returnNumber', '退货数量', '{退货数量}', 1);
INSERT INTO `dfs_notice_field_info`(`id`, `code`, `name`, `placeholder`, `is_sys_field`) VALUES (16, 'returnReason', '退货原因', '{退货原因}', 1);
INSERT INTO `dfs_notice_field_info`(`id`, `code`, `name`, `placeholder`, `is_sys_field`) VALUES (17, 'defectReason', '不良原因', '{不良原因}', 1);
INSERT INTO `dfs_notice_field_info`(`id`, `code`, `name`, `placeholder`, `is_sys_field`) VALUES (18, 'defectNum', '不良数量', '{不良数量}', 1);
INSERT INTO `dfs_notice_field_info`(`id`, `code`, `name`, `placeholder`, `is_sys_field`) VALUES (19, 'defectRate', '不良率', '{不良率}', 1);
INSERT INTO `dfs_notice_field_info`(`id`, `code`, `name`, `placeholder`, `is_sys_field`) VALUES (20, 'defectNumThreshold', '不良数量阈值', '{不良数量阈值}', 1);
INSERT INTO `dfs_notice_field_info`(`id`, `code`, `name`, `placeholder`, `is_sys_field`) VALUES (21, 'defectRateThreshold', '不良率阈值', '{不良率阈值}', 1);
INSERT INTO `dfs_notice_field_info`(`id`, `code`, `name`, `placeholder`, `is_sys_field`) VALUES (22, 'defectName', '不良名称', '{不良名称}', 1);
INSERT INTO `dfs_notice_field_info`(`id`, `code`, `name`, `placeholder`, `is_sys_field`) VALUES (23, 'orderNumber', '单号', '{单号}', 1);
INSERT INTO `dfs_notice_field_info`(`id`, `code`, `name`, `placeholder`, `is_sys_field`) VALUES (24, 'exceptionDealName', '异常处理人', '{异常处理人}', 1);
INSERT INTO `dfs_notice_field_info`(`id`, `code`, `name`, `placeholder`, `is_sys_field`) VALUES (25, 'exceptionCost', '异常耗时', '{异常耗时}', 1);
INSERT INTO `dfs_notice_field_info`(`id`, `code`, `name`, `placeholder`, `is_sys_field`) VALUES (26, 'exceptionInfo', '异常信息', '{异常信息}', 1);
INSERT INTO `dfs_notice_field_info`(`id`, `code`, `name`, `placeholder`, `is_sys_field`) VALUES (27, 'exceptionExplain', '异常描述', '{异常描述}', 1);
INSERT INTO `dfs_notice_field_info`(`id`, `code`, `name`, `placeholder`, `is_sys_field`) VALUES (28, 'fname', '工位名称', '{工位名称}', 1);
INSERT INTO `dfs_notice_field_info`(`id`, `code`, `name`, `placeholder`, `is_sys_field`) VALUES (29, 'nickname', '操作人名称', '{操作人名称}', 1);
INSERT INTO `dfs_notice_field_info`(`id`, `code`, `name`, `placeholder`, `is_sys_field`) VALUES (30, 'iqcType', '生产呼叫类型', '{生产呼叫类型}', 1);
INSERT INTO `dfs_notice_field_info`(`id`, `code`, `name`, `placeholder`, `is_sys_field`) VALUES (31, 'remark', '备注', '{备注}', 1);
INSERT INTO `dfs_notice_field_info`(`id`, `code`, `name`, `placeholder`, `is_sys_field`) VALUES (32, 'productFlowCode', '条码编号', '{条码编号}', 1);
INSERT INTO `dfs_notice_field_info`(`id`, `code`, `name`, `placeholder`, `is_sys_field`) VALUES (33, 'craftCode', '工艺编号', '{工艺编号}', 1);
INSERT INTO `dfs_notice_field_info`(`id`, `code`, `name`, `placeholder`, `is_sys_field`) VALUES (34, 'craftName', '工艺名称', '{工艺名称}', 1);
INSERT INTO `dfs_notice_field_info`(`id`, `code`, `name`, `placeholder`, `is_sys_field`) VALUES (35, 'bomNum', 'bom编号', '{bom编号}', 1);
INSERT INTO `dfs_notice_field_info`(`id`, `code`, `name`, `placeholder`, `is_sys_field`) VALUES (36, 'deviceCode', '设备编号', '{设备编号}', 1);
INSERT INTO `dfs_notice_field_info`(`id`, `code`, `name`, `placeholder`, `is_sys_field`) VALUES (37, 'deviceName', '设备名称', '{设备名称}', 1);
INSERT INTO `dfs_notice_field_info`(`id`, `code`, `name`, `placeholder`, `is_sys_field`) VALUES (38, 'deviceMagNickName', '设备负责人', '{设备负责人}', 1);
INSERT INTO `dfs_notice_field_info`(`id`, `code`, `name`, `placeholder`, `is_sys_field`) VALUES (39, 'alarmDefinitionName', '告警名称', '{告警名称}', 1);
INSERT INTO `dfs_notice_field_info`(`id`, `code`, `name`, `placeholder`, `is_sys_field`) VALUES (40, 'alarmDefinitionCode', '告警ID', '{告警ID}', 1);
INSERT INTO `dfs_notice_field_info`(`id`, `code`, `name`, `placeholder`, `is_sys_field`) VALUES (41, 'alarmLevelName', '告警级别名称', '{告警级别名称}', 1);
INSERT INTO `dfs_notice_field_info`(`id`, `code`, `name`, `placeholder`, `is_sys_field`) VALUES (42, 'alarmReportTime', '告警上报时间', '{告警上报时间}', 1);
INSERT INTO `dfs_notice_field_info`(`id`, `code`, `name`, `placeholder`, `is_sys_field`) VALUES (43, 'alarmRecoveryTime', '告警恢复时间', '{告警恢复时间}', 1);
INSERT INTO `dfs_notice_field_info`(`id`, `code`, `name`, `placeholder`, `is_sys_field`) VALUES (44, 'alarmDealUserNickname', '告警处理人', '{告警处理人}', 1);
INSERT INTO `dfs_notice_field_info`(`id`, `code`, `name`, `placeholder`, `is_sys_field`) VALUES (45, 'alarmExplain', '告警说明', '{告警说明}', 1);
INSERT INTO `dfs_notice_field_info`(`id`, `code`, `name`, `placeholder`, `is_sys_field`) VALUES (46, 'saleOrderNumber', '销售订单号', '{销售订单号}', 1);
INSERT INTO `dfs_notice_field_info`(`id`, `code`, `name`, `placeholder`, `is_sys_field`) VALUES (47, 'productOrderNumber', '生产订单号', '{生产订单号}', 1);
INSERT INTO `dfs_notice_field_info`(`id`, `code`, `name`, `placeholder`, `is_sys_field`) VALUES (48, 'workOrderNumber', '工单号', '{工单号}', 1);
INSERT INTO `dfs_notice_field_info`(`id`, `code`, `name`, `placeholder`, `is_sys_field`) VALUES (49, 'operationOrder', '作业工单号', '{作业工单号}', 1);
INSERT INTO `dfs_notice_field_info`(`id`, `code`, `name`, `placeholder`, `is_sys_field`) VALUES (50, 'deliveryApplicationOrder', '出货申请单号', '{出货申请单号}', 1);
INSERT INTO `dfs_notice_field_info`(`id`, `code`, `name`, `placeholder`, `is_sys_field`) VALUES (51, 'stockDeliveryOrder', '发货单号', '{发货单号}', 1);
INSERT INTO `dfs_notice_field_info`(`id`, `code`, `name`, `placeholder`, `is_sys_field`) VALUES (52, 'purchaseRequestOrder', '采购需求单号', '{采购需求单号}', 1);
INSERT INTO `dfs_notice_field_info`(`id`, `code`, `name`, `placeholder`, `is_sys_field`) VALUES (53, 'purchaseCode', '采购订单号', '{采购订单号}', 1);
INSERT INTO `dfs_notice_field_info`(`id`, `code`, `name`, `placeholder`, `is_sys_field`) VALUES (54, 'purchaseOrder', '采购收料单号', '{采购收料单号}', 1);
INSERT INTO `dfs_notice_field_info`(`id`, `code`, `name`, `placeholder`, `is_sys_field`) VALUES (55, 'purchaseReceiptOrder', '委外订单号', '{委外订单号}', 1);
INSERT INTO `dfs_notice_field_info`(`id`, `code`, `name`, `placeholder`, `is_sys_field`) VALUES (56, 'subcontractOrder', '委外订单收料单号', '{委外订单收料单号}', 1);
INSERT INTO `dfs_notice_field_info`(`id`, `code`, `name`, `placeholder`, `is_sys_field`) VALUES (57, 'subcontractReceiptOrder', '生产入库单号', '{生产入库单号}', 1);
INSERT INTO `dfs_notice_field_info`(`id`, `code`, `name`, `placeholder`, `is_sys_field`) VALUES (58, 'productInOrder', '采购入库单号', '{采购入库单号}', 1);
INSERT INTO `dfs_notice_field_info`(`id`, `code`, `name`, `placeholder`, `is_sys_field`) VALUES (59, 'purchaseWarehousing', '采购退料出库单号', '{采购退料出库单号}', 1);
INSERT INTO `dfs_notice_field_info`(`id`, `code`, `name`, `placeholder`, `is_sys_field`) VALUES (60, 'purchaseReturnOutOrder', '销售出库单号', '{销售出库单号}', 1);
INSERT INTO `dfs_notice_field_info`(`id`, `code`, `name`, `placeholder`, `is_sys_field`) VALUES (61, 'salesOut', '销售退货入库单号', '{销售退货入库单号}', 1);
INSERT INTO `dfs_notice_field_info`(`id`, `code`, `name`, `placeholder`, `is_sys_field`) VALUES (62, 'productionQuantity', '生产数量', '{生产数量}', 1);
INSERT INTO `dfs_notice_field_info`(`id`, `code`, `name`, `placeholder`, `is_sys_field`) VALUES (63, 'salesQuantity', '销售数量', '{销售数量}', 1);
INSERT INTO `dfs_notice_field_info`(`id`, `code`, `name`, `placeholder`, `is_sys_field`) VALUES (64, 'createTime', '创建时间', '{创建时间}', 1);
INSERT INTO `dfs_notice_field_info`(`id`, `code`, `name`, `placeholder`, `is_sys_field`) VALUES (65, 'supplierCode', '供应商编号', '{供应商编号}', 1);
INSERT INTO `dfs_notice_field_info`(`id`, `code`, `name`, `placeholder`, `is_sys_field`) VALUES (66, 'supplierName', '供应商名称', '{供应商名称}', 1);
INSERT INTO `dfs_notice_field_info`(`id`, `code`, `name`, `placeholder`, `is_sys_field`) VALUES (67, 'approvalStateName', '审批状态', '{审批状态}', 1);
INSERT INTO `dfs_notice_field_info`(`id`, `code`, `name`, `placeholder`, `is_sys_field`) VALUES (68, 'approvalTime', '审批时间', '{审批时间}', 1);
INSERT INTO `dfs_notice_field_info`(`id`, `code`, `name`, `placeholder`, `is_sys_field`) VALUES (69, 'stateName', '状态', '{状态}', 1);
INSERT INTO `dfs_notice_field_info`(`id`, `code`, `name`, `placeholder`, `is_sys_field`) VALUES (70, 'stateChangeTime', '状态变更时间', '{状态变更时间}', 1);
INSERT INTO `dfs_notice_field_info`(`id`, `code`, `name`, `placeholder`, `is_sys_field`) VALUES (71, 'preState', '变更前状态', '{变更前状态}', 1);
INSERT INTO `dfs_notice_field_info`(`id`, `code`, `name`, `placeholder`, `is_sys_field`) VALUES (72, 'inspectionSheetTypeName', '送检单类型', '{送检单类型}', 1);
INSERT INTO `dfs_notice_field_info`(`id`, `code`, `name`, `placeholder`, `is_sys_field`) VALUES (73, 'inspectionSheetCode', '送检单编码', '{送检单编码}', 1);
INSERT INTO `dfs_notice_field_info`(`id`, `code`, `name`, `placeholder`, `is_sys_field`) VALUES (74, 'inspectionConclusion', '检验结论', '{检验结论}', 1);
INSERT INTO `dfs_notice_field_info`(`id`, `code`, `name`, `placeholder`, `is_sys_field`) VALUES (75, 'reviewConclusion', '复核结论', '{复核结论}', 1);
INSERT INTO `dfs_notice_field_info`(`id`, `code`, `name`, `placeholder`, `is_sys_field`) VALUES (76, 'approvalConclusion', '核准结论', '{核准结论}', 1);
INSERT INTO `dfs_notice_field_info`(`id`, `code`, `name`, `placeholder`, `is_sys_field`) VALUES (77, 'conclusionChangeTime', '结论变更时间', '{结论变更时间}', 1);
INSERT INTO `dfs_notice_field_info`(`id`, `code`, `name`, `placeholder`, `is_sys_field`) VALUES (78, 'inspectionPerson', '送检人', '{送检人}', 1);
INSERT INTO `dfs_notice_field_info`(`id`, `code`, `name`, `placeholder`, `is_sys_field`) VALUES (79, 'projectDefineCode', '项目编号', '{项目编号}', 1);
INSERT INTO `dfs_notice_field_info`(`id`, `code`, `name`, `placeholder`, `is_sys_field`) VALUES (80, 'projectDefineName', '项目名称', '{项目名称}', 1);
INSERT INTO `dfs_notice_field_info`(`id`, `code`, `name`, `placeholder`, `is_sys_field`) VALUES (81, 'taskCode', '任务编号', '{任务编号}', 1);
INSERT INTO `dfs_notice_field_info`(`id`, `code`, `name`, `placeholder`, `is_sys_field`) VALUES (82, 'taskName', '任务名称', '{任务名称}', 1);
INSERT INTO `dfs_notice_field_info`(`id`, `code`, `name`, `placeholder`, `is_sys_field`) VALUES (83, 'customerName', '客户名称', '{客户名称}', 1);
INSERT INTO `dfs_notice_field_info`(`id`, `code`, `name`, `placeholder`, `is_sys_field`) VALUES (84, 'materialStandard', '物料规格', '{物料规格}', 1);
INSERT INTO `dfs_notice_field_info`(`id`, `code`, `name`, `placeholder`, `is_sys_field`) VALUES (85, 'relateSaleOrderNumber', '关联销售订单编号', '{关联销售订单编号}', 1);
INSERT INTO `dfs_notice_field_info`(`id`, `code`, `name`, `placeholder`, `is_sys_field`) VALUES (86, 'orderCode', '订单编号', '{订单编号}', 1);

truncate table dfs_notice_type_placeholder;
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (1, 'saleOrderAddNotice', '{销售订单号}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (2, 'saleOrderAddNotice', '{客户编码}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (3, 'saleOrderAddNotice', '{创建时间}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (4, 'productOrderAddNotice', '{生产订单号}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (5, 'productOrderAddNotice', '{创建时间}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (6, 'workOrderAddNotice', '{工单号}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (7, 'workOrderAddNotice', '{创建时间}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (8, 'workOrderAddNotice', '{产线名称}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (9, 'workOrderAddNotice', '{生产订单号}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (10, 'workOrderAddNotice', '{物料名称}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (11, 'workOrderAddNotice', '{物料编号}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (12, 'workOrderAddNotice', '{工序名称}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (13, 'workOrderAddNotice', '{工作中心名称}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (14, 'workOrderAddNotice', '{基本生产单元名称}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (15, 'purchaseOrderAddNotice', '{采购订单号}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (16, 'purchaseOrderAddNotice', '{创建时间}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (17, 'workOrderApprovalNotice', '{工单号}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (18, 'workOrderApprovalNotice', '{审批状态}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (19, 'workOrderApprovalNotice', '{审批时间}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (20, 'workOrderApprovalNotice', '{产线名称}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (21, 'workOrderApprovalNotice', '{生产订单号}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (22, 'workOrderApprovalNotice', '{物料名称}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (23, 'workOrderApprovalNotice', '{物料编号}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (24, 'workOrderApprovalNotice', '{工序名称}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (25, 'workOrderApprovalNotice', '{工作中心名称}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (26, 'workOrderApprovalNotice', '{基本生产单元名称}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (27, 'approvePurchaseOrderNotice', '{采购订单号}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (28, 'approvePurchaseOrderNotice', '{审批状态}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (29, 'approvePurchaseOrderNotice', '{审批时间}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (30, 'workOrderStateChangeNotice', '{工单号}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (31, 'workOrderStateChangeNotice', '{状态}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (32, 'workOrderStateChangeNotice', '{变更前状态}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (33, 'workOrderStateChangeNotice', '{产线名称}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (34, 'workOrderStateChangeNotice', '{生产订单号}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (35, 'workOrderStateChangeNotice', '{物料名称}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (36, 'workOrderStateChangeNotice', '{物料编号}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (37, 'workOrderStateChangeNotice', '{工序名称}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (38, 'workOrderStateChangeNotice', '{工作中心名称}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (39, 'workOrderStateChangeNotice', '{基本生产单元名称}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (40, 'purchaseOrderStateChangeNotice', '{采购订单号}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (41, 'purchaseOrderStateChangeNotice', '{状态}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (42, 'purchaseOrderStateChangeNotice', '{变更前状态}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (43, 'designNotice', '{客户编码}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (44, 'designNotice', '{物料编号}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (45, 'designNotice', '{物料名称}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (46, 'designNotice', '{下单数量}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (47, 'designNotice', '{下单时间}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (48, 'designNotice', '{交货日期}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (49, 'returnMessageNotice', '{客户编码}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (50, 'returnMessageNotice', '{销售订单号}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (51, 'returnMessageNotice', '{物料编号}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (52, 'returnMessageNotice', '{物料名称}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (53, 'returnMessageNotice', '{退货数量}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (54, 'returnMessageNotice', '{退货原因}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (55, 'productQuantityLowerThanSalesQuantityNotice', '{工单号}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (56, 'productQuantityLowerThanSalesQuantityNotice', '{生产订单号}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (57, 'productQuantityLowerThanSalesQuantityNotice', '{生产数量}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (58, 'productQuantityLowerThanSalesQuantityNotice', '{产线名称}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (59, 'productQuantityLowerThanSalesQuantityNotice', '{工序名称}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (60, 'productQuantityLowerThanSalesQuantityNotice', '{销售订单号}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (61, 'productQuantityLowerThanSalesQuantityNotice', '{销售数量}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (62, 'productQuantityLowerThanSalesQuantityNotice', '{物料名称}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (63, 'productTimeOut', '{产线名称}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (64, 'productTimeOut', '{工单号}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (65, 'productTimeOut', '{生产订单号}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (66, 'productTimeOut', '{物料名称}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (67, 'productTimeOut', '{物料编号}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (68, 'productTimeOut', '{工序名称}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (69, 'productTimeOut', '{工作中心名称}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (70, 'productTimeOut', '{基本生产单元名称}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (71, 'alarmReportNotice', '{产线名称}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (72, 'alarmReportNotice', '{工单号}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (73, 'alarmReportNotice', '{异常处理人}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (74, 'alarmReportNotice', '{异常信息}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (75, 'alarmReportNotice', '{异常描述}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (76, 'alarmDealNotice', '{产线名称}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (77, 'alarmDealNotice', '{工单号}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (78, 'alarmDealNotice', '{异常处理人}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (79, 'alarmDealNotice', '{异常耗时}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (80, 'alarmDealNotice', '{异常信息}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (81, 'alarmDealNotice', '{异常描述}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (82, 'takeMaterial', '{工序名称}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (83, 'takeMaterial', '{前工序名称}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (84, 'takeMaterial', '{产线名称}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (85, 'takeMaterial', '{工单号}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (86, 'takeMaterial', '{生产订单号}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (87, 'takeMaterial', '{生产数量}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (88, 'takeMaterial', '{物料名称}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (89, 'takeMaterial', '{物料编号}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (90, 'takeMaterial', '{工作中心名称}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (91, 'takeMaterial', '{基本生产单元名称}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (92, 'takeMaterialTimeOut', '{工序名称}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (93, 'takeMaterialTimeOut', '{前工序名称}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (94, 'takeMaterialTimeOut', '{产线名称}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (95, 'takeMaterialTimeOut', '{工单号}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (96, 'takeMaterialTimeOut', '{生产订单号}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (97, 'takeMaterialTimeOut', '{物料名称}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (98, 'takeMaterialTimeOut', '{物料编号}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (99, 'takeMaterialTimeOut', '{工作中心名称}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (100, 'takeMaterialTimeOut', '{基本生产单元名称}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (101, 'sendMaterial', '{工序名称}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (102, 'sendMaterial', '{后工序名称}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (103, 'sendMaterial', '{产线名称}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (104, 'sendMaterial', '{工单号}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (105, 'sendMaterial', '{生产订单号}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (106, 'sendMaterial', '{生产数量}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (107, 'sendMaterial', '{物料名称}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (108, 'sendMaterial', '{物料编号}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (109, 'sendMaterial', '{工作中心名称}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (110, 'sendMaterial', '{基本生产单元名称}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (111, 'sendMaterialTimeOut', '{工序名称}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (112, 'sendMaterialTimeOut', '{前工序名称}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (113, 'sendMaterialTimeOut', '{产线名称}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (114, 'sendMaterialTimeOut', '{工单号}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (115, 'sendMaterialTimeOut', '{生产订单号}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (116, 'sendMaterialTimeOut', '{物料名称}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (117, 'sendMaterialTimeOut', '{物料编号}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (118, 'sendMaterialTimeOut', '{工作中心名称}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (119, 'sendMaterialTimeOut', '{基本生产单元名称}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (120, 'locationMachineNotice', '{产线名称}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (121, 'locationMachineNotice', '{变更前状态}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (122, 'locationMachineNotice', '{状态}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (123, 'locationMachineNotice', '{工序名称}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (124, 'locationMachineNotice', '{工单号}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (125, 'locationMachineNotice', '{生产订单号}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (126, 'locationMachineNotice', '{物料名称}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (127, 'locationMachineNotice', '{物料编号}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (128, 'locationMachineNotice', '{工作中心名称}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (129, 'locationMachineNotice', '{基本生产单元名称}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (130, 'badNewsNotice', '{工单号}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (131, 'badNewsNotice', '{生产订单号}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (132, 'badNewsNotice', '{销售订单号}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (133, 'badNewsNotice', '{物料编号}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (134, 'badNewsNotice', '{物料名称}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (135, 'badNewsNotice', '{工序名称}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (136, 'badNewsNotice', '{不良原因}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (137, 'badNewsNotice', '{不良数量}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (138, 'badNewsNotice', '{产线名称}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (139, 'productPatrolNotice', '{工单号}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (140, 'productPatrolNotice', '{送检员}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (141, 'inspectionFormAddNotice', '{送检人}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (142, 'inspectionFormAddNotice', '{工序}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (143, 'inspectionFormAddNotice', '{送检单号}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (144, 'inspectionFormAddNotice', '{产品名称}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (145, 'inspectionFormAddNotice', '{产品编码}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (146, 'inspectionFormAddNotice', '{创建时间}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (147, 'inspectionFormAddNotice', '{送检类型}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (148, 'inspectionFormAddNotice', '{关联工单}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (149, 'defectNumThresholdNotice', '{工单号}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (150, 'defectNumThresholdNotice', '{不良数量阈值}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (151, 'defectNumThresholdNotice', '{工序名称}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (152, 'defectNumThresholdNotice', '{物料编号}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (153, 'defectNumThresholdNotice', '{物料名称}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (154, 'defectRateThresholdNotice', '{工单号}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (155, 'defectRateThresholdNotice', '{不良率阈值}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (156, 'defectRateThresholdNotice', '{工序名称}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (157, 'defectRateThresholdNotice', '{物料编号}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (158, 'defectRateThresholdNotice', '{物料名称}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (159, 'concreteDefectNumThresholdNotice', '{工单号}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (160, 'concreteDefectNumThresholdNotice', '{不良名称}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (161, 'concreteDefectNumThresholdNotice', '{不良数量阈值}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (162, 'concreteDefectNumThresholdNotice', '{工序名称}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (163, 'concreteDefectNumThresholdNotice', '{物料编号}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (164, 'concreteDefectNumThresholdNotice', '{物料名称}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (165, 'manualConcreteDefectNotice', '{工单号}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (166, 'manualConcreteDefectNotice', '{不良数量}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (167, 'manualConcreteDefectNotice', '{不良名称}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (168, 'manualConcreteDefectNotice', '{工序名称}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (169, 'manualConcreteDefectNotice', '{物料编号}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (170, 'manualConcreteDefectNotice', '{物料名称}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (171, 'iqcNotice', '{产线名称}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (172, 'iqcNotice', '{工位名称}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (173, 'iqcNotice', '{工序名称}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (174, 'iqcNotice', '{物料编号}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (175, 'iqcNotice', '{物料名称}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (176, 'iqcNotice', '{生产订单号}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (177, 'iqcNotice', '{工单号}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (178, 'iqcNotice', '{操作人名称}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (179, 'iqcNotice', '{生产呼叫类型}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (180, 'iqcNotice', '{备注}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (181, 'inspectionFormStateChangeNotice', '{生产订单号}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (182, 'inspectionFormStateChangeNotice', '{工单号}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (183, 'inspectionFormStateChangeNotice', '{送检单类型}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (184, 'inspectionFormStateChangeNotice', '{送检单编码}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (185, 'inspectionFormStateChangeNotice', '{工序}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (186, 'inspectionFormStateChangeNotice', '{工单投产时间}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (187, 'inspectionFormStateChangeNotice', '{检验单生效时间}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (188, 'inspectionFormStateChangeNotice', '{物料编号}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (189, 'inspectionFormStateChangeNotice', '{物料名称}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (190, 'inspectionFormStateChangeNotice', '{状态}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (191, 'inspectionFormStateChangeNotice', '{变更前状态}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (192, 'inspectionFormStateChangeNotice', '{状态变更时间}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (193, 'inspectionFormStateChangeNotice', '{检验结论}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (194, 'inspectionFormStateChangeNotice', '{复核结论}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (195, 'inspectionFormStateChangeNotice', '{核准结论}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (196, 'inspectionFormStateChangeNotice', '{结论变更时间}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (197, 'inspectProcessChangeNotice', '{工单号}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (198, 'inspectProcessChangeNotice', '{送检单类型}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (199, 'inspectProcessChangeNotice', '{结论变更时间}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (200, 'inspectProcessChangeNotice', '{送检阶段}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (201, 'inspectProcessChangeNotice', '{检验结论}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (202, 'taskAddNotice', '{项目编号}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (203, 'taskAddNotice', '{项目名称}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (204, 'taskAddNotice', '{阶段编号}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (205, 'taskAddNotice', '{阶段名称}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (206, 'taskAddNotice', '{任务编号}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (207, 'taskAddNotice', '{任务名称}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (208, 'taskUpdateNotice', '{项目编号}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (209, 'taskUpdateNotice', '{项目名称}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (210, 'taskUpdateNotice', '{阶段编号}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (211, 'taskUpdateNotice', '{阶段名称}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (212, 'taskUpdateNotice', '{任务编号}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (213, 'taskUpdateNotice', '{任务名称}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (214, 'taskTimeNotice', '{项目编号}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (215, 'taskTimeNotice', '{项目名称}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (216, 'taskTimeNotice', '{阶段编号}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (217, 'taskTimeNotice', '{阶段名称}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (218, 'taskTimeNotice', '{任务编号}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (219, 'taskTimeNotice', '{任务名称}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (220, 'processAssemblyExpirationNotice', '{条码编号}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (221, 'processAssemblyExpirationNotice', '{物料编号}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (222, 'processAssemblyExpirationNotice', '{物料名称}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (223, 'processAssemblyExpirationNotice', '{理论寿命}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (224, 'processAssemblyExpirationNotice', '{寿命单位}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (225, 'processAssemblyExpirationNotice', '{实际使用寿命}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (226, 'processAssemblyExpirationNotice', '{寿命通知上限}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (227, 'badInspectionNotice', '{送检人}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (228, 'badInspectionNotice', '{送检单号}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (229, 'badInspectionNotice', '{物料名称}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (230, 'badInspectionNotice', '{物料编号}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (231, 'badInspectionNotice', '{创建时间}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (232, 'badInspectionNotice', '{送检单类型}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (233, 'craftApprovalNotice', '{工艺编号}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (234, 'craftApprovalNotice', '{工艺名称}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (235, 'craftApprovalNotice', '{物料编号}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (236, 'craftApprovalNotice', '{物料名称}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (237, 'craftApprovalNotice', '{审批时间}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (238, 'craftApprovalNotice', '{审批状态}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (239, 'bomApprovalNotice', '{bom编号}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (240, 'bomApprovalNotice', '{bom名称}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (241, 'bomApprovalNotice', '{审批时间}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (242, 'bomApprovalNotice', '{审批状态}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (243, 'materialApprovalNotice', '{物料编号}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (244, 'materialApprovalNotice', '{物料名称}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (245, 'materialApprovalNotice', '{审批时间}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (246, 'materialApprovalNotice', '{审批状态}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (247, 'saleOrderStateChangeNotice', '{订单编号}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (248, 'saleOrderStateChangeNotice', '{状态}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (249, 'saleOrderStateChangeNotice', '{客户编码}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (250, 'saleOrderStateChangeNotice', '{客户名称}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (251, 'productOrderStateChangeNotice', '{关联销售订单编号}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (252, 'productOrderStateChangeNotice', '{订单编号}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (253, 'productOrderStateChangeNotice', '{状态}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (254, 'productOrderStateChangeNotice', '{物料编号}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (255, 'productOrderStateChangeNotice', '{物料名称}');
INSERT INTO `dfs_notice_type_placeholder`(`id`, `notice_type_code`, `placeholder`) VALUES (256, 'productOrderStateChangeNotice', '{物料规格}');

-- 更新精制发送消息接口
update dfs_config_open_api set path = '/jingzhi/push/notificationMsg' where model = 'jingzhi' and interface_code = 'pushMessageToJz';

-- ================================================此脚本专为消息需求设定，如需写脚本，请移步到3.17.1.1=======================================================
-- ================================================此脚本专为消息需求设定，如需写脚本，请移步到3.17.1.1=======================================================
-- ================================================此脚本专为消息需求设定，如需写脚本，请移步到3.17.1.1=======================================================
-- ================================================此脚本专为消息需求设定，如需写脚本，请移步到3.17.1.1=======================================================
-- ================================================此脚本专为消息需求设定，如需写脚本，请移步到3.17.1.1=======================================================
