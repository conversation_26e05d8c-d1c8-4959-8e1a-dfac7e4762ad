-- 补充旧qms下推菜单，兼容旧版本下推
INSERT INTO `dfs_dict`(`id`, `code`, `name`, `type`, `url`, `unit`, `des`, `value`, `p_code`, `pid`, `create_time`, `update_time`, `create_by`, `update_by`, `quality_level`, `is_process_assembly`) VALUES (NULL, 'purchase.purchaseOrderPushDownConfig.supplierBom.jumpPage', '下推产品检验单(跳转新页面方式)', 'purchase.purchaseOrderPushDownConfig.supplierBom', NULL, NULL, NULL, 'show', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0);
INSERT INTO `dfs_dict`(`id`, `code`, `name`, `type`, `url`, `unit`, `des`, `value`, `p_code`, `pid`, `create_time`, `update_time`, `create_by`, `update_by`, `quality_level`, `is_process_assembly`) VALUES (NULL, 'custom', '自定义', 'purchase.purchaseOrderPushDownConfig.supplierBom', NULL, NULL, NULL, 'show', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0);
INSERT INTO `dfs_dict`(`id`, `code`, `name`, `type`, `url`, `unit`, `des`, `value`, `p_code`, `pid`, `create_time`, `update_time`, `create_by`, `update_by`, `quality_level`, `is_process_assembly`) VALUES (NULL, 'purchase.purchaseReceiptPushDownConfig.incomingInspection.jumpPage', '批量下推产品检验单', 'purchase.purchaseReceiptPushDownConfig.incomingInspection', NULL, NULL, NULL, 'show', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0);
INSERT INTO `dfs_dict`(`id`, `code`, `name`, `type`, `url`, `unit`, `des`, `value`, `p_code`, `pid`, `create_time`, `update_time`, `create_by`, `update_by`, `quality_level`, `is_process_assembly`) VALUES (NULL, 'custom', '自定义', 'purchase.purchaseReceiptPushDownConfig.incomingInspection', NULL, NULL, NULL, 'show', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0);
INSERT INTO `dfs_order_push_down_config`(`id`, `code`, `name`, `full_path_code`, `parent_full_path_code`, `description`, `create_by`, `update_by`, `create_time`, `update_time`, `is_inner`, `is_show`, `type_code`, `app_id`, `url`) VALUES (NULL, 'supplierBom', '产品检验', 'purchase.purchaseOrderPushDownConfig.supplierBom', 'purchase.purchaseOrderPushDownConfig', NULL, NULL, NULL, NULL, NULL, 1, 0, 'supplierBom', NULL, NULL);
INSERT INTO `dfs_order_push_down_config`(`id`, `code`, `name`, `full_path_code`, `parent_full_path_code`, `description`, `create_by`, `update_by`, `create_time`, `update_time`, `is_inner`, `is_show`, `type_code`, `app_id`, `url`) VALUES (NULL, 'incomingInspection', '产品检验', 'purchase.purchaseReceiptPushDownConfig.incomingInspection', 'purchase.purchaseReceiptPushDownConfig', NULL, NULL, NULL, NULL, NULL, 1, 0, 'incomingInspection', NULL, NULL);
INSERT INTO `dfs_order_push_down_item`(`id`, `config_path`, `code`, `name`, `type`, `instance_type`, `url`, `enable`, `is_inner`, `is_show`, `description`, `create_by`, `update_by`, `create_time`, `update_time`) VALUES (NULL, 'purchase.purchaseOrderPushDownConfig.supplierBom', NULL, '下推产品检验单(跳转新页面方式)', 'RULE', 'purchase.purchaseOrderPushDownConfig.supplierBom.jumpPage', NULL, 1, 1, 1, NULL, 'admin', 'admin', '2024-07-04 17:16:48', '2024-10-28 11:40:19');
INSERT INTO `dfs_order_push_down_item`(`id`, `config_path`, `code`, `name`, `type`, `instance_type`, `url`, `enable`, `is_inner`, `is_show`, `description`, `create_by`, `update_by`, `create_time`, `update_time`) VALUES (NULL, 'purchase.purchaseReceiptPushDownConfig.incomingInspection', NULL, '批量下推产品检验单', 'RULE', 'purchase.purchaseReceiptPushDownConfig.incomingInspection.jumpPage', NULL, 1, 1, 1, NULL, 'admin', 'admin', '2024-07-04 17:16:48', '2024-08-14 17:03:48');

INSERT INTO `dfs_order_push_down_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('originalOrderStates', '源单状态(多选)', 'purchase.purchaseOrderPushDownConfig.supplierBom.jumpPage.originalOrderStates', 'purchase.purchaseOrderPushDownConfig.supplierBom.jumpPage', 'select-multiple', 'api', '/ams/purchases/state', 'get', NULL, 'code,name', '[2,3]', '[2,3]', NULL, 'sourceConf');
INSERT INTO `dfs_order_push_down_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('openPage', '是否新建页面(单选)', 'purchase.purchaseOrderPushDownConfig.supplierBom.jumpPage.openPage', 'purchase.purchaseOrderPushDownConfig.supplierBom.jumpPage', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"是\"},{\"value\":false,\"label\":\"否\"}]', 'true', NULL, 'targetConf');
INSERT INTO `dfs_order_push_down_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('applicationId', 'applicationId', 'purchase.purchaseOrderPushDownConfig.supplierBom.jumpPage.applicationId', 'purchase.purchaseOrderPushDownConfig.supplierBom.jumpPage', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"com.yk.genieos.dfs.qualities-manage\"', NULL, 'sysConf');
INSERT INTO `dfs_order_push_down_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('url', '前端路由', 'purchase.purchaseOrderPushDownConfig.supplierBom.jumpPage.url', 'purchase.purchaseOrderPushDownConfig.supplierBom.jumpPage', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"/qms/qualities-manage/productinspection\"', NULL, 'sysConf');
INSERT INTO `dfs_order_push_down_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('sourceOrderType', '源单据类型编码', 'purchase.purchaseOrderPushDownConfig.supplierBom.jumpPage.sourceOrderType', 'purchase.purchaseOrderPushDownConfig.supplierBom.jumpPage', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"purchaseOrder\"', NULL, 'sysConf');
INSERT INTO `dfs_order_push_down_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('targetOrderType', '目标单据类型编码', 'purchase.purchaseOrderPushDownConfig.supplierBom.jumpPage.targetOrderType', 'purchase.purchaseOrderPushDownConfig.supplierBom.jumpPage', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"supplierBom\"', NULL, 'sysConf');

INSERT INTO `dfs_order_push_down_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('originalOrderStates', '原单状态(多选)', 'purchase.purchaseReceiptPushDownConfig.incomingInspection.jumpPage.originalOrderStates', 'purchase.purchaseReceiptPushDownConfig.incomingInspection.jumpPage', 'select-multiple', 'api', '/ams/receipt/state', 'get', NULL, 'code,name', '[2,3]', '[2,3]', NULL, 'sourceConf');
INSERT INTO `dfs_order_push_down_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('sheetOrderState', '产品检验单状态', 'purchase.purchaseReceiptPushDownConfig.incomingInspection.sheetOrderState', 'purchase.purchaseReceiptPushDownConfig.incomingInspection.jumpPage', 'select', 'api', '/qms/enums/get/status', 'get', NULL, 'code,name', '[1,2]', '1', NULL, 'targetConf');
INSERT INTO `dfs_order_push_down_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('inspectionType', '检验类型(单选)', 'purchase.purchaseReceiptPushDownConfig.incomingInspection.inspectionType', 'purchase.purchaseReceiptPushDownConfig.incomingInspection.jumpPage', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":\"material\",\"label\":\"按物料送检\"},{\"value\":\"batch\",\"label\":\"分批次送检\"}]', '\"material\"', NULL, 'targetConf');
INSERT INTO `dfs_order_push_down_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('applicationId', 'applicationId', 'purchase.purchaseReceiptPushDownConfig.incomingInspection.jumpPage.applicationId', 'purchase.purchaseReceiptPushDownConfig.incomingInspection.jumpPage', 'input', 'input', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'sysConf');
INSERT INTO `dfs_order_push_down_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('url', '前端路由', 'purchase.purchaseReceiptPushDownConfig.incomingInspection.jumpPage.url', 'purchase.purchaseReceiptPushDownConfig.incomingInspection.jumpPage', 'input', 'input', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'sysConf');
INSERT INTO `dfs_order_push_down_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('sourceOrderType', '源单据类型编码', 'purchase.purchaseReceiptPushDownConfig.incomingInspection.jumpPage.sourceOrderType', 'purchase.purchaseReceiptPushDownConfig.incomingInspection.jumpPage', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"purchaseReceipt\"', NULL, 'sysConf');
INSERT INTO `dfs_order_push_down_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('targetOrderType', '目标单据类型编码', 'purchase.purchaseReceiptPushDownConfig.incomingInspection.jumpPage.targetOrderType', 'purchase.purchaseReceiptPushDownConfig.incomingInspection.jumpPage', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"incomingInspection\"', NULL, 'sysConf');

INSERT INTO `dfs_order_push_down_config_value_dict`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('originalOrderStates', '源单状态(多选)', 'purchase.purchaseOrderPushDownConfig.supplierBom.jumpPage.originalOrderStates', 'purchase.purchaseOrderPushDownConfig.supplierBom.jumpPage', 'select-multiple', 'api', '/ams/purchases/state', 'get', NULL, 'code,name', '[2,3]', '[2,3]', NULL, 'sourceConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('openPage', '是否新建页面(单选)', 'purchase.purchaseOrderPushDownConfig.supplierBom.jumpPage.openPage', 'purchase.purchaseOrderPushDownConfig.supplierBom.jumpPage', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"是\"},{\"value\":false,\"label\":\"否\"}]', 'true', NULL, 'targetConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('applicationId', 'applicationId', 'purchase.purchaseOrderPushDownConfig.supplierBom.jumpPage.applicationId', 'purchase.purchaseOrderPushDownConfig.supplierBom.jumpPage', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"com.yk.genieos.dfs.qualities-manage\"', NULL, 'sysConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('url', '前端路由', 'purchase.purchaseOrderPushDownConfig.supplierBom.jumpPage.url', 'purchase.purchaseOrderPushDownConfig.supplierBom.jumpPage', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"/qms/qualities-manage/productinspection\"', NULL, 'sysConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('sourceOrderType', '源单据类型编码', 'purchase.purchaseOrderPushDownConfig.supplierBom.jumpPage.sourceOrderType', 'purchase.purchaseOrderPushDownConfig.supplierBom.jumpPage', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"purchaseOrder\"', NULL, 'sysConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('targetOrderType', '目标单据类型编码', 'purchase.purchaseOrderPushDownConfig.supplierBom.jumpPage.targetOrderType', 'purchase.purchaseOrderPushDownConfig.supplierBom.jumpPage', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"supplierBom\"', NULL, 'sysConf');

INSERT INTO `dfs_order_push_down_config_value_dict`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('originalOrderStates', '原单状态(多选)', 'purchase.purchaseReceiptPushDownConfig.incomingInspection.jumpPage.originalOrderStates', 'purchase.purchaseReceiptPushDownConfig.incomingInspection.jumpPage', 'select-multiple', 'api', '/ams/receipt/state', 'get', NULL, 'code,name', '[2,3]', '[2,3]', NULL, 'sourceConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('sheetOrderState', '产品检验单状态', 'purchase.purchaseReceiptPushDownConfig.incomingInspection.sheetOrderState', 'purchase.purchaseReceiptPushDownConfig.incomingInspection.jumpPage', 'select', 'api', '/qms/enums/get/status', 'get', NULL, 'code,name', '[1,2]', '1', NULL, 'targetConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('inspectionType', '检验类型(单选)', 'purchase.purchaseReceiptPushDownConfig.incomingInspection.inspectionType', 'purchase.purchaseReceiptPushDownConfig.incomingInspection.jumpPage', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":\"material\",\"label\":\"按物料送检\"},{\"value\":\"batch\",\"label\":\"分批次送检\"}]', '\"material\"', NULL, 'targetConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('applicationId', 'applicationId', 'purchase.purchaseReceiptPushDownConfig.incomingInspection.jumpPage.applicationId', 'purchase.purchaseReceiptPushDownConfig.incomingInspection.jumpPage', 'input', 'input', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'sysConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('url', '前端路由', 'purchase.purchaseReceiptPushDownConfig.incomingInspection.jumpPage.url', 'purchase.purchaseReceiptPushDownConfig.incomingInspection.jumpPage', 'input', 'input', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'sysConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('sourceOrderType', '源单据类型编码', 'purchase.purchaseReceiptPushDownConfig.incomingInspection.jumpPage.sourceOrderType', 'purchase.purchaseReceiptPushDownConfig.incomingInspection.jumpPage', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"purchaseReceipt\"', NULL, 'sysConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('targetOrderType', '目标单据类型编码', 'purchase.purchaseReceiptPushDownConfig.incomingInspection.jumpPage.targetOrderType', 'purchase.purchaseReceiptPushDownConfig.incomingInspection.jumpPage', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"incomingInspection\"', NULL, 'sysConf');

-- 关联绑定itemId
UPDATE `dfs_order_push_down_config_value` a,`dfs_order_push_down_item` b
SET a.`item_id` = b.`id`
WHERE a.`config_full_path_code` = b.`instance_type` and a.`item_id` is null and b.`instance_type` = 'purchase.purchaseOrderPushDownConfig.supplierBom.jumpPage';
-- 关联绑定itemId
UPDATE `dfs_order_push_down_config_value` a,`dfs_order_push_down_item` b
SET a.`item_id` = b.`id`
WHERE a.`config_full_path_code` = b.`instance_type` and a.`item_id` is null and b.`instance_type` = 'purchase.purchaseReceiptPushDownConfig.incomingInspection.jumpPage';


UPDATE `dfs_order_push_down_config` SET `service_name` = 'qms'
WHERE `full_path_code` in ('purchase.purchaseOrderPushDownConfig.supplierBom');
UPDATE `dfs_order_push_down_config` SET `service_name` = 'qms'
WHERE `full_path_code` in ('purchase.purchaseReceiptPushDownConfig.incomingInspection');
UPDATE `dfs_order_push_down_config` SET `service_name` = 'qms-product-inspection'
WHERE `full_path_code` in ('purchase.purchaseReceiptPushDownConfig.inspectOrder');

-- 更新销售订单表单配置，客户名称允许编辑
UPDATE `dfs_form_field_rule_config` SET `is_edit` = 1, `edit_gray` = 0, `need_gray` = 0 WHERE `field_code` = 'customerName' AND `full_path_code` = ('saleOrder.editByCreate');

