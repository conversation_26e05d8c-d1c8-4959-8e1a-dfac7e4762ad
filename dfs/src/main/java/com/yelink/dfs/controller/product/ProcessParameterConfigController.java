package com.yelink.dfs.controller.product;

import com.yelink.dfs.common.BaseController;
import com.yelink.dfs.entity.common.VersionChangeCraftProcedureDTO;
import com.yelink.dfs.entity.product.ProcedureProcessParameterEntity;
import com.yelink.dfs.entity.product.ProcessParameterConfigEntity;
import com.yelink.dfs.service.common.VersionChangeRecordService;
import com.yelink.dfs.service.product.ProcedureProcessParameterService;
import com.yelink.dfs.service.product.ProcessParameterConfigService;
import com.yelink.dfscommon.pojo.ResponseData;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/5/6 11:43
 */
@Slf4j
@RestController
@EqualsAndHashCode(callSuper = true)
@RequestMapping("/process/parameter/config")
public class ProcessParameterConfigController extends BaseController {

    @Resource
    private ProcedureProcessParameterService procedureProcessParameterService;
    @Resource
    private ProcessParameterConfigService processParameterConfigService;
    @Resource
    private VersionChangeRecordService versionChangeRecordService;

    /**
     * 获取工序的工艺参数列表（新增/默认项）
     *
     * @param craftProcedureId 工艺工序id
     * @return
     */
    @GetMapping("/default/list")
    public ResponseData getList(@RequestParam(value = "craftProcedureId") Integer craftProcedureId) {
        List<ProcedureProcessParameterEntity> list = procedureProcessParameterService.getListByProcedureCode(craftProcedureId);
        return ResponseData.success(list);
    }

    /**
     * 获取工艺参数配置列表
     *
     * @param craftProcedureId 工艺工序id
     * @param craftId          工艺id
     * @return
     */
    @GetMapping("/list")
    public ResponseData getConfigList(@RequestParam(value = "craftProcedureId") Integer craftProcedureId,
                                      @RequestParam(value = "craftId") Integer craftId) {
        List<ProcessParameterConfigEntity> list = processParameterConfigService.getConfigList(craftProcedureId, craftId);
        return success(list);
    }

    /**
     * 批量导入工艺参数配置默认值
     *
     * @param configEntities
     * @return
     */
    @PostMapping("/add/batch")
    public ResponseData saveConfig(@RequestBody List<ProcessParameterConfigEntity> configEntities) {
        boolean b = processParameterConfigService.saveBatchConfig(configEntities);
        if(b) {
            configEntities.stream().map(ProcessParameterConfigEntity::getCraftProcedureId).distinct().forEach(craftProcedureId -> {
                versionChangeRecordService.changeVersionCraftProcedure(VersionChangeCraftProcedureDTO.builder()
                        .description("参数 - 恢复默认")
                        .craftProcedureId(craftProcedureId)
                        .build()
                );
            });
        }
        return ResponseData.success(b);
    }

    /**
     * 新增工艺参数配置
     *
     * @param configEntity
     * @return
     */
    @PostMapping("/add")
    public ResponseData saveConfig(@RequestBody ProcessParameterConfigEntity configEntity) {
        processParameterConfigService.saveConfig(configEntity);
        versionChangeRecordService.changeVersionCraftProcedure(VersionChangeCraftProcedureDTO.builder()
                .description("参数 - 新增")
                .craftProcedureId(configEntity.getCraftProcedureId())
                .build()
        );
        return ResponseData.success();
    }


    /**
     * 批量修改工艺参数配置
     *
     * @param configEntities
     * @return
     */
    @PutMapping("/update/batch")
    public ResponseData updateProcedureInspectionConfig(@RequestBody List<ProcessParameterConfigEntity> configEntities) {
        boolean b = processParameterConfigService.updateBatchConfig(configEntities);
        if(b) {
            configEntities.stream().map(ProcessParameterConfigEntity::getCraftProcedureId).distinct().forEach(craftProcedureId -> {
                versionChangeRecordService.changeVersionCraftProcedure(VersionChangeCraftProcedureDTO.builder()
                        .description("参数 - 更新")
                        .craftProcedureId(craftProcedureId)
                        .build()
                );
            });
        }
        return success(b);
    }


    /**
     * 删除工艺参数配置
     *
     * @param id
     * @returnq
     */
    @DeleteMapping("/remove/{id}")
    public ResponseData removeProcedureInspectionConfig(@PathVariable Integer id) {
        ProcessParameterConfigEntity one = processParameterConfigService.getById(id);
        boolean b = processParameterConfigService.removeById(id);
        if(b) {
            versionChangeRecordService.changeVersionCraftProcedure(VersionChangeCraftProcedureDTO.builder()
                    .description("参数 - 删除")
                    .craftProcedureId(one.getCraftProcedureId())
                    .build()
            );
        }
        return success(b);
    }


    /**
     * 获取工艺参数配置详情
     *
     * @param id
     * @return
     */
    @GetMapping("/detail/{id}")
    public ResponseData getDetail(@PathVariable Integer id) {
        ProcessParameterConfigEntity entity = processParameterConfigService.getById(id);
        return success(entity);
    }

}
