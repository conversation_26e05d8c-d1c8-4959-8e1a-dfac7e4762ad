package com.yelink.dfs.controller.screen;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yelink.dfs.common.BaseController;
import com.yelink.dfs.constant.OperationType;
import com.yelink.dfs.entity.screen.ScreenConfigEntity;
import com.yelink.dfs.service.screen.ScreenConfigService;
import com.yelink.dfscommon.annotation.OperLog;
import com.yelink.dfscommon.pojo.ResponseData;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;

/**
 * <AUTHOR>
 * @description 大屏配置
 * @Date 2021/9/8
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/screens/config")
public class ScreenConfigController extends BaseController {

    private ScreenConfigService screenConfigService;

    /**
     * 添加
     */
    @PostMapping("/add")
    @OperLog(module = "大屏管理", type = OperationType.ADD, desc = "新增了名称为#{screenName}的大屏")
    public ResponseData add(@RequestBody ScreenConfigEntity entity) {
        String username = getUsername();
        entity.setCreateBy(username);
        entity.setCreateTime(new Date());
        entity.setUpdateBy(username);
        entity.setUpdateTime(new Date());
        screenConfigService.save(entity);
        return success();
    }

    /**
     * 更新
     */
    @PutMapping("/update")
    @OperLog(module = "大屏管理", type = OperationType.UPDATE, desc = "修改了名称为#{screenName}的大屏")
    public ResponseData update(@RequestBody ScreenConfigEntity entity) {
        String username = getUsername();
        entity.setUpdateBy(username);
        entity.setUpdateTime(new Date());
        screenConfigService.updateById(entity);
        return success();
    }

    /**
     * 获取列表
     */
    @GetMapping("/list")
    public ResponseData getList(@RequestParam(value = "current", defaultValue = "1") Integer current,
                                @RequestParam(value = "size", defaultValue = "20") Integer size) {
        Page<ScreenConfigEntity> page = screenConfigService.getList(current, size);
        return success(page);
    }

    /**
     * 删除
     */
    @DeleteMapping("/delete/{id}")
    @OperLog(module = "大屏管理", type = OperationType.DELETE, desc = "删除了名称为#{screenName}的大屏")
    public ResponseData removeEntity(@PathVariable(value = "id") Integer id) {
        screenConfigService.removeById(id);
        return success();
    }

    /**
     * 获取详情
     */
    @GetMapping("/detail/{id}")
    public ResponseData detail(@PathVariable(value = "id") Integer id) {
        ScreenConfigEntity entity = screenConfigService.getById(id);
        return success(entity);
    }

}
