package com.yelink.dfs.controller.common;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yelink.dfs.common.BaseController;
import com.yelink.dfs.constant.OperationType;
import com.yelink.dfs.constant.rule.AutoIncrementConfigureTypeEnum;
import com.yelink.dfs.constant.rule.RulePrefixDTO;
import com.yelink.dfs.constant.rule.RulePrefixEnum;
import com.yelink.dfs.entity.order.RuleSeqEntity;
import com.yelink.dfs.entity.rule.RuleTypeConfigEntity;
import com.yelink.dfs.entity.rule.RuleTypeParentConfig;
import com.yelink.dfs.service.order.RuleSeqService;
import com.yelink.dfs.service.rule.NumberRuleService;
import com.yelink.dfs.service.rule.RuleTypeConfigService;
import com.yelink.dfs.service.rule.RuleTypeParentConfigService;
import com.yelink.dfs.vo.rule.RuleTypeTreeVO;
import com.yelink.dfscommon.annotation.OperLog;
import com.yelink.dfscommon.constant.dfs.code.ProductFlowCodeTypeEnum;
import com.yelink.dfscommon.constant.dfs.rule.NumberCodeDTO;
import com.yelink.dfscommon.constant.dfs.rule.NumberRulesConfigEntity;
import com.yelink.dfscommon.dto.dfs.RuleSeqDTO;
import com.yelink.dfscommon.pojo.ResponseData;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.yelink.dfs.constant.Constant.BAR_CODE_NUMBER;

/**
 * @Description: 编码生成规则
 * * @Author: yijx
 * * @Date: 2021/9/24
 */

@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/numberRule")
public class NumberRuleController extends BaseController {
    private NumberRuleService numberRuleService;
    private RuleSeqService ruleSeqService;
    private RuleTypeConfigService ruleTypeConfigService;
    private RuleTypeParentConfigService ruleTypeParentConfigService;

    /**
     * 批量新增该编码规则类型下的规则信息
     * （按xx归一：组成信息中有此字段才可按该字段归一，例如按供应商归一，需要有供应商的组成信息，否则会编号重复）
     *
     * @param numberRuleList 编码规则实例
     * @return
     */
    @PostMapping("/batch/update")
    public ResponseData batchAddOrUpdate(@RequestBody List<NumberRulesConfigEntity> numberRuleList) {
        numberRuleService.batchAddOrUpdate(numberRuleList, getUsername());
        return success();
    }

    /**
     * 新增编码规则
     */
    @PostMapping("/add")
    public ResponseData add(@RequestBody NumberRulesConfigEntity numberRule) {
        numberRule.setCreateTime(new Date());
        numberRule.setCreateBy(getUsername());
        numberRuleService.save(numberRule);
        return success();
    }

    /**
     * 编辑编码规则
     */
    @PostMapping("/update")
    public ResponseData update(@RequestBody NumberRulesConfigEntity numberRule) {
        numberRule.setUpdateTime(new Date());
        numberRule.setUpdateBy(getUsername());
        numberRuleService.updateById(numberRule);
        return success();
    }

    /**
     * 删除编码规则
     */
    @DeleteMapping("/delete")
    public ResponseData delete(@RequestParam("id") Integer id) {
        numberRuleService.removeById(id);
        return success();
    }

    /**
     * 按类型查询编码规则
     */
    @GetMapping("/getDetailByType")
    public ResponseData getByType(@RequestParam("typeCode") String typeCode) {
        List<NumberRulesConfigEntity> list = numberRuleService.getByTypeCode(typeCode);
        return success(list);
    }


    /**
     * 流水码按类型查询对应类型的编码规则
     */
    @GetMapping("/getDetailByCodeType/{type}")
    public ResponseData getDetailByCodeType(@PathVariable Integer type) {
        List<NumberRulesConfigEntity> list = null;
        if (type == ProductFlowCodeTypeEnum.PRODUCT_FLOW_CODE.getCode()) {
            RuleTypeConfigEntity config = ruleTypeConfigService.getByType("12"); // 生产工单流转码
            if (config != null) {
                list = numberRuleService.getByTypeCode(config.getType());
            }
        } else if (type == ProductFlowCodeTypeEnum.FINISHED_PRODUCT_CODE.getCode()) {
            RuleTypeConfigEntity config = ruleTypeConfigService.getByType("14"); // 生产工单成品码
            if (config != null) {
                list = numberRuleService.getByTypeCode(config.getType());
            }
        } else if (type == ProductFlowCodeTypeEnum.PURCHASE_PRODUCT_CODE.getCode()) {
            RuleTypeConfigEntity config = ruleTypeConfigService.getByType("22"); // 采购单品码
            if (config != null) {
                list = numberRuleService.getByTypeCode(config.getType());
            }
        }
        if (CollectionUtils.isEmpty(list)) {
            return fail("没有对应的编码规则");
        }
        return success(list);
    }


    /**
     * 查询所有编码规则的枚举类型
     */
    @GetMapping("/getType")
    public ResponseData getType() {
        List<RuleTypeConfigEntity> ruleTypes = ruleTypeConfigService.getAllRuleTypes();
        Map<String, String> map = new LinkedHashMap<>();
        for (RuleTypeConfigEntity ruleType : ruleTypes) {
            map.put(ruleType.getModuleName(), ruleType.getType());
        }
        return success(map);
    }

    /**
     * 根据编码规则类型查询所有对应的组成信息
     * 不传，则传所有的组成信息
     *
     * @param ruleType 编码规则类型
     */
    @GetMapping("/get/rules")
    public ResponseData getRules(@RequestParam(value = "ruleType", required = false) String ruleType) {
        List<RulePrefixDTO> list = numberRuleService.getRulesEnums(ruleType);
        return success(list);
    }

    /**
     * 查询所有编码规则
     */
    @GetMapping("/getAll")
    public ResponseData getAll(@RequestParam(value = "current", required = false) Integer current,
                             @RequestParam(value = "size", required = false) Integer size,
                             @RequestParam(value = "ruleType", required = false) String ruleType) {
        Page<NumberRulesConfigEntity> all = numberRuleService.getAll(current, size, ruleType);
        return success(all);
    }

    /**
     * 根据编码规则实例生成编号
     *
     * @param dto id --编码规则id
     *            <p>
     *            materialCode --物料编码
     *            <p>
     *            relatedMap --归一方式查询的关联单据对象
     *            key为关联单号类型
     *            value为关联单号（可以是生产工单号、销售订单号、制造单元类型编号、制造单元编号。。。多种单号）
     *            <p>
     *            numberMap --前端传递的编码（可以是生产工单号、销售订单号、制造单元类型编号、制造单元编号。。。多种单号），用于组成编码信息
     *            key为编码规则类型
     *            value为编码号
     *            numberMap 相关枚举 {@link RulePrefixEnum}
     *            relatedMap 相关枚举{@link AutoIncrementConfigureTypeEnum }
     */
    @PostMapping("/getSeqByType")
    @OperLog(module = "编码规则", type = OperationType.ADD, desc = "#{ruleTypeName}新增了编码#{code}")
    public ResponseData getSeqById(@RequestBody RuleSeqDTO dto) {
        NumberCodeDTO numberCodeDTO = numberRuleService.getSeqById(dto);
        if (numberCodeDTO == null) {
            return fail("生成编号失败，请检查编码规则配置");
        }
        return success(numberCodeDTO);
    }

    /**
     * 根据id查询
     */
    @GetMapping("/getDetailById")
    public ResponseData getById(@RequestParam("id") Integer id) {
        NumberRulesConfigEntity numberRule = numberRuleService.get(id);
        return success(numberRule);
    }

    /**
     * 返回8位数不良品条码编号
     */
    @GetMapping("/get/barcode/num")
    public ResponseData get(@RequestParam("num") Integer num) {
        List<String> numList = new ArrayList<>();
        QueryWrapper<RuleSeqEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(RuleSeqEntity::getRuleType, BAR_CODE_NUMBER);
        RuleSeqEntity one = ruleSeqService.getOne(queryWrapper);
        if (one == null) {
            RuleSeqEntity ruleSeqEntity = new RuleSeqEntity();
            ruleSeqEntity.setSeq(1);
            ruleSeqEntity.setRuleType(BAR_CODE_NUMBER);
            ruleSeqEntity.setCreateTime(new Date());
            ruleSeqService.save(ruleSeqEntity);
        }
        return numberRuleService.getBarCodeLoke(queryWrapper, num, numList);
    }

    /**
     * 根据编码规则类型查询所有对应的归一信息，不传则默认获取所有的归一信息
     */
    @GetMapping("/config/list")
    public ResponseData getConfigList(@RequestParam(value = "ruleType") String ruleType) {
        return success(ruleType == null ? AutoIncrementConfigureTypeEnum.getAllEnum() : AutoIncrementConfigureTypeEnum.getEnumByRuleType(ruleType));
    }

    /**
     * 查询编码规则树
     */
    @GetMapping("/tree")
    public ResponseData getRuleTypeTree() {
        List<RuleTypeTreeVO> treeList = ruleTypeParentConfigService.getRuleTypeTree();
        return success(treeList);
    }
}
