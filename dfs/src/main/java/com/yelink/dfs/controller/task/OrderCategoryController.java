package com.yelink.dfs.controller.task;

import com.yelink.dfs.common.BaseController;
import com.yelink.dfs.service.task.OrderCategoryService;
import com.yelink.dfscommon.constant.ParamException;
import com.yelink.dfs.entity.task.OrderCategoryEntity;
import com.yelink.dfs.entity.task.dto.OrderCategoryDeleteDTO;
import com.yelink.dfs.entity.task.dto.OrderCategoryInsertDTO;
import com.yelink.dfs.entity.task.dto.OrderCategoryUpdateDTO;
import com.yelink.dfscommon.pojo.ResponseData;
import com.yelink.dfscommon.utils.JacksonUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


/**
 * @Description:
 * @Author: zengzhengfu
 * @Date: 2024/12/5
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/order_category")
public class OrderCategoryController extends BaseController {

    private OrderCategoryService orderCategoryService;

    /**
     * 查询单据信息
     *
     * @param entity
     * @return
     */
    @PostMapping("/list")
    public ResponseData getForm(@RequestBody OrderCategoryEntity entity) {
        List<OrderCategoryEntity> list = orderCategoryService.getList(entity);
        return ResponseData.success(list);
    }

    /**
     * 新增单据信息
     *
     * @param entity
     * @return
     */
    @PostMapping("/add")
    public ResponseData addFrom(@RequestBody @Validated OrderCategoryInsertDTO dto, BindingResult bindingResult) {
        if (bindingResult.hasErrors()) {
            throw new ParamException(bindingResult);
        }
        OrderCategoryEntity entity = JacksonUtil.convertObject(dto, OrderCategoryEntity.class);
        orderCategoryService.add(entity);
        return ResponseData.success(entity);
    }

    /**
     * 更新单据信息
     *
     * @param dto
     * @return
     */
    @PostMapping("/update")
    public ResponseData updateFrom(@RequestBody @Validated OrderCategoryUpdateDTO dto, BindingResult bindingResult) {
        if (bindingResult.hasErrors()) {
            throw new ParamException(bindingResult);
        }
        OrderCategoryEntity entity = JacksonUtil.convertObject(dto, OrderCategoryEntity.class);
        orderCategoryService.updateByCategory(entity);
        return ResponseData.success(entity);
    }

    /**
     * 删除单据信息
     *
     * @param dto
     * @return
     */
    @DeleteMapping("/update")
    public ResponseData deleteFrom(@RequestBody @Validated OrderCategoryDeleteDTO dto, BindingResult bindingResult) {
        if (bindingResult.hasErrors()) {
            throw new ParamException(bindingResult);
        }
        orderCategoryService.deleteByOrderCategory(dto.getOrderCategory());
        return ResponseData.success();
    }
}
