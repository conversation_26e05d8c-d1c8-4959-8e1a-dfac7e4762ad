package com.yelink.dfs.controller.meta;

import com.yelink.dfs.entity.meta.dto.AutoCheckedBoxDTO;
import com.yelink.dfs.service.meta.MetaService;
import com.yelink.dfscommon.pojo.ResponseData;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/meta")
public class MetaController {

    @Resource
    private MetaService metaService;

    /**
     * 获取分组列表
     * 注意初始状态必须全部勾上，因为触发自动的时候不会先校验勾选前数据的正确性
     * @return 分组名称 : 功能点列表
     */
    @GetMapping("/groupList")
    public ResponseData groupList() {
        return ResponseData.success(metaService.groupList());
    }

    /**
     * 自动勾选
     * @param autoCheckedBoxDTO 请求入参
     * @return  所有需要保留的功能点(包含之前已经勾选的)
     */
    @PostMapping("/autoCheckBox")
    public ResponseData autoCheckBox(@RequestBody AutoCheckedBoxDTO autoCheckedBoxDTO) {
        return ResponseData.success(metaService.autoCheckBox(autoCheckedBoxDTO));
    }

    /**
     * 试运行清理数据， 即清理未勾选的项
     * @param checkedDefIdList 前端勾选的功能点的id
     */
    @PostMapping("/flushNoCheckedMetaTable")
    public ResponseData flushNoCheckedMetaTable(@RequestBody List<Integer> checkedDefIdList) {
        metaService.flushNoCheckedMetaTable(checkedDefIdList);
        return ResponseData.success();
    }

    /**
     * 获取任务进度
     * @return 进度
     */
    @GetMapping("/getTaskProcess")
    public ResponseData getTaskProcess() {
        return ResponseData.success(metaService.getTaskProcess());
    }
}
