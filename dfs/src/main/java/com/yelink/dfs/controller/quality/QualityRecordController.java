package com.yelink.dfs.controller.quality;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yelink.dfs.common.BaseController;
import com.yelink.dfs.entity.order.RecordWorkOrderCountEntity;
import com.yelink.dfs.entity.order.WorkOrderEntity;
import com.yelink.dfs.entity.order.dto.AbnormalTypeDTO;
import com.yelink.dfs.service.order.RecordWorkOrderCountService;
import com.yelink.dfscommon.constant.ParamException;
import com.yelink.dfscommon.pojo.ResponseData;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR>
 * 质量管理
 * @Date 2021/7/27 15:16
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/records")
public class QualityRecordController extends BaseController {
    private RecordWorkOrderCountService countService;

    /**
     * 质量记录列表
     *
     * @param workOrderNumber
     * @param materialCode
     * @param startTime
     * @param endTime
     * @param current
     * @param size
     * @return
     */
    @GetMapping("/list")
    public ResponseData getList(@RequestParam(value = "workOrderNumber", required = false) String workOrderNumber,
                                @RequestParam(value = "materialCode", required = false) String materialCode,
                                @RequestParam(value = "lineId", required = false) Integer lineId,
                                @RequestParam(value = "startTime", required = false) String startTime,
                                @RequestParam(value = "endTime", required = false) String endTime,
                                @RequestParam(value = "current", required = false) Integer current,
                                @RequestParam(value = "size", required = false) Integer size) {
        Page<RecordWorkOrderCountEntity> list = countService.getQualityAnalysis(workOrderNumber, materialCode, null, lineId, startTime, endTime, current, size);
        return ResponseData.success(list);
    }

    /**
     * 修改（返修良品数、最终良率、最终不良率）
     */
    @PutMapping("/update")
    public ResponseData editRepairRecords(@RequestBody RecordWorkOrderCountEntity entity,
                                          BindingResult bindingResult) {
        if (bindingResult.hasErrors()) {
            throw new ParamException(bindingResult);
        }
        RecordWorkOrderCountEntity result = countService.editRepairRecords(entity);
        return ResponseData.success(result);
    }


    /**
     * 质量记录详情
     *
     * @param
     * @return
     */
    @GetMapping("/detail/{id}")
    public ResponseData getDetail(@PathVariable Integer id) {
        List<AbnormalTypeDTO> list = countService.getDetail(id);
        return ResponseData.success(list);
    }


    /**
     * 获取筛选条件的工单(有产线的工单)
     */
    @GetMapping("/order/list")
    public ResponseData getWorkOrderList() {
        List<WorkOrderEntity> list = countService.getLineNotNullWorkOrderList();
        return ResponseData.success(list);
    }


    /**
     * 品质报表导出
     *
     * @param workOrderNumber
     * @param materialCode
     * @param lineId
     * @param startTime
     * @param endTime
     * @param response
     */
    @GetMapping("/export")
    public void Export(@RequestParam(value = "workOrderNumber", required = false) String workOrderNumber,
                       @RequestParam(value = "materialCode", required = false) String materialCode,
                       @RequestParam(value = "lineId", required = false) Integer lineId,
                       @RequestParam(value = "startTime", required = false) String startTime,
                       @RequestParam(value = "endTime", required = false) String endTime,
                       HttpServletResponse response) throws IOException {
        countService.export(workOrderNumber, materialCode, null, lineId, startTime, endTime, response);
    }


}
