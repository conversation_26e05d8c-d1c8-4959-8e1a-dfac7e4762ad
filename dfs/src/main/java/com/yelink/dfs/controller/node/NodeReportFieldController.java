package com.yelink.dfs.controller.node;

import com.yelink.dfs.common.BaseController;
import com.yelink.dfs.entity.node.NodeProcessReportRecordEntity;
import com.yelink.dfs.service.node.NodeExtendService;
import com.yelink.dfscommon.pojo.ResponseData;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/4/24 16:08
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/node/filed")
public class NodeReportFieldController extends BaseController {

    private NodeExtendService nodeExtendService;

    /**
     * 通过数据源获取数据列表
     *
     * @return
     */
    @PostMapping("/values")
    public ResponseData getFledValues(@RequestParam(value = "dataSource") String dataSource,
                                      @RequestBody NodeProcessReportRecordEntity entity) {
        if (StringUtils.isBlank(dataSource)) {
            return fail("参数为空");
        }
        List<String> values = nodeExtendService.getFledValues(dataSource, entity);
        return ResponseData.success(values);
    }


}
