package com.yelink.dfs.controller.defect;

import com.yelink.dfs.common.BaseController;
import com.yelink.dfs.entity.code.ProductFlowCodeEntity;
import com.yelink.dfs.entity.maintain.dto.MaintainRecordDTO;
import com.yelink.dfs.entity.maintain.dto.MaintainSchemePadDTO;
import com.yelink.dfs.service.code.ProductFlowCodeService;
import com.yelink.dfs.service.code.ScannerService;
import com.yelink.dfs.service.maintain.MaintainRecordService;
import com.yelink.dfs.service.maintain.MaintainSchemeService;
import com.yelink.dfscommon.pojo.ResponseData;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @description 设备日历
 * @Date 2021/05/24
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/maintain")
public class MaintainPadController extends BaseController {

    private MaintainSchemeService schemeService;
    private MaintainRecordService recordService;
    private ScannerService scannerService;
    private ProductFlowCodeService productFlowCodeService;

    /**
     * 根据工位id查询维修方案
     *
     * @param craftProcedureId 工艺工序id  如果有传工艺工序id优先取工序质检方案
     * @return
     */
    @GetMapping("/get/scheme/{fid}")
    public ResponseData getSchemeByFid(@PathVariable Integer fid, @RequestParam(required = false) Integer craftProcedureId) {
        MaintainSchemePadDTO padDTO = schemeService.getSchemeByFid(fid, craftProcedureId);
        return success(padDTO);
    }
    /**
     * 根据维修方案id查询维修方案
     *
     * @param schemeId 维修方案id
     * @return
     */
    @GetMapping("/get/scheme/by/id/{schemeId}")
    public ResponseData getSchemeBySchemeId(@PathVariable Integer schemeId) {
        MaintainSchemePadDTO padDTO = schemeService.getSchemePadDTO(schemeId);
        return success(padDTO);
    }
    /**
     * 保存移动端提交的记录
     *
     * @return
     */
    @PostMapping("/save/record")
    public ResponseData saveRecord(@RequestBody MaintainRecordDTO dto) {
        String username = getUsername();
        ProductFlowCodeEntity productFlowCodeEntity = productFlowCodeService.getByProductFlowCode(dto.getBarCode());
        if (productFlowCodeEntity != null) {
            scannerService.addCodeRecordMaintainAndRefreshReport(dto.getBarCode(), dto.getFid(), username, dto.getMaintianType(), dto.getBackCraftProcedureId(), dto.getCraftProcedureId(), null,dto.getTeamId());
        }
        recordService.saveRecord(dto, username);
        return success();
    }


}
