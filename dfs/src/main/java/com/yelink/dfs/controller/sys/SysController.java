package com.yelink.dfs.controller.sys;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.yelink.dfs.common.BaseController;
import com.yelink.dfs.constant.sys.AppletNameEnum;
import com.yelink.dfs.constant.sys.OrderCodeNameEnum;
import com.yelink.dfs.entity.sys.SysProvinceEntity;
import com.yelink.dfs.entity.sys.SysRouteEntity;
import com.yelink.dfs.open.v1.aksk.dto.OpenApiDTO;
import com.yelink.dfs.service.open.OpenApiConfigService;
import com.yelink.dfs.service.sys.SysProvinceService;
import com.yelink.dfs.service.sys.SysRouteService;
import com.yelink.dfs.service.sys.SysService;
import com.yelink.dfscommon.constant.OpenApiEnum;
import com.yelink.dfscommon.constant.RespCodeEnum;
import com.yelink.dfscommon.constant.ResponseException;
import com.yelink.dfscommon.constant.dfs.CompatorStateEnumDTO;
import com.yelink.dfscommon.pojo.ResponseData;
import com.yelink.inner.common.AppsService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 数据库表操作处理
 * @Date 2021/4/25
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/sys")
public class SysController extends BaseController {

    private SysService sysService;
    private AppsService appsService;
    private SysRouteService sysRouteService;
    private SysProvinceService sysProvinceService;
    private OpenApiConfigService openApiConfigService;

    /**
     * 判断是否开启frpc
     */
    @GetMapping("/frpc/state")
    public ResponseData frpcStatue() throws Exception {
        return success(/*sysService.frpcState()*/);
    }


    /**
     * 授权开启/关闭远程操作
     * operation  open-打开  close-关闭
     */
    @PostMapping("/switch/remote")
    public ResponseData switchRemote(@RequestParam("operation") String operation) throws Exception {
//        sysService.switchRemote(operation);
        return success();
    }

    /**
     * 获取dfs版本
     */
    @GetMapping("/dfs/version")
    public ResponseData dfsVersion() {
        return success((Object) sysService.dfsVersion());
    }

    /**
     * 获取ams版本
     */
    @GetMapping("/ams/version")
    public ResponseData amsVersion() {
        return success(sysService.amsVersion());
    }

    /**
     * 获取服务版本
     */
    @GetMapping("/service/version")
    public ResponseData serviceVersion(@RequestParam String imageName) {
        return success(sysService.serviceVersion(imageName));
    }

    /**
     * 获取所有配置的服务版本
     */
    @GetMapping("/config/service/version")
    public ResponseData configServiceVersion() {
        return success(sysService.configServiceVersion());
    }

    /**
     * 系统状态更改（true-运营  false-试运营）
     * gai
     *
     * @param state
     */
    @PutMapping("/update/state")
    public ResponseData updateSysState(@RequestParam("state") Boolean state) {
        sysService.updateSysState(state);
        return success();
    }

    /**
     * 系统数据一键还原
     */
    @GetMapping("/revert")
    public ResponseData dfsRevert() {
        getUsername();
        //sysService.dfsRevert(getUsername());
        return success();
    }

    /**
     * 系统公司二维码生成
     */
    @GetMapping("/company/code")
    public ResponseData dfsQRCode() {
        // 获取精制小程序详情
        OpenApiDTO openApiDTO = OpenApiDTO.builder()
                .modelCode(OpenApiEnum.GET_JZ_APP_DETAIL.getModelCode())
                .interfaceCode(OpenApiEnum.GET_JZ_APP_DETAIL.getInterfaceCode())
                .build();
        JSONObject res = openApiConfigService.callOpenUrl(openApiDTO);
        // 如果为空说明服务不可用
        if (res == null) {
            throw new ResponseException(RespCodeEnum.SERVICE_NOT_USE);
        }
        ResponseData responseData = JSONArray.parseObject(res.toJSONString(), new TypeReference<ResponseData>() {
        });
        return success(responseData);
//        ResponseData appDetail = yelinkAppsInterface.getAppDetail(token);
//        return success(appDetail);
    }

    /**
     * 获取dfs备份表 区分只备份结构和需要备份结构及数据的表
     *
     * @return
     */
    @GetMapping("/get/backup/table")
    public ResponseData getBackupTable() {
        return success(sysService.getBackupTable());
    }

    /**
     * 获取系统运行状态
     *
     * @return 状态枚举
     */
    @GetMapping("/status/get")
    public ResponseData statusGet() {
        return ResponseData.success(sysService.getStatus());
    }

    /**
     * 系统状态变更： 试运行 -> 正式运行
     *
     * @return 操作成功
     */
    @PostMapping("/status/change")
    public ResponseData statusChange() {
        sysService.statusChange();
        return ResponseData.success();
    }

    /**
     * 获取前端路由json
     *
     * @return
     */
    @GetMapping("/get/routeInfo")
    public ResponseData getRoutes() {
        JSONArray routes = sysRouteService.getRoutes();
        return ResponseData.success(routes);
    }


    /**
     * 新增前端路由
     *
     * @param sysRouteEntity
     * @return
     */
    @PostMapping("/save/routeInfo")
    public ResponseData saveOrUpdateRoutes(@RequestBody SysRouteEntity sysRouteEntity) {
        return ResponseData.success(sysRouteService.saveOrUpdateRoutes(sysRouteEntity));
    }

    /**
     * 解析前端路由json
     *
     * @return
     */
    @PostMapping("/analysis/routeInfo")
    public ResponseData analysisRouteInfo(@RequestBody List<SysRouteEntity> list) {
        sysRouteService.analysisRouteInfo(list);
        return ResponseData.success(true);
    }

    /**
     * 获取单据名称和code,用于业务配置下拉下拉数据选择
     */
    @GetMapping("/get/trigger/up/order")
    public ResponseData getTriggerUpOrder() {
        List<CompatorStateEnumDTO> list = new ArrayList<>();
        list.add(CompatorStateEnumDTO.builder().code("purchaseReceipt").name("采购收料单").build());
        list.add(CompatorStateEnumDTO.builder().code("purchaseInput").name("采购入库单").build());
        return ResponseData.success(list);
    }
    /**
     * 获取单据名称和code,用于业务配置下拉下拉数据选择
     */
    @GetMapping("/get/trigger/down/order")
    public ResponseData getTriggerDownOrder() {
        List<CompatorStateEnumDTO> list = new ArrayList<>();
        list.add(CompatorStateEnumDTO.builder().code("purchaseOrder").name("采购订单").build());
        list.add(CompatorStateEnumDTO.builder().code("purchaseReceipt").name("采购收料单").build());
        list.add(CompatorStateEnumDTO.builder().code("workOrderMaterialList").name("生产工单用料清单").build());
        list.add(CompatorStateEnumDTO.builder().code("productOrder").name("生产订单").build());
        return ResponseData.success(list);
    }


    /**
     * 获取所有单据名称和code
     *
     * @return
     */
    @GetMapping("/get/all/order/code/name")
    public ResponseData getAllOrderCodeAndName() {
        List<CompatorStateEnumDTO> list = Arrays.stream(OrderCodeNameEnum.values())
                .map(procedureInspectDataTypeEnum -> CompatorStateEnumDTO.builder()
                        .code(procedureInspectDataTypeEnum.getCode())
                        .name(procedureInspectDataTypeEnum.getName())
                        .build()).collect(Collectors.toList());
        return ResponseData.success(list);
    }

    /**
     * 获取所有单据名称和code
     *
     * @return
     */
    @GetMapping("/get/all/applet/code/name")
    public ResponseData getAllAppleCodeAndName() {
        List<CompatorStateEnumDTO> list = Arrays.stream(AppletNameEnum.values())
                .map(appletNameEnum -> CompatorStateEnumDTO.builder()
                        .code(appletNameEnum.getCode())
                        .name(appletNameEnum.getName())
                        .build()).collect(Collectors.toList());
        return ResponseData.success(list);
    }

    /**
     * 获取精制下载地址
     *
     * @return
     */
    @GetMapping("/get/jz/organization/package")
    public ResponseData getJzDownloadAddr() {
        return appsService.getJzDownloadAddr();
    }

    /**
     * 测试接口
     *
     * @return
     */
    @GetMapping("/test")
    public ResponseData test() {
        return ResponseData.success("dfs test message");
    }

    /**
     * 获取省、市、区全量数据
     */
    @GetMapping("/province/city/area")
    public ResponseData provinceCityArea() {
        List<SysProvinceEntity> provinceList = sysProvinceService.listProvinceCityArea();
        return ResponseData.success(provinceList);
    }
}
