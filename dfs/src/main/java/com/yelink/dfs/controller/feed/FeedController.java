package com.yelink.dfs.controller.feed;

import com.yelink.dfs.constant.product.MaterialMatchingEnum;
import com.yelink.dfs.entity.feed.dto.FeedSubmitDTO;
import com.yelink.dfs.service.feed.FeedRecordService;
import com.yelink.dfscommon.entity.CommonType;
import com.yelink.dfscommon.pojo.ResponseData;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 上料
 * <AUTHOR>
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/feed")
public class FeedController {

    private FeedRecordService feedRecordService;


    /**
     * 上料防错算法
     */
    @GetMapping("/material-matching/list")
    public ResponseData materialMatchingList(){
        List<CommonType> r = Arrays.stream(MaterialMatchingEnum.values())
                .map(e -> CommonType.builder().code(e.getType()).name(e.getName()).build())
                .collect(Collectors.toList());
        return ResponseData.success(r);
    }

    /**
     * 上料记录提交
     */
    @PostMapping("/record/submit")
    public ResponseData recordSubmit(@RequestBody @Valid FeedSubmitDTO dto){
        feedRecordService.recordSubmit(dto);
        return ResponseData.success();
    }
}
