package com.yelink.dfs.controller.product;


import cn.hutool.core.util.RandomUtil;
import com.yelink.dfs.common.BaseController;
import com.yelink.dfs.constant.OperationType;
import com.yelink.dfs.constant.product.MaterialReplaceStrategyEnum;
import com.yelink.dfs.constant.product.ReplaceSchemeStateEnum;
import com.yelink.dfs.entity.alarm.dto.AlarmPageDTO;
import com.yelink.dfs.entity.common.CommonState;
import com.yelink.dfs.entity.product.ReplaceSchemeEntity;
import com.yelink.dfs.entity.product.dto.ReplaceSchemeSelectDTO;
import com.yelink.dfs.service.product.ReplaceSchemeExtendService;
import com.yelink.dfs.service.product.ReplaceSchemeService;
import com.yelink.dfscommon.annotation.OperLog;
import com.yelink.dfscommon.constant.Constant;
import com.yelink.dfscommon.constant.RedisKeyPrefix;
import com.yelink.dfscommon.dto.ApproveBatchDTO;
import com.yelink.dfscommon.entity.CommonType;
import com.yelink.dfscommon.pojo.ResponseData;
import com.yelink.dfscommon.utils.DateUtil;
import com.yelink.dfscommon.utils.ExcelTemplateImportUtil;
import com.yelink.dfscommon.utils.ExcelUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * @Description: 物料替代方案 接口
 * @Author: zhuangwq
 * @Date: 2022/12/12
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/replace_schemes")
public class ReplaceSchemeController extends BaseController {

    private final ReplaceSchemeService replaceSchemeService;
    private final ReplaceSchemeExtendService ReplaceSchemeExtendService;

    /**
     * 查询替代方案列表
     *
     * @param
     * @return
     */
    @PostMapping("/list")
    public ResponseData getList(@RequestBody ReplaceSchemeSelectDTO selectDTO) {
        return success(replaceSchemeService.getList(selectDTO));
    }

    /**
     * 新增替代方案
     *
     * @param
     * @return
     */
    @PostMapping("/add")
    @OperLog(module = "产品管理", type = OperationType.ADD, desc = "新增了编码为#{schemeCode}的替代方案")
    public ResponseData add(@RequestBody @Valid ReplaceSchemeEntity entity) {
        replaceSchemeService.add(entity, getUsername());
        return success(entity);
    }

    /**
     * 新增生效的替代方案
     *
     * @param
     * @return
     */
    @PostMapping("/add/release")
    @OperLog(module = "产品管理", type = OperationType.ADD, desc = "新增并生效了编码为#{schemeCode}的替代方案")
    public ResponseData addReleasedOrder(@RequestBody @Valid ReplaceSchemeEntity entity) {
        replaceSchemeService.addReleasedOrder(entity, getUsername());
        return success(entity);
    }

    /**
     * 更新替代方案
     *
     * @param
     * @return
     */
    @PutMapping("/update")
    @OperLog(module = "产品管理", type = OperationType.UPDATE, desc = "更新了编码为#{schemeCode}的替代方案")
    public ResponseData update(@RequestBody ReplaceSchemeEntity entity) {
        replaceSchemeService.update(entity, getUsername());
        return success(entity);
    }

    /**
     * 删除替代方案
     *
     * @param id
     * @return
     */
    @DeleteMapping("/delete/{id}")
    @OperLog(module = "产品管理", type = OperationType.DELETE, desc = "删除了编码为#{schemeCode}的替代方案")
    public ResponseData remove(@PathVariable(value = "id") Integer id) {
        ReplaceSchemeEntity schemeEntity = replaceSchemeService.getById(id);
        replaceSchemeService.remove(id);
        return success(schemeEntity);
    }

    /**
     * 获取详情
     *
     * @param id
     * @return
     */
    @GetMapping("/detail/{id}")
    public ResponseData detail(@PathVariable(value = "id") Integer id) {
        return success(ReplaceSchemeExtendService.detail(id));
    }

    /**
     * 审批
     */
    @GetMapping("/examine/approve")
    public ResponseData examineOrApprove(@RequestParam(value = "approvalStatus") Integer approvalStatus,
                                         @RequestParam(value = "approvalSuggestion", required = false) String approvalSuggestion,
                                         @RequestParam(value = "id") Integer id) {
        String username = getUsername();
        replaceSchemeService.examineOrApprove(approvalStatus, approvalSuggestion, id, username);
        return success();
    }

    /**
     * 批量审批
     *
     * @return
     */
    @PostMapping("/approve/batch")
    public ResponseData approveBatch(@RequestBody ApproveBatchDTO dto) {
        dto.setActualApprover(getUsername());
        replaceSchemeService.approveBatch(dto);
        return success();
    }

    /**
     * 查询物料替换策略枚举（替代策略（不限制：noLimit））
     *
     * @return
     */
    @GetMapping("/strategy")
    public ResponseData getReplaceStrategy() {
        List<CommonType> list = new ArrayList<>();
        MaterialReplaceStrategyEnum[] values = MaterialReplaceStrategyEnum.values();
        for (MaterialReplaceStrategyEnum value : values) {
            if (!value.getEnable()) {
                continue;
            }
            CommonType type = new CommonType();
            type.setCode(value.getCode());
            type.setName(value.getName());
            list.add(type);
        }
        return success(list);
    }

    /**
     * 获取物料替代方案状态
     *
     * @return
     */
    @GetMapping("/state")
    public ResponseData getPurchaseState() {
        List<CommonState> list = new ArrayList<>();
        ReplaceSchemeStateEnum[] values = ReplaceSchemeStateEnum.values();
        for (ReplaceSchemeStateEnum value : values) {
            CommonState type = new CommonState();
            type.setCode(value.getCode());
            type.setName(value.getName());
            list.add(type);
        }
        return success(list);
    }


    /**
     * 替代方案导入：默认导入模板下载
     */
    @RequestMapping("/down/default/template")
    public void downloadDefaultTemplate(HttpServletResponse response) throws Exception {
        ReplaceSchemeExtendService.downloadDefaultTemplate("classpath:template/replaceSchemeTemplate.xlsx", response, "替代方案默认导入模板" + Constant.XLSX);
    }

    /**
     * 替代方案导入：导入自定义模板
     */
    @PostMapping("/import/template")
    @OperLog(module = "导入日志", type = OperationType.ADD, desc = "导入替代方案自定义模板")
    public ResponseData importTemplateExcel(MultipartFile file) {
        ReplaceSchemeExtendService.uploadCustomTemplate(file, this.getUsername());
        return success();
    }

    /**
     * 替代方案导入：下载自定义导入模板
     */
    @GetMapping("/export/custom/template")
    public void exportCustomExcel(HttpServletResponse response) throws IOException {
        //找到导出的模板
        InputStream inputStream = ReplaceSchemeExtendService.downloadCustomImportTemplate();
        ExcelTemplateImportUtil.responseToClient(response, inputStream, "替代方案导入模板" + Constant.XLSX);
    }

    /**
     * 替代方案导入：导出导入模板
     */
    @GetMapping("/export/template")
    public void exportTemplateExcel(HttpServletResponse response) throws IOException {
        //找到导出的模板
        byte[] bytes = ReplaceSchemeExtendService.downloadImportTemplate();
        ExcelTemplateImportUtil.responseToClient(response, bytes, "替代方案导入模板" + Constant.XLSX);
    }

    /**
     * 替代方案导入：导入数据
     */
    @RequestMapping("/import/data")
    public ResponseData importData(MultipartFile file) throws Exception {
        ExcelUtil.checkImportExcelFile(file);
        String importProgressKey = RedisKeyPrefix.REPLACE_SCHEME_IMPORT_PROGRESS + DateUtil.format(new Date(), DateUtil.yyyyMMddHHmmss_FORMAT) + "_" + RandomUtil.randomString(2);
        ReplaceSchemeExtendService.sycImportData(file.getOriginalFilename(), file.getInputStream(), getUsername(), importProgressKey);
        return success(importProgressKey);
    }

    /**
     * 替代方案导入：查询导入进度
     */
    @RequestMapping("/import/progress")
    public ResponseData getImportProgress() {
        return success(ReplaceSchemeExtendService.importProgress());
    }

    @PostMapping("/export")
    public void exportAlarmList(HttpServletResponse response, @RequestBody ReplaceSchemeSelectDTO dto) throws IOException {
        replaceSchemeService.export(response, dto);
    }

}
