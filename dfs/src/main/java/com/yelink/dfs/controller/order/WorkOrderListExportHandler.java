package com.yelink.dfs.controller.order;

import com.alibaba.fastjson.JSON;
import com.asyncexcel.core.DataParam;
import com.asyncexcel.core.ExcelContext;
import com.asyncexcel.core.ExportPage;
import com.asyncexcel.core.annotation.ExcelHandle;
import com.asyncexcel.core.exporter.DataExportParam;
import com.asyncexcel.core.exporter.ExportContext;
import com.asyncexcel.core.exporter.ExportHandler;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yelink.dfs.entity.order.WorkOrderEntity;
import com.yelink.dfs.entity.order.dto.WorkOrderExportDTO;
import com.yelink.dfs.entity.order.dto.WorkOrderSelectDTO;
import com.yelink.dfs.service.common.CommonService;
import com.yelink.dfs.service.order.WorkOrderService;
import com.yelink.dfscommon.constant.ExcelExportFormEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Objects;


/**
 * 生产工单列表数据导出
 *
 * <AUTHOR>
 * @Date 2023/7/5 11:18
 */
@Slf4j
@ExcelHandle
@RequiredArgsConstructor
public class WorkOrderListExportHandler implements ExportHandler<WorkOrderExportDTO> {
    public static final ExcelExportFormEnum EXPORT_FORM =  ExcelExportFormEnum.WORK_ORDER;

    private final WorkOrderService workOrderService;
    private final CommonService commonService;

    /**
     * 导出数据最大不能超过100万行
     */
    public static final long EXPORT_MAX_ROWS = 1000000;

    @Override
    public void init(ExcelContext ctx, DataParam param) {
        ExportContext context = (ExportContext) ctx;
        String cleanSheetNames = (String) param.getParameters().get("cleanSheetNames");
        Integer templateId = (Integer) param.getParameters().get("templateId");
        String templateSheetName = (String) param.getParameters().get("workOrder");
        String templateFileUrl = (String) param.getParameters().get("templateFileUrl");
        if (StringUtils.isNotBlank(templateFileUrl)) {
            // 模板文件是一个url地址
            commonService.initExcelContext(templateFileUrl, templateSheetName, cleanSheetNames, context, WorkOrderExportDTO.class, EXPORT_FORM.getFullPathCode());
        } else {
            commonService.initExcelContext(templateId,templateSheetName, cleanSheetNames, context, WorkOrderExportDTO.class, EXPORT_FORM.getFullPathCode());
        }
    }


    @Override
    public ExportPage<WorkOrderExportDTO> exportData(int startPage, int limit, DataExportParam param) {
        // 需要兼容列表导出、报表管理的导出
        WorkOrderSelectDTO workOrderSelectDTO = (WorkOrderSelectDTO) param.getParameters().get(WorkOrderSelectDTO.class.getName());
        if (Objects.isNull(workOrderSelectDTO)) {
            String filterFieldValue = (String)param.getParameters().get(this.getClass().getName());
            workOrderSelectDTO = StringUtils.isEmpty(filterFieldValue) ? new WorkOrderSelectDTO() : JSON.parseObject(filterFieldValue, WorkOrderSelectDTO.class);
        }
        workOrderSelectDTO.setCurrent(startPage);
        workOrderSelectDTO.setSize(limit);
        IPage<WorkOrderEntity> page = workOrderService.getWorkOrderEntityPage(workOrderSelectDTO, param.getCreateUserCode());
        List<WorkOrderExportDTO> list = workOrderService.convertToWorkOrderExportDTO(page.getRecords());
        ExportPage<WorkOrderExportDTO> result = new ExportPage<>();
        result.setTotal(Math.min(page.getTotal(), EXPORT_MAX_ROWS));
        result.setCurrent((long) startPage);
        result.setSize((long) limit);
        result.setRecords(list);
        return result;
    }


    @Override
    public void beforePerPage(ExportContext ctx, DataExportParam param) {
        log.info("开始查询第{}页", (long) Math.ceil(ctx.getSuccessCount() + ctx.getFailCount()) / ctx.getLimit());
    }

    @Override
    public void afterPerPage(List<WorkOrderExportDTO> list, ExportContext ctx, DataExportParam param) {
        log.info("已完成数量：{}", ctx.getSuccessCount());
    }

    @Override
    public void callBack(ExcelContext ctx, DataParam param) {
        ExportContext context = (ExportContext) ctx;
        log.info("导出完成：{}", context.getFailMessage());
    }
}
