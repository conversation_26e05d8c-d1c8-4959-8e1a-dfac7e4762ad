package com.yelink.dfs.controller.common;

import com.yelink.dfs.common.BaseController;
import com.yelink.dfs.constant.Constant;
import com.yelink.dfs.entity.common.ImportDataRecordEntity;
import com.yelink.dfs.entity.common.ImportDataSelectDTO;
import com.yelink.dfs.provider.FastDfsClientService;
import com.yelink.dfs.service.common.ImportDataRecordService;
import com.yelink.dfscommon.constant.Constants;
import com.yelink.dfscommon.pojo.ResponseData;
import com.yelink.dfscommon.utils.ExcelTemplateImportUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * <AUTHOR>
 * 导入数据记录
 * @Date 2022/3/16 20:42
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/import/data/records")
public class ImportDataRecordController extends BaseController {
    private ImportDataRecordService importDataRecordService;
    private FastDfsClientService fastDfsClientService;
    /**
     * 获取列表数据
     *
     * @param fileName
     * @param startTime
     * @param endTime
     * @param current
     * @param size
     * @return
     */
    @GetMapping("/list")
    public ResponseData getList(@RequestParam(value = "importType") String importType,
                                @RequestParam(value = "fileName", required = false) String fileName,
                                @RequestParam(value = "startTime", required = false) String startTime,
                                @RequestParam(value = "endTime", required = false) String endTime,
                                @RequestParam(value = "current", required = false) Integer current,
                                @RequestParam(value = "size", required = false) Integer size) {
        return success(importDataRecordService.getList(importType, fileName, startTime, endTime, current, size));
    }

    @PostMapping("/page")
    public ResponseData getPage(@RequestBody ImportDataSelectDTO dto) {
        return success(importDataRecordService.getPage(dto));
    }

    /**
     * 历史页面下载日志
     *
     * @param id
     * @return
     */
    @GetMapping("/download/log")
    public void getLogFileById(@RequestParam(value = "id") Integer id, HttpServletResponse response) throws IOException {
        ImportDataRecordEntity entity = importDataRecordService.getById(id);
        if (entity == null) {
            return;
        }
        byte[] data = fastDfsClientService.downloadFile(entity.getLogUrl());
        String strings = entity.getLogUrl();
        String importFileName = entity.getImportFileName();
        if(importFileName.contains(Constants.SPOT)){
            importFileName = importFileName.substring(0,importFileName.lastIndexOf("."));
        }
        ExcelTemplateImportUtil.responseToClient(response,data, importFileName + strings.substring(strings.lastIndexOf(".")));
    }




    /**
     * 根据导入类型和路径下载日志
     *
     * @param importType
     * @param url
     * @param response
     * @return
     * @throws IOException
     */
    @GetMapping("/download/the/latest/log")
    public void getTheLatestLogFile(@RequestParam(value = "importType") String importType,
                                    @RequestParam(value = "url") String url, HttpServletResponse response) throws IOException {

        ImportDataRecordEntity entity = importDataRecordService
                .lambdaQuery()
                .eq(ImportDataRecordEntity::getImportType,importType)
                .eq(ImportDataRecordEntity::getLogUrl,url)
                .one();
        if (entity == null) {
            return;
        }
        String strings = entity.getLogUrl();
        byte[] data = importDataRecordService.getTheLatestLogFileByUrl(importType, url);
        ExcelTemplateImportUtil.responseToClient(response,data,entity.getImportFileName() + strings.substring(strings.lastIndexOf(".")));
    }

}
