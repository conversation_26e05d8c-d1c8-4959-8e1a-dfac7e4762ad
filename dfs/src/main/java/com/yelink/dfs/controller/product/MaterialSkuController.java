package com.yelink.dfs.controller.product;

import com.yelink.dfs.common.BaseController;
import com.yelink.dfs.service.product.MaterialAuxiliaryAttrSkuService;
import com.yelink.dfscommon.constant.ParamException;
import com.yelink.dfscommon.constant.RespCodeEnum;
import com.yelink.dfscommon.constant.ResponseException;
import com.yelink.dfscommon.dto.dfs.MaterialSkuAndAttrDTO;
import com.yelink.dfscommon.dto.dfs.MaterialSkuInsertResDTO;
import com.yelink.dfscommon.entity.dfs.MaterialAuxiliaryAttrSkuEntity;
import com.yelink.dfscommon.pojo.ResponseData;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @description 物料sku
 * @Date 2021/4/14
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/material/sku")
public class MaterialSkuController extends BaseController {

    private MaterialAuxiliaryAttrSkuService materialSkuService;

    /**
     * 查询特征参数和sku列表
     *
     * @param skuDTO
     * @return
     */
    @PostMapping("/attr/list")
    public ResponseData getAttrList(@RequestBody @Valid MaterialSkuAndAttrDTO skuDTO, BindingResult bindingResult) {
        if (bindingResult.hasErrors()) {
            throw new ParamException(bindingResult);
        }
        List<MaterialAuxiliaryAttrSkuEntity> list = materialSkuService.getAttrList(skuDTO);
        return success(list);
    }

    /**
     * 新增物料sku
     *
     * @param
     * @return
     */
    @PostMapping("/add")
    public ResponseData add(@RequestBody @Valid List<MaterialAuxiliaryAttrSkuEntity> list, BindingResult bindingResult) {
        if (bindingResult.hasErrors()) {
            throw new ParamException(bindingResult);
        }
        if (CollectionUtils.isEmpty(list)) {
            throw new ResponseException(RespCodeEnum.MATERIAL_SKU_LIST_IS_EMPTY);
        }
        MaterialSkuInsertResDTO skuDTO = materialSkuService.add(list, getUsername());
        return success(skuDTO);
    }

}
