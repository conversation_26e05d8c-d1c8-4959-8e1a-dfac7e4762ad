package com.yelink.dfs.controller.device;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yelink.dfs.common.BaseController;
import com.yelink.dfs.entity.device.dto.DeviceOperateLogSelectDTO;
import com.yelink.dfs.entity.device.vo.DeviceOperateLogVO;
import com.yelink.dfs.service.device.DeviceOperateLogService;
import com.yelink.dfscommon.pojo.ResponseData;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 设备操作日志
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/device/operate/log")
public class DeviceOperateLogController extends BaseController {
    @Resource
    private DeviceOperateLogService deviceOperateLogService;

    /**
     * 查询设备组列表
     */
    @GetMapping("/page")
    public ResponseData page(DeviceOperateLogSelectDTO dto) {
        Page<DeviceOperateLogVO> page = deviceOperateLogService.getPage(dto);
        return success(page);
    }

}
