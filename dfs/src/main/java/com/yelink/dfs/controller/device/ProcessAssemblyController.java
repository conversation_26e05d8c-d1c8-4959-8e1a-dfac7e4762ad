package com.yelink.dfs.controller.device;

import cn.hutool.core.util.RandomUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yelink.dfs.common.BaseController;
import com.yelink.dfs.constant.RedisKeyPrefix;
import com.yelink.dfs.entity.device.ProcessAssemblyEntity;
import com.yelink.dfs.service.device.ProcessAssemblyService;
import com.yelink.dfscommon.constant.Constant;
import com.yelink.dfscommon.constant.ParamException;
import com.yelink.dfscommon.entity.common.ZipFileDTO;
import com.yelink.dfscommon.pojo.ResponseData;
import com.yelink.dfscommon.utils.DateUtil;
import com.yelink.dfscommon.utils.ExcelTemplateImportUtil;
import com.yelink.dfscommon.utils.ExcelUtil;
import com.yelink.dfscommon.utils.ZipAndRarUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.Resource;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.core.io.support.ResourcePatternResolver;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.Date;

/**
 * <AUTHOR>
 * @description 工艺工装管理
 * @Date 2022/9/16
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/device/process")
public class ProcessAssemblyController  extends BaseController {

    private ProcessAssemblyService processAssemblyService;
    private RedisTemplate redisTemplate;
    /**
     * 获取全部工艺工装列表
     * @return
     */
    @GetMapping("list")
    public ResponseData list() {
        return success(processAssemblyService.list());
    }

    /**
     * 获取全部工艺工装列表，分页
     * @return
     */
    @GetMapping("/list/page")
    public ResponseData listPage(@RequestParam(value = "current",defaultValue = "1")Integer current,
                                 @RequestParam(value = "size",defaultValue = "10")Integer size,
                                 @RequestParam(value = "code", required = false)String  code,
                                 @RequestParam(value = "name", required = false)String name) {
        Page<ProcessAssemblyEntity> entity  = processAssemblyService.listPage(current, size,code,name);
        return success(entity);
    }

    /**
     * 查询工艺工装信息详情
     * @return
     */
    @GetMapping("/getById/{id}")
    public ResponseData getById(@PathVariable(value = "id") Integer id) {
        ProcessAssemblyEntity entity = processAssemblyService.detail(id);
        return success(entity);
    }

    /**
     * 新增工艺工装信息
     * @return
     */
    @PostMapping("/insert")
    public ResponseData insert(@RequestBody ProcessAssemblyEntity processAssemblyEntity, BindingResult bindingResult) {
        if (bindingResult.hasErrors()) {
            throw new ParamException(bindingResult);
        }
        processAssemblyEntity.setCreateBy(getUsername());
        processAssemblyEntity.setUpdateBy(getUsername());
        processAssemblyService.saveProcessAssembly(processAssemblyEntity);
        return success(processAssemblyEntity);
    }

    /**
     * 删除工艺工装信息
     * @return
     */
    @DeleteMapping("/delete/{id}")
    public ResponseData delete(@PathVariable(value = "id") Integer id) {
        boolean b = processAssemblyService.removeById(id);
        if (!b) {
            return fail();
        }
        return success();
    }

    /**
     * 修改工艺工装信息
     * @return
     */
    @PutMapping("/update")
    public ResponseData update(@RequestBody ProcessAssemblyEntity processAssemblyEntity) {
        processAssemblyEntity.setUpdateBy(getUsername());
        processAssemblyEntity.setUpdateTime(new Date());
        processAssemblyService.update(processAssemblyEntity);
        return success();
    }

    /**
     * 工艺装备导入:下载默认导入模板
     */
    @GetMapping("/down/default/template")
    public void downDefaultTemplate(HttpServletResponse response) throws Exception {
        processAssemblyService.downloadDefaultTemplate("classpath:template/processAssemblyTemplate.xlsx", response, "工艺装备导入模板" + Constant.XLSX);
    }

    /**
     * 工艺装备导入
     */
    @PostMapping("/import")
    public ResponseData importData(MultipartFile file) throws Exception {
        //1、判断导入文件的合法性
        ExcelUtil.checkImportExcelFile(file);
        //异步数据导入
        String importProgressKey = RedisKeyPrefix.PROCESS_ASSEMBLY_IMPORT_PROGRESS + DateUtil.format(new Date(), DateUtil.yyyyMMddHHmmss_FORMAT) + "_" + RandomUtil.randomString(2);
        processAssemblyService.sycImportData(file.getOriginalFilename(), file.getInputStream(), getUsername(), importProgressKey);
        return ResponseData.success(importProgressKey);
    }

    /**
     * 工艺装备导入:查询导入进度
     */
    @GetMapping("/import/progress")
    public ResponseData getImportProgress() {
        return success(processAssemblyService.importProgress());
    }

    /**
     * 工艺装备附件导入:下载默认导入模板
     */
    @GetMapping("/down/default/appendix/template")
    public void downDefaultAppendixTemplate(HttpServletResponse response) throws Exception {
        ResourcePatternResolver resolver = new PathMatchingResourcePatternResolver();
        Resource resource = resolver.getResource("classpath:template/processAssemblyAppendixTemplate.zip");
        String templateName = URLEncoder.encode("fileTemplate" + Constant.ZIP, StandardCharsets.UTF_8.name());
        ExcelTemplateImportUtil.responseToClient(response, resource.getInputStream(), templateName);
    }

    /**
     * 工艺装备附件导入
     */
    @PostMapping("/import/appendix")
    public ResponseData importAppendixData(MultipartFile file) throws Exception {
        // 校验压缩文件
        ZipFileDTO dto = ZipAndRarUtil.checkFile(file, "工装附件列表");
        processAssemblyService.importAppendixData(dto.getUnzip(), dto.getExcelFile(), getUsername());
        return ResponseData.success();
    }

    /**
     * 工艺装备附件导入:查询导入进度
     */
    @GetMapping("/import/appendix/progress")
    public ResponseData getImportAppendixProgress() {
        Object o = redisTemplate.opsForValue().get(RedisKeyPrefix.PROCESS_ASSEMBLY_APPENDIX_IMPORT_PROGRESS);
        if (o != null) {
            return success(o);
        }
        return fail();
    }

}
