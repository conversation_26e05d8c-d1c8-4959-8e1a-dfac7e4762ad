package com.yelink.dfs.controller.code;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yelink.dfs.common.BaseController;
import com.yelink.dfs.entity.code.dto.RelationOrderDTO;
import com.yelink.dfs.open.v1.code.dto.CodeLogInsertDTO;
import com.yelink.dfs.open.v1.code.dto.CodeLogPageDTO;
import com.yelink.dfs.service.code.CodeLogService;
import com.yelink.dfscommon.constant.ParamException;
import com.yelink.dfscommon.entity.dfs.productFlowCode.CodeLogEntity;
import com.yelink.dfscommon.pojo.PageResult;
import com.yelink.dfscommon.pojo.Result;
import com.yelink.dfscommon.utils.JacksonUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.Date;
import java.util.List;


/**
 * @Description: 条码日志接口
 * @Author: zengzhengfu
 * @Date: 2021/4/8
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/code/log")
public class CodeLogController extends BaseController {

    private CodeLogService codeLogService;

    /**
     * 条码日志列表
     *
     * @param dto
     * @return
     */
    @PostMapping("/list")
    public Result<PageResult<CodeLogEntity>> getList(@RequestBody CodeLogPageDTO dto) {
        Page<CodeLogEntity> page = codeLogService.getList(dto);
        return Result.success(page);
    }

    /**
     * 添加条码日志
     *
     * @return
     */
    @PostMapping("/add")
    public Result add(@RequestBody @Valid CodeLogInsertDTO dto, BindingResult bindingResult) {
        if (bindingResult.hasErrors()) {
            throw new ParamException(bindingResult);
        }
        CodeLogEntity entity = JacksonUtil.convertObject(dto, CodeLogEntity.class);
        entity.setCreateTime(new Date());
        codeLogService.save(entity);
        return Result.success(entity.getId());
    }

    /**
     * 删除条码日志
     *
     * @return
     */
    @DeleteMapping("/delete")
    public Result deleteById(@RequestParam Integer id) {
        codeLogService.removeById(id);
        return Result.success();
    }

    /**
     * 查找条码日志关联的单据列表
     *
     * @param code 条码
     * @return 关联单据列表的结果
     */
    @GetMapping("/relation_order/list")
    public Result<List<RelationOrderDTO>> relationOrderList(@RequestParam String code) {
        List<RelationOrderDTO> relationNumbers = codeLogService.listRelationNumberByCode(code);
        return Result.success(relationNumbers);
    }
}
