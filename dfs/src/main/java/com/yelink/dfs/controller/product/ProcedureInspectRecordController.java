package com.yelink.dfs.controller.product;

import com.asyncexcel.core.model.ExcelTask;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yelink.dfs.common.BaseController;
import com.yelink.dfs.entity.common.CommonType;
import com.yelink.dfs.entity.common.ModelUploadFileEntity;
import com.yelink.dfs.entity.product.ProcedureInspectRecordEntity;
import com.yelink.dfs.entity.product.dto.ProcedureInspectRecordExportDTO;
import com.yelink.dfs.entity.product.dto.ProcedureInspectRecordSelectDTO;
import com.yelink.dfs.provider.FastDfsClientService;
import com.yelink.dfs.service.common.ModelUploadFileService;
import com.yelink.dfs.service.product.ProcedureInspectRecordService;
import com.yelink.dfscommon.pojo.ResponseData;
import com.yelink.dfscommon.utils.EasyExcelUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * @Description: 工序人力管理
 * @Author: shenzm
 * @Date: 2022/9/21
 */
@Slf4j
@RestController
@RequestMapping("/procedure/inspect_record")
public class ProcedureInspectRecordController extends BaseController {
    @Resource
    private ProcedureInspectRecordService inspectRecordService;
    @Resource
    private ModelUploadFileService modelUploadFileService;
    @Resource
    private FastDfsClientService fastDfsClientService;

    /**
     * 查询工序检验项记录列表
     *
     * @return
     */
    @PostMapping("/list")
    public ResponseData getList(@RequestBody ProcedureInspectRecordSelectDTO selectDTO) {
        Page<ProcedureInspectRecordEntity> entities = inspectRecordService.getList(selectDTO);
        return success(entities);
    }

    /**
     * 查询工序检验项的工序列表
     *
     * @param
     * @return
     */
    @GetMapping("/procedure/list")
    public ResponseData getProcedureList() {
        List<CommonType> entities = inspectRecordService.getProcedureList();
        return success(entities);
    }

    /**
     * 查询工序检验项的工序检验项列表
     *
     * @param
     * @return
     */
    @GetMapping("/procedure_inspect/list")
    public ResponseData getProcedureInspectList() {
        List<CommonType> entities = inspectRecordService.getProcedureInspectList();
        return success(entities);
    }

    /**
     * 工序检验记录列表导出 - 下载默认模板
     * 查询最新的10条工序检验记录数据导出数据源excel
     *
     * @param response
     * @throws IOException
     */
    @GetMapping("/default/export/template")
    public void listDefaultExportTemplate(HttpServletResponse response) throws IOException {
        ProcedureInspectRecordSelectDTO selectDTO = ProcedureInspectRecordSelectDTO.builder().build();
        selectDTO.setCurrent(1);
        selectDTO.setSize(10);
        Page<ProcedureInspectRecordEntity> page = inspectRecordService.getList(selectDTO);
        List<ProcedureInspectRecordExportDTO> list = inspectRecordService.convertToExportDTO(page.getRecords());
        EasyExcelUtil.export(response, "工序检验记录默认导出模板", "数据源", list, ProcedureInspectRecordExportDTO.class);
    }


    /**
     * 工序检验记录列表导出 - 自定义模板上传
     * 接收文件并清空名称为数据源sheet的内容
     *
     * @param file
     * @return
     */
    @PostMapping("/upload/list/export/template")
    public ResponseData uploadListExportTemplate(MultipartFile file) throws IOException {
        inspectRecordService.uploadListExportTemplate(file, getUsername());
        return success();
    }


    /**
     * 工序检验记录列表导出 - 工序检验记录模板自定义模板下载
     * 分页查询并填写数据源sheet的内容
     *
     * @param response
     * @return
     * @throws IOException
     */
    @PostMapping("/download/list/export/template")
    public ResponseData downloadListExportTemplate(HttpServletResponse response, Integer id) throws IOException {
        //获取最新的十条数据源
        ProcedureInspectRecordSelectDTO selectDTO = ProcedureInspectRecordSelectDTO.builder().build();
        selectDTO.setCurrent(1);
        selectDTO.setSize(10);
        Page<ProcedureInspectRecordEntity> page = inspectRecordService.getList(selectDTO);
        List<ProcedureInspectRecordExportDTO> list = inspectRecordService.convertToExportDTO(page.getRecords());
        //获取模板文件
        ModelUploadFileEntity uploadFile = modelUploadFileService.getById(id);
        byte[] bytes = fastDfsClientService.getFileStream(uploadFile.getFileAddress());
        //下载模板
        EasyExcelUtil.writeToExcelTemplate(response, list, "数据源", uploadFile.getFileName(), bytes, ProcedureInspectRecordExportDTO.class);
        return success();
    }


    /**
     * 工序检验记录导出  :异步
     *
     * @param selectDTO
     * @return
     * @throws IOException
     */
    @PostMapping("/syc/exports")
    public ResponseData export(@RequestBody ProcedureInspectRecordSelectDTO selectDTO) {
        Long result = inspectRecordService.exportTask(selectDTO, getUsername());
        return success(result);
    }


    /**
     * 分页查询当前导出任务列表
     *
     * @param size
     * @param current
     * @return
     */
    @GetMapping("/export/task/page")
    public ResponseData taskPage(@RequestParam(required = false, defaultValue = "20") Integer pageSize,
                                 @RequestParam(required = false, defaultValue = "1") Integer currentPage) {
        IPage<ExcelTask> iPage = inspectRecordService.taskPage(currentPage, pageSize);
        return success(iPage);
    }


    /**
     * 查询 导出进度
     *
     * @return
     */
    @GetMapping("/export/task/{taskId}")
    public ResponseData exportExcel(@PathVariable Long taskId) {
        ExcelTask excelTask = inspectRecordService.taskById(taskId);
        return success(excelTask);
    }


}
