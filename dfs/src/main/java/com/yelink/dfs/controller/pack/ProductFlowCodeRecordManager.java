package com.yelink.dfs.controller.pack;

import com.yelink.dfs.entity.product.CraftProcedureEntity;
import com.yelink.dfs.service.code.ProductFlowCodeService;
import com.yelink.dfs.service.code.ScannerService;
import com.yelink.dfs.service.product.CraftProcedureService;
import com.yelink.dfscommon.constant.ResponseException;
import com.yelink.dfscommon.constant.dfs.code.ProductFlowCodeRecordStateEnum;
import com.yelink.dfscommon.entity.dfs.ProductFlowCodeResultEntity;
import com.yelink.dfscommon.entity.dfs.productFlowCode.AddFlowCodeDTO;
import com.yelink.dfscommon.entity.dfs.productFlowCode.CodeRelevanceEntity;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * @description: 流水码记录
 * @author: shuang
 * @time: 2023/3/15
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class ProductFlowCodeRecordManager {
    private final ProductFlowCodeService productFlowCodeService;
    private final ScannerService scannerService;
    private final CraftProcedureService craftProcedureService;


    /**
     * 增加工序过站记录
     *
     * @param productFlowCode
     * @param workOrderNumber
     * @param fid
     */
    public ProductFlowCodeResultEntity addProductFlowCode(String productFlowCode, String workOrderNumber, Integer fid) {
        Integer craftProcedureId = getCraftProcedureId(workOrderNumber, fid);

        AddFlowCodeDTO addFlowCodeDTO = AddFlowCodeDTO.builder()
                .productFlowCode(productFlowCode)
                .fid(fid)
                .state(ProductFlowCodeRecordStateEnum.COMPLETE.getCode())
                .craftProcedureId(craftProcedureId).isEndTime(true)
                .build();
        return scannerService.addCodeRecordGeneral(addFlowCodeDTO);
    }

    /**
     * 针对测试工位增加失败的过站记录
     *
     * @param productFlowCode
     * @param workOrderNumber
     * @param fid
     * @return
     */
    private ProductFlowCodeResultEntity addDisableProductFlowCode(String productFlowCode, String workOrderNumber, Integer fid) {
        Integer craftProcedureId = getCraftProcedureId(workOrderNumber, fid);
        AddFlowCodeDTO addFlowCodeDTO = AddFlowCodeDTO.builder()
                .productFlowCode(productFlowCode)
                .fid(fid)
                .state(ProductFlowCodeRecordStateEnum.FAIL.getCode())
                .craftProcedureId(craftProcedureId).isEndTime(true)
                .build();
        return scannerService.addCodeRecordGeneral(addFlowCodeDTO);
    }

    /**
     * 获取工艺工序ID
     */
    private Integer getCraftProcedureId(String workOrderNumber, Integer fid) {
        //获取工艺工序ID
        List<CraftProcedureEntity> craftProcedureEntities = craftProcedureService.getCraftProcedureListByFidAndWorkOrder(fid,workOrderNumber);
        if (CollectionUtils.isEmpty(craftProcedureEntities)) {
            throw new ResponseException("未找到该工艺工序");
        }
        //不传工序的情况下默认只有一个工序
        CraftProcedureEntity craftProcedureEntity = craftProcedureEntities.get(0);
        return craftProcedureEntity.getId();
    }

    /**
     * 增加工序过站记录
     */
    public ProductFlowCodeResultEntity addProductFlowCode(String productFlowCode, String finishedProductCode, String workOrderNumber, Integer fid) {
        ProductFlowCodeResultEntity productFlowCodeResultEntity =  addProductFlowCode(productFlowCode, workOrderNumber, fid);
        productFlowCodeService.relevanceCode(CodeRelevanceEntity.builder().code(finishedProductCode).relevanceCode(productFlowCode).build());
        return productFlowCodeResultEntity;
    }
}
