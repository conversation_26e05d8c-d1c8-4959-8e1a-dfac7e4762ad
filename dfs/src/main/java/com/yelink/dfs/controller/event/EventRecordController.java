package com.yelink.dfs.controller.event;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yelink.dfs.common.BaseController;
import com.yelink.dfs.entity.event.EventRecordEntity;
import com.yelink.dfs.entity.event.dto.DebugMachineDTO;
import com.yelink.dfs.entity.event.dto.EventExcelDTO;
import com.yelink.dfs.entity.event.dto.EventInsertDTO;
import com.yelink.dfs.entity.event.dto.EventPageDTO;
import com.yelink.dfs.entity.event.dto.EventUpdateDTO;
import com.yelink.dfs.entity.event.dto.PadTempDTO;
import com.yelink.dfs.service.event.EventRecordService;
import com.yelink.dfscommon.pojo.ResponseData;
import com.yelink.dfscommon.utils.EasyExcelUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/01/07 19:29
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/record/event")
public class EventRecordController extends BaseController {

    private EventRecordService eventRecordService;


    @GetMapping("list")
    public ResponseData listEvent(EventPageDTO dto) {
        return success(eventRecordService.listEvent(dto));
    }
    @GetMapping("list-export")
    public void exportListEvent(EventPageDTO dto, HttpServletResponse response) throws IOException {
        dto.setCurrentPage(null);
        dto.setPageSize(null);
        Page<EventRecordEntity> page = eventRecordService.listEvent(dto);
        List<EventExcelDTO> exportList = JSON.parseArray(JSON.toJSONString(page.getRecords()), EventExcelDTO.class);
        EasyExcelUtil.export(response, "EventFile", "EventSheet", exportList, EventExcelDTO.class);
    }

    /**
     * 获取事件详情
     *
     * @param id
     * @return
     */
    @GetMapping("detail/{id}")
    public ResponseData getDetail(@PathVariable(value = "id") Integer id) {
        return success(eventRecordService.showEventDetail(id));
    }

    /**
     * 点击调机结束，选中调机事件
     *
     * @param dto 调机事件dto
     * @return
     */
    @PostMapping("end/debug")
    public ResponseData endDebug(DebugMachineDTO dto) {
        String userName = dto.getUserName();
        dto.setUserName(StringUtils.isBlank(userName) ? getUsername() : userName);
        eventRecordService.endDebug(dto, userName);
        return success();
    }

    /**
     * 在移动端上添加事件记录
     *
     * @param dto
     * @return
     */
    @PostMapping("pad/add")
    public ResponseData addEventRecordAtPad(@RequestBody PadTempDTO dto) {
        String userName = dto.getUserName();
        dto.setUserName(StringUtils.isBlank(userName) ? getUsername() : userName);
        eventRecordService.addEventRecordAtPad(dto);
        return success();
    }

    /**
     * 异常管理：小程序（单报工，订单报工，扫码报工）添加事件
     * @param dto 入参
     */
    @PostMapping("/exception-manager/add")
    public ResponseData addByExceptionManager(@RequestBody @Validated EventInsertDTO dto) {
        eventRecordService.addByExceptionManager(dto);
        return success();
    }

    @PostMapping("/exception-manager/update")
    public ResponseData updateByExceptionManager(@RequestBody @Validated EventUpdateDTO dto) {
        eventRecordService.updateByExceptionManager(dto);
        return success();
    }
}
