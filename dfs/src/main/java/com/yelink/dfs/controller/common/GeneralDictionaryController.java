package com.yelink.dfs.controller.common;

import com.yelink.dfs.common.BaseController;
import com.yelink.dfs.entity.model.GeneralDictionaryEntity;
import com.yelink.dfs.service.model.GeneralDictionaryService;
import com.yelink.dfscommon.constant.ParamException;
import com.yelink.dfscommon.pojo.ResponseData;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Optional;


/**
 * @description: 通用数据字典
 * @author: shuang
 * @time: 2020/12/7
 */
@Slf4j
@AllArgsConstructor
@RestController
@RequestMapping("/generals")
public class GeneralDictionaryController extends BaseController {

    private GeneralDictionaryService generalDictionaryService;


    /**
     * 1、根据编码获取类型详情
     *
     * @param number
     * @return
     */
    @GetMapping("/detail/{number}")
    public ResponseData selectByNumber(@PathVariable("number") String number) {
        Optional.ofNullable(number).orElseThrow(() -> new ParamException("参数不能为空"));
        GeneralDictionaryEntity generalDictionaryEntities = generalDictionaryService.selectByNumber(number);
        return success(generalDictionaryEntities);
    }

    /**
     * 2、根据父编码获取下一级子类型全部数据
     *
     * @param number
     * @return
     */
    @GetMapping("/list/parent/{number}")
    public ResponseData listByParentNumber(@PathVariable("number") String number) {
        Optional.ofNullable(number).orElseThrow(() -> new ParamException("参数不能为空"));
        List<GeneralDictionaryEntity> list = generalDictionaryService.listByParentNumber(number);
        return success(list);
    }

    /**
     * 3、通过上一级数据编码获取所有下级编码以及更下级编码
     *
     * @param number
     * @return
     */
    @GetMapping("/list/all/parent/{number}")
    public ResponseData listAllByParentNumber(@PathVariable("number") String number) {
        Optional.ofNullable(number).orElseThrow(() -> new ParamException("参数不能为空"));
        List<GeneralDictionaryEntity> list = generalDictionaryService.listAllByParentNumber(number);
        return success(list);
    }

    /**
     * 获取工位图片列表
     *
     * @return
     */
    @GetMapping("/list/img/facility")
    public ResponseData facilityImgList() {
        List<GeneralDictionaryEntity> list = generalDictionaryService.facilityImgList();
        return success(list);
    }

    /**
     * 获取连接图片列表
     *
     * @return
     */
    @GetMapping("/list/img/connect")
    public ResponseData connectImgList() {
        List<GeneralDictionaryEntity> list = generalDictionaryService.connectImgList();
        return success(list);
    }
}
