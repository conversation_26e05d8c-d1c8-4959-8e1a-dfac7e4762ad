package com.yelink.dfs.controller.sys;

import cn.hutool.core.util.RandomUtil;
import com.yelink.dfs.common.BaseController;
import com.yelink.dfs.constant.RedisKeyPrefix;
import com.yelink.dfs.service.sys.InitDataService;
import com.yelink.dfscommon.constant.Constant;
import com.yelink.dfscommon.pojo.ResponseData;
import com.yelink.dfscommon.utils.DateUtil;
import com.yelink.dfscommon.utils.ExcelUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Date;

/**
 * @description: 系统初始化数据接口
 * @author: shuang
 * @time: 2023/2/22
 */
@RestController
@RequestMapping("/init/data")
@RequiredArgsConstructor
public class InitDataController extends BaseController {


    private final InitDataService initDataService;

    /**
     * 下载一次性导入数据的excel模板
     *
     * @param response
     * @return
     */
    @GetMapping("/import/template/download")
    public ResponseData downImportTemplate(HttpServletResponse response) throws Exception {
        initDataService.downloadDefaultTemplate("classpath:template/InitDataTemplate.xlsx", response, "初始化数据导入模板" + Constant.XLSX);
        return ResponseData.success();
    }


    /**
     * 按照excel模板一次性导入基础/基本数据
     *
     * @param file
     * @return
     */
    @PostMapping("/import/disposable")
    public ResponseData importBaseData(MultipartFile file) throws IOException {
        //1、判断导入文件的合法性
        ExcelUtil.checkImportExcelFile(file);
        String importProgressKey = RedisKeyPrefix.BASE_DATA_BATCH_IMPORT_PROGRESS + DateUtil.format(new Date(), DateUtil.yyyyMMddHHmmss_FORMAT) + "_" + RandomUtil.randomString(2);
        initDataService.sycImportData(file.getOriginalFilename(), file.getInputStream(), getUsername(), importProgressKey);
        return ResponseData.success(importProgressKey);
    }

    /**
     * 查询导入记录列表
     *
     * @param fileName
     * @param startTime
     * @param endTime
     * @param current
     * @param size
     * @return
     */
    @GetMapping("/import/record")
    public ResponseData recordList(@RequestParam(value = "fileName", required = false) String fileName,
                                   @RequestParam(value = "startTime", required = false) String startTime,
                                   @RequestParam(value = "endTime", required = false) String endTime,
                                   @RequestParam(value = "current", required = false, defaultValue = "1") Integer current,
                                   @RequestParam(value = "size", required = false, defaultValue = "10") Integer size) {
        return ResponseData.success(initDataService.recordList(current, size, fileName, startTime, endTime));
    }

    /**
     * 获取导入进度
     *
     * @return
     */
    @GetMapping("/import/progress")
    public ResponseData getSaleOrderImportProgress() {
        return success(initDataService.importProgress());
    }

}
