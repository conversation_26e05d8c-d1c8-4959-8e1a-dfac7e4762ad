package com.yelink.dfs.controller.model;

import com.yelink.dfs.common.BaseController;
import com.yelink.dfs.entity.model.CapacityLabelEntity;
import com.yelink.dfs.entity.model.CapacityLabelBasicUnitRelationEntity;
import com.yelink.dfs.entity.model.dto.CapacityLabelSelectDTO;
import com.yelink.dfs.service.model.CapacityLabelService;
import com.yelink.dfs.service.model.CapacityLabelBasicUnitRelationService;
import com.yelink.dfscommon.pojo.ResponseData;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 能力标签Controller
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/capacity/label")
public class CapacityLabelController extends BaseController {

    @Autowired
    private CapacityLabelService capacityLabelService;
    @Autowired
    private CapacityLabelBasicUnitRelationService relationService;

    /**
     * 根据标签编码查询标签信息
     * @param
     * @return
     */
    @PostMapping("/list")
    public ResponseData getList(@RequestBody CapacityLabelSelectDTO selectDTO) {
        return success(capacityLabelService.getList(selectDTO));
    }

    /**
     * 获取生产资源列表（设备、制造单元、班组）
     * @return
     */
    @GetMapping("/resource/list")
    public ResponseData productResourceList() {
        return success(capacityLabelService.productResourceList());
    }

    /**
     * 根据标签编码查询标签信息
     * @param
     * @return
     */
    @GetMapping("/detail")
    public ResponseData getByLabelCode(@RequestParam(value = "labelCode") String labelCode) {
        return success(capacityLabelService.getByLabelCode(labelCode));
    }

    /**
     * 新增标签
     * @param
     * @return
     */
    @PostMapping("/add")
    public ResponseData add(@RequestBody CapacityLabelEntity entity) {
        capacityLabelService.add(entity);
        return success();
    }

    /**
     * 修改标签
     * @param
     * @return
     */
    @PutMapping("/update")
    public ResponseData update(@RequestBody CapacityLabelEntity entity) {
        capacityLabelService.update(entity);
        return success();
    }

    /**
     * 删除标签
     * @param
     * @return
     */
    @DeleteMapping("/delete/{id}")
    public ResponseData delete(@PathVariable Integer id) {
        capacityLabelService.delete(id);
        return success();
    }

    /**
     * 查询标签关联的生产基本单元
     * @param
     * @return
     */
    @GetMapping("/relation")
    public ResponseData getRelationByLabelCode(@RequestParam(value = "labelCode") String labelCode) {
        return success(relationService.getByLabelCode(labelCode));
    }
}