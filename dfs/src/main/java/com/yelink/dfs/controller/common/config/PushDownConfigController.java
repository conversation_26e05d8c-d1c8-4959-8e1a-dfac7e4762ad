package com.yelink.dfs.controller.common.config;


import com.yelink.dfs.common.BaseController;
import com.yelink.dfs.entity.common.config.dto.PushDownConfigDTO;
import com.yelink.dfs.service.common.config.BusinessConfigService;
import com.yelink.dfscommon.constant.dfs.config.BomSplitTypeEnum;
import com.yelink.dfscommon.constant.dfs.config.CraftSplitTypeEnum;
import com.yelink.dfscommon.constant.dfs.config.WorkOrderSplittingRulesTypeEnum;
import com.yelink.dfscommon.dto.common.config.FullPathCodeDTO;
import com.yelink.dfscommon.entity.CommonType;
import com.yelink.dfscommon.pojo.ResponseData;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;

/**
 * 统一配置,下推配置
 *
 * <AUTHOR>
 * @since 2022-09-20 16:31:08
 */
@RestController
@AllArgsConstructor
@RequestMapping("/config/push/down")
public class PushDownConfigController extends BaseController {
    private final BusinessConfigService businessConfigService;


    /**
     * bom拆分方式枚举
     */
    @GetMapping("/bom/split")
    public ResponseData getBomSplitTypeEnum() {
        List<CommonType> list = new ArrayList<>();
        BomSplitTypeEnum[] values = BomSplitTypeEnum.values();
        for (BomSplitTypeEnum value : values) {
            list.add(CommonType.builder().code(value.getTypeCode()).name(value.getTypeName()).build());
        }
        return success(list);
    }

    /**
     * bom拆分方式枚举,不过滤采购品
     */
    @GetMapping("/not/filter/bom/split")
    public ResponseData getBomSplitTypeNotFilterEnum() {
        List<CommonType> list = new ArrayList<>();
        BomSplitTypeEnum[] values = BomSplitTypeEnum.values();
        for (BomSplitTypeEnum value : values) {
            if (BomSplitTypeEnum.FIRST_LEVEL_BOM == value || BomSplitTypeEnum.MULTI_LEVEL_BOM == value) {
                continue;
            }
            list.add(CommonType.builder().code(value.getTypeCode()).name(value.getTypeName()).build());
        }
        return success(list);
    }

    /**
     * 工艺拆分方式枚举
     */
    @GetMapping("/craft/split")
    public ResponseData getCraftSplitTypeEnum() {
        List<CommonType> list = new ArrayList<>();
        CraftSplitTypeEnum[] values = CraftSplitTypeEnum.values();
        for (CraftSplitTypeEnum value : values) {
            list.add(CommonType.builder().code(value.getTypeCode()).name(value.getTypeName()).build());
        }
        return success(list);
    }

    /**
     * 工单拆分规则枚举
     */
    @GetMapping("/work/order/split/rules")
    public ResponseData getWorkOrderSplittingRules() {
        List<CommonType> list = new ArrayList<>();
        WorkOrderSplittingRulesTypeEnum[] values = WorkOrderSplittingRulesTypeEnum.values();
        for (WorkOrderSplittingRulesTypeEnum value : values) {
            list.add(CommonType.builder().code(value.getTypeCode()).name(value.getTypeName()).build());
        }
        return success(list);
    }

    /**
     * 获取下推配置
     */
    @PostMapping("/get")
    public ResponseData getPushDownConfig(@RequestBody FullPathCodeDTO dto) {
        List<PushDownConfigDTO> configs = businessConfigService.getValueDtoList(dto, PushDownConfigDTO.class);
        return success(configs);
    }

}

