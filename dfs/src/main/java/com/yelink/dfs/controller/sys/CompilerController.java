package com.yelink.dfs.controller.sys;

import com.yelink.dfs.utils.SpringUtil;
import com.yelink.dfscommon.utils.MyClassLoader;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.beans.factory.support.BeanDefinitionBuilder;
import org.springframework.beans.factory.support.BeanDefinitionRegistry;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.util.ClassUtils;
import org.springframework.util.ReflectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.mvc.method.RequestMappingInfo;
import org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping;

import javax.tools.JavaCompiler;
import javax.tools.ToolProvider;
import java.io.File;
import java.lang.reflect.Method;

/**
 * @Description: 动态编译
 * @Author: zengzhengfu
 * @Date: 2022/8/18
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/sys")
public class CompilerController {

    private static final String JAVA_SUFFIX = ".java";
    private static final String CLASS_SUFFIX = ".class";

    @GetMapping("/compiler")
    public void deal() {
        String extendPath = getJarPath() + "extend/";
        //获取该路径下的所有java文件
        File parentFile = new File(extendPath);
        File[] files = parentFile.listFiles();
        for (File file : files) {
            String fileName = file.getName();
            if (fileName.endsWith(CLASS_SUFFIX)) {
                //删除class文件
                file.delete();
            }
            if (fileName.endsWith(JAVA_SUFFIX)) {
                //编译加载
                try {
                    compilerAndLoad(extendPath, fileName);
                } catch (Exception e) {
                    log.error("编译加载过程异常：", e);
                }
            }
        }
    }

    private void compilerAndLoad(String extendPath, String fileName) throws Exception {
        log.info("开始编译加载{}", fileName);
        //动态编译
        JavaCompiler javac = ToolProvider.getSystemJavaCompiler();
        int status = javac.run(null, null, null, "-d", extendPath, extendPath + fileName);
        if (status != 0) {
            log.info("{}编译失败！", fileName);
            return;
        }
        log.info("{}编译成功！", fileName);
        //beanName为首字母小写
        String beanName = lowerCaseToFirstLetter(fileName);
        try {
            //自定义类加载器的加载路径
            MyClassLoader myClassLoader = new MyClassLoader(extendPath);
            //包名+类名
            Class clazz = myClassLoader.loadClass(fileName.replace(JAVA_SUFFIX, ""));
            BeanDefinitionBuilder beanDefinitionBuilder = BeanDefinitionBuilder.genericBeanDefinition(clazz);
            registerBean(beanName, beanDefinitionBuilder.getRawBeanDefinition(), SpringUtil.getApplicationContext());
        } catch (ClassNotFoundException e) {
            log.info("{}bean注册失败", beanName);
            return;
        }

        final RequestMappingHandlerMapping requestMappingHandlerMapping = (RequestMappingHandlerMapping)
                SpringUtil.getBean("requestMappingHandlerMapping");

        if (requestMappingHandlerMapping != null) {
            Object bean = SpringUtil.getBean(beanName);
            if (bean == null) {
                return;
            }
            unregisterController(beanName);
            //注册Controller
            Method method = requestMappingHandlerMapping.getClass().getSuperclass().getSuperclass().
                    getDeclaredMethod("detectHandlerMethods", Object.class);
            method.setAccessible(true);
            method.invoke(requestMappingHandlerMapping, beanName);
            log.info("{} controller注册成功", beanName);
        }

    }

    private static void registerBean(String beanName, BeanDefinition beanDefinition, ApplicationContext context) {
        ConfigurableApplicationContext configurableApplicationContext = (ConfigurableApplicationContext) context;
        BeanDefinitionRegistry beanDefinitionRegistry = (BeanDefinitionRegistry) configurableApplicationContext.getBeanFactory();
        beanDefinitionRegistry.registerBeanDefinition(beanName, beanDefinition);
    }

    public static void unregisterController(String controllerBeanName) {
        final RequestMappingHandlerMapping requestMappingHandlerMapping = (RequestMappingHandlerMapping)
                SpringUtil.getBean("requestMappingHandlerMapping");
        if (requestMappingHandlerMapping != null) {
            String handler = controllerBeanName;
            Object controller = SpringUtil.getBean(handler);
            if (controller == null) {
                return;
            }
            final Class<?> targetClass = controller.getClass();
            ReflectionUtils.doWithMethods(targetClass, new ReflectionUtils.MethodCallback() {
                @Override
                public void doWith(Method method) {
                    Method specificMethod = ClassUtils.getMostSpecificMethod(method, targetClass);
                    try {
                        Method createMappingMethod = RequestMappingHandlerMapping.class.
                                getDeclaredMethod("getMappingForMethod", Method.class, Class.class);
                        createMappingMethod.setAccessible(true);
                        RequestMappingInfo requestMappingInfo = (RequestMappingInfo)
                                createMappingMethod.invoke(requestMappingHandlerMapping, specificMethod, targetClass);
                        if (requestMappingInfo != null) {
                            requestMappingHandlerMapping.unregisterMapping(requestMappingInfo);
                        }
                    } catch (Exception e) {
                        log.error("",e);
                    }
                }
            }, ReflectionUtils.USER_DECLARED_METHODS);
        }
    }

    private String getJarPath() {
        //获取jar包同级目录
        String path = CompilerController.class.getProtectionDomain().getCodeSource().getLocation().getPath();
        String[] pathSplit = path.split("/");
        String jarName = pathSplit[pathSplit.length - 1];
        String jarPath = path.replace(jarName, "");
        log.info("jar包路径：{}" , jarPath);
        return jarPath;
    }


    /**
     * 首字母转小写
     *
     * @param string
     * @return
     */
    private static String lowerCaseToFirstLetter(String string) {
        if (Character.isLowerCase(string.charAt(0))) {
            return string;
        } else {
            return Character.toLowerCase(string.charAt(0)) + string.substring(1);
        }

    }

}
