package com.yelink.dfs.controller.common;


import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yelink.dfs.common.BaseController;
import com.yelink.dfs.constant.DictTypeEnum;
import com.yelink.dfs.constant.OperationType;
import com.yelink.dfs.entity.product.dto.MaterialTypeAddDTO;
import com.yelink.dfs.open.v1.common.dto.DictDTO;
import com.yelink.dfs.open.v1.common.dto.DictInsertDTO;
import com.yelink.dfs.open.v1.common.dto.TermsSelectDTO;
import com.yelink.dfs.service.common.DictService;
import com.yelink.dfs.service.common.config.BusinessConfigService;
import com.yelink.dfscommon.annotation.OperLog;
import com.yelink.dfscommon.common.unit.constant.RoundingType;
import com.yelink.dfscommon.constant.ParamException;
import com.yelink.dfscommon.constant.dfs.config.ConfigConstant;
import com.yelink.dfscommon.dto.common.config.FullPathCodeDTO;
import com.yelink.dfscommon.entity.CommonType;
import com.yelink.dfscommon.entity.dfs.DictEntity;
import com.yelink.dfscommon.pojo.ResponseData;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;
import java.util.List;
import java.util.Map;


/**
 * @Description: 基础管理
 * @Author: zengzhengfu
 * @Date: 2021/4/8
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/dicts")
public class DictController extends BaseController {

    private DictService dictService;
    private BusinessConfigService businessConfigService;


    /**
     * ============================条款信息==============================
     */
    /**
     * 条款列表
     *
     * @return
     */
    @PostMapping("/terms/list")
    public ResponseData termsList(@RequestBody TermsSelectDTO selectDTO) {
        Page<DictEntity> list = dictService.termsList(selectDTO);
        return success(list);
    }

    /**
     * 批量查询条款列表
     *
     * @param ids 多个条款id，逗号隔开
     * @return
     */
    @GetMapping("/terms/batch/list")
    public ResponseData termsBatchList(@RequestParam String ids) {
        List<DictEntity> list = dictService.termsBatchList(ids);
        return success(list);
    }

    /**
     * 获取条款详情信息
     */
    @GetMapping("/terms/detail")
    public ResponseData termsDetailById(@RequestParam String id) {
        DictEntity dictEntity = dictService.getById(id);
        return success(dictEntity);
    }


    /**
     * 获取条款类型
     *
     * @return
     */
    @GetMapping("/terms/type")
    public ResponseData termsType() {
        List<CommonType> list = dictService.termsType();
        return success(list);
    }

    /**
     * 新增条款信息
     *
     * @param entity
     * @return
     */
    @PostMapping("/terms/insert")
    @OperLog(module = "基础管理", type = OperationType.ADD, desc = "新增了名称为：#{name}，id为#{id}的条款")
    public ResponseData addTerms(@RequestBody @Validated({DictEntity.TermsInsert.class}) DictEntity entity, BindingResult bindingResult) {
        if (bindingResult.hasErrors()) {
            throw new ParamException(bindingResult);
        }
        String username = getUsername();
        entity.setCreateBy(username);
        entity.setUpdateBy(username);
        dictService.addTerms(entity);
        return success(entity);
    }

    /**
     * 修改条款信息
     *
     * @param entity
     * @param bindingResult
     * @return
     */
    @PutMapping("/terms/update")
    @OperLog(module = "基础管理", type = OperationType.UPDATE, desc = "修改了名称为：#{name}，id为#{id}的条款")
    public ResponseData updateTerms(@RequestBody @Validated({DictEntity.TermsUpdate.class}) DictEntity entity, BindingResult bindingResult) {
        if (bindingResult.hasErrors()) {
            throw new ParamException(bindingResult);
        }
        String username = getUsername();
        entity.setUpdateBy(username);
        dictService.updateTerms(entity);
        return success(entity);
    }

    /**
     * 删除条款信息
     *
     * @param id
     * @return
     */
    @DeleteMapping("/terms/delete/{id}")
    @OperLog(module = "基础管理", type = OperationType.DELETE, desc = "删除id为#{id},名称为#{name}的条款")
    public ResponseData delete(@PathVariable Integer id) {
        DictEntity entity = dictService.getById(id);
        dictService.deleteTerms(id);
        return success(entity);
    }


    /**
     * =============================单位信息=============================
     */
    /**
     * 查询单位列表
     *
     * @param name
     * @param id
     * @param current
     * @param size
     * @return
     */
    @GetMapping("/unit/list")
    public ResponseData unitList(
            @RequestParam(value = "name", required = false) String name,
            @RequestParam(value = "type", required = false) String type,
            @RequestParam(value = "id", required = false) Integer id,
            @RequestParam(value = "currentPage", required = false) Integer current,
            @RequestParam(value = "size", required = false) Integer size) {
        Page<DictEntity> list = dictService.unitList(name, type, id, current, size);
        return success(list);
    }


    /**
     * 新增单位信息
     *
     * @param entity
     * @param bindingResult
     * @return
     */
    @PostMapping("/unit/insert")
    @OperLog(module = "基础管理", type = OperationType.ADD, desc = "新增了名称为：#{name}，id为#{id}的单位")
    public ResponseData addUnits(@RequestBody @Validated({DictEntity.UnitInsert.class}) DictEntity entity, BindingResult bindingResult) {
        if (bindingResult.hasErrors()) {
            throw new ParamException(bindingResult);
        }
        String username = getUsername();
        entity.setCreateBy(username);
        entity.setCreateTime(new Date());
        dictService.addUnits(entity);
        return success(entity);
    }

    /**
     * 获取物料的单位精度
     */
    @GetMapping("/unit/accuracy_long")
    public ResponseData accuracyLong(@RequestParam String materialCode) {
        return ResponseData.success(dictService.accuracyLong(materialCode));
    }
    /**
     * 获取物料集合的单位精度
     */
    @PostMapping("/unit/accuracy_longs")
    public ResponseData accuracyLongs(@RequestBody List<String> materialCodes) {
        return ResponseData.success(dictService.accuracyLongs(materialCodes));
    }

    /**
     * 单位精度的舍入类型 列表
     */
    @GetMapping("/unit/rounding_type/list")
    public ResponseData roundingTypeList() {
        return ResponseData.success(CommonType.covertToList(RoundingType.class));
    }


    /**
     * 修改单位信息
     *
     * @param entity
     * @param bindingResult
     * @return
     */
    @PutMapping("/unit/update")
    @OperLog(module = "基础管理", type = OperationType.UPDATE, desc = "修改了名称为：#{name}，id为#{id}的单位")
    public ResponseData updateUnit(@RequestBody @Validated({DictEntity.UnitUpdate.class}) DictEntity entity, BindingResult bindingResult) {
        if (bindingResult.hasErrors()) {
            throw new ParamException(bindingResult);
        }
        String username = getUsername();
        entity.setUpdateBy(username);
        entity.setUpdateTime(new Date());
        dictService.updateUnit(entity);
        return success(entity);
    }

    /**
     * 删除单位
     *
     * @param id
     * @return
     */
    @DeleteMapping("/unit/delete/{id}")
    @OperLog(module = "基础管理", type = OperationType.DELETE, desc = "删除id为#{id},名称为#{name}的单位")
    public ResponseData deleteUnit(@PathVariable Integer id) {
        DictEntity entity = dictService.getById(id);
        dictService.deleteUnit(id);
        return success(entity);
    }

    /**
     * 查询通用类型列表
     *
     * @param dto
     * @return
     */
    @GetMapping("/common/type/list")
    public ResponseData commonTypeList(DictDTO dto, @RequestParam(value = "currentPage", required = false) Integer current) {
        dto.setCurrent(current);
        Page<DictEntity> list = dictService.commonTypeList(dto);
        return success(list);
    }

    /**
     * 新增物料类型信息
     *
     * @param entity
     * @param bindingResult
     * @return
     */
    @PostMapping("/material/type/insert")
    @OperLog(module = "基础管理", type = OperationType.ADD, desc = "新增了名称为：#{name}，id为#{id}的物料类型")
    public ResponseData addMaterialType(@RequestBody @Validated MaterialTypeAddDTO entity, BindingResult bindingResult) {
        if (bindingResult.hasErrors()) {
            throw new ParamException(bindingResult);
        }
        String username = getUsername();
        entity.getDictEntity().setCreateBy(username);
        entity.getDictEntity().setUpdateBy(username);
        entity.getDictEntity().setCreateTime(new Date());
        entity.getDictEntity().setUpdateTime(new Date());
        dictService.addMaterialType(entity);
        return success(entity);
    }

    /**
     * 修改物料类型信息
     */
    @PutMapping("/material/type/update")
    @OperLog(module = "基础管理", type = OperationType.UPDATE, desc = "修改了名称为：#{name}，id为#{id}的物料类型")
    public ResponseData updateMaterialType(@RequestBody @Validated MaterialTypeAddDTO entity, BindingResult bindingResult) {
        if (bindingResult.hasErrors()) {
            throw new ParamException(bindingResult);
        }
        entity.getDictEntity().setUpdateBy(getUsername());
        entity.getDictEntity().setUpdateTime(new Date());
        dictService.updateMaterialType(entity);
        return success(entity);
    }



    /**
     * 删除物料类型信息
     *
     * @param id
     * @return
     */
    @DeleteMapping("/material/type/delete/{id}")
    @OperLog(module = "基础管理", type = OperationType.DELETE, desc = "删除id为#{id},名称为#{name}的物料类型")
    public ResponseData deleteMaterialType(@PathVariable Integer id) {
        DictEntity entity = dictService.getById(id);
        dictService.removeMaterialTypeById(id);
        return success(entity);
    }

    /**
     * 新增设备类型信息
     *
     * @param entity
     * @param bindingResult
     * @return
     */
    @PostMapping("/device/sort/insert")
    @OperLog(module = "基础管理", type = OperationType.ADD, desc = "新增了名称为：#{name}，id为#{id}的设备类型")
    public ResponseData addDeviceSort(@RequestBody @Validated({DictEntity.MaterialTypeInsert.class}) DictEntity entity, BindingResult bindingResult) {
        if (bindingResult.hasErrors()) {
            throw new ParamException(bindingResult);
        }
        entity.setCreateBy(getUsername());
        entity.setCreateTime(new Date());
        entity.setUpdateTime(new Date());
        dictService.addDeviceSort(entity);
        return success(entity);
    }

    /**
     * 修改设备类型信息
     */
    @PutMapping("/device/sort/update")
    @OperLog(module = "基础管理", type = OperationType.UPDATE, desc = "修改了名称为：#{name}，id为#{id}的设备类型")
    public ResponseData updatDeviceSort(@RequestBody @Validated({DictEntity.MaterialTypeUpdate.class}) DictEntity entity, BindingResult bindingResult) {
        if (bindingResult.hasErrors()) {
            throw new ParamException(bindingResult);
        }
        entity.setUpdateBy(getUsername());
        entity.setUpdateTime(new Date());
        dictService.updatDeviceSort(entity);
        return success(entity);
    }

    /**
     * 删除设备类型信息
     *
     * @param id
     * @return
     */
    @DeleteMapping("/device/sort/delete/{id}")
    @OperLog(module = "基础管理", type = OperationType.DELETE, desc = "删除id为#{id},名称为#{name}的设备类型")
    public ResponseData deleteDeviceSort(@PathVariable Integer id) {
        DictEntity entity = dictService.getById(id);
        dictService.deleteDeviceSortById(id);
        return success(entity);
    }


    /**
     * 获取数据详细信息
     *
     * @param id
     * @return
     */
    @DeleteMapping("/detail/{id}")
    public ResponseData detailDeviceSort(@PathVariable Integer id) {
        DictEntity entity = dictService.detail(id);
        return success(entity);
    }

    /**
     * 获取首班最早时间
     *
     * @return
     */
    @GetMapping("/get/begin/time")
    public ResponseData getBeginTimeOfDay() {
        DictEntity entity = dictService.getBeginTimeEntity();
        return success(entity);
    }

    /**
     * 设置首班最早时间
     *
     * @return
     */
    @GetMapping("/set/begin/time")
    @OperLog(module = "首班开始时间", type = OperationType.UPDATE, desc = "修改首班开始时间")
    public ResponseData setBeginTimeOfDay(@RequestParam(value = "time") String time) {
        String username = getUsername();
        dictService.setBeginTimeOfDay(time, username);
        return success();
    }

    /**
     * 删除手动采集指标
     *
     * @param id
     * @return
     */
    @DeleteMapping("/manual/target/delete/{id}")
    @OperLog(module = "基础管理", type = OperationType.DELETE, desc = "删除id为#{id},名称为#{name}的手动采集指标")
    public ResponseData deleteManualTarget(@PathVariable Integer id) {
        DictEntity entity = dictService.getById(id);
        dictService.removeMaterialTypeById(id);
        return success(entity);
    }

    /**
     * 获取去重后的物料类型名称
     *
     * @param
     * @return
     */
    @GetMapping("/distinct/type/name/list")
    public ResponseData getDistinctTypeNameList(@RequestParam(value = "typeName") String typeName) {
        return success(dictService.getDistinctTypeNameList(typeName));
    }


    /**
     * 获取生产基本单元默认配置
     *
     * @param
     * @return
     */
    @GetMapping("/production/basic/unit/get")
    public ResponseData getProductionBasicUnit() {
        FullPathCodeDTO dto = FullPathCodeDTO.builder()
                .fullPathCode(ConfigConstant.PRODUCTION_BASIC_UNIT).build();
        Map<String, String> valueMap = businessConfigService.getValueMap(dto);
        String type = DictTypeEnum.PRODUCTION_BASIC_UNIT.getType();
        String productionBasicUnit = JSON.parseObject(valueMap.get(type), String.class);
        if (StringUtils.isBlank(productionBasicUnit)) {
            throw new RuntimeException("无生产基本单元默认配置");
        }
        DictEntity dictEntity = DictEntity.builder().code(productionBasicUnit).type(type).build();
        return success(dictEntity);
    }


    /**
     * 修改生产基本单元默认配置
     *
     * @param entity
     * @param bindingResult
     * @return
     */
    @PutMapping("/production/basic/unit/update")
    @OperLog(module = "基础管理", type = OperationType.UPDATE, desc = "修改了名称为：#{name}，id为#{id}的基本单元默认配置")
    public ResponseData editProductionBasicUnit(@RequestBody @Validated({DictEntity.UnitUpdate.class}) DictEntity entity, BindingResult bindingResult) {
        if (bindingResult.hasErrors()) {
            throw new ParamException(bindingResult);
        }
        String username = getUsername();
        entity.setUpdateBy(username);
        entity.setUpdateTime(new Date());
        dictService.updateById(entity);
        return success(entity);
    }

    /**
     * 查询物料检验方式枚举
     *
     * @return
     */
    @GetMapping("/material/inspect/method")
    public ResponseData getInspectMethod() {
        return ResponseData.success(dictService.getMaterialInspectMethod());
    }

    /**
     * 查询物料检验触发条件枚举
     *
     * @return
     */
    @GetMapping("/material/inspect/trigger/condition")
    public ResponseData getMaterialInspectTriggerCondition() {
        return ResponseData.success(dictService.getMaterialInspectTriggerCondition());
    }

    /**
     * 查询工艺工序检验方式枚举
     *
     * @return
     */
    @GetMapping("/craft_procedure/inspect/method")
    public ResponseData getCraftProcedureInspectMethod(@RequestParam(value = "inspectTypeName") String inspectTypeName) {
        return ResponseData.success(dictService.getCraftProcedureInspectMethod(inspectTypeName));
    }

    /**
     * 查询工艺工序检验触发条件枚举
     *
     * @param inspectTypeName 检验类型名称
     * @return
     */
    @GetMapping("/craft_procedure/inspect/trigger/condition")
    public ResponseData getCraftProcedureInspectTriggerCondition(@RequestParam(value = "inspectTypeName") String inspectTypeName) {
        return ResponseData.success(dictService.getCraftProcedureInspectTriggerCondition(inspectTypeName));
    }

    /**
     * 键值对数据,类似redis - set
     */
    @PostMapping("/set/value")
    public ResponseData setValue(@RequestBody DictInsertDTO dictInsert) {
        dictService.setValue(dictInsert);
        return ResponseData.success();
    }

    /**
     * 键值对数据,类似redis - get
     */
    @GetMapping("/get/value")
    public ResponseData getValue(@RequestParam String code) {
        DictEntity one = dictService.lambdaQuery()
                .eq(DictEntity::getType, DictTypeEnum.KEY_VALUE.getType())
                .eq(DictEntity::getCode, code)
                .last("limit 1").one();
        return ResponseData.success(one);
    }
}

