package com.yelink.dfs.controller.code;

import com.yelink.dfs.common.BaseController;
import com.yelink.dfs.constant.code.ScanCodeRuleDataFormatEnum;
import com.yelink.dfs.constant.code.ScanCodeRuleResolverTypeEnum;
import com.yelink.dfs.constant.code.ScanCodeRuleStateEnum;
import com.yelink.dfs.constant.code.ScanCodeRuleTypeEnum;
import com.yelink.dfs.entity.code.ScanCodeRuleEntity;
import com.yelink.dfs.service.code.ScanCodeRuleService;
import com.yelink.dfscommon.dto.ScanCodeRuleGetDTO;
import com.yelink.dfscommon.dto.ScanCodeRuleResolverDTO;
import com.yelink.dfscommon.dto.ScanCodeRuleSelectDTO;
import com.yelink.dfscommon.entity.CommonType;
import com.yelink.dfscommon.pojo.ResponseData;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;

/**
 * <AUTHOR>
 * @Description 扫码规则
 * @Date 2022/3/31 14:36
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/scan/code/rule")
public class ScanCodeRuleController extends BaseController {

    private ScanCodeRuleService scanCodeRuleService;

    /**
     * 获取解析规则列表
     *
     * @return
     */
    @PostMapping("/list")
    public ResponseData list(@RequestBody ScanCodeRuleSelectDTO scanCodeRuleSelectDTO) {
        return success(scanCodeRuleService.list(scanCodeRuleSelectDTO));
    }

    /**
     * 获取解析规则列表
     *
     * @return
     */
    @PostMapping("/get")
    public ResponseData get(@RequestBody ScanCodeRuleGetDTO scanCodeRuleGetDTO) {
        return success(scanCodeRuleService.getByCode(scanCodeRuleGetDTO));
    }

    /**
     * 添加解析规则
     *
     * @param scanCodeRuleEntity
     * @return
     */
    @PostMapping("/add")
    public ResponseData add(@RequestBody ScanCodeRuleEntity scanCodeRuleEntity) {
        scanCodeRuleEntity.setId(null);
        scanCodeRuleEntity.setCreateBy(getUsername());
        scanCodeRuleEntity.setCreateTime(new Date());
        scanCodeRuleService.add(scanCodeRuleEntity);
        return success();
    }

    /**
     * 修改解析规则
     *
     * @param scanCodeRuleEntity
     * @return
     */
    @PutMapping("/edit")
    public ResponseData edit(@RequestBody ScanCodeRuleEntity scanCodeRuleEntity) {
        scanCodeRuleEntity.setUpdateBy(getUsername());
        scanCodeRuleEntity.setUpdateTime(new Date());
        scanCodeRuleService.edit(scanCodeRuleEntity);
        return success();
    }

    /**
     * 删除解析规则
     *
     * @param id
     * @return
     */
    @DeleteMapping("/delete/{id}")
    public ResponseData delete(@PathVariable Integer id) {
        scanCodeRuleService.removeById(id);
        return success();
    }

    /**
     * 解析规则详情
     *
     * @param id
     * @return
     */
    @GetMapping("/detail/{id}")
    public ResponseData detail(@PathVariable Integer id) {
        return success(scanCodeRuleService.detailById(id));
    }
    /**
     * 条码解析
     *
     * @return
     */
    @PostMapping("/resolver")
    public ResponseData resolver(@RequestBody ScanCodeRuleResolverDTO dto) {
        return success(scanCodeRuleService.resolver(dto));
    }

    /**
     * 条码解析测试
     *
     * @return
     */
    @PostMapping("/resolver/test")
    public ResponseData resolverTest(@RequestBody ScanCodeRuleEntity scanCodeRuleEntity) {
        return success(scanCodeRuleService.resolverTest(scanCodeRuleEntity));
    }

    /**
     * 状态列表
     *
     * @return
     */
    @GetMapping("/state/list")
    public ResponseData stateList() {
        return ResponseData.success(CommonType.covertToList(ScanCodeRuleStateEnum.class));
    }

    /**
     * 类型列表
     *
     * @return
     */
    @GetMapping("/type/list")
    public ResponseData typeList() {
        return ResponseData.success(CommonType.covertToList(ScanCodeRuleTypeEnum.class));
    }

    /**
     * 解码方法类型列表
     *
     * @return
     */
    @GetMapping("/resolver/type/list")
    public ResponseData resolverTypeList() {
        return ResponseData.success(CommonType.covertToList(ScanCodeRuleResolverTypeEnum.class));
    }

    /**
     * 解码返回数据格式列表
     *
     * @return
     */
    @GetMapping("/data/format/list")
    public ResponseData dataFormatList() {
        return ResponseData.success(CommonType.covertToList(ScanCodeRuleDataFormatEnum.class));
    }

}
