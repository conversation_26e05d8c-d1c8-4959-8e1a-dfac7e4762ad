package com.yelink.dfs.controller.alarm;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yelink.dfs.common.BaseController;
import com.yelink.dfs.constant.DictTypeEnum;
import com.yelink.dfs.constant.OperationType;
import com.yelink.dfs.constant.alarm.AlarmDealStateEnum;
import com.yelink.dfs.constant.alarm.AlarmTypesEnum;
import com.yelink.dfs.constant.sensor.SensorTypeCodeEnum;
import com.yelink.dfs.entity.alarm.AlarmEntity;
import com.yelink.dfs.entity.alarm.SensorType;
import com.yelink.dfs.entity.alarm.dto.AlarmAddDTO;
import com.yelink.dfs.entity.alarm.dto.AlarmAssignDTO;
import com.yelink.dfs.entity.alarm.dto.AlarmDealDTO;
import com.yelink.dfs.entity.alarm.dto.AlarmInsertDTO;
import com.yelink.dfs.entity.alarm.dto.AlarmPageDTO;
import com.yelink.dfs.entity.alarm.dto.AlarmUpdateDTO;
import com.yelink.dfs.entity.alarm.dto.ExceptionManagerListDTO;
import com.yelink.dfs.entity.alarm.dto.PadAddAlarmDTO;
import com.yelink.dfs.entity.alarm.dto.PadUpdateAlarmDTO;
import com.yelink.dfs.entity.alarm.dto.PadUpdateAlarmExtendDTO;
import com.yelink.dfs.entity.user.MyAuthenticationUserDetail;
import com.yelink.dfs.service.alarm.AlarmService;
import com.yelink.dfs.service.common.DictService;
import com.yelink.dfscommon.annotation.OperLog;
import com.yelink.dfscommon.constant.ParamException;
import com.yelink.dfscommon.constant.RespCodeEnum;
import com.yelink.dfscommon.entity.CommonType;
import com.yelink.dfscommon.entity.dfs.DictEntity;
import com.yelink.dfscommon.pojo.ResponseData;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * @description: 告警信息接口
 * @author: yejiarun
 * @time: 2020/12/10
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/alarms")
public class AlarmController extends BaseController {

    private AlarmService alarmService;
    private DictService dictService;

    /**
     * @param number  告警状态码
     * @param code    告警聚合码
     * @param state   处理状态
     * @param type    传感器类型
     * @param current
     * @param size
     * @return
     */
    @GetMapping("/list")
    public ResponseData list(@RequestParam(value = "number", required = false) String number,
                             @RequestParam(value = "code", required = false) String code,
                             @RequestParam(value = "state", required = false) String state,
                             @RequestParam(value = "type", required = false) Integer type,
                             @RequestParam(value = "lineId", required = false) Integer lineId,
                             @RequestParam(value = "fid", required = false) Integer fid,
                             @RequestParam(value = "modelType", required = false) String modelType,
                             @RequestParam(value = "current", required = false) Integer current,
                             @RequestParam(value = "size", required = false) Integer size,
                             @RequestParam(value = "deviceCode", required = false) String deviceCode,
                             @RequestParam(value = "alarmType", required = false) String alarmType) {
        Page<AlarmEntity> list = alarmService.getList(number, code, state, type, lineId, fid, modelType, current, size, deviceCode, alarmType);
        return success(list);
    }

    /**
     * 获取告警类型
     *
     * @return
     */
    @GetMapping("/type/alarm")
    public ResponseData getAlarmType() {
        QueryWrapper<DictEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(DictEntity::getType, DictTypeEnum.ALARM_TYPE.getType());
        List<DictEntity> list = dictService.list(queryWrapper);
        return success(list);
    }

    /**
     * 获取传感器类型
     *
     * @return
     */
    @GetMapping("/type/sensor")
    public ResponseData getSensorType() {
        List<SensorType> list = new ArrayList<>();
        SensorTypeCodeEnum[] values = new SensorTypeCodeEnum[]{
                SensorTypeCodeEnum.TEMPERATURE_AND_HUMIDITY_SENSOR,
                SensorTypeCodeEnum.COUNTER_SENSOR,
                SensorTypeCodeEnum.WRISTBAND,
                SensorTypeCodeEnum.CUBICLE_BOARD,
                SensorTypeCodeEnum.LINE_BOARD,
        };
        for (SensorTypeCodeEnum value : values) {
            SensorType type = new SensorType();
            type.setCode(value.getCode());
            type.setName(value.getName());
            list.add(type);
        }
        return success(list);
    }


    /**
     * 通过告警Id查询告警信息
     *
     * @param id
     * @return
     */
    @GetMapping("/select/{id}")
    public ResponseData selectById(@PathVariable Integer id) {
        AlarmEntity entity = alarmService.getById(id);
        if (entity == null) {
            return fail(RespCodeEnum.ALARM_FAIL2SEL);
        }
        return success(entity);
    }


    /**
     * 通过处理状态码获取告警信息
     * 处理状态：0-未处理 1-处理中 2-已处理
     *
     * @param code
     * @return
     */
    @GetMapping("/select/state/{code}")
    public ResponseData selectByStateCode(@PathVariable Integer code) {
        QueryWrapper<AlarmEntity> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(AlarmEntity::getDealState, code);
        List<AlarmEntity> list = alarmService.list(wrapper);
        return success(list);
    }

    /**
     * 根据是否误报获取告警信息
     * 处理结果：是否误报：0 否 ，1 是误报
     *
     * @param result
     * @return
     */
    @GetMapping("/select/result/{result}")
    public ResponseData selectByResult(@PathVariable String result) {
        QueryWrapper<AlarmEntity> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(AlarmEntity::getDealResult, result);
        List<AlarmEntity> list = alarmService.list(wrapper);
        return success(list);
    }

    /**
     * 通过公司ID获取告警信息
     *
     * @param cid
     * @return
     */
    @GetMapping("/select/company/{cid}")
    public ResponseData selectByCompanyId(@PathVariable Integer cid) {
        QueryWrapper<AlarmEntity> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(AlarmEntity::getCid, cid);
        List<AlarmEntity> list = alarmService.list(wrapper);
        return success(list);
    }

    /**
     * 通过厂区ID获取告警信息
     *
     * @param aid
     * @return
     */
    @GetMapping("/select/area/{aid}")
    public ResponseData selectByAreaId(@PathVariable Integer aid) {
        QueryWrapper<AlarmEntity> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(AlarmEntity::getAid, aid);
        List<AlarmEntity> list = alarmService.list(wrapper);
        return success(list);
    }

    /**
     * 通过车间ID获取告警信息
     *
     * @param gid
     * @return
     */
    @GetMapping("/select/grid/{gid}")
    public ResponseData selectByGridId(@PathVariable Integer gid) {
        QueryWrapper<AlarmEntity> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(AlarmEntity::getGid, gid);
        List<AlarmEntity> list = alarmService.list(wrapper);
        return success(list);
    }

    /**
     * 通过产线ID获取告警信息
     *
     * @param pid
     * @return
     */
    @GetMapping("/select/line/{pid}")
    public ResponseData selectByProlineId(@PathVariable Integer pid) {
        QueryWrapper<AlarmEntity> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(AlarmEntity::getProductionLineId, pid);
        List<AlarmEntity> list = alarmService.list(wrapper);
        return success(list);
    }

    /**
     * 通过设施ID获取告警信息
     *
     * @param fid
     * @return
     */
    @GetMapping("/select/faci/{fid}")
    public ResponseData selectByFacilitiesId(@PathVariable Integer fid) {
        QueryWrapper<AlarmEntity> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(AlarmEntity::getFid, fid);
        List<AlarmEntity> list = alarmService.list(wrapper);
        return success(list);
    }

    /**
     * 通过传感器编号获取告警信息
     *
     * @param eui
     * @return
     */
    @GetMapping("/select/sensor/{eui}")
    public ResponseData selectBySensorId(@PathVariable String eui) {
        QueryWrapper<AlarmEntity> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(AlarmEntity::getEui, eui);
        List<AlarmEntity> list = alarmService.list(wrapper);
        return success(list);
    }


    /**
     * 供前端插入数据调试用
     *
     * @param entity
     * @return
     */
    @PostMapping("/insert")
    public ResponseData add(@RequestBody AlarmEntity entity) {
        boolean save = alarmService.save(entity);
        if (save) {
            return success();
        }
        return fail("添加失败");
    }


    /**
     * 处理告警
     *
     * @param
     * @return
     */
    @PostMapping("/deal")
    public ResponseData deal(@RequestBody AlarmEntity entity) {
        String username = getUsername();
        MyAuthenticationUserDetail userInfo = getUserInfo();
        entity.setUpdateBy(username);
        entity.setDealName(userInfo.getNickname());
        entity.setDealPhone(userInfo.getMobile());
        entity.setDealTime(new Date());
        alarmService.deal(entity);
        return success();
    }


    /**
     * 获取告警级别
     *
     * @param
     * @return
     */
    @GetMapping("/level")
    public ResponseData getAlarmLevel() {
        QueryWrapper<DictEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(DictEntity::getType, DictTypeEnum.ALARM_LEVEL.getType());
        List<DictEntity> list = dictService.list(queryWrapper);
        return success(list);
    }

    /**
     * 获取告警修复建议
     *
     * @param
     * @return
     */
    @GetMapping("/advice")
    public ResponseData getAlarmAdvice() {
        QueryWrapper<DictEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(DictEntity::getType, DictTypeEnum.ALARM_ADVICE.getType());
        List<DictEntity> list = dictService.list(queryWrapper);
        return success(list);
    }

    /**
     * 查询故障报警列表
     *
     * @param
     * @return
     */
    @GetMapping("/fault/alarm/list")
    public ResponseData getFaultAlarmList(@RequestParam(value = "alarmDefinitionCode", required = false) String alarmDefinitionCode,
                                          @RequestParam(value = "alarmCodeId", required = false) String alarmCodeId,
                                          @RequestParam(value = "deviceId") Integer deviceId,
                                          @RequestParam(value = "alarmDes", required = false) String alarmDes,
                                          @RequestParam(value = "reportStartTime", required = false) String reportStartTime,
                                          @RequestParam(value = "reportEndTime", required = false) String reportEndTime,
                                          @RequestParam(value = "current", required = false) Integer current,
                                          @RequestParam(value = "size", required = false) Integer size) {
        Page<AlarmEntity> alarmList = alarmService.getFaultAlarmList(reportStartTime, reportEndTime, alarmDefinitionCode, alarmCodeId,
                alarmDes, deviceId, current, size, false);
        return success(alarmList);
    }

    /**
     * 批量手动恢复告警
     *
     * @param list
     * @param bindingResult
     * @return
     */
    @PutMapping("/recovery/alarm")
    @OperLog(module = "设备管理", type = OperationType.UPDATE, desc = "批量恢复了告警编号ID为#{alarmCodeId}的告警")
    public ResponseData batchRecoveryAlarm(@RequestBody List<AlarmEntity> list, BindingResult bindingResult) {
        if (bindingResult.hasErrors()) {
            throw new ParamException(bindingResult);
        }
        String username = getUsername();
        //返回的列表是没有处理成功的数据
        List<AlarmEntity> alarmEntities = alarmService.batchRecoveryAlarm(list, username);
        return success(alarmEntities);
    }

    /**
     * 获取恢复类型
     *
     * @param
     * @return
     */
    @GetMapping("/recovery/type")
    public ResponseData getAlarmRecoveryType() {
        return success(alarmService.getAlarmRecoveryTypes());
    }

    /**
     * 获取告警上报人（包含系统上报人）
     *
     * @param
     * @return
     */
    @GetMapping("/report/personnel")
    public ResponseData getAlarmReportPersonnel() {
        return ResponseData.success(alarmService.getAlarmReportPersonnel());
    }


    /**
     * 获取告警ID
     *
     * @return
     */
    @GetMapping("/alarm/code")
    public ResponseData getAlarmId() {
        return success(alarmService.getAlarmId());
    }

    /**
     * 分页查询告警列表
     */
    @GetMapping("/page/list")
    public ResponseData list(AlarmPageDTO dto) {
        return success(alarmService.listByPage(dto));
    }

    @GetMapping("/alarm-type/list")
    public ResponseData stateList() {
        return ResponseData.success(CommonType.covertToList(AlarmTypesEnum.class));
    }


    /**
     * 告警批量导出
     *
     * @return
     */
    @GetMapping("/alarm/export")
    public void exportAlarmList(@RequestParam(value = "alarmType", required = false) String alarmType,
                                @RequestParam(value = "alarmCodeId", required = false) String alarmCodeId,
                                HttpServletResponse response) throws IOException {
        if (null != alarmCodeId) {
            String[] split = alarmCodeId.split(",");
            alarmService.exportAlarmList(alarmType, split, response);
        }

    }

    /**
     * 历史告警导出
     *
     */
    @GetMapping("/alarm/exports")
    public void exportAlarmList(HttpServletResponse response, AlarmPageDTO dto) throws IOException {
        alarmService.exportAlarmLists(response, dto);
    }


    /**
     * 根据id查询详细信息
     *
     * @param alarmId
     * @return
     */
    @GetMapping("/detail/{alarmId}")
    public ResponseData getAlarmByCode(@PathVariable("alarmId") Integer alarmId) {
        AlarmEntity entity = alarmService.getAlarmByCode(alarmId);
        return ResponseData.success(entity);
    }

    /**
     * 修改指定处理人
     *
     * @param alarmEntity
     * @return
     */
    @PutMapping("/update")
    @OperLog(module = "设备管理", type = OperationType.UPDATE, desc = "修改了告警编号ID为#{alarmCodeId}的告警处理人")
    public ResponseData updateByAlarmId(@RequestBody AlarmEntity alarmEntity) {
        String username = getUsername();
        alarmEntity.setUpdateBy(username);
        alarmEntity.setAlarmUpdateTime(new Date());
        alarmEntity.setUpdateTime(new Date());
        return success(alarmService.updateByAlarmId(alarmEntity));
    }

    /**
     * 告警新增
     */
    @PostMapping("/add")
    public ResponseData manualAddAlarm(@RequestBody @Valid AlarmAddDTO dto) {
        alarmService.add(dto);
        return success();
    }

    /**
     * 告警新增——(pad端）
     *
     * @param entity
     * @return
     */
    @PostMapping("/pad/add")
    public ResponseData addEventRecordAtPad(@RequestBody PadAddAlarmDTO entity) {
        String reportPerson = entity.getReportPerson();
        String username = getUsername();
        entity.setReportPerson(StringUtils.isBlank(reportPerson) ? username : reportPerson);
        entity.setCreateBy(username);
        return success(alarmService.addAlarmRecordAtPad(entity));
    }
    @PostMapping("/pad/edit")
    public ResponseData editEventRecordAtPad(@RequestBody @Validated PadUpdateAlarmDTO dto) {
        alarmService.editAlarmRecordAtPad(dto);
        return success();
    }
    @PostMapping("/pad/edit/extend")
    public ResponseData editEventRecordExtendAtPad(@RequestBody @Validated PadUpdateAlarmExtendDTO dto) {
        alarmService.editEventRecordExtendAtPad(dto);
        return success();
    }


    /**
     * 移动端获取所有告警
     *
     * @param dealName
     * @param current
     * @param size
     * @return
     */
    @GetMapping("/app/list")
    public ResponseData getListForApp(@RequestParam(value = "dealName", required = false) String dealName,
                                      @RequestParam(value = "current", defaultValue = "1") Integer current,
                                      @RequestParam(value = "size", defaultValue = "10") Integer size,
                                      @RequestParam(value = "alarmClassifyCode", required = false) String alarmClassifyCode) {
        Page<AlarmEntity> page = alarmService.getListForApp(dealName, current, size, alarmClassifyCode);
        return success(page);
    }

    /**
     * 修改指定处理人
     *
     * @param alarmId
     * @param dealName
     * @return
     */
    @PutMapping("/appoint/handled")
    @OperLog(module = "设备管理", type = OperationType.UPDATE, desc = "指定了告警编号ID为#{alarmCodeId}的告警处理人")
    public ResponseData appointHandledBy(@RequestParam(value = "alarmId") Integer alarmId,
                                         @RequestParam(value = "dealName") String dealName) {
        String username = getUsername();
        AlarmEntity alarmEntity = alarmService.appointHandledBy(alarmId, dealName, username);
        return success(alarmEntity);
    }

    /**
     * 批量指定并通知处理人
     * 1、如果告警Id不传，默认选择所有未恢复的历史告警
     *
     * @param
     * @return
     */
    @PutMapping("/batch/appoint/handled")
    public ResponseData batchAppointHandledBy(@RequestBody(required = false) List<Integer> alarmIds,
                                              @RequestParam(value = "dealName") String dealName) {
        String username = getUsername();
        alarmService.batchAppointHandledBy(alarmIds, dealName, username);
        return success();
    }

    /**
     * 判断历史告警是否存在指定处理人(true -存在  false-不存在)
     *
     * @param
     * @return
     */
    @GetMapping("/judge/appoint/handled")
    public ResponseData judgeAppointHandledBy(@RequestBody(required = false) List<Integer> alarmIds) {
        return success(alarmService.judgeAppointHandledBy(alarmIds));
    }

    /**
     * 开始处理告警
     *
     * @param alarmId
     * @return
     */
    @PutMapping("/deal/alarm")
    @OperLog(module = "设备管理", type = OperationType.UPDATE, desc = "告警编号ID为#{alarmCodeId}的告警开始被处理")
    public ResponseData dealAlarm(@RequestParam(value = "alarmId") Integer alarmId) {
        String username = getUsername();
        AlarmEntity alarmEntity = alarmService.dealAlarm(alarmId, username);
        return success(alarmEntity);
    }

    /**
     * 恢复告警
     *
     * @param alarmId
     * @return
     */
    @PutMapping("/manual/recovery/alarm")
    @OperLog(module = "设备管理", type = OperationType.UPDATE, desc = "告警编号ID为#{alarmCodeId}的告警被手动恢复")
    public ResponseData recoveryAlarmById(@RequestParam(value = "alarmId") Integer alarmId) {
        String username = getUsername();
        AlarmEntity alarmEntity = alarmService.recoveryAlarm(alarmId, username);
        return success(alarmEntity);
    }

    /**
     * 异常管理：小程序（单报工，订单报工，扫码报工）添加告警
     * @param dto 入参
     */
    @PostMapping("/exception-manager/add")
    public ResponseData addByExceptionManager(@RequestBody @Validated AlarmInsertDTO dto) {
        alarmService.addByExceptionManager(dto);
        return success();
    }
    @PostMapping("/exception-manager/update")
    public ResponseData updateByExceptionManager(@RequestBody @Validated AlarmUpdateDTO dto) {
        alarmService.updateByExceptionManager(dto);
        return success();
    }

    /**
     * 处理异常
     */
    @PostMapping("/exception-manager/deal")
    public ResponseData dealByExceptionManager(@RequestBody @Validated AlarmDealDTO dto) {
        alarmService.dealByExceptionManager(dto);
        return success();
    }

    /**
     * 指派异常给他人
     */
    @PostMapping("/exception-manager/assign")
    public ResponseData assignByExceptionManager(@RequestBody @Validated AlarmAssignDTO dto) {
        alarmService.assignByExceptionManager(dto);
        return success();
    }

    @PostMapping("/exception-manager/page")
    public ResponseData pageByExceptionManager(@RequestBody ExceptionManagerListDTO dto) {
        return success(alarmService.pageByExceptionManager(dto));
    }

    @GetMapping("/deal-state/list")
    public ResponseData dealStateList() {
        return success(CommonType.covertToList(AlarmDealStateEnum.class));
    }
}
