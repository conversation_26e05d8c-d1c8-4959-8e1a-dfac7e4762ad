package com.yelink.dfs.controller.metrics;

import com.yelink.dfs.entity.metrics.dto.StaffWorkOrderPageDTO;
import com.yelink.dfs.service.statement.MetricsCenterService;
import com.yelink.dfscommon.pojo.ResponseData;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/metrics/staff")
public class MetricsStaffController {
    @Resource
    private MetricsCenterService metricsCenterService;

    @PostMapping("/staff_work_order/page")
    public ResponseData staffWorkOrderPage(@RequestBody StaffWorkOrderPageDTO dto) {

        return ResponseData.success(metricsCenterService.staffWorkOrderPage(dto));
    }
}
