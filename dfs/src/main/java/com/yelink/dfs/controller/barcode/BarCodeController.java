package com.yelink.dfs.controller.barcode;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yelink.dfs.common.BaseController;
import com.yelink.dfs.constant.OperationType;
import com.yelink.dfs.constant.barcode.BarCodeStateEnum;
import com.yelink.dfs.entity.barcode.BarCodeEntity;
import com.yelink.dfs.entity.barcode.dto.BarCodeQuantityVerifyDTO;
import com.yelink.dfs.entity.barcode.dto.RuleStateDTO;
import com.yelink.dfs.entity.barcode.dto.RuleTypeDTO;
import com.yelink.dfs.entity.code.dto.BarCodeSelectDTO;
import com.yelink.dfs.service.barcode.BarCodeService;
import com.yelink.dfscommon.annotation.OperLog;
import com.yelink.dfscommon.constant.dfs.barcode.BarCodePrintTypeEnum;
import com.yelink.dfscommon.constant.dfs.barcode.BarCodeTypeEnum;
import com.yelink.dfscommon.entity.dfs.barcode.PrintDTO;
import com.yelink.dfscommon.pojo.ResponseData;
import com.yelink.dfscommon.utils.JacksonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 条码管理
 * @Date 2021/05/24
 */
@Slf4j
@RestController
@RequestMapping("/bar/code")
public class BarCodeController extends BaseController {
    @Resource
    private BarCodeService barCodeService;

    /**
     * 通过单号获取条码列表
     *
     * @param current
     * @param size
     * @return
     */
    @GetMapping("/list/{orderNumber}")
    public ResponseData getList(@PathVariable String orderNumber,
                                @RequestParam(value = "materialCode", required = false) String materialCode,
                                @RequestParam(value = "current", required = false) Integer current,
                                @RequestParam(value = "size", required = false) Integer size) {
        //TODO 没有通过单号类型对数据进行筛选，如不同类型存在相同单号，会查询到其它类型数据
        Page<BarCodeEntity> page = barCodeService.getListByOrder(orderNumber, materialCode, current, size);
        return success(page);
    }

    @GetMapping("/list/orderNumber")
    public ResponseData getList2(@RequestParam String orderNumber,
                                 @RequestParam(value = "materialCode", required = false) String materialCode,
                                 @RequestParam(value = "current", required = false) Integer current,
                                 @RequestParam(value = "size", required = false) Integer size) {
        return getList(orderNumber, materialCode, current, size);
    }

    /**
     * 批号列表
     *
     * @return
     */
    @PostMapping("/list")
    public ResponseData getList(@RequestBody BarCodeSelectDTO barCodeSelectDTO) {
        barCodeSelectDTO.setUserName(getUsername());
        Page<BarCodeEntity> page = barCodeService.getList(barCodeSelectDTO);
        return success(page);
    }

    /**
     * 获取所有条码列表
     *
     * @return
     */
    @GetMapping("/all/list")
    public ResponseData getAllList() {
        List<BarCodeEntity> list = barCodeService.list();
        return success(list);
    }

    /**
     * 新增批次
     *
     * @param entity
     * @return
     */
    @PostMapping("/add")
    public ResponseData addBarCode(@RequestBody BarCodeEntity entity) {
        //异步处理数据
        entity.setCreateBy(getUsername());
        entity.setCreateTime(new Date());
        entity.setCheckTimesNeedAddOne(false);
        barCodeService.generateAndAddBarCode(entity);
        return success();
    }

    /**
     * 同步批次
     *
     * @param barCodeEntityList
     * @return
     */
    @PostMapping("/syn")
    public ResponseData synBarCode(@RequestBody List<BarCodeEntity> barCodeEntityList) {
        barCodeService.synBarCode(barCodeEntityList);
        return success();
    }

    /**
     * 修改条码
     *
     * @param entity
     * @return
     */
    @PutMapping("/update")
    public ResponseData updateBarCode(@RequestBody BarCodeEntity entity) {
        entity.setUpdateBy(getUsername());
        entity.setUpdateTime(new Date());
        return success(barCodeService.updateBarCode(entity));
    }

    /**
     * 删除批次
     */
    @DeleteMapping("/remove/{barCodeId}")
    public ResponseData removeBarCode(@PathVariable Integer barCodeId) {
        barCodeService.removeBarCode(barCodeId);
        return success();
    }

    /**
     * 批量删除批次
     */
    @PostMapping("/batch/remove")
    public ResponseData batchRemoveBarCode(@RequestBody List<Integer> barCodeIds) {
        barCodeService.batchRemoveBarCode(barCodeIds);
        return success();
    }

    /**
     * 获取条码详情
     *
     * @param barCodeId
     * @return
     */
    @GetMapping("/detail/{barCodeId}")
    public ResponseData getDetail(@PathVariable Integer barCodeId) {
        BarCodeEntity detail = barCodeService.getDetail(barCodeId);
        return success(detail);
    }

    /**
     * 批次标签打印前校验 标签是否已打印
     *
     * @param
     * @return
     */
    @PostMapping("/judge/print")
    public ResponseData judgeBeforePrint(@RequestBody BarCodeSelectDTO barCodeSelectDTO) {
        barCodeService.judgeBeforePrint(barCodeSelectDTO);
        return success();
    }

    /**
     * 批次标签打印
     *
     * @param barCodeSelectDTO
     * @return
     */
    @OperLog(module = "批次标签", type = OperationType.PRINT, desc = "打印了批次号为#{printCodes}的批次")
    @PostMapping("/print")
    public ResponseData print(@RequestBody BarCodeSelectDTO barCodeSelectDTO) {
        log.info("{}调用了打印接口：{}", getUsername(), JacksonUtil.toJSONString(barCodeSelectDTO));
        PrintDTO print = barCodeService.print(barCodeSelectDTO);
        return success(print);
    }


    /**
     * 获取批次状态
     *
     * @return
     */
    @GetMapping("/state")
    public ResponseData getStateList() {
        List<RuleStateDTO> list = Arrays.stream(BarCodeStateEnum.values())
                .map(barCodeStateEnum -> RuleStateDTO.builder()
                        .code(barCodeStateEnum.getCode())
                        .name(barCodeStateEnum.getName())
                        .build())
                .collect(Collectors.toList());
        return success(list);
    }

    /**
     * 获取批次类型
     *
     * @return
     */
    @GetMapping("/type")
    public ResponseData getTypeList() {
        List<RuleTypeDTO> list = Arrays.stream(BarCodeTypeEnum.values())
                .map(barCodeRuleTypeEnum -> RuleTypeDTO.builder()
                        .type(barCodeRuleTypeEnum.getCode())
                        .name(barCodeRuleTypeEnum.getName())
                        .build()).collect(Collectors.toList());
        return success(list);
    }

    /**
     * 勾选多个条码导出excel
     *
     * @param barCodeIds
     * @return
     */
    @GetMapping("/excel/export")
    public void exportExcel(@RequestParam(value = "barCodeIds") String barCodeIds,
                            HttpServletResponse response) {
        barCodeService.exportExcel(barCodeIds, response);
    }

    /**
     * 获取批次打印的类型(工单批次，采购批次)
     *
     * @return
     */
    @GetMapping("/print/type")
    public ResponseData getPrintTypeList() {
        List<RuleTypeDTO> list = Arrays.stream(BarCodePrintTypeEnum.values())
                .map(barCodeRuleTypeEnum -> RuleTypeDTO.builder()
                        .type(barCodeRuleTypeEnum.getCode())
                        .name(barCodeRuleTypeEnum.getName())
                        .build()).collect(Collectors.toList());
        return success(list);
    }

    /**
     * 获取最近一次批次
     *
     * @param
     * @return
     */
    @GetMapping("/last/one")
    public ResponseData getLastOne() {
        LambdaQueryWrapper<BarCodeEntity> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.orderByDesc(BarCodeEntity::getCreateTime);
        lambdaQueryWrapper.orderByDesc(BarCodeEntity::getBarCodeId);
        lambdaQueryWrapper.last("limit 1");
        return success(barCodeService.list(lambdaQueryWrapper));
    }

    /**
     * 根据类型获取新增工单批次、采购批次、生产流水码、生产成品码的进度
     *
     * @param
     * @return
     */
    @GetMapping("/different/batch/code")
    public ResponseData getWorkOrderBatchProgress(@RequestParam(value = "type") String type,
                                                  @RequestParam(value = "relatedNumber", required = false) String relatedNumber) {
        String progress = barCodeService.getDifferentBatchAndCodeProgress(type, relatedNumber);
        return success(progress);
    }

    /**
     * 新增单据前进行批次数量校验
     *
     * @param quantityVerifyDTOS 数量校验实体
     * @return
     */
    @PostMapping("/quantity/verify")
    public ResponseData quantityVerify(@RequestBody BarCodeQuantityVerifyDTO quantityVerifyDTOS) {
        return ResponseData.success(barCodeService.quantityVerify(quantityVerifyDTOS));
    }

    /**
     * 查询批次关联的单据号
     *
     * @param relatedType 关联单据类型
     * @return
     */
    @GetMapping("/relation/number/list")
    public ResponseData getRelationNumberList(@RequestParam(value = "relatedType") String relatedType,
                                              @RequestParam(value = "relationNumber", required = false) String relationNumber) {
        return success(barCodeService.getRelationNumberList(relatedType, relationNumber));
    }

    /**
     * 批次计划总数
     *
     * @param relatedType 关联单据类型
     * @return
     */
    @GetMapping("/count/sum")
    public ResponseData getCount(@RequestParam(value = "relatedType") String relatedType,
                                 @RequestParam(value = "relationNumber") String relationNumber) {
        return success(barCodeService.getBarCodeCount(relatedType, relationNumber));
    }
}
