package com.yelink.dfs.controller.notice;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yelink.dfs.common.BaseController;
import com.yelink.dfs.entity.notice.LocationMachineWebSocketHistoryEntity;
import com.yelink.dfs.entity.notice.dto.SocketSelectDTO;
import com.yelink.dfs.service.notice.LocationMachineWebSocketHistoryService;
import com.yelink.dfscommon.pojo.ResponseData;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @Date 2021/8/18 16:16
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/web/socket/history")
public class WebSocketHistoryController extends BaseController {

    private LocationMachineWebSocketHistoryService locationMachineWebSocketHistoryService;


    /**
     * socket消息历史列表
     *
     * @param socketSelectDTO
     * @return
     */
    @PostMapping("/list")
    public ResponseData getLocalMachineList(@RequestBody SocketSelectDTO socketSelectDTO) {
        Page<LocationMachineWebSocketHistoryEntity> page = locationMachineWebSocketHistoryService.listHistory(socketSelectDTO);
        return ResponseData.success(page);
    }
    


}
