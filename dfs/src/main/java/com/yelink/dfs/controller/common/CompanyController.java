package com.yelink.dfs.controller.common;

import com.yelink.dfs.common.BaseController;
import com.yelink.dfs.constant.OperationType;
import com.yelink.dfs.entity.model.CompanyEntity;
import com.yelink.dfs.service.impl.common.WorkPropertise;
import com.yelink.dfs.service.model.CompanyService;
import com.yelink.dfs.service.model.WorkCenterService;
import com.yelink.dfscommon.annotation.OperLog;
import com.yelink.dfscommon.pojo.ResponseData;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * @description: 公司信息
 * @author: shuang
 * @time: 2021/12/24
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/companies")
public class CompanyController extends BaseController {

    private CompanyService companyService;
    private WorkPropertise workPropertise;
    private WorkCenterService workCenterService;

    /**
     * 获取当前公司信息
     *
     * @return
     */
    @GetMapping
    public ResponseData detail() {
        return success(companyService.detail());
    }

    /**
     * 更新公司信息
     *
     * @param companyEntity
     * @return
     */
    @PutMapping("update")
    @OperLog(module = "公司信息", type = OperationType.UPDATE, desc = "修改了公司信息")
    public ResponseData update(@RequestBody CompanyEntity companyEntity) {
        companyService.updateEntity(companyEntity);
        return success(companyEntity);
    }

    /**
     * 获取厂区配置
     *
     * @param
     * @return
     */
    @GetMapping("/factory")
    public ResponseData getFactory() {
        return success((Object) workPropertise.getFactory());
    }

    /**
     * 作业工单特性是否开启 true 开启 false 关闭
     *
     * @param
     * @return
     */
    @GetMapping("/is/open/operation/order")
    public ResponseData isOpenOperationOrder(@RequestParam(required = false) Integer workOrderId, @RequestParam(required = false) Integer lineId, @RequestParam(required = false) Integer workCenterId) {
        if (workCenterId != null) {
            return ResponseData.success(workCenterService.isOperationWorkCenter(workCenterId));
        } else if (lineId != null) {
            return ResponseData.success(workCenterService.isOperationByLineId(lineId));
        } else if (workOrderId != null) {
            return ResponseData.success(workCenterService.isOperationWorkOrder(workOrderId));
        } else {
            return ResponseData.success(workPropertise.getIsOpenOperationOrder());
        }
    }
}
