package com.yelink.dfs.controller.test.pushdown;

import com.yelink.dfs.common.BaseController;
import com.yelink.dfscommon.pojo.ResponseData;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * @Description:内置下推校验接口
 * @Author: z<PERSON>zhen<PERSON><PERSON>
 * @Date: 2024/11/11
 */
@Slf4j
@RestController
@RequestMapping("/test/push_down/verify")
public class PushDownVerifyController extends BaseController {


    @PostMapping("/success")
    public ResponseData verifySuccess() {
        return success();
    }

    @PostMapping("/fail")
    public ResponseData verifyFail() {
        return fail("单据校验失败");
    }

}
