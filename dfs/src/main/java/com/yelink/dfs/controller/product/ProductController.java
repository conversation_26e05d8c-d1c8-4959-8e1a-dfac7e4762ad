package com.yelink.dfs.controller.product;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.yelink.dfs.common.BaseController;
import com.yelink.dfs.entity.product.ProductEntity;
import com.yelink.dfs.service.product.ProductService;
import com.yelink.dfscommon.constant.ParamException;
import com.yelink.dfscommon.constant.RespCodeEnum;
import com.yelink.dfscommon.pojo.ResponseData;
import com.yelink.dfscommon.utils.DateUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.Date;

/**
 * @Description: 产品管理接口
 * @Author: yejiarun
 * @Date: 2020/12/07
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/products")
public class ProductController extends BaseController {

    private ProductService productService;

    /**
     * 分页、模糊查询
     *
     * @param name        产品名称
     * @param code        产品编号
     * @param currentPage 当前页
     * @param size        当前页数
     * @return
     */
    @GetMapping("/list")
    public ResponseData list(@RequestParam(value = "name", required = false) String name,
                             @RequestParam(value = "code", required = false) String code,
                             @RequestParam(value = "current", required = false) Integer currentPage,
                             @RequestParam(value = "size", required = false) Integer size) {
        return success(productService.list(name, code, currentPage, size));
    }

    /**
     * 通过Id查询产品信息
     *
     * @param id
     * @return
     */
    @GetMapping("/select/{id}")
    public ResponseData selectById(@PathVariable Integer id) {
        ProductEntity entity = productService.getById(id);
        if (entity == null) {
            return fail(RespCodeEnum.PRODUCTION_FAIL2SEL);
        }
        return success(entity);
    }


    /**
     * 添加产品信息
     *
     * @param
     * @return
     */
    @PostMapping("/insert")
    public ResponseData add(@RequestBody @Valid ProductEntity product, BindingResult bindingResult) {
        if (bindingResult.hasErrors()) {
            throw new ParamException(bindingResult);
        }
        String username = getUsername();
        Date date = DateUtil.formatToDate(new Date(), DateUtil.DATETIME_FORMAT);
        product.setCreateBy(username);
        product.setCreateDate(date);
        productService.saveEntity(product);
        return ResponseData.success();
    }


    /**
     * 修改信息
     *
     * @param product
     * @return
     */
    @PutMapping("/update")
    public ResponseData update(@RequestBody ProductEntity product) {
        product.setUpdateDate(new Date());
        product.setUpdateBy(getUsername());
        productService.updateById(product);
        return ResponseData.success();
    }

    /**
     * 通过Id删除信息
     *
     * @param id
     * @return
     */
    @DeleteMapping("/delete/{id}")
    public ResponseData delete(@PathVariable Integer id) {
        boolean remove = productService.removeById(id);
        if (remove) {
            return success();
        } else {
            return fail(RespCodeEnum.PRODUCTION_FAIL2DEL);
        }
    }

    /**
     * 通过productCode批量删除产品信息
     *
     * @param code
     * @return
     */
    @DeleteMapping("/delete/code/{code}")
    public ResponseData deleteByProductCode(@PathVariable String code) {
        QueryWrapper<ProductEntity> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(ProductEntity::getProductCode, code);
        boolean remove = productService.remove(wrapper);
        if (remove) {
            return success();
        } else {
            return fail(RespCodeEnum.PRODUCTION_FAIL2DEL);
        }
    }

}
