package com.yelink.dfs.controller.order;

import com.yelink.dfs.common.BaseController;
import com.yelink.dfs.entity.order.WorkOrderBasicUnitRelationEntity;
import com.yelink.dfs.service.order.WorkOrderBasicUnitRelationService;
import com.yelink.dfscommon.pojo.ResponseData;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 工单-生产基本单元
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/work_order/basic_unit_relation")
public class WorkOrderBasicUnitRelationController extends BaseController {
    @Resource
    private WorkOrderBasicUnitRelationService workOrderBasicUnitRelationService;


    /**
     * 获取工单中.处于投产状态的生产基本单元
     * @param workOrderNumber 工单号
     */
    @GetMapping("/by_work_order_number")
    public ResponseData getByWorkOrderNumber(@RequestParam String workOrderNumber) {
        return ResponseData.success(
                workOrderBasicUnitRelationService.lambdaQuery()
                        .eq(WorkOrderBasicUnitRelationEntity::getWorkOrderNumber, workOrderNumber)
                        .list()
        );
    }

}
