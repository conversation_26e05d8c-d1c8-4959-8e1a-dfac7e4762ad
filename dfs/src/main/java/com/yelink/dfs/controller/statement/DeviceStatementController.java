package com.yelink.dfs.controller.statement;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yelink.dfs.common.BaseController;
import com.yelink.dfs.entity.order.RecordDeviceDayCountEntity;
import com.yelink.dfs.entity.statement.vo.DeviceDayCountVO;
import com.yelink.dfs.service.order.RecordDeviceDayCountService;
import com.yelink.dfscommon.pojo.ResponseData;
import com.yelink.dfscommon.utils.EasyExcelUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @description: 报表
 * @time 2021/5/21 15:49
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/device/statement")
public class DeviceStatementController extends BaseController {
    private final RecordDeviceDayCountService recordDeviceDayCountService;

    /**
     * 日报, 支持按日期、设备进行筛选
     * 字段：设备名称、设备编码、日期、产量、单位、车间名称、产线名称
     *
     * @return
     */
    @GetMapping("/list/by/day")
    public ResponseData getRecordListByDay(@RequestParam(value = "deviceCode", required = false) String deviceCode,
                                           @RequestParam(value = "deviceName", required = false) String deviceName,
                                           @RequestParam(value = "startDate", required = false) String startDate,
                                           @RequestParam(value = "endDate", required = false) String endDate,
                                           @RequestParam(value = "current") Integer current,
                                           @RequestParam(value = "size") Integer size) {
        Page<RecordDeviceDayCountEntity> page = recordDeviceDayCountService.getRecordListByDay(deviceCode, deviceName, startDate, endDate, current, size);
        return success(page);
    }
    @GetMapping("/list/by/day/export")
    public void getRecordListByDayExport(HttpServletResponse response,
                                         @RequestParam(required = false) String deviceCode,
                                         @RequestParam(required = false) String deviceName,
                                         @RequestParam(required = false) String startDate,
                                         @RequestParam(required = false) String endDate) throws IOException {
        int current = 1, size = Integer.MAX_VALUE;
        Page<RecordDeviceDayCountEntity> page = recordDeviceDayCountService.getRecordListByDay(deviceCode, deviceName, startDate, endDate, current, size);
        List<DeviceDayCountVO.DeviceDayCountDayVO> list = page.getRecords().stream().map(DeviceDayCountVO::convertToDeviceDayCountDayVO).collect(Collectors.toList());
        EasyExcelUtil.export(response, "设备产量日报", "数据列表", list, DeviceDayCountVO.DeviceDayCountDayVO.class);
    }

    /**
     * 月报,支持按日期、设备进行筛选
     * 字段：设备名称、设备编码、日期、产量、单位、车间名称、产线名称
     *
     * @return
     */
    @GetMapping("/list/by/month")
    public ResponseData getRecordListByMonth(@RequestParam(value = "deviceCode", required = false) String deviceCode,
                                             @RequestParam(value = "deviceName", required = false) String deviceName,
                                             @RequestParam(value = "startDate", required = false) String startDate,
                                             @RequestParam(value = "endDate", required = false) String endDate,
                                             @RequestParam(value = "current") Integer current,
                                             @RequestParam(value = "size") Integer size) {
        Page<RecordDeviceDayCountEntity> page = recordDeviceDayCountService.getRecordListByMonth(deviceCode, deviceName, startDate, endDate, current, size);
        return success(page);
    }
    @GetMapping("/list/by/month/export")
    public void getRecordListByMonthExport(HttpServletResponse response,
                                           @RequestParam(required = false) String deviceCode,
                                           @RequestParam(required = false) String deviceName,
                                           @RequestParam(required = false) String startDate,
                                           @RequestParam(required = false) String endDate) throws IOException {
        int current = 1, size = Integer.MAX_VALUE;
        Page<RecordDeviceDayCountEntity> page = recordDeviceDayCountService.getRecordListByMonth(deviceCode, deviceName, startDate, endDate, current, size);
        List<DeviceDayCountVO.DeviceDayCountMonthVO> list = page.getRecords().stream().map(DeviceDayCountVO::convertToDeviceDayCountMonthVO).collect(Collectors.toList());
        EasyExcelUtil.export(response, "设备产量月报", "数据列表", list, DeviceDayCountVO.DeviceDayCountMonthVO.class);
    }


}

