package com.yelink.dfs.controller.pushdown;

import com.yelink.dfs.constant.order.PriorityTypeEnum;
import com.yelink.dfs.entity.order.WorkOrderEntity;
import com.yelink.dfs.entity.product.MaterialEntity;
import com.yelink.dfs.service.common.config.OrderPushDownConfigService;
import com.yelink.dfs.service.common.config.OrderPushDownConfigValueDictService;
import com.yelink.dfs.service.common.config.OrderPushDownItemService;
import com.yelink.dfs.service.common.config.OrderPushDownRecordService;
import com.yelink.dfs.service.model.WorkCenterService;
import com.yelink.dfs.service.order.WorkOrderService;
import com.yelink.dfs.service.product.MaterialService;
import com.yelink.dfscommon.constant.ModelEnum;
import com.yelink.dfscommon.constant.dfs.order.WorkOrderStateEnum;
import com.yelink.dfscommon.constant.dfs.pushdown.PushDownItemTypeEnum;
import com.yelink.dfscommon.constant.dfs.pushdown.PushDownOrderStateEnum;
import com.yelink.dfscommon.constant.dfs.pushdown.PushDownWriteBackTypeEnum;
import com.yelink.dfscommon.dto.common.config.PushDownFullPathCodeDTO;
import com.yelink.dfscommon.dto.pushdown.item.PushDownItemEditStateDTO;
import com.yelink.dfscommon.dto.pushdown.item.PushDownItemSelectDTO;
import com.yelink.dfscommon.dto.pushdown.item.PushDownRecordSelectDTO;
import com.yelink.dfscommon.dto.pushdown.item.PushDownRuleItemAddDTO;
import com.yelink.dfscommon.dto.pushdown.item.PushDownRuleItemEditDTO;
import com.yelink.dfscommon.dto.pushdown.item.PushDownTreeInsertDTO;
import com.yelink.dfscommon.dto.pushdown.item.PushDownTreeUpdateDTO;
import com.yelink.dfscommon.dto.pushdown.item.PushDownVerifyItemAddDTO;
import com.yelink.dfscommon.dto.pushdown.item.PushDownVerifyItemEditDTO;
import com.yelink.dfscommon.dto.pushdown.item.PushDownWriteBackItemAddDTO;
import com.yelink.dfscommon.dto.pushdown.item.PushDownWriteBackItemEditDTO;
import com.yelink.dfscommon.entity.CommonType;
import com.yelink.dfscommon.entity.dfs.OrderPushDownConfigEntity;
import com.yelink.dfscommon.entity.dfs.OrderPushDownItemEntity;
import com.yelink.dfscommon.entity.dfs.WorkCenterEntity;
import com.yelink.dfscommon.pojo.ResponseData;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.Date;
import java.util.List;

import static com.yelink.dfscommon.pojo.ResponseData.success;

/**
 * 单据下推配置控制层
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/order_push_downs")
public class OrderPushDownController {

    @Resource
    private OrderPushDownItemService orderPushDownItemService;
    @Resource
    private OrderPushDownConfigService orderPushDownConfigService;
    @Resource
    private OrderPushDownConfigValueDictService valueDictService;
    @Resource
    private OrderPushDownRecordService orderPushDownRecordService;
    @Resource
    private MaterialService materialService;
    @Resource
    private WorkCenterService workCenterService;
    @Resource
    @Lazy
    private WorkOrderService workOrderService;

    /**
     * 查询单据下推配置树
     *
     * @param dto 某节点的全路径编码
     * @return List<BusinessConfigEntity> 配置树结构
     */
    @PostMapping("/tree")
    public ResponseData getTreeByFullPathCode(@RequestBody PushDownFullPathCodeDTO dto) {
        List<OrderPushDownConfigEntity> options = orderPushDownConfigService.getTreeByFullPathCode(dto);
        return success(options);
    }

    /**
     * 添加单据下推配置树
     *
     * @param
     * @return
     */
    @PostMapping("/tree/add")
    public ResponseData addTreeNode(@RequestBody PushDownTreeInsertDTO insertDTO) {
        orderPushDownConfigService.addTreeNode(insertDTO);
        return success();
    }

    /**
     * 编辑单据下推配置树
     *
     * @param
     * @return
     */
    @PostMapping("/tree/update")
    public ResponseData updateTreeNode(@RequestBody PushDownTreeUpdateDTO updateDTO) {
        orderPushDownConfigService.updateTreeNode(updateDTO);
        return success();
    }

    /**
     * 删除单据下推配置树
     *
     * @param
     * @return
     */
    @DeleteMapping("/tree/delete")
    public ResponseData deleteTreeNode(@RequestParam(value = "id") Integer id) {
        orderPushDownConfigService.deleteTreeNode(id);
        return success();
    }

    /**
     * 查询规则列表
     *
     * @param
     * @return
     */
    @PostMapping("/item/list")
    public ResponseData itemList(@RequestBody PushDownItemSelectDTO selectDTO) {
        return success(orderPushDownItemService.getPage(selectDTO));
    }

    /**
     * 查询规则详情
     *
     * @param
     * @return
     */
    @GetMapping("/item/detail")
    public ResponseData itemDetail(@RequestParam Integer id) {
        return success(orderPushDownItemService.getDetail(id));
    }

    /**
     * 添加下推规则
     *
     * @param
     * @return
     */
    @PostMapping("/item/add/rule")
    public ResponseData itemAdd(@RequestBody PushDownRuleItemAddDTO addDTO) {
        addDTO.setType(PushDownItemTypeEnum.RULE);
        orderPushDownItemService.add(addDTO);
        return success();
    }

    /**
     * 添加反写规则
     *
     * @param
     * @return
     */
    @PostMapping("/item/add/write_back")
    public ResponseData itemAddWriteBack(@RequestBody @Valid PushDownWriteBackItemAddDTO addDTO) {
        addDTO.setType(PushDownItemTypeEnum.WRITE_BACK);
        orderPushDownItemService.add(addDTO);
        return success();
    }

    /**
     * 添加校验规则
     *
     * @param
     * @return
     */
    @PostMapping("/item/add/verify")
    public ResponseData itemAddVerify(@RequestBody @Valid PushDownVerifyItemAddDTO addDTO) {
        addDTO.setType(PushDownItemTypeEnum.VERIFY);
        orderPushDownItemService.add(addDTO);
        return success();
    }

    /**
     * 编辑下推规则
     *
     * @param editDTO 下推规则对象
     * @return
     */
    @PostMapping("/item/edit/rule")
    public ResponseData itemEdit(@RequestBody PushDownRuleItemEditDTO editDTO) {
        editDTO.setType(PushDownItemTypeEnum.RULE);
        orderPushDownItemService.edit(editDTO);
        return success();
    }

    /**
     * 编辑反写规则
     *
     * @param editDTO 反写规则对象
     * @return
     */
    @PostMapping("/item/edit/write_back")
    public ResponseData itemEditWriteBack(@RequestBody @Valid PushDownWriteBackItemEditDTO editDTO) {
        editDTO.setType(PushDownItemTypeEnum.WRITE_BACK);
        orderPushDownItemService.edit(editDTO);
        return success();
    }

    /**
     * 编辑校验规则
     *
     * @param editDTO 校验规则对象
     * @return
     */
    @PostMapping("/item/edit/verify")
    public ResponseData itemEditVerify(@RequestBody @Valid PushDownVerifyItemEditDTO editDTO) {
        editDTO.setType(PushDownItemTypeEnum.VERIFY);
        orderPushDownItemService.edit(editDTO);
        return success();
    }

    /**
     * 编辑校验规则状态
     *
     * @param editDTO 编辑对象
     * @return
     */
    @PostMapping("/item/update/state")
    public ResponseData updateItemState(@RequestBody @Valid PushDownItemEditStateDTO editDTO) {
        orderPushDownItemService.lambdaUpdate().eq(OrderPushDownItemEntity::getId, editDTO.getId())
                .set(OrderPushDownItemEntity::getEnable, editDTO.getEnable())
                .update();
        return success();
    }

    /**
     * 删除规则
     *
     * @param id 规则id
     * @return
     */
    @DeleteMapping("/item/delete")
    public ResponseData itemDelete(@RequestParam Integer id) {
        orderPushDownItemService.delete(id);
        return success();
    }

    /**
     * 规则类型列表
     *
     * @param
     * @return
     */
    @GetMapping("/item/types")
    public ResponseData itemTypes() {
        return success(CommonType.covertToList(PushDownItemTypeEnum.class));
    }

    /**
     * 查询反写规则类型
     *
     * @param
     * @return
     */
    @GetMapping("/item/write_back_types")
    public ResponseData itemWriteBackTypes() {
        return success(CommonType.covertToList(PushDownWriteBackTypeEnum.class));
    }

    /**
     * 查询下推规则类型
     *
     * @param typeCode 下推单据的类型编码
     * @return
     */
    @GetMapping("/item/convert_rule/types")
    public ResponseData getConvertRuleType(@RequestParam(value = "typeCode") String typeCode) {
        return success(valueDictService.getPushDownRuleType(typeCode));
    }

    /**
     * 查询下推规则里具体类型下的字典信息
     *
     * @param typeCode 类型编码
     * @param fullPathCode 当前节点的全路径编码
     * @return
     */
    @GetMapping("/item/convert_rule/dict")
    public ResponseData getConvertRuleDictByTypeCode(@RequestParam(value = "typeCode") String typeCode,
                                                     @RequestParam(value = "fullPathCode", required = false) String fullPathCode) {
        return success(valueDictService.getPushDownRuleDictByTypeCode(typeCode, fullPathCode));
    }

    /**
     * 下推记录列表
     *
     * @param selectDTO 查询条件
     * @return
     */
    @PostMapping("/record/list")
    public ResponseData getRecordList(@RequestBody PushDownRecordSelectDTO selectDTO) {
        return success(orderPushDownRecordService.getRecordList(selectDTO));
    }

    /**
     * 获取下推源单单据状态枚举
     */
    @GetMapping("/original_order_state_types")
    public ResponseData getOriginalOrderStateEnum() {
        return success(CommonType.covertToList(PushDownOrderStateEnum.class));
    }


    /**
     * 下推url生成工单数据demo
     */
    @PostMapping("/rule/test_url")
    public ResponseData ruleTest(@RequestBody @Valid WorkOrderEntity entity) {
        MaterialEntity materialEntity = materialService.lambdaQuery()
                .orderByDesc(MaterialEntity::getId).last("limit 1").one();
        WorkCenterEntity workCenterEntity = workCenterService.lambdaQuery()
                .orderByDesc(WorkCenterEntity::getId).last("limit 1").one();
        WorkOrderEntity workOrderEntity = WorkOrderEntity.builder()
                .workOrderNumber("test")
                .workOrderName("test")
                .startDate(new Date())
                .endDate(new Date())
                .materialCode(materialEntity.getCode())
                .state(WorkOrderStateEnum.CREATED.getCode())
                .priority(PriorityTypeEnum.NORMAL.getName())
                .planQuantity(1.0)
                .type(ModelEnum.WORK_ORDER.getType())
                .createBy("admin")
                .createDate(new Date())
                .workCenterId(workCenterEntity.getId())
                .workCenterName(workCenterEntity.getName())
                .build();
        workOrderService.save(workOrderEntity);
        return ResponseData.success();
    }

}
