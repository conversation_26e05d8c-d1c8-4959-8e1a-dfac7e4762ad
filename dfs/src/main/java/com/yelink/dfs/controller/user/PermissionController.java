package com.yelink.dfs.controller.user;

import com.asyncexcel.core.exporter.DataExportParam;
import com.asyncexcel.core.model.ExcelTask;
import com.asyncexcel.springboot.ExcelService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yelink.dfs.common.BaseController;
import com.yelink.dfs.entity.manufacture.ProductionLineEntity;
import com.yelink.dfs.entity.user.SysFieldPermissionEntity;
import com.yelink.dfs.entity.user.SysPermissionEntity;
import com.yelink.dfs.entity.user.SysRoleProductionBasicUnitEntity;
import com.yelink.dfs.entity.user.dto.AppBindPermissionDTO;
import com.yelink.dfs.entity.user.dto.BindPermissionDTO;
import com.yelink.dfs.entity.user.dto.FieldBindPermissionDTO;
import com.yelink.dfs.entity.user.dto.PermissionExportSelectDTO;
import com.yelink.dfs.entity.user.dto.SysPermissionDTO;
import com.yelink.dfs.service.common.ImportProgressService;
import com.yelink.dfs.service.user.SysFieldPermissionService;
import com.yelink.dfs.service.user.SysPermissionService;
import com.yelink.dfs.service.user.SysRoleProductionBasicUnitService;
import com.yelink.dfscommon.common.excel.BusinessCodeEnum;
import com.yelink.dfscommon.constant.Constant;
import com.yelink.dfscommon.constant.RedisKeyPrefix;
import com.yelink.dfscommon.pojo.ResponseData;
import com.yelink.dfscommon.utils.ExcelTemplateImportUtil;
import com.yelink.dfscommon.utils.ExcelUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description
 * @Date 2021/5/10
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/permissions")
public class PermissionController extends BaseController {

    private SysPermissionService sysPermissionService;
    private SysRoleProductionBasicUnitService roleProductionBasicUnitService;
    private SysFieldPermissionService sysFieldPermissionService;
    private ExcelService excelService;
    private ImportProgressService importProgressService;

    /**
     * 角色绑定权限，角色绑定产线
     *
     * @param roleId            角色ID
     * @param bindPermissionDTO 逗号分隔的权限ID
     */
    @PostMapping("/bind/{roleId}")
    public ResponseData bind(@PathVariable("roleId") Integer roleId, @RequestBody BindPermissionDTO bindPermissionDTO) {
        String username = getUsername();
        sysPermissionService.bind(roleId, bindPermissionDTO, username);
        return success();
    }

    /**
     * 获取该角色绑定和未绑定的权限列表 （Web 端角色权限维护）
     *
     * @param roleId 角色ID
     */
    @GetMapping("/list/all/{roleId}")
    public ResponseData getAllPermissionListByRoleId(@PathVariable(value = "roleId") Integer roleId) {
        List<SysPermissionDTO> list = sysPermissionService.getAllPermissionListByRoleId(roleId, getUsername());
        return success(list);
    }

    /**
     * 获取角色绑定的产线列表
     *
     * @param roleId 角色ID
     */
    @GetMapping("/line/list/{roleId}")
    public ResponseData getLineByRoleId(@PathVariable(value = "roleId") Integer roleId) {
        List<ProductionLineEntity> list = sysPermissionService.getLineByRoleId(roleId);
        return success(list);
    }

    /**
     * 获取登录用户绑定的权限列表
     */
    @GetMapping("/list")
    public ResponseData getPermissionListByUsername() {
        String username = getUsername();
        List<SysPermissionEntity> list = sysPermissionService.getPermissionListByUsername(username);
        return success(list);
    }

    /**
     * 获取Pad 角色权限列表（移动端角色权限维护）
     */
    @GetMapping("/list/all/pad/permission/{roleId}")
    public ResponseData getAllPermissionListByPadId(@PathVariable(value = "roleId") Integer roleId) {
        List<SysPermissionDTO> list = sysPermissionService.getAllPadPermissionListByRoleId(roleId, getUsername());
        return success(list);
    }

    /**
     * 获取Pad 用户绑定的权限列表
     */
    @GetMapping("/pad/permission/list")
    public ResponseData getPadPermissionListByUsername() {
        String username = getUsername();
        List<SysPermissionEntity> list = sysPermissionService.getPadPermissionListByUsername(username);
        return success(list);
    }

    /**
     * 获取小程序列表
     */
    @GetMapping("/app/list")
    public ResponseData getAppList() {
        List<SysPermissionEntity> list = sysPermissionService.getAppList();
        return success(list);
    }


    /**
     * 绑定Pad 角色权限
     */
    @PostMapping("/bind/pad/permission")
    public ResponseData bindPad(@RequestBody AppBindPermissionDTO appBindPermissionDTO) {
        String username = getUsername();
        sysPermissionService.bindPad(appBindPermissionDTO.getRoleId(), appBindPermissionDTO.getPermissionIds(), username);
        return success();
    }

    /**
     * 获取角色绑定的隔离列表
     *
     * @param roleId 角色ID
     */
    @GetMapping("/isolation/list/{roleId}")
    public ResponseData getIsolationByRoleId(@PathVariable(value = "roleId") Integer roleId) {
        List<SysRoleProductionBasicUnitEntity> list = roleProductionBasicUnitService.lambdaQuery().eq(SysRoleProductionBasicUnitEntity::getRoleId, roleId).list();
        return success(list);
    }

    /**
     * 获取该角色绑定的字段权限列表
     *
     * @param roleId 角色ID
     */
    @GetMapping("/field/list")
    public ResponseData getFieldPermissionListByRole(@RequestParam(value = "roleId") Integer roleId) {
        List<SysFieldPermissionEntity> list = sysFieldPermissionService.getFieldPermissionListByRole(roleId);
        return success(list);
    }

    /**
     * 更新该角色绑定的字段权限
     *
     */
    @PostMapping("/field/update")
    public ResponseData updateFieldPermissionByRole(@RequestBody FieldBindPermissionDTO dto) {
        sysFieldPermissionService.updateFieldPermissionByRole(dto);
        return success();
    }

    /**
     * 导出
     */
    @PostMapping("/excel/exports")
    public ResponseData export(@RequestParam(required = false) Integer templateId, @RequestBody PermissionExportSelectDTO dto) {
        DataExportParam<PermissionExportSelectDTO> dataExportParam = new DataExportParam<>();
        dataExportParam.setExportFileName(BusinessCodeEnum.ROLE_PERMISSIONS.getCodeName());
        dataExportParam.setLimit(100000);
        dataExportParam.setBusinessCode(BusinessCodeEnum.ROLE_PERMISSIONS.name());
        dataExportParam.setCreateUserCode(getUsername());
        Map<String, Object> parameters = new HashMap<>();
        parameters.put(PermissionExportSelectDTO.class.getName(), dto);
        dataExportParam.setParameters(parameters);

        Long result = excelService.doExport(dataExportParam, RolePermissionExportHandler.class,
                RoleWorkCenterExportHandler.class);
        return success(result);
    }

    /**
     * 导出进度
     *
     * @return
     */
    @GetMapping("/export/task/{taskId}")
    public ResponseData detailListTaskById(@PathVariable Long taskId) {
        ExcelTask excelTask = new ExcelTask();
        excelTask.setId(taskId);
        excelTask.setBusinessCode(BusinessCodeEnum.ROLE_PERMISSIONS.name());
        IPage<ExcelTask> excelTaskIPage = excelService.listPage(excelTask, 1, 1);
        List<ExcelTask> records = excelTaskIPage.getRecords();
        if (CollectionUtils.isEmpty(records)) {
            log.info("任务id错误");
            return fail();
        }
        return success(records.get(0));
    }

    /**
     * 下载默认导入模板
     */
    @GetMapping("/down/default/template")
    public void downDefaultTemplate(HttpServletResponse response) throws Exception {
        byte[] bytes = ExcelUtil.exportDefaultExcel("classpath:template/rolePermissionTemplate.xlsx");
        ExcelTemplateImportUtil.responseToClient(response, bytes, "角色权限默认导入模板" + Constant.XLSX);
    }

    /**
     * 导入
     */
    @PostMapping("/import")
    public ResponseData importExcel(MultipartFile file) throws IOException {
        //1、判断导入文件的合法性
        ExcelUtil.checkImportExcelFile(file);
        sysPermissionService.importExcel(file.getOriginalFilename(), file.getInputStream(), getUsername());
        return success();
    }

    /**
     * 获取导入进度
     */
    @GetMapping("/import/progress")
    public ResponseData getCraftProcedureImportProgress() {
        return success(importProgressService.importProgress(RedisKeyPrefix.ROLE_PERMISSION_IMPORT_PROGRESS));
    }

}
