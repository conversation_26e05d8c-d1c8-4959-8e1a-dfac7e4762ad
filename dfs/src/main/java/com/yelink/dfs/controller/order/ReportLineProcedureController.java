package com.yelink.dfs.controller.order;

import com.yelink.dfs.entity.target.record.dto.ReportLineProcedureSelectDTO;
import com.yelink.dfs.entity.target.record.dto.ReportLineProcedureUpdateDTO;
import com.yelink.dfs.service.target.record.ReportLineProcedureService;
import com.yelink.dfscommon.pojo.ResponseData;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/reportLineProcedure")
public class ReportLineProcedureController {

    @Resource
    private ReportLineProcedureService reportLineProcedureService;

    @GetMapping("/list")
    public ResponseData list(ReportLineProcedureSelectDTO dto) {
        return ResponseData.success(reportLineProcedureService.getList(dto));
    }

    @PostMapping("/page")
    public ResponseData page(@RequestBody ReportLineProcedureSelectDTO dto) {
        return ResponseData.success(reportLineProcedureService.getPage(dto));
    }

    @PostMapping("/edit")
    public ResponseData edit(@RequestBody @Valid ReportLineProcedureUpdateDTO dto) {
        reportLineProcedureService.edit(dto);
        return ResponseData.success();
    }

    @GetMapping("/getDefaultCraftProcedureId")
    public ResponseData getDefaultCraftProcedureId(@RequestParam Integer lineId, @RequestParam String workOrderNumber) {
        return ResponseData.success(reportLineProcedureService.getDefaultCraftProcedureId(lineId, workOrderNumber));
    }

}
