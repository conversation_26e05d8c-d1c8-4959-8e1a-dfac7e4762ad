package com.yelink.dfs.controller.task;

import com.yelink.dfs.common.BaseController;
import com.yelink.dfs.service.task.TaskLogService;
import com.yelink.dfscommon.pojo.ResponseData;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;


/**
 * 任务日志
 * <AUTHOR>
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/task/log")
public class TaskLogController extends BaseController {

    private TaskLogService taskLogService;

    /**
     * 查任务应用的日志列表
     */
    @GetMapping("/listByTaskId")
    public ResponseData listByTaskId(@RequestParam Integer taskId) {
        return ResponseData.success(taskLogService.listByTaskId(taskId));
    }

}
