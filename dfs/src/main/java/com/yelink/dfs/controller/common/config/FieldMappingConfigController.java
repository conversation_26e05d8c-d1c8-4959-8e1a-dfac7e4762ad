package com.yelink.dfs.controller.common.config;

import com.yelink.dfs.common.BaseController;
import com.yelink.dfs.service.common.config.FieldMappingService;
import com.yelink.dfscommon.dto.common.config.FieldMappingSelectDTO;
import com.yelink.dfscommon.entity.dfs.FieldMappingEntity;
import com.yelink.dfscommon.pojo.ResponseData;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/11/1 15:01
 */
@RestController
@AllArgsConstructor
@RequestMapping("/field/push/down")
public class FieldMappingConfigController extends BaseController {

    private FieldMappingService fieldMappingService;

    /**
     * 获取字段下推映射关系列表
     *
     * @param selectDTO
     */
    @PostMapping("/list")
    public ResponseData getFieldMapping(@RequestBody FieldMappingSelectDTO selectDTO) {
        List<FieldMappingEntity> list = fieldMappingService.getFieldMappingList(selectDTO);
        return success(list);
    }


}
