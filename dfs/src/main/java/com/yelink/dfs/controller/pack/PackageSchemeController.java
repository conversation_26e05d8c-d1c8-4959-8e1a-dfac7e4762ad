package com.yelink.dfs.controller.pack;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yelink.dfs.common.BaseController;
import com.yelink.dfs.constant.OperationType;
import com.yelink.dfs.constant.pack.PackageSchemeStateEnum;
import com.yelink.dfs.entity.common.CommonState;
import com.yelink.dfs.entity.pack.PackageLevelEntity;
import com.yelink.dfs.entity.pack.PackageSchemeEntity;
import com.yelink.dfs.entity.pack.dto.PackageSchemeDTO;
import com.yelink.dfs.entity.pack.dto.PackageSchemeRelateDataDTO;
import com.yelink.dfs.service.pack.PackageLevelService;
import com.yelink.dfs.service.pack.PackageSchemeService;
import com.yelink.dfscommon.annotation.OperLog;
import com.yelink.dfscommon.pojo.ResponseData;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;

/**
 * 包装方案
 *
 * <AUTHOR>
 * @Date 2021-11-16 17:58
 */
@Controller
@RestController
@RequestMapping("/packages/scheme")
@AllArgsConstructor
public class PackageSchemeController extends BaseController {
    private PackageSchemeService packageSchemeService;
    private final PackageLevelService packageLevelService;

    /**
     * 查询列表
     *
     * @param dto
     * @return
     */
    @PostMapping("/list")
    public ResponseData packageList(@RequestBody PackageSchemeDTO dto) {
        Page<PackageSchemeEntity> list = packageSchemeService.getList(dto);
        return ResponseData.success(list);

    }

    /**
     * 新增包装方案
     *
     * @param entity
     * @return
     */
    @PostMapping("/add")
    @OperLog(module = "包装方案", type = OperationType.ADD, desc = "新增了编码为#{schemeCode}的包装方案")
    public ResponseData addEntity(@RequestBody PackageSchemeEntity entity) {
        String username = getUsername();
        entity.setCreateBy(username);
        entity.setUpdateBy(username);
        return ResponseData.success(packageSchemeService.addEntity(entity));
    }

    /**
     * 更新
     *
     * @param entity
     * @return
     */
    @PutMapping("/update")
    @OperLog(module = "包装方案", type = OperationType.UPDATE, desc = "更新了编码为#{schemeCode}的包装方案")
    public ResponseData updateEntity(@RequestBody PackageSchemeEntity entity) {
        String username = getUsername();
        entity.setUpdateBy(username);
        packageSchemeService.updateEntity(entity);
        return ResponseData.success(entity);
    }

    /**
     * 详情
     *
     * @param id
     * @return
     */
    @GetMapping("/detail/{id}")
    public ResponseData getDetail(@PathVariable Integer id) {
        PackageSchemeEntity entity = packageSchemeService.getDetailById(id);
        return ResponseData.success(entity);
    }

    /**
     * 删除
     *
     * @param id
     * @return
     */
    @DeleteMapping("/delete/{id}")
    @OperLog(module = "包装方案", type = OperationType.DELETE, desc = "删除了编码为#{schemeCode}的包装方案")
    public ResponseData deleteById(@PathVariable Integer id) {
        PackageSchemeEntity deleteEntity = packageSchemeService.getById(id);
        packageSchemeService.deleteById(id);
        return ResponseData.success(deleteEntity);
    }

    @GetMapping("/state")
    public ResponseData getState() {
        ArrayList<CommonState> list = new ArrayList<>();
        PackageSchemeStateEnum[] values = PackageSchemeStateEnum.values();
        for (PackageSchemeStateEnum value : values) {
            CommonState type = new CommonState();
            type.setCode(value.getCode());
            type.setName(value.getName());
            list.add(type);
        }
        return success(list);
    }

    /**
     * 数据影响分析
     *
     * @param code 包装方案编码
     * @return
     */
    @GetMapping("/impact/analysis")
    public ResponseData dataImpactAnalysis(@RequestParam(value = "code") String code) {
        List<PackageSchemeRelateDataDTO> relateDataDTOS = packageSchemeService.dataImpactAnalysis(code);
        return success(relateDataDTOS);
    }

    /**
     * 通过包装方案code查询包装层级
     */
    @GetMapping("/list/package/level")
    public ResponseData listPackageLevel(@RequestParam String packageSchemeCode) {
        List<PackageLevelEntity> list = packageLevelService.getBySchemeCode(packageSchemeCode);
        return ResponseData.success(list);
    }

}
