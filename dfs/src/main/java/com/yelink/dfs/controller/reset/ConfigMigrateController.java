package com.yelink.dfs.controller.reset;

import com.yelink.dfs.common.BaseController;
import com.yelink.dfs.constant.reset.ConfigMigrateEnum;
import com.yelink.dfs.service.reset.ConfigMigrateService;
import com.yelink.dfscommon.entity.CommonType;
import com.yelink.dfscommon.pojo.ResponseData;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * @description: 配置迁移接口
 * @author: shuang
 * @time: 2023/3/3
 */
@RestController
@RequestMapping("/config/migrate")
@RequiredArgsConstructor
@Slf4j
public class ConfigMigrateController extends BaseController {

    private final ConfigMigrateService configMigrateService;

    /**
     * 导出配置
     * @param codes
     * @param response
     * @throws IOException
     */
    @GetMapping("/export")
    @Transactional(rollbackFor = Exception.class)
    public void export(@RequestParam String codes, HttpServletResponse response) throws IOException {
        configMigrateService.export(codes, response, getNickname());
    }

    /**
     * 导入配置
     * @param file
     * @return
     */
    @PostMapping("/import")
    @ResponseBody
    public ResponseData sycImport(MultipartFile file) throws IOException {
        configMigrateService.sycImport(file.getOriginalFilename(), file.getBytes());
        return ResponseData.success();
    }

    /**
     * 查询导入进度
     */
    @GetMapping("/import/progress")
    public ResponseData getImportProgress() {
        return success(configMigrateService.importProgress());
    }

    /**
     * 查询迁移配置列表
     * @return
     */
    @GetMapping("/type")
    public ResponseData getTypeList() {
        return success(CommonType.covertToList(ConfigMigrateEnum.class));
    }

}
