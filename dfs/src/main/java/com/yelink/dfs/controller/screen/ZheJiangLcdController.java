package com.yelink.dfs.controller.screen;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yelink.dfs.common.BaseController;
import com.yelink.dfs.constant.AppendixTypeEnum;
import com.yelink.dfs.constant.Constant;
import com.yelink.dfs.constant.DictTypeEnum;
import com.yelink.dfs.constant.model.FacilitiesTypeEnum;
import com.yelink.dfs.constant.model.ModelIdEnum;
import com.yelink.dfs.constant.order.AssignmentStateEnum;
import com.yelink.dfs.entity.device.DeviceEntity;
import com.yelink.dfs.entity.maintain.MaintainSchemeFacilitiesEntity;
import com.yelink.dfs.entity.manufacture.ProductionLineEntity;
import com.yelink.dfs.entity.model.FacilitiesEntity;
import com.yelink.dfs.entity.model.ModelEntity;
import com.yelink.dfs.entity.order.RecordWorkOrderCountEntity;
import com.yelink.dfs.entity.order.RecordWorkOrderUnqualifiedEntity;
import com.yelink.dfs.entity.order.WorkOrderBasicUnitRelationEntity;
import com.yelink.dfs.entity.order.WorkOrderEntity;
import com.yelink.dfs.entity.order.dto.RecordWorkOrderCountSelectDTO;
import com.yelink.dfs.entity.product.CraftProcedureEntity;
import com.yelink.dfs.entity.product.MaterialEntity;
import com.yelink.dfs.entity.product.ProcedureFileEntity;
import com.yelink.dfs.entity.product.dto.CraftFileNewDTO;
import com.yelink.dfs.entity.screen.dto.FacilitiesDTO;
import com.yelink.dfs.entity.screen.dto.LineSituationDTO;
import com.yelink.dfs.entity.screen.dto.PassStandardDTO;
import com.yelink.dfs.entity.screen.dto.ScreenVisitDTO;
import com.yelink.dfs.mapper.model.FacUserMapper;
import com.yelink.dfs.mapper.model.ModelMapper;
import com.yelink.dfs.service.common.AppendixService;
import com.yelink.dfs.service.common.DictService;
import com.yelink.dfs.service.device.DeviceService;
import com.yelink.dfs.service.maintain.MaintainSchemeFacilitiesService;
import com.yelink.dfs.service.manufacture.ProductionLineService;
import com.yelink.dfs.service.model.FacilitiesService;
import com.yelink.dfs.service.order.OrderWorkOrderService;
import com.yelink.dfs.service.order.RecordWorkOrderCountService;
import com.yelink.dfs.service.order.WorkOrderBasicUnitRelationService;
import com.yelink.dfs.service.order.WorkOrderService;
import com.yelink.dfs.service.order.WorkOrderSubcontractService;
import com.yelink.dfs.service.product.CraftProcedureService;
import com.yelink.dfs.service.product.MaterialService;
import com.yelink.dfs.service.product.ProcedureFileService;
import com.yelink.dfs.service.product.SkuService;
import com.yelink.dfscommon.constant.Constants;
import com.yelink.dfscommon.constant.RespCodeEnum;
import com.yelink.dfscommon.constant.ResponseException;
import com.yelink.dfscommon.constant.dfs.order.WorkOrderStateEnum;
import com.yelink.dfscommon.entity.AppendixEntity;
import com.yelink.dfscommon.entity.ams.ProductOrderEntity;
import com.yelink.dfscommon.entity.ams.dto.SaleOrderVO;
import com.yelink.dfscommon.entity.dfs.DictEntity;
import com.yelink.dfscommon.pojo.ResponseData;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @Date 2021/6/8 14:22
 */
@Slf4j
@RestController
@RequestMapping("/lcds")
public class ZheJiangLcdController extends BaseController {

    @Resource
    private ProductionLineService lineService;
    @Resource
    private DictService dictService;
    @Resource
    private RecordWorkOrderCountService recordWorkOrderCountService;
    @Resource
    private WorkOrderService workOrderService;
    @Resource
    private FacUserMapper facUserMapper;
    @Resource
    private FacilitiesService facilitiesService;
    @Resource
    private ModelMapper modelMapper;
    @Resource
    private CraftProcedureService craftProcedureService;
    @Resource
    private ProcedureFileService procedureFileService;
    @Resource
    private DeviceService deviceService;
    @Resource
    private MaterialService materialService;
    @Resource
    private MaintainSchemeFacilitiesService maintainSchemeFacilitiesService;
    @Resource
    private AppendixService appendixService;
    @Resource
    private OrderWorkOrderService orderWorkOrderService;
    @Resource
    @Lazy
    private WorkOrderSubcontractService workOrderSubcontractService;
    @Resource
    @Lazy
    private WorkOrderBasicUnitRelationService basicUnitRelationService;
    @Resource
    private SkuService skuService;

    /**
     * 获取异常列表
     *
     * @return
     * <AUTHOR>
     */
    @GetMapping("/list/abnormal/{fid}")
    public ResponseData listAbnormal(@PathVariable(value = "fid") String fid) {
        List<DictEntity> dictEntityList = dictService.listByTypeDes(DictTypeEnum.ABNORMAL_NAME.getType(), fid);
        if (CollectionUtils.isEmpty(dictEntityList)) {
            return success();
        }
        //初始化一个map
        Map<String, List<DictEntity>> map = new HashMap<>();
        for (DictEntity dict : dictEntityList) {
            String key = dict.getUrl();
            if (StringUtils.isEmpty(key)) {
                continue;
            }
            if (map.containsKey(key)) {
                //map中存在以此id作为的key，将数据存放当前key的map中
                map.get(key).add(dict);
            } else {
                //map中不存在以此id作为的key，新建key用来存放数据
                List<DictEntity> userList = new ArrayList<>();
                userList.add(dict);
                map.put(key, userList);
            }
        }
        return success(map);
    }


    /**
     * 获取工单检查情况详情
     *
     * @param workOrderNumber
     * @return
     * <AUTHOR>
     */
    @Deprecated
    @GetMapping("/count/{workOrderNumber}/{fid}")
    public ResponseData getCountRecord(@PathVariable(value = "workOrderNumber") String workOrderNumber, @PathVariable(value = "fid") Integer fid) {
        Date recordDate = dictService.getRecordDate(new Date());
        RecordWorkOrderCountEntity workOrderCountEntity = recordWorkOrderCountService.getByWorkOrderNumber(workOrderNumber, fid, recordDate);
        return ResponseData.success(workOrderCountEntity);
    }

    @PostMapping("/count/workOrderNumber/fid")
    public ResponseData getCountRecord2(@RequestBody RecordWorkOrderCountSelectDTO dto) {
        String workOrderNumber = dto.getWorkOrderNumber();
        Integer fid = dto.getFid();
        Date recordDate = dictService.getRecordDate(new Date());
        RecordWorkOrderCountEntity workOrderCountEntity = recordWorkOrderCountService.getByWorkOrderNumber(workOrderNumber, fid, recordDate);
        return ResponseData.success(workOrderCountEntity);
    }

    @GetMapping("/workOrder/count")
    public ResponseData getCountRecordNoFid(@RequestParam(value = "workOrderNumber") String workOrderNumber) {
        Date recordDate = dictService.getRecordDate(new Date());
        RecordWorkOrderCountEntity workOrderCountEntity = recordWorkOrderCountService.getByWorkOrderNumber(workOrderNumber, null, recordDate);
        return ResponseData.success(workOrderCountEntity);
    }

    /**
     * 获取工位维修下工单检查情况详情
     *
     * @param workOrderNumber
     * @return
     * <AUTHOR>
     */
    @GetMapping("/maintain/count")
    public ResponseData getMaintainRecord(@RequestParam(value = "workOrderNumber") String workOrderNumber, @RequestParam(value = "fid") Integer fid) {
        Date recordDate = dictService.getRecordDate(new Date());
        RecordWorkOrderCountEntity workOrderCountEntity = recordWorkOrderCountService.getMaintainByWorkOrderNumber(workOrderNumber, fid, recordDate, true);
        return ResponseData.success(workOrderCountEntity);
    }

    /**
     * 提交一次工单检查
     *
     * @param recordWorkOrderUnqualifiedEntity
     * @return
     * <AUTHOR>
     */
    @PostMapping("/submit/inspection")
    public ResponseData submitInspection(@RequestBody RecordWorkOrderUnqualifiedEntity recordWorkOrderUnqualifiedEntity) {
        String username = getUsername();
        recordWorkOrderUnqualifiedEntity.setCreateBy(username);
        recordWorkOrderCountService.submitInspection(recordWorkOrderUnqualifiedEntity);
        return ResponseData.success();
    }

    /**
     * 获取产线下状态为发放、投入、挂起、完成的工单
     *
     * @param lineId
     * @return
     * <AUTHOR>
     */
    @GetMapping("/list/workOrder")
    public ResponseData listWorkOrderByLineId(@RequestParam(value = "lineId", required = false) Integer lineId,
                                              @RequestParam(value = "teamId", required = false) Integer teamId,
                                              @RequestParam(value = "deviceId", required = false) Integer deviceId,
                                              @RequestParam(value = "workCenterId", required = false) Integer workCenterId,
                                              @RequestParam(value = "state", required = false) Integer state,
                                              @RequestParam(value = "workOrderNumber", required = false) String workOrderNumber,
                                              @RequestParam(value = "workOrderName", required = false) String workOrderName,
                                              @RequestParam(value = "materialCode", required = false) String materialCode,
                                              @RequestParam(value = "materialName", required = false) String materialName,
                                              @RequestParam(value = "assignmentState", required = false) String assignmentState,
                                              @RequestParam(value = "current", required = false, defaultValue = "1") Integer current,
                                              @RequestParam(value = "size", required = false, defaultValue = "10") Integer size,
                                              @RequestParam(value = "projectDefineName", required = false) String projectDefineName,
                                              @RequestParam(value = "isSubcontract", required = false, defaultValue = "false") Boolean isSubcontract) throws ParseException {
        Page<WorkOrderEntity> workOrderEntities = workOrderService.listWorkOrderByLineId(lineId, teamId, deviceId, workCenterId, state, workOrderNumber, workOrderName, materialCode, materialName, current, size, assignmentState, isSubcontract, projectDefineName);
        return ResponseData.success(workOrderEntities);
    }


    /**
     * 获取状态数量统计
     *
     * @return
     */
    @GetMapping("/list/state/count")
    public ResponseData listStateCount(@RequestParam(value = "lineId", required = false) Integer lineId,
                                       @RequestParam(value = "teamId", required = false) Integer teamId,
                                       @RequestParam(value = "deviceId", required = false) Integer deviceId,
                                       @RequestParam(value = "workCenterId", required = false) Integer workCenterId,
                                       @RequestParam(value = "isBarCodePad", required = false) Boolean isBarCodePad) throws ParseException {
        return ResponseData.success(workOrderService.listStateCount(lineId, deviceId, teamId, workCenterId, isBarCodePad, getUsername()));
    }

    /**
     * 质检app获取产线下所有状态为投入、的工单
     *
     * @param productionLineId
     * @return
     * <AUTHOR>
     */
    @GetMapping("/defect/list/workOrder/{productionLineId}")
    public ResponseData listWorkOrderByLineIdForDefect(@PathVariable(value = "productionLineId") Integer productionLineId) {
        List<WorkOrderEntity> workOrderEntities = workOrderService.listWorkOrderByLineIdForDefect(productionLineId);
        return ResponseData.success(workOrderEntities);
    }

    /**
     * 上料记录app获取产线下所有状态为投入、挂起，生效的工单
     *
     * @param productionLineId
     * @return
     * <AUTHOR>
     */
    @GetMapping("/feed/list/workOrder/{productionLineId}")
    public ResponseData listWorkOrderByLineIdForFeed(@PathVariable(value = "productionLineId") Integer productionLineId,
                                                     @RequestParam(value = "state", required = false) Integer state) {
        List<WorkOrderEntity> workOrderEntities = workOrderService.listWorkOrderByLineIdForFeed(productionLineId, state);
        return ResponseData.success(workOrderEntities);
    }

    /**
     * 根据userName找到属于他的所有工位
     *
     * @param userName
     * @return
     */
    @GetMapping("/list/facilities/{userName}")
    public ResponseData listFacilitiesByUserName(@PathVariable(value = "userName") String userName,
                                                 @RequestParam(value = "isDefective", required = false) Boolean isDefective,
                                                 @RequestParam(value = "lineId", required = false) Integer lineId,
                                                 @RequestParam(value = "isMaintain", required = false) Boolean isMaintain) {
        //免密登录临时方案
        if (StringUtils.isBlank(userName) || userName.equals(Constant.NULL)) {
            userName = Constant.ADMIN;
        }
        List<FacilitiesEntity> list = facUserMapper.getFacByUsername(userName, FacilitiesTypeEnum.CUBICLE.getCode());
        //找到管理人账号为userName的FacilitiesEntity
        LambdaQueryWrapper<FacilitiesEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(FacilitiesEntity::getTypeOneCode, FacilitiesTypeEnum.CUBICLE.getCode());
        wrapper.eq(FacilitiesEntity::getMagName, userName);
        List<FacilitiesEntity> listTwo = facilitiesService.list(wrapper);
        list.addAll(listTwo);

        //List<FacilitiesEntity> result = new ArrayList<>();
        //去重
        /*list.stream().collect(Collectors.groupingBy(FacilitiesEntity::getFid))
                .forEach((Integer fid, List<FacilitiesEntity> ls) -> result.add(ls.get(0)));*/

        Collection<FacilitiesEntity> values = list.stream().collect(Collectors.groupingBy(FacilitiesEntity::getFid, Collectors.collectingAndThen(Collectors.toList(), o -> o.get(0)))).values();

        List<FacilitiesEntity> result = new ArrayList<>(values);

        //是否为质检工位
        if (isDefective != null) {
            result = result.stream().filter(o -> o.getIsDefective().equals(isDefective)).collect(Collectors.toList());
        }
        // 根据产线过滤
        if (lineId != null) {
            result = result.stream().filter(o -> o.getProductionLineId().equals(lineId)).collect(Collectors.toList());
        }
        //是否维修工位
        if (isMaintain != null) {
            List<Integer> fids = result.stream().map(FacilitiesEntity::getFid).collect(Collectors.toList());
            List<MaintainSchemeFacilitiesEntity> schemeByFids = maintainSchemeFacilitiesService.getSchemeByFids(fids);
            List<Integer> maintainFids = schemeByFids.stream().map(MaintainSchemeFacilitiesEntity::getFid).collect(Collectors.toList());
            if (isMaintain) {
                result = result.stream().filter(o -> maintainFids.contains(o.getFid())).collect(Collectors.toList());
            } else {
                result = result.stream().filter(o -> !maintainFids.contains(o.getFid())).collect(Collectors.toList());
            }
        }

        //排序
        result.sort(Comparator.comparing(FacilitiesEntity::getCreateTime));
        return ResponseData.success(result);
    }


    /**
     * @return
     * <AUTHOR>
     */
    @GetMapping("/visit")
    public ResponseData getVisitScreen() {
        LambdaQueryWrapper<ModelEntity> modelWrapper = new LambdaQueryWrapper<>();
        modelWrapper.eq(ModelEntity::getName, ModelIdEnum.LCD_LINE.getModelName());
        List<ModelEntity> modelEntities = modelMapper.selectList(modelWrapper);
        ModelEntity modelEntity;
        if (!CollectionUtils.isEmpty(modelEntities)) {
            modelEntity = modelEntities.get(0);
        } else {
            throw new ResponseException(RespCodeEnum.LCD_LINE_MODEL_NOT_FOUND);
        }
        LambdaQueryWrapper<ProductionLineEntity> lineWrapper = new LambdaQueryWrapper<>();
        lineWrapper.eq(ProductionLineEntity::getModelId, modelEntity.getId()).last("limit 1");
        ProductionLineEntity productionLineEntity = lineService.getOne(lineWrapper);
        //获取产线下所有工位
        List<FacilitiesEntity> facilitiesList = facilitiesService.getByLineId(productionLineEntity.getProductionLineId());
        // 根据产线实例ID获取实例设备列表
        ScreenVisitDTO dto = ScreenVisitDTO.builder()
                .productionLineName(productionLineEntity.getName())
                .facilitiesList(facilitiesList)
                .build();
        return ResponseData.success(dto);
    }

    /**
     * 设置直通率标准
     *
     * @param dto
     * @return
     */
    @PostMapping("/set/pass/standard")
    public ResponseData setStandard(@RequestBody PassStandardDTO dto) {
        dictService.setStandard(dto);
        return success();
    }

    /**
     * 获取直通率标准
     *
     * @return
     * <AUTHOR>
     */
    @GetMapping("/get/pass/standard/{fid}")
    public ResponseData getStandard(@PathVariable Integer fid) {
        PassStandardDTO passStandard = dictService.getPassStandard(fid);
        return success(passStandard);
    }

    /**
     * 获取物料编号
     *
     * @return
     * <AUTHOR>
     */
    @GetMapping("/material/{fid}")
    public ResponseData getMaterialCodes(@PathVariable Integer fid,
                                         @RequestParam(value = "start", required = false) String start,
                                         @RequestParam(value = "end", required = false) String end) {
        Map<String, String> map = recordWorkOrderCountService.getMaterialCodes(fid, start, end);
        return success(map);
    }

    /**
     * 检测工位的直通率图表
     *
     * @return
     * <AUTHOR>
     */
    @GetMapping("/pass/{fid}")
    public ResponseData getPassLine(@PathVariable Integer fid,
                                    @RequestParam(value = "start", required = false) String start,
                                    @RequestParam(value = "end", required = false) String end,
                                    @RequestParam(value = "workOrders", required = false) String workOrders) {
        JSONObject passLine = recordWorkOrderCountService.getPassLine(fid, start, end, workOrders);
        return success(passLine);
    }

    /**
     * 获取产线的工单执行情况
     *
     * @return
     */
    @GetMapping("/section/work")
    public ResponseData getWorkOrderSituation(@RequestParam(value = "workOrderNumber") String workOrderNumber) {
        return getWorkOrderByLineId(workOrderNumber);
    }

    /**
     * 获取产线的工单执行情况
     *
     * @return
     */
    @GetMapping("/section/work/{workOrderNumber}")
    public ResponseData getWorkOrderByLineId(@PathVariable(value = "workOrderNumber") String workOrderNumber) {
        //找到正在生产的工单
        WorkOrderEntity workOrderEntity = workOrderService.getWorkOrderByNumberWithIsolation(workOrderNumber, getUsername());
        if (workOrderEntity == null) {
            return success();
        }
//        Integer productionLineId = workOrderEntity.getLineId();
        //找到产线负责人名字
//        ProductionLineEntity productionLineEntity = lineService.getById(productionLineId);
        // 判断此工单是否正在占用计数器
//        boolean isCountNow = false;
//        String occupyWorkOrderNumber = orderExecuteSeqService.getOccupyOrderByLineId(productionLineId);
//        if (workOrderNumber.equals(occupyWorkOrderNumber)) {
//            isCountNow = true;
//        }
        workOrderService.setCraftProcedure(workOrderEntity);
        // 获取工单关联的生产基本单元列表
        List<WorkOrderBasicUnitRelationEntity> basicUnitRelationEntities = basicUnitRelationService.getByWorkOrderNumber(workOrderEntity.getWorkOrderNumber());
        String productBasicUnitNames = basicUnitRelationEntities.stream().map(WorkOrderBasicUnitRelationEntity::getProductionBasicUnitName).collect(Collectors.joining(Constants.SEP));

        //找到工位情况列表
//        LambdaQueryWrapper<FacilitiesEntity> wrapper = new LambdaQueryWrapper<>();
//        wrapper.eq(FacilitiesEntity::getProductionLineId, productionLineId);
//        wrapper.eq(FacilitiesEntity::getTypeOneCode, FacilitiesTypeEnum.CUBICLE.getCode());
//        List<FacilitiesEntity> facilitiesEntities = facilitiesService.getByLineId(productionLineId);
        List<Integer> productBasicUnitIds = basicUnitRelationEntities.stream().map(WorkOrderBasicUnitRelationEntity::getProductionBasicUnitId).collect(Collectors.toList());
        List<FacilitiesDTO> facilitiesDTOList = new ArrayList<>();
        if (CollectionUtils.isEmpty(productBasicUnitIds)) {
            facilitiesDTOList = facilitiesService.lambdaQuery()
                    .eq(FacilitiesEntity::getProductionLineId, productBasicUnitIds)
                    .list().stream().map(facilitiesEntity -> FacilitiesDTO.builder()
                            .fid(facilitiesEntity.getFid())
                            .fname(facilitiesEntity.getFname())
                            .magNickname(facilitiesEntity.getMagNickname())
                            .state(facilitiesEntity.getState())
                            .build()).collect(Collectors.toList());
        }

        //判断是否显示修改当天计划数量
        Date startDate = workOrderEntity.getStartDate();
        Date endDate = workOrderEntity.getEndDate();
        Date now = new Date();
        boolean setPlan = now.getTime() >= startDate.getTime() && now.getTime() <= endDate.getTime();
        //获取物料名称
        MaterialEntity materialEntity = materialService.getMaterialEntityByCode(workOrderEntity.getMaterialCode());
        ArrayList<WorkOrderEntity> workOrderEntities = new ArrayList<>();
        workOrderEntities.add(workOrderEntity);
        Map<Integer, SaleOrderVO> map = orderWorkOrderService.orderMaterialListByWorkOrders(workOrderEntities);
        SaleOrderVO saleOrderVO = map.get(workOrderEntity.getWorkOrderId());
        List<Integer> workOrderIds = new ArrayList<>();
        workOrderIds.add(workOrderEntity.getWorkOrderId());
        Map<Integer, ProductOrderEntity> productOrderEntityMap = orderWorkOrderService.listProductOrderByWorkIdList(workOrderIds);
        ProductOrderEntity productOrderEntity = productOrderEntityMap.get(workOrderEntity.getWorkOrderId());
        Double theoryHour = workOrderService.workOrderTheoryHour(workOrderEntity);
        workOrderSubcontractService.showSubcontract(Collections.singletonList(workOrderEntity));
        LineSituationDTO lineSituationDTO = LineSituationDTO.builder()
                .productOrderEntity(productOrderEntity)
                .saleOrderVO(saleOrderVO)
//                .lineId(productionLineId)
                .lineName(productBasicUnitNames)
//                .lineModelId(productionLineEntity == null ? null : productionLineEntity.getModelId())
                .workOrderNumber(workOrderEntity.getWorkOrderNumber())
                .skuId(workOrderEntity.getSkuId())
                .workOrderName(workOrderEntity.getWorkOrderName())
                .actualStartDate(workOrderEntity.getActualStartDate())
                .actualEndDate(workOrderEntity.getActualEndDate())
                .state(workOrderEntity.getState())
                .stateName(WorkOrderStateEnum.getNameByCode(workOrderEntity.getState()))
                .planQuantity(workOrderEntity.getPlanQuantity())
                .finishCount(workOrderEntity.getFinishCount())
//                .name(productionLineEntity == null ? null : productionLineEntity.getMagNickname())
                .materialFields(materialEntity)
                .unqualified(workOrderEntity.getUnqualified() == null ? 0.0 : workOrderEntity.getUnqualified())
                .schedule(workOrderEntity.getProgress() == null ? 0 : workOrderEntity.getProgress() * 100)
                .facilitiesDTOList(facilitiesDTOList)
                .setPlan(setPlan)
//                .isCountNow(isCountNow)
                .lineCode(workOrderEntity.getLineCode())
                .coefficient(workOrderEntity.getCoefficient())
                .startDate(workOrderEntity.getStartDate())
                .endDate(workOrderEntity.getEndDate())
                .workOrderId(workOrderEntity.getWorkOrderId())
                .workCenterId(workOrderEntity.getWorkCenterId())
                .workCenterName(workOrderEntity.getWorkCenterName())
                .workCenterType(workOrderEntity.getWorkCenterType())
                .workCenterRelevanceType(workOrderEntity.getWorkCenterRelevanceType())
                .remark(workOrderEntity.getRemark())
//                .deviceId(deviceEntity == null ? null : deviceEntity.getDeviceId())
//                .deviceCode(deviceEntity == null ? null : deviceEntity.getDeviceCode())
                .deviceName(productBasicUnitNames)
//                .teamId(sysTeamEntity == null ? null : sysTeamEntity.getId())
//                .teamCode(sysTeamEntity == null ? null : sysTeamEntity.getTeamCode())
                .teamName(productBasicUnitNames)
                .procedureName(workOrderEntity.getProcedureName())
                .assignmentStateName(AssignmentStateEnum.getNameByType(workOrderEntity.getAssignmentState()))
                .priority(workOrderEntity.getPriority())
                .autoCount(workOrderEntity.getAutoCount())
                .theoryHour(theoryHour)
                .isSubcontract(workOrderEntity.getIsSubcontract())
                .workOrderSubcontracts(workOrderEntity.getWorkOrderSubcontracts())
                .sku(skuService.getById(workOrderEntity.getSkuId()))
                .projectDefineId(workOrderEntity.getProjectDefineId())
                .projectDefineName(workOrderEntity.getProjectDefineName())
                .build();



        return ResponseData.success(lineSituationDTO);
    }

    /**
     * 获取工位状态列表
     *
     * @return
     * <AUTHOR>
     */
    @GetMapping("/list/facilities/state")
    public ResponseData listFacState() {
        return success(dictService.listByType(DictTypeEnum.FACILITIES_STATE.getType()));
    }


    @GetMapping("/list/procedureId/file/sop")
    public ResponseData listProcedureIdFile(@RequestParam Integer procedureId, @RequestParam String workOrderNumber) {
        WorkOrderEntity workOrderEntity = workOrderService.getSimpleWorkOrderByNumber(workOrderNumber);
        List<CraftFileNewDTO> procedureFiles = workOrderService.getProcedureFiles(workOrderNumber, null, procedureId);
        List<CraftFileNewDTO> workOrderFiles = workOrderService.getWorkOrderFiles(workOrderEntity.getWorkOrderId());
        List<CraftFileNewDTO> materialFiles = workOrderService.getMaterialFiles(workOrderEntity.getMaterialCode());
        List<CraftFileNewDTO> bomFiles = workOrderService.getBomFiles(workOrderEntity.getMaterialCode());
        List<CraftFileNewDTO> result = Stream.of(procedureFiles, workOrderFiles, materialFiles, bomFiles).flatMap(Collection::stream).filter(Objects::nonNull).collect(Collectors.toList());
        return ResponseData.success(result);
    }

    /**
     * 获取工位对应的文件url
     *
     * @param fid
     * @param workOrderNumber
     * @return
     */
    @GetMapping("/list/facilities/file/sop")
    public ResponseData listFile(@RequestParam Integer fid, @RequestParam String workOrderNumber,
                                 @RequestParam(value = "name", required = false) String name,
                                 @RequestParam(value = "type", required = false) String type) {

        FacilitiesEntity facilitiesEntity = facilitiesService.getById(fid);
        if (facilitiesEntity == null) {
            return success();
        }
        WorkOrderEntity workOrderEntity = workOrderService.getSimpleWorkOrderByNumber(workOrderNumber);
        if (workOrderEntity == null) {
            return success();
        }
        ////找到最新的工艺id
        //CraftEntity craftEntity = craftService.getCraftByMaterialCode(workOrderEntity.getMaterialCode());
        Integer craftId = workOrderEntity.getCraftId();
        //找到每个工位对应的工单的附件
        ModelEntity modelEntity = modelMapper.selectById(facilitiesEntity.getModelId());
        List<CraftProcedureEntity> craftProcedureEntities = craftProcedureService.getEntityByCraftIdAndFacType(craftId, modelEntity.getName());
        List<CraftFileNewDTO> fileList = new ArrayList<>();
        if (CollectionUtils.isEmpty(craftProcedureEntities)) {
            return success();
        }
        for (CraftProcedureEntity craftProcedureEntity : craftProcedureEntities) {
            List<ProcedureFileEntity> entities = procedureFileService.getEntityByProcedureIdAndCraftId(craftProcedureEntity.getId());
            if (!CollectionUtils.isEmpty(entities)) {
                for (ProcedureFileEntity fileEntity : entities) {
                    CraftFileNewDTO craftFileDTO = CraftFileNewDTO.builder()
                            .name(craftProcedureEntity.getProcedureName())
                            .filePath(fileEntity.getFileUrl())
                            .typeName(AppendixTypeEnum.PROCEDURE_APPENDIX.getName())
                            .type(AppendixTypeEnum.PROCEDURE_APPENDIX.getCode())
                            .fileName(fileEntity.getName()).build();
                    fileList.add(craftFileDTO);
                }
            }
        }


        //获取工位对应的文件url
        List<CraftFileNewDTO> listTwo = this.getDeviceFile(fid, type, name);
        if (!CollectionUtils.isEmpty(listTwo)) {
            fileList.addAll(listTwo);
        }
        return success(fileList);

    }

    /**
     * 获取工位对应的文件url
     *
     * @param fid
     * @return
     */
    private List<CraftFileNewDTO> getDeviceFile(Integer fid, String type, String name) {
        LambdaQueryWrapper<DeviceEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(DeviceEntity::getFid, fid);
        List<DeviceEntity> list = deviceService.list(wrapper);
        if (CollectionUtils.isEmpty(list)) {
            return null;

        }
        List<Integer> relateIds = list.stream().map(DeviceEntity::getDeviceId).collect(Collectors.toList());
        List<AppendixEntity> appendixEntities = appendixService.getFileByTypeAndName(type, name, relateIds);
        List<CraftFileNewDTO> craftFileDTOList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(appendixEntities)) {
            for (AppendixEntity fileEntity : appendixEntities) {
                CraftFileNewDTO craftFileDTO = CraftFileNewDTO.builder()
                        .name(fileEntity.getRelateName())
                        .filePath(fileEntity.getFilePath())
                        .typeName(AppendixTypeEnum.DEVICE_APPENDIX.getName())
                        .type(AppendixTypeEnum.DEVICE_APPENDIX.getCode())
                        .fileName(fileEntity.getFileName()).build();
                craftFileDTOList.add(craftFileDTO);
            }
        }
        return craftFileDTOList;
    }

    /**
     * 修改工位状态
     *
     * @return
     */
    @GetMapping("/facilities/edit")
    public ResponseData facEditState(@RequestParam Integer fid, @RequestParam Integer state) {
        FacilitiesEntity facilitiesEntity = FacilitiesEntity.builder()
                .fid(fid)
                .state(state)
                .build();
        facilitiesService.updateById(facilitiesEntity);

        return success();
    }
}
