package com.yelink.dfs.controller.statement;


import com.asyncexcel.core.model.ExcelTask;
import com.asyncexcel.springboot.ExcelService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yelink.dfs.common.BaseController;
import com.yelink.dfs.constant.OperationType;
import com.yelink.dfs.constant.common.ModelUploadFileEnum;
import com.yelink.dfs.entity.common.ModelUploadFileEntity;
import com.yelink.dfs.entity.common.UploadEntity;
import com.yelink.dfs.entity.statement.ManageReportDownloadRecordEntity;
import com.yelink.dfs.entity.statement.ManageReportEntity;
import com.yelink.dfs.entity.statement.ManageReportTaskEntity;
import com.yelink.dfs.entity.statement.ManageReportTypeEntity;
import com.yelink.dfs.entity.statement.ManageReportTypeRelationEntity;
import com.yelink.dfs.entity.statement.dto.DataSourceTypeConfigDTO;
import com.yelink.dfs.entity.statement.dto.ManageReportDataSourceConfigDTO;
import com.yelink.dfs.entity.statement.dto.ManageReportSelectDTO;
import com.yelink.dfs.entity.statement.dto.ReportDownloadFilterFieldDTO;
import com.yelink.dfs.entity.statement.dto.TaskResultSelectDTO;
import com.yelink.dfs.entity.statement.dto.TaskSelectDTO;
import com.yelink.dfs.entity.statement.vo.TaskExecuteResultVO;
import com.yelink.dfs.service.common.ModelUploadFileService;
import com.yelink.dfs.service.statement.ManageReportDownloadRecordService;
import com.yelink.dfs.service.statement.ManageReportService;
import com.yelink.dfs.service.statement.ManageReportTaskService;
import com.yelink.dfs.service.statement.ManageReportTypeRelationService;
import com.yelink.dfs.service.statement.ManageReportTypeService;
import com.yelink.dfscommon.annotation.OperLog;
import com.yelink.dfscommon.common.excel.BusinessCodeEnum;
import com.yelink.dfscommon.constant.Constant;
import com.yelink.dfscommon.constant.RespCodeEnum;
import com.yelink.dfscommon.constant.ResponseException;
import com.yelink.dfscommon.pojo.ResponseData;
import com.yelink.dfscommon.utils.ExcelTemplateImportUtil;
import lombok.AllArgsConstructor;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * 报表管理(DfsManageReport)表控制层
 *
 * <AUTHOR>
 * @since 2023-07-04 10:39:41
 */
@RestController
@AllArgsConstructor
@RequestMapping("/manage/report")
public class ManageReportController extends BaseController {
    private final ManageReportService manageReportService;
    private final ManageReportTypeService manageReportTypeService;
    private final ManageReportTypeRelationService manageReportTypeRelationService;
    private final ManageReportTaskService manageReportTaskService;
    private final ManageReportDownloadRecordService manageReportDownloadRecordService;
    private final ModelUploadFileService modelUploadFileService;
    private final ExcelService excelService;

    /**
     * 查询全部数据源可以配置的筛选字段
     */
    @GetMapping("/data/source/config")
    public ResponseData getDataSourceConfig(@RequestParam(required = false) String model) {
        List<DataSourceTypeConfigDTO> dataSourceConfigs = manageReportService.getDataSourceConfigV2(model);
        return ResponseData.success(dataSourceConfigs);
    }
    @GetMapping("/data/source/config/v2")
    public ResponseData getDataSourceConfig2(@RequestParam(required = false) String model) {
        List<DataSourceTypeConfigDTO> dataSourceConfigs = manageReportService.getDataSourceConfigV2(model);
        // 不要大分类
        List<ManageReportDataSourceConfigDTO> dataSources = new ArrayList<>();
        for (DataSourceTypeConfigDTO dataSourceConfig : dataSourceConfigs) {
            dataSources.addAll(dataSourceConfig.getDataSources());
        }
        return ResponseData.success(dataSources);
    }


    /**
     * 下载默认模板, 根据选择的数据源自动生成
     */
    @GetMapping("/default/template/export")
    public void downloadTemplate(@RequestParam String dataSources, HttpServletResponse response) throws IOException {
        InputStream inputStream = manageReportService.getDefaultTemplateInputStream(dataSources);
        ExcelTemplateImportUtil.responseToClient(response, inputStream, "报表管理默认模板" + Constant.XLSX);
    }

    /**
     * web、"报表中心"小程序-上传模板
     *         针对具体报表上传模板
     */
    @PostMapping("/import/report/template")
    public ResponseData importReportTemplate(MultipartFile file, @RequestParam Integer reportId) {
        ModelUploadFileEntity uploadFile = manageReportService.importReportTemplate(file, reportId);
        return ResponseData.success(uploadFile);
    }
    /**
     * 上传模板 - 新建报表,没reportId时使用
     */
    @PostMapping("/import/template")
    public ResponseData importExcelTemplate(MultipartFile file) {
        String fileName = file.getOriginalFilename();
        if (StringUtils.isEmpty(fileName)) {
            throw new ResponseException(RespCodeEnum.FILE_NAME_NOT_EMPTY);
        }
        // 上传
        ModelUploadFileEntity uploadFile = modelUploadFileService.uploadFile(file, ModelUploadFileEnum.MANAGE_REPORT.getCode(), getUsername());
        return ResponseData.success(uploadFile);
    }
    /**
     * 删除模板
     */
    @DeleteMapping("/template/delete")
    public ResponseData deleteTemplate(@RequestParam Integer id) {
        manageReportService.deleteReportTemplate(id);
        return ResponseData.success(true);
    }
    ///**
    // * 上传的自定义模板列表
    // */
    //@GetMapping("/template/list")
    //public ResponseData list() {
    //    List<ModelUploadFileEntity> list = modelUploadFileService.listByType(ModelUploadFileEnum.MANAGE_REPORT.getCode());
    //    return ResponseData.success(list);
    //}
    ///**
    // * 系统内置的默认模板
    // */
    //@GetMapping("/list/inner/default/template")
    //public ResponseData listDefaultTemplate() {
    //    List<ManageReportDefaultTemplateVO> templates = new ArrayList<>();
    //    for (ReportDefaultTemplateEnum value : ReportDefaultTemplateEnum.values()) {
    //        ModelUploadFileEntity uploadFileEntity = manageReportService.applyInnerDefaultTemplate(value);
    //        templates.add(ManageReportDefaultTemplateVO.builder()
    //                .defaultTemplateCode(value.getDefaultTemplateCode())
    //                .fileName(value.getDefaultTemplateName() + Constant.XLSX)
    //                .desc(value.getDesc())
    //                .templateId(uploadFileEntity.getId())
    //                .fileAddress(uploadFileEntity.getFileAddress())
    //                .build());
    //    }
    //    return ResponseData.success(templates);
    //}

    /**
     * 报表列表
     */
    @PostMapping("/list")
    public ResponseData listReport(@RequestBody ManageReportSelectDTO selectDTO) {
        Assert.notNull(selectDTO.getCurrent(), RespCodeEnum.PARAM_CURRENT_EXCEPTION.getMsgDes());
        Assert.notNull(selectDTO.getSize(), RespCodeEnum.PARAM_SIZE_EXCEPTION.getMsgDes());
        Page<ManageReportEntity> pageData = manageReportService.pageReport(selectDTO);
        return ResponseData.success(pageData);
    }
    /**
     * "报表中心"小程序 - 报表按类型展示
     */
    @GetMapping("/list/type/report")
    public ResponseData listTypeReport() {
        List<ManageReportTypeEntity> reportTypes = manageReportTypeService.listRelateReport(null);
        // 没有被关联类型的报表放到通用分类里面
        Set<Integer> typeReportIds = new HashSet<>();
        for (ManageReportTypeEntity reportType : reportTypes) {
            if (!CollectionUtils.isEmpty(reportType.getManageReports())) {
                for (ManageReportEntity manageReport : reportType.getManageReports()) {
                    typeReportIds.add(manageReport.getReportId());
                }
            }
        }
        List<ManageReportEntity> notTypeReports = manageReportService.listManageReport(null, typeReportIds);
        manageReportService.setStateNameForList(notTypeReports);

        reportTypes.add(ManageReportTypeEntity.builder()
                .typeName("通用")
                .manageReports(notTypeReports)
                .build());
        return ResponseData.success(reportTypes);
    }
    /**
     * 报表-详情
     */
    @GetMapping("/detail")
    public ResponseData detail(@RequestParam Integer reportId) {
        ManageReportEntity detail = manageReportService.detail(reportId);
        return ResponseData.success(detail);
    }

    /**
     * 新增报表
     */
    @PostMapping("/add")
    @OperLog(module = "报表管理", type = OperationType.ADD, desc = "新增了报表名称为#{reportName}的报表")
    public ResponseData addReport(@RequestBody ManageReportEntity manageReport) {
        ManageReportEntity add = manageReportService.addReportV2(manageReport);
        return ResponseData.success(add);
    }

    /**
     * 编辑报表
     */
    @PutMapping("/update")
    @OperLog(module = "报表管理", type = OperationType.UPDATE, desc = "修改了报表名称为#{reportName}的报表")
    public ResponseData updateReport(@RequestBody ManageReportEntity manageReport) {
        ManageReportEntity manageReportEntity = manageReportService.updateReport(manageReport);
        return ResponseData.success(manageReportEntity);
    }

    /**
     * 修改状态
     */
    @PostMapping("/update/state")
    @OperLog(module = "报表管理", type = OperationType.UPDATE, desc = "修改了报表名称为#{reportName}的报表状态")
    public ResponseData updateReportState(@RequestParam Integer state, @RequestParam Integer reportId) {
        ManageReportEntity manageReportEntity = manageReportService.updateReportState(reportId, state);
        return ResponseData.success(manageReportEntity);
    }

    /**
     * 删除
     */
    @DeleteMapping("/delete")
    @OperLog(module = "报表管理", type = OperationType.DELETE, desc = "删除了报表名称为#{reportName}的报表")
    public ResponseData deleteReport(@RequestParam Integer reportId) {
        ManageReportEntity manageReportEntity = manageReportService.deleteReport(reportId);
        return ResponseData.success(manageReportEntity);
    }

    /**
     * 异步下载导出
     */
    @PostMapping("/async/download")
    public ResponseData asyncDownloadExport(@RequestBody ReportDownloadFilterFieldDTO selectDTO) {
        Assert.notNull(selectDTO.getReportId(), "reportId不能为空");
        long excelTaskId = manageReportService.asyncDownloadExport(selectDTO);
        // 保存报表下载关联记录
        ManageReportEntity byId = manageReportService.lambdaQuery()
                .select(ManageReportEntity::getReportId, ManageReportEntity::getModel, ManageReportEntity::getReportName)
                .eq(ManageReportEntity::getReportId, selectDTO.getReportId())
                .one();
        manageReportDownloadRecordService.save(ManageReportDownloadRecordEntity.builder()
                .excelTaskId(excelTaskId)
                .taskCode(null)
                .reportName(byId.getReportName())
                .model(byId.getModel())
                .createTime(new Date())
                .build());
        return success(excelTaskId);
    }

    /**
     * 查询 异步下载导出进度
     */
    @GetMapping("/export/task/{taskId}")
    public ResponseData exportTask(@PathVariable Long taskId) {
        ExcelTask excelTask = new ExcelTask();
        excelTask.setId(taskId);
        excelTask.setBusinessCode(BusinessCodeEnum.MANAGE_REPORT.name());
        IPage<ExcelTask> page = excelService.listPage(excelTask, 1, 1);
        List<ExcelTask> records = page.getRecords();
        return success(records.get(0));
    }

    /**
     * "报表中心"小程序 查询下载
     *      保存成临时文件，改变条件查询，只更新对应sheet
     */
    @PostMapping("/download")
    public ResponseData downloadExport(@RequestBody ReportDownloadFilterFieldDTO selectDTO) {
        Assert.notNull(selectDTO.getReportId(), "reportId不能为空");
        //小程序下载导出
        Long taskId = manageReportService.downloadExport(selectDTO);
        return success(taskId);
    }
    /**
     * "报表中心"小程序 查询下载: 查询下载导出进度
     */
    @GetMapping("/get/download/file")
    public ResponseData downloadFile(@RequestParam Integer taskId) {
        //同步下载导出
        UploadEntity uploadEntity = manageReportService.downloadFile(taskId);
        return success(uploadEntity);
    }

    /**
     * 报表定时任务：新建定时任务
     */
    @PostMapping("/task/add")
    @OperLog(module = "报表管理", type = OperationType.ADD, desc = "新增了任务名称为#{taskCode}的报表定时任务")
    public ResponseData addReportTask(@RequestBody ManageReportTaskEntity manageReportTask) {
        Assert.notNull(manageReportTask.getReportId(), "reportId不能为空");

        ManageReportTaskEntity add = manageReportService.addReportTask(manageReportTask);
        return ResponseData.success(add);
    }

    /**
     * 报表定时任务：任务列表
     */
    @PostMapping("/task/list")
    public ResponseData listReportTask(@RequestBody TaskSelectDTO selectDTO) {
        Assert.notNull(selectDTO.getModel(), "所属模块不能为空");
        Assert.notNull(selectDTO.getCurrent(), RespCodeEnum.PARAM_CURRENT_EXCEPTION.getMsgDes());
        Assert.notNull(selectDTO.getSize(), RespCodeEnum.PARAM_SIZE_EXCEPTION.getMsgDes());

        Page<ManageReportTaskEntity> pageData = manageReportService.listReportTask(selectDTO);
        return ResponseData.success(pageData);
    }

    /**
     * 报表定时任务：删除任务
     */
    @DeleteMapping("/task/delete")
    @OperLog(module = "报表管理", type = OperationType.DELETE, desc = "删除了任务名称为#{taskCode}的报表定时任务")
    public ResponseData deleteTask(@RequestParam Integer taskId) {
        ManageReportTaskEntity byId = manageReportTaskService.getById(taskId);
        manageReportTaskService.deleteTask(taskId);
        return ResponseData.success(byId);
    }

    /**
     * 报表定时任务：停止任务(改变状态)
     */
    @PostMapping("/task/update/state")
    public ResponseData updateTaskState(@RequestParam Integer state, @RequestParam Integer taskId) {
        manageReportService.updateTaskState(taskId, state);
        return ResponseData.success();
    }

    /**
     * 报表定时任务：定时任务执行历史结果
     */
    @PostMapping("/task/execute/result")
    public ResponseData taskExecuteResult(@RequestBody TaskResultSelectDTO selectDTO) {
        Assert.notNull(selectDTO.getModel(), "所属模块不能为空");
        Page<TaskExecuteResultVO> pageData = manageReportService.pageTaskExecuteResult(selectDTO);
        return ResponseData.success(pageData);
    }

    /**
     * 删除报表下载记录
     */
    @DeleteMapping("/remove/report/download/record")
    public ResponseData removeReportDownloadRecord(@RequestBody List<Integer> excelTaskIds) {
        Assert.notEmpty(excelTaskIds, "参数不能为空");
        boolean update = manageReportDownloadRecordService.lambdaUpdate()
                .in(ManageReportDownloadRecordEntity::getExcelTaskId, excelTaskIds)
                .set(ManageReportDownloadRecordEntity::getDeleted, 1)
                .update();
        return ResponseData.success(update);
    }


    /**
     * 报表类型 - 列表
     */
    @GetMapping("/type/list")
    public ResponseData listReportType(@RequestParam(required = false) Integer typeId) {
        List<ManageReportTypeEntity> reportTypes = manageReportTypeService.listReportType(typeId);
        return ResponseData.success(reportTypes);
    }
    /**
     * 报表类型 - 添加
     */
    @PostMapping("/type/add")
    @OperLog(module = "报表管理", type = OperationType.ADD, desc = "新增了报表类型名称为#{typeName}的报表类型")
    public ResponseData addReportType(@RequestBody ManageReportTypeEntity reportType) {
        Assert.notNull(reportType.getTypeName(), "报表类型名称不能为空");
        ManageReportTypeEntity addReportType = manageReportTypeService.addReportType(reportType);
        return ResponseData.success(addReportType);
    }
    /**
     * 报表类型 - 编辑
     */
    @PostMapping("/type/update")
    @OperLog(module = "报表管理", type = OperationType.UPDATE, desc = "更新了报表类型名称为#{typeName}的报表类型")
    public ResponseData updateReportType(@RequestBody ManageReportTypeEntity reportType) {
        Assert.notNull(reportType.getTypeId(), "typeId不能为空");
        ManageReportTypeEntity updateReportType = manageReportTypeService.updateReportType(reportType);
        return ResponseData.success(updateReportType);
    }
    /**
     * 报表类型 - 删除
     */
    @DeleteMapping("/type/remove")
    @OperLog(module = "报表管理", type = OperationType.DELETE, desc = "删除了报表类型名称为#{typeName}的报表类型")
    public ResponseData removeReportType(@RequestParam Integer typeId) {
        ManageReportTypeEntity byId = manageReportTypeService.getById(typeId);
        manageReportTypeService.removeReportType(typeId);
        return ResponseData.success(byId);
    }
    /**
     * 报表类型 - 类型列表,并查出类型下关联的报表
     */
    @GetMapping("/type/list/relate/report")
    public ResponseData listRelateReport(@RequestParam(required = false) Integer typeId) {
        List<ManageReportTypeEntity> reportTypes = manageReportTypeService.listRelateReport(typeId);
        return ResponseData.success(reportTypes);
    }
    /**
     * 报表类型 - 查询能够添加到该类型的报表
     */
    @GetMapping("/type/list/can/add/report")
    public ResponseData listCanAddReport(@RequestParam Integer typeId) {
        List<ManageReportEntity> manageReports = manageReportTypeService.listCanAddReport(typeId);
        return ResponseData.success(manageReports);
    }
    /**
     * 报表类型 - 关联报表和类型
     */
    @PostMapping("/type/add/report/relate")
    public ResponseData reportTypeRelate(@RequestBody List<ManageReportTypeRelationEntity> list) {
        manageReportTypeRelationService.reportTypeRelate(list);
        return ResponseData.success(true);
    }
    /**
     * 报表类型 - 移除报表和类型关联
     */
    @PostMapping("/type/remove/report/relate")
    public ResponseData removeReportTypeRelate(@RequestBody List<ManageReportTypeRelationEntity> list) {
        manageReportTypeRelationService.removeReportTypeRelate(list);
        return ResponseData.success(true);
    }


}

