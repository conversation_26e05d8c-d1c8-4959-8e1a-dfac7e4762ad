package com.yelink.dfs.controller.product;

import com.yelink.dfs.common.BaseController;
import com.yelink.dfs.constant.Constant;
import com.yelink.dfs.constant.OperationType;
import com.yelink.dfs.service.excel.MaterialTypeExcelImport;
import com.yelink.dfscommon.annotation.OperLog;
import com.yelink.dfscommon.pojo.ResponseData;
import com.yelink.dfscommon.utils.ExcelTemplateImportUtil;
import com.yelink.dfscommon.utils.ExcelUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/material/type")
public class MaterialTypeController extends BaseController {
    @Resource
    private MaterialTypeExcelImport materialTypeExcelImport;

    @PostMapping("/import")
    @OperLog(module = "系统管理", type = OperationType.ADD, desc = "导入了物料类型")
    public ResponseData importDeviceTarget(MultipartFile file) {
        materialTypeExcelImport.asyncImport(file);
        return success();
    }
    @GetMapping("/import_progress")
    public ResponseData importDeviceTargetProgress(){
        return success(materialTypeExcelImport.importProgress());
    }
    @GetMapping("/import_template")
    public void importDeviceTargetTemplate(HttpServletResponse response) throws IOException {
        byte[] bytes = ExcelUtil.exportDefaultExcel("classpath:template/materialTypeTemplate.xlsx");
        ExcelTemplateImportUtil.responseToClient(response, bytes, "物料类型导入模板" + Constant.XLSX);
    }
}
