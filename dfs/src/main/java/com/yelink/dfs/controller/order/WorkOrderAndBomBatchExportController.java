package com.yelink.dfs.controller.order;

import com.yelink.dfs.common.BaseController;
import com.yelink.dfs.constant.Constant;
import com.yelink.dfs.constant.common.ModelUploadFileEnum;
import com.yelink.dfs.entity.common.ModelUploadFileEntity;
import com.yelink.dfs.entity.order.vo.WorkOrderBatchVO;
import com.yelink.dfs.provider.FastDfsClientService;
import com.yelink.dfs.service.common.ModelUploadFileService;
import com.yelink.dfs.service.order.WorkOrderAndBomBatchExportService;
import com.yelink.dfscommon.utils.ExcelTemplateImportUtil;
import com.yelink.dfs.utils.FileUtil;
import com.yelink.dfscommon.constant.RespCodeEnum;
import com.yelink.dfscommon.constant.ResponseException;
import com.yelink.dfscommon.pojo.ResponseData;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.util.List;
import java.util.Objects;

/**
 * 导出管理-通过模板映射关系导出不同单据excel/pdf
 * <AUTHOR>
 * @date 2022-9-21 16:12
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/work/order/batch/export")
public class WorkOrderAndBomBatchExportController extends BaseController {

    private final FastDfsClientService fastDfsClientService;
    private final WorkOrderAndBomBatchExportService workOrderAndBomBatchExportService;
    private final ModelUploadFileService modelUploadFileService;

    /**
     * 下载默认模板
     * @param response
     * @throws Exception
     */
    @GetMapping("/template/export/default")
    public void exportTemplate(HttpServletResponse response)  throws Exception{
        //找到导出的模板
        byte[] bytes = workOrderAndBomBatchExportService.downloadDefaultTemplate();
        ExcelTemplateImportUtil.responseToClient(response, bytes, "生产工单单据导出默认模板" + Constant.XLSX);
    }

    /**
     * 下载指定上传单据模板
     * @param id
     * @param response
     * @throws Exception
     */
    @GetMapping("/template/export")
    public void downloadTemplate(@RequestParam Integer id, HttpServletResponse response) throws Exception {
        ModelUploadFileEntity byId = modelUploadFileService.getById(id);
        if (Objects.isNull(byId)) {
            throw new ResponseException(RespCodeEnum.TEMPLATE_NOT_EXIST);
        }
        byte[] bytes = fastDfsClientService.getFileStream(byId.getFileAddress());
        ExcelTemplateImportUtil.responseToClient(response, bytes, byId.getFileName());
    }

    /**
     * 删除上传的单据模板
     * @param id
     * @return
     */
    @GetMapping("/template/delete")
    public ResponseData deleteTemplate(@RequestParam Integer id) {
        workOrderAndBomBatchExportService.removeTemplateById(id);
        return success();
    }

    /**
     * 上传单据模板
     * @param file
     * @return
     */
    @PostMapping("/template/upload")
    public ResponseData uploadTemplate(MultipartFile file) {
        workOrderAndBomBatchExportService.uploadTemplate(getUsername(), file);
        return success(true);
    }

    /**
     * 生产工单-上传单据模板列表
     * @return
     */
    @GetMapping("/template/list")
    public ResponseData list() {
        List<ModelUploadFileEntity> list = modelUploadFileService.listByType(ModelUploadFileEnum.WORK_ORDER.getCode());
        return success(list);
    }

    /**
     * 导出单据为pdf
     * @param batchVO
     * @param response
     * @throws Exception
     */
    @PostMapping("/print/pdf")
    public void printDataToPdf(@RequestBody WorkOrderBatchVO batchVO, HttpServletResponse response) throws Exception{
        File pdfFile = workOrderAndBomBatchExportService.asposePrintDataToPdf(batchVO);
        try {
                FileUtil.exportPdfFile(response, pdfFile, batchVO.getFileName());
        }finally {
            FileUtils.deleteQuietly(pdfFile);
        }
    }

    /**
     * 导出单据为EXCEL
     * @param batchVO
     * @param response
     * @throws Exception
     */
    @PostMapping("/export/excel")
    public void exportDataToExcel(@RequestBody WorkOrderBatchVO batchVO, HttpServletResponse response) throws Exception {
        File pdfFile = workOrderAndBomBatchExportService.asposePrintDataToExcel(batchVO);
        try {
            InputStream inputStream = new FileInputStream(pdfFile);
            ExcelTemplateImportUtil.responseToClient(response, inputStream , batchVO.getFileName());
        } finally {
            FileUtils.deleteQuietly(pdfFile);
        }
    }

}

