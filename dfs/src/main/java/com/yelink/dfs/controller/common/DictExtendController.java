package com.yelink.dfs.controller.common;


import com.yelink.dfs.common.BaseController;
import com.yelink.dfs.entity.device.dto.ShiftUserDeviceDTO;
import com.yelink.dfs.service.common.impl.DictServiceExtend;
import com.yelink.dfscommon.pojo.ResponseData;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;


/**
 * 字典扩展
 * <AUTHOR>
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/dictExtend/")
public class DictExtendController extends BaseController {

    @Resource
    private DictServiceExtend dictServiceExtend;

    @PostMapping("/shiftUserDevice/add")
    public ResponseData shiftUserDeviceAdd(@RequestBody @Validated ShiftUserDeviceDTO dto) {
        dictServiceExtend.shiftUserDeviceAdd(dto);
        return ResponseData.success();
    }
    @PostMapping("/shiftUserDevice/edit")
    public ResponseData shiftUserDeviceEdit(@RequestBody @Validated ShiftUserDeviceDTO dto) {
        dictServiceExtend.shiftUserDeviceEdit(dto);
        return ResponseData.success();
    }
    @GetMapping("/shiftUserDevice/list")
    public ResponseData shiftUserDeviceList() {
        return ResponseData.success(dictServiceExtend.shiftUserDeviceList());
    }
    @GetMapping("/getUsernameByShiftDevice")
    public ResponseData getUsernameByShiftDevice(@RequestParam Integer shiftId, @RequestParam Integer deviceId) {
        return ResponseData.success(dictServiceExtend.getUsernameByShiftDevice(shiftId, deviceId));
    }

}

