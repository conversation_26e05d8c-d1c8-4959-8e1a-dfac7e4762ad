package com.yelink.dfs.controller.common;

import com.yelink.dfs.common.BaseController;
import com.yelink.dfscommon.constant.FilteringCriteriaEnum;
import com.yelink.dfs.entity.defect.dto.FilterEnumDTO;
import com.yelink.dfscommon.pojo.ResponseData;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;


/**
 * @description: 上传接口
 * @author: zengfu
 * @create: 2020-06-11 11:36
 **/
@Slf4j
@RestController()
@RequiredArgsConstructor
@RequestMapping("/filter")
public class FilterController extends BaseController {

    /**
     * 获取流水码类型(采购单品码，生产成品码，生产流水码)
     *
     * @return
     */
    @GetMapping("/types")
    public ResponseData getTypeList() {
        List<FilterEnumDTO> list = Arrays.stream(FilteringCriteriaEnum.values())
                .map(filteringCriteriaEnum -> FilterEnumDTO.builder()
                        .sql(filteringCriteriaEnum.getSql())
                        .name(filteringCriteriaEnum.getName())
                        .type(filteringCriteriaEnum.getType())
                        .build()).collect(Collectors.toList());
        return success(list);
    }
}
