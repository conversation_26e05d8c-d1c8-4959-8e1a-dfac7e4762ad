package com.yelink.dfs.controller.node;

import com.yelink.dfs.common.BaseController;
import com.yelink.dfs.entity.node.NodeExceptionEntity;
import com.yelink.dfs.service.node.NodeExceptionService;
import com.yelink.dfscommon.pojo.ResponseData;
import com.yelink.dfscommon.utils.DateUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;

/**
 * <AUTHOR>
 * 节点异常
 * @Date 2022/4/22 10:41
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/node/exceptions")
public class NodeExceptionController extends BaseController {
    private NodeExceptionService nodeExceptionService;

    /**
     * 列表
     *
     * @param size
     * @param current
     * @param nodeExceptionName 节点异常名称
     * @return
     */
    @GetMapping("/list")
    public ResponseData list(@RequestParam(value = "size", required = false) Integer size,
                             @RequestParam(value = "current", required = false) Integer current,
                             @RequestParam(value = "nodeExceptionName", required = false) String nodeExceptionName) {
        return ResponseData.success(nodeExceptionService.getList(size, current, nodeExceptionName));
    }

    /**
     * 新增
     *
     * @param entity
     * @return
     * @throws Exception
     */
    @PostMapping("/insert")
    public ResponseData add(@RequestBody NodeExceptionEntity entity) {
        entity.setCreateBy(getUsername());
        entity.setCreateTime(DateUtil.formatToDate(new Date(), DateUtil.DATETIME_FORMAT));
        Boolean result = nodeExceptionService.add(entity);
        if (result) {
            return ResponseData.success();
        }
        return ResponseData.fail();
    }

    /**
     * 修改
     *
     * @param entity
     * @return
     * @throws Exception
     */
    @PutMapping("/update")
    public ResponseData edit(@RequestBody NodeExceptionEntity entity) {
        entity.setUpdateBy(getUsername());
        entity.setUpdateTime(DateUtil.formatToDate(new Date(), DateUtil.DATETIME_FORMAT));
        Boolean result = nodeExceptionService.edit(entity);
        if (result) {
            return ResponseData.success();
        }
        return ResponseData.fail();
    }

    /**
     * 删除
     *
     * @param id
     * @return
     */
    @DeleteMapping("/delete/{id}")
    public ResponseData remove(@PathVariable(value = "id") Integer id) {
        Boolean result = nodeExceptionService.delete(id);
        if (result) {
            return ResponseData.success();
        }
        return ResponseData.fail();
    }
}
