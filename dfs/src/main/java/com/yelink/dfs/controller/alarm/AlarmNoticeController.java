package com.yelink.dfs.controller.alarm;

import com.yelink.dfs.common.BaseController;
import com.yelink.dfs.constant.OperationType;
import com.yelink.dfs.constant.alarm.AlarmTypesEnum;
import com.yelink.dfs.entity.alarm.AlarmNoticeEntity;
import com.yelink.dfs.entity.alarm.dto.AlarmCycleDTO;
import com.yelink.dfs.entity.alarm.dto.AlarmLevelTimeDTO;
import com.yelink.dfs.service.alarm.AlarmNoticeService;
import com.yelink.dfs.utils.EnumUtil;
import com.yelink.dfscommon.annotation.OperLog;
import com.yelink.dfscommon.constant.RespCodeEnum;
import com.yelink.dfscommon.constant.ResponseException;
import com.yelink.dfscommon.pojo.ResponseData;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/3/6 17:36
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/alarmnotices")
public class AlarmNoticeController extends BaseController {
    private AlarmNoticeService alarmNoticeService;

    @GetMapping("/list")
    public ResponseData list(@RequestParam(value = "alarm_level", required = false) Integer alarmLevel,
                             @RequestParam(value = "user_id", required = false) String userId,
                             @RequestParam(value = "type", required = false) String type) {
        List<AlarmNoticeEntity> list = alarmNoticeService.list(alarmLevel, userId, type);
        return success(list);
    }

    /**
     * 告警通知设置
     *
     * @param entity
     * @return
     */
    @PostMapping("/update")
    @OperLog(module = "告警管理", type = OperationType.UPDATE, desc = "修改了告警级别为#{alarmLevelName}的告警配置信息")
    public ResponseData editNotice(@RequestBody AlarmNoticeEntity entity) {
        entity.setUpdateBy(getUsername());
        AlarmNoticeEntity alarmNoticeEntity = alarmNoticeService.editNotice(entity);
        return success(alarmNoticeEntity);
    }


    /**
     * 告警等级时长设置
     *
     * @param alarmLevelTimeDTO
     * @return
     */
    @PostMapping("/update/level")
    @OperLog(module = "告警管理", type = OperationType.UPDATE, desc = "修改了告警级别为#{alarmLevelName}的告警配置信息")
    public ResponseData editLevelTime(@RequestBody AlarmLevelTimeDTO alarmLevelTimeDTO) {
        Boolean result = alarmNoticeService.editLevelTime(alarmLevelTimeDTO, getUsername());
        return success(result);
    }

    /**
     * 获取告警等级时长
     *
     * @return
     */
    @GetMapping("/level/time/list")
    public ResponseData getLevelTime() {
        List<AlarmLevelTimeDTO> dtos = alarmNoticeService.getLevelTime();
        return success(dtos);
    }

    /**
     * 告警周期设置（告警周期、恢复周期）
     *
     * @param alarmCycleDTO
     * @return
     */
    @PostMapping("/update/cycle")
    public ResponseData editAlarmCycle(@RequestBody AlarmCycleDTO alarmCycleDTO) {
        if (alarmCycleDTO == null) {
            return ResponseData.fail();
        }
        Boolean result = alarmNoticeService.editAlarmCycle(alarmCycleDTO, getUsername());
        return success(result);
    }

    /**
     * 通过告警类型告警周期查询
     *
     * @param alarmType
     * @return
     */
    @GetMapping("/select/cycle/{alarmType}")
    public ResponseData getAlarmCycle(@PathVariable String alarmType) {
        if (StringUtils.isBlank(alarmType)) {
            throw new ResponseException(RespCodeEnum.PARAMETER_IS_NULL);
        }
        //判断参数是否传正确
        if (!EnumUtil.isInclude(AlarmTypesEnum.class, alarmType)) {
            throw new ResponseException(RespCodeEnum.ALARM_TYPE_IS_NOT_FOUND);
        } else {
            AlarmCycleDTO result = alarmNoticeService.getAlarmCycle(alarmType);
            return success(result);
        }
    }

    /**
     * 获取智能恢复告警周期
     */
    @GetMapping("/select/recover/smart")
    public ResponseData getSmartRecoverAlarm() {
        AlarmCycleDTO result = alarmNoticeService.getSmartRecoverAlarm();
        return ResponseData.success(result);
    }

    /**
     * 更新智能恢复告警周期
     * @param durationMin 时长（分钟）
     */
    @GetMapping("/update/recover/smart")
    public ResponseData updateSmartRecoverAlarm(@RequestParam Integer durationMin) {
        alarmNoticeService.updateSmartRecoverAlarm(durationMin);
        return ResponseData.success();
    }
}
