package com.yelink.dfs.controller.event;


import com.yelink.dfs.common.BaseController;
import com.yelink.dfs.entity.event.EventClassifyEntity;
import com.yelink.dfs.entity.event.dto.EventDetailTempDTO;
import com.yelink.dfs.service.event.EventClassifyService;
import com.yelink.dfscommon.pojo.ResponseData;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/01/07 19:29
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/classify/event")
public class EventClassifyController extends BaseController {


    private EventClassifyService eventClassifyService;


    /**
     * 获取事件分类中的关键字段
     *
     * @return
     */
    @GetMapping("/type")
    public ResponseData getEventClassifyField() {
        return success(eventClassifyService.getEventClassifyField());
    }


    /**
     * 查询事件分类列表
     *
     * @param pageSize          当前页数
     * @param currentPage       当前页
     * @param eventClassifyName 事件分类
     * @return
     */
    @GetMapping("/list")
    public ResponseData list(@RequestParam(value = "pageSize", required = false) Integer pageSize,
                             @RequestParam(value = "currentPage", required = false) Integer currentPage,
                             @RequestParam(value = "eventClassifyName", required = false) String eventClassifyName) {
        return success(eventClassifyService.listByPage(pageSize, currentPage, eventClassifyName));
    }


    /**
     * 获取事件分类——（pad+web端）
     *
     * @param
     * @return
     */
    @GetMapping("/event/classify")
    public ResponseData getEventClassifyName() {
        return success(eventClassifyService.getEventClassifyName());
    }

    /**
     * 新增事件类型
     *
     * @param entity
     * @return
     */
    @PostMapping("/add")
    public ResponseData addEventClassify(@RequestBody EventClassifyEntity entity) {
        entity.setCreateBy(getUsername());
        entity.setCreateTime(new Date());
        return ResponseData.success(eventClassifyService.addEventClassify(entity));
    }

    /**
     * 获取事件类型详情
     *
     * @param
     * @return
     */
    @GetMapping("/detail/{id}")
    public ResponseData getEventClassifyDetail(@PathVariable("id") Integer id) {
        return ResponseData.success(eventClassifyService.getEventClassifyDetail(id));
    }

    /**
     * 编辑事件类型
     *
     * @param entity
     * @return
     */
    @PutMapping("/update")
    public ResponseData updateEventClassify(@RequestBody EventClassifyEntity entity) {
        entity.setUpdateBy(getUsername());
        entity.setUpdateTime(new Date());
        return success(eventClassifyService.updateEventClassify(entity));
    }

    /**
     * 删除事件类型
     *
     * @return
     */
    @DeleteMapping("/delete/{id}")
    public ResponseData deleteEventClassify(@PathVariable Integer id) {
        return ResponseData.success(eventClassifyService.remove(id));
    }


    /**
     * 获取事件的字段列表(用于pad端新增事件)
     *
     * @param classifyId
     * @return
     */
    @GetMapping("field/list/{classifyId}")
    public ResponseData getEventFieldList(@PathVariable(value = "classifyId") Integer classifyId) {
        List<EventDetailTempDTO> fieldList = eventClassifyService.getEventFieldList(classifyId);
        return success(fieldList);
    }

    /**
     * 通过字段的值判断当前字段的列表(用于pad端新增事件)
     *
     * @param currentEname
     * @return
     */
    @PostMapping("current/list")
    public ResponseData getCurrentFieldList(@RequestParam(value = "currentEname") String currentEname,
                                            @RequestBody List<EventDetailTempDTO> fieldList) {
        List<String> list = eventClassifyService.getCurrentFieldList(currentEname, fieldList);
        return success(list);
    }
}
