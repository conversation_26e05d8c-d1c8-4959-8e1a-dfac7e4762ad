package com.yelink.dfs.controller.barcode;

import com.yelink.dfs.common.BaseController;
import com.yelink.dfs.entity.barcode.dto.RuleTypeDTO;
import com.yelink.dfs.open.v1.barcode.dto.LabelTypeInsertDTO;
import com.yelink.dfs.open.v1.barcode.dto.LabelTypeSelectDTO;
import com.yelink.dfs.open.v1.barcode.dto.LabelTypeUpdateDTO;
import com.yelink.dfs.service.barcode.LabelService;
import com.yelink.dfs.service.barcode.LabelTypeConfigService;
import com.yelink.dfscommon.annotation.OperLog;
import com.yelink.dfscommon.constant.OperationType;
import com.yelink.dfscommon.constant.RespCodeEnum;
import com.yelink.dfscommon.constant.ResponseException;
import com.yelink.dfscommon.constant.dfs.barcode.LabelTypeEnum;
import com.yelink.dfscommon.entity.dfs.DictEntity;
import com.yelink.dfscommon.entity.dfs.barcode.LabelEntity;
import com.yelink.dfscommon.entity.dfs.barcode.LabelSortEntity;
import com.yelink.dfscommon.pojo.ResponseData;
import com.yelink.dfscommon.utils.JacksonUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description 标签规则
 * @Date 2021/05/24
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/bar/code/rule")
public class LabelController extends BaseController {

    private LabelService labelService;
    private LabelTypeConfigService labelTypeConfigService;

    /**
     * 获取规则列表
     *
     * @param current
     * @param size
     * @return
     */
    @GetMapping("/list")
    public ResponseData getList(@RequestParam(value = "current", required = false, defaultValue = "1") Integer current,
                                @RequestParam(value = "size", required = false, defaultValue = "20") Integer size,
                                @RequestParam(value = "codeType", required = false) String codeType,
                                @RequestParam(value = "ruleName", required = false) String ruleName
    ) {
        return success(labelService.getList(current, size, codeType, ruleName));
    }

    /**
     * 新版 新增标签规则(使用打印模板)
     *
     * @param
     * @return
     */
    @OperLog(module = "标签打印", type = OperationType.ADD, desc = "新增了类型为#{codeTypeName},名称为#{ruleName}的标签打印模板")
    @PostMapping("/add/template")
    public ResponseData addTemplate(@RequestBody @Validated({LabelEntity.Insert.class}) LabelEntity entity) {
        entity.setCreateBy(getUsername());
        entity.setCreateTime(new Date());
        labelService.addTemplate(entity);
        entity.setCodeTypeName(LabelTypeEnum.getNameByCode(entity.getCodeType()));
        return success(entity);
    }

    /**
     * 新版 更新标签规则(使用打印模板)
     *
     * @param
     * @return
     */
    @OperLog(module = "标签打印", type = OperationType.UPDATE, desc = "修改了类型为#{codeTypeName},名称为#{ruleName}的标签打印模板")
    @PutMapping("/update/template")
    public ResponseData updateTemplate(@RequestBody LabelEntity entity) {
        if (StringUtils.isAnyBlank(entity.getRuleCode(), entity.getRuleName())) {
            throw new ResponseException(RespCodeEnum.BAR_CODE_RULE_CODE_OR_NAME_IS_NOT_NULL);
        }
        entity.setUpdateBy(getUsername());
        entity.setUpdateTime(new Date());
        labelService.updateById(entity);
        entity.setCodeTypeName(LabelTypeEnum.getNameByCode(entity.getCodeType()));
        return success(entity);
    }

    /**
     * 设置标签模板顺序
     *
     * @param
     * @return
     */
    @PostMapping("/sort")
    public ResponseData sort(@RequestBody List<LabelSortEntity> list) {
        List<LabelEntity> labelEntities = JacksonUtil.convertArray(list, LabelEntity.class);
        labelService.updateBatchById(labelEntities);
        return success();
    }

    /**
     * 设置标签规则为默认规则
     *
     * @param
     * @return
     */
    @GetMapping("/default")
    public ResponseData setDefault(@RequestParam(value = "ruleId") Integer ruleId,
                                   @RequestParam(value = "codeType") String codeType) {
        // 设置该类型下的全为false
        labelService.lambdaUpdate().eq(LabelEntity::getCodeType, codeType).set(LabelEntity::getIsDefault, false).update();
        // 设置类型为默认
        labelService.lambdaUpdate().eq(LabelEntity::getRuleId, ruleId).set(LabelEntity::getIsDefault, true).update();
        return success();
    }

    /**
     * 获取详情
     *
     * @param ruleId
     * @return
     */
    @GetMapping("/detail/{ruleId}")
    public ResponseData getDetail(@PathVariable Integer ruleId) {
        return success(labelService.getById(ruleId));
    }

    /**
     * 查询标签类型列表
     *
     * @return
     */
    @PostMapping("/type")
    public ResponseData getLabelTypeList(@RequestBody LabelTypeSelectDTO selectDTO) {
        return success(labelTypeConfigService.list(selectDTO));
    }

    /**
     * 查询标签类型详情
     *
     * @return
     */
    @GetMapping("/type/detail")
    public ResponseData getLabelTypeDetail(@RequestParam(value = "typeCode") String typeCode) {
        return success(labelTypeConfigService.detail(typeCode));
    }

    /**
     * 查询标签打印小程序的标签类型列表
     *
     * @param
     * @return
     */
    @GetMapping("/print_app/type")
    public ResponseData getPrintAppType() {
        return success(labelTypeConfigService.getPrintAppType());
    }

    /**
     * 更新标签打印小程序的标签类型是否显示
     *
     * @param list 更新对象
     * @return
     */
    @PostMapping("/print_app/type/update")
    public ResponseData updatePrintAppType(@RequestBody List<DictEntity> list) {
        labelTypeConfigService.updatePrintAppType(list);
        return success();
    }

    /**
     * 新增标签类型
     *
     * @param
     * @return
     */
    @PutMapping("/add/type")
    public ResponseData addLabelType(@RequestBody LabelTypeInsertDTO insertDTO) {
        labelTypeConfigService.addLabelType(insertDTO);
        return success();
    }

    /**
     * 编辑标签类型
     *
     * @param
     * @return
     */
    @PutMapping("/update/type")
    public ResponseData updateLabelType(@RequestBody LabelTypeUpdateDTO updateDTO) {
        labelTypeConfigService.updateLabelType(updateDTO);
        return success();
    }

    /**
     * 根据编码规则类型查询所有对应的组成信息
     *
     * @return
     */
    @GetMapping("/info/{codeType}")
    public ResponseData getLabelCodeInfoList(@PathVariable String codeType) {
        return success(labelService.getLabelCodeInfoList(codeType));
    }

    /**
     * 根据编码规则类型查询所有对应的组成信息树
     *
     * @return
     */
    @GetMapping("/info/tree/{codeType}")
    public ResponseData getLabelCodeInfoTree(@PathVariable String codeType) {
        return success(labelService.getLabelCodeInfoTree(codeType));
    }

    /**
     * 物料拓展字段组成信息
     *
     * @return
     */
    @GetMapping("/material/extend")
    public ResponseData getMaterialConfigField() {
        return success(labelService.getMaterialConfigField());
    }

    /**
     * 删除标签规则
     *
     * @return
     */
    @OperLog(module = "标签打印", type = OperationType.DELETE, desc = "删除了类型为#{codeTypeName},名称为#{ruleName}的标签打印模板")
    @DeleteMapping("/delete/{id}")
    public ResponseData deleteInspectionItemGroup(@PathVariable Integer id) {
        LabelEntity entity = labelService.getById(id);
        labelService.lambdaUpdate().eq(LabelEntity::getRuleId, id).remove();
        entity.setCodeTypeName(LabelTypeEnum.getNameByCode(entity.getCodeType()));
        return success(entity);
    }

    /**
     * 根据不同类型，获取对应的标签规则
     * 2.5版本,允许添加多个相同标签类型
     *
     * @param
     * @return
     */
    @GetMapping("/one")
    public ResponseData getBarCodeRuleEntity(@RequestParam(value = "codeType") String codeType) {
        List<LabelEntity> list = labelService.lambdaQuery().eq(LabelEntity::getCodeType, codeType).list();
        if (CollectionUtils.isEmpty(list)) {
            return success();
        }
        return success(list.get(0));
    }

    /**
     * 获取单品打印类型
     *
     * @param
     * @return
     */
    @GetMapping("/single_item/type")
    public ResponseData getSingleItemType() {
        List<RuleTypeDTO> list = labelTypeConfigService.getSingleItemType();
        return success(list);
    }

    /**
     * 获取批次打印类型
     *
     * @param
     * @return
     */
    @GetMapping("/bar_code/type")
    public ResponseData getBarCodeType() {
        List<RuleTypeDTO> list = labelTypeConfigService.getBarCodeType();
        return success(list);
    }

    /**
     * 获取单据打印类型
     *
     * @param
     * @return
     */
    @GetMapping("/order/type")
    public ResponseData getOrderType() {
        List<RuleTypeDTO> list = labelTypeConfigService.getOrderType();
        return success(list);
    }

    /**
     * 获取其他打印类型
     *
     * @param
     * @return
     */
    @GetMapping("/other/type")
    public ResponseData getOtherType() {
        List<RuleTypeDTO> list = labelTypeConfigService.getOtherType();
        return success(list);
    }


}
