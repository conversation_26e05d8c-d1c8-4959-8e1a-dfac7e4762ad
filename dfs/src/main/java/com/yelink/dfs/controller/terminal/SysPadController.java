package com.yelink.dfs.controller.terminal;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yelink.dfs.common.BaseController;
import com.yelink.dfs.constant.OperationType;
import com.yelink.dfs.entity.terminal.SysPadEntity;
import com.yelink.dfs.entity.terminal.dto.SysTerminalDTO;
import com.yelink.dfs.entity.terminal.vo.PadPermissionVO;
import com.yelink.dfs.entity.user.SysPermissionEntity;
import com.yelink.dfs.entity.user.dto.SysPermissionDTO;
import com.yelink.dfs.open.v1.aksk.dto.OpenApiDTO;
import com.yelink.dfs.service.open.OpenApiConfigService;
import com.yelink.dfs.service.terminal.SysPadService;
import com.yelink.dfscommon.annotation.OperLog;
import com.yelink.dfscommon.constant.OpenApiEnum;
import com.yelink.dfscommon.constant.ParamException;
import com.yelink.dfscommon.constant.RespCodeEnum;
import com.yelink.dfscommon.dto.FreeLoginDTO;
import com.yelink.dfscommon.pojo.ResponseData;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 终端管理
 * @Date 2021/12/21
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/pads")
public class SysPadController extends BaseController {
    private static final long serialVersionUID = 1L;

    private SysPadService sysPadService;
    private final OpenApiConfigService openApiConfigService;

    /**
     * 查询Pad权限列表
     *
     * @param name    Pad 名称
     * @param mac     Pad Mac地址
     * @param current
     * @param size
     * @return
     */
    @GetMapping("/terminal/list")
    public ResponseData list(@RequestParam(value = "name", required = false) String name,
                             @RequestParam(value = "mac", required = false) String mac,
                             @RequestParam(value = "current", defaultValue = "1") Integer current,
                             @RequestParam(value = "size", defaultValue = "10") Integer size) {
        Page<PadPermissionVO> list = sysPadService.list(current, size, name, mac, getUsername());
        return success(list);
    }

    /**
     * 通过ID查询Pad
     *
     * @param id Pad ID
     * @return
     */
    @GetMapping("/terminal/detail/{id}")
    public ResponseData selectById(@PathVariable Integer id) {
        if (sysPadService.padIdIsExist(id)) {
            return fail(RespCodeEnum.PAD_NOT_EXIST);
        } else {
            SysPadEntity entity = sysPadService.selectById(id);
            return success(entity);
        }
    }

    /**
     * 批量添加Pad权限 MAC地址不可重复
     *
     * @param entityList
     * @return
     */
    @OperLog(module = "终端权限", type = OperationType.ADD, desc = "新增了Mac为#{mac}的Pad")
    @PostMapping("/terminal/batch/insert")
    public ResponseData add(@RequestBody @Valid List<SysPadEntity> entityList, BindingResult bindingResult) {
        String username = getUsername();
        if (bindingResult.hasErrors()) {
            throw new ParamException(bindingResult);
        }
        sysPadService.insertEntity(entityList, username);
        String collect = entityList.stream().map(SysPadEntity::getMac).collect(Collectors.joining(","));
        return success(SysPadEntity.builder().mac(collect).build());
    }

    /**
     * 获取终端设备列表
     */
    @GetMapping("/terminal/info/list")
    public ResponseData getTerminalInfo() {
        ArrayList<SysTerminalDTO> terminalDTOS = new ArrayList<>();
        List<SysPadEntity> list = sysPadService.list();
        for (SysPadEntity sysPadEntity : list) {
            SysTerminalDTO build = SysTerminalDTO.builder().id(sysPadEntity.getId()).mac(sysPadEntity.getMac()).name(sysPadEntity.getName()).build();
            terminalDTOS.add(build);
        }
        return success(terminalDTOS);
    }

    /**
     * 终端设备权限编辑 ** 废弃方案
     *
     * @param entity
     * @return
     */
    @OperLog(module = "终端权限", type = OperationType.UPDATE, desc = "更新了Mac为#{mac}的Pad")
    @PutMapping("/terminal/update")
    public ResponseData update(@RequestBody SysPadEntity entity) {
        if (sysPadService.padIdIsExist(entity.getId())) {
            return fail(RespCodeEnum.PAD_NOT_EXIST);
        } else {
            sysPadService.updateById(entity, getUsername());
            return success(entity);
        }
    }

    /**
     * 通过Id删除Pad
     *
     * @param padId
     * @return
     */
    @DeleteMapping("/terminal/delete/{padId}")
    @Transactional(rollbackFor = Exception.class)
    @OperLog(module = "终端权限", type = OperationType.DELETE, desc = "删除了#{name}的Pad权限")
    public ResponseData deleteById(@PathVariable Integer padId) {
        if (sysPadService.padIdIsExist(padId)) {
            return fail(RespCodeEnum.PAD_NOT_EXIST);
        } else {
            String username = getUsername();
            return success(sysPadService.deleteByPadId(padId, username));
        }
    }

    /**
     * 获取当前pad权限列表 ** 废弃方案
     *
     * @return
     */
    @GetMapping("/terminal/permission/list/{padId}")
    public ResponseData getPermissionListByPadId(@PathVariable(value = "padId") String padId) {
        List<SysPermissionDTO> list = sysPadService.getPermissionListByPadId(padId);
        return success(list);
    }

    /**
     * 获取所有终端权限列表
     *
     * @return
     */
    @GetMapping("/terminal/all/permission/list")
    public ResponseData getAllPermissionList() {
        List<SysPermissionDTO> list = sysPadService.getAllPermissionList();
        //List<SysPadPermissionVO> list = sysPadService.allList();
        return success(list);
    }

    /**
     * 根据登录用户获取小程序按钮权限
     */
    @GetMapping("/user/button/permissions")
    public ResponseData getPermissionByMac() {
        List<SysPermissionEntity> padPermissionList = sysPadService.getPermissionByUser(getUsername());
        return success(padPermissionList);
    }


    /**
     * 根据小程序一级权限id 获取对应按钮权限
     *
     * @param id 一级权限id
     * @return
     */
    @GetMapping("/app/permission/{id}")
    public ResponseData getAppPermissionById(@PathVariable String id) {
        List<SysPermissionEntity> padPermissionList = sysPadService.getAppPermissionById(id);
        return success(padPermissionList);
    }


    /**
     * 查询Pad列表
     *
     * @return
     */
    @GetMapping("/terminal/all/pad/list")
    public ResponseData selectAllPad() {
        return success(sysPadService.list());
    }

    /**
     * 新增终端设备
     */
    @OperLog(module = "终端权限", type = OperationType.ADD, desc = "新增了Eui为#{mac}的Pad")
    @Transactional(rollbackFor = Exception.class)
    @PostMapping("/terminal/insert")
    public ResponseData addTerminal(@RequestBody @Valid SysPadEntity sysPadEntity, BindingResult bindingResult) {
        if (bindingResult.hasErrors()) {
            throw new ParamException(bindingResult);
        }
        sysPadService.addTerminal(sysPadEntity);
        // 远程调用ECM注册pad，用于是否开启免密登录
        if (syncFreeLogin(sysPadEntity)){
            return success("同步免密登录数据异常");
        }
        return success(sysPadEntity);
    }

    /**
     * 终端设备详情编辑
     *
     * @param entity
     * @return
     */
    @OperLog(module = "终端权限", type = OperationType.UPDATE, desc = "更新了Mac为#{mac}的Pad")
    @Transactional(rollbackFor = Exception.class)
    @PutMapping("/terminal/update/detail")
    public ResponseData updateDetail(@RequestBody SysPadEntity entity) {
        if (sysPadService.padIdIsExist(entity.getId())) {
            return fail(RespCodeEnum.PAD_NOT_EXIST);
        } else {
            sysPadService.updateDetailById(entity, getUsername());
            // 远程调用ECM注册pad，用于是否开启免密登录
            if (syncFreeLogin(entity)) {
                return success("同步免密登录数据异常");
            }
            return success(entity);
        }
    }

    /**
     * 同步精制免密登录
     *
     * @param sysPadEntity
     * @return
     */
    private boolean syncFreeLogin(@Valid @RequestBody SysPadEntity sysPadEntity) {
        try {
            FreeLoginDTO build = FreeLoginDTO.builder().username(sysPadEntity.getMac()).zhName(sysPadEntity.getName()).freeLogin(sysPadEntity.getFreeLogin()).build();
            OpenApiDTO openApiDTO = OpenApiDTO.builder()
                    .sendReqObject(build)
                    .modelCode(OpenApiEnum.JZ_REGISTERED_PAD.getModelCode())
                    .interfaceCode(OpenApiEnum.JZ_REGISTERED_PAD.getInterfaceCode())
                    .build();
            openApiConfigService.callOpenUrl(openApiDTO);
//            yelinkAppsInterface.registeredPad(appsService.getToken(), build);
        } catch (Exception e) {
            log.error("",e);
            return true;
        }
        return false;
    }
}
