package com.yelink.dfs.controller.device;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.asyncexcel.core.model.ExcelTask;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yelink.dfs.common.BaseController;
import com.yelink.dfs.constant.Constant;
import com.yelink.dfs.constant.OperationType;
import com.yelink.dfs.constant.RedisKeyPrefix;
import com.yelink.dfs.constant.device.DeviceAddTypeEnum;
import com.yelink.dfs.constant.device.DevicesStateEnum;
import com.yelink.dfs.constant.device.DevicesUseStateEnum;
import com.yelink.dfs.entity.device.DeviceEntity;
import com.yelink.dfs.entity.device.dto.DeviceGroupTypeDTO;
import com.yelink.dfs.entity.device.dto.DeviceImportExcelDTO;
import com.yelink.dfs.entity.device.dto.DeviceListDTO;
import com.yelink.dfs.entity.device.dto.DeviceOperateLogDTO;
import com.yelink.dfs.entity.device.dto.DeviceSelectDTO;
import com.yelink.dfs.entity.device.dto.DeviceStateVo;
import com.yelink.dfs.entity.device.vo.DeviceExcelVO;
import com.yelink.dfs.service.device.DeviceOperateLogService;
import com.yelink.dfs.service.device.DeviceService;
import com.yelink.dfscommon.annotation.OperLog;
import com.yelink.dfscommon.constant.CommonEnum;
import com.yelink.dfscommon.dto.StateEnumDTO;
import com.yelink.dfscommon.dto.TimePeriodEnergyConsumptionDTO;
import com.yelink.dfscommon.entity.PreviewTemplateEntity;
import com.yelink.dfscommon.entity.dfs.barcode.PrintDTO;
import com.yelink.dfscommon.pojo.ResponseData;
import com.yelink.dfscommon.utils.DateUtil;
import com.yelink.dfscommon.utils.EasyExcelUtil;
import com.yelink.dfscommon.utils.ExcelTemplateImportUtil;
import com.yelink.dfscommon.utils.ExcelUtil;
import com.yelink.dfscommon.utils.JacksonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * <AUTHOR>
 * @description 设备管理
 * @Date 2021/3/3
 */
@Slf4j
@RestController
@RequestMapping("/devices")
public class DeviceController extends BaseController {
    @Resource
    private DeviceService deviceService;
    @Resource

    protected RedisTemplate<String, Object> redisTemplate;
    @Resource
    private DeviceOperateLogService deviceOperateLogService;

    /**
     * 获取设备分类
     *
     * @param
     * @return
     */
    @GetMapping("/type")
    public ResponseData getDeviceType() {
        return success(deviceService.getDeviceType());
    }

    /**
     * 获取设备所有状态
     *
     * @return
     */
    @GetMapping("/states")
    public ResponseData getAllDeviceState() {
        ArrayList<DeviceStateVo> list = new ArrayList<>();
        for (DevicesStateEnum value : DevicesStateEnum.values()) {
            list.add(DeviceStateVo.builder()
                    .code(value.getCode())
                    .name(value.getName())
                    .build());
        }
        return success(list);
    }

    /**
     * 获取设备列表
     *
     * @param dto
     * @return
     */
    @PostMapping("/list")
    public ResponseData list(@RequestBody DeviceSelectDTO dto) {
        return success(deviceService.listByPage(dto));
    }


    /**
     * 获取设备使用状态列表
     *
     * @return
     */
    @GetMapping("/use/status/list")
    public ResponseData getUseStatusList() {
        List<CommonEnum> list = new ArrayList<>();
        DevicesUseStateEnum[] values = DevicesUseStateEnum.values();
        for (DevicesUseStateEnum stateEnum : values) {
            list.add(CommonEnum.builder()
                    .code(stateEnum.getCode())
                    .name(stateEnum.getName())
                    .build());
        }
        return ResponseData.success(list);
    }

    /**
     * 下载导入模板
     */
    @GetMapping("/export/template")
    public void exportTemplateExcel(HttpServletResponse response) throws IOException {
        //找到导入的模板
        byte[] bytes = deviceService.downloadImportTemplate();
        ExcelTemplateImportUtil.responseToClient(response, bytes, "设备数据导入模板" + Constant.XLSX);
    }

    /**
     * 设备导入前分析
     *
     * @param file
     * @return
     */
    @PostMapping("/analysis/import")
    public ResponseData analysisImport(MultipartFile file) {
        PreviewTemplateEntity previewTemplateEntity = deviceService.analysisImport(file);
        return ResponseData.success(previewTemplateEntity);
    }

    /**
     * 导入设备
     *
     * @param importList
     * @return
     */
    @PostMapping("/import")
    public ResponseData deviceImport(@RequestBody List<DeviceImportExcelDTO> importList) {
        Boolean aBoolean = deviceService.importList(importList, getUsername());
        if (Boolean.TRUE.equals(aBoolean)) {
            return ResponseData.success();
        }
        return ResponseData.fail();
    }

    /**
     * 设备导出  :异步
     *
     * @param dto
     * @return
     * @throws IOException
     */
    @PostMapping("/syc/exports")
    public ResponseData export(@RequestBody DeviceSelectDTO dto) {
        Long result = deviceService.exportTask(dto, getUsername());
        return success(result);
    }

    /**
     * 分页查询当前导出任务列表
     *
     * @param size
     * @param current
     * @return
     */
    @GetMapping("/export/task/page")
    public ResponseData taskPage(@RequestParam(value = "pageSize", required = false, defaultValue = "20") Integer size,
                                 @RequestParam(value = "currentPage", required = false, defaultValue = "1") Integer current) {
        IPage<ExcelTask> iPage = deviceService.taskPage(current, size);
        return success(iPage);
    }


    /**
     * 查询 导出进度
     *
     * @return
     */
    @GetMapping("/export/task/{taskId}")
    public ResponseData exportExcel(@PathVariable Long taskId) {
        ExcelTask excelTask = deviceService.taskById(taskId);
        return success(excelTask);
    }

    // 新的导入导出

    @GetMapping("/excel/import-template")
    public void downloadTemplate(HttpServletResponse response) throws IOException {
        //找到导出的模板
        byte[] bytes = ExcelUtil.exportDefaultExcel("classpath:template/deviceTemplate2.xlsx");
        ExcelTemplateImportUtil.responseToClient(response, bytes, "生产设备导入模板" + Constant.XLSX);
    }
    @PostMapping("/excel/import")
    public ResponseData excelImport(MultipartFile file) {
        deviceService.excelImport(file);
        return success();
    }
    @GetMapping("/excel/import-progress")
    public ResponseData getImportExcelProgress() {
        Object o = redisTemplate.opsForValue().get(RedisKeyPrefix.DEVICE_IMPORT_PROGRESS);
        if (o != null) {
            return success(o);
        }
        return fail();
    }
    @PostMapping("/excel/export")
    public void export(@RequestBody DeviceSelectDTO dto, HttpServletResponse response) throws IOException  {
        dto.setCurrentPage(null);
        List<DeviceEntity> records = deviceService.listByPage(dto).getRecords();
        List<DeviceExcelVO> exportList = records.stream().map(DeviceExcelVO::convertToExportDTO).collect(Collectors.toList());
        EasyExcelUtil.export(response, "DeviceFile", "DeviceSheet", exportList, DeviceExcelVO.class);
    }

    /**
     * 设备默认导出模板 - 下载默认模板
     * 查询最新的10条工单数据导出数据源excel
     */
    @GetMapping("/export/default/template")
    public void exportExcelDefaultTemplate(HttpServletResponse response) throws Exception {
        List<DeviceEntity> records = deviceService.listByPage(DeviceSelectDTO.builder().pageSize(10).currentPage(1).build()).getRecords();
        List<DeviceExcelVO> exportList = records.stream().map(DeviceExcelVO::convertToExportDTO).collect(Collectors.toList());
        EasyExcelUtil.export(response, "设备台账默认导出模板", "数据源", exportList, DeviceExcelVO.class);
    }

    /**
     * 生产工单列表导出 - 自定义模板上传
     * 接收文件并清空名称为数据源sheet的内容
     *
     * @param file
     * @return
     */
    @PostMapping("/upload/export/diy/template")
    public ResponseData uploadListExportTemplate(MultipartFile file) throws IOException {
        deviceService.uploadListExportTemplate(file, getUsername());
        return success();
    }

    /**
     * 获取全部的设备列表（不做分页和查询条件）
     */
    @GetMapping("/all/list")
    public ResponseData list(DeviceListDTO dto) {
        List<DeviceEntity> result = deviceService.lambdaQuery()
                .like(StringUtils.isNotEmpty(dto.getDeviceCode()), DeviceEntity::getDeviceCode, dto.getDeviceCode())
                .like(StringUtils.isNotEmpty(dto.getDeviceName()), DeviceEntity::getDeviceName, dto.getDeviceName())
                .list();
        return success(result);
    }

    /**
     * 按设备类型分组 获取设备列表
     */
    @GetMapping("/all/listGroupByType")
    public ResponseData listGroupByType() {
        List<DeviceGroupTypeDTO> deviceGroupTypeDTOS = deviceService.listGroupByType();
        return success(deviceGroupTypeDTOS);
    }

    /**
     * 分页获取设备列表 for 设备弹窗
     *
     * @param size
     * @param current
     * @param deviceModelId 设备分类id 多个逗号分割
     * @param deviceCode    设备编号
     * @param typeCode      设备类型code 多个逗号分割
     * @param deviceName    设备名称
     * @return
     */
    @GetMapping("/popover/list")
    public ResponseData listForPopover(@RequestParam(value = "deviceName", required = false) String deviceName,
                                       @RequestParam(value = "deviceCode", required = false) String deviceCode,
                                       @RequestParam(value = "typeCode", required = false) String typeCode,
                                       @RequestParam(value = "deviceModelId", required = false) String deviceModelId,
                                       @RequestParam(defaultValue = "1", value = "current") int current,
                                       @RequestParam(defaultValue = "10", value = "size") int size) {
        Page<DeviceEntity> page = deviceService.listForPopover(size, current, deviceModelId, deviceCode, typeCode, deviceName);
        return success(page);
    }


    /**
     * 添加设备
     *
     * @param deviceEntity 设备实体
     * @return
     * @throws Exception
     */
    @PostMapping("/insert")
    @OperLog(module = "设备管理", type = OperationType.ADD, desc = "新增了设备编号为#{deviceCode}的生产设备")
    public ResponseData add(@RequestBody DeviceEntity deviceEntity) {
        deviceEntity.setCreateBy(getUsername());
        deviceEntity.setCreateTime(new Date());
        deviceEntity.setUpdateBy(getUsername());
        deviceEntity.setUpdateTime(new Date());
        deviceEntity.setStateUpdateTime(new Date());
        deviceEntity.setState(DevicesStateEnum.RUNNING.getCode());
        deviceService.saveEntity(deviceEntity);
        deviceService.bindSensor(deviceEntity.getEuis(), deviceEntity.getDeviceId());
        return success(deviceEntity);
    }

    /**
     * 修改设备
     *
     * @param deviceEntity
     * @return
     * @throws Exception
     */
    @PutMapping("/update")
    @OperLog(module = "设备管理", type = OperationType.UPDATE, desc = "修改了设备编号为#{deviceCode}的生产设备")
    public ResponseData update(@RequestBody DeviceEntity deviceEntity) {
        deviceEntity.setUpdateBy(getUsername());
        deviceEntity.setUpdateTime(DateUtil.formatToDate(new Date(), DateUtil.DATETIME_FORMAT));
        deviceService.updateEntity(deviceEntity);
        return success(deviceEntity);
    }

    /**
     * 删除设备
     *
     * @param id 设备id
     * @return
     */
    @DeleteMapping("/delete/{id}")
    @OperLog(module = "设备管理", type = OperationType.DELETE, desc = "删除了设备编号为#{deviceCode}，名称为#{deviceName}的生产设备")
    public ResponseData remove(@PathVariable(value = "id") Integer id) {
        DeviceEntity entity = deviceService.getById(id);
        deviceService.delete(id);
        return success(entity);
    }

    /**
     * 获取详细设备信息
     *
     * @param id 设备id
     * @return
     */
    @GetMapping("/detail/{id}")
    public ResponseData getDetailById(@PathVariable(value = "id") Integer id) {
        DeviceEntity entity = deviceService.getEntityById(id);
        return success(entity);
    }

    /**
     * 获取车间下设备信息
     *
     * @param gid     车间id
     * @param current 当前页
     * @param size    当前页数
     * @return
     */
    @GetMapping("/list/grid")
    public ResponseData getDeviceListByGid(@RequestParam(value = "gid") String gid,
                                           @RequestParam(value = "current", defaultValue = "1") Integer current,
                                           @RequestParam(value = "size", defaultValue = "10") Integer size) {
        Page<DeviceEntity> list = deviceService.deviceByGid(gid, current, size);
        return success(list);
    }

    /**
     * 获取工位下设备信息(根据负责人)
     * 数据隔离：只有设备的负责人、设备所在的工位负责人、工位的员工能够看到
     *
     * @param instanceId 实例id（车间实例，产线实例）
     * @return
     */
    @GetMapping("/list/line/mag")
    public ResponseData getDeviceByLineIdAndUsername(@RequestParam(value = "instanceId") Integer instanceId,
                                                     @RequestParam(value = "type") String type) {
        List<DeviceEntity> list = deviceService.deviceByLineIdAndUsername(instanceId, getUsername(), type);
        return success(list);
    }


    /**
     * 获取工位下设备信息
     *
     * @param productionLineId 工位id
     * @return
     */
    @GetMapping("/list/line")
    public ResponseData getDeviceListByLineId(@RequestParam(value = "productionLineId") Integer productionLineId) {
        List<DeviceEntity> list = deviceService.deviceByLineId(productionLineId);
        return success(list);
    }

    /**
     * 查询该工位关联的所有设备
     */
    @GetMapping("/list/device")
    public ResponseData listDeviceByFid(@RequestParam Integer fid) {
        List<DeviceEntity> list = deviceService.lambdaQuery()
                .eq(DeviceEntity::getFid, fid)
                .list();
        return success(list);
    }

    /**
     * 绑定传感器
     *
     * @param euis     绑定的传感器eui
     * @param deviceId 设备id
     * @return
     */
    @GetMapping("/bind/sensor")
    public ResponseData bingSensor(@RequestParam(value = "euis", required = false) String euis,
                                   @RequestParam(value = "deviceId") Integer deviceId) {
        deviceService.bindSensor(euis, deviceId);
        return success();
    }

    /**
     * 获取传感器列表
     *
     * @param
     * @return
     */
    @GetMapping("/sensor/list")
    public ResponseData getSensorList() {
        return success(deviceService.getSensorList());
    }

    /**
     * 设备大屏
     *
     * @param
     * @return
     */
    @GetMapping("/screen")
    public ResponseData screen() {
        return success(deviceService.screen());
    }

    /**
     * 设备指标曲线
     *
     * @param deviceId
     * @return
     */
    @GetMapping("/curve")
    public ResponseData target(@RequestParam(value = "deviceId") Integer deviceId,
                               @RequestParam(value = "start", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date start,
                               @RequestParam(value = "end", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date end) {
        JSONObject curve = deviceService.curve(deviceId, start, end);
        return success(curve);
    }

    /**
     * 获取灯检设备-指标TV1~TV9-时间近15个时间点的数据
     *
     * @param
     * @return
     */
    @GetMapping("/lamp/target/tv/time")
    public ResponseData getLampTvOneToTvNineDataAboutTime(@RequestParam(value = "deviceId") Integer deviceId) {
        return success(deviceService.getLampTvOneToTvNineDataAboutTime(deviceId));
    }

    /**
     * 获取灯检设备-指标TV1~TV9-批次近5条数据
     *
     * @param
     * @return
     */
    @GetMapping("/lamp/target/tv/work/order")
    public ResponseData getLampTvOneToTvNineDataAboutWorkOrder(@RequestParam(value = "deviceId") Integer deviceId) {
        return success(deviceService.getLampTvOneToTvNineDataAboutWorkOrder(deviceId));
    }

    /**
     * 手动上报设备运行/停止
     *
     * @param
     * @return
     */
    @PutMapping("/update/record")
    public ResponseData updateRunRecordTableByReport(@RequestParam(value = "deviceId") Integer deviceId,
                                                     @RequestParam(value = "addType") String addType) {
        Integer state = DeviceAddTypeEnum.getStateByType(addType);
        String username = getUsername();
        deviceService.updateRunRecordTableByReport(deviceId, state, username);
        deviceOperateLogService.insertOperateLog(
                DeviceOperateLogDTO
                        .builder()
                        .appName("设备综合管理")
                        .pageName("参数监控")
                        .operateTarget("运行状态切换")
                        .operateContent(DeviceAddTypeEnum.getNameByType(addType))
                        .deviceId(deviceId)
                        .build()
        );
        return success();
    }

    /**
     * 获取设备运行记录的开始/停止时间列表
     *
     * @param
     * @return
     */
    @GetMapping("/record/time/list")
    public ResponseData getRunRecordTimeByDeviceId(@RequestParam(value = "deviceId") Integer deviceId) {
        return success(deviceService.getRunRecordTimeByDeviceId(deviceId));
    }

    /**
     * 根据modelId拿到设备
     *
     * @param modelId
     * @return
     */
    @GetMapping("/list/model/{modelId}")
    public ResponseData getByModelId(@PathVariable(value = "modelId") Integer modelId) {
        LambdaQueryWrapper<DeviceEntity> deviceWrapper = new LambdaQueryWrapper<>();
        deviceWrapper.eq(DeviceEntity::getModelId, modelId);
        List<DeviceEntity> sterilizationList = deviceService.list(deviceWrapper);
        JSONArray jsonArray = JSONArray.parseArray(JSONObject.toJSONString(sterilizationList));
        return ResponseData.success(jsonArray);
    }

    /**
     * 根据制造单元模型查询设备列表
     *
     * @param workCenterId 工作中心ID
     * @param deviceName   设备名称
     * @return
     */
    @GetMapping("/list/by/model")
    public ResponseData getListByModel(@RequestParam(value = "workCenterId") Integer workCenterId,
                                       @RequestParam(value = "deviceName", required = false) String deviceName) {
        List<DeviceEntity> deviceEntities = deviceService.getListByLineModelId(workCenterId, deviceName);

        return ResponseData.success(deviceEntities);
    }

    /**
     * 根据工作中心查询设备列表
     *
     * @param workCenterId 工作中心ID
     * @param deviceName   设备名称
     * @return
     */
    @GetMapping("/list/by/team")
    public ResponseData getListByWorkCenter(@RequestParam(value = "workCenterId") Integer workCenterId,
                                            @RequestParam(value = "deviceName", required = false) String deviceName) {
        List<DeviceEntity> deviceEntities = deviceService.getListByWorkCenter(workCenterId, deviceName);

        return ResponseData.success(deviceEntities);
    }

    /**
     * 获取设备状态
     *
     * @return
     */
    @GetMapping("/status/list")
    public ResponseData getComparatorList() {
        List<StateEnumDTO> list = deviceService.getDeviceStatusList();
        return success(list);
    }

    /**
     * 生产作业小程序：
     * 获取生产设备对应的产线，产线对应的作业工单信息（生效、投产、挂起、今日完成，按照计划开始时间的先后顺序）
     *
     * @param deviceCode 设备编码
     * @param state      状态
     * @return 作业工单信息
     */
    @GetMapping("/get/operation/order/list")
    public ResponseData getOperationOrderList(@RequestParam(value = "deviceCode") String deviceCode,
                                              @RequestParam(value = "state", required = false) String state,
                                              @RequestParam(value = "type", required = false) String type) {
        return success(deviceService.getOperationOrderList(deviceCode, state));
    }

    /**
     * 用气能耗列表
     *
     * @param dto
     * @return
     */
    @PostMapping("/gas/consumption")
    public ResponseData getGasConsumption(@RequestBody TimePeriodEnergyConsumptionDTO dto) {
        return success(deviceService.getGasConsumption(dto));
    }

    /**
     * 通过设备id获取设备参数列表
     *
     * @param deviceId
     * @return
     */
    @GetMapping("/parameter/list/{deviceId}")
    public ResponseData getParameterListByDeviceId(@PathVariable Integer deviceId) {
        return success(deviceService.getParameterListByDeviceId(deviceId));
    }

    /**
     * 设备运行状态总览
     *
     * @return
     */
    @GetMapping("/status/overview")
    public ResponseData getDeviceStatusOverview() {
        return success(deviceService.getDeviceStatusOverview());
    }

    /**
     * 分页获取设备运行状态
     *
     * @param dto
     * @return
     */
    @PostMapping("/running/status/list")
    public ResponseData getDeviceRunningStatus(@RequestBody DeviceSelectDTO dto) {
        Page<DeviceEntity> page = deviceService.getRecordDeviceDayRunList(dto);
        return success(page);
    }

    /**
     * 设备标签打印
     *
     * @param selectDTO 查询条件
     * @return
     */
    @OperLog(module = "设备标签", type = OperationType.PRINT, desc = "打印了编码为#{printCodes}的设备")
    @PostMapping("/print")
    public ResponseData print(@RequestBody DeviceSelectDTO selectDTO) {
        log.info("{}调用了打印接口：{}", getUsername(), JacksonUtil.toJSONString(selectDTO));
        PrintDTO print = deviceService.print(selectDTO);
        return success(print);
    }
}
