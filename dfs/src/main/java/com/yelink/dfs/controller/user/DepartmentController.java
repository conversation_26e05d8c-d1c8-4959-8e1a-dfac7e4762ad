package com.yelink.dfs.controller.user;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yelink.dfs.common.BaseController;
import com.yelink.dfs.constant.OperationType;
import com.yelink.dfs.entity.user.DepartmentEntity;
import com.yelink.dfs.entity.user.DepartmentUserEntity;
import com.yelink.dfs.entity.user.dto.DepartmentExportDTO;
import com.yelink.dfs.open.v1.user.dto.DepartmentSelectDTO;
import com.yelink.dfs.service.user.DepartmentService;
import com.yelink.dfscommon.annotation.OperLog;
import com.yelink.dfscommon.constant.ParamException;
import com.yelink.dfscommon.pojo.ResponseData;
import com.yelink.dfscommon.utils.EasyExcelUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 部门表
 * @Date 2021/5/7
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/departments")
public class DepartmentController extends BaseController {

    private DepartmentService departmentService;

    /**
     * 获取部门树
     *
     * @return List<DepartmentEntity>
     */
    @GetMapping("/tree")
    public ResponseData getDepartmentTree() {
        List<DepartmentEntity> list = departmentService.getDepartmentTree();
        return success(list);
    }

    /**
     * 新增部门
     *
     * @param departmentEntity 部门
     * @return OrderEntity
     */
    @PostMapping("/insert")
    @OperLog(module = "部门管理", type = OperationType.ADD, desc = "新增了部门为#{departmentName}的部门")
    public ResponseData addDepartment(@RequestBody DepartmentEntity departmentEntity, BindingResult bindingResult) {
        if (bindingResult.hasErrors()) {
            throw new ParamException(bindingResult);
        }
        String username = getUsername();
        departmentEntity.setCreateBy(username);
        departmentEntity.setCreateTime(new Date());
        departmentEntity.setUpdateTime(new Date());
        departmentService.saveEntity(departmentEntity);
        return ResponseData.success();
    }

    /**
     * 删除部门
     *
     * @param id 部门id
     */
    @DeleteMapping("/delete/{id}")
    @OperLog(module = "部门管理", type = OperationType.DELETE, desc = "删除了部门为#{departmentName}的部门")
    public ResponseData deleteDepartment(@PathVariable(value = "id") Integer id) {
        DepartmentEntity entity = departmentService.getById(id);
        departmentService.removeEntityById(id, true);
        return success(entity);
    }

    /**
     * 更新部门
     *
     * @param departmentEntity 部门
     * @return DepartmentEntity
     */
    @PutMapping("/update")
    @OperLog(module = "部门管理", type = OperationType.UPDATE, desc = "修改了部门为#{departmentName}的部门")
    public ResponseData updateDepartment(@RequestBody DepartmentEntity departmentEntity) {
        departmentService.updateDepartment(departmentEntity, getUsername());
        return success();
    }

    /**
     * 部门详情
     *
     * @return DepartmentEntity
     */
    @PutMapping("/detail")
    public ResponseData detail(@PathVariable(value = "id") Integer id) {
        DepartmentEntity departmentEntity = departmentService.getById(id);
        return success(departmentEntity);
    }


    /**
     * 获取属于该部门的成员
     *
     * @param id 部门id
     * @return 用户列表
     */
    @GetMapping("/members/{id}")
    public ResponseData selectMemberByDepartmentId(@PathVariable("id") Integer id) {
        List<DepartmentUserEntity> departmentUserEntities = departmentService.selectMemberByDepartmentId(id);
        return success(departmentUserEntities);
    }

    /**
     * 获取用户所在的部门列表
     *
     * @param username 用户编码
     * @return 部门列表
     */
    @GetMapping("/list")
    public ResponseData selectMemberByDepartmentId(@RequestParam(value = "username") String username) {
        List<DepartmentEntity> departmentUserEntities = departmentService.selectList(username);
        return success(departmentUserEntities);
    }

    /**
     * 部门信息导出
     *
     * @param selectDTO 查询条件
     * @param response HTTP响应
     * @throws IOException IO异常
     */
    @PostMapping("/export")
    public void exportDepartmentList(@RequestBody DepartmentSelectDTO selectDTO,
                                     HttpServletResponse response) throws IOException {
        selectDTO.setCurrent(1);
        selectDTO.setSize(Integer.MAX_VALUE);
        Page<DepartmentEntity> page = departmentService.getList(selectDTO);
        List<DepartmentEntity> records = page.getRecords();
        List<DepartmentExportDTO> exports = records.stream().map(DepartmentExportDTO::convertToExport).collect(Collectors.toList());

        EasyExcelUtil.export(response, "部门数据导出", "部门", exports, DepartmentExportDTO.class);
    }

}
