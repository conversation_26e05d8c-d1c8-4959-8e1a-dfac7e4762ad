package com.yelink.dfs.controller.weight;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yelink.dfs.common.BaseController;
import com.yelink.dfs.controller.weight.dto.WeightTargetPageDto;
import com.yelink.dfs.entity.weight.WeightTargetExtendEntity;
import com.yelink.dfs.service.weight.WeightTargetService;
import com.yelink.dfscommon.pojo.ResponseData;
import com.yelink.dfscommon.utils.EasyExcelUtil;
import com.yelink.dfscommon.utils.MathUtil;
import lombok.AllArgsConstructor;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * @description: 称重统计
 * @author: shuang
 * @time: 2022/7/14
 */
@AllArgsConstructor
@RestController
@RequestMapping("/weight/targets")
public class WeightTargetController extends BaseController {

    private final WeightTargetService weightTargetService;


    /**
     * 条件分页查询上料记录统计以及关联的工单信息
     *
     * @param weightTargetPageDto
     * @return
     */
    @GetMapping("/page")
    public ResponseData page(@ModelAttribute WeightTargetPageDto weightTargetPageDto) {
        Assert.notNull(weightTargetPageDto.getCurrent(), "current不能为null");
        Assert.notNull(weightTargetPageDto.getSize(), "size不能为null");
        Page<WeightTargetExtendEntity> page = weightTargetService.pageBy(weightTargetPageDto);
        return ResponseData.success(page);
    }

    /**
     * 按条件导出上料记录统计以及关联的工单信息
     */
    @GetMapping("/export")
    public void export(@ModelAttribute WeightTargetPageDto weightTargetPageDto, HttpServletResponse response) throws IOException {
        weightTargetPageDto.setCurrent(1);
        weightTargetPageDto.setSize(Integer.MAX_VALUE);
        List<WeightTargetExtendEntity> records = weightTargetService.pageBy(weightTargetPageDto).getRecords();
        // 因为导出的是 Xxx率(%),所以结果需要*100
        records.forEach(record -> {
            record.setTheoryOutputRate(MathUtil.mul(record.getTheoryOutputRate(), 100));
            record.setActualOutputRate(MathUtil.mul(record.getActualOutputRate(), 100));
            record.setOutputComplianceRate(MathUtil.mul(record.getOutputComplianceRate(), 100));
            record.setTheoryWasteRate(MathUtil.mul(record.getTheoryWasteRate(), 100));
            record.setActualWasteRate(MathUtil.mul(record.getActualWasteRate(), 100));
        });
        EasyExcelUtil.export(response, "weightTarget", "称重统计", records, WeightTargetExtendEntity.class);
    }

    /**
     * 查询这个工单的上料记录统计和工单信息
     */
    @GetMapping("/workOrder/detail")
    public ResponseData workOrderDetail(@RequestParam String workOrderCode) {
        WeightTargetExtendEntity weightTargetExtendEntity = weightTargetService.workOrderTargetDetail(workOrderCode);
        return ResponseData.success(weightTargetExtendEntity);
    }
}
