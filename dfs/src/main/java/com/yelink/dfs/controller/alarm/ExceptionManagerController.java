package com.yelink.dfs.controller.alarm;

import cn.hutool.core.collection.CollUtil;
import com.yelink.dfs.constant.alarm.ExceptionManagerEnum;
import com.yelink.dfs.entity.alarm.dto.ExceptionManagerListDTO;
import com.yelink.dfs.entity.alarm.dto.ExceptionManagerDetailVO;
import com.yelink.dfs.entity.order.WorkOrderEntity;
import com.yelink.dfs.service.alarm.AlarmService;
import com.yelink.dfs.service.event.EventRecordService;
import com.yelink.dfs.service.order.WorkOrderService;
import com.yelink.dfscommon.pojo.ResponseData;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/exception-manager")
public class ExceptionManagerController {

    @Resource
    private AlarmService alarmService;
    @Resource
    private EventRecordService eventService;
    @Resource
    private WorkOrderService workOrderService;

    @GetMapping("/pad/list")
    public ResponseData listByExceptionManager(ExceptionManagerListDTO dto) {
        // 先查订单
        if(!StringUtils.isEmpty(dto.getProductOrderNumber())) {
            List<WorkOrderEntity> workOrders = workOrderService.lambdaQuery().eq(WorkOrderEntity::getProductOrderNumber, dto.getProductOrderNumber()).list();
            if(!CollUtil.isEmpty(workOrders)) {
                dto.setWorkOrderNumberList(workOrders.stream().map(WorkOrderEntity::getWorkOrderNumber).collect(Collectors.toList()));
            }else {
                return ResponseData.success(Collections.EMPTY_LIST);
            }
        }
        List<ExceptionManagerDetailVO> alarms = alarmService.listByExceptionManager(dto);
        List<ExceptionManagerDetailVO> events = eventService.listByExceptionManager(dto);
        List<ExceptionManagerDetailVO> result = Stream.of(alarms, events).flatMap(Collection::stream).sorted(Comparator.comparing(
                ExceptionManagerDetailVO::getReportTime,
                // null放最后， 剩下的倒叙
                Comparator.nullsLast(Comparator.reverseOrder())
        )).collect(Collectors.toList());
        return ResponseData.success(result);
    }

    @GetMapping("/pad/detail")
    public ResponseData detailByExceptionManager(@RequestParam Integer id, @RequestParam String type) {
        ExceptionManagerEnum typeEnum = ExceptionManagerEnum.getCodeByName(type);
        ExceptionManagerDetailVO vo = null;
        switch (typeEnum) {
            case ALARM:
                vo = alarmService.exceptionDetail(id);
                break;
            case EVENT:
                vo = eventService.exceptionDetail(id);
                break;
            default:
        }
        return ResponseData.success(vo);
    }
}
