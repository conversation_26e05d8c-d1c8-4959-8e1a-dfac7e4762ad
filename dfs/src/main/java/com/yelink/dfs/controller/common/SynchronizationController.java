package com.yelink.dfs.controller.common;

import com.yelink.dfs.common.BaseController;
import com.yelink.dfs.entity.common.SynchronizationVo;
import com.yelink.dfs.service.common.SynchronizationService;
import com.yelink.dfscommon.pojo.ResponseData;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @Date 2021/9/6 10:55
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/sync")
public class SynchronizationController extends BaseController {
    private SynchronizationService service;

    /**
     * 加锁
     *
     * @param entity
     * @return
     */
    @PostMapping("/add/lock")
    public ResponseData addLock(@RequestBody SynchronizationVo entity) {
        String username = getUsername();
        return success((Object) service.addLock(entity, username));
    }


    /**
     * 释放锁
     */

    @PostMapping("/release/lock")
    public ResponseData releaseLock(@RequestBody SynchronizationVo entity) {
        boolean result = service.releaseLock(entity);
        return ResponseData.success(result);
    }


}
