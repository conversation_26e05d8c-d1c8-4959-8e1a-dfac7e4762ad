package com.yelink.dfs.controller.stock;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yelink.dfs.common.BaseController;
import com.yelink.dfs.common.CodeFactory;
import com.yelink.dfs.constant.Constant;
import com.yelink.dfs.constant.OperationType;
import com.yelink.dfs.constant.stock.StockDeliveryStateEnum;
import com.yelink.dfs.entity.product.MaterialEntity;
import com.yelink.dfs.entity.stock.StockDeliveryEntity;
import com.yelink.dfs.entity.stock.StockDeliveryMaterialEntity;
import com.yelink.dfs.entity.stock.dto.StockDeliveryDTO;
import com.yelink.dfs.service.impl.common.WorkPropertise;
import com.yelink.dfs.service.product.MaterialService;
import com.yelink.dfs.service.stock.StockDeliveryService;
import com.yelink.dfscommon.annotation.OperLog;
import com.yelink.dfscommon.api.wms.StockInAndOutInterface;
import com.yelink.dfscommon.constant.RespCodeEnum;
import com.yelink.dfscommon.constant.ResponseException;
import com.yelink.dfscommon.constant.wms.StockInputOrOutputTypeEnum;
import com.yelink.dfscommon.constant.wms.StockOrderStateEnum;
import com.yelink.dfscommon.dto.ApproveBatchDTO;
import com.yelink.dfscommon.dto.wms.StockInAndOutSelectDTO;
import com.yelink.dfscommon.entity.CommonType;
import com.yelink.dfscommon.entity.common.BatchChangeStateDTO;
import com.yelink.dfscommon.entity.wms.StockInAndOutEntity;
import com.yelink.dfscommon.entity.wms.StockMaterialDetailEntity;
import com.yelink.dfscommon.pojo.ResponseData;
import com.yelink.dfscommon.utils.JacksonUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/5/7 17:52
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/stocks/delivery")
public class StockDeliveryController extends BaseController {

    private StockInAndOutInterface stockInAndOutInterface;
    private StockDeliveryService deliveryService;
    private CodeFactory codeFactory;
    private WorkPropertise workPropertise;
    private MaterialService materialService;

    /**
     * 查询所有发货记录
     *
     * @param stockDeliveryDTO
     * @return
     */
    @PostMapping("/list")
    public ResponseData getList(@RequestBody StockDeliveryDTO stockDeliveryDTO) {
        Page<StockDeliveryEntity> list = deliveryService.getList(stockDeliveryDTO);
        return success(list);
    }

    /**
     * 创建发货单
     *
     * @return
     */
    @PostMapping("/create")
    @OperLog(module = "库房管理", type = OperationType.ADD, desc = "创建了单号为#{sendNumber}的发货单")
    public ResponseData createSendOrder(@RequestBody StockDeliveryEntity entity) {
        entity.setCreateBy(getUsername());
        entity.setCreateTime(new Date());
        //生成出货单号
        String orderNumber = StringUtils.isNotEmpty(entity.getSendNumber()) ? entity.getSendNumber() : codeFactory.getOrderNumber(workPropertise.getStockDeliveryHeader());
        checkNumberBeforeSet(orderNumber);
        entity.setSendNumber(orderNumber);
        deliveryService.createSendOrder(entity);
        return success(entity);
    }

    /**
     * 创建生效的发货单
     *
     * @return
     */
    @PostMapping("/create/released")
    @OperLog(module = "库房管理", type = OperationType.ADD, desc = "创建了单号为#{sendNumber}的发货单")
    public ResponseData createReleasedSendOrder(@RequestBody StockDeliveryEntity entity) {
        //生成出货单号
        String orderNumber = StringUtils.isNotEmpty(entity.getSendNumber()) ? entity.getSendNumber() : codeFactory.getOrderNumber(workPropertise.getStockDeliveryHeader());
        checkNumberBeforeSet(orderNumber);
        entity.setSendNumber(orderNumber);
        String username = getUsername();
        entity.setCreateBy(username);
        entity.setUpdateBy(username);
        entity.setCreateTime(new Date());
        entity.setUpdateTime(new Date());
        deliveryService.createReleasedSendOrder(entity);
        return success(entity);
    }

    /**
     * 校验生成的单号是否重复
     *
     * @param orderNumber
     */
    private void checkNumberBeforeSet(String orderNumber) {
        if (deliveryService.lambdaQuery().eq(StockDeliveryEntity::getSendNumber, orderNumber).exists()) {
            throw new ResponseException(RespCodeEnum.STOCK_ORDER_NUMBER_EXIST);
        }
    }

    /**
     * 修改发货单
     *
     * @param entity
     * @return
     */
    @OperLog(module = "库房管理", type = OperationType.UPDATE, desc = "修改了单号为#{sendNumber}的发货单")
    @PutMapping("/update")
    public ResponseData updateSendOrder(@RequestBody StockDeliveryEntity entity) {
        entity.setUpdateBy(getUsername());
        entity.setUpdateTime(new Date());
        deliveryService.checkStateBeforeUpdate(entity);
        return success(entity);
    }

    /**
     * 删除发货单
     *
     * @param deliveryId
     * @return
     */
    @OperLog(module = "库房管理", type = OperationType.ADD, desc = "删除了ID为#{deliveryId}的调拨记录")
    @DeleteMapping("/delete/{deliveryId}")
    public ResponseData deleteSendOrder(@PathVariable Integer deliveryId) {
        StockDeliveryEntity entity = deliveryService.deleteSendOrder(deliveryId);
        return success(entity);
    }

    /**
     * 查询发货单详情
     *
     * @param deliveryId
     * @return
     */
    @GetMapping("/detail/{deliveryId}")
    public ResponseData getSendOrderDetail(@PathVariable Integer deliveryId) {
        StockDeliveryEntity entity = deliveryService.getSendOrderDetail(deliveryId);
        return success(entity);
    }

    /**
     * 获取发货单状态
     *
     * @return
     */
    @GetMapping("/state")
    public ResponseData getDeliveryState() {
        List<CommonType> list = new ArrayList<>();
        for (StockDeliveryStateEnum value : StockDeliveryStateEnum.values()) {
            list.add(CommonType.builder()
                    .code(value.getCode())
                    .name(value.getName())
                    .build());
        }
        return success(list);
    }

    /**
     * 获取发放,完成状态下的交付出库单
     *
     * @return
     */
    @GetMapping("/output/order")
    public ResponseData getOutputOrder() {
        List<StockInAndOutEntity> outputOrder = JacksonUtil.getResponseArray(stockInAndOutInterface.getOutputList(
                StockInAndOutSelectDTO
                        .builder()
                        .states(StockOrderStateEnum.RELEASED.getCode() + Constant.SEP + StockOrderStateEnum.FINISHED.getCode())
                        .inOrOutType(StockInputOrOutputTypeEnum.OUTPUT_DELIVERY.getTypeCode())
                        .build(), getUsername()), StockInAndOutEntity.class);
        List<String> list = outputOrder.stream().map(StockInAndOutEntity::getOrderNumber).collect(Collectors.toList());
        return success(list);
    }

    /**
     * 获取出库单关联物料
     *
     * @return
     */
    @GetMapping("/output/material/{outputOrder}")
    public ResponseData getOutputMaterial(@PathVariable String outputOrder) {
        StockInAndOutEntity detail = JacksonUtil.getResponseObject(stockInAndOutInterface.getDetailByOrderNumber(outputOrder), StockInAndOutEntity.class);
        List<StockMaterialDetailEntity> materialDetails = detail.getMaterialDetails();
        ArrayList<StockDeliveryMaterialEntity> materialEntities = new ArrayList<>();
        for (StockMaterialDetailEntity materialDetail : materialDetails) {
            MaterialEntity materialEntity = materialService.getMaterialEntityByCode(materialDetail.getProductCode());
            StockDeliveryMaterialEntity build = StockDeliveryMaterialEntity.builder()
                    .materialFields(materialEntity)
                    .materielCode(materialDetail.getProductCode())
                    .amount(materialDetail.getActualAmount())
                    .build();
            materialEntities.add(build);
        }
        return success(materialEntities);
    }
    @GetMapping("/output/material")
    public ResponseData getOutputMaterial2(@RequestParam String outputOrder) {
        return getOutputMaterial(outputOrder);
    }

    /**
     * 审批
     */
    @GetMapping("/examine/approve")
    public ResponseData examineOrApprove(@RequestParam(value = "approvalStatus") Integer approvalStatus,
                                         @RequestParam(value = "approvalSuggestion", required = false) String approvalSuggestion,
                                         @RequestParam(value = "id") Integer id) {
        String username = getUsername();
        deliveryService.examineOrApprove(approvalStatus, approvalSuggestion, id, username);
        return success();
    }

    /**
     * 批量审批
     *
     * @return
     */
    @PostMapping("/approve/batch")
    public ResponseData approveBatch(@RequestBody ApproveBatchDTO dto) {
        dto.setActualApprover(getUsername());
        deliveryService.approveBatch(dto);
        return success();
    }


    /**
     * 发货管理批量编辑
     */
    @PostMapping("/batch/update/state")
    public ResponseData batchUpdate(@RequestBody BatchChangeStateDTO batchApprovalDTO) {
        if (batchApprovalDTO.getIds() == null) {
            return fail("编辑的单据id为空");
        }
        String username = getUsername();
        Boolean ret = deliveryService.batchUpdateState(batchApprovalDTO, username);
        return ResponseData.success(ret);
    }

}
