package com.yelink.dfs.controller.common;

import com.yelink.dfs.common.BaseController;
import com.yelink.dfs.constant.product.ProductionStateEnum;
import com.yelink.dfs.entity.common.CommonType;
import com.yelink.dfs.service.common.CommonService;
import com.yelink.dfs.service.common.config.AppletsLicenseConfigService;
import com.yelink.dfscommon.constant.RedisKeyPrefix;
import com.yelink.dfscommon.pojo.ResponseData;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;

/**
 * @description: 公共特性的控制层
 * @author: zwq
 * @time: 2024/10/17
 */
@RestController
@AllArgsConstructor
@RequestMapping("/common")
public class CommonController extends BaseController {

    private final CommonService commonService;

    /**
     * 获取json脚本
     * @return
     */
    @GetMapping("/delete/process")
    public ResponseData deleteProgress(@RequestParam String code){
        return ResponseData.success(commonService.getProgress(RedisKeyPrefix.DELETE_PROGRESS + code));
    }

    /**
     * 获取生产状态
     *
     * @return
     */
    @GetMapping("/production/state")
    public ResponseData getBomProductionState() {
        List<CommonType> list = new ArrayList<>();
        ProductionStateEnum[] values = ProductionStateEnum.values();
        for (ProductionStateEnum value : values) {
            CommonType type = new CommonType();
            type.setCode(value.getCode());
            type.setName(value.getName());
            list.add(type);
        }
        return success(list);
    }

}
