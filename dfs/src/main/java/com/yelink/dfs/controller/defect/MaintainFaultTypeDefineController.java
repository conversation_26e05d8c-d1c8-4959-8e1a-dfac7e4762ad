package com.yelink.dfs.controller.defect;

import com.yelink.dfs.common.BaseController;
import com.yelink.dfs.entity.defect.MaintainFaultTypeDefineEntity;
import com.yelink.dfs.service.defect.MaintainFaultTypeDefineService;
import com.yelink.dfscommon.pojo.ResponseData;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;

/**
 * @Description:故障类型定义controller
 * @Author: SQ
 * @Date: 2022/4/19 17:00
 * @Version:1.0
 */
@RestController
@AllArgsConstructor
@RequestMapping("/fault/define")
public class MaintainFaultTypeDefineController extends BaseController {

    private final MaintainFaultTypeDefineService maintainFaultTypeDefineService;

    /**
     * 查询故障定义分类列表
     *
     * @return
     */
    @GetMapping("/list")
    public ResponseData list(@RequestParam(value = "pageSize", required = false) Integer pageSize,
                             @RequestParam(value = "currentPage", required = false) Integer currentPage,
                             @RequestParam(value = "name", required = false) String name) {
        return ResponseData.success(maintainFaultTypeDefineService.getList(pageSize, currentPage, name));
    }

    /**
     * 新增故障定义分类
     *
     * @param entity
     * @return
     * @throws Exception
     */
    @PostMapping("/insert")
    public ResponseData add(@RequestBody MaintainFaultTypeDefineEntity entity) {
        entity.setCreateBy(getUsername());
        entity.setCreateTime(new Date());
        Boolean result = maintainFaultTypeDefineService.add(entity);
        if (result) {
            return ResponseData.success();
        }
        return ResponseData.fail();
    }

    /**
     * 修改
     *
     * @param entity
     * @return
     * @throws Exception
     */
    @PutMapping("/update")
    public ResponseData edit(@RequestBody MaintainFaultTypeDefineEntity entity) {
        entity.setUpdateBy(getUsername());
        entity.setUpdateTime(new Date());
        Boolean result = maintainFaultTypeDefineService.edit(entity);
        if (result) {
            return ResponseData.success();
        }
        return ResponseData.fail();
    }

    /**
     * 删除
     *
     * @param id
     * @return
     * @throws Exception
     */
    @DeleteMapping("/delete/{id}")
    public ResponseData delete(@PathVariable(value = "id") Integer id) {
        maintainFaultTypeDefineService.delete(id);
        return ResponseData.success();
    }

    /**
     * 查询
     *
     * @param id
     * @return
     * @throws Exception
     */
    @GetMapping("/get")
    public ResponseData get(@RequestParam(value = "id") Integer id) {
        return ResponseData.success(maintainFaultTypeDefineService.getById(id));
    }

    /**
     * 质检返工方案新增时查询故障类型方案
     * @return
     */
    @GetMapping("/type/list")
    public ResponseData getList(){
        return ResponseData.success(maintainFaultTypeDefineService.listFaultTypes());
    }




}
