package com.yelink.dfs.controller.furnace;

import com.yelink.dfs.common.BaseController;
import com.yelink.dfs.constant.furnace.FurnaceExportReportTypeEnum;
import com.yelink.dfs.service.furnace.FurnaceExportService;
import com.yelink.dfs.service.furnace.LaboratoryReportService;
import com.yelink.dfscommon.constant.CommonEnum;
import com.yelink.dfscommon.pojo.ResponseData;
import com.yelink.inner.service.KafkaWebSocketPublisher;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/3/30 14:13
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/furnace/export")
public class FurnaceExportController extends BaseController {
    private FurnaceExportService furnaceExportService;
    private LaboratoryReportService laboratoryReportService;
    private KafkaWebSocketPublisher kafkaWebSocketPublisher;



    /**
     * 按时间导出各类excel
     *
     * @param startTime
     * @param endTime
     */
    @GetMapping("/by/exportType")
    public void exportExcel(@RequestParam(value = "startTime", required = false) String startTime,
                            @RequestParam(value = "endTime", required = false) String endTime,
                            @RequestParam(value = "fid", required = false) Integer fid,
                            @RequestParam(value = "exportType") String exportType,
                            HttpServletResponse response) throws IOException {
        if (exportType.equals(FurnaceExportReportTypeEnum.REFINING_FURNACE.getExportType())) {
            //产品经理说精炼炉目前只有一个 不需要通过精炼炉的工位id去过滤数据
            furnaceExportService.exportRefiningFurnace(startTime, endTime, response);
        }
        if (exportType.equals(FurnaceExportReportTypeEnum.SPECTROGRAPH.getExportType())) {
            laboratoryReportService.exportExcel(fid, startTime, endTime, response);
        }
        if (exportType.equals(FurnaceExportReportTypeEnum.ELECTRIC_FURNACE.getExportType())) {
            furnaceExportService.exportElectricFurnace(startTime, endTime, fid, response);
        }
    }


    /**
     * 获取导出类型
     *
     * @return
     */
    @GetMapping("/type/list")
    public ResponseData getExportTypeList() {
        List<CommonEnum> list = furnaceExportService.geProgramTypeList();
        return ResponseData.success(list);
    }


}
