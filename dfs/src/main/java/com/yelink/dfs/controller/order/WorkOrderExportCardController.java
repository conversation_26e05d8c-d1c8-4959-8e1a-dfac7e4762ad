package com.yelink.dfs.controller.order;

import com.yelink.dfs.entity.order.vo.WorkOrderFlowCardVO;
import com.yelink.dfs.service.order.WorkOrderExportService;
import com.yelink.dfs.utils.FileUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.io.File;

/**
 * 康居人流程卡打印
 *
 * <AUTHOR>
 * @Date 2022-10-18 12:01
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/work/order/export/card")
public class WorkOrderExportCardController {


    private WorkOrderExportService workOrderExportService;


    /**
     * 导出流程卡
     * @param
     * @param
     * @throws Exception
     */
    @PostMapping("/export/flow/card")
    public void exportFlowCart(@RequestBody WorkOrderFlowCardVO flowCardVO,HttpServletResponse response) throws Exception{
        File pdfFile = workOrderExportService.exportFlowCart(flowCardVO);
        try {
           FileUtil.exportPdfFile(response, pdfFile,flowCardVO.getFileName());
        }finally {
           FileUtils.deleteQuietly(pdfFile);
        }
    }
}
