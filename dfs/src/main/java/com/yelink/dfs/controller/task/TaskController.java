package com.yelink.dfs.controller.task;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yelink.dfs.common.BaseController;
import com.yelink.dfs.constant.task.TaskStateEnum;
import com.yelink.dfs.entity.common.CommonType;
import com.yelink.dfs.service.task.TaskService;
import com.yelink.dfscommon.constant.ParamException;
import com.yelink.dfs.entity.task.TaskEntity;
import com.yelink.dfs.entity.task.dto.OrderTaskChartVO;
import com.yelink.dfs.entity.task.dto.OrderTaskOverviewVO;
import com.yelink.dfs.entity.task.dto.OrderTaskUpdateChartDTO;
import com.yelink.dfs.entity.task.dto.TaskDeleteDTO;
import com.yelink.dfs.entity.task.dto.TaskPageDTO;
import com.yelink.dfs.entity.task.dto.TaskUpsertDTO;
import com.yelink.dfscommon.pojo.ResponseData;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;


/**
 * @Description:
 * @Author: zengzhengfu
 * @Date: 2024/12/5
 */
@Slf4j
@RestController("DfsTaskController")
@AllArgsConstructor
@RequestMapping("/task")
public class TaskController extends BaseController {

    private TaskService taskService;

    /**
     * 查询任务
     *
     * @param dto
     * @return
     */
    @PostMapping("/list")
    public ResponseData getTask(@RequestBody TaskPageDTO dto) {
        Page<TaskEntity> list = taskService.getPage(dto);
        return ResponseData.success(list);
    }

    /**
     * 查询任务总览
     *
     * @param
     * @return
     */
    @PostMapping("/overview")
    public ResponseData getTaskOverview(@RequestBody TaskPageDTO dto) {
        OrderTaskOverviewVO overviewVO = taskService.getTaskOverview(dto);
        return ResponseData.success(overviewVO);
    }

    /**
     * 获取任务状态枚举
     *
     * @param
     * @return
     */
    @GetMapping("/state/enum")
    public ResponseData getTaskStateEnum() {
        List<CommonType> list = new ArrayList<>();
        TaskStateEnum[] values = TaskStateEnum.values();
        for (TaskStateEnum value : values) {
            CommonType type = new CommonType();
            type.setCode(value.getCode());
            type.setName(value.getName());
            list.add(type);
        }
        return success(list);
    }

    /**
     * 查询单据大类的任务链图表
     *
     * @param
     * @return
     */
    @GetMapping("/order/chart")
    public ResponseData getOrderTaskChart() {
        OrderTaskChartVO chartVO = taskService.getOrderTaskChart();
        return ResponseData.success(chartVO);
    }

    /**
     * 更新单据大类的任务链图表
     *
     * @param
     * @return
     */
    @PostMapping("/update/order/chart")
    public ResponseData updateOrderTaskChart(@RequestBody List<OrderTaskUpdateChartDTO> updateChartDTOS) {
        taskService.updateOrderTaskChart(updateChartDTOS);
        return ResponseData.success();
    }

    /**
     * 新增任务
     *
     * @param dto
     * @return
     */
    @PostMapping("/upsert")
    public ResponseData upsertTask(@RequestBody @Validated TaskUpsertDTO dto, BindingResult bindingResult) {
        if (bindingResult.hasErrors()) {
            throw new ParamException(bindingResult);
        }
        taskService.upsert(dto);
        return ResponseData.success(dto);
    }

    /**
     * 删除任务
     *
     * @param dto
     * @return
     */
    @PostMapping("/delete")
    public ResponseData deleteTask(@RequestBody TaskDeleteDTO dto) {
        taskService.delete(dto);
        return ResponseData.success();
    }

    @GetMapping("/taskTree")
    public ResponseData taskTree(@RequestParam Integer taskId) {
        return ResponseData.success(taskService.taskTree(taskId));
    }
}
