package com.yelink.dfs.controller.common;


import com.asyncexcel.core.model.ExcelTask;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yelink.dfs.common.BaseController;
import com.yelink.dfs.open.v1.common.dto.OperationLogPageDTO;
import com.yelink.dfs.service.common.OperationLogService;
import com.yelink.dfscommon.entity.dfs.OperationLogEntity;
import com.yelink.dfscommon.pojo.ResponseData;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;


/**
 * @Description: 操作记录
 * @Author: zengzhengfu
 * @Date: 2021/4/8
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/log")
public class OperationLogController extends BaseController {

    private OperationLogService operationLogService;

    /**
     * 操作日志列表
     *
     * @param dto
     * @return
     */
    @PostMapping("/list")
    public ResponseData termsList(@RequestBody OperationLogPageDTO dto) {
        Page<OperationLogEntity> page = operationLogService.logList(dto);
        return success(page);
    }

    /**
     * 日志导出  :异步
     *
     * @param dto
     * @return
     * @throws IOException
     */
    @PostMapping("/syc/exports")
    public ResponseData export(@RequestBody OperationLogPageDTO dto) {
        Long result = operationLogService.exportTask(dto, getUsername());
        return success(result);
    }


    /**
     * 分页查询当前导出任务列表
     *
     * @param size
     * @param current
     * @return
     */
    @GetMapping("/export/task/page")
    public ResponseData taskPage(@RequestParam(value = "pageSize", required = false, defaultValue = "20") Integer size,
                                 @RequestParam(value = "currentPage", required = false, defaultValue = "1") Integer current) {
        IPage<ExcelTask> iPage = operationLogService.taskPage(current, size);
        return success(iPage);
    }


    /**
     * 查询 导出进度
     *
     * @return
     */
    @GetMapping("/export/task/{taskId}")
    public ResponseData exportExcel(@PathVariable Long taskId) {
        ExcelTask excelTask = operationLogService.taskById(taskId);
        return success(excelTask);
    }

}
