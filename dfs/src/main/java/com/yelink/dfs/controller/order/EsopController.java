package com.yelink.dfs.controller.order;

import com.yelink.dfs.entity.product.dto.CraftFileNewDTO;
import com.yelink.dfs.service.order.EsopService;
import com.yelink.dfscommon.pojo.ResponseData;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/esop")
public class EsopController {

    private final EsopService esopService;

    @GetMapping("/newest/list")
    public ResponseData orderList(@RequestParam(required = false) Integer lineId,
                                  @RequestParam(required = false) Integer fid,
                                  @RequestParam(required = false, value="workOrderNumber") String targetWorkOrderNumber) {
        List<CraftFileNewDTO> result = esopService.getNewestEsop(lineId, fid, targetWorkOrderNumber);
        return ResponseData.success(result);
    }


}
