package com.yelink.dfs.controller.product;

import com.asyncexcel.core.DataParam;
import com.asyncexcel.core.ExcelContext;
import com.asyncexcel.core.ExportPage;
import com.asyncexcel.core.annotation.ExcelHandle;
import com.asyncexcel.core.exporter.DataExportParam;
import com.asyncexcel.core.exporter.ExportContext;
import com.asyncexcel.core.exporter.ExportHandler;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yelink.dfs.entity.product.CraftEntity;
import com.yelink.dfs.entity.product.dto.CraftSelectDTO;
import com.yelink.dfs.entity.product.dto.SimpleCraftExportDTO;
import com.yelink.dfs.entity.product.dto.SimpleCraftTempExportDTO;
import com.yelink.dfs.service.common.CommonService;
import com.yelink.dfs.service.product.CraftService;
import com.yelink.dfscommon.utils.JacksonUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.List;


/**
 * 生产工单列表数据导出
 *
 * <AUTHOR>
 * @Date 2023/7/5 11:18
 */
@Slf4j
@ExcelHandle
@RequiredArgsConstructor
public class CraftTempSimpleListExportHandler implements ExportHandler<SimpleCraftTempExportDTO> {

    private final CraftService craftService;
    private final CommonService commonService;

    /**
     * 导出数据最大不能超过100万行
     */
    public static final long EXPORT_MAX_ROWS = 1000000;

    @Override
    public void init(ExcelContext ctx, DataParam param) {
        CraftSelectDTO selectDTO = (CraftSelectDTO) param.getParameters().get(CraftSelectDTO.class.getName());
        ExportContext context = (ExportContext) ctx;
        commonService.initExcelContext(selectDTO.getTemplateId(), selectDTO.getTemplateSheetName(), selectDTO.getTemplateSheetName(), context, SimpleCraftTempExportDTO.class, null);
    }

    @Override
    public ExportPage<SimpleCraftTempExportDTO> exportData(int startPage, int limit, DataExportParam param) {
        CraftSelectDTO selectDTO = (CraftSelectDTO) param.getParameters().get(CraftSelectDTO.class.getName());
        selectDTO.setCurrent(startPage);
        selectDTO.setSize(limit);
        Page<CraftEntity> page = craftService.getList(selectDTO);
        List<SimpleCraftExportDTO> list = craftService.convertToSimpleCraftExportDTO(page.getRecords());
        List<SimpleCraftTempExportDTO> exportDTOS = JacksonUtil.convertArray(list, SimpleCraftTempExportDTO.class);
        ExportPage<SimpleCraftTempExportDTO> result = new ExportPage<>();
        result.setTotal(Math.min(page.getTotal(), EXPORT_MAX_ROWS));
        result.setCurrent((long) startPage);
        result.setSize((long) limit);
        result.setRecords(exportDTOS);
        return result;
    }


    @Override
    public void beforePerPage(ExportContext ctx, DataExportParam param) {
        log.info("开始查询第{}页", (long) Math.ceil(ctx.getSuccessCount() + ctx.getFailCount()) / ctx.getLimit());
    }

    @Override
    public void afterPerPage(List<SimpleCraftTempExportDTO> list, ExportContext ctx, DataExportParam param) {
        log.info("已完成数量：{}", ctx.getSuccessCount());
    }

    @Override
    public void callBack(ExcelContext ctx, DataParam param) {
        ExportContext context = (ExportContext) ctx;
        log.info("导出完成：{}", context.getFailMessage());
    }
}
