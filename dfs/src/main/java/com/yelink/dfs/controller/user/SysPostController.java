package com.yelink.dfs.controller.user;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yelink.dfs.common.BaseController;
import com.yelink.dfs.constant.OperationType;
import com.yelink.dfs.entity.user.SysUserEntity;
import com.yelink.dfs.mapper.user.enums.EnabledEnum;
import com.yelink.dfs.service.user.SysPostService;
import com.yelink.dfscommon.annotation.OperLog;
import com.yelink.dfscommon.constant.CommonEnum;
import com.yelink.dfscommon.constant.ParamException;
import com.yelink.dfscommon.entity.dfs.SysPostEntity;
import com.yelink.dfscommon.pojo.ResponseData;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;


/**
 * @description: 岗位信息
 * @author: zhenghengqiu
 * @time: 2022/5/31 16:37
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/posts")
public class SysPostController extends BaseController {

    private SysPostService sysPostService;

    /**
     * 岗位列表分页
     *
     * @return
     */
    @GetMapping("/list")
    public ResponseData list(@RequestParam(value = "current", defaultValue = "1") Integer current,
                             @RequestParam(value = "size", defaultValue = "10") Integer size,
                             @RequestParam(value = "status", required = false) String status) {
        Page<SysPostEntity> list = sysPostService.list(current, size, status);
        return success(list);
    }
    /**
     * 岗位列表
     *
     * @return
     */
    @GetMapping("/lists")
    public ResponseData lists() {
        List<SysPostEntity> list = sysPostService.list();
        return success(list);
    }

    /**
     * 根据岗位id查询岗位详细信息
     *
     * @param id
     * @return
     */
    @GetMapping("/select/{id}")
    public ResponseData selectById(@PathVariable Integer id) {
        SysPostEntity sysPostEntity = sysPostService.getById(id);
        return success(sysPostEntity);
    }

    /**
     * 添加岗位
     *
     * @param sysPostEntity
     * @param bindingResult
     * @return
     */
    @PostMapping("/insert")
    @OperLog(module = "岗位管理", type = OperationType.ADD, desc = "新增了岗位编号为#{postCode}的岗位")
    public ResponseData add(@RequestBody @Valid SysPostEntity sysPostEntity, BindingResult bindingResult) {
        if (bindingResult.hasErrors()) {
            throw new ParamException(bindingResult);
        }
        sysPostEntity.setCreateBy(getUsername());
        sysPostEntity.setCreateTime(new Date());
        sysPostEntity.setUpdateBy(getUsername());
        sysPostEntity.setUpdateTime(new Date());
        if (sysPostService.saveEntity(sysPostEntity)) {
            return success(sysPostEntity);
        }
        return fail();
    }

    /**
     * 修改信息
     *
     * @param sysPostEntity
     * @return
     */
    @PutMapping("/update")
    @OperLog(module = "岗位管理", type = OperationType.UPDATE, desc = "修改了岗位编号为#{postCode}的岗位")
    public ResponseData update(@RequestBody SysPostEntity sysPostEntity) {
        sysPostEntity.setUpdateTime(new Date());
        sysPostEntity.setUpdateBy(getUsername());
        sysPostService.updateEntity(sysPostEntity);
        return ResponseData.success();
    }

    /**
     * 通过Id删除岗位
     *
     * @param id
     * @return
     */
    @DeleteMapping("/delete/{id}")
    @OperLog(module = "岗位管理", type = OperationType.DELETE, desc = "删除了岗位编号为#{postCode}的岗位")
    public ResponseData deleteById(@PathVariable Integer id) {
        if (sysPostService.deleteById(id)) {
            return success();
        }
        return fail();
    }

    /**
     * 获取该岗位已存在的其他等级
     *
     * @param postName
     * @return
     */
    @GetMapping("/other/levels")
    public ResponseData getOtherLevels(@RequestParam(value = "postName") String postName,
                                       @RequestParam(value = "id") Integer id) {
        List<SysPostEntity> list = sysPostService.getOtherLevels(postName, id);
        return success(list);
    }

    /**
     * 获取岗位树
     *
     * @return List<SysPostEntity>
     */
    @GetMapping("/tree")
    public ResponseData getPostTree() {
        List<SysPostEntity> list = sysPostService.getPostTree();
        return success(list);
    }

    /**
     * 岗位状态列表
     *
     * @return
     */
    @GetMapping("/status/list")
    public ResponseData getStatusList() {
        CommonEnum.CommonEnumBuilder builder = CommonEnum.builder();
        return success(Arrays.stream(EnabledEnum.values())
                .map(o -> builder.code(o.getCode()).name(o.getName()).build()).collect(Collectors.toList()));
    }

    /**
     * 岗位添加组员
     *
     * @param id
     * @return
     */
    @PostMapping("/bind/{id}")
    public ResponseData bind(@PathVariable("id") Integer id, String userIds) {
        sysPostService.bind(id, userIds);
        return success();
    }

    /**
     * 获取该岗位对应的用户列表
     *
     * @param id 岗位id
     */
    @GetMapping("/members/{id}")
    public ResponseData getMemberList(@PathVariable(value = "id") Integer id) {
        List<SysUserEntity> list = sysPostService.getMemberList(id);
        return success(list);
    }

    /**
     * 获取启用的岗位列表
     *
     * @return
     */
    @GetMapping("/getList")
    public ResponseData getList() {
        List<SysPostEntity> list = sysPostService.getList();
        return success(list);
    }

}
