package com.yelink.dfs.controller.pack;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yelink.dfs.common.BaseController;
import com.yelink.dfs.constant.OperationType;
import com.yelink.dfs.entity.code.dto.PackageCodePrintDTO;
import com.yelink.dfs.entity.pack.PackageOrderEntity;
import com.yelink.dfs.entity.pack.dto.BatchUpdatePackageOrderDTO;
import com.yelink.dfs.entity.pack.dto.PackageOrderDTO;
import com.yelink.dfs.service.pack.PackageOrderService;
import com.yelink.dfscommon.annotation.OperLog;
import com.yelink.dfscommon.constant.dfs.pack.PackageOrderRelateTypeEnum;
import com.yelink.dfscommon.constant.dfs.pack.PackageOrderStateEnum;
import com.yelink.dfscommon.dto.ApproveBatchDTO;
import com.yelink.dfscommon.entity.CommonType;
import com.yelink.dfscommon.entity.dfs.barcode.PrintDTO;
import com.yelink.dfscommon.pojo.ResponseData;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 包装工单
 *
 * <AUTHOR>
 * @Date 2021-11-16 17:58
 */
@Controller
@RestController
@RequestMapping("/package/orders")
@AllArgsConstructor
public class PackageOrderController extends BaseController {

    private PackageOrderService packageOrderService;

    /**
     * 查询列表
     *
     * @param dto
     * @return
     */
    @PostMapping("/list")
    public ResponseData packageList(@RequestBody PackageOrderDTO dto) {
        Page<PackageOrderEntity> list = packageOrderService.getList(dto, getUsername());
        return ResponseData.success(list);
    }

    /**
     * 新增包装工单
     *
     * @param entity
     * @return
     */
    @PostMapping("/insert")
    @OperLog(module = "包装工单", type = OperationType.ADD, desc = "新增了编码为#{packageOrderNumber}的包装工单")
    public ResponseData addEntity(@RequestBody PackageOrderEntity entity) {
        String username = getUsername();
        entity.setCreateBy(username);
        entity.setUpdateBy(username);
        return ResponseData.success(packageOrderService.addEntity(entity));
    }

    /**
     * 创建生效的工单
     *
     * @param entity
     * @return
     */
    @PostMapping("/insert/released")
    @OperLog(module = "包装工单", type = OperationType.ADD, desc = "新增了编码为#{packageOrderNumber}的包装工单")
    public ResponseData addReleasedWorkOrder(@RequestBody PackageOrderEntity entity) {
        String username = getUsername();
        entity.setCreateBy(username);
        entity.setUpdateBy(username);
        packageOrderService.addReleased(entity);
        return success(entity);
    }

    /**
     * 更新
     *
     * @param entity
     * @return
     */
    @PutMapping("/update")
    @OperLog(module = "包装工单", type = OperationType.UPDATE, desc = "更新了编码为#{packageOrderNumber}的包装工单")
    public ResponseData updateEntity(@RequestBody PackageOrderEntity entity) {
        String username = getUsername();
        entity.setUpdateBy(username);
        packageOrderService.updateEntity(entity);
        return ResponseData.success(entity);
    }

    /**
     * 详情
     *
     * @param id
     * @return
     */
    @GetMapping("/detail/{id}")
    public ResponseData getDetail(@PathVariable Integer id) {
        PackageOrderEntity entity = packageOrderService.getDetailById(id);
        return ResponseData.success(entity);
    }

    /**
     * 删除
     *
     * @param id
     * @return
     */
    @DeleteMapping("/delete/{id}")
    @OperLog(module = "包装工单", type = OperationType.DELETE, desc = "删除了编码为#{packageOrderNumber}的包装工单")
    public ResponseData deleteById(@PathVariable Integer id) {
        PackageOrderEntity entity = packageOrderService.deleteById(id);
        return ResponseData.success(entity);
    }

    /**
     * 状态列表
     *
     * @return
     */
    @GetMapping("/state")
    public ResponseData getState() {
        return ResponseData.success(CommonType.covertToList(PackageOrderStateEnum.class));
    }

    /**
     * 关联类型列表
     *
     * @return
     */
    @GetMapping("/relate/type")
    public ResponseData getRelateType() {
        return ResponseData.success(CommonType.covertToList(PackageOrderRelateTypeEnum.class));
    }

    /**
     * 审批
     */
    @GetMapping("/examine/approve")
    public ResponseData examineOrApprove(@RequestParam(value = "approvalStatus") Integer approvalStatus,
                                         @RequestParam(value = "approvalSuggestion", required = false) String approvalSuggestion,
                                         @RequestParam(value = "id") Integer id) {
        String username = getUsername();
        packageOrderService.examineOrApprove(approvalStatus, approvalSuggestion, id, username);
        return success();
    }

    /**
     * 批量审批
     *
     * @return
     */
    @PostMapping("/approve/batch")
    public ResponseData approveBatch(@RequestBody ApproveBatchDTO dto) {
        dto.setActualApprover(getUsername());
        packageOrderService.approveBatch(dto);
        return success();
    }

    /**
     * 包装工位机
     * 包装码- 打印-返回数据
     */
    @PostMapping("/package/code/print")
    public ResponseData packagePrint(@RequestBody PackageCodePrintDTO packageCodePrintDTO) {
        PrintDTO printDTO = packageOrderService.printPackageCode(packageCodePrintDTO);
        return ResponseData.success(printDTO);
    }

    /**
     * 批量编辑
     *
     * @param
     * @return
     */
    @PutMapping("/batch/update")
    public ResponseData batchUpdate(@RequestBody @Validated BatchUpdatePackageOrderDTO dto) {
        if (StringUtils.isBlank(dto.getUsername())) {
            dto.setUsername(getUsername());
        }
        packageOrderService.batchUpdate(dto);
        return success();
    }

    /**
     * 批量编辑处理进度
     *
     * @return
     */
    @GetMapping("/batch/update/progress")
    public ResponseData batchUpdateProgress() {
        String s = packageOrderService.getBatchUpdateProgress();
        return success(s);
    }


}
