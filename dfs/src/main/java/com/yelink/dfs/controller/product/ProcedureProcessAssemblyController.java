package com.yelink.dfs.controller.product;

import com.yelink.dfs.common.BaseController;
import com.yelink.dfs.constant.model.VersionModelIdEnum;
import com.yelink.dfs.entity.common.VersionChangeCraftProcedureDTO;
import com.yelink.dfs.entity.common.VersionChangeDTO;
import com.yelink.dfs.entity.product.ProcedureProcessAssemblyEntity;
import com.yelink.dfs.service.common.VersionChangeRecordService;
import com.yelink.dfs.service.product.ProcedureProcessAssemblyService;
import com.yelink.dfscommon.pojo.ResponseData;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * @Description: 工序转配管理
 * @Author: shenzm
 * @Date: 2022/9/19
 */
@Slf4j
@RestController
@RequestMapping("/procedure/process/assembly")
public class ProcedureProcessAssemblyController extends BaseController {

    @Resource
    private ProcedureProcessAssemblyService procedureProcessAssemblyService;
    @Resource
    private VersionChangeRecordService versionChangeRecordService;

    /**
     * 根据工序id获取工序工装
     * @return
     */
    @GetMapping("/getListById/{procedureId}")
    public ResponseData getListById(@PathVariable(value = "procedureId") Integer id) {
        List<ProcedureProcessAssemblyEntity> entities = procedureProcessAssemblyService.getListById(id);
        return success(entities);
    }

    /**
     * 新增工序工装信息
     *
     * @return
     */
    @PostMapping("/insert")
    public ResponseData insert(@RequestBody ProcedureProcessAssemblyEntity entity) {
        entity.setCreateBy(getUsername());
        entity.setUpdateBy(getUsername());
        entity.setCreateTime(new Date());
        entity.setUpdateTime(new Date());
        Boolean b= procedureProcessAssemblyService.saveEntity(entity);
        if (!b) {
            return fail();
        } else {
            versionChangeRecordService.changeVersionCraftProcedure(VersionChangeCraftProcedureDTO.builder()
                    .description("工装 - 新增")
                    .craftProcedureId(entity.getProcedureId())
                    .build()
            );
            return success();
        }

    }

    /**
     * 更新工序工装信息
     *
     * @return
     */
    @PutMapping("/update")
    public ResponseData update(@RequestBody ProcedureProcessAssemblyEntity entity) {
        entity.setUpdateBy(getUsername());
        entity.setUpdateTime(new Date());
        boolean b = procedureProcessAssemblyService.updateById(entity);
        if (!b) {
            return fail();
        }
        versionChangeRecordService.changeVersionCraftProcedure(VersionChangeCraftProcedureDTO.builder()
                .description("工装 - 更新")
                .craftProcedureId(entity.getProcedureId())
                .build()
        );
        return success();
    }

    /**
     * 根据工序id删除对应工序工装信息
     *
     * @return
     */
    @DeleteMapping("/deleteByProcedureId/{procedureId}")
    public ResponseData deleteByProcedureId(@PathVariable(value = "procedureId") Integer id) {
        boolean b = procedureProcessAssemblyService.deleteByProcedureId(id);
        if (!b) {
            return fail();
        }
        return success();
    }

    /**
     * 根据工序工装id删除工序工装信息
     *
     * @return
     */
    @DeleteMapping("/delete/{id}")
    public ResponseData delete(@PathVariable(value = "id") Integer id) {
        ProcedureProcessAssemblyEntity entity = procedureProcessAssemblyService.getById(id);
        boolean b = procedureProcessAssemblyService.removeById(id);
        if (!b) {
            return fail();
        }
        versionChangeRecordService.changeVersionCraftProcedure(VersionChangeCraftProcedureDTO.builder()
                .description("工装 - 删除")
                .craftProcedureId(entity.getProcedureId())
                .build()
        );
        return success();
    }

}
