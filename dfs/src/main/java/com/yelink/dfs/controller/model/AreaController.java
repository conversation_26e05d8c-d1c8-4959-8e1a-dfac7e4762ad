package com.yelink.dfs.controller.model;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yelink.dfs.common.BaseController;
import com.yelink.dfs.constant.OperationType;
import com.yelink.dfs.constant.RedisKeyPrefix;
import com.yelink.dfs.entity.manufacture.ProductionLineEntity;
import com.yelink.dfs.entity.model.AreaEntity;
import com.yelink.dfs.entity.user.MyAuthenticationUserDetail;
import com.yelink.dfs.open.v1.model.dto.AreaSelectDTO;
import com.yelink.dfs.provider.constant.KafkaMessageTypeEnum;
import com.yelink.dfs.service.manufacture.ProductionLineService;
import com.yelink.dfs.service.model.AreaService;
import com.yelink.dfscommon.annotation.OperLog;
import com.yelink.dfscommon.constant.Constants;
import com.yelink.dfscommon.constant.ParamException;
import com.yelink.dfscommon.constant.RespCodeEnum;
import com.yelink.dfscommon.pojo.ResponseData;
import com.yelink.dfscommon.provider.MessagePushToKafkaService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * @description: 厂区信息接口
 * @author: yejiarun
 * @time: 2020/12/09
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/areas")
public class AreaController extends BaseController {

    private AreaService areaService;
    private RedisTemplate redisTemplate;
    private MessagePushToKafkaService messagePushToKafkaService;
    private ProductionLineService productionLineService;

    /**
     * 分页条件查询厂区列表
     *
     * @param aid     id
     * @param code    厂区编号
     * @param name    厂区名称
     * @param current
     * @param size
     * @return
     */
    @GetMapping("/list")
    public ResponseData list(@RequestParam(value = "aid", required = false) Integer aid,
                             @RequestParam(value = "code", required = false) String code,
                             @RequestParam(value = "name", required = false) String name,
                             @RequestParam(value = "current", defaultValue = "1") Integer current,
                             @RequestParam(value = "size", defaultValue = "20") Integer size) {
        AreaSelectDTO build = AreaSelectDTO.builder().aid(aid).code(code).name(name).build();
        build.setCurrent(current);
        build.setSize(size);
        Page<AreaEntity> list = areaService.list(build);
        return success(list);
    }

    /**
     * 通过Id查询厂区信息
     *
     * @param id
     * @return
     */
    @GetMapping("/select/{id}")
    public ResponseData selectById(@PathVariable Integer id) {
        AreaEntity entity = areaService.detail(id);
        return success(entity);
    }

    /**
     * 添加厂区信息(并绑定员工信息)
     * <p>
     * 添加厂区信息需要公司ID、厂区名称
     * <p>
     * 是否需要查询公司ID存不存在？
     *
     * @param entity
     * @return
     */
    @PostMapping("/insert")
    @OperLog(module = "工厂配置", type = OperationType.ADD, desc = "新增了厂区编码为#{acode}的厂区信息")
    public ResponseData add(@RequestBody @Valid AreaEntity entity, BindingResult bindingResult) {
        if (bindingResult.hasErrors()) {
            throw new ParamException(bindingResult);
        }

        MyAuthenticationUserDetail userInfo = getUserInfo();
        String username = getUsername();
        int compId = userInfo.getCompId();
        entity.setCid(compId);
        entity.setCreateBy(username);
        entity.setUpdateBy(username);
        areaService.addEntity(entity);
        return success(entity);
    }

    /**
     * 修改厂区信息
     *
     * @param entity
     * @return
     */
    @PutMapping("/update")
    @OperLog(module = "工厂配置", type = OperationType.UPDATE, desc = "修改了厂区编码为#{acode}的厂区信息")
    public ResponseData update(@RequestBody AreaEntity entity) {

        Optional.ofNullable(entity.getAid()).orElseThrow(() -> new ParamException("对象Id不能为空"));
        entity.setUpdateBy(getUsername());
        entity.setUpdateTime(new Date());
        boolean update = areaService.updateById(entity);
        redisTemplate.delete(redisTemplate.keys(RedisKeyPrefix.INSTANCE_TREE + "*"));
        //推送消息
        messagePushToKafkaService.pushNewMessage(entity, Constants.KAFKA_MAIN_DATA_TOPIC, KafkaMessageTypeEnum.AREA_UPDATE_MESSAGE);
        if (update) {
            if (Boolean.TRUE.equals(entity.getUpdateLineCheckType())) {
                productionLineService.lambdaUpdate().eq(ProductionLineEntity::getAid, entity.getAid())
                        .set(ProductionLineEntity::getMaterialCheckType, entity.getMaterialCheckType()).update();
            }
            return success(entity);
        } else {
            return fail(RespCodeEnum.AREA_FAIL2UP);
        }
    }

    /**
     * 通过Id删除厂区信息
     *
     * @param id
     * @return
     */
    @DeleteMapping("/delete/{id}")
    @OperLog(module = "工厂配置", type = OperationType.DELETE, desc = "删除了厂区编码为#{acode}，名称为#{aname}的厂区信息")
    public ResponseData delete(@PathVariable Integer id) {
        return success(areaService.deleteById(id, getUsername()));
    }

    /**
     * 查询所有厂区列表
     *
     * @return
     */
    @GetMapping("/selectAll")
    public ResponseData list() {
        List<AreaEntity> list = areaService.list();
        return success(list);
    }
}
