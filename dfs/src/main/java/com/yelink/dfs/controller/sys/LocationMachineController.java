package com.yelink.dfs.controller.sys;

import com.yelink.dfscommon.constant.LocationMachineEnum;
import com.yelink.dfscommon.entity.CommonType;
import com.yelink.dfscommon.pojo.ResponseData;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import static com.yelink.dfscommon.pojo.ResponseData.success;

/**
 * 工位机
 * <AUTHOR>
 */
@RestController
@RequestMapping("/location/machine")
public class LocationMachineController {

    @GetMapping("/enum")
    public ResponseData getEnum() {
        List<CommonType> result = Arrays.stream(LocationMachineEnum.values())
                .map(e -> CommonType.builder().code(e.getCode()).name(e.getName()).build())
                .collect(Collectors.toList());
        return success(result);
    }
}
