package com.yelink.dfs.controller.sys;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yelink.dfs.common.BaseController;
import com.yelink.dfs.constant.OperationType;
import com.yelink.dfs.constant.app.AppDescEnum;
import com.yelink.dfs.constant.user.PermissionTypeEnum;
import com.yelink.dfs.entity.model.dto.InstanceDTO;
import com.yelink.dfs.entity.sys.AppFacEntity;
import com.yelink.dfs.entity.user.SysPermissionEntity;
import com.yelink.dfs.mapper.user.SysPermissionMapper;
import com.yelink.dfs.service.sys.AppFacService;
import com.yelink.dfs.service.user.SysPermissionService;
import com.yelink.dfscommon.annotation.OperLog;
import com.yelink.dfscommon.pojo.ResponseData;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/4/11 15:08
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/sys")
public class AppFacController extends BaseController {

    private AppFacService areaAppFacService;
    private SysPermissionMapper permissionMapper;
    private SysPermissionService permissionService;

    /**
     * 列表分页查询
     *
     * @param current
     * @param size
     * @return
     */
    @GetMapping("/list")
    public ResponseData list(@RequestParam(value = "current", required = false) Integer current,
                             @RequestParam(value = "size", required = false) Integer size) {
        Page<AppFacEntity> list = areaAppFacService.getList(current, size);
        return success(list);
    }

    /**
     * 批量新增
     *
     * @param appIds
     * @return
     */
    @PostMapping("/insert")
    public ResponseData addAppFac(@RequestBody List<String> appIds) {
        String username = getUsername();
        areaAppFacService.add(appIds, username);
        return success();
    }

    /**
     * 编辑
     *
     * @return AppFacEntity
     */
    @PostMapping(value = "/update")
    public ResponseData updateAppFac(@RequestBody AppFacEntity appFacEntity) {
        appFacEntity.setUpdateBy(getUsername());
        appFacEntity.setUpdateTime(new Date());
        areaAppFacService.updateAppFac(appFacEntity);
        return success();
    }

    /**
     * 获取详情
     *
     * @param
     * @return
     */
    @GetMapping("/detail/{id}")
    public ResponseData getByAppFacId(@PathVariable("id") Integer id) {
        AppFacEntity appFacEntity = areaAppFacService.getByAppFacId(id);
        return ResponseData.success(appFacEntity);
    }

    /**
     * 获取权限表配置的工位机类型的小程序列表
     *
     * @return
     */
    @GetMapping("/app/list")
    public ResponseData appList() {
        // 同步小程序
        permissionService.judgeAppButtonPermission();
        permissionService.updateAppPermission();
        List<SysPermissionEntity> allFirstPadPermission = permissionService.lambdaQuery()
                .select(SysPermissionEntity::getId, SysPermissionEntity::getName)
                .eq(SysPermissionEntity::getIsWeb, 0)
                .eq(SysPermissionEntity::getType, PermissionTypeEnum.CATALOGUE.getCode())
                .eq(SysPermissionEntity::getDescription, AppDescEnum.LOCATION_MACHINE.getCode())
                .list();
        return success(allFirstPadPermission);
    }

    /**
     * 获取(车间下的产线模型、产线模型下的工位模型，工位模型下的工位实例)
     *
     * @return
     */
    @GetMapping("/model/true/list")
    public ResponseData getModelFacTrueList() {
        List<InstanceDTO> list = areaAppFacService.getModelFacTrueList();
        return success(list);
    }


    /**
     * 通过Id删除信息
     *
     * @param id
     * @return
     */
    @DeleteMapping("/delete/{id}")
    @OperLog(module = "小程序设置", type = OperationType.DELETE, desc = "删除了设备名称为#{appName}的小程序")
    public ResponseData delete(@PathVariable Integer id) {
        AppFacEntity appFacEntity = areaAppFacService.getByAppFacId(id);
        areaAppFacService.removeEntityById(id);
        return success(appFacEntity);
    }

    /**
     * 获取小程序对应的工位，工位对应的产线
     *
     * @param
     * @return
     */
    @GetMapping("/get")
    public ResponseData getByAppFac(@RequestParam(value = "appId") String appId,
                                    @RequestParam(required = false) Integer lineId,
                                    @RequestParam(required = false) String workOrderNumber) {
        List<InstanceDTO> list = areaAppFacService.getByAppId(appId, lineId, workOrderNumber);
        return ResponseData.success(list);
    }

    /**
     * 通过appid获取信息
     *
     * @param
     * @return
     */
    @GetMapping("/get/fac/{appId}")
    public ResponseData getFacAppId(@PathVariable String appId) {
        List<AppFacEntity> list = areaAppFacService.lambdaQuery().eq(AppFacEntity::getAppId, appId).list();
        return ResponseData.success(list);
    }
}
