package com.yelink.dfs.controller.shift;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yelink.dfs.common.BaseController;
import com.yelink.dfs.constant.OperationType;
import com.yelink.dfs.entity.shift.ShiftEntity;
import com.yelink.dfs.open.v1.shift.dto.ShiftSelectDTO;
import com.yelink.dfs.service.energy.manage.DeviceShiftEnergyConsumptionService;
import com.yelink.dfs.service.energy.manage.DeviceShiftGasConsumptionService;
import com.yelink.dfs.service.shift.ShiftCountService;
import com.yelink.dfs.service.shift.ShiftService;
import com.yelink.dfscommon.annotation.OperLog;
import com.yelink.dfscommon.constant.ParamException;
import com.yelink.dfscommon.pojo.ResponseData;
import com.yelink.dfscommon.utils.DateUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description: 班次配置
 * @time 2021/5/21 15:49
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/shift")
public class ShiftController extends BaseController {
    private ShiftService shiftService;
    private DeviceShiftGasConsumptionService deviceShiftGasConsumptionService;
    private DeviceShiftEnergyConsumptionService deviceShiftEnergyConsumptionService;
    private ShiftCountService shiftCountService;

    /**
     * 分页、模糊查询
     *
     * @param shiftType   班次类型
     * @param currentPage 当前页
     * @param size        当前页数
     * @return
     */
    @GetMapping("/list")
    public ResponseData list(@RequestParam(value = "shiftType", required = false) String shiftType,
                             @RequestParam(value = "current", required = false) Integer currentPage,
                             @RequestParam(value = "size", required = false) Integer size) {
        Page<ShiftEntity> page = shiftService.getList(shiftType, currentPage, size);
        return success(page);
    }
    @GetMapping("/list2")
    public ResponseData list2(ShiftSelectDTO dto) {
        return success(shiftService.list2(dto));
    }

    /**
     * 通过id查询班次配置信息
     *
     * @param id
     * @return
     */
    @GetMapping("/select/{id}")
    public ResponseData selectById(@PathVariable("id") Integer id) {
        ShiftEntity shiftEntity = shiftService.selectById(id);
        return success(shiftEntity);
    }

    /**
     * 新增班次配置
     *
     * @param entity
     * @param bindingResult
     * @return
     */
    @PostMapping("/insert")
    @OperLog(module = "班次配置管理", type = OperationType.ADD, desc = "新增了班次类型为#{shiftType}的班次配置")
    public ResponseData insert(@RequestBody ShiftEntity entity, BindingResult bindingResult) {
        if (bindingResult.hasErrors()) {
            throw new ParamException(bindingResult);
        }
        String username = getUsername();
        entity.setCreateBy(username);
        entity.setUpdateBy(username);
        if (shiftService.addEntity(entity)) {
            return success(entity);
        } else {
            return fail();
        }
    }

    /**
     * 修改班次配置
     *
     * @param entity
     * @param bindingResult
     * @return
     */
    @PutMapping("/update")
    @OperLog(module = "班次配置管理", type = OperationType.UPDATE, desc = "修改了班次类型为#{shiftType}的班次配置")
    public ResponseData update(@RequestBody @Validated({ShiftEntity.Update.class}) ShiftEntity entity, BindingResult bindingResult) {
        if (bindingResult.hasErrors()) {
            throw new ParamException(bindingResult);
        }
        String username = getUsername();
        entity.setUpdateBy(username);
        if (shiftService.updateEntity(entity)) {
            return success(entity);
        } else {
            return fail();
        }
    }

    /**
     * 通过Id删除班次配置
     *
     * @param id
     * @return
     */
    @DeleteMapping("/delete/{id}")
    @OperLog(module = "班次配置管理", type = OperationType.DELETE, desc = "删除了班次类型为#{shiftType}的班次配置")
    public ResponseData delete(@PathVariable Integer id) {
        if (shiftService.delEntity(id)) {
            return success();
        } else {
            return fail();
        }
    }

    /**
     * 查询当班耗电量
     *
     * @return
     */
    @PostMapping("/current/energy/consumption")
    public ResponseData getCurrentShiftEnergyConsumption(@RequestBody List<String> deviceCodeList) {
        Double consumption = deviceShiftEnergyConsumptionService.getCurrentShiftConsumption(deviceCodeList);
        return success(consumption);
    }

    /**
     * 查询当班耗气量
     *
     * @return
     */
    @PostMapping("/current/gas/consumption")
    public ResponseData getCurrentShiftGasConsumption(@RequestBody List<String> deviceCodeList) {
        Double consumption = deviceShiftGasConsumptionService.getCurrentShiftConsumption(deviceCodeList);
        return success(consumption);
    }

    /**
     * 查询当班产量
     *
     * @return
     */
    @PostMapping("/current/count")
    public ResponseData getCurrentShiftCount() {
        return success(shiftCountService.getCurrentShiftCount());
    }

    @GetMapping("/getShitByTime")
    public ResponseData getShitByTime(@RequestParam(required = false) String time) {
        Date timeParam;
        if(time == null) {
            timeParam = new Date();
        }else {
            timeParam = DateUtil.parseDateTime(time);
            if(timeParam == null) {
                return ResponseData.fail("时间格式异常");
            }
        }
        return success(shiftService.getShitByTime(timeParam));
    }
}

