package com.yelink.dfs.controller.screen;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yelink.dfs.common.BaseController;
import com.yelink.dfs.constant.Constant;
import com.yelink.dfs.constant.device.DevicesStateEnum;
import com.yelink.dfs.constant.screen.ScreenShowTypeEnum;
import com.yelink.dfs.constant.target.TargetAlarmLevelEnum;
import com.yelink.dfs.entity.alarm.dto.AlarmVo;
import com.yelink.dfs.entity.device.DeviceEntity;
import com.yelink.dfs.entity.manufacture.ProductionLineEntity;
import com.yelink.dfs.entity.model.FacilitiesEntity;
import com.yelink.dfs.entity.model.GridEntity;
import com.yelink.dfs.entity.model.ModelEntity;
import com.yelink.dfs.entity.model.dto.InstanceDTO;
import com.yelink.dfs.entity.screen.ConfigYzjScreenEntity;
import com.yelink.dfs.entity.screen.ScreenDeviceTagEntity;
import com.yelink.dfs.entity.screen.dto.GridLineDeviceDTO;
import com.yelink.dfs.entity.screen.dto.LineDeviceDTO;
import com.yelink.dfs.entity.screen.dto.ScreenDeviceDTO;
import com.yelink.dfs.entity.screen.dto.TagDeviceDTO;
import com.yelink.dfs.service.alarm.AlarmService;
import com.yelink.dfs.service.common.CommonService;
import com.yelink.dfs.service.device.DeviceService;
import com.yelink.dfs.service.impl.common.WorkPropertise;
import com.yelink.dfs.service.manufacture.ProductionLineService;
import com.yelink.dfs.service.model.FacilitiesService;
import com.yelink.dfs.service.model.GridService;
import com.yelink.dfs.service.model.ModelService;
import com.yelink.dfs.service.screen.ConfigYzjScreenService;
import com.yelink.dfs.service.screen.ScreenDeviceTagService;
import com.yelink.dfs.service.screen.SectionScreenService;
import com.yelink.dfs.service.screen.YangtzeRiverService;
import com.yelink.dfs.service.target.TargetModelService;
import com.yelink.dfscommon.pojo.ResponseData;
import com.yelink.dfscommon.vo.ScreenTargetModelVo;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @description: 处理1.6扬子江大屏接口
 * @time 2021/6/1 16:11
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/screens")
public class YangtzeRiverController extends BaseController {

    private AlarmService alarmService;
    private TargetModelService targetModelService;
    private DeviceService deviceService;
    private ProductionLineService lineService;
    private ConfigYzjScreenService configYzjScreenService;
    private YangtzeRiverService yangtzeRiverService;
    private ModelService modelService;
    private FacilitiesService facilitiesService;
    private GridService gridService;
    private ScreenDeviceTagService screenDeviceTagService;
    private WorkPropertise workPropertise;
    private SectionScreenService sectionScreenService;
    private CommonService commonService;


    private static final String LCM = "lcm";
    /**
     * 工段大屏的map
     */
    private Map<Integer, Function<ConfigYzjScreenEntity, Object>> sectionScreenMap;

    @PostConstruct
    public void sectionScreenInit() {
        sectionScreenMap.put(ScreenShowTypeEnum.DETAIL.getCode(), configYzjScreenEntity ->
                sectionScreenService.targetDetail(configYzjScreenEntity));
        sectionScreenMap.put(ScreenShowTypeEnum.CHART.getCode(), configYzjScreenEntity ->
                sectionScreenService.targetChart(configYzjScreenEntity));
        sectionScreenMap.put(ScreenShowTypeEnum.ALARM.getCode(), configYzjScreenEntity ->
                sectionScreenService.targetAlarm(configYzjScreenEntity));
        sectionScreenMap.put(ScreenShowTypeEnum.USER.getCode(), configYzjScreenEntity ->
                sectionScreenService.targetUser(configYzjScreenEntity));
        sectionScreenMap.put(ScreenShowTypeEnum.LAMP_ONE.getCode(), configYzjScreenEntity ->
                sectionScreenService.targetLampOne(configYzjScreenEntity));
        sectionScreenMap.put(ScreenShowTypeEnum.LAMP_TWO.getCode(), configYzjScreenEntity ->
                sectionScreenService.targetLampTwo(configYzjScreenEntity));
        sectionScreenMap.put(ScreenShowTypeEnum.DAY_ALARM_TOP_EIGHT.getCode(), configYzjScreenEntity ->
                sectionScreenService.targetDayAlarmTopEight(configYzjScreenEntity));
        sectionScreenMap.put(ScreenShowTypeEnum.MONTH_ALARM_TOP_EIGHT.getCode(), configYzjScreenEntity ->
                sectionScreenService.targetMonthAlarmTopEight(configYzjScreenEntity));
        sectionScreenMap.put(ScreenShowTypeEnum.WEEK_ALARM_TOP_EIGHT.getCode(), configYzjScreenEntity ->
                sectionScreenService.targetWeekAlarmTopEight(configYzjScreenEntity));
        sectionScreenMap.put(ScreenShowTypeEnum.CHATS.getCode(), configYzjScreenEntity ->
                sectionScreenService.targetChats(configYzjScreenEntity));
    }

    /**
     * 获取产线类型下的设备实例列表（绑定工位的设备）
     *
     * @param
     * @return
     */
    @GetMapping("/line/{modelId}")
    public ResponseData getInstanceDeviceByLine(@PathVariable(value = "modelId") Integer modelId) {
        List<ScreenDeviceDTO> screenDeviceDTOS = new ArrayList<>();
        // 根据产线模型Id获取产线实例列表
        LambdaQueryWrapper<ProductionLineEntity> lineWrapper = new LambdaQueryWrapper<>();
        lineWrapper.eq(ProductionLineEntity::getModelId, modelId);
        getTargetAndAlarmAndOeeByLine(screenDeviceDTOS, lineWrapper);
        return success(screenDeviceDTOS);
    }

    /**
     * 需求更改：将首页设备大屏进行根据tagId进行查询设备
     * 获取tag下边的设备
     *
     * @param
     * @return
     */
    @GetMapping("/tag/{tagId}")
    public ResponseData getDeviceById(@PathVariable(value = "tagId") Integer tagId) {
        List<ScreenDeviceDTO> screenDeviceDTOS = new ArrayList<>();
        // 如果tagId为空，则返回为空集合
        if (tagId == null) {
            return success(screenDeviceDTOS);
        }
        QueryWrapper<DeviceEntity> lineWrapper = new QueryWrapper<>();
        lineWrapper.lambda().eq(DeviceEntity::getTagId, tagId);
        List<DeviceEntity> deviceList = deviceService.list(lineWrapper);
        deviceList.forEach(deviceEntity -> {
            // 获取实例设备的指标、告警信息
            getTargetAndAlarmAndOee(screenDeviceDTOS, deviceEntity);
        });
        return success(screenDeviceDTOS);
    }

    /**
     * 设备大屏
     *
     * @param
     * @return
     */
    @GetMapping("/tag/device")
    public ResponseData getDeviceByIds(@RequestParam(required = false) String deviceIds) {
        List<ScreenDeviceDTO> screenDeviceDTOS = new ArrayList<>();
        QueryWrapper<DeviceEntity> lineWrapper = new QueryWrapper<>();
        lineWrapper.lambda().in(StringUtils.isNotBlank(deviceIds), DeviceEntity::getDeviceId, Arrays.stream(deviceIds.split(",")).collect(Collectors.toList()));
        List<DeviceEntity> deviceList = deviceService.list(lineWrapper);
        deviceList.forEach(deviceEntity -> {
            // 获取实例设备的指标、告警信息
            getTargetAndAlarmAndOee(screenDeviceDTOS, deviceEntity);
        });
        return success(screenDeviceDTOS);
    }

    /**
     * 获取车间类型下的设备实例列表(绑定车间的设备)
     *
     * @param
     * @return
     */
    @GetMapping("/grid/{modelId}")
    public ResponseData getInstanceDeviceByGrid(@PathVariable(value = "modelId") Integer modelId) {
        List<ScreenDeviceDTO> screenDeviceDTOS = new ArrayList<>();
        // 根据车间模型Id获取产线实例列表
        LambdaQueryWrapper<GridEntity> gridWrapper = new LambdaQueryWrapper<>();
        gridWrapper.eq(GridEntity::getModelId, modelId);
        getTargetAndAlarmAndOeeByGrid(screenDeviceDTOS, gridWrapper);
        return success(screenDeviceDTOS);
    }


    /**
     * 根据设备实例ID，获取该设备的指标、告警信息
     *
     * @param
     * @return
     */
    @GetMapping("/device/information/{deviceId}")
    public ResponseData getTargetAndAlarmAndOeeByDeviceId(@PathVariable(value = "deviceId") Integer deviceId) {
        DeviceEntity deviceEntity = deviceService.getById(deviceId);
        List<ScreenDeviceDTO> screenDeviceDTOS = new ArrayList<>();
        getTargetAndAlarmAndOee(screenDeviceDTOS, deviceEntity);
        return success(screenDeviceDTOS);
    }

    /**
     * 根据设备实例ID，获取该设备的指标、告警信息
     *
     * @param
     * @return
     */
    @GetMapping("/device/target/information/{deviceId}")
    public ResponseData getTargetAndAlarmByDeviceId(@PathVariable(value = "deviceId") Integer deviceId,
                                                    @RequestParam(value = "targetNames", required = false) String targetNames) {
        DeviceEntity deviceEntity = deviceService.getById(deviceId);
        if (deviceEntity == null) {
            return success();
        }
        // 获取模型名称
        ModelEntity modelEntity = modelService.lambdaQuery().eq(ModelEntity::getId, deviceEntity.getModelId()).one();
        // 获取所有的指标和对应的最新值
        List<ScreenTargetModelVo> modelVoList = targetModelService.getTargetModelListBySelect(deviceEntity, targetNames);
        // 查询该设备所有的告警信息
        List<AlarmVo> alarmVos = alarmService.getDeviceFaultAlarmList(deviceEntity.getDeviceId());
        // 告警汇总，777：轻微告警，888：一般告警，999：严重告警
        Map<Integer, Long> alarmMap = alarmVos.stream().collect(Collectors.groupingBy(AlarmVo::getAlarmLevel, Collectors.counting()));
        for (TargetAlarmLevelEnum anEnum : TargetAlarmLevelEnum.values()) {
            if (!alarmMap.containsKey(Integer.valueOf(anEnum.getLevelCode()))) {
                alarmMap.put(Integer.valueOf(anEnum.getLevelCode()), 0L);
            }
        }
        // 获取实时oee
        Double oee = yangtzeRiverService.getLastOeeByDeviceId(deviceEntity.getDeviceId(), new Date());
        ScreenDeviceDTO dto = ScreenDeviceDTO.builder()
                .deviceName(deviceEntity.getDeviceName())
                .deviceStateName(DevicesStateEnum.getNameByCode(deviceEntity.getState()))
                .modelName(modelEntity.getName())
                .modelImg(deviceEntity.getModelImg())
                .targetList(modelVoList)
                .oee(oee)
                .alarmVos(alarmVos)
                .alarmMap(alarmMap)
                .build();
        return success(dto);
    }

    /**
     * 根据设备实例ID，获取该设备的所有指标与数据
     *
     * @param
     * @return
     */
    @GetMapping("/device/target/value/{deviceId}")
    public ResponseData getTargetAndValueByDeviceId(@PathVariable(value = "deviceId") Integer deviceId,
                                                    @RequestParam(value = "targetNames", required = false) String targetNames) {
        DeviceEntity deviceEntity = deviceService.getById(deviceId);
        if (deviceEntity == null) {
            return success();
        }
        // 获取模型名称
        ModelEntity modelEntity = modelService.lambdaQuery().eq(ModelEntity::getId, deviceEntity.getModelId()).one();
        // 获取所有的指标和对应的最新值
        List<ScreenTargetModelVo> modelVoList = targetModelService.getTargetModelListBySelect(deviceEntity, targetNames);
        ScreenDeviceDTO dto = ScreenDeviceDTO.builder()
                .deviceName(deviceEntity.getDeviceName())
                .deviceStateName(DevicesStateEnum.getNameByCode(deviceEntity.getState()))
                .modelName(modelEntity.getName())
                .modelImg(deviceEntity.getModelImg())
                .targetList(modelVoList)
                .build();
        return success(dto);
    }

    private void getTargetAndAlarmAndOeeByLine(List<ScreenDeviceDTO> screenDeviceDTOS, LambdaQueryWrapper<ProductionLineEntity> lineWrapper) {
        //lcm的特殊处理,后期会删掉
        if (LCM.equals(workPropertise.getFactory())) {
            List<DeviceEntity> deviceList = this.lcmFilterDevice();
            deviceList.forEach(deviceEntity -> {
                // 获取实例设备的指标、告警信息
                getTargetAndAlarmAndOee(screenDeviceDTOS, deviceEntity);
            });
            return;
        }
        List<ProductionLineEntity> lineList = lineService.list(lineWrapper);
        lineList.forEach(productionLineEntity -> {
            // 根据产线实例ID获取实例设备列表
            List<DeviceEntity> deviceList = deviceService.deviceByLineId(productionLineEntity.getProductionLineId());
            deviceList.forEach(deviceEntity -> {
                // 获取实例设备的指标、告警信息
                getTargetAndAlarmAndOee(screenDeviceDTOS, deviceEntity);
            });
        });
    }

    /**
     * lcm需要展示的设备，目前先写死，后期会整改
     *
     * @return
     */
    private List<DeviceEntity> lcmFilterDevice() {
        LambdaQueryWrapper<DeviceEntity> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(DeviceEntity::getDeviceCode, "lcdzdslj01")
                .or(o -> o.eq(DeviceEntity::getDeviceCode, "lcddzqxj01"))
                .or(o -> o.eq(DeviceEntity::getDeviceCode, "qzdcogsb01"))
                .or(o -> o.eq(DeviceEntity::getDeviceCode, "qzdfogsb01"))
                .or(o -> o.eq(DeviceEntity::getDeviceCode, "qzdsdhydjj01"));
        return deviceService.list(lambdaQueryWrapper);

    }

    private void getTargetAndAlarmAndOeeByGrid(List<ScreenDeviceDTO> screenDeviceDTOS, LambdaQueryWrapper<GridEntity> gridWrapper) {
        List<GridEntity> gridList = gridService.list(gridWrapper);
        gridList.forEach(gridEntity -> {
            LambdaQueryWrapper<DeviceEntity> deviceWrapper = new LambdaQueryWrapper<>();
            deviceWrapper.eq(DeviceEntity::getGid, gridEntity.getGid());
            List<DeviceEntity> deviceEntities = deviceService.list(deviceWrapper);
            deviceEntities.forEach(deviceEntity -> {
                // 获取实例设备的指标、告警信息
                getTargetAndAlarmAndOee(screenDeviceDTOS, deviceEntity);
            });
        });
    }

    /**
     * 获取实例设备的指标、告警信息、OEE
     */
    private void getTargetAndAlarmAndOee(List<ScreenDeviceDTO> screenDeviceDTOS, DeviceEntity deviceEntity) {
        // 获取模型名称
        LambdaUpdateWrapper<ModelEntity> modelWrapper = new LambdaUpdateWrapper<>();
        modelWrapper.eq(ModelEntity::getId, deviceEntity.getModelId());
        ModelEntity modelEntity = modelService.getOne(modelWrapper);
        // 获取所有的指标和对应的最新值
        List<ScreenTargetModelVo> modelVoList = targetModelService.getTargetModelList(deviceEntity);
        // 查询该设备所有的告警信息
//        String join = String.join(",", String.valueOf(AlarmDealStateEnum.NOT_DEAL_ALARM.getStateCode()), String.valueOf(AlarmDealStateEnum.DEALING_ALARM.getStateCode()));
//        Page<AlarmEntity> alarmList = alarmService.getFaultAlarmList(null, null, join, null, null, deviceEntity.getDeviceId(), null, null);
        List<AlarmVo> alarmVos = alarmService.getDeviceFaultAlarmList(deviceEntity.getDeviceId());
        // 告警汇总，777：轻微告警，888：一般告警，999：严重告警
        Map<Integer, Long> alarmMap = alarmVos.stream().collect(Collectors.groupingBy(AlarmVo::getAlarmLevel, Collectors.counting()));
        for (TargetAlarmLevelEnum anEnum : TargetAlarmLevelEnum.values()) {
            if (!alarmMap.containsKey(Integer.valueOf(anEnum.getLevelCode()))) {
                alarmMap.put(Integer.valueOf(anEnum.getLevelCode()), 0L);
            }
        }
        // 获取该设备的OEE
//        Map<String, ScreenRecordDeviceOeeVo> oee = yangtzeRiverService.getOEE(deviceEntity.getDeviceId());
        // 获取实时oee
        Double oee = yangtzeRiverService.getLastOeeByDeviceId(deviceEntity.getDeviceId(), new Date());
        ScreenDeviceDTO dto = ScreenDeviceDTO.builder()
                .deviceName(deviceEntity.getDeviceName())
                .deviceStateName(DevicesStateEnum.getNameByCode(deviceEntity.getState()))
                .modelName(modelEntity.getName())
                .modelImg(deviceEntity.getModelImg())
                .targetList(modelVoList)
//                .oeeLine(oee)
                .oee(oee)
                .alarmVos(alarmVos)
                .alarmMap(alarmMap)
                .build();
        screenDeviceDTOS.add(dto);
    }


    /**
     * 大屏信息统一接口
     *
     * @return
     */
    @GetMapping("/information")
    public ResponseData screensInformation(@RequestParam(value = "eui") String eui,
                                           @RequestParam(value = "current") Integer current,
                                           @RequestParam(value = "size") Integer size) {
        Map<String, Object> resultMap = new HashMap<>(2);
        List<Map> resultList = new ArrayList<>();
        // 获取`dfs_config_yzj_screen` 表里的配置数据
        LambdaQueryWrapper<ConfigYzjScreenEntity> configQueryWrapper = new LambdaQueryWrapper<>();
        configQueryWrapper.eq(ConfigYzjScreenEntity::getEui, eui);
        Page<ConfigYzjScreenEntity> page = configYzjScreenService.page(new Page<>(current, size), configQueryWrapper);
        List<ConfigYzjScreenEntity> configYzjScreenEntityList = page.getRecords();
        Iterator<ConfigYzjScreenEntity> iterator = configYzjScreenEntityList.iterator();
        //拿到大屏标题
        String screenName = null;
        if (!CollectionUtils.isEmpty(configYzjScreenEntityList)) {
            screenName = configYzjScreenEntityList.get(0).getScreenName();
        }
        resultMap.put(Constant.SCREEN_NAME, screenName);
        resultMap.put("total", page.getTotal());
        resultMap.put("size", page.getSize());
        resultMap.put("current", page.getCurrent());
        while (iterator.hasNext()) {
            Map<String, Object> hashMap = new HashMap<>(2);
            ConfigYzjScreenEntity configYzjScreenEntity = iterator.next();
            Function<ConfigYzjScreenEntity, Object> result = sectionScreenMap.get(configYzjScreenEntity.getType());
            if (result != null) {
                hashMap.put(Constant.CONTENT, result.apply(configYzjScreenEntity));
            }
            hashMap.put(Constant.CONFIG_YZJ_SCREEN_ENTITY, configYzjScreenEntity);
            resultList.add(hashMap);
        }
        resultMap.put(Constant.CONTENT, resultList);
        return success(resultMap);
    }

    /**
     * 大屏Excel配置表导入(导入excel)
     *
     * @param
     * @return
     */
    @PostMapping("/import")
    public ResponseData screenImport(MultipartFile file) {
        try {
            configYzjScreenService.importFile(file);
        } catch (IOException e) {
            return fail(e.getMessage());
        }
        return success();
    }


    /**
     * 获取监控大屏的tab列表（产线列表、车间温湿度）
     *
     * @param
     * @return
     */
    @GetMapping("/monitor/screen/list")
    public ResponseData getMonitorScreenList() {
        if (LCM.equals(workPropertise.getFactory())) {
            return success(this.listLcdModel());
        }
        return success(yangtzeRiverService.getMonitorScreenList());
    }

    /**
     * lcd定制展示产线 TODO后期会删除
     *
     * @return
     */
    private List<ModelEntity> listLcdModel() {
        LambdaQueryWrapper<ModelEntity> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(ModelEntity::getName, "COG线");
        return modelService.list(lambdaQueryWrapper);
    }

    /**
     * 获取车间下所有的设备
     *
     * @param
     * @return
     */
    @GetMapping("/get/all/{modelId}")
    public ResponseData getAllInstanceDeviceOfGrid(@PathVariable(value = "modelId") Integer modelId) {
        List<ScreenDeviceDTO> dtos = new ArrayList<>();
        // 拿到绑定车间的设备
        LambdaQueryWrapper<GridEntity> gridWrapper = new LambdaQueryWrapper<>();
        gridWrapper.eq(GridEntity::getModelId, modelId);
        getTargetAndAlarmAndOeeByGrid(dtos, gridWrapper);
        //拿到车间下绑定工位的设备
        List<ModelEntity> modelListByPid = modelService.getModelListByPid(modelId);
        modelListByPid.forEach(i -> {
            LambdaQueryWrapper<ProductionLineEntity> lineWrapper = new LambdaQueryWrapper<>();
            lineWrapper.eq(ProductionLineEntity::getGid, i.getId());
            getTargetAndAlarmAndOeeByLine(dtos, lineWrapper);
        });
        return success(dtos);
    }

    /**
     * 获取所有车间实例-产线实例-设备实例
     *
     * @param
     * @return
     */
    @GetMapping("/get/all")
    public ResponseData getAllInstanceDeviceOfAllGrid() {
        List<GridLineDeviceDTO> gridLineDeviceDTOS = new ArrayList<>();
        //1.拿到所有的车间实例
        List<GridEntity> gridList = gridService.list();
        //2.根据车间实例拿到产线实例
        for (GridEntity grid : gridList) {
            ArrayList<LineDeviceDTO> lineDeviceDTOS = new ArrayList<>();
            List<ProductionLineEntity> lineList = lineService.getLineList(grid.getGcode());
            for (ProductionLineEntity line : lineList) {
                //3.根据产线实例拿到工位实例
                ArrayList<DeviceEntity> deviceEntities = new ArrayList<>();
                List<FacilitiesEntity> facilities = facilitiesService.getByLineId(line.getProductionLineId());
                for (FacilitiesEntity facilitiesEntity : facilities) {
                    //4.根据工位实例拿到设备实例
                    LambdaQueryWrapper<DeviceEntity> deviceWrapper = new LambdaQueryWrapper<>();
                    deviceWrapper.eq(DeviceEntity::getFid, facilitiesEntity.getFid());
                    List<DeviceEntity> deviceList = deviceService.list(deviceWrapper);
                    deviceEntities.addAll(deviceList);
                }
                //设置产线和设备信息
                LineDeviceDTO lineDeviceDTO = new LineDeviceDTO();
                lineDeviceDTO.setDeviceEntities(deviceEntities);
                lineDeviceDTO.setProductionLineEntity(line);
                lineDeviceDTOS.add(lineDeviceDTO);
            }
            //设置车间信息
            List<DeviceEntity> deviceEntities = deviceService.lambdaQuery()
                    .eq(DeviceEntity::getGid, grid.getGid()).isNull(DeviceEntity::getProductionLineId)
                    .list();
            GridLineDeviceDTO gridlineDTO = GridLineDeviceDTO.builder()
                    .gridEntity(grid)
                    .lineDeviceDTOS(lineDeviceDTOS)
                    .deviceEntities(deviceEntities)
                    .build();
            gridLineDeviceDTOS.add(gridlineDTO);
        }
        //加上没有绑定任何模型的设备
        List<DeviceEntity> deviceEntities = deviceService.lambdaQuery()
                .isNull(DeviceEntity::getGid).isNull(DeviceEntity::getProductionLineId)
                .list();
        gridLineDeviceDTOS.add(
                GridLineDeviceDTO.builder()
                .gridEntity(null)
                .lineDeviceDTOS(null)
                .deviceEntities(deviceEntities)
                .build()
        );
        return success(gridLineDeviceDTOS);
    }


    /**
     * 按tag名称展示设备指标
     *
     * @param
     * @return
     */
    @GetMapping("/get/device/target")
    public ResponseData getTarget(@RequestParam("tagId") Integer tagId) {
        //用来获取单个设备指标的集合
        ArrayList<ScreenDeviceDTO> screenDeviceDTOS = new ArrayList<>();
        //实际返回多个设备的指标集合
        ArrayList<ScreenDeviceDTO> backScreenDeviceDTOS = new ArrayList<>();
        LambdaQueryWrapper<DeviceEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(DeviceEntity::getTagId, tagId);
        List<DeviceEntity> list = deviceService.list(wrapper);
        list.forEach(deviceEntity -> {
            getTargetAndAlarmAndOee(screenDeviceDTOS, deviceEntity);
            //拿到单个设备的指标
            ScreenDeviceDTO screenDeviceDTO = screenDeviceDTOS.get(0);
            backScreenDeviceDTOS.add(screenDeviceDTO);
        });
        return success(backScreenDeviceDTOS);
    }

    /**
     * 编辑tag(增删改用一个接口)
     *
     * @param
     * @return
     */
    @PostMapping("/edit/tag/device")
    @Transactional(rollbackFor = Exception.class)
    public ResponseData deleteTagDevice(@RequestBody List<TagDeviceDTO> tagDeviceDTOS) {
        for (TagDeviceDTO tagDeviceDTO : tagDeviceDTOS) {
            List<DeviceEntity> deviceEntities = tagDeviceDTO.getDeviceEntities();
            ScreenDeviceTagEntity screenDeviceTagEntity = tagDeviceDTO.getScreenDeviceTagEntity();
            //如果前端没传tagId则代表是新增逻辑
            if (screenDeviceTagEntity.getId() == null) {
                //tag名称不让重复
                LambdaQueryWrapper<ScreenDeviceTagEntity> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(ScreenDeviceTagEntity::getTagName, screenDeviceTagEntity.getTagName());
                List<ScreenDeviceTagEntity> list = screenDeviceTagService.list(queryWrapper);
                if (list.size() != 0) {
                    return fail("tag的名称不能重复");
                }
                screenDeviceTagEntity.setCreateTime(new Date());
                screenDeviceTagService.save(screenDeviceTagEntity);
                deviceEntities.forEach(deviceEntity -> {
                    //增加过的设备关联tag
                    LambdaUpdateWrapper<DeviceEntity> wrapper = new LambdaUpdateWrapper<>();
                    wrapper.eq(DeviceEntity::getDeviceId, deviceEntity.getDeviceId());
                    wrapper.set(DeviceEntity::getTagId, screenDeviceTagEntity.getId());
                    deviceService.update(wrapper);
                });
            } else {
                //前端传了tag_id则是更新逻辑
                screenDeviceTagService.updateById(screenDeviceTagEntity);
                deviceEntities.forEach(deviceEntity -> {
                    DeviceEntity deviceEntity1 = deviceService.getById(deviceEntity.getDeviceId());
                    //如果device对应的tag_id字段没值则是勾选，有值则是删除逻辑
                    if (deviceEntity1.getTagId() == null) {
                        LambdaUpdateWrapper<DeviceEntity> wrapper = new LambdaUpdateWrapper<>();
                        wrapper.eq(DeviceEntity::getDeviceId, deviceEntity1.getDeviceId());
                        wrapper.set(DeviceEntity::getTagId, screenDeviceTagEntity.getId());
                        deviceService.update(wrapper);
                    } else {
                        LambdaUpdateWrapper<DeviceEntity> wrapper = new LambdaUpdateWrapper<>();
                        wrapper.eq(DeviceEntity::getDeviceId, deviceEntity1.getDeviceId());
                        wrapper.set(DeviceEntity::getTagId, null);
                        deviceService.update(wrapper);
                    }
                });
            }
        }
        return success();
    }

    /**
     * 获取勾选过的设备并按tag区分
     *
     * @param
     * @return
     */
    @GetMapping("/get/all/tag/device")
    public ResponseData getTagDevice() {
        ArrayList<TagDeviceDTO> tagDeviceDTOS = new ArrayList<>();
        List<ScreenDeviceTagEntity> screenDeviceTagEntities = screenDeviceTagService.list();
        screenDeviceTagEntities.forEach(screenDeviceTagEntity -> {
            TagDeviceDTO tagDeviceDTO = new TagDeviceDTO();
            LambdaQueryWrapper<DeviceEntity> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(DeviceEntity::getTagId, screenDeviceTagEntity.getId());
            List<DeviceEntity> deviceEntities = deviceService.list(wrapper);
            tagDeviceDTO.setScreenDeviceTagEntity(screenDeviceTagEntity);
            tagDeviceDTO.setDeviceEntities(deviceEntities);
            tagDeviceDTOS.add(tagDeviceDTO);
        });
        return success(tagDeviceDTOS);
    }

    /**
     * 获取所有的tag
     *
     * @param
     * @return
     */
    @GetMapping("/get/all/tag")
    public ResponseData getTag() {
        List<ScreenDeviceTagEntity> list = screenDeviceTagService.list();
        return success(list);
    }

    /**
     * 根据id删除tag（解除与设备的关联）
     *
     * @param
     * @return
     */
    @PostMapping("/delete/tag")
    @Transactional(rollbackFor = Exception.class)
    public ResponseData deleteTag(@RequestParam("tagId") Integer tagId) {
        screenDeviceTagService.removeById(tagId);
        LambdaUpdateWrapper<DeviceEntity> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(DeviceEntity::getTagId, tagId);
        wrapper.set(DeviceEntity::getTagId, null);
        deviceService.update(wrapper);
        return success();
    }

    /**
     * 获取产线和车间的实例(给Pad用的)
     *
     * @param
     * @return
     */
    @GetMapping("/line/grid/instance/list")
    public ResponseData getLineAndGridInstanceList() {
        List<InstanceDTO> dtos = gridService.getLineAndGridInstanceList();
        return success(dtos);
    }

}
