package com.yelink.dfs.controller.common.config;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yelink.dfs.common.BaseController;
import com.yelink.dfs.entity.common.CommonType;
import com.yelink.dfs.service.common.config.PageRedirectService;
import com.yelink.dfscommon.constant.InputTypeEnum;
import com.yelink.dfscommon.constant.PageRedirectTagEnum;
import com.yelink.dfscommon.dto.common.config.PageRedirectParamDTO;
import com.yelink.dfscommon.dto.common.config.PageRedirectSelectDTO;
import com.yelink.dfscommon.entity.dfs.PageRedirectEntity;
import com.yelink.dfscommon.pojo.ResponseData;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/4/28 15:01
 */
@RestController
@AllArgsConstructor
@RequestMapping("/page/redirect")
public class PageRedirectController extends BaseController {

    private PageRedirectService pageRedirectService;

    /**
     * 获取页面跳转列表
     *
     * @param selectDTO 查询条件
     * @return 页面跳转列表
     */
    @PostMapping("/page")
    public ResponseData getPage(@RequestBody PageRedirectSelectDTO selectDTO) {
        Page<PageRedirectEntity> page = pageRedirectService.getPage(selectDTO);
        return success(page);
    }

    /**
     * 新增页面跳转配置
     *
     * @return
     */
    @PostMapping("/insert")
    public ResponseData insert(@RequestBody PageRedirectEntity entity) {
        pageRedirectService.insert(entity);
        return success();
    }

    /**
     * 更新页面跳转配置
     *
     * @return
     */
    @PostMapping("/update")
    public ResponseData update(@RequestBody PageRedirectEntity entity) {
        pageRedirectService.update(entity);
        return success();
    }

    /**
     * 删除页面跳转配置
     *
     * @return
     */
    @PostMapping("/delete")
    public ResponseData delete(@RequestBody PageRedirectParamDTO paramDTO) {
        pageRedirectService.delete(paramDTO);
        return success();
    }

    /**
     * 页面跳转配置详情
     *
     * @return
     */
    @PostMapping("/detail")
    public ResponseData detail(@RequestBody PageRedirectParamDTO paramDTO) {
        return success(pageRedirectService.detail(paramDTO));
    }

    /**
     * 获标识类型枚举
     *
     * @param
     * @return
     */
    @GetMapping("/tag/list")
    public ResponseData getInputTypeEnum() {
        PageRedirectTagEnum[] values = PageRedirectTagEnum.values();
        List<CommonType> inputTypes = new ArrayList<>();
        for (PageRedirectTagEnum pageRedirectTagEnum : values) {
            inputTypes.add(CommonType.builder().type(pageRedirectTagEnum.getCode()).name(pageRedirectTagEnum.getName()).build());
        }
        return success(inputTypes);
    }

}
