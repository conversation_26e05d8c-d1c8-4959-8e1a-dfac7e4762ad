package com.yelink.dfs.controller.device;

import com.yelink.dfs.common.BaseController;
import com.yelink.dfs.constant.OperationType;
import com.yelink.dfs.entity.device.DeviceGroupEntity;
import com.yelink.dfs.entity.device.dto.DeviceTypeAndDeviceDTO;
import com.yelink.dfs.entity.target.dto.DeviceTypeOeeToYangzjDTO;
import com.yelink.dfs.service.device.DeviceGroupService;
import com.yelink.dfscommon.annotation.OperLog;
import com.yelink.dfscommon.constant.ParamException;
import com.yelink.dfscommon.pojo.ResponseData;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;
import java.util.List;

/**
 * 控制器
 *
 * <AUTHOR>
 * @Date 2021-07-08 15:54
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/groups")
public class DeviceGroupController extends BaseController {
    private DeviceGroupService groupService;

    /**
     * 查询设备组列表
     *
     * @param size
     * @param current
     * @return
     */
    @GetMapping("/list")
    public ResponseData list(@RequestParam(value = "size", required = false) Integer size,
                             @RequestParam(value = "current", required = false) Integer current) {
        return success(groupService.listByPage(size, current));
    }

    /**
     * 新增
     *
     * @param entity
     * @param bindingResult
     * @return
     */
    @PostMapping("/insert")
    @OperLog(module = "设备管理", type = OperationType.ADD, desc = "新增了设备组名称为#{groupName}的设备组")
    public ResponseData addOrder(@RequestBody @Validated({DeviceGroupEntity.Insert.class}) DeviceGroupEntity entity, BindingResult bindingResult) {
        if (bindingResult.hasErrors()) {
            throw new ParamException(bindingResult);
        }
        String username = getUsername();
        entity.setCreateBy(username);
        entity.setCreateTime(new Date());
        DeviceGroupEntity result = groupService.saveEntity(entity);
        return success(result);
    }

    /**
     * 修改
     *
     * @param entity
     * @param bindingResult
     * @return
     */
    @PutMapping("/update")
    @OperLog(module = "设备管理", type = OperationType.UPDATE, desc = "修改了设备组名称为#{groupName}的设备组")
    public ResponseData update(@RequestBody @Validated({DeviceGroupEntity.Update.class}) DeviceGroupEntity entity, BindingResult bindingResult) {
        if (bindingResult.hasErrors()) {
            throw new ParamException(bindingResult);
        }
        String username = getUsername();
        entity.setUpdateBy(username);
        entity.setUpdateTime(new Date());
        DeviceGroupEntity result = groupService.toUpdate(entity);
        return success(result);
    }


    /**
     * 通过Id删除
     *
     * @param id
     * @return
     */
    @DeleteMapping("/delete/{id}")
    @OperLog(module = "设备管理", type = OperationType.DELETE, desc = "删除了设备组名称为#{groupName}的设备组")
    public ResponseData delete(@PathVariable Integer id) {
        Boolean result = groupService.delete(id);
        if (result) {
            return success();
        }
        return fail();
    }

    /**
     * 获取所有设备类型列表以及对应的设备
     *
     * @return
     */
    @GetMapping("/device/type")
    public ResponseData getDeviceTypeList() {
        List<DeviceTypeAndDeviceDTO> list = groupService.getDeviceTypeList();
        return ResponseData.success(list);
    }

    @GetMapping("/device/oee")
    public ResponseData getDeviceTypeOee(@RequestParam(value = "deviceId") String deviceId) {
        List<DeviceTypeOeeToYangzjDTO> list = groupService.getDeviceTypeOee(deviceId);
        return ResponseData.success(list);
    }

    /**
     * 根据模型编码查询设备组列表
     *
     * @param modelCode
     * @return
     */
    @GetMapping("/list/by/model/code")
    public ResponseData listByModelCode(@RequestParam(value = "modelCode") String modelCode) {
        return success(groupService.listByModelCode(modelCode));
    }

}
