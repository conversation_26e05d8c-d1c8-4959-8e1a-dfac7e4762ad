package com.yelink.dfs.controller.weight.constant;

import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

/**
 * @description: 称重类型枚举
 * @author: shuang
 * @time: 2022/7/15
 */
@Getter
public enum WeightTypeEnum {

    /**
     * 称重操作类型枚举
     */
    FEEDING_TYPE("上料"),
    BLANKING_TYPE("下料"),
    WASTE_TYPE("废料");

    @JsonValue
    private final String name;

    WeightTypeEnum(String name) {
        this.name = name;
    }

    @Override
    public String toString() {
        return this.name;
    }

}
