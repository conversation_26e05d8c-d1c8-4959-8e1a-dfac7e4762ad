package com.yelink.dfs.controller.notice;

import com.yelink.dfs.common.BaseController;
import com.yelink.dfs.service.notice.ManualNoticeService;
import com.yelink.dfscommon.pojo.ResponseData;
import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 手动发消息通知
 *
 * <AUTHOR>
 * @Date 2021/8/18 16:16
 */
@Slf4j
@RestController
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@RequestMapping("/manual/notice")
public class ManualNoticeController extends BaseController {

    private ManualNoticeService manualNoticeService;


    /**
     * 精制通知--人工叫料
     *
     * @param lineName    产线名称
     * @param stationName 工位名称
     * @return
     */
    @GetMapping("/call/material")
    public ResponseData callMaterial(@RequestParam(value = "lineName") String lineName,
                                     @RequestParam(value = "stationName", required = false) String stationName) {
        manualNoticeService.callMaterial(lineName, stationName);
        return ResponseData.success();
    }

    /**
     * 精制通知(根据NoticeType进行推送)
     *
     * @param noticeType    类型
     * @param message 消息
     * @return
     */
    @GetMapping("/call/notice/type")
    public ResponseData callBynoticeType(@RequestParam(value = "noticeType") String noticeType,
                                     @RequestParam(value = "message", required = false) String message) {
        manualNoticeService.callBynoticeType(noticeType, message);
        return ResponseData.success();
    }


}
