package com.yelink.dfs.controller.product;

import cn.hutool.core.util.RandomUtil;
import com.asyncexcel.core.exporter.DataExportParam;
import com.asyncexcel.core.model.ExcelTask;
import com.asyncexcel.springboot.ExcelService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yelink.dfs.common.BaseController;
import com.yelink.dfs.constant.Constant;
import com.yelink.dfs.constant.OperationType;
import com.yelink.dfs.constant.RedisKeyPrefix;
import com.yelink.dfs.constant.product.MaterialSortEnum;
import com.yelink.dfs.constant.product.MaterialStateEnum;
import com.yelink.dfs.entity.common.CommonState;
import com.yelink.dfs.entity.common.CommonType;
import com.yelink.dfs.entity.common.ModelTypeDTO;
import com.yelink.dfs.entity.common.ModelUploadFileEntity;
import com.yelink.dfs.entity.common.PreviewTemplateEntity;
import com.yelink.dfs.entity.order.vo.OrderMaterialVO;
import com.yelink.dfs.entity.product.BomEntity;
import com.yelink.dfs.entity.product.BomRawMaterialEntity;
import com.yelink.dfs.entity.product.MaterialEntity;
import com.yelink.dfs.entity.product.dto.MaterialBatchUpdateInspectDemandDTO;
import com.yelink.dfs.entity.product.dto.MaterialExcelDTO;
import com.yelink.dfs.entity.product.dto.MaterialRelateDataDTO;
import com.yelink.dfs.entity.product.dto.MaterialsSelectDTO;
import com.yelink.dfs.entity.supplier.SupplierMaterialEntity;
import com.yelink.dfs.open.v1.aksk.ams.ExtProductOrderInterface;
import com.yelink.dfs.provider.FastDfsClientService;
import com.yelink.dfs.service.common.ImportProgressService;
import com.yelink.dfs.service.common.ModelUploadFileService;
import com.yelink.dfs.service.impl.export.handler.MaterialExportHandler;
import com.yelink.dfs.service.order.WorkOrderService;
import com.yelink.dfs.service.product.BomService;
import com.yelink.dfs.service.product.MaterialService;
import com.yelink.dfs.service.supplier.SupplierMaterialService;
import com.yelink.dfscommon.annotation.OperLog;
import com.yelink.dfscommon.common.excel.BusinessCodeEnum;
import com.yelink.dfscommon.constant.ParamException;
import com.yelink.dfscommon.constant.ResponseException;
import com.yelink.dfscommon.dto.ApproveBatchDTO;
import com.yelink.dfscommon.dto.ams.open.order.ProductOrderSelectOpenDTO;
import com.yelink.dfscommon.entity.ams.ProductOrderEntity;
import com.yelink.dfscommon.entity.ams.ProductOrderMaterialEntity;
import com.yelink.dfscommon.entity.common.BatchChangeStateDTO;
import com.yelink.dfscommon.entity.dfs.DictEntity;
import com.yelink.dfscommon.entity.dfs.barcode.PrintDTO;
import com.yelink.dfscommon.pojo.PageResult;
import com.yelink.dfscommon.pojo.ResponseData;
import com.yelink.dfscommon.utils.DateUtil;
import com.yelink.dfscommon.utils.EasyExcelUtil;
import com.yelink.dfscommon.utils.ExcelTemplateImportUtil;
import com.yelink.dfscommon.utils.ExcelUtil;
import com.yelink.dfscommon.utils.JacksonUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.io.Resource;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.core.io.support.ResourcePatternResolver;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @description 物料表
 * @Date 2021/4/14
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/materials")
public class MaterialController extends BaseController {
    private final MaterialService materialService;
    private final SupplierMaterialService supplierMaterialService;
    private final ExtProductOrderInterface extProductOrderInterface;
    private final ExcelService excelService;
    private final ImportProgressService importProgressService;
    private final ModelUploadFileService modelUploadFileService;
    private final FastDfsClientService fastDfsClientService;
    private final RedisTemplate redisTemplate;
    private final BomService bomService;
    private final WorkOrderService workOrderService;
    /**
     * 查询物料列表
     *
     * @param materialsDTO
     * @return
     */
    @PostMapping("/list")
    public ResponseData list(@RequestBody MaterialsSelectDTO materialsDTO) {
        Page<MaterialEntity> list = materialService.getList(materialsDTO);
        return success(list);
    }

    /**
     * 通过工序查询物料名称列表
     *
     * @param procedureId 工序id
     * @return
     */
    @GetMapping("/name/procedure/list")
    public ResponseData selectNameByProcedure(@RequestParam(value = "procedureId") Integer procedureId) {
        List<String> list = materialService.selectNameByProcedure(procedureId);
        return success(list);
    }

    /**
     * 通过工作中心查询物料名称列表
     *
     * @param workCenterId 工作中心id
     * @return
     */
    @GetMapping("/name/model/list")
    public ResponseData selectNameByModelId(@RequestParam(value = "workCenterId") Integer workCenterId) {
        List<String> list = materialService.selectNameByWorkCenterId(workCenterId);
        return success(list);
    }

    /**
     * 通过工序查询物料编码列表
     *
     * @param procedureId 工序id
     * @return
     */
    @GetMapping("/code/procedure/list")
    public ResponseData selectCodeByProcedure(@RequestParam(value = "procedureId") Integer procedureId) {
        List<String> list = materialService.selectCodeByProcedure(procedureId);
        return success(list);
    }

    /**
     * 通过工作中心id查询物料编码列表
     *
     * @param workCenterId 工序id
     * @return
     */
    @GetMapping("/code/model/list")
    public ResponseData selectCodeByModelId(@RequestParam(value = "workCenterId") Integer workCenterId) {
        List<String> list = materialService.selectCodeByWorkCenterId(workCenterId);
        return success(list);
    }

    /**
     * 新增物料(成品、半成品、原料)，
     * 如果为成品/半成品，则同时新增成品/半成品与原料的绑定关系
     *
     * @param materialEntity 物料对象
     * @param bindingResult
     * @return
     */
    @PostMapping("/insert")
    @OperLog(module = "产品管理", type = OperationType.ADD, desc = "新增了编码为#{code}的物料")
    public ResponseData add(@RequestBody @Valid MaterialEntity materialEntity, BindingResult bindingResult) {
        if (bindingResult.hasErrors()) {
            throw new ParamException(bindingResult);
        }
        materialEntity.setCreateBy(getUsername());
        materialEntity.setCreateTime(new Date());
        materialEntity.setUpdateBy(getUsername());
        materialEntity.setUpdateTime(new Date());
        if (materialService.saveEntity(materialEntity)) {
            return success(materialEntity);
        }
        return fail();
    }

    /**
     * 新增生效物料
     *
     * @param materialEntity 物料对象
     * @param bindingResult
     * @return
     */
    @PostMapping("/insert/released")
    @OperLog(module = "产品管理", type = OperationType.ADD, desc = "新增了编码为#{code}的物料")
    public ResponseData addReleasedEntity(@RequestBody @Valid MaterialEntity materialEntity, BindingResult bindingResult) {
        if (bindingResult.hasErrors()) {
            throw new ParamException(bindingResult);
        }
        materialEntity.setCreateBy(getUsername());
        materialEntity.setUpdateBy(getUsername());
        materialEntity.setCreateTime(new Date());
        materialEntity.setUpdateTime(new Date());
        materialService.saveReleasedEntity(materialEntity);
        return success(materialEntity);
    }

    /**
     * 更新物料，同时更新成品/半成品与原料的绑定关系
     *
     * @param materialEntity 物料对象
     * @return
     */
    @PutMapping("/update")
    @OperLog(module = "产品管理", type = OperationType.UPDATE, desc = "修改了编码为#{code}的物料")
    public ResponseData update(@RequestBody @Valid MaterialEntity materialEntity) {
        materialEntity.setUpdateTime(new Date());
        materialEntity.setUpdateBy(getUsername());
        if (materialService.updateEntity(materialEntity)) {
            return success(materialEntity);
        }
        return fail();
    }

    /**
     * 删除物料对象，如果为父物料，则同时删除绑定的原料
     *
     * @param id 物料ID
     * @return
     */
    @DeleteMapping("/delete/{id}")
    @OperLog(module = "产品管理", type = OperationType.DELETE, desc = "删除了编码为#{code}的物料")
    public ResponseData delete(@PathVariable Integer id) {
        MaterialEntity entity = materialService.getById(id);
        materialService.removeEntityById(id);
        return success(entity);
    }

    /**
     * 查询物料详细信息 (接口废弃，使用下面的接口)
     *
     * @param code 物料code
     * @return
     */
    @GetMapping("/detail/{code}")
    @Deprecated
    public ResponseData detail(@PathVariable(value = "code") String code) {
        return ResponseData.success(materialService.selectByMaterialCode(code));
    }

    /**
     * 查询物料详细信息
     *
     * @param code 物料code
     * @return
     */
    @GetMapping("/detail/code")
    public ResponseData detailByCode(@RequestParam(value = "code") String code) {
        return ResponseData.success(materialService.selectByMaterialCode(code));
    }

    /**
     * 查询物料详细信息列表
     *
     * @param codeList 物料code
     * @return
     */
    @PostMapping("/detail/code/list")
    public ResponseData detailByCodeList(@RequestBody List<String> codeList) {
        List<BomRawMaterialEntity> bomRawMaterialEntities = new ArrayList<>();
        if(CollectionUtils.isEmpty(codeList)){
            return success(bomRawMaterialEntities);
        }
        codeList=codeList.stream().distinct().collect(Collectors.toList());
        for(String code:codeList){
            MaterialEntity materialEntity = materialService.selectByMaterialCode(code);
            if(materialEntity==null){
                continue;
            }
            BomRawMaterialEntity bomRawMaterialEntity = BomRawMaterialEntity
                    .builder().code(materialEntity.getCode()+"material").stockQuantity(materialEntity.getStockQuantity()).materialFields(materialEntity.getMaterialFields()).build();
            BomEntity bomEntity=bomService.getLatestBomByCode(materialEntity.getCode(),null);
            if(bomEntity==null){continue;}
//            List<BomRawMaterialEntity> bomRawMaterialEntities1=bomService.getMultiLevelBom(materialEntity.getCode(),null,bomEntity.getCode());
            BomEntity bom = workOrderService.getMultiLevelBom(materialEntity.getCode(), null);
//            if(bomEntityTemp==null){
//                continue;
//            }
//            BomRawMaterialEntity bomRawMaterialEntityTemp=BomRawMaterialEntity
//                    .builder().code(bomEntityTemp.getCode()+"bom").materialFields(bomEntityTemp.getMaterialFields()).children(bomEntityTemp.getBomRawMaterialEntities())
//                    .build();
            bomRawMaterialEntity.setChildren(bom.getBomRawMaterialEntities());
            bomRawMaterialEntities.add(bomRawMaterialEntity);
        }
        return ResponseData.success(bomRawMaterialEntities);
    }


    /**
     * 获取物料状态
     */
    @GetMapping("/state")
    public ResponseData getMaterialState() {
        List<CommonState> list = new ArrayList<>();
        MaterialStateEnum[] values = MaterialStateEnum.values();
        for (MaterialStateEnum value : values) {
            CommonState type = new CommonState();
            type.setCode(value.getCode());
            type.setName(value.getName());
            list.add(type);
        }
        return success(list);
    }

    /**
     * 数据分析
     * 如果需要停用、废除某个物料,需查询所有关联该物料的数据
     * 相关模块：BOM、销售订单、采购需求单、采购清单、出入库单、供应商物料清单、生产工单、领料单
     *
     * @param code 物料编号
     */
    @GetMapping("/analysis/{code}")
    public ResponseData judgeIsExistRelateData(@PathVariable(value = "code") String code) {
        List<MaterialRelateDataDTO> list = materialService.getRelateData(code);
        return success(list);
    }

    /**
     * 获取物料类型数据字典
     */
    @GetMapping("/material/type/list")
    public ResponseData getMaterialType() {
        List<DictEntity> list = materialService.getMaterialType();
        return success(list);
    }

    /**
     * 获取物料分类
     */
    @GetMapping("/sort")
    public ResponseData getMaterialSort() {
        List<ModelTypeDTO> list = new ArrayList<>();
        MaterialSortEnum[] values = MaterialSortEnum.values();
        for (MaterialSortEnum value : values) {
            ModelTypeDTO type = new ModelTypeDTO();
            type.setCode(value.getCode());
            type.setName(value.getName());
            list.add(type);
        }
        return success(list);
    }

    /**
     * 下载物料导入默认模板
     */
    @GetMapping("/export/default/template")
    public void exportExcelDefaultTemplate(HttpServletResponse response) throws Exception {
        materialService.downloadDefaultTemplate("classpath:template/materialTemplate.xlsx", response, "物料默认模板" + Constant.XLSX);
    }

    /**
     * 导入 自定义物料导入模板
     */
    @PostMapping("/import/template")
    @OperLog(module = "导入日志", type = OperationType.ADD, desc = "导入物料自定义模板")
    public ResponseData importExcelTemplate(MultipartFile file) {
        materialService.uploadCustomTemplate(file, this.getUsername());
        return success();
    }

    /**
     * 用户导入的 自定义物料导入模板 下载
     */
    @GetMapping("/export/template")
    public void exportExcelTemplate(HttpServletResponse response) throws IOException {
        //找到导出的模板
        byte[] bytes = materialService.downloadImportTemplate();
        ExcelTemplateImportUtil.responseToClient(response, bytes, "物料数据导入模板" + Constant.XLSX);
    }

    /**
     * 导入管理：下载自定义导入模板
     *
     * @param response
     */
    @GetMapping("/export/custom/template")
    public void exportCustomExcel(HttpServletResponse response) throws IOException {
        //找到导出的模板
        InputStream inputStream = materialService.downloadCustomImportTemplate();
        ExcelTemplateImportUtil.responseToClient(response, inputStream, "物料自定义数据导入模板" + com.yelink.dfscommon.constant.Constant.XLSX);
    }

    /**
     * 导入excel
     */
    @PostMapping("/import")
    public ResponseData importMaterialExcel(MultipartFile file) throws IOException {
        //1、判断导入文件的合法性
        ExcelUtil.checkImportExcelFile(file);
        String importProgressKey = RedisKeyPrefix.MATERIAL_IMPORT_PROGRESS + DateUtil.format(new Date(), DateUtil.yyyyMMddHHmmss_FORMAT) + "_" + RandomUtil.randomString(2);
        materialService.sycImportData(file.getOriginalFilename(), file.getInputStream(), getUsername(), importProgressKey);
        return success(importProgressKey);
    }

    /**
     * 获取物料导入的进度
     */
    @GetMapping("/import/progress")
    public ResponseData getMaterialCompletenessProgress() {
        return success(materialService.importProgress());
    }


    /**
     * 导出 - 异步
     */
    @PostMapping("/syc/exports")
    public ResponseData export(@RequestBody MaterialsSelectDTO materialsDTO) {
        DataExportParam<MaterialsSelectDTO> dataExportParam = new DataExportParam<>();
        dataExportParam.setExportFileName(BusinessCodeEnum.MATERIAL_REPORT.getCodeName());
        dataExportParam.setLimit(10000);
        dataExportParam.setBusinessCode(BusinessCodeEnum.MATERIAL_REPORT.name());
        dataExportParam.setCreateUserCode(getUsername());

        Map<String, Object> parameters = new HashMap<>();
        parameters.put(MaterialsSelectDTO.class.getName(), materialsDTO);
        dataExportParam.setParameters(parameters);

        Long taskId = excelService.doExport(dataExportParam, MaterialExportHandler.class);
        return success(taskId);
    }

    /**
     * 物料列表导出 - 下载默认模板
     * 查询最新的10条物料数据导出数据源excel
     *
     * @param response
     * @throws IOException
     */
    @GetMapping("/default/export/template")
    public void listDefaultExportTemplate(HttpServletResponse response) throws IOException {
        MaterialsSelectDTO selectDTO = new MaterialsSelectDTO();
        selectDTO.setCurrent(1);
        selectDTO.setSize(10);
        Page<MaterialEntity> page = materialService.getList(selectDTO);
        List<MaterialExcelDTO> list = materialService.convertToExportDTO(page.getRecords());
        EasyExcelUtil.export(response, "物料默认导出模板", "数据源", list, MaterialExcelDTO.class);
    }

    /**
     * 物料列表导出 - 自定义模板上传
     * 接收文件并清空名称为数据源sheet的内容
     *
     * @param file
     * @return
     */
    @PostMapping("/upload/list/export/template")
    public ResponseData uploadListExportTemplate(MultipartFile file) throws IOException {
        materialService.uploadListExportTemplate(file, getUsername());
        return success();
    }


    /**
     * 物料列表导出 - 物料模板自定义模板下载
     * 分页查询并填写数据源sheet的内容
     *
     * @param response
     * @return
     * @throws IOException
     */
    @PostMapping("/download/list/export/template")
    public ResponseData downloadListExportTemplate(HttpServletResponse response, Integer id) throws IOException {
        //获取最新的十条数据源
        MaterialsSelectDTO selectDTO = new MaterialsSelectDTO();
        selectDTO.setCurrent(1);
        selectDTO.setSize(10);
        Page<MaterialEntity> page = materialService.getList(selectDTO);
        List<MaterialExcelDTO> list = materialService.convertToExportDTO(page.getRecords());
        //获取模板文件
        ModelUploadFileEntity uploadFile = modelUploadFileService.getById(id);
        byte[] bytes = fastDfsClientService.getFileStream(uploadFile.getFileAddress());
        //下载模板
        EasyExcelUtil.writeToExcelTemplate(response, list, "数据源", uploadFile.getFileName(), bytes, MaterialExcelDTO.class);
        return success();
    }

    /**
     * 导出 - 分页查询当前导出任务列表
     */
    @GetMapping("/export/task/page")
    public ResponseData taskPage(@RequestParam(required = false, defaultValue = "20") Integer pageSize,
                                 @RequestParam(required = false, defaultValue = "1") Integer currentPage) {
        ExcelTask excelTask = new ExcelTask();
        excelTask.setBusinessCode(BusinessCodeEnum.MATERIAL_REPORT.name());
        return success(excelService.listPage(excelTask, currentPage, pageSize));
    }

    /**
     * 查询 导出进度
     *
     * @return
     */
    @GetMapping("/export/task/{taskId}")
    public ResponseData exportExcel(@PathVariable Long taskId) {
        ExcelTask excelTask = materialService.taskById(taskId);
        return success(excelTask);
    }

    /**
     * 审批
     */
    @GetMapping("/examine/approve")
    public ResponseData examineOrApprove(@RequestParam(value = "approvalStatus") Integer approvalStatus,
                                         @RequestParam(value = "approvalSuggestion", required = false) String approvalSuggestion,
                                         @RequestParam(value = "id") Integer id) {
        String username = getUsername();
        materialService.examineOrApprove(approvalStatus, approvalSuggestion, id, username);
        return success();
    }

    /**
     * 批量审批
     *
     * @return
     */
    @PostMapping("/approve/batch")
    public ResponseData approveBatch(@RequestBody ApproveBatchDTO dto) {
        dto.setActualApprover(getUsername());
        materialService.approveBatch(dto);
        return success();
    }

    /**
     * 物料批量编辑
     */
    @PostMapping("/batch/update/state")
    public ResponseData batchUpdate(@RequestBody BatchChangeStateDTO batchApprovalDTO) {
        if (batchApprovalDTO.getIds() == null) {
            return fail("编辑的单据id为空");
        }
        String username = getUsername();
        Boolean ret = materialService.batchUpdateState(batchApprovalDTO, username);
        return ResponseData.success(ret);
    }


    /**
     * 物料批量导入附件前预览(解压后)
     *
     * @param file
     * @return
     */
    @PostMapping("/import/preview")
    public ResponseData previewBeforeImport(@RequestBody MultipartFile file) throws Exception {
        PreviewTemplateEntity previewTemplateEntity = materialService.getFilesNameOrPath(file);
        return success(previewTemplateEntity);
    }

    /**
     * 保存导入的物料附件
     *
     * @param file
     * @return
     */
    @PostMapping("/save/import/file")
    public ResponseData saveImportMaterial(@RequestBody MultipartFile file) throws Exception {
        String username = getUsername();
        return ResponseData.success(materialService.saveMaterialImport(file, username));
    }

    /**
     * 根据工单获取半成品物料集合
     *
     * @param materialCode
     * @return
     */
    @GetMapping("/semi/products")
    public ResponseData getSemiProducts(String materialCode) {
        Set<MaterialEntity> materialEntities = materialService.getSemiProducts(materialCode);
        return success(materialEntities);
    }


    /**
     * 查询生产订单 物料名称列表 模糊查询
     *
     * @param materialName 物料名称
     * @param materialCode 物料编码
     * @return
     */
    @GetMapping("/product/order/material")
    public ResponseData selectProductOrderMaterial(@RequestParam(value = "materialName", required = false) String materialName,
                                                   @RequestParam(value = "materialCode", required = false) String materialCode) {
        List<String> materialCodeList = new ArrayList<>();
        if (StringUtils.isNotEmpty(materialName)) {
            List<String> CodeList = materialService.lambdaQuery()
                    .select(MaterialEntity::getCode)
                    .like(MaterialEntity::getName, materialName)
                    .list().stream().map(MaterialEntity::getCode).collect(Collectors.toList());
            materialCodeList.addAll(CodeList);
        }
        if (StringUtils.isNotBlank(materialCode)) {
            List<String> CodeList = materialService.lambdaQuery()
                    .select(MaterialEntity::getCode)
                    .like(MaterialEntity::getCode, materialCode)
                    .list().stream().map(MaterialEntity::getCode).collect(Collectors.toList());
            materialCodeList.addAll(CodeList);
        }
        String materialCodes = StringUtils.join(materialCodeList, Constant.SEP);
        PageResult<ProductOrderEntity> pageResult = extProductOrderInterface.getPage(ProductOrderSelectOpenDTO.builder().materialCode(materialCodes).build());

        if (CollectionUtils.isEmpty(pageResult.getRecords())) {
            return success(new ArrayList<>());
        }

        List<OrderMaterialVO> vos = new ArrayList<>();
        List<String> codes = pageResult.getRecords().stream().map(ProductOrderEntity::getProductOrderMaterial).map(ProductOrderMaterialEntity::getMaterialCode).collect(Collectors.toList());
        Map<String, String> codeNameMap = materialService.lambdaQuery()
                .select(MaterialEntity::getCode, MaterialEntity::getName)
                .in(MaterialEntity::getCode, codes)
                .list()
                .stream().collect(Collectors.toMap(MaterialEntity::getCode, MaterialEntity::getName));
        pageResult.getRecords().forEach(record -> vos.add(OrderMaterialVO.builder()
                .materialCode(record.getProductOrderMaterial().getMaterialCode())
                .materialName(codeNameMap.get(record.getProductOrderMaterial().getMaterialCode()))
                .build()));
        return success(vos.stream().distinct().collect(Collectors.toList()));
    }

    /**
     * 根据物料编码和物料规格查询物料列表
     *
     * @param materialCodes
     * @param materialStandard
     * @return
     */
    @GetMapping("/lists")
    public ResponseData getList(@RequestBody List<String> materialCodes,
                                @RequestParam(value = "materialStandard", required = false) String materialStandard) {
        List<MaterialEntity> materialEntityList = materialService.getList(materialCodes, materialStandard);
        return success(materialEntityList);
    }


    /**
     * 采购收货小程序：
     * 根据物料code模糊查询, 并返回对应的供应商物料code
     */
    @GetMapping("/list/material/supplier")
    public ResponseData listMaterial(@RequestParam String materialCode,
                                     @RequestParam(required = false) String supplierCode) {
        MaterialEntity material = materialService.lambdaQuery()
                .eq(MaterialEntity::getCode, materialCode)
                .one();
        if (Objects.isNull(material)) {
            log.error("未找到该编码对应物料, {}", materialCode);
            throw new ResponseException("未找到该物料编码对应物料");
        }
        materialService.setMaterialTypeName(Stream.of(material).collect(Collectors.toList()));
        if (StringUtils.isNotEmpty(supplierCode)) {
            // 查询供应商物料编码
            List<SupplierMaterialEntity> supplierMaterials = supplierMaterialService.lambdaQuery()
                    .eq(SupplierMaterialEntity::getSupplierCode, supplierCode)
                    .eq(SupplierMaterialEntity::getMaterialId, material.getId())
                    .list();
            Map<Integer, String> materialIdSupplierMaterialCodeMap = supplierMaterials.stream().collect(Collectors.toMap(SupplierMaterialEntity::getMaterialId, SupplierMaterialEntity::getSupplierMaterialCode));
            material.setSupplierMaterialCode(materialIdSupplierMaterialCodeMap.get(material.getId()));
        }
        return ResponseData.success(material);
    }

    /**
     * 物料标签打印前校验 标签是否已打印
     *
     * @param
     * @return
     */
    @PostMapping("/judge/print")
    public ResponseData judgeBeforePrint(@RequestBody MaterialsSelectDTO selectDTO) {
        materialService.judgeBeforePrint(selectDTO);
        return success();
    }

    /**
     * 物料标签打印
     *
     * @param selectDTO 查询条件
     * @return
     */
    @OperLog(module = "物料标签", type = OperationType.PRINT, desc = "打印了编码为#{printCodes}的物料")
    @PostMapping("/print")
    public ResponseData print(@RequestBody MaterialsSelectDTO selectDTO) {
        log.info("{}调用了打印接口：{}", getUsername(), JacksonUtil.toJSONString(selectDTO));
        PrintDTO print = materialService.print(selectDTO);
        return success(print);
    }

    /**
     * 物料检验默认导入模板
     */
    @GetMapping("/inspect/default/template")
    public void downloadMaterialDefaultTemplate(HttpServletResponse response) throws IOException {
        //找到导出的模板
        byte[] bytes = ExcelUtil.exportDefaultExcel("classpath:template/materialInspectImportTemplate.xlsx");
        ExcelTemplateImportUtil.responseToClient(response, bytes, "物料检验默认导入模板" + Constant.XLSX);
    }

    /**
     * 物料检验导入
     */
    @PostMapping("/inspect/import")
    public ResponseData importMaterialInspectExcel(MultipartFile file) throws IOException {
        //1、判断导入文件的合法性
        ExcelUtil.checkImportExcelFile(file);
        materialService.importMaterialInspectExcel(file.getOriginalFilename(), file.getInputStream(), getUsername());
        return success();
    }

    /**
     * 获取物料检验导入进度
     */
    @GetMapping("/inspect/import/progress")
    public ResponseData getImportInspectExcelProgress() {
        return success(importProgressService.importProgress(RedisKeyPrefix.MATERIAL_INSPECT_IMPORT_PROGRESS));
    }

    /**
     * 物料附件导入：默认导入模板下载
     */
    @GetMapping("/export/procedure/file/default/template")
    public void downloadFileDefaultTemplate(HttpServletResponse response) throws IOException {
        ResourcePatternResolver resolver = new PathMatchingResourcePatternResolver();
        Resource resource = resolver.getResource("classpath:template/materialFileTemplate.zip");
        ExcelTemplateImportUtil.responseToClient(response, resource.getInputStream(), "物料附件默认导入模板" + Constant.ZIP);
    }

    /**
     * 校验是否能废弃
     * @param materialId 物料id
     * @return 进度条key
     */
    @GetMapping("/delete/check")
    public ResponseData deleteCheck(@RequestParam(value = "materialId") Integer materialId) {
        return success(materialService.deleteCheck(materialId));
    }

    /**
     * 废弃校验进度
     * @param key redisKey
     * @return 进度
     */
    @GetMapping("/delete/check/progress")
    public ResponseData deleteCheckProgress(@RequestParam(value = "key") String key) {
        return success(materialService.deleteCheckProgress(key));
    }

    /**
     * 物料批量设置检验要求
     */
    @PostMapping("/batch/update/inspect/demand")
    public ResponseData batchUpdateInspectDemand(@RequestBody MaterialBatchUpdateInspectDemandDTO updateDto) {
        updateDto.setUsername(getUsername());
        materialService.batchUpdateInspectDemand(updateDto);
        return success();
    }

    /**
     * 物料批量设置检验要求进度
     * @return 进度
     */
    @GetMapping("/batch/update/inspect/demand/progress")
    public ResponseData batchUpdateInspectDemandProgress() {
        String progressKey = RedisKeyPrefix.MATERIAL_BATCH_UPDATE_INSPECT_PROGRESS + getUsername();
        return success((String) redisTemplate.opsForValue().get(progressKey));
    }

    /**
     * 获取保质期单位列表
     */
    @GetMapping("/shelf_life/unit")
    public ResponseData getShelfLifeUnit() {
        List<CommonType> list = materialService.getShelfLifeUnit();
        return success(list);
    }

}
