package com.yelink.dfs.controller.furnace;


import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.yelink.dfs.common.BaseController;
import com.yelink.dfs.constant.OperationType;
import com.yelink.dfs.entity.furnace.SecondaryMaterialEntity;
import com.yelink.dfs.entity.furnace.dto.LaboratoryReportAndMaterialDto;
import com.yelink.dfs.service.furnace.FurnaceCommonService;
import com.yelink.dfs.service.furnace.SecondaryMaterialService;
import com.yelink.dfscommon.annotation.OperLog;
import com.yelink.dfscommon.pojo.ResponseData;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description 辅料控制层
 * @Date 2021/8/3
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/secondary/material")
public class SecondaryMaterialController extends BaseController {

    private SecondaryMaterialService secondaryMaterialService;
    private FurnaceCommonService furnaceCommonService;

    /**
     * 获取最新一条化验信息及辅料信息
     *
     * @param
     * @return
     */
    @GetMapping("/newest/test")
    public ResponseData getNewestTestAndMaterial(@RequestParam(value = "furnaceCode", required = false) String furnaceCode,
                                                 @RequestParam(value = "fid", required = false) Integer fid) {
        LaboratoryReportAndMaterialDto dto = secondaryMaterialService.getNewestTestAndMaterial(furnaceCode, fid);
        return success(dto);
    }

    /**
     * 精炼炉获取辅料信息
     *
     * @param workOrderNumber
     * @param fid   电炉环节工位id
     * @param furnaceCode
     * @return
     */
    @GetMapping("/material/by/work/order")
    public ResponseData getMaterialListByWorkOrder(@RequestParam(value = "workOrderNumber") String workOrderNumber,
                                                   @RequestParam(value = "fid") Integer fid,
                                                   @RequestParam(value = "furnaceCode") String furnaceCode,
                                                   @RequestParam(value = "refiningFurnaceFid") Integer refiningFurnaceFid) {
        List<SecondaryMaterialEntity> list = secondaryMaterialService.getMaterialListByWorkOrder(workOrderNumber, fid, furnaceCode,refiningFurnaceFid);
        return success(list);
    }

    /**
     * 获取辅料信息详情
     *
     * @param
     * @return
     */
    @GetMapping("/detail/{id}")
    public ResponseData getMaterialDetail(@PathVariable(value = "id") Integer id) {
        SecondaryMaterialEntity entity = secondaryMaterialService.getEntityById(id);
        return success(entity);
    }

    /**
     * 更新辅料信息
     *
     * @param
     * @return
     */
    @OperLog(module = "炉次管理", type = OperationType.UPDATE, desc = "更新了炉次号#{furnaceCode}-辅料编号为#{materialCode}的辅料信息")
    @PutMapping("/update")
    public ResponseData updateMaterial(@RequestBody SecondaryMaterialEntity entity) {
        String username = getUsername();
        entity.setUpdateBy(username);
        entity.setUpdateTime(new Date());
        secondaryMaterialService.updateById(entity);

        //向前端发送websocket刷新辅料信息
        LaboratoryReportAndMaterialDto dto = secondaryMaterialService.getNewestTestAndMaterial(entity.getFurnaceCode(), entity.getFid());
        secondaryMaterialService.submitMessage(dto, entity.getFid(), entity.getFurnaceCode());
        return success(entity);
    }

    /**
     * 删除辅料信息
     *
     * @param
     * @return
     */
    @OperLog(module = "炉次管理", type = OperationType.DELETE, desc = "删除了炉次号#{furnaceCode}-辅料编号为#{materialCode}的辅料信息")
    @DeleteMapping("/delete/{id}")
    public ResponseData deleteMaterial(@PathVariable(value = "id") Integer id) {
        SecondaryMaterialEntity entity = secondaryMaterialService.getById(id);
        secondaryMaterialService.removeById(id);
        return success(entity);
    }

    /**
     * 确认辅料信息
     *
     * @param
     * @return
     */
    @GetMapping("/confirm")
    public ResponseData confirmMaterialInfo(@RequestParam(value = "furnaceCode") String furnaceCode,
                                            @RequestParam(value = "fid") Integer fid,
                                            @RequestParam(value = "sequence") Integer sequence) {
        secondaryMaterialService.confirmMaterialInfo(furnaceCode, fid, sequence);
        return success();
    }

    /**
     * 刷新电子秤称重重量
     *
     * @param
     * @return
     */
    @GetMapping("/refresh/weigh/{id}")
    public ResponseData refreshWeigh(@PathVariable(value = "id") Integer id) {
        SecondaryMaterialEntity entity = secondaryMaterialService.refreshWeigh(id);
        return success(entity);
    }

    /**
     * 确认添加辅料
     *
     * @param
     * @return
     */
    @GetMapping("/confirm/add/{id}")
    public ResponseData confirmAddMaterial(@PathVariable(value = "id") Integer id) {
        secondaryMaterialService.confirmAddMaterial(id);
        return success();
    }

    /**
     * 通过电炉工位获取炉长确认后的辅料表
     *
     * @param
     * @return
     */
    @GetMapping("/confirm/material/furnace")
    public ResponseData getConfirmMaterialByFurnace() {
        // 获取电炉工位
        List<SecondaryMaterialEntity> list = secondaryMaterialService.getConfirmMaterialByFurnace();
        return success(list);
    }

    /**
     * 添加BOM绑定的辅料
     *
     * @param
     * @return
     */
    @PostMapping("/add")
    public ResponseData saveByWorkOrderProductCode(@RequestParam(value = "workOrderNum") String workOrderNum,
                                                   @RequestParam(value = "sequence") Integer sequence,
                                                   @RequestParam(value = "furnaceCode") String furnaceCode,
                                                   @RequestParam(value = "fid") Integer fid) {
        String username = getUsername();
        furnaceCommonService.saveByWorkOrderProductCode(workOrderNum, sequence, furnaceCode, fid, username);
        return success();
    }

    /**
     * 编辑实际出钢量
     *
     * @param
     * @return
     */
    @GetMapping("/update/actual/tapping")
    public ResponseData updateActualTappingCapacity(@RequestParam(value = "value") Double value,
                                                    @RequestParam(value = "fid") Integer fid,
                                                    @RequestParam(value = "furnaceCode", required = false) String furnaceCode) {
        LambdaUpdateWrapper<SecondaryMaterialEntity> uw = new LambdaUpdateWrapper<>();
        uw.eq(SecondaryMaterialEntity::getFid, fid)
                .eq(SecondaryMaterialEntity::getFurnaceCode, furnaceCode)
                .isNull(SecondaryMaterialEntity::getSeq)
                .set(SecondaryMaterialEntity::getTappingCapacityVal, value);
        secondaryMaterialService.update(uw);

        //向前端发送websocket刷新辅料信息
        LaboratoryReportAndMaterialDto dto = secondaryMaterialService.getNewestTestAndMaterial(furnaceCode, fid);
        // 发送websocket消息
        secondaryMaterialService.submitMessage(dto, fid, furnaceCode);
        return success();
    }


}
