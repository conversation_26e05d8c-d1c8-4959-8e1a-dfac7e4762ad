package com.yelink.dfs.controller.order;


import cn.hutool.core.util.RandomUtil;
import com.asyncexcel.core.exporter.DataExportParam;
import com.asyncexcel.core.model.ExcelTask;
import com.asyncexcel.springboot.ExcelService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yelink.dfs.common.BaseController;
import com.yelink.dfs.constant.OperationType;
import com.yelink.dfs.constant.common.ModelUploadFileEnum;
import com.yelink.dfs.entity.common.CommonState;
import com.yelink.dfs.entity.common.CommonType;
import com.yelink.dfs.entity.order.dto.WorkOrderMaterialListExportDTO;
import com.yelink.dfs.entity.product.BomEntity;
import com.yelink.dfs.service.common.ModelUploadFileService;
import com.yelink.dfs.service.order.WorkOrderMaterialListService;
import com.yelink.dfscommon.annotation.OperLog;
import com.yelink.dfscommon.common.excel.BusinessCodeEnum;
import com.yelink.dfscommon.constant.Constant;
import com.yelink.dfscommon.constant.ExcelExportFormEnum;
import com.yelink.dfscommon.constant.RedisKeyPrefix;
import com.yelink.dfscommon.constant.ResponseException;
import com.yelink.dfscommon.constant.ams.MaterialListStateEnum;
import com.yelink.dfscommon.constant.ams.ShowTypeEnum;
import com.yelink.dfscommon.constant.ams.WorkOrderMaterialListMaterialChooseEnum;
import com.yelink.dfscommon.dto.ApproveBatchDTO;
import com.yelink.dfscommon.dto.ams.order.CraftProcedureMaterialListDTO;
import com.yelink.dfscommon.dto.ams.order.MaterialListDTO;
import com.yelink.dfscommon.dto.dfs.OrderMaterialListDTO;
import com.yelink.dfscommon.dto.dfs.push.AbstractPushDTO;
import com.yelink.dfscommon.dto.dfs.push.BomPushWorkOrderMaterialListVO;
import com.yelink.dfscommon.entity.ams.dto.MaterialListSelectDTO;
import com.yelink.dfscommon.entity.dfs.WorkOrderMaterialListEntity;
import com.yelink.dfscommon.pojo.ResponseData;
import com.yelink.dfscommon.utils.DateUtil;
import com.yelink.dfscommon.utils.EasyExcelUtil;
import com.yelink.dfscommon.utils.ExcelTemplateImportUtil;
import com.yelink.dfscommon.utils.ExcelUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 生产工单用料清单
 *
 * <AUTHOR>
 * @Date 2022/10/19 21:33
 */
@Slf4j
@RestController
@RequestMapping("/work_order/material/lists")
public class WorkOrderMaterialListController extends BaseController {
    @Resource
    private WorkOrderMaterialListService materialListService;
    @Resource
    private ModelUploadFileService modelUploadFileService;
    @Resource
    private ExcelService excelService;
    @Resource
    private RedisTemplate redisTemplate;

    /**
     * 查询生产工单用料清单列表
     */
    @PostMapping("/list")
    public ResponseData getList(@RequestBody MaterialListSelectDTO materialListSelectDTO) {
        Page<WorkOrderMaterialListEntity> materialListPage = materialListService.getList(materialListSelectDTO);
        return ResponseData.success(materialListPage);
    }

    /**
     * 新增生产工单用料清单
     */
    @PostMapping("/add")
    @OperLog(module = "订单管理", type = OperationType.ADD, desc = "新增了单号为#{materialListCode}的生产工单用料清单")
    public ResponseData add(@RequestBody @Validated WorkOrderMaterialListEntity entity) {
        entity.setCreateBy(getUsername());
        materialListService.add(entity);
        return ResponseData.success(entity);
    }

    /**
     * 新增生产工单用料清单
     */
    @PostMapping("/add/released")
    @OperLog(module = "订单管理", type = OperationType.ADD, desc = "新增了单号为#{materialListCode}的生产工单用料清单")
    public ResponseData addReleased(@RequestBody @Validated WorkOrderMaterialListEntity entity) {
        entity.setCreateBy(getUsername());
        materialListService.addReleasedOrder(entity);
        return ResponseData.success(entity);
    }

    /**
     * 更新生产工单用料清单
     */
    @PutMapping("/update")
    @OperLog(module = "订单管理", type = OperationType.UPDATE, desc = "修改了单号为#{materialListCode}的生产工单用料清单")
    public ResponseData update(@RequestBody @Validated WorkOrderMaterialListEntity entity) {
        entity.setUpdateBy(getUsername());
        materialListService.update(entity);
        return ResponseData.success(entity);
    }

    /**
     * 删除生产工单用料清单
     */
    @DeleteMapping("/delete")
    @OperLog(module = "订单管理", type = OperationType.DELETE, desc = "删除了单号为#{materialListCode}的生产工单用料清单")
    public ResponseData delete(@RequestParam(value = "materialListId") Integer materialListId) {
        WorkOrderMaterialListEntity workOrderMaterialList = materialListService.delete(materialListId);
        return ResponseData.success(workOrderMaterialList);
    }

    /**
     * 获取生产工单用料清单详情
     */
    @GetMapping("/detail")
    public ResponseData detail(@RequestParam(value = "materialListId") Integer materialListId) {
        return ResponseData.success(materialListService.detail(materialListId));
    }

    /**
     * 审批
     */
    @GetMapping("/examine/approve")
    public ResponseData examineOrApprove(@RequestParam Integer approvalStatus,
                                         @RequestParam Integer materialListId,
                                         @RequestParam(required = false) String approvalSuggestion) {
        String username = getUsername();
        Boolean ret = materialListService.examineOrApprove(approvalStatus, approvalSuggestion, materialListId, username);
        return ResponseData.success(ret);
    }

    /**
     * 批量审批
     */
    @PostMapping("/approve/batch")
    public ResponseData approveBatch(@RequestBody ApproveBatchDTO dto) {
        dto.setActualApprover(getUsername());
        Boolean ret = materialListService.approveBatch(dto);
        return ResponseData.success(ret);
    }

    /**
     * 获取所有状态以及对应的code
     */
    @GetMapping("/all/state")
    public ResponseData getAllState() {
        MaterialListStateEnum[] values = MaterialListStateEnum.values();
        List<CommonState> states = new ArrayList<>();
        for (MaterialListStateEnum stateEnum : values) {
            states.add(CommonState.builder().code(stateEnum.getCode()).name(stateEnum.getName()).build());
        }
        return ResponseData.success(states);
    }

    /**
     * 获取用料清单物料选择枚举
     */
    @GetMapping("/material/choose")
    public ResponseData getMaterialChoose() {
        WorkOrderMaterialListMaterialChooseEnum[] values = WorkOrderMaterialListMaterialChooseEnum.values();
        List<CommonType> states = new ArrayList<>();
        for (WorkOrderMaterialListMaterialChooseEnum stateEnum : values) {
            states.add(CommonType.builder().code(stateEnum.getCode()).name(stateEnum.getName()).build());
        }
        return ResponseData.success(states);
    }

    /**
     * 智能推荐：获取标准件和替代件的库存数量、替代数量
     */
    @PostMapping("/recommend/quantity")
    public ResponseData getStockQuantity(@RequestBody List<MaterialListDTO> dtos) {
        return ResponseData.success(materialListService.getRecommendQuantity(dtos));
    }

    /**
     * 通过生产工单号查询最新的bom以及出入库用量
     *
     * @param workOrderNumber 生产工单号
     */
    @GetMapping("/bom/material")
    public ResponseData getBomWithStockByOrder(@RequestParam(value = "workOrderNumber") String workOrderNumber) {
        BomEntity bomEntity = materialListService.getBomWithStockByOrder(workOrderNumber);
        return ResponseData.success(bomEntity);
    }

    /**
     * 通过工单的工艺工序/工单的物料获取用料清单
     * 逻辑：
     * 1、该工艺工序存在工序用料，如果不存在走2逻辑，存在则取工序用料
     * 2、获取工单物料的一级BOM
     */
    @PostMapping("/list/craft_procedure")
    public ResponseData getMaterialListByCraftProcedure(@RequestBody CraftProcedureMaterialListDTO dto) {
        BomEntity bomEntity = materialListService.getMaterialListByCraftProcedure(dto);
        if (Objects.isNull(bomEntity)) {
            return ResponseData.success(materialListService.getBomEntities(dto.getMaterialCode(), dto.getSkuId(), dto.getBusinessType()));
        }
        return ResponseData.success(Collections.singletonList(bomEntity));
    }

    /**
     * 源单据：获取生产工单用料清单物料行列表
     */
    @PostMapping("/material_list")
    public ResponseData getPushDownMaterialList(@RequestBody OrderMaterialListDTO dto) {
        return ResponseData.success(materialListService.getPushDownMaterialList(dto));
    }

    /**
     * 目标单据：按BOM下推生产工单用料清单：获取生产工单用料清单下推物料行列表
     */
    @PostMapping("/bom/push/material/list")
    public ResponseData getPushMaterialList(@RequestBody AbstractPushDTO pushDown) {
        List<BomPushWorkOrderMaterialListVO.TargetOrderPushMaterialListVO> list = materialListService.getBomPushMaterialList(pushDown);
        return ResponseData.success(list);
    }

    /**
     * 目标单据：按BOM下推生产工单用料清单：生成生产工单用料清单
     */
    @PostMapping("/bom/push/work_order_material_list")
    public ResponseData craftPushWorkOrder(@RequestBody BomPushWorkOrderMaterialListVO pushDown) {
        String code = RandomUtil.randomString(10);
        materialListService.bomPushWorkOrderMaterialList(pushDown, getUsername(), code);
        return ResponseData.success(code, null);
    }

    /**
     * 默认导入模板下载
     */
    @GetMapping("/down/default/template")
    public void downloadDefaultTemplate(HttpServletResponse response) throws Exception {
        materialListService.downloadDefaultTemplate("classpath:template/workOrderMaterialListTemplate.xlsx", response, "生产工单用料清单默认导入模板" + Constant.XLSX);
    }

    /**
     * 导入自定义模板
     */
    @PostMapping("/import/template")
    @OperLog(module = "导入日志", type = OperationType.ADD, desc = "导入生产工单用料清单自定义模板")
    public ResponseData importTemplateExcel(MultipartFile file) {
        materialListService.uploadCustomTemplate(file, this.getUsername());
        return success();
    }

    /**
     * 下载自定义导入模板
     */
    @GetMapping("/export/custom/template")
    public void exportCustomExcel(HttpServletResponse response) throws IOException {
        //找到导入的模板
        InputStream inputStream = materialListService.downloadCustomImportTemplate();
        ExcelTemplateImportUtil.responseToClient(response, inputStream, "生产工单用料清单导入模板" + Constant.XLSX);
    }

    /**
     * 下载导入模板
     */
    @GetMapping("/export/template")
    public void exportTemplateExcel(HttpServletResponse response) throws IOException {
        //找到导入的模板
        byte[] bytes = materialListService.downloadImportTemplate();
        ExcelTemplateImportUtil.responseToClient(response, bytes, "生产工单用料清单导入模板" + Constant.XLSX);
    }

    /**
     * 导入数据
     */
    @PostMapping("/import/data")
    public ResponseData importData(MultipartFile file) throws Exception {
        ExcelUtil.checkImportExcelFile(file);
        //5、异步数据导入
        String importProgressKey = RedisKeyPrefix.WORK_ORDER_MATERIAL_LIST_IMPORT_PROGRESS + DateUtil.format(new Date(), DateUtil.yyyyMMddHHmmss_FORMAT) + "_" + RandomUtil.randomString(2);
        materialListService.sycImportData(file.getOriginalFilename(), file.getInputStream(), getUsername(), importProgressKey);
        return success(importProgressKey);
    }

    /**
     * 查询导入进度
     */
    @GetMapping("/import/progress")
    public ResponseData getImportProgress(@RequestParam(value = "key") String key) {
        Object obj = redisTemplate.opsForValue().get(key);
        return success(obj);
    }

    /**
     * 生产工单用料清单导出 - 下载默认导出模板
     */
    @GetMapping("/default/export/template")
    public void downloadSaleOrderDefaultExportTemplate(HttpServletResponse response) throws IOException {
        MaterialListSelectDTO selectDTO = new MaterialListSelectDTO();
        selectDTO.setCurrent(1);
        selectDTO.setSize(10);
        selectDTO.setShowType(ShowTypeEnum.MATERIAL.getType());
        Page<WorkOrderMaterialListEntity> page = materialListService.getList(selectDTO);
        List<WorkOrderMaterialListExportDTO> exports = WorkOrderMaterialListExportDTO.convertToExportDTO(page.getRecords());
        EasyExcelUtil.export(response, "生产工单用料清单默认导出模板", "数据源", exports, WorkOrderMaterialListExportDTO.class, ExcelExportFormEnum.WORK_ORDER_MATERIAL_LIST.getFullPathCode());
    }

    /**
     * 生产工单用料清单导出 - 自定义模板上传
     */
    @PostMapping("/upload/export/template")
    public ResponseData uploadExportTemplate(MultipartFile file) throws IOException {
        String username = getUsername();
        EasyExcelUtil.isValidExcelFile(file);
        MultipartFile modifiedFile = new MockMultipartFile(
                file.getName(),
                file.getOriginalFilename(),
                file.getContentType(),
                EasyExcelUtil.clearAndConvertToBytes(file, "数据源"));
        modelUploadFileService.uploadReferencedFile(modifiedFile, ModelUploadFileEnum.WORK_ORDER_MATERIAL_LIST_EXPORT.getCode(), username);
        return ResponseData.success();
    }

    /**
     * 生产工单用料清单导出 - 导出
     */
    @PostMapping("/excel/export")
    public ResponseData excelExport(@RequestBody MaterialListSelectDTO selectDTO) {
        String username = getUsername();
        DataExportParam<MaterialListSelectDTO> dataExportParam = new DataExportParam<>();
        dataExportParam.setExportFileName(BusinessCodeEnum.WORK_ORDER_MATERIAL_LIST_EXPORT.getCodeName());
        dataExportParam.setLimit(10000);
        dataExportParam.setBusinessCode(BusinessCodeEnum.WORK_ORDER_MATERIAL_LIST_EXPORT.name());
        dataExportParam.setCreateUserCode(username);
        Map<String, Object> parameters = new HashMap<>();
        parameters.put(MaterialListSelectDTO.class.getName(), selectDTO);
        parameters.put("cleanSheetNames", "数据源");
        parameters.put("templateId", selectDTO.getTemplateId());
        //导出模板要填充数据的sheetName
        parameters.put("templateSheetName", "数据源");
        dataExportParam.setParameters(parameters);
        return ResponseData.success(excelService.doExport(dataExportParam, WorkOrderMaterialListExportHandler.class));
    }

    /**
     * 生产工单用料清单导出 - 查询导出进度
     */
    @GetMapping("/export/task/{taskId}")
    public ResponseData exportExcelTask(@PathVariable Long taskId) {
        ExcelTask excelTask = new ExcelTask();
        excelTask.setId(taskId);
        excelTask.setBusinessCode(BusinessCodeEnum.WORK_ORDER_MATERIAL_LIST_EXPORT.name());
        IPage<ExcelTask> excelTaskIPage = excelService.listPage(excelTask, 1, 1);
        List<ExcelTask> records = excelTaskIPage.getRecords();
        if (CollectionUtils.isEmpty(records)) {
            throw new ResponseException("没有查询到对应的导出任务详情");
        }
        return ResponseData.success(records.get(0));
    }

    /**
     * 生产工单用料清单导出 - 分页查询当前导出任务列表
     */
    @GetMapping("/export/task/page")
    public ResponseData taskPage(@RequestParam(value = "pageSize", required = false, defaultValue = "20") Integer size,
                                 @RequestParam(value = "currentPage", required = false, defaultValue = "1") Integer current) {
        ExcelTask excelTask = new ExcelTask();
        excelTask.setBusinessCode(BusinessCodeEnum.WORK_ORDER_MATERIAL_LIST_EXPORT.name());
        return ResponseData.success(excelService.listPage(excelTask, current, size));
    }


}
