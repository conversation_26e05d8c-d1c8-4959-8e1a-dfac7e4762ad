package com.yelink.dfs.controller.stock;

import cn.hutool.core.util.RandomUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yelink.dfs.common.BaseController;
import com.yelink.dfs.constant.OperationType;
import com.yelink.dfs.constant.order.DeliveryApplicationStateEnum;
import com.yelink.dfs.constant.order.OrderStateEnum;
import com.yelink.dfs.entity.product.MaterialEntity;
import com.yelink.dfs.entity.stock.DeliveryApplicationEntity;
import com.yelink.dfs.entity.stock.DeliveryApplicationMaterialEntity;
import com.yelink.dfs.entity.stock.dto.DeliveryApplicationDTO;
import com.yelink.dfs.entity.stock.dto.SalePushDeliveryApplicationOrderDTO;
import com.yelink.dfs.open.v1.aksk.ams.ExtSaleOrderInterface;
import com.yelink.dfs.service.common.CommonService;
import com.yelink.dfs.service.product.MaterialService;
import com.yelink.dfs.service.stock.DeliveryApplicationExtendService;
import com.yelink.dfs.service.stock.DeliveryApplicationService;
import com.yelink.dfscommon.annotation.OperLog;
import com.yelink.dfscommon.api.dfs.OpenApiInterface;
import com.yelink.dfscommon.constant.OrderShowTypeEnum;
import com.yelink.dfscommon.constant.RedisKeyPrefix;
import com.yelink.dfscommon.constant.ams.ShowTypeEnum;
import com.yelink.dfscommon.dto.ApproveBatchDTO;
import com.yelink.dfscommon.dto.ams.open.order.SaleOrderSelectOpenDTO;
import com.yelink.dfscommon.dto.dfs.DeliveryApplicationSelectDTO;
import com.yelink.dfscommon.dto.dfs.OrderMaterialListDTO;
import com.yelink.dfscommon.dto.dfs.push.AbstractPushDTO;
import com.yelink.dfscommon.entity.ams.dto.SaleOrderVO;
import com.yelink.dfscommon.entity.common.BatchChangeStateDTO;
import com.yelink.dfscommon.pojo.PageResult;
import com.yelink.dfscommon.pojo.ResponseData;
import com.yelink.dfscommon.utils.JacksonUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import static com.yelink.dfscommon.constant.Constant.SEP;

/**
 * <AUTHOR>
 * @date 2021/5/7 17:52
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/delivery/application")
public class DeliveryApplicationController extends BaseController {

    private CommonService commonService;
    private DeliveryApplicationService applicationService;
    private DeliveryApplicationExtendService deliveryApplicationExtendService;
    private MaterialService materialService;
    private ExtSaleOrderInterface extSaleOrderInterface;
    private OpenApiInterface openApiInterface;

    /**
     * 查询所有出货申请记录
     */
    @Deprecated
    @GetMapping("/list")
    public ResponseData getList(DeliveryApplicationSelectDTO dto) {
        Page<DeliveryApplicationEntity> list = deliveryApplicationExtendService.getList(dto);
        return success(list);
    }

    @PostMapping("/page")
    public ResponseData pageDeliveryApplication(@RequestBody DeliveryApplicationSelectDTO selectDTO) {
        Page<DeliveryApplicationEntity> list = deliveryApplicationExtendService.getList(selectDTO);
        return success(list);
    }

    /**
     * 创建销售出货单
     */
    @PostMapping("/create")
    @OperLog(module = "库房管理", type = OperationType.ADD, desc = "创建了单号为#{applicationNum}的销售出货单")
    public ResponseData createSendOrder(@RequestBody DeliveryApplicationEntity entity) {
        String username = getUsername();
        entity.setCreateBy(username);
        entity.setUpdateBy(username);
        entity.setCreateTime(new Date());
        applicationService.createDeliveryApplication(entity);
        return success(entity);
    }

    /**
     * 创建生效的销售出货单
     */
    @PostMapping("/create/released")
    @OperLog(module = "库房管理", type = OperationType.ADD, desc = "创建了单号为#{applicationNum}的销售出货单")
    public ResponseData createReleasedDeliveryApplication(@RequestBody DeliveryApplicationEntity entity) {
        String username = getUsername();
        entity.setCreateBy(username);
        entity.setUpdateBy(username);
        entity.setCreateTime(new Date());
        entity.setUpdateTime(new Date());
        applicationService.createReleasedDeliveryApplication(entity);
        return success(entity);
    }


    /**
     * 修改销售出货单
     */
    @OperLog(module = "库房管理", type = OperationType.UPDATE, desc = "修改了单号为#{applicationNum}的发货单")
    @PutMapping("/update")
    public ResponseData updateDeliveryApplication(@RequestBody DeliveryApplicationEntity entity) {
        String username = getUsername();
        entity.setUpdateBy(username);
        entity.setUpdateTime(new Date());
        applicationService.checkStateBeforeUpdate(entity);
        return success(entity);
    }

    /**
     * 删除销售出货单
     */
    @OperLog(module = "库房管理", type = OperationType.ADD, desc = "删除了ID为#{deliveryId}的调拨记录")
    @DeleteMapping("/delete/{id}")
    public ResponseData deleteDeliveryApplication(@PathVariable Integer id) {
        DeliveryApplicationEntity entity = applicationService.deleteDeliveryApplication(id);
        return success(entity);
    }

    /**
     * 查询销售出货单详情
     */
    @GetMapping("/detail/{applicationId}")
    public ResponseData getDeliveryApplicationDetail(@PathVariable Integer applicationId) {
        DeliveryApplicationEntity entity = applicationService.getDeliveryApplicationDetail(applicationId);
        return success(entity);
    }

    /**
     * 获取发货单状态
     */
    @GetMapping("/state")
    public ResponseData getDeliveryState() {
        List<DeliveryApplicationDTO> list = new ArrayList<>();
        for (DeliveryApplicationStateEnum value : DeliveryApplicationStateEnum.values()) {
            list.add(DeliveryApplicationDTO.builder()
                    .code(value.getCode())
                    .name(value.getName())
                    .build());
        }
        return success(list);
    }

    /**
     * 获取销售订单
     */
    @GetMapping("/sale/order")
    public ResponseData getOutputOrder() {
        List<Integer> states = new ArrayList<>();
        states.add(OrderStateEnum.RELEASED.getCode());
        states.add(OrderStateEnum.FINISHED.getCode());

        SaleOrderSelectOpenDTO build = SaleOrderSelectOpenDTO.builder().states(OrderStateEnum.RELEASED.getCode() + SEP + OrderStateEnum.FINISHED.getCode()).showType(OrderShowTypeEnum.ORDER.getType()).build();
        PageResult<SaleOrderVO> responsePage = extSaleOrderInterface.getPage(build);

        if (CollectionUtils.isEmpty(responsePage.getRecords())) {
            return success(new ArrayList<>());
        }
        List<String> collect = responsePage.getRecords().stream().map(SaleOrderVO::getSaleOrderNumber).collect(Collectors.toList());
        return success(collect);
    }

    /**
     * 获取订单关联的物料
     */
    @GetMapping("/sale/material/{saleOrder}")
    public ResponseData getOutputMaterial(@PathVariable String saleOrder) {
        Page<SaleOrderVO> page = JacksonUtil.getResponsePage(openApiInterface.getSaleOrderPage(SaleOrderSelectOpenDTO.builder().saleOrderNumber(saleOrder).showType(ShowTypeEnum.MATERIAL.getType()).build()), SaleOrderVO.class);

        if (CollectionUtils.isEmpty(page.getRecords())) {
            return success(new ArrayList<>());
        }

        List<DeliveryApplicationMaterialEntity> materials = page.getRecords().stream().map(SaleOrderVO::getSaleOrderMaterial).map(materialEntity -> {
            MaterialEntity entity = materialService.getEntityByCodeAndSkuId(materialEntity.getMaterialCode(), materialEntity.getSkuId());
            return DeliveryApplicationMaterialEntity.builder()
                    .relateOrderMaterialId(materialEntity.getId())
                    .materialCode(materialEntity.getMaterialCode())
                    .skuId(materialEntity.getSkuId())
                    .materialFields(entity)
                    .amount(materialEntity.getSalesQuantity())
                    .build();
        }).collect(Collectors.toList());
        return success(materials);
    }
    @GetMapping("/sale/material")
    public ResponseData getOutputMaterial2(@RequestParam String saleOrder) {
        return getOutputMaterial(saleOrder);
    }

    /**
     * 销售出货单新增前的判断：
     * 新增销售出货单时，判断销售订单关联的销售出货单的数量之和(包括当前的销售出货单) > 销售订单的计划数量
     */
    @PostMapping("/insert/judge")
    public ResponseData judgeBeforeInsert(@RequestBody DeliveryApplicationEntity entity) {
        return success(applicationService.judgeBeforeInsert(entity));
    }

    /**
     * 审批
     */
    @GetMapping("/examine/approve")
    public ResponseData examineOrApprove(@RequestParam(value = "approvalStatus") Integer approvalStatus,
                                         @RequestParam(value = "approvalSuggestion", required = false) String approvalSuggestion,
                                         @RequestParam(value = "id") Integer id) {
        String username = getUsername();
        applicationService.examineOrApprove(approvalStatus, approvalSuggestion, id, username);
        return success();
    }

    /**
     * 批量审批
     */
    @PostMapping("/approve/batch")
    public ResponseData approveBatch(@RequestBody ApproveBatchDTO dto) {
        dto.setActualApprover(getUsername());
        applicationService.approveBatch(dto);
        return success();
    }

    /**
     * 发货申请批量编辑
     */
    @PostMapping("/batch/update/state")
    public ResponseData batchUpdate(@RequestBody BatchChangeStateDTO batchApprovalDTO) {
        if (batchApprovalDTO.getIds() == null) {
            return fail("编辑的单据id为空");
        }
        applicationService.batchUpdateState(batchApprovalDTO, getUsername());
        return ResponseData.success();
    }

    /**
     * 源单据：获取销售出货单物料行列表
     *
     * @return
     */
    @PostMapping("/material_list")
    public ResponseData getPushDownMaterialList(@RequestBody OrderMaterialListDTO dto) {
        return ResponseData.success(applicationService.getPushDownMaterialList(dto));
    }

    /**
     * 目标单据：销售订单下推出货申请单（即销售出货单）：获取下推物料行列表
     */
    @PostMapping("/sale/push/material/list")
    public ResponseData getSalePushMaterialList(@RequestBody AbstractPushDTO pushDown) {
        List<SalePushDeliveryApplicationOrderDTO.PushMaterialListVO> list = applicationService.getSalePushMaterialList(pushDown);
        return ResponseData.success(list);
    }
    /**
     * 目标单据：销售订单下推出货申请单（即销售出货单）：生成出货申请单
     */
    @PostMapping("/sale/push/order")
    public ResponseData salePushDeliveryApplicationOrder(@RequestBody SalePushDeliveryApplicationOrderDTO.PushOrderReq pushDown) {
        // 随机生成一个code,用于标识本次下推
        String code = RandomUtil.randomString(10);
        applicationService.salePushDeliveryApplicationOrder(pushDown, getUsername(), code);
        return ResponseData.success(code, null);
    }
    /**
     * 目标单据：下推进度
     */
    @GetMapping("/push/process")
    public ResponseData pushDownProcess(@RequestParam String code) {
        return ResponseData.success(commonService.getProgress(RedisKeyPrefix.PUSH_DOWN_PROGRESS + code));
    }

}
