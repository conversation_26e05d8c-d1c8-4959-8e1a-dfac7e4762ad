package com.yelink.dfs.controller.model;

import com.yelink.dfs.common.BaseController;
import com.yelink.dfs.entity.model.LineFileEntity;
import com.yelink.dfs.entity.product.dto.CraftFileDTO;
import com.yelink.dfs.provider.FastDfsClientService;
import com.yelink.dfs.service.model.LineFileService;
import com.yelink.dfscommon.pojo.ResponseData;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @Description 产线与附件关联
 * @Date 2022/1/6 17:04
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/lineFile")
public class LineFileController extends BaseController {

    private LineFileService lineFileService;
    private FastDfsClientService fastDfsClientService;

    /**
     * 通过id删除产线附件
     *
     * @param id
     * @return
     */
    @DeleteMapping("/delete/{id}")
    public ResponseData deleteLineFile(@PathVariable Integer id) {
        boolean result;
        LineFileEntity lineFileEntity = lineFileService.getById(id);
        //1.删除fastDfs的上传文件
        try {
            fastDfsClientService.deleteFile(lineFileEntity.getFile());
        } catch (Exception e) {
            log.error("产线删除文件出错", e);
        }
        //2.删除表中的对应的文件数据
        result = lineFileService.removeById(lineFileEntity);
        return result ? success() : fail();
    }


    /**
     * 通过产线id获取产线的附件(支持附件名称模糊查询)
     *
     * @param lineId
     * @param fileName
     * @return
     */
    @GetMapping("/line/file")
    public ResponseData getWorkOrderFileByWorkNumber(@RequestParam(value = "lineId") Integer lineId,
                                                     @RequestParam(value = "fileName",required = false) String fileName) {
        List<CraftFileDTO> LineFileList = lineFileService.getLineFileByWorkNumber(lineId, fileName);
        return success(LineFileList);
    }
}
