package com.yelink.dfs.controller.furnace;

import com.yelink.dfs.common.BaseController;
import com.yelink.dfs.entity.furnace.RefiningFurnaceRecordEntity;
import com.yelink.dfs.service.furnace.RefiningFurnaceRecordService;
import com.yelink.dfscommon.pojo.ResponseData;
import lombok.AllArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;
import java.util.List;

/**
 * 精炼炉控制器
 *
 * <AUTHOR>
 * @Date 2022-04-01 17:01
 */
@RestController
@AllArgsConstructor
@RequestMapping("/refining/furnace/record")
public class RefiningFurnaceController extends BaseController {
    private RefiningFurnaceRecordService refiningFurnaceService;


    /**
     * 通过选择的工位查询未确认的并未开始的炉次列表信息
     *
     * @param fid
     * @return
     */
    @GetMapping("/list")
    public ResponseData getList(@RequestParam(value = "fid") Integer fid) {
        List<RefiningFurnaceRecordEntity> list = refiningFurnaceService.getList(fid);
        return ResponseData.success(list);
    }

    /**
     * 通过精炼炉的id查询正在冶炼的炉次（只会存在一个正在冶炼的炉次）
     * @param fid
     * @return
     */
    @GetMapping("/running")
    public ResponseData getRunningFurnace(@RequestParam(value = "fid") Integer fid) {
        return ResponseData.success(refiningFurnaceService.getRunningFurnace(fid));
    }
    /**
     * 通过id查询精炼炉详细信息
     *
     * @param id
     * @return
     */
    @GetMapping("/detail")
    public ResponseData getDetail(@RequestParam(value = "id") Integer id) {
        RefiningFurnaceRecordEntity entity = refiningFurnaceService.getDetail(id);
        return ResponseData.success(entity);
    }

    /**
     * 绑定精炼炉负责人
     *
     * @param entity
     * @return
     */
    @PutMapping("/binding/principal")
    public ResponseData updateBindingPrincipal(@RequestBody @Validated({RefiningFurnaceRecordEntity.Update.class}) RefiningFurnaceRecordEntity entity) {
        entity.setUpdateBy(getUsername());
        entity.setUpdateTime(new Date());
        Boolean aBoolean = refiningFurnaceService.updateBindingPrincipal(entity);
        if (aBoolean) {
            return ResponseData.success();
        }
        return ResponseData.fail();
    }

    /**
     * 精炼炉修改状态并绑定精炼炉工位
     *
     * @param entity
     * @return
     */
    @PutMapping("/update/state")
    public ResponseData updateState(@RequestBody @Validated({RefiningFurnaceRecordEntity.Update.class}) RefiningFurnaceRecordEntity entity) {
        entity.setUpdateBy(getUsername());
        entity.setUpdateTime(new Date());
        Boolean aBoolean = refiningFurnaceService.updateState(entity);
        if (aBoolean) {
            return ResponseData.success();
        }
        return ResponseData.fail();
    }

    /**
     * 精炼炉提交炉次
     *
     * @param entity
     * @return
     */
    @PutMapping("/update/isconfirm")
    public ResponseData updateIsConfirm(@RequestBody @Validated({RefiningFurnaceRecordEntity.Update.class}) RefiningFurnaceRecordEntity entity) {
        entity.setUpdateBy(getUsername());
        entity.setUpdateTime(new Date());
        Boolean aBoolean = refiningFurnaceService.updateIsConfirm(entity);
        if (aBoolean) {
            return ResponseData.success();
        }
        return ResponseData.fail();
    }

    /**
     * 修改精炼炉出钢量
     */
    @PutMapping("/update/tapping/capacity/val")
    public ResponseData updateTappingCapacityVal(@RequestBody @Validated({RefiningFurnaceRecordEntity.Update.class}) RefiningFurnaceRecordEntity entity) {
        entity.setUpdateBy(getUsername());
        entity.setUpdateTime(new Date());
        Boolean aBoolean = refiningFurnaceService.updateTappingCapacityVal(entity);
        if (aBoolean) {
            return ResponseData.success();
        }
        return ResponseData.fail();
    }

}
