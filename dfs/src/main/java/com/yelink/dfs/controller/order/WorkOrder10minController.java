package com.yelink.dfs.controller.order;

import com.yelink.dfs.common.BaseController;
import com.yelink.dfs.entity.order.dto.WorkOrder10minDetailDTO;
import com.yelink.dfs.entity.order.dto.WorkOrderNumber10minDTO;
import com.yelink.dfs.entity.order.vo.WorkOrder10minDetailVO;
import com.yelink.dfs.entity.order.vo.WorkOrder10minOrderInfoVO;
import com.yelink.dfs.service.impl.target.metrics.MetricsWorkOrder10minExtendService;
import com.yelink.dfscommon.pojo.ResponseData;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 工单
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/work-orders/10min")
public class WorkOrder10minController extends BaseController {

    @Resource
    private MetricsWorkOrder10minExtendService metricsWorkOrder10minExtendService;

    /**
     * 实时生产数据小程序 - 获取工单号列表
     */
    @PostMapping("/number-list")
    public ResponseData orderNumberList(@RequestBody @Validated WorkOrderNumber10minDTO dto) {
        return success(metricsWorkOrder10minExtendService.orderNumberList(dto));
    }

    @PostMapping("/detail")
    public ResponseData detail(@RequestBody @Validated WorkOrder10minDetailDTO dto) {
        WorkOrder10minDetailVO result = metricsWorkOrder10minExtendService.detail(dto);
        return success(result);
    }
    @GetMapping("/order-info")
    public ResponseData orderInfo(@RequestParam String workOrderNumber) {
        WorkOrder10minOrderInfoVO result = metricsWorkOrder10minExtendService.orderInfo(workOrderNumber);
        return success(result);
    }
}
