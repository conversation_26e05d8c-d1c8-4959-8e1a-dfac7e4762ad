package com.yelink.dfs.controller.product;

import com.yelink.dfs.common.BaseController;
import com.yelink.dfs.entity.product.MaterialConfigFieldEntity;
import com.yelink.dfs.service.product.MaterialConfigFieldService;
import com.yelink.dfs.service.product.MaterialTypeConfigFieldService;
import com.yelink.dfscommon.pojo.ResponseData;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @description 物料表
 * @Date 2021/4/14
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/material/filed/config")
public class MaterialTypeConfigFieldController extends BaseController {

    private MaterialTypeConfigFieldService materialTypeConfigFieldService;
    private MaterialConfigFieldService materialConfigFieldService;

    /**
     * 查询物料类型字段默认配置列表
     *
     * @return
     */
    @GetMapping("/list")
    public ResponseData list(@RequestParam(value = "isUsing", required = false) Integer isUsing) {
//        LambdaQueryWrapper<MaterialConfigFieldEntity> queryWrapper = new LambdaQueryWrapper<>();
//        queryWrapper.eq(isUsing != null, MaterialConfigFieldEntity::getIsUsing, isUsing);
        return success(materialTypeConfigFieldService.list(isUsing));
    }

    /**
     * 查询物料类型字段默认配置列表
     *
     * @return
     */
    @GetMapping("/field/option/conf/get/{id}")
    public ResponseData getOptionConf(@PathVariable(value = "id") Integer id) {
        return success(materialConfigFieldService.getOptionConf(id));
    }

    /**
     * 更新物料类型字段配置
     *
     * @return
     */
    @PutMapping("/update")
    public ResponseData update(@RequestBody MaterialConfigFieldEntity entity) {
        materialConfigFieldService.update(entity);
        return success();
    }

    /**
     * 批量更新物料类型字段配置
     *
     * @return
     */
    @PutMapping("/batch/update")
    public ResponseData batchUpdate(@RequestBody List<MaterialConfigFieldEntity> list) {
        materialConfigFieldService.batchUpdate(list);
        return success();
    }

//    /**
//     * 物料扩展字段取值配置
//     *
//     * @param
//     * @return
//     */
//    @PostMapping("/field/option/conf/sycn")
//    public ResponseData sycnOptionConf(@RequestBody FormFieldOptionDTO optionDTO) {
//        materialConfigFieldService.sycnOptionConf(optionDTO);
//        return success();
//    }

    /**
     * 查询对应物料类型的字段配置
     *
     * @return
     */
    @GetMapping("/type/list/{materialTypeId}")
    public ResponseData typeList(@PathVariable Integer materialTypeId) {
        return success(materialTypeConfigFieldService.detailByMaterialTypeId(materialTypeId));
    }

    /**
     * 获取有启用字段全集
     *
     * @return
     */
    @GetMapping("/list/using")
    public ResponseData usingList() {
        return success(materialTypeConfigFieldService.listUsing());
    }
}
