package com.yelink.dfs.controller.supplier;

import cn.hutool.core.util.RandomUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yelink.dfs.common.BaseController;
import com.yelink.dfs.constant.OperationType;
import com.yelink.dfs.constant.order.CounterpartTypeEnum;
import com.yelink.dfs.constant.supplier.SupplierCategoryEnum;
import com.yelink.dfs.constant.supplier.SupplierMaterialStateEnum;
import com.yelink.dfs.constant.supplier.SupplierStateEnum;
import com.yelink.dfs.entity.common.CommonState;
import com.yelink.dfs.entity.order.CounterpartEntity;
import com.yelink.dfs.entity.order.dto.CounterpartDTO;
import com.yelink.dfs.entity.supplier.SupplierEntity;
import com.yelink.dfs.entity.supplier.SupplierMaterialEntity;
import com.yelink.dfs.entity.supplier.dto.SupplierMaterialBatchUpdateDTO;
import com.yelink.dfs.entity.supplier.dto.SupplierMaterialByMaterialDTO;
import com.yelink.dfs.entity.supplier.dto.SupplierMaterialDTO;
import com.yelink.dfs.open.v1.supplier.dto.SupplierSelectDTO;
import com.yelink.dfs.service.common.DictService;
import com.yelink.dfs.service.order.CustomerService;
import com.yelink.dfs.service.supplier.SupplierMaterialExpandService;
import com.yelink.dfs.service.supplier.SupplierMaterialService;
import com.yelink.dfs.service.supplier.SupplierService;
import com.yelink.dfscommon.annotation.OperLog;
import com.yelink.dfscommon.constant.Constant;
import com.yelink.dfscommon.constant.ParamException;
import com.yelink.dfscommon.constant.RedisKeyPrefix;
import com.yelink.dfscommon.constant.RespCodeEnum;
import com.yelink.dfscommon.constant.approve.config.ApprovalStatusEnum;
import com.yelink.dfscommon.dto.ApproveBatchDTO;
import com.yelink.dfscommon.dto.ImportProgressDTO;
import com.yelink.dfscommon.entity.CommonType;
import com.yelink.dfscommon.entity.dfs.DictEntity;
import com.yelink.dfscommon.entity.dfs.supplier.SupplierMaterialSelectDTO;
import com.yelink.dfscommon.pojo.ResponseData;
import com.yelink.dfscommon.utils.DateUtil;
import com.yelink.dfscommon.utils.EasyExcelUtil;
import com.yelink.dfscommon.utils.ExcelUtil;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @description 供应商档案
 * @Date 2021/4/7
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/suppliers")
public class SupplierController extends BaseController {
    @Resource
    private SupplierService supplierService;
    @Resource
    private SupplierMaterialService supplierMaterialService;
    @Resource
    private CustomerService customerService;
    @Resource
    private DictService dictService;
    private final RedisTemplate redisTemplate;

    private final SupplierMaterialExpandService supplierMaterialExpandService;


    /**
     * 查询供应商列表 / 查询所有非不合格供应商
     *
     * @param name        名称
     * @param code        编号
     * @param currentPage 当前页
     * @param size        当前页条数
     * @param type        类型
     *                    states
     * @return Page<SupplierEntity>
     */
    @GetMapping("/list")
    public ResponseData list(@RequestParam(value = "name", required = false) String name,
                             @RequestParam(value = "approvalStatus", required = false) String approvalStatus,
                             @RequestParam(value = "code", required = false) String code,
                             @RequestParam(value = "currentPage", required = false) Integer currentPage,
                             @RequestParam(value = "size", required = false) Integer size,
                             @RequestParam(value = "type", required = false) String type,
                             @RequestParam(value = "state", required = false) String states,
                             @RequestParam(value = "category", required = false) String category,
                             @RequestParam(value = "contactsName", required = false) String contacts,
                             @RequestParam(value = "counterpartName", required = false) String counterpartName) {
        Page<SupplierEntity> list = supplierService.list(name, approvalStatus, contacts, counterpartName, code, currentPage, size, type, states, category);
        return success(list);
    }

    @RequestMapping("/list2")
    public ResponseData list(SupplierSelectDTO dto) {
        return success(supplierService.getList(dto));
    }

    /**
     * 新增供应商，同时绑定所关联的原料
     *
     * @param supplierEntity 供应商对象
     * @param bindingResult
     * @return
     */
    @PostMapping("/insert")
    @OperLog(module = "采购管理", type = OperationType.ADD, desc = "新增了供应商编码为#{code}的供应商")
    public ResponseData add(@RequestBody @Valid SupplierEntity supplierEntity, BindingResult bindingResult) {
        if (bindingResult.hasErrors()) {
            throw new ParamException(bindingResult);
        }
        Date date = DateUtil.formatToDate(new Date(), DateUtil.DATETIME_FORMAT);
        supplierEntity.setCreateBy(getUsername());
        supplierEntity.setCreateTime(date);
        if (supplierService.saveEntity(supplierEntity)) {
            return success(supplierEntity);
        }
        return fail();
    }

    /**
     * 新增生效的供应商，同时绑定所关联的原料
     *
     * @param supplierEntity 供应商对象
     * @param bindingResult
     * @return
     */
    @PostMapping("/insert/released")
    @OperLog(module = "采购管理", type = OperationType.ADD, desc = "新增了供应商编码为#{code}的供应商")
    public ResponseData addReleasedEntity(@RequestBody @Valid SupplierEntity supplierEntity, BindingResult bindingResult) {
        if (bindingResult.hasErrors()) {
            throw new ParamException(bindingResult);
        }
        String username = getUsername();
        supplierEntity.setCreateBy(username);
        supplierEntity.setUpdateBy(username);
        Date now = new Date();
        supplierEntity.setCreateTime(now);
        supplierEntity.setUpdateTime(now);
        supplierService.saveReleasedEntity(supplierEntity);
        return success(supplierEntity);
    }
    @ApiOperation(value = "供应商列表")
    @GetMapping("/category/enum")
    public ResponseData supplerCategoryEnum() {
        return ResponseData.success(CommonType.covertToList(SupplierCategoryEnum.class));
    }

    /**
     * 修改供应商
     *
     * @param supplierEntity 供应商对象
     * @return
     */
    @PutMapping("/update")
    @OperLog(module = "采购管理", type = OperationType.UPDATE, desc = "修改了供应商编码为#{code}的供应商")
    public ResponseData update(@RequestBody SupplierEntity supplierEntity) {
        supplierEntity.setUpdateTime(new Date());
        supplierEntity.setUpdateBy(getUsername());
        supplierService.updateEntityById(supplierEntity);
        return success(supplierEntity);
    }

    /**
     * 删除供应商对象
     *
     * @param id 供应商ID
     * @return
     */
    @DeleteMapping("/delete/{id}")
    @OperLog(module = "采购管理", type = OperationType.DELETE, desc = "删除了供应商编码为#{code}的供应商")
    public ResponseData delete(@PathVariable Integer id) {
        SupplierEntity entity = supplierService.getById(id);
        supplierService.removeById(id);
        supplierMaterialService.removeBySupplierId(id);
        return success(entity);
    }

    /**
     * 查询供应商详细信息，同时查询绑定的物料
     *
     * @param code 供应商ID
     * @return
     */
    @GetMapping("/detail/{code}")
    public ResponseData detail(@PathVariable(value = "code") String code) {
        return ResponseData.success(supplierService.getEntityByCode(code));
    }
    @GetMapping("/detailByCode")
    public ResponseData detailByCode(@RequestParam(value = "code") String code) {
        return ResponseData.success(supplierService.getEntityByCode(code));
    }

    /**
     * 获取需求清单状态
     *
     * @return
     */
    @GetMapping("/state")
    public ResponseData getRequestState() {
        List<CommonState> list = new ArrayList<>();
        SupplierStateEnum[] values = SupplierStateEnum.values();
        for (SupplierStateEnum value : values) {
            CommonState type = new CommonState();
            type.setCode(value.getCode());
            type.setName(value.getName());
            list.add(type);
        }
        return success(list);
    }

    /**
     * 供应商档案--审批
     */
    @GetMapping("/examine/approve")
    public ResponseData examineOrApprove(@RequestParam(value = "approvalStatus") Integer approvalStatus,
                                         @RequestParam(value = "approvalSuggestion", required = false) String approvalSuggestion,
                                         @RequestParam(value = "id") Integer id) {
        String username = getUsername();
        supplierService.examineOrApprove(approvalStatus, approvalSuggestion, id, username);
        return success();
    }

    /**
     * 批量审批
     *
     * @return
     */
    @PostMapping("/approve/batch")
    public ResponseData approveBatch(@RequestBody ApproveBatchDTO dto) {
        dto.setActualApprover(getUsername());
        supplierService.approveBatch(dto);
        return success();
    }

    /**
     * 获取供应商物料清单
     *
     * @return
     */
    @PostMapping("/material/list")
    public ResponseData getMaterialList(@RequestBody SupplierMaterialSelectDTO selectDTO) {
        Page<SupplierMaterialDTO> page = supplierService.getMaterialList(selectDTO);
        return success(page);
    }

    /**
     * 获取供应商简单列表
     *
     * @return
     */
    @GetMapping("/simple/list")
    public ResponseData getSimpleList(@RequestParam(value = "state", required = false) Integer state) {
        List<SupplierEntity> list = supplierService.lambdaQuery().eq(state != null, SupplierEntity::getState, state).list();
        return success(list);
    }

    /**
     * 获取供应商物料简单列表
     *
     * @return
     */
    @GetMapping("/materials")
    public ResponseData getMaterials(@RequestParam(value = "materialCode", required = false) String materialCode) {
        List<SupplierMaterialDTO> list = supplierService.getMaterials(materialCode);
        return success(list);
    }

    /**
     * 获取供应商未有的物料列表
     *
     * @return
     */
    @GetMapping("/not/exist/materials/{supplierId}")
    public ResponseData getNotExistMaterials(@PathVariable Integer supplierId) {
        ArrayList<SupplierMaterialDTO> list = supplierService.getNotExistMaterials(supplierId);
        return success(list);
    }

    /**
     * 查询供应商详细信息和供应商物料
     *
     * @param supplierId 供应商ID
     * @return
     */
    @GetMapping("/material/detail/{supplierId}")
    public ResponseData getDetailWithMaterials(@PathVariable Integer supplierId) {
        return ResponseData.success(supplierService.getDetailWithMaterials(supplierId));
    }

    /**
     * 以供应商维度 刷新供应商物料清单
     *
     * @param
     * @return
     */
    @PostMapping("/insert/material")
    @OperLog(module = "采购管理", type = OperationType.UPDATE, desc = "刷新了供应商编码为#{code}的供应商物料清单")
    public ResponseData refreshSupplierMaterialsBySupplier(@RequestBody SupplierEntity entity) {
        supplierService.refreshSupplierMaterialsBySupplier(entity);
        return success(entity);
    }

    /**
     * 以物料维度 刷新供应商物料清单
     *
     * @param
     * @return
     */
    @PostMapping("/insert/material/by_material")
    @OperLog(module = "采购管理", type = OperationType.UPDATE, desc = "刷新了物料id为#{materialId}的供应商物料清单")
    public ResponseData refreshSupplierMaterialsByMaterial(@RequestBody SupplierMaterialByMaterialDTO dto) {
        supplierService.refreshSupplierMaterialsByMaterial(dto);
        return success(dto);
    }

    /**
     * 删除供应商物料清单
     *
     * @param
     * @return
     */
    @DeleteMapping("/delete/material")
    @OperLog(module = "采购管理", type = OperationType.DELETE, desc = "删除了ID为#{supplierId}的供应商，物料ID为#{materialId}的物料")
    public ResponseData deleteRawMaterial(@RequestParam(value = "supplierId") Integer supplierId,
                                          @RequestParam(value = "materialId") Integer materialId) {
        SupplierMaterialEntity entity = supplierMaterialService.removeEntityById(materialId, supplierId);
        return success(entity);
    }


    /**
     * 获取供应商物料清单状态以及对应的code
     * {@link SupplierMaterialStateEnum}
     *
     * @return
     */
    @GetMapping("/materials/all/state")
    public ResponseData getMaterialListAllState() {
        return success(supplierMaterialService.getMaterialAllState());
    }

    /**
     * 批量更新供应商物料清单的物料状态
     *
     * @param
     * @return
     */
    @PostMapping("/batch/update/material")
    @OperLog(module = "采购管理", type = OperationType.UPDATE, desc = "批量更新了供应商物料清单")
    public ResponseData batchUpdateSupplierMaterials(@RequestBody SupplierMaterialBatchUpdateDTO batchUpdateDTO) {
        supplierService.batchUpdateSupplierMaterials(batchUpdateDTO);
        return success();
    }

    /**
     * 新增供应商档案对接人
     *
     * @return
     */
    @PostMapping("/insert/counterpart")
    public ResponseData insertCounterpart(@RequestBody CounterpartDTO counterpartDTO) {
        String type = CounterpartTypeEnum.SUPPLIER_TYPE.getType();
        customerService.insertCounterpart(counterpartDTO, type);
        return success();
    }

    /**
     * 查询供应商档案对接人
     * isEnable   有值时只查询对接人配置信息
     *
     * @return
     */
    @GetMapping("/get/counterpart")
    public ResponseData getCounterparts(@RequestParam(value = "isEnable", required = false) String isEnable) {
        String type = CounterpartTypeEnum.SUPPLIER_TYPE.getType();
        List<CounterpartEntity> customerEntities = customerService.getCounterparts(type, isEnable);
        return success(customerEntities);
    }

    /**
     * 查询已存在的供应商档案对接人
     *
     * @return
     */
    @GetMapping("/get/exist/counterpart")
    public ResponseData getExistCounterparts() {
        List<CounterpartEntity> lists = supplierService.getExistCounterparts();
        return success(lists);
    }

    /**
     * 新增供应商类型
     *
     * @return
     */
    @PostMapping("/insert/supplier/type")
    public ResponseData insertCounterpart(@RequestBody DictEntity dictEntity) {
        String username = getUsername();
        dictService.addSupplierType(dictEntity, username);
        return success();
    }

    /**
     * 获取供应商类型分页
     *
     * @return
     */
    @GetMapping("/get/supplier/type")
    public ResponseData getSupplierTypePage(@RequestParam(value = "current", required = false) Integer current,
                                            @RequestParam(value = "size", required = false) Integer size,
                                            @RequestParam(value = "code", required = false) String code,
                                            @RequestParam(value = "name", required = false) String name) {
        Page<DictEntity> entity = dictService.getSupplierType(current, size, code, name);
        return success(entity);
    }

    /**
     * 获取供应商类型不分页
     *
     * @return
     */
    @GetMapping("/get/supplier/type/list")
    public ResponseData getSupplierTypes() {
        List<DictEntity> entity = dictService.getSupplierTypes();
        return success(entity);
    }

    /**
     * 删除供应商类型
     *
     * @return
     */
    @DeleteMapping("/delete/supplier/type/{id}")
    public ResponseData deleteProcessAssembly(@PathVariable(value = "id") Integer id) {
        supplierService.removeSupplierTypeById(id);
        return success();
    }

    /**
     * 更新供应商类型
     *
     * @return
     */
    @PutMapping("/update/supplier/type")
    public ResponseData updateSupplierType(@RequestBody DictEntity dictEntity) {
        String username = getUsername();
        supplierService.updateSupplier(dictEntity, username);
        return success();
    }

    /**
     * 获取所有状态以及对应的code
     */
    @GetMapping("/all/state")
    public ResponseData getAllState() {
        List<ApprovalStatusEnum> values = ApprovalStatusEnum.getSystemValues();
        List<CommonState> states = new ArrayList<>();
        for (ApprovalStatusEnum stateEnum : values) {
            states.add(CommonState.builder().code(stateEnum.getCode()).name(stateEnum.getName()).build());
        }
        return success(states);
    }

    /**
     * 下载供应商档案模板
     */
    @GetMapping("/export/template")
    public void exportTemplate(HttpServletResponse response) {
        supplierService.exportTemplate(response);
    }

    /**
     * 供应商列表导出
     *
     * @param name        名称
     * @param code        编号
     * @param currentPage 当前页
     * @param size        当前页条数
     * @param type        类型
     *                    states
     * @return Page<SupplierEntity>
     */
    @GetMapping("/export/supplier")
    public void exportSupplier(@RequestParam(value = "name", required = false) String name,
                               @RequestParam(value = "approvalStatus", required = false) String approvalStatus,
                               @RequestParam(value = "code", required = false) String code,
                               @RequestParam(value = "currentPage", required = false) Integer currentPage,
                               @RequestParam(value = "size", required = false) Integer size,
                               @RequestParam(value = "type", required = false) String type,
                               @RequestParam(value = "state", required = false) String states,
                               @RequestParam(value = "category", required = false) String category,
                               @RequestParam(value = "contactsName", required = false) String contacts,
                               @RequestParam(value = "counterpartName", required = false) String counterpartName,
                               HttpServletResponse response) {
        supplierService.exportSupplierList(name, approvalStatus, contacts, counterpartName, code, currentPage, size, type, states, category, response);
    }

    /**
     * 供应商档案信息导入
     *
     * @return
     */
    @PostMapping("/upload/supplier")
    public ResponseData uploadSupplierList(MultipartFile file) {
        //1、判断导入文件的合法性
        ExcelUtil.checkImportExcelFile(file);
        //2、初始化处理进度
        ImportProgressDTO build = ImportProgressDTO.builder()
                .progress(Double.valueOf(Constant.ZERO_VALUE))
                .executionDescription("正在处理中，请耐心等候...")
                .executionStatus(false)
                .build();
        redisTemplate.opsForValue().set(RedisKeyPrefix.SUPPLIER_IMPORT_PROGRESS, JSONObject.toJSONString(build), 1, TimeUnit.HOURS);
        //4、加锁
        Boolean lockStatus = redisTemplate.opsForValue().setIfAbsent(RedisKeyPrefix.SUPPLIER_IMPORT_LOCK, new Date(), 1, TimeUnit.HOURS);
        if (lockStatus == null || !lockStatus) {
            log.info("获取redis同步锁失败");
            return fail(RespCodeEnum.IMPORT_OPREATION_NOT_RE);
        }
        String username = getUsername();
        supplierService.uploadCustomerList(file, username);
        return ResponseData.success();
    }


    /**
     * 查询导入进度
     *
     * @return
     */
    @GetMapping("/import/progress")
    public ResponseData getImportProgress() {
        Object obj = redisTemplate.opsForValue().get(RedisKeyPrefix.SUPPLIER_IMPORT_PROGRESS);
        return success(obj);
    }


    /**
     * 供应商物料导入模板下载
     *
     * @param response
     * @throws Exception
     */
    @RequestMapping("/down/default/template")
    public void downloadDefaultTemplate(HttpServletResponse response) throws Exception {
        supplierMaterialExpandService.downloadDefaultTemplate("classpath:template/supplierMaterialTemplate.xlsx", response, "供应商物料导入模板" + Constant.XLSX);
    }

    /**
     * 根据模板导入供应商物料数据
     *
     * @param file
     * @return
     * @throws Exception
     */
    @RequestMapping("/import/supplier/material/data")
    public ResponseData importSupplierMaterialData(MultipartFile file) throws Exception {
        ExcelUtil.checkImportExcelFile(file);
        String importProgressKey = RedisKeyPrefix.SUPPLIER_MATERIAL_IMPORT_PROGRESS + DateUtil.format(new Date(), DateUtil.yyyyMMddHHmmss_FORMAT) + "_" + RandomUtil.randomString(2);
        supplierMaterialExpandService.sycImportData(file.getOriginalFilename(), file.getInputStream(), getUsername(),importProgressKey);
        return success(importProgressKey);
    }


    /**
     * 查询导入进度
     *
     * @return
     */
    @RequestMapping("/import/supplier/material/progress")
    public ResponseData getImportMaterialProgress() {
        return success(supplierMaterialExpandService.importProgress());
    }

    /**
     * 供应商物料清单导出
     *
     * @return
     */
    @PostMapping("/materials/export")
    public void exportCustomerMaterialListList(@RequestBody SupplierMaterialSelectDTO selectDTO,
                                               HttpServletResponse response) throws IOException {
        selectDTO.setCurrent(1);
        selectDTO.setSize(Integer.MAX_VALUE);
        Page<SupplierMaterialDTO> page = supplierService.getMaterialList(selectDTO);
        EasyExcelUtil.export(response, "供应商物料清单", "供应商物料清单", page.getRecords(), SupplierMaterialDTO.class);
    }

}
