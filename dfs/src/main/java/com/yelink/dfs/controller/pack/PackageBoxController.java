package com.yelink.dfs.controller.pack;

import com.yelink.dfs.common.BaseController;
import com.yelink.dfs.constant.Constant;
import com.yelink.dfs.constant.pack.PackageBoxStateEnum;
import com.yelink.dfs.entity.management.PackageRecordEntity;
import com.yelink.dfs.entity.order.dto.WorkOrderInspectionPackageSelectDTO;
import com.yelink.dfs.entity.order.vo.InspectionPackageCountVO;
import com.yelink.dfs.entity.pack.PackageBoxEntity;
import com.yelink.dfs.entity.pack.PackageBoxMacSnRelationEntity;
import com.yelink.dfs.entity.pack.PackageTempRelateCodeEntity;
import com.yelink.dfs.entity.pack.dto.PackageBoxAddDTO;
import com.yelink.dfs.entity.pack.dto.PackageBoxBatchAddDTO;
import com.yelink.dfs.entity.pack.dto.PackageBoxPrintBagDTO;
import com.yelink.dfs.entity.pack.dto.PackageBoxSelectDTO;
import com.yelink.dfs.entity.pack.dto.PackageUnpackDTO;
import com.yelink.dfs.entity.pack.vo.PackageBoxViewVO;
import com.yelink.dfs.open.v1.locationMachine.dto.PackageCompleteDTO;
import com.yelink.dfs.open.v1.locationMachine.dto.PackageCreateBoxDTO;
import com.yelink.dfs.service.management.PackageRecordService;
import com.yelink.dfs.service.pack.PackageBoxMacSnRelationService;
import com.yelink.dfs.service.pack.PackageBoxService;
import com.yelink.dfs.service.pack.PackageTempRelateCodeService;
import com.yelink.dfscommon.constant.Constants;
import com.yelink.dfscommon.constant.dfs.pack.PackageBoxRelateTypeEnum;
import com.yelink.dfscommon.entity.CommonType;
import com.yelink.dfscommon.entity.dfs.barcode.PrintDTO;
import com.yelink.dfscommon.pojo.ResponseData;
import com.yelink.dfscommon.utils.EasyExcelUtil;
import com.yelink.dfscommon.utils.JacksonUtil;
import com.yelink.dfscommon.utils.PageData;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2023/3/22 10:42
 */
@Slf4j
@RestController
@AllArgsConstructor
public class PackageBoxController extends BaseController {
    private final PackageBoxService packageBoxService;
    private final PackageRecordService packageRecordService;
    private final PackageBoxMacSnRelationService packageBoxMacSnRelationService;
    private final PackageTempRelateCodeService packageTempRelateCodeService;

    /**
     * 包装追溯-包装视图列表
     */
    @PostMapping("/package/box/list")
    public ResponseData listWorkPackageBox(@RequestBody PackageBoxSelectDTO selectDTO) {
        Assert.notNull(selectDTO.getCurrent(), "current不能为null");
        Assert.notNull(selectDTO.getSize(), "size不能为null");
        PageData<PackageBoxViewVO> pageData = packageBoxService.listWorkPackageBox(selectDTO);
        return ResponseData.success(pageData);
    }
    /**
     * 包装追溯-包装视图列表-导出
     */
    @PostMapping("/package/box/list/export")
    public void listPackageBoxExport(HttpServletResponse response, @RequestBody PackageBoxSelectDTO selectDTO) throws IOException {
        selectDTO.setCurrent(1);
        selectDTO.setSize(Integer.MAX_VALUE);
        PageData<PackageBoxViewVO> pageData = packageBoxService.listWorkPackageBox(selectDTO);
        EasyExcelUtil.export(response, "包装视图" + Constant.XLSX, "sheet", pageData.getRecords(), PackageBoxViewVO.class);
    }

    /**
     * 包装追溯-包装视图列表-详情
     */
    @GetMapping("/package/box/get")
    public ResponseData getBoxById(@RequestParam Integer boxId) {
        PackageBoxViewVO packageBox = packageBoxService.getPackageBoxById(boxId);
        return ResponseData.success(packageBox);
    }

    /**
     * 获取包装箱标列表
     */
    @GetMapping("/package/box/number/list")
    public ResponseData listPackageBox(@RequestParam String workOrderNumber) {
        List<PackageBoxEntity> packageBoxList = packageBoxService.lambdaQuery()
                .eq(PackageBoxEntity::getWorkOrderNumber, workOrderNumber)
                .orderByDesc(PackageBoxEntity::getCreateTime)
                .list();
        List<Integer> boxIds = packageBoxList.stream().map(PackageBoxEntity::getId).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(boxIds)) {
            for (PackageBoxEntity packageBox : packageBoxList) {
                Long count = packageBoxMacSnRelationService.lambdaQuery()
                        .eq(PackageBoxMacSnRelationEntity::getBoxId, packageBox.getId())
                        .count();
                packageBox.setPackageCount(count.intValue());
            }
        }
        return ResponseData.success(packageBoxList);
    }
    /**
     * 获取包装详情
     */
    @GetMapping("/package/box/detail")
    public ResponseData packageBoxDetail(@RequestParam Integer boxId) {
        PackageBoxEntity byId = packageBoxService.getById(boxId);
        List<PackageBoxMacSnRelationEntity> list = packageBoxMacSnRelationService.lambdaQuery()
                .eq(PackageBoxMacSnRelationEntity::getBoxId, boxId)
                .list();
        byId.setRelationList(list);
        return ResponseData.success(byId);
    }

    /**
     * 通用包装工位机-删除空箱
     */
    @DeleteMapping("/package/box/delete/{id}")
    public ResponseData packageBoxDelete(@PathVariable Integer id) {
        PackageBoxEntity entity = packageBoxService.deleteById(id);
        return ResponseData.success(entity);
    }

    /**
     * 关联类型列表
     *
     * @return
     */
    @GetMapping("/package/box/relate/type")
    public ResponseData packageBoxRelateType() {
        return ResponseData.success(CommonType.covertToList(PackageBoxRelateTypeEnum.class));
    }

    /**
     * 外箱包装工位机- 查询铭牌sn码列表
     */
    @GetMapping("/package/list/sn")
    public ResponseData listMacSn(@RequestParam String boxNumber) {
        List<PackageBoxMacSnRelationEntity> list = packageBoxMacSnRelationService.lambdaQuery()
                .eq(PackageBoxMacSnRelationEntity::getBoxNumber, boxNumber)
                .orderByDesc(PackageBoxMacSnRelationEntity::getCreateTime)
                .list();
        return ResponseData.success(list);
    }

    /**
     * 通用包装工位机-获取本层级已包装数
     */
    @GetMapping("/general/package/get/level/package/num")
    public ResponseData getLevelPackageNumber(@RequestParam String workOrderNumber,
                                              @RequestParam Integer packageLevelId) {
        Long count = packageBoxService.lambdaQuery()
                .eq(PackageBoxEntity::getPackageContainerId, packageLevelId)
                .eq(PackageBoxEntity::getWorkOrderNumber, workOrderNumber)
                .eq(PackageBoxEntity::getState, PackageBoxStateEnum.PACKAGE_COMPLETED.getCode())
                .count();
        return ResponseData.success(count);
    }
    /**
     * 通用包装工位机-生成箱标
     */
    @PostMapping("/general/package/create/box")
    public ResponseData createBox(@RequestParam String boxNumber,
                                  @RequestParam String workOrderNumber,
                                  @RequestParam Integer packageLevelId,
                                  @RequestParam(required = false) Integer ruleId) {
        Assert.isTrue(StringUtils.isNotBlank(boxNumber), "boxNumber不能为空");
        PackageCreateBoxDTO dto = PackageCreateBoxDTO.builder().boxNumber(boxNumber).relateNumber(workOrderNumber)
                .packageLevelId(packageLevelId).ruleId(ruleId).username(getUsername())
                .relateType(PackageBoxRelateTypeEnum.WORK_ORDER.getCode()).build();
        PackageBoxEntity packageBox = packageBoxService.createBox(dto);
        return ResponseData.success(packageBox);
    }

    /**
     * 通用包装工位机-扫码,添加关联
     */
    @PostMapping("/general/package/scan/code")
    public ResponseData scanCode(@RequestParam String scanCode,
                                 @RequestParam String boxNumber,
                                 @RequestParam String workOrderNumber,
                                 @RequestParam Integer facId,
                                 @RequestParam Integer packageLevelId,
                                 @RequestParam(required = false) Boolean enableScanSnAdd) {
        packageBoxService.packageScanSn(scanCode, boxNumber, workOrderNumber, facId, packageLevelId, enableScanSnAdd);
        return ResponseData.success();
    }
    /**
     * 通用包装工位机-移除此箱
     */
    @DeleteMapping("/general/package/remove/sn")
    public ResponseData generalPackageRemoveRelation(@RequestParam String nameplateSn,
                                                     @RequestParam String boxNumber,
                                                     @RequestParam String workOrderNumber,
                                                     @RequestParam Integer facId) {
        packageBoxService.generalPackageRemoveRelation(nameplateSn, boxNumber, workOrderNumber, facId, PackageBoxRelateTypeEnum.WORK_ORDER.getCode());
        return ResponseData.success();
    }

    /**
     * 机加包装工位机-移除此箱
     */
    @DeleteMapping("/general/mache/package/remove/sn")
    public ResponseData generalMachePackageRemoveRelation(@RequestParam Integer id) {
        packageBoxService.generalMachePackageRemoveRelation(id);
        return ResponseData.success();
    }

    /**
     * 通用包装工位机- 查询铭牌sn码列表
     */
    @GetMapping("/general/package/list/sn")
    public ResponseData listMacSn(@RequestParam String boxNumber,
                                  @RequestParam String workOrderNumber) {
        PackageBoxEntity packageBox = packageBoxService.getPackageBoxByNumber(boxNumber, workOrderNumber);
        List<PackageBoxMacSnRelationEntity> list = packageBoxMacSnRelationService.lambdaQuery()
                .eq(PackageBoxMacSnRelationEntity::getBoxId, packageBox.getId())
                .orderByDesc(PackageBoxMacSnRelationEntity::getCreateTime)
                .list();
        return ResponseData.success(list);
    }

    /**
     * 机加包装工位机- 查询包装记录
     */
    @GetMapping("/general/package/list/record")
    public ResponseData listRecord(@RequestParam String boxNumber,
                                  @RequestParam String workOrderNumber) {
        return ResponseData.success(packageRecordService.lambdaQuery().eq(PackageRecordEntity::getWorkOrderNumber, workOrderNumber)
                .eq(PackageRecordEntity::getContainerSn, boxNumber)
                .eq(PackageRecordEntity::getIsDelete, Constants.SKU_ID_DEFAULT_VAL)
                .list());
    }
    /**
     * 通用包装工位机-继续包装-未完成合箱、已拆箱列表， 标签补打-已完成包装的列表
     */
    @GetMapping("/general/package/list")
    public ResponseData listPackageBox(@RequestParam String workOrderNumber,
                                       @RequestParam(required = false) String states,
                                       @RequestParam(required = false) Integer packageLevelId) {
        List<PackageBoxEntity> list = packageBoxService.generalListPackageBox(workOrderNumber, states, packageLevelId);
        return ResponseData.success(list);
    }
    /**
     * 通用包装工位机-完成包装
     */
    @PostMapping("/general/package/complete")
    public ResponseData generalPackageComplete(@RequestParam String boxNumber,
                                               @RequestParam String workOrderNumber,
                                               @RequestParam Integer facId) {
        PackageCompleteDTO dto = PackageCompleteDTO.builder().boxNumber(boxNumber).relateNumber(workOrderNumber).fid(facId)
                .username(getUsername()).relateType(PackageBoxRelateTypeEnum.WORK_ORDER.getCode()).build();
        packageBoxService.generalPackageComplete(dto);
        return ResponseData.success();
    }

    /**
     * 机加包装工位机-完成包装
     */
    @PostMapping("/general/mache/package/complete")
    public ResponseData generalMachePackageComplete(@RequestParam String boxNumber,
                                                    @RequestParam String workOrderNumber) {
        packageBoxService.generalMachePackageComplete(boxNumber, workOrderNumber, getUsername());
        return ResponseData.success();
    }

    /**
     * 通用包装工位机
     * 外箱包装工位机- 打印-返回数据
     */
    @PostMapping("/package/print")
    public ResponseData packagePrint(@RequestParam String boxNumber,
                                     @RequestParam String workOrderNumber,
                                     @RequestParam Integer ruleId) {
        PrintDTO printDTO = packageBoxService.printPackage(boxNumber, workOrderNumber, ruleId);
        return ResponseData.success(printDTO);
    }

    /**
     * 通用拆箱工位机-扫码,得到容器sn
     */
    @GetMapping("/general/unpacking/scan/get/package/box")
    public ResponseData generalsUnpackingScanCodeGetPackageBox(@RequestParam String scanCode,
                                                               @RequestParam String workOrderNumber,
                                                               @RequestParam Integer packageLevelId) {
        PackageBoxEntity packageBox = packageBoxService.generalsUnpackingScanCodeGetPackageBox(scanCode, workOrderNumber, packageLevelId);
        return ResponseData.success(packageBox);
    }
    /**
     * 通用拆箱工位机-扫码,得到sn
     */
    @GetMapping("/general/unpacking/scan/get/sn")
    public ResponseData generalUnpackingScanCodeGetSn(@RequestParam String scanCode,
                                                      @RequestParam String boxNumber,
                                                      @RequestParam String workOrderNumber,
                                                      @RequestParam Integer packageLevelId) {
        String nameplateSn = packageBoxService.generalUnpackingScanCodeGetSn(scanCode, boxNumber, workOrderNumber, packageLevelId);
        return ResponseData.success(nameplateSn, "操作成功");
    }
    /**
     * 通用拆箱工位机-确定拆箱
     */
    @PostMapping("/general/unpacking/confirm")
    public ResponseData generalUnpackingConfirm(@RequestBody PackageUnpackDTO packageUnpacking) {
        Assert.isTrue(!CollectionUtils.isEmpty(packageUnpacking.getNameplateSns()), "nameplateSns不能为空");
        Assert.isTrue(StringUtils.isNotBlank(packageUnpacking.getBoxNumber()), "boxNumber不能为空");
        Assert.isTrue(StringUtils.isNotBlank(packageUnpacking.getWorkOrderNumber()), "workOrderNumber不能为空");
        Assert.notNull(packageUnpacking.getFacId(), "facId不能为空");

        String username = getUsername();
        packageBoxService.generalUnpackingConfirm(packageUnpacking, username);
        return ResponseData.success();
    }
    /**
     * 通用拆箱工位机-整箱拆分
     */
    @PostMapping("/general/unpacking/whole/box/split")
    public ResponseData generalUnpackingWholeBoxSplit(@RequestBody PackageUnpackDTO packageUnpacking) {
        Assert.isTrue(StringUtils.isNotBlank(packageUnpacking.getBoxNumber()), "boxNumber不能为空");
        Assert.isTrue(StringUtils.isNotBlank(packageUnpacking.getWorkOrderNumber()), "workOrderNumber不能为空");
        Assert.notNull(packageUnpacking.getFacId(), "facId不能为空");

        String username = getUsername();
        packageBoxService.generalUnpackingWholeBoxSplit(packageUnpacking, username);
        return ResponseData.success();
    }


    /**
     * 机加包装工位机打印袋子标签
     *
     * @param packageBoxPrintBagDTO 打印的数据
     * @return
     */
    @PostMapping("/package/print/bag")
    public ResponseData print(@RequestBody PackageBoxPrintBagDTO packageBoxPrintBagDTO) {
        log.info("{}调用了打印接口：{}", getUsername(), JacksonUtil.toJSONString(packageBoxPrintBagDTO));
        PrintDTO print = packageBoxService.printBag(packageBoxPrintBagDTO,getUsername());
        return success(print);
    }

    /**
     * 机加包装工位机打印箱子标签
     *
     * @param packageBoxPrintBagDTO 打印的数据
     * @return
     */
    @PostMapping("/package/print/box")
    public ResponseData printBox(@RequestBody PackageBoxPrintBagDTO packageBoxPrintBagDTO) {
        log.info("{}调用了打印接口：{}", getUsername(), JacksonUtil.toJSONString(packageBoxPrintBagDTO));
        PrintDTO print = packageBoxService.printBox(packageBoxPrintBagDTO,getUsername());
        return success(print);
    }

    /**
     * 包装，并添加包装记录
     */
    @PostMapping("/package/box")
    public ResponseData packageBox(@RequestBody PackageBoxAddDTO packageBox) {
        packageBoxService.addPackageBox(packageBox);
        return ResponseData.success();
    }


    /**
     * 质检包装工位机: 查询左上角的计划总数、已完成数据、本工序过站数、包装箱、包装数
     */
    @PostMapping("/inspection/package/select/count")
    public ResponseData inspectionPackageSelectCount(@RequestBody WorkOrderInspectionPackageSelectDTO selectDTO) {
        InspectionPackageCountVO countVO = packageBoxService.inspectionPackageSelectCount(selectDTO);
        return success(countVO);
    }

    /**
     * 质检包装工位机, 提交后，添加可以被包装的流水码
     */
    @PostMapping("/inspection/package/add/can/package")
    public ResponseData inspectionPackageAddCanPackage(@RequestBody List<PackageTempRelateCodeEntity> list) {
        packageTempRelateCodeService.inspectionPackageAddCanPackage(list);
        return success();
    }
    /**
     * 质检包装工位机, 查询可以被包装的流水码
     */
    @GetMapping("/inspection/package/list/can/package")
    public ResponseData inspectionPackageListCanPackage(@RequestParam String workOrderNumber,
                                                        @RequestParam Integer craftProcedureId) {
        List<PackageTempRelateCodeEntity> list = packageTempRelateCodeService.lambdaQuery()
                .eq(PackageTempRelateCodeEntity::getWorkOrderNumber, workOrderNumber)
                .eq(PackageTempRelateCodeEntity::getCraftProcedureId, craftProcedureId)
                .orderByDesc(PackageTempRelateCodeEntity::getCreateTime)
                .list();
        return success(list);
    }
    /**
     * 凯福士质检包装工位机, 包装，并添加包装记录，包装完成
     */
    @PostMapping("/inspection/package/box")
    public ResponseData inspectionPackageBox(@RequestBody PackageBoxAddDTO packageBox) {
        packageBoxService.inspectionPackageBox(packageBox);
        return ResponseData.success();
    }


    /**
     * 批量新增包装码
     */
    @PostMapping("/package/box/batch/add")
    public ResponseData batchAddPackageCode(@RequestBody PackageBoxBatchAddDTO packageBoxBatchAddDTO) {
        packageBoxService.batchAddPackageCode(packageBoxBatchAddDTO,getUsername());
        return ResponseData.success();
    }
}
