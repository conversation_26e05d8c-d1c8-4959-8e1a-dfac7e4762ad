package com.yelink.dfs.controller.product;

import cn.hutool.core.util.RandomUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yelink.dfs.common.BaseController;
import com.yelink.dfs.constant.RedisKeyPrefix;
import com.yelink.dfs.entity.product.dto.MaterialAttributeTypeSelectDTO;
import com.yelink.dfs.service.product.MaterialAttributeTypeService;
import com.yelink.dfscommon.constant.Constant;
import com.yelink.dfscommon.entity.dfs.MaterialAttributeTypeEntity;
import com.yelink.dfscommon.pojo.ResponseData;
import com.yelink.dfscommon.utils.DateUtil;
import com.yelink.dfscommon.utils.ExcelUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description 物料表
 * @Date 2021/4/14
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/material/attribute/types")
public class MaterialAttributeTypeController extends BaseController {

    private final MaterialAttributeTypeService materialAttributeTypeService;

    /**
     * 查询属性分类列表
     *
     * @param
     * @return
     */
    @PostMapping("/tree")
    public ResponseData getTree() {
        List<MaterialAttributeTypeEntity> list = materialAttributeTypeService.getTree();
        return success(list);
    }

    /**
     * 查询属性分类列表
     *
     * @param
     * @return
     */
    @PostMapping("/list")
    public ResponseData getList(@RequestBody MaterialAttributeTypeSelectDTO selectDTO) {
        Page<MaterialAttributeTypeEntity> page = materialAttributeTypeService.getList(selectDTO);
        return success(page);
    }

    /**
     * 新增属性分类
     *
     * @param
     * @return
     */
    @PostMapping("/add")
    public ResponseData add(@RequestBody MaterialAttributeTypeEntity entity) {
        String username = getUsername();
        entity.setCreateBy(username);
        entity.setUpdateBy(username);
        entity.setCreateTime(new Date());
        entity.setUpdateTime(new Date());
        materialAttributeTypeService.saveEntity(entity);
        return success(entity);
    }

    /**
     * 更新属性分类
     *
     * @param
     * @return
     */
    @PutMapping("/update")
    public ResponseData update(@RequestBody MaterialAttributeTypeEntity entity) {
        String username = getUsername();
        entity.setUpdateBy(username);
        entity.setUpdateTime(new Date());
        materialAttributeTypeService.updateEntity(entity);
        return success();
    }

    /**
     * 删除属性分类
     *
     * @param
     * @return
     */
    @DeleteMapping("/delete/{id}")
    public ResponseData delete(@PathVariable(value = "id") Integer id) {
        materialAttributeTypeService.delete(id);
        return success();
    }

    /**
     * 获取详情
     *
     * @param
     * @return
     */
    @GetMapping("/detail/{id}")
    public ResponseData detail(@PathVariable(value = "id") Integer id) {
        return success(materialAttributeTypeService.detail(id));
    }

    /**
     * 属性分类顺序调整
     *
     * @param
     * @return
     */
    @PostMapping("/seq/adjust")
    public ResponseData seqAdjust(@RequestBody List<MaterialAttributeTypeEntity> list) {
        materialAttributeTypeService.seqAdjust(list);
        return success();
    }

    /**
     * 属性分类导入:下载默认导入模板
     */
    @GetMapping("/down/default/template")
    public void downDefaultTemplate(HttpServletResponse response) throws Exception {
        materialAttributeTypeService.downloadDefaultTemplate("classpath:template/materialAttributeTypeTemplate.xlsx", response, "属性分类导入模板" + Constant.XLSX);
    }

    /**
     * 属性分类导入
     */
    @PostMapping("/import")
    public ResponseData importData(MultipartFile file) throws Exception {
        //1、判断导入文件的合法性
        ExcelUtil.checkImportExcelFile(file);
        //异步数据导入
        String importProgressKey = RedisKeyPrefix.MATERIAL_ATTRIBUTE_TYPE_IMPORT_PROGRESS + DateUtil.format(new Date(), DateUtil.yyyyMMddHHmmss_FORMAT) + "_" + RandomUtil.randomString(2);
        materialAttributeTypeService.sycImportData(file.getOriginalFilename(), file.getInputStream(), getUsername(),importProgressKey);
        return ResponseData.success(importProgressKey);
    }

    /**
     * 属性分类导入:查询导入进度
     */
    @GetMapping("/import/progress")
    public ResponseData getImportProgress() {
        return success(materialAttributeTypeService.importProgress());
    }


}
