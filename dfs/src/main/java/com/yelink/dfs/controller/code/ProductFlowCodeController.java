package com.yelink.dfs.controller.code;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.asyncexcel.core.exporter.DataExportParam;
import com.asyncexcel.core.exporter.ExportHandler;
import com.asyncexcel.core.model.ExcelTask;
import com.asyncexcel.springboot.ExcelService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yelink.dfs.common.BaseController;
import com.yelink.dfs.constant.Constant;
import com.yelink.dfs.constant.OperationType;
import com.yelink.dfs.constant.rule.RulePrefixEnum;
import com.yelink.dfs.entity.code.ProductFlowCodeEntity;
import com.yelink.dfs.entity.code.dto.ProductFlowCodeExportDTO;
import com.yelink.dfs.entity.code.dto.ScannerDTO;
import com.yelink.dfs.entity.defect.dto.StateEnumDTO;
import com.yelink.dfs.entity.product.MaterialEntity;
import com.yelink.dfs.entity.product.dto.ProductFlowCodeSelectDTO;
import com.yelink.dfs.entity.product.dto.ProductFlowJudgeDTO;
import com.yelink.dfs.entity.product.dto.RelevanceSelectDTO;
import com.yelink.dfs.entity.target.record.dto.BindBatchDTO;
import com.yelink.dfs.service.code.CodeRelevanceService;
import com.yelink.dfs.service.code.ProductFlowCodeService;
import com.yelink.dfs.service.impl.code.ProductFlowCodeExportHandler;
import com.yelink.dfs.service.impl.code.ProductFlowCodeRelevanceExportHandler;
import com.yelink.dfs.service.impl.code.ProductFlowCodeTwoExportHandler;
import com.yelink.dfs.service.rule.NumberRuleService;
import com.yelink.dfscommon.annotation.OperLog;
import com.yelink.dfscommon.common.excel.BusinessCodeEnum;
import com.yelink.dfscommon.constant.ResponseException;
import com.yelink.dfscommon.constant.dfs.code.ProductFlowCodeTypeEnum;
import com.yelink.dfscommon.constant.dfs.rule.NumberCodeDTO;
import com.yelink.dfscommon.constant.dfs.rule.NumberRuleCodeDTO;
import com.yelink.dfscommon.dto.dfs.RuleDetailDTO;
import com.yelink.dfscommon.dto.dfs.RuleSeqDTO;
import com.yelink.dfscommon.entity.dfs.CheckReportDTO;
import com.yelink.dfscommon.entity.dfs.productFlowCode.CodeRelevanceEntity;
import com.yelink.dfscommon.pojo.ResponseData;
import com.yelink.dfscommon.utils.EasyExcelUtil;
import com.yelink.dfscommon.utils.ExcelTemplateImportUtil;
import com.yelink.dfscommon.utils.ExcelUtil;
import com.yelink.dfscommon.utils.JacksonUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description 单品码
 * @Date 2022/3/31 14:36
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/production/code")
public class ProductFlowCodeController extends BaseController {

    private ProductFlowCodeService productFlowCodeService;
    public NumberRuleService numberRuleService;
    private CodeRelevanceService codeRelevanceService;
    private ExcelService excelService;
    /**
     * 查询码列表
     *
     * @return
     */
    @PostMapping("/list")
    public ResponseData list(@RequestBody ProductFlowCodeSelectDTO productFlowCodeSelectDTO) {
        Page<ProductFlowCodeEntity> list = productFlowCodeService.list(productFlowCodeSelectDTO);
        return success(list);
    }

    /**
     * 查询码详情
     *
     * @return
     */
    @GetMapping("/detail")
    public ResponseData detail(@RequestParam Integer id) {
        return success(productFlowCodeService.getDetailById(id));
    }

    /**
     * 查询码详情
     *
     * @return
     */
    @GetMapping("/detail/by/code")
    public ResponseData detail(@RequestParam String code) {
        ProductFlowCodeEntity codeEntity = productFlowCodeService.getByProductFlowCode(code);
        if (codeEntity == null) {
            throw new ResponseException("找不到对应条码信息");
        }
        return success(productFlowCodeService.getDetailById(codeEntity.getId()));
    }

    /**
     * 查询码列表(含过站记录，如无必要不调用！！！换成list)
     *
     * @return
     */
    @PostMapping("/list/show/records")
    public ResponseData listAndShowRecords(@RequestBody ProductFlowCodeSelectDTO productFlowCodeSelectDTO) {
        Page<ProductFlowCodeEntity> list = productFlowCodeService.listAndShowRecords(productFlowCodeSelectDTO);
        return success(list);
    }

    /**
     * 查询工单所用为订单流水码还是工单流水码
     *
     * @return
     */
    @PostMapping("/get/relation/type")
    public ResponseData getRelationType(@RequestBody ProductFlowCodeSelectDTO productFlowCodeSelectDTO) {
        return success(productFlowCodeService.getRelationType(productFlowCodeSelectDTO.getWorkOrder()));
    }
    /**
     * 查询订单下工艺路线
     *
     * @return
     */
    @PostMapping("/list/craft/procedure/order")
    public ResponseData listCraftProcedureListByOrderNumber(@RequestParam String orderNumber) {
        return success(productFlowCodeService.getCraftProcedureListByOrderNumber(orderNumber));
    }

    /**
     * 查询工单下工艺路线
     *
     * @return
     */
    @PostMapping("/list/craft/procedure/worker/order")
    public ResponseData listCraftProcedureListByWorkOrder(@RequestParam String workerNumber) {
        return success(productFlowCodeService.getCraftProcedureListByWorkOrder(workerNumber));
    }

    /**
     * 新增单品码
     *
     * @param entity
     * @return
     */
    @PostMapping("/add")
    public ResponseData addProductFlowCode(@RequestBody ProductFlowCodeEntity entity) {
        productFlowCodeService.addProductionCode(entity, getUsername());
        return success();
    }

    /**
     * 批量新增单品码
     *
     * @param list
     * @return
     */
    @PostMapping("/batch/add")
    public ResponseData batchAddProductionCode(@RequestBody List<ProductFlowCodeEntity> list) {
        for (ProductFlowCodeEntity entity : list) {
            if (entity.getNumberMap() == null) {
                continue;
            }
            List<RuleDetailDTO> ruleDetailDTOs = JSONArray.parseArray(entity.getRuleDetail(), RuleDetailDTO.class);
            NumberCodeDTO codeDTO = numberRuleService.getSeqById(RuleSeqDTO.builder().id(entity.getNumberRuleId())
                    .ruleType(ProductFlowCodeTypeEnum.getTypeByCode(entity.getType())).relatedMap(entity.getRelatedMap())
                    .numberMap(entity.getNumberMap()).build());
            List<NumberRuleCodeDTO> prefixValueList = codeDTO.getPrefixValueList();
            for (int i = 0; i < prefixValueList.size(); i++) {
                NumberRuleCodeDTO dto = prefixValueList.get(i);
                // 匹配手动输入
                if (RulePrefixEnum.ARTIFICIAL_TYPE.getCode().equals(dto.getCode())) {
                    dto.setValue(ruleDetailDTOs.get(i).getValue());
                }
            }
            entity.setRuleDetail(JSON.toJSONString(prefixValueList));
        }
        productFlowCodeService.batchAddProductionCode(list, getUsername());
        return success();
    }

    /**
     * 同步单品码
     *
     * @param entityList
     * @return
     */
    @PostMapping("/syn")
    public ResponseData synProductFlowCode(@RequestBody List<ProductFlowCodeEntity> entityList) {
        productFlowCodeService.synProductFlowCode(entityList,getUsername());
        return success();
    }


    /**
     * 批量更新，打印后改变状态(未打印--已打印)
     *
     * @param productFlowCodeDTO 上料记录对象列表
     * @return
     */
    @PutMapping("/update")
    public ResponseData updateBatchEntity(@RequestBody ProductFlowCodeSelectDTO productFlowCodeDTO) {
        productFlowCodeService.updateBatchEntity(productFlowCodeDTO);
        return success();
    }


    /**
     * 校验删除单品码
     *
     * @return
     */
    @DeleteMapping("/check/delete/{id}")
    public ResponseData checkDeleteProductFlowCode(@PathVariable Integer id) {
        return success(productFlowCodeService.checkRemoveProductFlowCode(id));
    }

    /**
     * 删除单品码
     *
     * @return
     */
    @DeleteMapping("/delete/{id}")
    public ResponseData deleteProductFlowCode(@PathVariable Integer id) {
        productFlowCodeService.removeProductFlowCode(id);
        return success();
    }


    /**
     * 勾选多个单品码导出excel
     *
     * @param productFlowCodeSelectDTO
     * @return
     */
    @GetMapping("/excel/export")
    public void exportExcel(
            ProductFlowCodeSelectDTO productFlowCodeSelectDTO,
            HttpServletResponse response) throws IOException {
        productFlowCodeService.exportExcel(productFlowCodeSelectDTO, response);
    }

    /**
     * 默认导出模板
     */
    @GetMapping("/default/export/template")
    public void downloadSaleOrderDefaultExportTemplate(HttpServletResponse response) throws IOException {
        ProductFlowCodeSelectDTO selectDTO = new ProductFlowCodeSelectDTO();
        selectDTO.setLimit(10);
        Page<ProductFlowCodeEntity> page = productFlowCodeService.exportList(selectDTO);
        List<ProductFlowCodeExportDTO> list = JacksonUtil.convertArray(page.getRecords(), ProductFlowCodeExportDTO.class);
        EasyExcelUtil.export(response, "物料条码默认导出模板", "数据源", list, ProductFlowCodeExportDTO.class);
    }

    /**
     * 勾选多个单品码导出excel
     *
     * @param dto
     * @return
     */
    @PostMapping("/excel/export")
    public ResponseData exportExcel(@RequestBody ProductFlowCodeSelectDTO dto){
        if (CollectionUtils.isEmpty(dto.getIds()) && StringUtils.isBlank(dto.getWorkOrder())){
            throw new ResponseException("全部导出要输入关联单号");
        }
        DataExportParam<ProductFlowCodeSelectDTO> dataExportParam = new DataExportParam<>();
        dataExportParam.setExportFileName(BusinessCodeEnum.PRODUCT_FLOW_CODE.getCodeName());
        dataExportParam.setLimit(10000);
        dataExportParam.setBusinessCode(BusinessCodeEnum.PRODUCT_FLOW_CODE.name());
        dataExportParam.setCreateUserCode(getUsername());
        dto.setTemplateSheetName("数据源");
        Map<String, Object> parameters = new HashMap<>(8);
        parameters.put(ProductFlowCodeSelectDTO.class.getName(), dto);
        parameters.put("cleanSheetNames", "数据源");
        dataExportParam.setParameters(parameters);
        Long result = excelService.doExport(dataExportParam, ProductFlowCodeExportHandler.class);
        return success(result);
    }

    /**
     * 标识关联关系导出excel
     *
     * @param relevanceSelectDTO
     * @return
     */
    @PostMapping("/excel/relevance/export")
    public ResponseData exportRelevanceExcel(@RequestBody RelevanceSelectDTO relevanceSelectDTO){
        if (relevanceSelectDTO.getMaterialCode()==null) {
            throw new ResponseException("请输入要导出数据的物料编号");
        }
        DataExportParam<RelevanceSelectDTO> dataExportParam = new DataExportParam<>();
        dataExportParam.setExportFileName(BusinessCodeEnum.PRODUCT_FLOW_RELEVANCE_CODE.getCodeName());
        dataExportParam.setLimit(10000);
        dataExportParam.setBusinessCode(BusinessCodeEnum.PRODUCT_FLOW_RELEVANCE_CODE.name());
        dataExportParam.setCreateUserCode(getUsername());
        Map<String, Object> parameters = new HashMap<>(8);
        parameters.put(RelevanceSelectDTO.class.getName(), relevanceSelectDTO);
        dataExportParam.setParameters(parameters);
        Long result = excelService.doExport(dataExportParam, ProductFlowCodeRelevanceExportHandler.class);
        return success(result);
    }

//    /**
//     * 标识关联关系导出excel
//     *
//     * @param relevanceSelectDTO
//     * @return
//     */
//    @PostMapping("/excel/bill/export")
//    public ResponseData exportBillExcel(@RequestBody RelevanceSelectDTO relevanceSelectDTO){
//        if (relevanceSelectDTO.getMaterialCode()==null) {
//            throw new ResponseException("请输入要导出数据的物料编号");
//        }
//        DataExportParam<RelevanceSelectDTO> dataExportParam = new DataExportParam<>();
//        dataExportParam.setExportFileName(BusinessCodeEnum.PRODUCT_FLOW_RELEVANCE_CODE.getCodeName());
//        dataExportParam.setLimit(10000);
//        dataExportParam.setBusinessCode(BusinessCodeEnum.PRODUCT_FLOW_RELEVANCE_CODE.name());
//        dataExportParam.setCreateUserCode(getUsername());
//        Map<String, Object> parameters = new HashMap<>(8);
//        parameters.put(RelevanceSelectDTO.class.getName(), relevanceSelectDTO);
//        dataExportParam.setParameters(parameters);
//        Class<?> cls = ProductFlowCodeExportHandler.class; // 假设MyClass是要创建的Class类型
//        List<Mate>
//        Object array = Array.newInstance(cls, 3);
//        Long result = excelService.doExport(dataExportParam, ProductFlowCodeRelevanceExportHandler.class);
//        return success(result);
//    }

    /**
     * 勾选多个物料单品码导出excel
     *
     * @param relevanceSelectDTO
     * @return
     */
    @PostMapping("/excel/export/test")
    public ResponseData exportExcelTest(@RequestBody RelevanceSelectDTO relevanceSelectDTO){
        List<String> materiallist = new ArrayList<>();
        materiallist.add(relevanceSelectDTO.getMaterialCode());
        if(!CollectionUtils.isEmpty(relevanceSelectDTO.getRelevanceMaterialList())) {
            for (MaterialEntity materialEntity:relevanceSelectDTO.getRelevanceMaterialList()){
                materiallist.add(materialEntity.getCode());
            }
        }
        relevanceSelectDTO.setMaterialList(materiallist);

        DataExportParam<RelevanceSelectDTO> dataExportParam = new DataExportParam<>();
        dataExportParam.setExportFileName(BusinessCodeEnum.PRODUCT_FLOW_CODE.getCodeName());
        dataExportParam.setLimit(10000);
        dataExportParam.setBusinessCode(BusinessCodeEnum.PRODUCT_FLOW_CODE.name());
        dataExportParam.setCreateUserCode(getUsername());
        Map<String, Object> parameters = new HashMap<>(8);
        parameters.put(RelevanceSelectDTO.class.getName(), relevanceSelectDTO);
        dataExportParam.setParameters(parameters);
        Class<? extends ExportHandler>[] handlers = new Class[materiallist.size()+1];
        for(int i=0;i<materiallist.size();i++){
            handlers[i]= ProductFlowCodeTwoExportHandler.class;
        }
        handlers[materiallist.size()]= ProductFlowCodeRelevanceExportHandler.class;
        Long result = excelService.doExport(dataExportParam,handlers);

        return success(result);
    }



    /**
     * 查询 详细信息导入进度
     *
     * @return
     */
    @GetMapping("/export/task/{taskId}")
    public ResponseData detailListTaskById(@PathVariable Long taskId) {
        ExcelTask excelTask = productFlowCodeService.detailListTaskById(taskId);
        return success(excelTask);
    }

    /**
     * 查询关联关系导出任务进度
     *
     * @return
     */
    @GetMapping("/export/relevance/task/{taskId}")
    public ResponseData detailRelevanceTaskById(@PathVariable Long taskId) {
        ExcelTask excelTask = productFlowCodeService.detailRelevanceTaskById(taskId);
        return success(excelTask);
    }

    /**
     * 分页查询当前导出任务列表
     *
     * @param pageSize
     * @param currentPage
     * @return
     */
    @GetMapping("/export/task/page")
    public ResponseData taskDetailPage(@RequestParam(required = false, defaultValue = "20") Integer pageSize,
                                       @RequestParam(required = false, defaultValue = "1") Integer currentPage) {
        IPage<ExcelTask> iPage = productFlowCodeService.detailTaskPage(currentPage, pageSize);
        return success(iPage);
    }

    /**
     * 分页查询当前导出任务列表(关联关系)
     *
     * @param pageSize
     * @param currentPage
     * @return
     */
    @GetMapping("/relevance/export/task/page")
    public ResponseData relevanceTaskDetailPage(@RequestParam(required = false, defaultValue = "20") Integer pageSize,
                                                @RequestParam(required = false, defaultValue = "1") Integer currentPage) {
        IPage<ExcelTask> iPage = productFlowCodeService.relevanceTaskPage(currentPage, pageSize);
        return success(iPage);
    }

    /**
     * xx码打印前校验 标签是否已打印
     *
     * @param
     * @return
     */
    @PostMapping("/judge/print")
    public ResponseData judgeBeforePrint(@RequestBody ProductFlowCodeSelectDTO productFlowCodeDTO) {
        productFlowCodeService.judgeBeforePrint(productFlowCodeDTO);
        return success();
    }

    /**
     * xx码分页条件打印
     *
     * @return
     */
    @OperLog(module = "流水码标签", type = OperationType.PRINT, desc = "打印了编码为#{printCodes}的流水码")
    @PostMapping("/print")
    public ResponseData print(@RequestBody ProductFlowCodeSelectDTO productFlowCodeDTO) {
        log.info("{}调用了打印接口：{}", getUsername(), JacksonUtil.toJSONString(productFlowCodeDTO));
        return success(productFlowCodeService.print(productFlowCodeDTO));
    }

    /**
     * 查询流水码关联的单据号
     *
     * @param relatedType 关联单据类型
     * @return
     */
    @GetMapping("/relation/number/list")
    public ResponseData getRelationNumberList(@RequestParam(value = "relatedType") String relatedType,
                                              @RequestParam(value = "relationNumber", required = false) String relationNumber) {
        return success(productFlowCodeService.getRelationNumberList(relatedType, relationNumber));
    }

    /**
     * 获取流水码类型(采购单品码，生产成品码，生产流水码)
     *
     * @return
     */
    @GetMapping("/types")
    public ResponseData getTypeList() {
        List<StateEnumDTO> list = Arrays.stream(ProductFlowCodeTypeEnum.values())
                .map(productFlowCodeTypeEnum -> StateEnumDTO.builder()
                        .code(productFlowCodeTypeEnum.getCode())
                        .name(productFlowCodeTypeEnum.getName())
                        .type(productFlowCodeTypeEnum.getType())
                        .build()).collect(Collectors.toList());
        return success(list);
    }

    /***
     * 新老化管理校验流水码
     */
    @GetMapping("/check/product/flow/code")
    public ResponseData checkProductFlowCode(@RequestParam(value = "productFlowCode") String productFlowCode,
                                             @RequestParam(value = "workOrderNumber") String workOrderNumber) {
        if (StringUtils.isBlank(productFlowCode)) {
            return ResponseData.fail();
        }
        Boolean aBoolean = productFlowCodeService.checkProductFlowCode(productFlowCode, workOrderNumber);
        return ResponseData.success(aBoolean);
    }

    /**
     * 获取当前工位对应的产线上最新投产的工单流水码
     *
     * @param fid 工位id
     * @return
     */
    @GetMapping("/get/flow/code/newest")
    public ResponseData getNewestFlowCodeByFid(@RequestParam Integer fid, @RequestParam(required = false) String workOrderNumber) {
        return success(productFlowCodeService.getNewestFlowCode(fid, workOrderNumber));
    }


    /**
     * 根据单品码 获取关联单号
     *
     * @param flowCode
     * @return
     */
    @GetMapping("/get/relation/by/flowCode")
    public ResponseData getRelationByFlowCode(@RequestParam String flowCode) {
        String relationByFlowCode = productFlowCodeService.getRelationByFlowCode(flowCode);
        return success((Object) relationByFlowCode);
    }

    /**
     * 扫码判断是否流水码
     *
     * @param flowCode
     * @return
     */
    @GetMapping("/flowCode/judge")
    public ResponseData flowCodeJudge(@RequestParam(value = "flowCode") String flowCode,
                                      @RequestParam(value = "workCenterId", required = false) Integer workCenterId,
                                      @RequestParam(value = "lineId", required = false) Integer lineId,
                                      @RequestParam(value = "teamId", required = false) Integer teamId,
                                      @RequestParam(value = "deviceId", required = false) Integer deviceId) throws ParseException {
        ProductFlowJudgeDTO isFlowCode = productFlowCodeService.JudgeFlowCode(flowCode, workCenterId, lineId, teamId, deviceId);
        return success(isFlowCode);
    }

    /**
     * 检查数量
     *
     * @param checkReportDTO
     * @return
     */
    @PostMapping("/check/num")
    public ResponseData checkNum(@RequestBody CheckReportDTO checkReportDTO) {
        return success(productFlowCodeService.checkNum(checkReportDTO));
    }

    /**
     * 流水码关联批次
     *
     * @param bindBatchDTO
     * @return
     */
    @PostMapping("/bind/batch")
    public ResponseData bindBatch(@RequestBody BindBatchDTO bindBatchDTO) {
        productFlowCodeService.bindBatch(bindBatchDTO);
        return success();
    }

    /**
     * 获取成品码关联条码
     *
     * @param scannerDTO
     * @return
     */
    @PostMapping("/relevance/list")
    public ResponseData listRelevance(@RequestBody ScannerDTO scannerDTO) {
        ProductFlowCodeEntity productFlowCodeEntity = productFlowCodeService.getByProductFlowCode(scannerDTO.getFinishedProductCode());
        if (productFlowCodeEntity == null) {
            throw new ResponseException("找不到条码" + scannerDTO.getFinishedProductCode() + "对应信息");
        }
        if (!scannerDTO.getRelationNumber().equals(productFlowCodeEntity.getRelationNumber())) {
            throw new ResponseException("条码工单号与所选工单不一致");
        }
        return success(productFlowCodeService.listRelevance(scannerDTO.getFinishedProductCode()));
    }

    /**
     * 获取成品码
     *
     * @param scannerDTO
     * @return
     */
    @PostMapping("/get/relevance/code")
    public ResponseData getRelevanceCode(@RequestBody ScannerDTO scannerDTO) {
        ProductFlowCodeEntity productFlowCodeEntity = productFlowCodeService.getByProductFlowCode(scannerDTO.getFinishedProductCode());
        if (productFlowCodeEntity == null) {
            throw new ResponseException("找不到条码" + scannerDTO.getFinishedProductCode() + "对应信息");
        }
        if (!scannerDTO.getRelationNumber().equals(productFlowCodeEntity.getRelationNumber())) {
            throw new ResponseException("条码工单号与所选工单不一致");
        }
        if (ProductFlowCodeTypeEnum.FINISHED_PRODUCT_CODE.getCode() == productFlowCodeEntity.getType()) {
            return success(productFlowCodeEntity.getProductFlowCode());
        }
        if (ProductFlowCodeTypeEnum.PRODUCT_FLOW_CODE.getCode() == productFlowCodeEntity.getType()) {
            CodeRelevanceEntity codeRelevanceEntity = codeRelevanceService.lambdaQuery().eq(CodeRelevanceEntity::getRelevanceCode, scannerDTO.getFinishedProductCode()).last("limit 1").one();
            if (codeRelevanceEntity == null) {
                throw new ResponseException("没有绑定成品码");
            }
            return success(codeRelevanceEntity.getCode());
        }
        return success(productFlowCodeService.listRelevance(scannerDTO.getFinishedProductCode()));
    }

    /**
     * 解绑成品码关联条码
     *
     * @param id id
     * @return
     */
    @DeleteMapping("/relevance/unbind/{id}")
    @OperLog(module = "条码管理", type = OperationType.DELETE, desc = "解绑编码为#{code}和编码为{relevanceCode}的关联关系")
    public ResponseData unbind(@PathVariable Integer id) {
        CodeRelevanceEntity codeRelevanceEntity = codeRelevanceService.getById(id);
        codeRelevanceService.removeById(codeRelevanceEntity.getId());
        return success(codeRelevanceEntity);
    }

    /**
     * 替换成品码关联条码
     *
     * @param codeRelevanceEntity codeRelevanceEntity
     * @return codeRelevanceEntity
     */
    @PostMapping("/relevance/replace")
    @OperLog(module = "条码管理", type = OperationType.DELETE, desc = "替换了编码为#{code}和编码为{relevanceCode}的关联关系")
    public ResponseData relevanceReplace(@RequestBody CodeRelevanceEntity codeRelevanceEntity) {
        ProductFlowCodeEntity productFlowCodeEntity = productFlowCodeService.getByProductFlowCode(codeRelevanceEntity.getRelevanceCode());
        if (productFlowCodeEntity == null) {
            throw new ResponseException("找不到条码" + codeRelevanceEntity.getCode() + "对应信息");
        }
        CodeRelevanceEntity codeRelevanceEntityTemp = codeRelevanceService.lambdaQuery().eq(CodeRelevanceEntity::getRelevanceCode, codeRelevanceEntity.getRelevanceCode())
                .last("limit 1").one();
        if (codeRelevanceEntityTemp != null) {
            throw new ResponseException("条码" + codeRelevanceEntityTemp.getRelevanceCode() + "已绑定" + codeRelevanceEntityTemp.getCode());
        }
        codeRelevanceService.updateById(codeRelevanceEntity);
        return success(codeRelevanceEntity);
    }

    /**
     * 解绑成品码关联条码
     *
     * @param codeRelevanceEntity codeRelevanceEntity
     * @return codeRelevanceEntity
     */
    @PostMapping("/relevance/unbind/all")
    @OperLog(module = "条码管理", type = OperationType.DELETE, desc = "解绑编码为#{code}的所有关联关系")
    public ResponseData unbindAll(@RequestBody CodeRelevanceEntity codeRelevanceEntity) {
        codeRelevanceService.lambdaUpdate().eq(CodeRelevanceEntity::getCode, codeRelevanceEntity.getRelevanceCode()).remove();
        return success(codeRelevanceEntity);
    }


    /**
     * 标识数据导入
     */
    @PostMapping("/import")
    public ResponseData importExcel(MultipartFile file) throws IOException {
        //1、判断导入文件的合法性
        ExcelUtil.checkImportExcelFile(file);
        productFlowCodeService.importCodeExcel(file.getOriginalFilename(), file.getInputStream(), getUsername());
        return ResponseData.success();
    }

    /**
     * 标识默认导入模板
     */
    @GetMapping("/inspect/default/template")
    public void downloadMaterialDefaultTemplate(HttpServletResponse response) throws IOException {
        //找到导出的模板
        byte[] bytes = ExcelUtil.exportDefaultExcel("classpath:template/codeImportTemplate.xlsx");
        ExcelTemplateImportUtil.responseToClient(response, bytes, "物料标识默认导入模板" + Constant.XLSX);
    }


    /**
     * 获取物料标识导入的进度
     *
     * @return
     */
    @GetMapping("/import/progress")
    public ResponseData getCodeCompletenessProgress() {
        return success((Object) productFlowCodeService.getExcelImportProgress());
    }


    /**
     * 导出条码
     *
     * @param response
     * @throws IOException
     */
    @PostMapping("/export")
    public void exportCode(HttpServletResponse response,@RequestBody ProductFlowCodeSelectDTO selectDTO) throws IOException {
      productFlowCodeService.export(response,selectDTO);
    }
}
