package com.yelink.dfs.controller.energy;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yelink.dfs.common.BaseController;
import com.yelink.dfs.entity.energy.manage.DeviceEnergyConsumptionEntity;
import com.yelink.dfs.entity.energy.manage.dto.ConsumptionPageDTO;
import com.yelink.dfs.entity.energy.manage.dto.DeviceDTO;
import com.yelink.dfs.entity.energy.manage.vo.ConsumptionCarbonExportVO;
import com.yelink.dfs.entity.energy.manage.vo.ConsumptionElectricityExportVO;
import com.yelink.dfs.entity.energy.manage.vo.ConsumptionGasExportVO;
import com.yelink.dfs.entity.energy.manage.vo.ConsumptionWaterExportVO;
import com.yelink.dfs.service.energy.manage.DeviceCarbonConsumptionService;
import com.yelink.dfs.service.energy.manage.DeviceEnergyConsumptionService;
import com.yelink.dfs.service.energy.manage.DeviceGasConsumptionService;
import com.yelink.dfs.service.energy.manage.DeviceWaterConsumptionService;
import com.yelink.dfscommon.pojo.ResponseData;
import com.yelink.dfscommon.utils.EasyExcelUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 设备日历
 * @Date 2021/05/24
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/energy/consume")
public class EnergyManageController extends BaseController {

    private DeviceEnergyConsumptionService consumptionService;
    private DeviceGasConsumptionService gasConsumptionService;
    private DeviceWaterConsumptionService waterConsumptionService;
    private DeviceCarbonConsumptionService carbonConsumptionService;

    /**
     * 获取能耗列表
     *
     * @param start
     * @param end
     * @return
     */
    @GetMapping("/list")
    public ResponseData listDeviceCalendar(@RequestParam(value = "deviceNames", required = false) String deviceNames,
                                           @RequestParam(value = "deviceCodes", required = false) String deviceCodes,
                                           @RequestParam(value = "start", required = false) String start,
                                           @RequestParam(value = "end", required = false) String end,
                                           @RequestParam(value = "current", required = false) Integer current,
                                           @RequestParam(value = "size", required = false) Integer size) {
        Page<DeviceEnergyConsumptionEntity> page = consumptionService.getList(null, deviceNames, deviceCodes, start, end, current, size);
        return success(page);
    }

    /**
     * 用电
     */
    @GetMapping("/page/electricity")
    public ResponseData pageElectricity(ConsumptionPageDTO dto) {
        return success(consumptionService.consumptionPage(dto));
    }
    @GetMapping("/export/electricity")
    public ResponseData exportElectricity(ConsumptionPageDTO dto, HttpServletResponse response) throws IOException {
        List<ConsumptionElectricityExportVO> result = JSONObject.parseArray(
                JSON.toJSONString(consumptionService.consumptionPage(dto)),
                ConsumptionElectricityExportVO.class
        );
        EasyExcelUtil.export(response, "consumptionElectricity", "consumptionElectricitySheet", result, ConsumptionElectricityExportVO.class);
        return success(consumptionService.consumptionPage(dto));
    }
    /**
     * 用气
     */
    @GetMapping("/page/gas")
    public ResponseData pageGas(ConsumptionPageDTO dto) {
        return success(gasConsumptionService.consumptionPage(dto));
    }
    @GetMapping("/export/gas")
    public ResponseData exportGas(ConsumptionPageDTO dto, HttpServletResponse response) throws IOException {
        List<ConsumptionGasExportVO> result = JSONObject.parseArray(
                JSON.toJSONString(consumptionService.consumptionPage(dto)),
                ConsumptionGasExportVO.class
        );
        EasyExcelUtil.export(response, "consumptionGas", "consumptionGasSheet", result, ConsumptionGasExportVO.class);
        return success(consumptionService.consumptionPage(dto));
    }
    /**
     * 用水
     */
    @GetMapping("/page/water")
    public ResponseData pageWater(ConsumptionPageDTO dto) {
        return success(waterConsumptionService.consumptionPage(dto));
    }
    @GetMapping("/export/water")
    public ResponseData exportWater(ConsumptionPageDTO dto, HttpServletResponse response) throws IOException {
        List<ConsumptionWaterExportVO> result = JSONObject.parseArray(
                JSON.toJSONString(consumptionService.consumptionPage(dto)),
                ConsumptionWaterExportVO.class
        );
        EasyExcelUtil.export(response, "consumptionWater", "consumptionWaterSheet", result, ConsumptionWaterExportVO.class);
        return success(consumptionService.consumptionPage(dto));
    }
    /**
     * 碳排放
     */
    @GetMapping("/page/carbon")
    public ResponseData pageCarbon(ConsumptionPageDTO dto) {
        return success(carbonConsumptionService.consumptionPage(dto));
    }
    @GetMapping("/export/carbon")
    public ResponseData exportCarbon(ConsumptionPageDTO dto, HttpServletResponse response) throws IOException {
        List<ConsumptionCarbonExportVO> result = JSONObject.parseArray(
                JSON.toJSONString(consumptionService.consumptionPage(dto)),
                ConsumptionCarbonExportVO.class
        );
        EasyExcelUtil.export(response, "consumptionCarbon", "consumptionCarbonSheet", result, ConsumptionCarbonExportVO.class);
        return success(consumptionService.consumptionPage(dto));
    }

    /**
     * 获取设备能耗曲线
     *
     * @param start
     * @param end
     * @return
     */
    @GetMapping("/lines")
    public ResponseData listDeviceCalendars(@RequestParam(value = "deviceNames", required = false) String deviceNames,
                                            @RequestParam(value = "deviceCodes", required = false) String deviceCodes,
                                            @RequestParam(value = "start", required = false) String start,
                                            @RequestParam(value = "end", required = false) String end) {
        Map<String, List<DeviceEnergyConsumptionEntity>> map = consumptionService.getLines(deviceNames, deviceCodes, start, end);
        return success(map);
    }

    /**
     * 获取数据表中所有的设备
     *
     * @return
     */
    @GetMapping("/devices")
    public ResponseData listDeviceCalendar() {
        List<DeviceDTO> devices = consumptionService.getDevices();
        return success(devices);
    }

    /**
     * 根据设备类型分类获取数据表中所有的设备
     *
     * @return
     */
    @GetMapping("/type/devices")
    public ResponseData getListDeviceByType() {
        Map<String, List<DeviceDTO>> map = consumptionService.getListDeviceByType();
        return success(map);
    }
}
