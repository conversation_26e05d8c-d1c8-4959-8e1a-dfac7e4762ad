package com.yelink.dfs.controller.event;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yelink.dfs.common.BaseController;
import com.yelink.dfs.constant.Constant;
import com.yelink.dfs.entity.event.EventDefinitionEntity;
import com.yelink.dfs.entity.event.dto.EventDefinitionExcelDTO;
import com.yelink.dfs.service.event.EventDefinitionService;
import com.yelink.dfscommon.constant.RespCodeEnum;
import com.yelink.dfscommon.constant.ResponseException;
import com.yelink.dfscommon.pojo.ResponseData;
import com.yelink.dfscommon.utils.EasyExcelUtil;
import com.yelink.dfscommon.utils.ExcelTemplateImportUtil;
import com.yelink.dfscommon.utils.ExcelUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Date;
import java.util.List;


/**
 * <AUTHOR>
 * @Date 2021/11/2 19:28
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/definition/event")
public class EventDefinitionController extends BaseController {
    private EventDefinitionService eventDefinitionService;


    /**
     * 查询事件定义列表
     *
     * @param pageSize
     * @param currentPage
     * @param eventClassifyName
     * @return
     */
    @GetMapping("/list")
    public ResponseData list(@RequestParam(value = "pageSize", required = false) Integer pageSize,
                             @RequestParam(value = "currentPage", required = false) Integer currentPage,
                             @RequestParam(value = "eventClassifyName", required = false) String eventClassifyName,
                             @RequestParam(value = "eventDefinitionName", required = false) String eventDefinitionName) {
        return success(eventDefinitionService.listByPage(pageSize, currentPage, eventClassifyName,eventDefinitionName));
    }
    @GetMapping("/all")
    public ResponseData all(@RequestParam(value = "classifyId", required = false) Integer classifyId) {
        return success(eventDefinitionService.getAllList(classifyId));
    }



    /**
     * 新增事件定义
     *
     * @param entity
     * @return
     */
    @PostMapping("/add")
    public ResponseData addEventDefinition(@RequestBody EventDefinitionEntity entity) {
        entity.setCreateBy(getUsername());
        entity.setCreateTime(new Date());
        if (entity.getEventClassifyId() == null) {
            throw new ResponseException(RespCodeEnum.EVENT_CLASSIFY_ID_IS_NULL);
        }
        eventDefinitionService.saveEventDefinition(entity);
        return success();
    }

    @GetMapping("/detail")
    public ResponseData detailEventDefinition(@RequestParam Integer id) {
        return success(eventDefinitionService.detailEventDefinition(id));
    }


    /**
     * 修改事件定义
     *
     * @param entity
     * @return
     */
    @PutMapping("/update")
    public ResponseData update(@RequestBody EventDefinitionEntity entity) {
        String username = getUsername();
        entity.setUpdateBy(username);
        entity.setUpdateTime(new Date());
        if (entity.getEventClassifyId() == null) {
            throw new ResponseException(RespCodeEnum.EVENT_CLASSIFY_ID_IS_NULL);
        }
        eventDefinitionService.updateEventDefinition(entity);
        return success(entity);
    }


    /**
     * 删除事件定义
     *
     * @param id
     * @return
     */
    @DeleteMapping("/del/{id}")
    public ResponseData del(@PathVariable(value = "id") Integer id) {
        return success(eventDefinitionService.removeById(id));
    }
    @GetMapping("/down-template")
    public void download(HttpServletResponse response) throws IOException {
        byte[] bytes = ExcelUtil.exportDefaultExcel("classpath:template/eventDefinition.xlsx");
        ExcelTemplateImportUtil.responseToClient(response, bytes, "事件定义模板" + Constant.XLSX);
    }

    /**
     * 事件定义导入前预览
     *
     */
    @PostMapping("/import/preview")
    public ResponseData previewBeforeImport(MultipartFile file) throws IOException {
        JSONObject jsonObjects = eventDefinitionService.importExcel(file);
        return success(jsonObjects);
    }
    @PostMapping("/import/save")
    public ResponseData importDefineList(@RequestBody List<EventDefinitionExcelDTO> dtoList) {
        eventDefinitionService.saveDefinitionImport(dtoList);
        return ResponseData.success();
    }
    @GetMapping("/export")
    public void export(@RequestParam(value = "eventClassifyName", required = false) String eventClassifyName,
                       @RequestParam(value = "eventDefinitionName", required = false) String eventDefinitionName,
                       HttpServletResponse response) throws IOException {
        Page<EventDefinitionEntity> page = eventDefinitionService.listByPage(null, null, eventClassifyName, eventDefinitionName);
        List<EventDefinitionExcelDTO> exportList = JSON.parseArray(JSON.toJSONString(page.getRecords()), EventDefinitionExcelDTO.class);
        EasyExcelUtil.export(response, "AlarmDefinitionFile", "AlarmDefinitionSheet", exportList, EventDefinitionExcelDTO.class);
    }

}
