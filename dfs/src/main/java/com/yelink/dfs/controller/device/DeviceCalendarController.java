package com.yelink.dfs.controller.device;

import com.yelink.dfs.common.BaseController;
import com.yelink.dfs.constant.OperationType;
import com.yelink.dfs.entity.device.DeviceCalendarEntity;
import com.yelink.dfs.service.device.DeviceCalendarService;
import com.yelink.dfscommon.annotation.OperLog;
import com.yelink.dfscommon.constant.ParamException;
import com.yelink.dfscommon.pojo.ResponseData;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description 设备日历
 * @Date 2021/05/24
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/devices/calendar")
public class DeviceCalendarController extends BaseController {

    private DeviceCalendarService calendarService;

    /**
     * 获取设备日历
     *
     * @param start
     * @param end
     * @return
     */
    @GetMapping("/list/{deviceId}")
    public ResponseData listDeviceCalendar(@PathVariable Integer deviceId,
                                           @RequestParam(value = "start", required = false) String start,
                                           @RequestParam(value = "end", required = false) String end) {
        List<DeviceCalendarEntity> list = calendarService.getList(deviceId, start, end);
        return success(list);
    }

    /**
     * 修改设备日历
     *
     * @return
     */
    @PutMapping("/update")
    @OperLog(module = "设备管理", type = OperationType.UPDATE, desc = "修改了日期为#{calendarDate}的设备日历")
    public ResponseData updateDeviceCalendar(@RequestBody DeviceCalendarEntity entity,
                                             BindingResult bindingResult) {
        if (bindingResult.hasErrors()) {
            throw new ParamException(bindingResult);
        }
        String username = getUsername();
        entity.setUpdateBy(username);
        entity.setUpdateTime(new Date());
        calendarService.updateDeviceCalendar(entity);
        return success(entity);
    }

    /**
     * 获取设备日历详情
     *
     * @param id
     * @return
     */
    @GetMapping("/detail/{id}")
    public ResponseData getDetail(@PathVariable Integer id) {
        DeviceCalendarEntity calendarEntity = calendarService.getDetail(id);
        return success(calendarEntity);
    }


}
