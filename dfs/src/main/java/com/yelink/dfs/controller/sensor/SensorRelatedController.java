package com.yelink.dfs.controller.sensor;

import com.yelink.dfs.common.BaseController;
import com.yelink.dfs.entity.sensor.SensorRelatedEntity;
import com.yelink.dfs.service.sensor.SensorRelatedService;
import com.yelink.dfscommon.pojo.ResponseData;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * @description: 传感器添加-设施+传感器
 * @author: shuang
 * @time: 2020/12/10
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/sensors")
public class SensorRelatedController extends BaseController {

    private SensorRelatedService sensorRelatedService;

    /**
     * 获取绑定的采集器
     *
     * @param
     * @return
     */
    @GetMapping("/get/related/{sensorId}")
    public ResponseData getRelatedSensors(@PathVariable Integer sensorId) {
        SensorRelatedEntity entity = sensorRelatedService.getRelatedSensors(sensorId);
        return success(entity);
    }

}
