package com.yelink.dfs.controller.approve;

import com.alibaba.fastjson.JSON;
import com.yelink.dfs.common.BaseController;
import com.yelink.dfs.entity.approve.config.ApproveConfigEntity;
import com.yelink.dfs.entity.approve.config.ApproveDockingConfigEntity;
import com.yelink.dfs.entity.approve.config.ApproveNodeConfigEntity;
import com.yelink.dfs.entity.approve.config.ApproveTemplateControlEntity;
import com.yelink.dfs.entity.approve.config.dto.ApproveFullPathCodeDTO;
import com.yelink.dfs.entity.approve.config.dto.ApproveNodeSelectDTO;
import com.yelink.dfs.entity.approve.config.dto.ApproveOuterTemplateDTO;
import com.yelink.dfs.entity.common.CommonState;
import com.yelink.dfs.entity.user.SysUserEntity;
import com.yelink.dfs.service.approve.config.ApproveConfigService;
import com.yelink.dfs.service.approve.config.ApproveDockingConfigService;
import com.yelink.dfs.service.approve.config.ApproveNodeConfigService;
import com.yelink.dfs.service.approve.config.ApproveNodeFieldService;
import com.yelink.dfs.service.approve.config.ApproveNodeModuleService;
import com.yelink.dfs.service.approve.config.ApproveTemplateControlService;
import com.yelink.dfs.service.approve.config.ApproveTemplateService;
import com.yelink.dfs.service.common.config.BusinessConfigService;
import com.yelink.dfscommon.constant.approve.config.ApprovalStatusEnum;
import com.yelink.dfscommon.constant.approve.config.ApproveModuleEnum;
import com.yelink.dfscommon.constant.approve.config.IsApproveEnum;
import com.yelink.dfscommon.constant.dfs.config.ConfigConstant;
import com.yelink.dfscommon.constant.dfs.config.ConfigValueConstant;
import com.yelink.dfscommon.dto.common.config.FullPathCodeDTO;
import com.yelink.dfscommon.pojo.ResponseData;
import com.yelink.wechat.client.exception.ClientException;
import com.yelink.wechat.server.service.WeChatFlowService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 模块审批管理
 * @Date 2021/05/24
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/approve")
public class ApproveConfigController extends BaseController {

    private ApproveConfigService approveConfigService;
    private BusinessConfigService businessConfigService;
    private ApproveDockingConfigService approveDockingConfigService;
    private ApproveNodeConfigService approveNodeConfigService;
    private ApproveNodeFieldService approveNodeFieldService;
    private ApproveNodeModuleService approveNodeModuleService;
    private ApproveTemplateControlService approveTemplateControlService;
    private ApproveTemplateService approveTemplateService;
    private WeChatFlowService weChatFlowService;

    /**
     * 获取所有审批配置
     *
     * @return
     */
    @GetMapping("/list")
    public ResponseData getList() {
        List<ApproveConfigEntity> list = approveConfigService.getList();
        return success(list);
    }

    /**
     * 保存所有配置
     *
     * @return
     */
    @Deprecated
    @PostMapping("/save/list")
    public ResponseData saveList(@RequestBody List<ApproveConfigEntity> list) {
        approveConfigService.saveOrUpdateBatchById(list);
        return success();
    }

    /**
     * 通过类型/模块编号获取配置
     *
     * @return
     */
    @GetMapping("/config/{code}")
    public ResponseData getConfigByCode(@PathVariable String code) {
        Boolean config = approveConfigService.getConfigByCode(code);
        return success(config);
    }

    /**
     * 通过类型/模块编号获取签名配置
     *
     * @return
     */
    @GetMapping("/signature/config/{code}")
    public ResponseData getApproveSignatureConfigByCode(@PathVariable String code) {
        // 查询签名配置
        FullPathCodeDTO dto = FullPathCodeDTO.builder()
                .fullPathCode(ConfigConstant.SIGNATURE).build();
        Map<String, String> valueMap = businessConfigService.getValueMap(dto);
        Boolean orderApproveSignature = JSON.parseObject(valueMap.get(ConfigValueConstant.ORDER_APPROVE_SIGNATURE), Boolean.class);
        if (Boolean.FALSE.equals(orderApproveSignature)) {
            return success(false);
        }
        // 查询审批配置
        ApproveConfigEntity approveConfig = approveConfigService.selectByCode(code);
        if (approveConfig == null || IsApproveEnum.NO.getCode().equals(approveConfig.getIsApprove())) {
            return success(false);
        }
        return success(approveConfig.getApproveSignature());
    }

    @GetMapping("/get/config")
    public ResponseData getApproveConfigByCode(@RequestParam String code) {
        ApproveConfigEntity approveConfig = approveConfigService.selectByCode(code);
        return success(approveConfig);
    }


    /**
     * 通过类型/模块编号获取配置
     *
     * @return
     */
    @GetMapping("/add/list")
    public ResponseData addList() {
        ApproveModuleEnum[] enums = ApproveModuleEnum.values();
        ApproveConfigEntity.ApproveConfigEntityBuilder builder = ApproveConfigEntity.builder();
        ArrayList<ApproveConfigEntity> list = new ArrayList<>(enums.length);
        for (ApproveModuleEnum anEnum : enums) {
            list.add(builder.code(anEnum.getCode()).name(anEnum.getName()).isApprove(IsApproveEnum.NO.getCode()).build());
        }
        approveConfigService.saveBatch(list);
        return success();
    }

    /**
     * 通过编号获取有审批权限的用户
     *
     * @return
     */
    @GetMapping("/users/{code}")
    public ResponseData getApproveUsersByCode(@PathVariable String code) {
        List<SysUserEntity> users = approveConfigService.getApproveUsersByCode(code);
        return success(users);
    }

    /**
     * 获取审批状态列表
     *
     * @return
     */
    @GetMapping("/approval/status")
    public ResponseData getApprovalStatusList() {
        List<CommonState> list = new ArrayList<>();
        List<ApprovalStatusEnum> values = ApprovalStatusEnum.getSystemValues();
        for (ApprovalStatusEnum value : values) {
            CommonState type = new CommonState();
            type.setCode(value.getCode());
            type.setName(value.getName());
            list.add(type);
        }
        return success(list);
    }

    /**
     * 查询审批对接配置
     *
     * @return
     */
    @GetMapping("/docking/config/get")
    public ResponseData getDockingConfig(@RequestParam String dockingCode) {
        return success(approveDockingConfigService.getDockingConfig(dockingCode));
    }

    /**
     * 保存或更新审批对接配置
     *
     * @return
     */
    @PostMapping("/docking/config/update")
    public ResponseData updateDockingConfig(@RequestBody ApproveDockingConfigEntity entity) {
        entity.setUpdateBy(getUsername());
        entity.setUpdateTime(new Date());
        approveDockingConfigService.updateDockingConfig(entity);
        return success();
    }

    /**
     * 查询审批节点树
     *
     * @return
     */
    @PostMapping("/node/tree")
    public ResponseData getNodeTree(@RequestBody ApproveFullPathCodeDTO dto) {
        return success(approveNodeConfigService.getNodeTree(dto));
    }

    /**
     * 查询开启了企微审批节点树
     *
     * @return
     */
    @PostMapping("/wechat/node/tree")
    public ResponseData getWeChatNodeTree() {
        return success(approveNodeConfigService.getWeChatNodeTree());
    }

    /**
     * 查询审批节点和模板信息
     *
     * @return
     */
    @PostMapping("/node/detail")
    public ResponseData getNodeDetail(@RequestBody ApproveFullPathCodeDTO dto) {
        return success(approveNodeConfigService.getNodeDetail(dto));
    }

    /**
     * 查询审批节点审批信息
     *
     * @return
     */
    @PostMapping("/node/config")
    public ResponseData nodeConfig(@RequestBody ApproveFullPathCodeDTO dto) {
        return success(approveNodeConfigService.getApproveConfig(dto.getFullPathCode()));
    }

    /**
     * 保存审批节点和模板信息
     *
     * @return
     */
    @PostMapping("/node/save")
    public ResponseData save(@RequestBody ApproveNodeConfigEntity entity) {
        entity.setUpdateBy(getUsername());
        entity.setUpdateTime(new Date());
        approveNodeConfigService.saveEntity(entity);
        return success();
    }

    /**
     * 同步企微模板到系统
     *
     * @return
     */
    @PostMapping("/sync/template")
    public ResponseData syncTemplate(@RequestBody ApproveOuterTemplateDTO dto) throws ClientException {
        dto.setUserName(getUsername());
        approveTemplateService.syncTemplateFromWeChat(dto);
        return success();
    }

    /**
     * 查询审批节点模块信息
     *
     * @return
     */
    @PostMapping("/node/module")
    public ResponseData getNodeModule(@RequestBody ApproveFullPathCodeDTO dto) {
        return success(approveNodeModuleService.getNodeModule(dto));
    }

    /**
     * 查询审批节点模块的业务字段信息
     *
     * @return
     */
    @PostMapping("/node/module/field")
    public ResponseData getNodeModuleField(@RequestBody ApproveFullPathCodeDTO dto) {
        return success(approveNodeFieldService.getNodeModuleField(dto));
    }

    /**
     * 查询控件和业务字段关联信息
     *
     * @return
     */
    @PostMapping("/control/field/relation")
    public ResponseData getControlFieldRelation(@RequestBody ApproveFullPathCodeDTO dto) {
        return success(approveNodeConfigService.getControlFieldRelation(dto));
    }

    /**
     * 更新控件和业务字段关联信息
     *
     * @return
     */
    @PostMapping("/control/field/update")
    public ResponseData updateControlFieldRelation(@RequestBody ApproveTemplateControlEntity entity) {
        return success(approveTemplateControlService.updateById(entity));
    }

    /**
     * 提交企微审批
     *
     * @return
     */
    @PostMapping("/wechat/approve")
    public ResponseData wechatApprove(@RequestBody ApproveFullPathCodeDTO dto) {
        approveTemplateService.wechatApprove(dto);
        return success();
    }

    /**
     * 审批节点列表
     *
     * @return
     */
    @PostMapping("/node/config/list")
    public ResponseData getNodeList(@RequestBody ApproveNodeSelectDTO dto) {
        List<ApproveNodeConfigEntity> list = approveNodeConfigService.getNodeList(dto);
        return success(list);
    }

    /**
     * 更新节点审批信息
     *
     * @return
     */
    @PostMapping("/node/config/update")
    public ResponseData updateNodeApprove(@RequestBody ApproveNodeConfigEntity entity) {
        entity.setUpdateBy(getUsername());
        entity.setUpdateTime(new Date());
        approveNodeConfigService.updateById(entity);
        return success();
    }

}
