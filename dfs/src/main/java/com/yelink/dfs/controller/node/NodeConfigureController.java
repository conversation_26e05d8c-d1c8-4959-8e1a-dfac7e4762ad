package com.yelink.dfs.controller.node;

import com.yelink.dfs.common.BaseController;
import com.yelink.dfs.entity.node.NodeConfigureEntity;
import com.yelink.dfs.service.node.NodeConfigureService;
import com.yelink.dfs.service.node.NodeReportFiledService;
import com.yelink.dfscommon.constant.CommonEnum;
import com.yelink.dfscommon.pojo.ResponseData;
import com.yelink.dfscommon.utils.DateUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * 节点配置
 * @Date 2022/4/22 10:41
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/node/configures")
public class NodeConfigureController extends BaseController {
    private NodeConfigureService nodeConfigureService;
    private NodeReportFiledService reportFiledService;

    /**
     * 列表
     *
     * @param size
     * @param current
     * @param appointTyp 指定类型
     * @param state      状态
     * @param approver   审批人
     * @return
     */
    @GetMapping("/list")
    public ResponseData list(@RequestParam(value = "size", required = false) Integer size,
                             @RequestParam(value = "current", required = false) Integer current,
                             @RequestParam(value = "appointTyp", required = false) String appointTyp,
                             @RequestParam(value = "materialCode", required = false) String materialCode,
                             @RequestParam(value = "materialName", required = false) String materialName,
                             @RequestParam(value = "saleOrderNumber", required = false) String saleOrderNumber,
                             @RequestParam(value = "materialType", required = false) Integer materialType,
                             @RequestParam(value = "state", required = false) Integer state,
                             @RequestParam(value = "addType", required = false) String addType,
                             @RequestParam(value = "approver", required = false) String approver) {
        return ResponseData.success(nodeConfigureService.getList(size, current, appointTyp, materialCode, materialName, saleOrderNumber, materialType, state, addType, approver));
    }

    /**
     * 新增
     *
     * @param entity
     * @return
     */
    @PostMapping("/insert")
    public ResponseData add(@RequestBody NodeConfigureEntity entity) {
        entity.setCreateBy(getUsername());
        entity.setCreateTime(DateUtil.formatToDate(new Date(), DateUtil.DATETIME_FORMAT));
        Boolean result = nodeConfigureService.add(entity);
        if (result) {
            return ResponseData.success();
        }
        return ResponseData.fail();
    }

    /**
     * 新增
     *
     * @param entity
     * @return
     * @throws Exception
     */
    @PostMapping("/insert/released")
    public ResponseData addReleasedConfigure(@RequestBody NodeConfigureEntity entity) {
        String username = getUsername();
        entity.setCreateBy(username);
        entity.setCreateBy(username);
        entity.setCreateTime(new Date());
        entity.setUpdateTime(new Date());
        nodeConfigureService.addReleasedConfigure(entity);
        return ResponseData.success();
    }


    /**
     * 新增工厂配置
     *
     * @param entity
     * @return
     */
    @PostMapping("/insert/factory/configuration")
    public ResponseData addFactoryConfiguration(@RequestBody NodeConfigureEntity entity) {
        entity.setCreateBy(getUsername());
        entity.setCreateTime(DateUtil.formatToDate(new Date(), DateUtil.DATETIME_FORMAT));
        Boolean result = nodeConfigureService.addFactoryConfiguration(entity);
        if (result) {
            return ResponseData.success();
        }
        return ResponseData.fail();
    }
    
    /**
     * 新增工厂配置
     *
     * @param entity
     * @return
     */
    @PostMapping("/insert/released/factory/configuration")
    public ResponseData addReleasedFactoryConfiguration(@RequestBody NodeConfigureEntity entity) {
        String username = getUsername();
        entity.setCreateBy(username);
        entity.setUpdateBy(username);
        entity.setCreateTime(new Date());
        entity.setUpdateTime(new Date());
         nodeConfigureService.addReleasedFactoryConfiguration(entity);
        return ResponseData.success();
    }

    /**
     * 获取工厂配置：默认给到最新
     *
     * @return
     */
    @GetMapping("/get/factory/configuration")
    public ResponseData getFactoryConfiguration() {
        return ResponseData.success(nodeConfigureService.getFactoryConfiguration());
    }


    /**
     * 修改
     *
     * @param entity
     * @return
     * @throws Exception
     */
    @PutMapping("/update")
    public ResponseData edit(@RequestBody NodeConfigureEntity entity) {
        entity.setUpdateBy(getUsername());
        entity.setUpdateTime(DateUtil.formatToDate(new Date(), DateUtil.DATETIME_FORMAT));
        Boolean result = nodeConfigureService.edit(entity);
        if (result) {
            return ResponseData.success();
        }
        return ResponseData.fail();
    }

    /**
     * 删除
     *
     * @param id
     * @return
     */
    @DeleteMapping("/delete/{id}")
    public ResponseData remove(@PathVariable(value = "id") Integer id) {
        Boolean result = nodeConfigureService.delete(id);
        if (result) {
            return ResponseData.success();
        }
        return ResponseData.fail();
    }

    /**
     * 详情
     *
     * @param id
     * @return
     */
    @GetMapping("/detail/{id}")
    public ResponseData detail(@PathVariable(value = "id") Integer id) {
        return ResponseData.success(nodeConfigureService.detail(id));
    }


    /**
     * 审批
     */
    @GetMapping("/examine/approve")
    public ResponseData examineOrApprove(@RequestParam(value = "approvalStatus") Integer approvalStatus,
                                         @RequestParam(value = "approvalSuggestion", required = false) String approvalSuggestion,
                                         @RequestParam(value = "id") Integer id) {
        nodeConfigureService.examineOrApprove(approvalStatus, approvalSuggestion, id, getUsername());
        return ResponseData.success();
    }

    /**
     * 上报单据列表
     */
    @GetMapping("/report/bill/list")
    public ResponseData getReportBillList() {
        return ResponseData.success(nodeConfigureService.getReportBillList());
    }

    /**
     * 上报子类型列表
     */
    @GetMapping("/sub/type/list")
    public ResponseData getSubReportTypeList() {
        List<CommonEnum> list = reportFiledService.getSubReportTypeList();
        return ResponseData.success(list);
    }

    /**
     * 获取指定对象
     */
    @GetMapping("/appoint/type/list")
    public ResponseData getAppointTypeList() {
        List<CommonEnum> list = nodeConfigureService.getAppointTypeList();
        return ResponseData.success(list);
    }
}
