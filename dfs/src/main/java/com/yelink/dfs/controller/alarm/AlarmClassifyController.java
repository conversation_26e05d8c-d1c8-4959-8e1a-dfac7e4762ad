package com.yelink.dfs.controller.alarm;

import com.yelink.dfs.common.BaseController;
import com.yelink.dfs.entity.alarm.AlarmClassifyEntity;
import com.yelink.dfs.service.alarm.AlarmClassifyService;
import com.yelink.dfs.service.alarm.AlarmDefinitionService;
import com.yelink.dfscommon.pojo.ResponseData;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;


/**
 * <AUTHOR>
 * @Date 2021/11/2 19:29
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/classify")
public class AlarmClassifyController extends BaseController {

    private AlarmClassifyService alarmClassifyService;
    private AlarmDefinitionService alarmDefinitionService;

    /**
     * 获取告警分类中的关键字段
     *
     * @param
     * @return
     */
    @GetMapping("/type")
    public ResponseData getAlarmClassifyField() {
        return success(alarmClassifyService.getAlarmClassifyField());
    }


    /**
     * 查询告警分类列表
     *
     * @param pageSize          当前页数
     * @param currentPage       当前页
     * @param alarmClassifyName 告警分类
     * @return
     */
    @GetMapping("/list")
    public ResponseData list(@RequestParam(value = "pageSize", required = false) Integer pageSize,
                             @RequestParam(value = "currentPage", required = false) Integer currentPage,
                             @RequestParam(value = "alarmClassifyName", required = false) String alarmClassifyName) {
        return success(alarmClassifyService.listByPage(pageSize, currentPage, alarmClassifyName));
    }

    /**
     * 获取告警分类——（pad+web端）
     *
     * @param
     * @return
     */
    @GetMapping("/alarm/classify")
    public ResponseData getAlarmClassifyName() {
        return success(alarmClassifyService.getAlarmClassifyName());
    }

    /**
     * 新增告警类型
     *
     * @param entity
     * @return
     */
    @PostMapping("/add")
    public ResponseData addAlarmClassify(@RequestBody AlarmClassifyEntity entity) {
        entity.setCreateBy(getUsername());
        entity.setCreateTime(new Date());
        return ResponseData.success(alarmClassifyService.addAlarmClassify(entity));
    }
    /**
     * 获取告警类型详情
     *
     * @param
     * @return
     */
    @GetMapping("/detail/{id}")
    public ResponseData getAlarmClassifyDetail(@PathVariable("id") Integer id) {
        return ResponseData.success(alarmClassifyService.getAlarmClassifyDetail(id));
    }
    /**
     * 编辑告警类型
     *
     * @param entity
     * @return
     */
    @PutMapping("/update")
    public ResponseData updateAlarmClassify(@RequestBody AlarmClassifyEntity entity) {
        entity.setUpdateBy(getUsername());
        entity.setUpdateTime(new Date());
        return success(alarmClassifyService.updateAlarmClassify(entity));
    }

    /**
     * 删除告警类型
     *
     * @return
     */
    @DeleteMapping("/delete/{id}")
    public ResponseData deleteAlarmClassify(@PathVariable Integer id) {
        Boolean remove = alarmDefinitionService.removeAlarmClassifyById(id);
        return ResponseData.success(remove);
    }
}
