package com.yelink.dfs.controller.notice;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yelink.dfs.common.BaseController;
import com.yelink.dfs.constant.OperationType;
import com.yelink.dfs.constant.defect.DefectDefineStateEnum;
import com.yelink.dfs.constant.notice.NoticeLimitScopeEnum;
import com.yelink.dfs.constant.notice.NoticeMethodEnum;
import com.yelink.dfs.constant.notice.NoticeStateEnum;
import com.yelink.dfs.constant.notice.NoticeTriggerModeEnum;
import com.yelink.dfs.constant.product.ProcedureStateEnum;
import com.yelink.dfs.entity.alarm.dto.UserVo;
import com.yelink.dfs.entity.defect.DefectDefineEntity;
import com.yelink.dfs.entity.manufacture.ProductionLineEntity;
import com.yelink.dfs.entity.notice.InfoNoticeConfigEntity;
import com.yelink.dfs.entity.notice.NoticeLevelRelationConfigEntity;
import com.yelink.dfs.entity.product.ProcedureEntity;
import com.yelink.dfs.service.defect.DefectDefineService;
import com.yelink.dfs.service.manufacture.ProductionLineService;
import com.yelink.dfs.service.notice.InfoNoticeConfigService;
import com.yelink.dfs.service.product.ProcedureService;
import com.yelink.dfscommon.annotation.OperLog;
import com.yelink.dfscommon.constant.dfs.notice.NoticeTypeEnum;
import com.yelink.dfscommon.constant.qms.InspectionSheetConclusionEnum;
import com.yelink.dfscommon.dto.dfs.NoticeConfigBuilder;
import com.yelink.dfscommon.dto.dfs.NoticeConfigDTO;
import com.yelink.dfscommon.entity.CommonType;
import com.yelink.dfscommon.pojo.ResponseData;
import com.yelink.dfscommon.utils.JacksonUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/8/18 16:16
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/circulation/notice/config")
public class InfoNoticeConfigController extends BaseController {

    private InfoNoticeConfigService infoNoticeConfigService;
    private ProcedureService procedureService;
    private ProductionLineService lineService;
    private DefectDefineService defectDefineService;

    /**
     * 通知配置列表
     *
     * @param current
     * @param size
     * @param noticeType 消息通知类型
     * @return
     */
    @GetMapping("/list")
    public ResponseData getList(@RequestParam(value = "current", required = false) Integer current,
                                @RequestParam(value = "size", required = false) Integer size,
                                @RequestParam(value = "noticeType", required = false) String noticeType,
                                @RequestParam(value = "state", required = false) Integer state) {
        Page<InfoNoticeConfigEntity> page = infoNoticeConfigService.getList(current, size, noticeType, state);
        return ResponseData.success(page);
    }

    /**
     * 详情
     *
     * @param id
     * @return
     */
    @GetMapping("/detail/{id}")
    public ResponseData getConfigDetail(@PathVariable Integer id) {
        InfoNoticeConfigEntity entity = infoNoticeConfigService.getDetail(id);
        return ResponseData.success(entity);
    }

    /**
     * 通知方式列表
     *
     * @return
     */
    @GetMapping("/methods")
    public ResponseData getMethods() {
        NoticeMethodEnum[] values = NoticeMethodEnum.values();
        List<CommonType> list = new ArrayList<>();
        for (NoticeMethodEnum anEnum : values) {
            list.add(CommonType.builder().code(anEnum.getCode()).name(anEnum.getName()).build());
        }
        return ResponseData.success(list);
    }

    /**
     * 配置状态列表
     *
     * @return
     */
    @GetMapping("/states")
    public ResponseData getConfigStates() {
        NoticeStateEnum[] values = NoticeStateEnum.values();
        List<CommonType> list = new ArrayList<>();
        for (NoticeStateEnum anEnum : values) {
            list.add(CommonType.builder().code(anEnum.getCode()).name(anEnum.getName()).build());
        }
        return ResponseData.success(list);
    }

/**
     * 配置状态列表
     *
     * @return
     */
    @GetMapping("/check/state")
    public ResponseData getCheckStates() {
        InspectionSheetConclusionEnum[] values = InspectionSheetConclusionEnum.values();
        List<CommonType> list = new ArrayList<>();
        for (InspectionSheetConclusionEnum anEnum : values) {
            list.add(CommonType.builder().code(anEnum.getCode()).name(anEnum.getName()).build());
        }
        return ResponseData.success(list);
    }

    /**
     * 通知消息类型列表
     *
     * @return
     */
    @GetMapping("/types")
    public ResponseData getNoticeTypes() {
        NoticeTypeEnum[] values = NoticeTypeEnum.values();
        List<CommonType> list = new ArrayList<>();
        for (NoticeTypeEnum anEnum : values) {
            list.add(CommonType.builder().code(anEnum.getCode()).name(anEnum.getName()).build());
        }
        return ResponseData.success(list);
    }

    /**
     * 获取联系人列表
     *
     * @param
     * @return
     */
    @GetMapping("/users")
    public ResponseData getUserList() {
        List<UserVo> list = infoNoticeConfigService.getUserList();
        return success(list);
    }

    /**
     * 获取占位符列表
     *
     * @param noticeType 消息通知类型
     * @return
     */
    @GetMapping("/placeholder")
    public ResponseData getPlaceholders(@RequestParam(value = "noticeType", required = false) String noticeType) {
        return success(infoNoticeConfigService.getPlaceholders(noticeType));
    }

    /**
     * 获取第三方占位符列表
     *
     * @param noticeType 消息通知类型
     * @return
     */
    @GetMapping("/third_party/placeholder")
    public ResponseData getThirdPartyPlaceholders(@RequestParam(value = "noticeType", required = false) String noticeType) {
        return success(infoNoticeConfigService.getThirdPartyPlaceholders(noticeType));
    }

    /**
     * 获取工序列表
     *
     * @param
     * @return
     */
    @GetMapping("/procedures")
    public ResponseData getProcedureList(@RequestParam(value = "procedureName") String procedureName) {
        List<ProcedureEntity> procedureEntities = procedureService.lambdaQuery()
                .like(StringUtils.isNotBlank(procedureName), ProcedureEntity::getName, procedureName)
                .eq(ProcedureEntity::getState, ProcedureStateEnum.RELEASED.getCode())
                .list();
        return success(procedureEntities);
    }

    /**
     * 获取工序列表
     *
     * @param
     * @return
     */
    @GetMapping("/defects")
    public ResponseData getDefectList(@RequestParam(value = "defectName") String defectName) {
        List<DefectDefineEntity> defineEntityList = defectDefineService.lambdaQuery()
                .like(StringUtils.isNotBlank(defectName), DefectDefineEntity::getDefectName, defectName)
                .eq(DefectDefineEntity::getState, DefectDefineStateEnum.RELEASED.getCode())
                .list();
        return success(defineEntityList);
    }

    /**
     * 获取产线列表
     *
     * @param
     * @return
     */
    @GetMapping("/lines")
    public ResponseData getProductionLines(@RequestParam(value = "lineName") String lineName) {
        List<ProductionLineEntity> lineEntities = lineService.lambdaQuery()
                .like(StringUtils.isNotBlank(lineName), ProductionLineEntity::getName, lineName)
                .list();
        return success(lineEntities);
    }

    /**
     * 新增
     *
     * @param entity
     * @return
     */
    @PostMapping("/add")
    @OperLog(module = "流转通知配置", type = OperationType.ADD, desc = "新增了名称为#{configName}的流转通知配置")
    public ResponseData insertConfig(@RequestBody InfoNoticeConfigEntity entity) {
        String username = getUsername();
        entity.setCreateBy(username);
        entity.setCreateTime(new Date());
        infoNoticeConfigService.addConfig(entity);
        return ResponseData.success(entity);
    }

    /**
     * 修改
     *
     * @param entity
     * @return
     */
    @PutMapping("/update")
    @OperLog(module = "流转通知配置", type = OperationType.UPDATE, desc = "更新了名称为#{configName}的流转通知配置")
    public ResponseData editConfig(@RequestBody InfoNoticeConfigEntity entity) {
        String username = getUsername();
        entity.setUpdateBy(username);
        entity.setUpdateTime(new Date());
        infoNoticeConfigService.updateConfig(entity);
        return ResponseData.success();
    }

    /**
     * 删除
     *
     * @param id
     * @return
     */
    @DeleteMapping("/remove/{id}")
    @OperLog(module = "流转通知配置", type = OperationType.DELETE, desc = "删除了名称为#{configName}的流转通知配置")
    public ResponseData removeConfigById(@PathVariable Integer id) {
        InfoNoticeConfigEntity entity = infoNoticeConfigService.getById(id);
        if (entity == null) {
            return ResponseData.fail("该配置不存在");
        }
        infoNoticeConfigService.removeConfigById(id);
        return ResponseData.success(entity);
    }

    /**
     * 消息通知限制范围类型
     *
     * @param
     * @return
     */
    @GetMapping("/limitScope")
    public ResponseData getLimitScope() {
        NoticeLimitScopeEnum[] values = NoticeLimitScopeEnum.values();
        List<CommonType> list = new ArrayList<>();
        for (NoticeLimitScopeEnum e : values) {
            list.add(CommonType.builder().code(e.getCode()).name(e.getName()).build());
        }
        return ResponseData.success(list);
    }

    /**
     * 获取消息通知层级树
     *
     * @return
     */
    @GetMapping("/level/tree")
    public ResponseData getLevelTree() {
        List<NoticeLevelRelationConfigEntity> levelTree = infoNoticeConfigService.getLevelTree();
        return ResponseData.success(levelTree);
    }

    /**
     * 消息通知配置导出（每种类型对应一个sheet页）
     *
     * @param
     * @return
     */
    @PostMapping("/export/notice/config")
    public ResponseData exportNoticeConfig(HttpServletResponse response) {
        infoNoticeConfigService.exportNoticeConfig(response);
        return ResponseData.success();
    }

    /**
     * 触发方式枚举
     *
     * @param
     * @return
     */
    @GetMapping("/trigger/mode")
    public ResponseData getTriggerMode() {
        NoticeTriggerModeEnum[] values = NoticeTriggerModeEnum.values();
        List<CommonType> list = new ArrayList<>();
        for (NoticeTriggerModeEnum e : values) {
            list.add(CommonType.builder().code(e.getCode()).name(e.getName()).build());
        }
        return ResponseData.success(list);
    }

    /**
     * 根据通知配置，发送消息通知至精致平台
     *
     * @param noticeConfigBuilder 消息对象
     */
    @PostMapping("/send")
    public ResponseData sendCirculationNotice(@RequestBody NoticeConfigBuilder noticeConfigBuilder) {
        infoNoticeConfigService.sendInfoNotice(noticeConfigBuilder);
        return ResponseData.success();
    }

    /**
     * 根据通知配置，发送消息通知至精致平台
     *
     * @param dto 消息对象
     */
    @PostMapping("/send/v2")
    public ResponseData sendCirculationNotice2(@RequestBody NoticeConfigDTO dto) {
        infoNoticeConfigService.sendInfoNotice(JacksonUtil.convertObject(dto, NoticeConfigBuilder.class));
        return ResponseData.success();
    }
}
