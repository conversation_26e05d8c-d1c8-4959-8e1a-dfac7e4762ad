package com.yelink.dfs.controller.product;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yelink.dfs.common.BaseController;
import com.yelink.dfs.constant.Constant;
import com.yelink.dfs.constant.product.DataTypeEnum;
import com.yelink.dfs.constant.product.InspectItemTypeEnum;
import com.yelink.dfs.constant.product.ProcedureInspectStateEnum;
import com.yelink.dfs.entity.defect.dto.DefineBatchUpdateDTO;
import com.yelink.dfs.entity.product.ProcedureInspectionEntity;
import com.yelink.dfs.entity.product.dto.ProcedureInspectDTO;
import com.yelink.dfs.entity.product.vo.ProcedureInspectExportVO;
import com.yelink.dfs.service.common.ImportProgressService;
import com.yelink.dfs.service.product.ProcedureInspectionService;
import com.yelink.dfscommon.constant.RedisKeyPrefix;
import com.yelink.dfscommon.constant.dfs.CompatorStateEnumDTO;
import com.yelink.dfscommon.pojo.ResponseData;
import com.yelink.dfscommon.utils.EasyExcelUtil;
import com.yelink.dfscommon.utils.ExcelTemplateImportUtil;
import com.yelink.dfscommon.utils.ExcelUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2022/5/6 11:43
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/procedure/inspect")
public class ProcedureInspectionController extends BaseController {

    private ProcedureInspectionService procedureInspectionService;
    private ImportProgressService importProgressService;

    /**
     * 获取工序检验项列表(模糊查询)
     *
     * @return
     */
    @GetMapping("/list")
    public ResponseData getList(ProcedureInspectDTO procedureInspectDTO) {
        Page<ProcedureInspectionEntity> page = procedureInspectionService.getList(procedureInspectDTO);
        return ResponseData.success(page);
    }

    /**
     * 新增工序检验项
     *
     * @param entity
     * @return
     */
    @PostMapping("/add")
    public ResponseData addProcedureInspection(@RequestBody ProcedureInspectionEntity entity) {
        entity.setCreateBy(getUsername());
        entity.setCreateTime(new Date());
        procedureInspectionService.saveProcedureInspection(entity);
        return ResponseData.success();
    }


    /**
     * 修改工序检验项
     *
     * @param entity
     * @return
     */
    @PostMapping("/update")
    public ResponseData updateProcedureInspection(@RequestBody ProcedureInspectionEntity entity) {
        entity.setUpdateBy(getUsername());
        entity.setUpdateTime(new Date());
        procedureInspectionService.updateProcedureInspection(entity);
        return success();
    }


    /**
     * 删除工序检验项
     *
     * @param id
     * @returnq
     */
    @DeleteMapping("/remove/{id}")
    public ResponseData removeProcedureInspection(@PathVariable Integer id) {
        ProcedureInspectionEntity qualityInspectionSchemeEntity = procedureInspectionService.removeProcedureInspectionById(id);
        return success(qualityInspectionSchemeEntity);
    }

    /**
     * 保存并生效工序检验项定义
     *
     * @param entity
     * @return
     */
    @PostMapping("/add/released")
    public ResponseData addReleasedDefine(@RequestBody ProcedureInspectionEntity entity) {
        String username = getUsername();
        entity.setCreateBy(username);
        entity.setUpdateBy(username);
        entity.setCreateTime(new Date());
        entity.setUpdateTime(new Date());
        procedureInspectionService.addReleasedDefine(entity);
        return ResponseData.success();
    }

    /**
     * 批量编辑工序检验项定义
     *
     * @param
     * @return
     */
    @PutMapping("/batch/update")
    public ResponseData updateDefine(@RequestBody DefineBatchUpdateDTO batchUpdateDTO) {
        batchUpdateDTO.setUsername(getUsername());
        procedureInspectionService.batchUpdateDefine(batchUpdateDTO);
        return success();
    }


    /**
     * 获取工序检验项详情
     *
     * @param id
     * @return
     */
    @GetMapping("/detail/{id}")
    public ResponseData getSchemeDetail(@PathVariable Integer id) {
        ProcedureInspectionEntity entity = procedureInspectionService.getProcedureInspectionDetail(id);
        return success(entity);
    }

    /**
     * 获取状态
     *
     * @retur
     */
    @GetMapping("/state")
    public ResponseData getState() {
        List<CompatorStateEnumDTO> list = Arrays.stream(ProcedureInspectStateEnum.values())
                .map(procedureInspectStateEnum -> CompatorStateEnumDTO.builder()
                        .code(procedureInspectStateEnum.getFieldEname())
                        .name(procedureInspectStateEnum.getFieldName())
                        .build()).collect(Collectors.toList());
        return success(list);
    }

    /**
     * 获取数据类型
     *
     * @retur
     */
    @GetMapping("/data/type")
    public ResponseData getDataType() {
        List<CompatorStateEnumDTO> list = Arrays.stream(DataTypeEnum.values())
                .map(procedureInspectDataTypeEnum -> CompatorStateEnumDTO.builder()
                        .code(procedureInspectDataTypeEnum.getFieldEname())
                        .name(procedureInspectDataTypeEnum.getFieldName())
                        .build()).collect(Collectors.toList());
        return success(list);
    }
    /**
     * 工序检验项 检验类型枚举
     */
    @GetMapping("/item/type/enum")
    public ResponseData getInspectItemType() {
        List<CompatorStateEnumDTO> list = Arrays.stream(InspectItemTypeEnum.values())
                .map(procedureInspectDataTypeEnum -> CompatorStateEnumDTO.builder()
                        .code(procedureInspectDataTypeEnum.getCode())
                        .name(procedureInspectDataTypeEnum.getName())
                        .build()).collect(Collectors.toList());
        return success(list);
    }

    /**
     * 工序校验项定义导入：工序校验项定义导入默认模板下载
     */
    @GetMapping("/export/default/template")
    public void downloadDefaultTemplate(HttpServletResponse response) throws IOException {
        //找到导出的模板
        byte[] bytes = ExcelUtil.exportDefaultExcel("classpath:template/procedureInspectDefineTemplate.xlsx");
        ExcelTemplateImportUtil.responseToClient(response, bytes, "工序校验项定义默认导入模板" + Constant.XLSX);
    }
    /**
     * 工序校验项定义导入
     */
    @PostMapping("/excel/import")
    public ResponseData importExcel(MultipartFile file) throws IOException {
        //1、判断导入文件的合法性
        ExcelUtil.checkImportExcelFile(file);
        procedureInspectionService.importExcel(file.getOriginalFilename(), file.getInputStream(), getUsername());
        return success();
    }
    /**
     * 工序校验项定义导入：获取工艺工序检验导入进度
     */
    @GetMapping("/excel/import/progress")
    public ResponseData getImportInspectExcelProgress() {
        return success(importProgressService.importProgress(RedisKeyPrefix.PROCEDURE_INSPECT_DEFINE_IMPORT_PROGRESS));
    }

    /**
     * 导出
     *
     * @param procedureInspectDTO
     * @param response
     * @return
     * @throws IOException
     */
    @PostMapping("/export")
    public ResponseData export(@RequestBody ProcedureInspectDTO procedureInspectDTO, HttpServletResponse response) throws IOException {
        List<ProcedureInspectionEntity> records = procedureInspectionService.getList(procedureInspectDTO).getRecords();
        List<ProcedureInspectExportVO> result = JSONObject.parseArray(JSON.toJSONString(records), ProcedureInspectExportVO.class);
        EasyExcelUtil.export(response, EasyExcelUtil.getMarkFields(ProcedureInspectExportVO.class),
                "工序校验项定义", "工序检验项定义", result, ProcedureInspectExportVO.class);
        return ResponseData.success();
    }
}
