package com.yelink.dfs.controller.order;

import com.yelink.dfs.common.BaseController;
import com.yelink.dfs.entity.order.WorkOrderRemarkEntity;
import com.yelink.dfs.entity.order.dto.WorkOrderRemarkInsertDTO;
import com.yelink.dfs.entity.order.dto.WorkOrderRemarkUpdateDTO;
import com.yelink.dfs.service.order.WorkOrderRemarkService;
import com.yelink.dfscommon.pojo.ResponseData;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 工单 备注
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/work-orders/remark")
public class WorkOrderRemarkController extends BaseController {

    @Resource
    private WorkOrderRemarkService workOrderRemarkService;

    @GetMapping("/list")
    public ResponseData list(@RequestParam String workOrderNumber) {
        List<WorkOrderRemarkEntity> list = workOrderRemarkService.listWorkOrderRemark(workOrderNumber);
        return success(list);
    }

    @PostMapping("/add")
    public ResponseData add(@RequestBody @Validated WorkOrderRemarkInsertDTO dto) {
        workOrderRemarkService.add(dto);
        return success();
    }

    @PostMapping("/edit")
    public ResponseData edit(@RequestBody @Validated WorkOrderRemarkUpdateDTO dto) {
        workOrderRemarkService.edit(dto);
        return success();
    }

    @DeleteMapping("/remove")
    public ResponseData remove(@RequestParam Integer id) {
        workOrderRemarkService.removeById(id);
        return success();
    }


}
