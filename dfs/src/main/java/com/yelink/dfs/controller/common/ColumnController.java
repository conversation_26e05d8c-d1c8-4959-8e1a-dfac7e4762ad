package com.yelink.dfs.controller.common;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.yelink.dfs.common.BaseController;
import com.yelink.dfs.constant.target.MethodConst;
import com.yelink.dfs.entity.user.SysUserColumnRecordEntity;
import com.yelink.dfs.service.user.SysUserColumnRecordService;
import com.yelink.dfscommon.constant.Constant;
import com.yelink.dfscommon.pojo.ResponseData;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @Date 2022/5/12 11:16
 */
@RestController
@Slf4j
@AllArgsConstructor
@RequestMapping("/columns")
public class ColumnController extends BaseController {
    private SysUserColumnRecordService sysUserColumnRecordService;
    private RedisTemplate redisTemplate;

    /**
     * 按用户保存列配置
     *
     * @param
     * @return
     */
    @PutMapping("/user/column")
    public ResponseData saveOrUpdateUserColumn(@RequestBody SysUserColumnRecordEntity entity) {
        String username = StringUtils.isNotBlank(entity.getUserName()) ? entity.getUserName() : getUsername();
        entity.setUserName(username);
        String key = MethodConst.USER_COLUMN + Constant.UNDERLINE + username;
        // 加锁，防止并发问题
        try {
            Boolean lockStatus = redisTemplate.opsForValue().setIfAbsent(key, new Date(), 1, TimeUnit.SECONDS);
            if (lockStatus == null || !lockStatus) {
                return success();
            }
            LambdaUpdateWrapper<SysUserColumnRecordEntity> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(SysUserColumnRecordEntity::getUserName, entity.getUserName())
                    .eq(SysUserColumnRecordEntity::getModularType, entity.getModularType());
            boolean isSuccess = sysUserColumnRecordService.saveOrUpdate(entity, updateWrapper);
            return success(isSuccess);
        } catch (Exception e) {
            log.error("列配置保存失败", e);
        } finally {
            redisTemplate.delete(key);
        }
        return success(false);
    }

    /**
     * 获取用户列配置
     *
     * @param
     * @return
     */
    @GetMapping("/get/user/column")
    public ResponseData getUserColumn(@RequestParam(value = "modelType") String modelType,
                                      @RequestParam(value = "username", required = false) String username) {
        username = StringUtils.isNotBlank(username) ? username : getUsername();
        LambdaQueryWrapper<SysUserColumnRecordEntity> qw = new LambdaQueryWrapper<>();
        qw.eq(SysUserColumnRecordEntity::getUserName, username);
        qw.eq(SysUserColumnRecordEntity::getModularType, modelType);
        return success(sysUserColumnRecordService.getOne(qw));
    }


}
