package com.yelink.dfs.controller.order;


import com.yelink.dfs.common.BaseController;
import com.yelink.dfs.service.order.CustomerMaterialListService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 客户管理
 *
 * <AUTHOR>
 * @Date 2021/4/6 21:33
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/customer/materials")
public class CustomerMaterialListController extends BaseController {

    private CustomerMaterialListService customerMaterialListService;



}
