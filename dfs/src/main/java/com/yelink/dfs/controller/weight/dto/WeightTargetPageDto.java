package com.yelink.dfs.controller.weight.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @description: 分页查询参数
 * @author: shuang
 * @time: 2022/7/15
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class WeightTargetPageDto {

    /**
     * 页数
     */
    private Integer current;
    /**
     * 分页大小
     */
    private Integer size;
    /**
     * 关联的生产工工单编号
     */
    private String workOrderCode;
    /**
     * 产品名称
     */
    private String productName;
    /**
     * 产品编码
     */
    private String productCode;

}
