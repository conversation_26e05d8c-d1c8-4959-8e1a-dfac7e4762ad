package com.yelink.dfs.controller.product;

import cn.hutool.core.util.RandomUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yelink.dfs.common.BaseController;
import com.yelink.dfs.constant.RedisKeyPrefix;
import com.yelink.dfs.constant.product.MaterialAttributeDataTypeEnum;
import com.yelink.dfs.constant.product.MaterialAttributeInputTypeEnum;
import com.yelink.dfs.entity.product.dto.MaterialAttributeSelectDTO;
import com.yelink.dfs.service.product.MaterialAttributeService;
import com.yelink.dfscommon.constant.Constant;
import com.yelink.dfscommon.entity.CommonType;
import com.yelink.dfscommon.entity.common.BatchChangeStateDTO;
import com.yelink.dfscommon.entity.dfs.MaterialAttributeEntity;
import com.yelink.dfscommon.pojo.ResponseData;
import com.yelink.dfscommon.utils.DateUtil;
import com.yelink.dfscommon.utils.ExcelUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description 物料表
 * @Date 2021/4/14
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/material/attributes")
public class MaterialAttributeController extends BaseController {

    private final MaterialAttributeService materialAttributeService;

    /**
     * 查询物料属性列表
     *
     * @param
     * @return
     */
    @PostMapping("/list")
    public ResponseData getList(@RequestBody MaterialAttributeSelectDTO selectDTO) {
        Page<MaterialAttributeEntity> page = materialAttributeService.getList(selectDTO);
        return success(page);
    }

    /**
     * 新增物料属性
     *
     * @param
     * @return
     */
    @PostMapping("/add")
    public ResponseData add(@RequestBody MaterialAttributeEntity entity) {
        String username = getUsername();
        entity.setCreateBy(username);
        entity.setUpdateBy(username);
        entity.setCreateTime(new Date());
        entity.setUpdateTime(new Date());
        materialAttributeService.saveEntity(entity);
        return success();
    }

    /**
     * 更新物料属性
     *
     * @param
     * @return
     */
    @PutMapping("/update")
    public ResponseData update(@RequestBody MaterialAttributeEntity entity) {
        String username = getUsername();
        entity.setUpdateBy(username);
        entity.setUpdateTime(new Date());
        materialAttributeService.updateEntity(entity);
        return success();
    }

    /**
     * 删除物料属性
     *
     * @param
     * @return
     */
    @DeleteMapping("/delete/{id}")
    public ResponseData delete(@PathVariable(value = "id") Integer id) {
        materialAttributeService.delete(id);
        return success();
    }

    /**
     * 获取详情
     *
     * @param
     * @return
     */
    @GetMapping("/detail/{id}")
    public ResponseData detail(@PathVariable(value = "id") Integer id) {
        return success(materialAttributeService.detail(id));
    }

    /**
     * 获取字段类型
     */
    @GetMapping("/data/types")
    public ResponseData getDataTypes() {
        List<CommonType> list = new ArrayList<>();
        MaterialAttributeDataTypeEnum[] values = MaterialAttributeDataTypeEnum.values();
        for (MaterialAttributeDataTypeEnum value : values) {
            list.add(CommonType.builder().code(value.getCode()).name(value.getName()).build());
        }
        return success(list);
    }

    /**
     * 获取输入框类型
     */
    @GetMapping("/input/types")
    public ResponseData getInputTypes() {
        List<CommonType> list = new ArrayList<>();
        MaterialAttributeInputTypeEnum[] values = MaterialAttributeInputTypeEnum.values();
        for (MaterialAttributeInputTypeEnum value : values) {
            list.add(CommonType.builder().code(value.getCode()).name(value.getName()).build());
        }
        return success(list);
    }

    /**
     * 物料属性批量编辑
     */
    @PostMapping("/batch/update/state")
    public ResponseData batchUpdate(@RequestBody BatchChangeStateDTO batchApprovalDTO) {
        if (batchApprovalDTO.getIds() == null) {
            return fail("编辑的单据id为空");
        }
        String username = getUsername();
        Boolean ret = materialAttributeService.batchUpdateState(batchApprovalDTO, username);
        return ResponseData.success(ret);
    }

    /**
     * 物料属性导入:下载默认导入模板
     */
    @GetMapping("/down/default/template")
    public void downDefaultTemplate(HttpServletResponse response) throws Exception {
        materialAttributeService.downloadDefaultTemplate("classpath:template/materialAttributeTemplate.xlsx", response, "物料属性导入模板" + Constant.XLSX);
    }

    /**
     * 物料属性导入
     */
    @PostMapping("/import")
    public ResponseData importData(MultipartFile file) throws Exception {
        //1、判断导入文件的合法性
        ExcelUtil.checkImportExcelFile(file);
        //异步数据导入
        String importProgressKey = RedisKeyPrefix.MATERIAL_ATTRIBUTE_IMPORT_PROGRESS + DateUtil.format(new Date(), DateUtil.yyyyMMddHHmmss_FORMAT) + "_" + RandomUtil.randomString(2);
        materialAttributeService.sycImportData(file.getOriginalFilename(), file.getInputStream(), getUsername(), importProgressKey);
        return ResponseData.success(importProgressKey);
    }

    /**
     * 物料属性导入:查询导入进度
     */
    @GetMapping("/import/progress")
    public ResponseData getImportProgress() {
        return success(materialAttributeService.importProgress());
    }

}
