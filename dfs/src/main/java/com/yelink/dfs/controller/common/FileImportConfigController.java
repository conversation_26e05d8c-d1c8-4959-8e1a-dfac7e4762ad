package com.yelink.dfs.controller.common;

import com.yelink.dfs.common.BaseController;
import com.yelink.dfs.constant.Constant;
import com.yelink.dfs.constant.OperationType;
import com.yelink.dfs.entity.common.config.dto.FileImportConfigEditDTO;
import com.yelink.dfs.service.common.FileImportConfigService;
import com.yelink.dfs.service.common.UploadService;
import com.yelink.dfscommon.annotation.OperLog;
import com.yelink.dfscommon.constant.RespCodeEnum;
import com.yelink.dfscommon.constant.ResponseException;
import com.yelink.dfscommon.constant.dfs.UploadFileCodeEnum;
import com.yelink.dfscommon.pojo.ResponseData;
import com.yelink.dfscommon.utils.ExcelTemplateImportUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/fileImportConfig/")
public class FileImportConfigController extends BaseController {
    @Resource
    private FileImportConfigService fileImportConfigService;
    @Resource
    private UploadService uploadService;


    @GetMapping("/list")
    public ResponseData list() {
        return ResponseData.success(fileImportConfigService.list());
    }


    @PostMapping("/edit")
    public ResponseData edit(@RequestBody List<FileImportConfigEditDTO> params) {
        fileImportConfigService.edit(params);
        return ResponseData.success();
    }

    /**
     * 导入 自定义物料导入模板
     */
    @PostMapping("/import/template")
    @OperLog(module = "导入日志", type = OperationType.ADD, desc = "导入批量的自定义模板")
    public ResponseData importExcelTemplate(MultipartFile file) {
        if (StringUtils.isEmpty(file.getOriginalFilename())) {
            throw new ResponseException(RespCodeEnum.FILE_NAME_NOT_EMPTY);
        }
        // 上传
        uploadService.uploadReferencedFile(UploadFileCodeEnum.FILE_IMPORT_TEMPLATE.getCode(), getUsername(), file);
        return ResponseData.success();
    }

    /**
     * 用户导入的 自定义物料导入模板 下载
     */
    @GetMapping("/export/template")
    public void exportExcelTemplate(HttpServletResponse response) throws IOException {
        byte[] download = uploadService.download(UploadFileCodeEnum.FILE_IMPORT_TEMPLATE.getCode());
        if (download != null && !ObjectUtils.isEmpty(download)) {
            //用户自定义模板
            ExcelTemplateImportUtil.responseToClient(response, download, UploadFileCodeEnum.FILE_IMPORT_TEMPLATE.getName() + Constant.XLSX);
        }else {
            throw new ResponseException("未上传自定模板");
        }
    }
}
