package com.yelink.dfs.controller.alarm;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yelink.dfs.common.BaseController;
import com.yelink.dfs.constant.Constant;
import com.yelink.dfs.entity.alarm.AlarmDefinitionEntity;
import com.yelink.dfs.entity.alarm.dto.AlarmDefinitionBatchUpdateDTO;
import com.yelink.dfs.entity.alarm.dto.AlarmDefinitionExcelDTO;
import com.yelink.dfs.entity.alarm.dto.AlarmDetailTempDTO;
import com.yelink.dfs.service.alarm.AlarmDefinitionService;
import com.yelink.dfscommon.constant.RespCodeEnum;
import com.yelink.dfscommon.constant.ResponseException;
import com.yelink.dfscommon.pojo.ResponseData;
import com.yelink.dfscommon.utils.EasyExcelUtil;
import com.yelink.dfscommon.utils.ExcelTemplateImportUtil;
import com.yelink.dfscommon.utils.ExcelUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.Date;
import java.util.List;


/**
 * <AUTHOR>
 * @Date 2021/11/2 19:28
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/definition")
public class AlarmDefinitionController extends BaseController {
    private AlarmDefinitionService alarmDefinitionService;

    /**
     * 获取告警定义中的告警级别
     *
     * @param
     * @return
     */
    @GetMapping("/type")
    public ResponseData getAlarmDefinitionLevel() {
        return success(alarmDefinitionService.getAlarmDefinitionLevel());
    }


    /**
     * 获取告警定义中的数据来源
     *
     * @param
     * @return
     */
    @GetMapping("/data/sources")
    public ResponseData getAlarmDataSources() {
        return success(alarmDefinitionService.getAlarmDataSources());
    }

    /**
     * 查询告警定义列表
     *
     * @param pageSize
     * @param currentPage
     * @param alarmClassifyName
     * @param alarmLevelName
     * @return
     */
    @GetMapping("/list")
    public ResponseData list(@RequestParam(value = "pageSize", required = false) Integer pageSize,
                             @RequestParam(value = "currentPage", required = false) Integer currentPage,
                             @RequestParam(value = "alarmClassifyName", required = false) String alarmClassifyName,
                             @RequestParam(value = "alarmLevelName", required = false) String alarmLevelName,
                             @RequestParam(value = "alarmDefinitionName", required = false) String alarmDefinitionName) {
        return success(alarmDefinitionService.listByPage(pageSize, currentPage, alarmClassifyName, alarmLevelName, alarmDefinitionName));
    }
    /**
     * 通过设备分类id查询绑定的告警定义列表
     *
     * @param deviceModelId
     * @return
     */
    @GetMapping("/list/by/model")
    public ResponseData listByModelId(@RequestParam(value = "deviceModelId", required = false) Integer deviceModelId) {
        return success(alarmDefinitionService.listByModelId(deviceModelId));
    }
    /**
     * 通过告警类型获取所有告警定义——（pad+web端）
     *
     * @return
     */
    @GetMapping("/all")
    public ResponseData list(
            @RequestParam(value = "alarmClassifyId", required = false) Integer alarmClassifyId,
            @RequestParam(value = "classifyId", required = false) Integer classifyId) {
        return success(alarmDefinitionService.getAllList(alarmClassifyId, classifyId));
    }

    /**
     * 获取告警定义中的告警定义ID
     *
     * @param
     * @return
     */
    @GetMapping("/definitions/id")
    public ResponseData getAlarmDefinitionId() {
        return success(alarmDefinitionService.getAlarmDefinitionId());
    }


    /**
     * 新增告警定义
     *
     * @param entity
     * @return
     */
    @PostMapping("/add")
    public ResponseData addAlarmDefinition(@RequestBody AlarmDefinitionEntity entity) {
        entity.setCreateBy(getUsername());
        entity.setCreateTime(new Date());
        if (entity.getAlarmClassifyId() == null) {
            throw new ResponseException(RespCodeEnum.ALARM_CLASSIFY_ID_IS_NULL);
        }
        alarmDefinitionService.saveAlarmDefinition(entity);
        return success();
    }
    @GetMapping("/detail")
    public ResponseData detailAlarmDefinition(@RequestParam Integer id) {
        return success(alarmDefinitionService.detailAlarmDefinition(id));
    }


    /**
     * 修改告警定义
     *
     * @param entity
     * @return
     */
    @PutMapping("/update")
    public ResponseData update(@RequestBody AlarmDefinitionEntity entity) {
        String username = getUsername();
        entity.setUpdateBy(username);
        entity.setUpdateTime(new Date());
        if (entity.getAlarmClassifyId() == null) {
            throw new ResponseException(RespCodeEnum.ALARM_CLASSIFY_ID_IS_NULL);
        }
        alarmDefinitionService.updateAlarmDefinition(entity);
        return success(entity);
    }

    /**
     * 批量修改告警定义
     *
     * @param dto
     * @return
     */
    @PutMapping("/batch/update")
    public ResponseData batchUpdate(@RequestBody AlarmDefinitionBatchUpdateDTO dto) {
        String username = getUsername();
        alarmDefinitionService.batchUpdate(dto, username);
        return success();
    }

    /**
     * 批量编辑处理进度
     *
     * @return
     */
    @GetMapping("/batch/update/progress")
    public ResponseData batchUpdateProgress() {
        String s = alarmDefinitionService.getBatchUpdateProgress();
        return success(s);
    }

    /**
     * 告警定义导入前预览
     *
     * @return
     */
    @PostMapping("/alarm/definition/preview")
    public ResponseData previewBeforeImport(MultipartFile file) throws IOException {
        JSONObject jsonObjects = alarmDefinitionService.importExcel(file);
        return success(jsonObjects);
    }

    /**
     * 保存导入的告警定义
     *
     * @return
     */
    @PostMapping("/alarm/definition/import")
    public ResponseData importDefineList(@RequestBody List<AlarmDefinitionExcelDTO> dtoList) {
        alarmDefinitionService.saveDefinitionImport(dtoList);
        return ResponseData.success();
    }

    @GetMapping("/alarm/definition/export")
    public void exportDefineList(@RequestParam(value = "alarmClassifyName", required = false) String alarmClassifyName,
                                 @RequestParam(value = "alarmLevelName", required = false) String alarmLevelName,
                                 @RequestParam(value = "alarmDefinitionName", required = false) String alarmDefinitionName,
                                 HttpServletResponse response) throws IOException {
        Page<AlarmDefinitionEntity> page = alarmDefinitionService.listByPage(null, null, alarmClassifyName, alarmLevelName, alarmDefinitionName);
        List<AlarmDefinitionExcelDTO> exportList = JSON.parseArray(JSON.toJSONString(page.getRecords()), AlarmDefinitionExcelDTO.class);
        EasyExcelUtil.export(response, "AlarmDefinitionFile", "AlarmDefinitionSheet", exportList, AlarmDefinitionExcelDTO.class);
    }


    /**
     * 告警定义模板下载
     *
     * @return
     */
    @GetMapping("/excel/export")
    public void download(HttpServletResponse response) throws IOException {
        byte[] bytes = ExcelUtil.exportDefaultExcel("classpath:template/alarmDefinition.xlsx");
        ExcelTemplateImportUtil.responseToClient(response, bytes, "告警定义模板" + Constant.XLSX);
    }

    /**
     * 根据code查询告警定义——(pad端接口)
     * @param code
     * @return
     */
    @GetMapping("/alarm/definition/{code}")
    public ResponseData getAlarmDefinition(@PathVariable String code){
        AlarmDefinitionEntity alarmDefinitionEntity=alarmDefinitionService.getDefinitionByCode(code);
        return ResponseData.success(alarmDefinitionEntity);
    }

    /**
     * 通过字段的值判断当前字段的列表(用于pad端新增事件)
     *
     * @param currentEname
     * @return
     */
    @PostMapping("current/list")
    public ResponseData getCurrentFieldList(@RequestParam(value = "currentEname") String currentEname,
                                            @RequestBody List<AlarmDetailTempDTO> fieldList) {
        List<String> list = alarmDefinitionService.getCurrentFieldList(currentEname, fieldList);
        return success(list);
    }



}
