package com.yelink.dfs.controller.model;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yelink.dfs.common.BaseController;
import com.yelink.dfs.constant.OperationType;
import com.yelink.dfs.constant.model.FacilitiesCountTypeEnum;
import com.yelink.dfs.constant.model.FacilitiesTypeEnum;
import com.yelink.dfs.entity.common.CommonState;
import com.yelink.dfs.entity.manufacture.ProductionLineEntity;
import com.yelink.dfs.entity.model.AreaEntity;
import com.yelink.dfs.entity.model.FacilitiesEntity;
import com.yelink.dfs.entity.model.GridEntity;
import com.yelink.dfs.entity.target.record.ReportFacStateEntity;
import com.yelink.dfs.service.impl.common.WorkPropertise;
import com.yelink.dfs.service.manufacture.ProductionLineService;
import com.yelink.dfs.service.model.AreaService;
import com.yelink.dfs.service.model.FacilitiesService;
import com.yelink.dfs.service.model.GridService;
import com.yelink.dfs.service.model.ModelService;
import com.yelink.dfs.service.target.record.ReportFacStateService;
import com.yelink.dfscommon.annotation.OperLog;
import com.yelink.dfscommon.constant.Constant;
import com.yelink.dfscommon.constant.NetworkStatusEnum;
import com.yelink.dfscommon.constant.ParamException;
import com.yelink.dfscommon.constant.RedisKeyPrefix;
import com.yelink.dfscommon.constant.RespCodeEnum;
import com.yelink.dfscommon.dto.ImportProgressDTO;
import com.yelink.dfscommon.entity.CommonType;
import com.yelink.dfscommon.pojo.ResponseData;
import com.yelink.dfscommon.utils.ExcelTemplateImportUtil;
import com.yelink.dfscommon.utils.ExcelUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.Resource;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.core.io.support.ResourcePatternResolver;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;


/**
 * @Description: 设施接口 ，传感器的绑定操作
 * @Author: zhengfu ，shuang
 * @Date: 2020/12/3
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/facilities")
public class FacilitiesController extends BaseController {

    private final AreaService areaService;
    private final GridService gridService;
    private final ProductionLineService productionLineService;
    private final FacilitiesService facilitiesService;
    private final ReportFacStateService reportFacStateService;
    private final WorkPropertise workPropertise;
    private final ModelService modelService;
    private final RedisTemplate<String, Object> redisTemplate;

    /**
     * 添加工位cubicle,工作站station,生产设备produce,安全设施security
     * 并且同时绑定设备
     *
     * @return
     */
    @PostMapping("/insert/{type}")
    @OperLog(module = "工厂配置", type = OperationType.ADD, desc = "新增了工位编码为#{fcodes}的工位信息")
    public ResponseData addFacilities(@PathVariable String type, @RequestBody @Valid FacilitiesEntity entity,
                                      BindingResult bindingResult) {
        if (bindingResult.hasErrors()) {
            throw new ParamException(bindingResult);
        }
        String username = getUsername();
        entity.setCreateBy(username);
        entity.setUpdateBy(username);
        facilitiesService.addFacilities(entity, type);
        return success(entity);
    }

    /**
     * 修改生产设备、工作站、安全设施
     *
     * @return
     */
    @PutMapping("/update")
    @OperLog(module = "工厂配置", type = OperationType.UPDATE, desc = "修改了工位编码为#{fcode}的工位信息")
    public ResponseData update(@RequestBody FacilitiesEntity entity) {
        entity.setUpdateBy(getUsername());
        entity.setUpdateTime(new Date());
        if (entity.getIsCheck().equals(FacilitiesCountTypeEnum.NONE.getCode())) {
            entity.setIsMain(false);
        }
        facilitiesService.updateFacilities(entity);
        return success(facilitiesService.getById(entity.getFid()));
    }

    /**
     * 绑定传感器
     *
     * @param euis
     * @param fid
     * @return
     */
    @PostMapping("/bind/{fid}")
    @OperLog(module = "工厂配置", type = OperationType.UPDATE, desc = "绑定了fid为#{fid}的传感器")
    public ResponseData bind(@PathVariable("fid") Integer fid, @RequestBody String euis) {
        facilitiesService.bind(fid, euis);
        return success();
    }

    /**
     * 绑定传感器回显
     *
     * @param fid
     * @return
     */
    @GetMapping("/bind/detail/{fid}")
    public ResponseData bindDetail(@PathVariable("fid") Integer fid) {
        return success(facilitiesService.bindDetail(fid));
    }

    /**
     * 查询生产设备、工作站、安全设施
     *
     * @param type
     * @param name
     * @param lineId
     * @param fcode
     * @param orderBy
     * @param modelName 工位类型名称
     * @param current
     * @param size
     * @return
     */
    @GetMapping("/list")
    public ResponseData list(@RequestParam(value = "type", required = false) String type,
                             @RequestParam(value = "name", required = false) String name,
                             @RequestParam(value = "lineId", required = false) String lineId,
                             @RequestParam(value = "fcode", required = false) String fcode,
                             @RequestParam(value = "orderBy", required = false) String orderBy,
                             @RequestParam(value = "modelName", required = false) String modelName,
                             @RequestParam(value = "current", required = false) Integer current,
                             @RequestParam(value = "size", required = false) Integer size) {
        Page<FacilitiesEntity> page = facilitiesService.getList(current, size, name, type, fcode, lineId, orderBy, modelName);
        return success(page);
    }

    /**
     * 查询该产线下所有工位
     */
    @GetMapping("/list/fac")
    public ResponseData listFacByLineId(@RequestParam(required = false) Integer lineId) {
        List<FacilitiesEntity> list = facilitiesService.lambdaQuery()
                .eq(Objects.nonNull(lineId), FacilitiesEntity::getProductionLineId, lineId)
                .eq(FacilitiesEntity::getTypeOneCode, FacilitiesTypeEnum.CUBICLE.getCode())
                .list();
        return ResponseData.success(list);
    }
    /**
     * 查询所有工位(按产线分组)
     */
    @GetMapping("/list/fac/group/line")
    public ResponseData listFacGroupByLine() {
        List<ProductionLineEntity> productionLineEntityList = productionLineService.list();
        if(CollectionUtils.isEmpty(productionLineEntityList)){
            return ResponseData.success(productionLineEntityList);
        }
        for(ProductionLineEntity productionLineEntity:productionLineEntityList){
            List<FacilitiesEntity> list = facilitiesService.lambdaQuery()
                    .eq(FacilitiesEntity::getTypeOneCode, FacilitiesTypeEnum.CUBICLE.getCode())
                    .eq(FacilitiesEntity::getProductionLineId,productionLineEntity.getProductionLineId())
                    .list();
            productionLineEntity.setFacilities(list);
        }
     return ResponseData.success(productionLineEntityList);
    }

    /**
     * 查询生产设备、工作站、安全设施详情
     *
     * @return
     */
    @GetMapping("/select/{fid}")
    public ResponseData detail(@PathVariable("fid") Integer id) {

        FacilitiesEntity detail = facilitiesService.detail(id);
        Integer lineId = detail.getProductionLineId();
        if (lineId != null) {
            ProductionLineEntity lineEntity = productionLineService.getById(lineId);
            String lineName = lineEntity.getName();
            detail.setLinename(lineName);
            detail.setProductLineCode(lineEntity.getProductionLineCode());
        }
        AreaEntity areaEntity = areaService.getById(detail.getAid());
        String aname = areaEntity == null ? null : areaEntity.getAname();

        GridEntity gridEntity = gridService.getById(detail.getGid());
        String gname = gridEntity == null ? null : gridEntity.getGname();

        detail.setAname(aname);
        detail.setGname(gname);
        detail.setModelName(modelService.getModelNameById(detail.getModelId()));

        return success(detail);
    }

    /**
     * 根据用户查询工位
     *
     * @return
     */
    @GetMapping("/select/facilities")
    public ResponseData getFacilitiesByUser() {
        List<FacilitiesEntity> list = facilitiesService.getFacilitiesByUser(getUsername());
        return success(list);
    }


    /**
     * 工位看板
     *
     * @return
     */
    /*@GetMapping("/cubicle/board/{eui}")
    public ResponseData cubicleBoard(@PathVariable("eui") String eui) {
        CubicleBoardEntity cubicleBoardEntity = facilitiesService.cubicleBoard(eui);
        log.info(JSON.toJSONString(cubicleBoardEntity));
        return success(cubicleBoardEntity);
    }*/

    /**
     * 查询产线上所有工位看板数据
     *
     * @return
     */
    /*@GetMapping("/cubicle/board/all/{lineId}")
    public ResponseData allCubicleBoard(@PathVariable("lineId") Integer lineId) {
        List<CubicleBoardEntity> list = facilitiesService.allCubicleBoard(lineId);
        log.info(JSON.toJSONString(list));
        return success(list);
    }*/

    /**
     * 通过工位id查询工位看板
     *
     * @param fid
     * @return
     */
    /*@GetMapping("/cubicle/board/fid/{fid}")
    public ResponseData cubicleBoard(@PathVariable("fid") Integer fid) {
        CubicleBoardEntity cubicleBoardEntity = facilitiesService.cubicleBoard(fid);
        log.info(JSON.toJSONString(cubicleBoardEntity));
        return success(cubicleBoardEntity);
    }*/

    /**
     * 员工登录工位看板
     *
     * @param eui 设备eui
     * @param num 工号
     * @return
     */
    /*@GetMapping("/cubicle/bind/{eui}/{num}")
    public ResponseData cubicleBoard(@PathVariable("eui") String eui, @PathVariable("num") String num) {
        facilitiesService.bindCubicleUser(eui, num);
        return success();
    }*/

    /**
     * 删除工位
     *
     * @param fid
     * @return
     */
    @DeleteMapping("/delete/{fid}")
    @OperLog(module = "工厂配置", type = OperationType.DELETE, desc = "删除了工位编码为#{fcode}，名称为#{fname}的工位")
    public ResponseData delete(@PathVariable("fid") Integer fid) {
        return success(facilitiesService.deleteFac(fid));
    }

    /**
     * 查询工位下的传感器
     *
     * @return
     */
    @GetMapping("/sensor/list/{fid}")
    public ResponseData list(@PathVariable(value = "fid") int fid,
                             @RequestParam(defaultValue = "10", value = "pageSize") int pageSize,
                             @RequestParam(defaultValue = "1", value = "currentPage") int currentPage) {

        return success(facilitiesService.sensorlistByPage(fid, pageSize, currentPage));
    }

    /**
     * 查询工位状态历史记录
     *
     * @param fid
     * @return
     */
    @GetMapping("/history/state/{fid}")
    public ResponseData getStateHistory(@PathVariable Integer fid) {
        QueryWrapper<ReportFacStateEntity> countWrapper = new QueryWrapper<>();
        countWrapper.lambda().eq(ReportFacStateEntity::getFid, fid)
                .orderByDesc(ReportFacStateEntity::getCreateTime);
        List<ReportFacStateEntity> list = reportFacStateService.list(countWrapper);
        return success(list);
    }

    /**
     * 工位不良品明细导出excel
     *
     * @param
     * @return
     */
    @GetMapping("/unqualified/export")
    public void exportExcel(HttpServletResponse response) {
        facilitiesService.exportExcel(response);
    }

    /**
     * 投入工单是否需要选择工位
     *
     * @param
     * @return
     */
    @GetMapping("/select/fac")
    public ResponseData needSelectFac() {
        return success(workPropertise.getSelectFac());
    }

    /**
     * 开启工单前查询工位
     * 前端回传选中工位，以该工位计数作为产线产量来源
     *
     * @param
     * @return
     */
    @PostMapping("/output/source")
    public ResponseData outputSource(@RequestParam("fids") String fids, @RequestParam("lineId") Integer lineId) {
        facilitiesService.outputSource(fids, lineId);
        return success();
    }

    /**
     * 获取类型
     *
     * @return
     */
    @GetMapping("/type/count")
    public ResponseData getCountType() {
        List<CommonType> list = new ArrayList<>();
        FacilitiesCountTypeEnum[] values = FacilitiesCountTypeEnum.values();
        for (FacilitiesCountTypeEnum value : values) {
            CommonType type = new CommonType();
            type.setCode(value.getCode());
            type.setName(value.getName());
            list.add(type);
        }
        return success(list);
    }

    /**
     * 获取工位下的负责人
     *
     * @param fid
     * @return
     */
    @GetMapping("/director/{fid}")
    public ResponseData getFacDirector(@PathVariable Integer fid) {
        return success(facilitiesService.getFacDirector(fid));
    }

    /**
     * 获取工位下的人员信息（负责人以及绑定的员工）
     *
     * @param fid
     * @return
     */
    @GetMapping("/personnel/{fid}")
    public ResponseData getFacPersonnel(@PathVariable Integer fid) {
        return success(facilitiesService.getFacPersonnel(fid));
    }

    /**
     * 查询工位下某个工单的投入量、损耗量、待投量
     *
     * @param
     * @return
     */
    @GetMapping("/work/order/count")
    public ResponseData getWorkOrderCountByFac(@RequestParam(value = "workOrderNum") String workOrderNum,
                                               @RequestParam(value = "fid") Integer fid) {
        return success(facilitiesService.getFacCountByWorkOrder(workOrderNum, fid));
    }

    /**
     * 获取绑定了工位投入量指标的工位实例列表
     *
     * @param
     * @return
     */
    @GetMapping("/bind/counter/list")
    public ResponseData getBindCounterFacList() {
        return success(facilitiesService.getBindCounterFacList());
    }

    /**
     * 上报工位报损(默认为1)
     *
     * @param
     * @return
     */
    @GetMapping("/report/loss")
    public ResponseData reportFacLoss(@RequestParam(value = "workOrderNum") String workOrderNum,
                                      @RequestParam(value = "fid") Integer fid,
                                      @RequestParam(value = "newAddQuantity", defaultValue = "1", required = false) Double newAddQuantity) {
        return success(facilitiesService.reportFacLoss(workOrderNum, fid, newAddQuantity));
    }

    /**
     * 获取工位报损数
     *
     * @param
     * @return
     */
    @GetMapping("/get/loss")
    public ResponseData getFacLoss(@RequestParam(value = "workOrderNum") String workOrderNum,
                                   @RequestParam(value = "fid") Integer fid) {
        return success(facilitiesService.getFacLoss(workOrderNum, fid));
    }

    /**
     * 下载用户信息默认导入模板
     */
    @GetMapping("/down/default/template")
    public void downUserDefaultTemplate(HttpServletResponse response) throws Exception {
        ResourcePatternResolver resolver = new PathMatchingResourcePatternResolver();
        Resource resource = resolver.getResource("classpath:template/facilityTemplate.xlsx");
        String templateName = URLEncoder.encode("工位数据导入模板" + Constant.XLSX, StandardCharsets.UTF_8.name());
        ExcelTemplateImportUtil.responseToClient(response, resource.getInputStream(), templateName);
    }


    /**
     * 工位数据模板批量导入
     *
     * @param file
     * @return
     * @throws Exception
     */
    @PostMapping("/batch/import")
    public ResponseData importFacilitiesData(MultipartFile file) throws Exception {
        //1、判断导入文件的合法性
        ExcelUtil.checkImportExcelFile(file);
        //2、初始化处理进度
        ImportProgressDTO build = ImportProgressDTO.builder()
                .progress(Double.valueOf(Constant.ZERO_VALUE))
                .executionDescription("正在处理中，请耐心等候...")
                .executionStatus(false)
                .build();
        redisTemplate.opsForValue().set(RedisKeyPrefix.FACILITY_IMPORT_PROGRESS, JSONObject.toJSONString(build), 1, TimeUnit.HOURS);
        //4、加锁
        Boolean lockStatus = redisTemplate.opsForValue().setIfAbsent(RedisKeyPrefix.FACILITY_IMPORT_LOCK, new Date(), 1, TimeUnit.HOURS);
        if (lockStatus == null || !lockStatus) {
            log.info("获取redis同步锁失败");
            return fail(RespCodeEnum.IMPORT_OPREATION_NOT_RE);
        }
        //5、异步数据导入
        facilitiesService.importFacilitiesData(file.getInputStream(), getUsername(), getUserInfo().getCompId());
        return ResponseData.success();
    }


    /**
     * 查询导入进度
     *
     * @return
     */
    @GetMapping("/import/progress")
    public ResponseData getImportProgress() {
        Object obj = redisTemplate.opsForValue().get(RedisKeyPrefix.FACILITY_IMPORT_PROGRESS);
        return success(obj);
    }

    /**
     * 查询控制校验级别
     *
     * @return
     */
    @GetMapping("/network/status")
    public ResponseData getNetworkStatus(@RequestParam(value = "id") Integer id, @RequestParam(value = "type") String type) {
        return success(facilitiesService.getNetworkStatus(id, type));
    }

    /**
     * 查询控制校验级别枚举列表
     *
     * @return
     */
    @GetMapping("/network/status/enum/list")
    public ResponseData getNetworkStatusEnumList() {
        List<CommonState> list = new ArrayList<>();
        NetworkStatusEnum[] values = NetworkStatusEnum.values();
        for (NetworkStatusEnum value : values) {
            CommonState type = new CommonState();
            type.setCode(value.getCode());
            type.setName(value.getName());
            list.add(type);
        }
        return success(list);
    }

}

