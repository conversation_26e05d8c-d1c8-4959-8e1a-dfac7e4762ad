package com.yelink.dfs.controller.defect;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yelink.dfs.common.BaseController;
import com.yelink.dfs.constant.Constant;
import com.yelink.dfs.constant.ImportTypeEnum;
import com.yelink.dfs.constant.RedisKeyPrefix;
import com.yelink.dfs.constant.defect.DealEnum;
import com.yelink.dfs.constant.defect.DefectDefineStateEnum;
import com.yelink.dfs.constant.defect.DefectRelatedTypeEnum;
import com.yelink.dfs.constant.defect.DefectSchemeStatusEnum;
import com.yelink.dfs.constant.defect.DefectSchemeTypeEnum;
import com.yelink.dfs.constant.defect.DefectTypeDictEnum;
import com.yelink.dfs.entity.common.CommonType;
import com.yelink.dfs.entity.common.ModelUploadFileEntity;
import com.yelink.dfs.entity.defect.DefectDefineEntity;
import com.yelink.dfs.entity.defect.DefectSchemeEntity;
import com.yelink.dfs.entity.defect.dto.DefectAppointNoticeDTO;
import com.yelink.dfs.entity.defect.dto.DefectRecordQueryDTO;
import com.yelink.dfs.entity.defect.dto.DefineBatchUpdateDTO;
import com.yelink.dfs.entity.defect.dto.FacDTO;
import com.yelink.dfs.entity.defect.dto.StateEnumDTO;
import com.yelink.dfs.entity.defect.dto.StatementRecordExcelDTO;
import com.yelink.dfs.entity.defect.dto.TypeEnumDTO;
import com.yelink.dfs.entity.order.RecordWorkOrderCountEntity;
import com.yelink.dfs.entity.order.RecordWorkOrderUnqualifiedEntity;
import com.yelink.dfs.entity.order.vo.DefectCountVO;
import com.yelink.dfs.open.v1.defect.dto.DefectSchemeSelectDTO;
import com.yelink.dfs.provider.FastDfsClientService;
import com.yelink.dfs.service.common.ImportDataRecordService;
import com.yelink.dfs.service.common.ImportProgressService;
import com.yelink.dfs.service.common.ModelUploadFileService;
import com.yelink.dfs.service.common.config.BusinessConfigValueService;
import com.yelink.dfs.service.defect.BaseTypeService;
import com.yelink.dfs.service.defect.DefectDefineService;
import com.yelink.dfs.service.defect.DefectSchemeService;
import com.yelink.dfs.service.order.RecordWorkOrderCountService;
import com.yelink.dfs.service.order.RecordWorkOrderUnqualifiedService;
import com.yelink.dfs.utils.ExcelTemplateExportUtils;
import com.yelink.dfscommon.constant.dfs.config.ConfigConstant;
import com.yelink.dfscommon.constant.dfs.defect.DefectEnum;
import com.yelink.dfscommon.pojo.ResponseData;
import com.yelink.dfscommon.utils.ExcelTemplateImportUtil;
import com.yelink.dfscommon.utils.ExcelUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 设备日历
 * @Date 2021/05/24
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/defect")
public class DefectController extends BaseController {

    private BaseTypeService baseTypeService;
    private DefectDefineService defineService;
    private DefectSchemeService schemeService;
    private RecordWorkOrderUnqualifiedService unqualifiedService;
    private RecordWorkOrderCountService workOrderCountService;
    private FastDfsClientService fastDfsClientService;
    private ModelUploadFileService modelUploadFileService;
    private BusinessConfigValueService businessConfigValueService;
    private ImportProgressService importProgressService;
    private ImportDataRecordService importDataRecordService;


    /**
     * #####################不良定义#####################
     * 获取不良定义列表
     *
     * @param current
     * @param size
     * @return
     */
    @GetMapping("/define/list")
    public ResponseData getDefineList(@RequestParam(value = "defectName", required = false) String defectName,
                                      @RequestParam(value = "defectType", required = false) String defectType,
                                      @RequestParam(value = "start", required = false) String start,
                                      @RequestParam(value = "end", required = false) String end,
                                      @RequestParam(value = "current", required = false) Integer current,
                                      @RequestParam(value = "size", required = false) Integer size,
                                      @RequestParam(value = "states", required = false) String states,
                                      @RequestParam(value = "description", required = false) String description,
                                      @RequestParam(value = "maintainCodes", required = false) String maintainCodes,
                                      @RequestParam(value = "maintainNames", required = false) String maintainNames) {
        Page<DefectDefineEntity> page = defineService.getList(defectName, defectType, start, end, current, size, states, description, maintainCodes, maintainNames);
        return success(page);
    }

    /**
     * 不良定义导入：默认模板下载
     */
    @GetMapping("/define/export/default/template")
    public void downloadInspectDefaultTemplate(HttpServletResponse response) throws IOException {
        //找到导出的模板
        byte[] bytes = ExcelUtil.exportDefaultExcel("classpath:template/defectDefineImportTemplate.xlsx");
        ExcelTemplateImportUtil.responseToClient(response, bytes, "不良定义默认导入模板" + Constant.XLSX);
    }
    /**
     * 不良定义导入：导入
     */
    @PostMapping("/define/excel/import")
    public ResponseData importInspectExcel(MultipartFile file) throws IOException {
        //1、判断导入文件的合法性
        ExcelUtil.checkImportExcelFile(file);
        defineService.sycImportData(file.getOriginalFilename(), file.getInputStream(), getUsername());
        return success();
    }
    /**
     * 不良定义导入：导入进度
     */
    @GetMapping("/define/import/progress")
    public ResponseData getImportInspectExcelProgress() {
        return success(importProgressService.importProgress(RedisKeyPrefix.DEFECT_DEFINE_IMPORT_PROGRESS));
    }
    /**
     * 不良定义导入：查看日志
     */
    @RequestMapping("/define/import/record")
    public ResponseData recordList(@RequestParam(required = false) String fileName,
                                   @RequestParam(required = false) String startTime,
                                   @RequestParam(required = false) String endTime,
                                   @RequestParam(required = false, defaultValue = "1") Integer current,
                                   @RequestParam(required = false, defaultValue = "10") Integer size) {
        return success(importDataRecordService.getList(ImportTypeEnum.DEFECT_DEFINE_IMPORT.getType(), fileName, startTime, endTime, current, size));
    }

    /**
     * 不良定义导出
     *
     * @return
     */
    @GetMapping("/define/export")
    public void exportDefineList(@RequestParam(value = "defectName", required = false) String defectName,
                                 @RequestParam(value = "defectType", required = false) String defectType,
                                 @RequestParam(value = "start", required = false) String start,
                                 @RequestParam(value = "end", required = false) String end,
                                 HttpServletResponse response) throws IOException {
        defineService.exportDefineList(defectName, defectType, start, end, response);
    }

    /**
     * 获取生效的不良定义列表
     *
     * @return
     */
    @GetMapping("/released/define/list")
    public ResponseData getReleasedDefineList(@RequestParam(value = "defectCode", required = false) String defectCode,
                                              @RequestParam(value = "defectName", required = false) String defectName) {
        List<DefectDefineEntity> list = defineService.getReleasedList(defectCode, defectName);
        return success(list);
    }

    /**
     * 获取不良定义详情
     *
     * @param defectId
     * @return
     */
    @GetMapping("/define/detail/{defectId}")
    public ResponseData getDefineDetail(@PathVariable Integer defectId) {
        DefectDefineEntity entity = defineService.getDetailById(defectId);
        return success(entity);
    }

    /**
     * 获取不良定义详情绑定的质检信息(Pad)
     *
     * @param defectId
     * @return
     */
    @GetMapping("/define/maintain/pad/detail/{defectId}")
    public ResponseData getDefineMaintainPadDetail(@PathVariable Integer defectId) {
        return success(defineService.getMaintainSchemePad(defectId));
    }

    /**
     * 获取不良类型
     *
     * @return
     */
    @GetMapping("/define/type/list")
    public ResponseData getDefineTypes(@RequestParam(value = "name", required = false) String name) {
        List<TypeEnumDTO> list = baseTypeService.getTypeList(DefectTypeDictEnum.DEFECT_TYPE.getCode(), name);
        return success(list);
    }

    /**
     * 添加不良类型
     *
     * @return
     */
    @PostMapping("/define/type/add")
    public ResponseData addDefineType(@RequestBody TypeEnumDTO dto) {
        String username = getUsername();
        baseTypeService.addType(DefectTypeDictEnum.DEFECT_TYPE.getCode(), dto, username);
        return success();
    }

    /**
     * 具体不良推送
     *
     * @return
     */
    @PostMapping("/appoint/notice")
    public ResponseData defectAppointNotice(@RequestBody DefectAppointNoticeDTO dto) {
        defineService.defectAppointNotice(dto);
        return success();
    }

    /**
     * 修改不良类型
     *
     * @return
     */
    @PutMapping("/define/type/update")
    public ResponseData updateDefineType(@RequestBody TypeEnumDTO dto) {
        String username = getUsername();
        baseTypeService.updateType(DefectTypeDictEnum.DEFECT_TYPE.getCode(), dto, username);
        return success();
    }

    /**
     * 删除不良类型
     *
     * @return
     */
    @DeleteMapping("/define/type/remove/{code}")
    public ResponseData removeDefineType(@PathVariable String code) {
        defineService.removeDefectTypeByCode(code);
        return success();
    }

    /**
     * 获取不良定义状态
     *
     * @return
     */
    @GetMapping("/define/state/list")
    public ResponseData getDefineStates() {
        DefectDefineStateEnum[] values = DefectDefineStateEnum.values();
        ArrayList<StateEnumDTO> list = new ArrayList<>(values.length);
        for (DefectDefineStateEnum typeEnum : values) {
            list.add(StateEnumDTO.builder().code(typeEnum.getCode()).name(typeEnum.getName()).build());
        }
        return success(list);
    }

    /**
     * 获取处理状态
     *
     * @return
     */
    @GetMapping("/deal/state/list")
    public ResponseData getDealStateList() {
        DealEnum[] values = DealEnum.values();
        ArrayList<StateEnumDTO> list = new ArrayList<>(values.length);
        for (DealEnum typeEnum : values) {
            list.add(StateEnumDTO.builder().code(typeEnum.getCode()).name(typeEnum.getName()).build());
        }
        return success(list);
    }

    /**
     * 涉及缺陷枚举列表
     */
    @GetMapping("/involve/enum/list")
    public ResponseData listInvolveDefect() {
        DefectEnum[] values = DefectEnum.values();
        ArrayList<CommonType> list = new ArrayList<>(values.length);
        for (DefectEnum typeEnum : values) {
            list.add(CommonType.builder().code(typeEnum.getCode()).name(typeEnum.getName()).build());
        }
        return success(list);
    }

    /**
     * 添加不良定义
     *
     * @param entity
     * @return
     */
    @PostMapping("/add/define")
    public ResponseData addDefine(@RequestBody DefectDefineEntity entity) {
        entity.setCreateBy(getUsername());
        entity.setCreateTime(new Date());
        defineService.addDefine(entity);
        return success();
    }

    /**
     * 保存并生效不良定义
     *
     * @param entity
     * @return
     */
    @PostMapping("/add/released/define")
    public ResponseData addReleasedDefine(@RequestBody DefectDefineEntity entity) {
        String username = getUsername();
        entity.setCreateBy(username);
        entity.setUpdateBy(username);
        entity.setCreateTime(new Date());
        entity.setUpdateTime(new Date());
        defineService.addReleasedDefine(entity);
        return ResponseData.success();
    }

    /**
     * 修改不良定义
     *
     * @param entity
     * @return
     */
    @PutMapping("/update/define")
    public ResponseData updateDefine(@RequestBody DefectDefineEntity entity) {
        entity.setUpdateBy(getUsername());
        entity.setUpdateTime(new Date());
        defineService.updateDefine(entity);
        return success();
    }

    /**
     * 删除不良定义详情
     *
     * @param defectId
     * @return
     */
    @DeleteMapping("/remove/define/{defectId}")
    public ResponseData removeDefine(@PathVariable Integer defectId) {
        defineService.removeDefineById(defectId);
        return success();
    }

    /**
     * 批量编辑不良定义
     *
     * @param
     * @return
     */
    @PutMapping("/batch/update/define")
    public ResponseData updateDefine(@RequestBody DefineBatchUpdateDTO batchUpdateDTO) {
        batchUpdateDTO.setUsername(getUsername());
        defineService.batchUpdateDefine(batchUpdateDTO);
        return success();
    }


    /**
     * #####################质检方案#####################
     * 获取质检方案列表
     *
     * @param current
     * @param size
     * @return
     */
    @GetMapping("/scheme/list")
    public ResponseData getSchemeList(@RequestParam(value = "schemeName", required = false) String schemeName,
                                      @RequestParam(value = "schemeType", required = false) String schemeType,
                                      @RequestParam(value = "status", required = false) String status,
                                      @RequestParam(value = "current", required = false) Integer current,
                                      @RequestParam(value = "size", required = false) Integer size,
                                      @RequestParam(value = "type", required = false) String type) {
        DefectSchemeSelectDTO selectDTO = DefectSchemeSelectDTO.builder()
                .schemeName(schemeName)
                .schemeType(schemeType)
                .status(status)
                .current(current)
                .size(size)
                .type(type)
                .build();
        Page<DefectSchemeEntity> page = schemeService.getList(selectDTO);
        return success(page);
    }

    /**
     * 获取质检方案详情
     *
     * @param schemeId
     * @return
     */
    @GetMapping("/scheme/detail/{schemeId}")
    public ResponseData getSchemeDetail(@PathVariable Integer schemeId) {
        DefectSchemeEntity entity = schemeService.getSchemeDetail(schemeId);
        return success(entity);
    }

    /**
     * 获取质检类型
     *
     * @return
     */
    @GetMapping("/scheme/type/list")
    public ResponseData getSchemeTypes() {
        DefectSchemeTypeEnum[] values = DefectSchemeTypeEnum.values();
        ArrayList<TypeEnumDTO> list = new ArrayList<>(values.length);
        for (DefectSchemeTypeEnum typeEnum : values) {
            list.add(TypeEnumDTO.builder().code(typeEnum.getCode()).name(typeEnum.getName()).build());
        }
        return success(list);
    }

    /**
     * 获取质检方案状态
     *
     * @return
     */
    @GetMapping("/scheme/state/list")
    public ResponseData getSchemeStates() {
        DefectSchemeStatusEnum[] values = DefectSchemeStatusEnum.values();
        ArrayList<StateEnumDTO> list = new ArrayList<>(values.length);
        for (DefectSchemeStatusEnum typeEnum : values) {
            list.add(StateEnumDTO.builder().code(typeEnum.getCode()).name(typeEnum.getName()).build());
        }
        return success(list);
    }

    /**
     * 获取所有生效的方案
     *
     * @param type 关联的生产基本单元类型
     * @return
     */
    @GetMapping("/scheme/released/list")
    public ResponseData getReleasedSchemeList(@RequestParam(value = "type") String type) {
        List<DefectSchemeEntity> list = schemeService.getReleasedList(type);
        return success(list);
    }

    /**
     * 获取质检关联类型
     *
     * @return
     */
    @GetMapping("/related/type/list")
    public ResponseData getRelatedTypes() {
        DefectRelatedTypeEnum[] values = DefectRelatedTypeEnum.values();
        ArrayList<TypeEnumDTO> list = new ArrayList<>(values.length);
        for (DefectRelatedTypeEnum typeEnum : values) {
            list.add(TypeEnumDTO.builder().code(typeEnum.getCode()).name(typeEnum.getName()).build());
        }
        return success(list);
    }


    /**
     * 添加质检方案
     *
     * @param entity
     * @return
     */
    @PostMapping("/add/scheme")
    public ResponseData addScheme(@RequestBody DefectSchemeEntity entity) {
        entity.setCreateBy(getUsername());
        entity.setCreateTime(new Date());
        schemeService.addScheme(entity);
        return success();
    }

    /**
     * 修改质检方案
     *
     * @param entity
     * @return
     */
    @PostMapping("/update/scheme")
    public ResponseData updateScheme(@RequestBody DefectSchemeEntity entity) {
        entity.setUpdateBy(getUsername());
        entity.setUpdateTime(new Date());
        schemeService.updateScheme(entity);
        return success();
    }

    /**
     * 删除质检方案
     *
     * @param schemeId
     * @return
     */
    @DeleteMapping("/remove/scheme/{schemeId}")
    public ResponseData removeScheme(@PathVariable Integer schemeId) {
        schemeService.removeSchemeById(schemeId);
        return success();
    }

    /**
     * 查询质检方案关联类型下的生产基本单元列表
     *
     * @param type 质检方案的关联类型
     * @return
     */
    @GetMapping("/product_basic_unit/list")
    public ResponseData getProductBasicUnitListByType(@RequestParam(value = "type") String type) {
        return success(schemeService.getProductBasicUnitListByType(type));
    }

    /**
     * #####################质检记录#####################
     * 获取质检记录列表
     *
     * @param
     * @param
     * @return
     */
    @GetMapping("/record/list")
    public ResponseData getRecordList(DefectRecordQueryDTO queryDTO) {
        Page<RecordWorkOrderUnqualifiedEntity> page = unqualifiedService.getList(queryDTO);
        return success(page);
    }

    /**
     * 质量管理-质量报表-工位质检明细表
     */
    @PostMapping("/statement/record/list")
    public ResponseData statementRecordList(@RequestBody DefectRecordQueryDTO queryDTO) {
        Page<RecordWorkOrderUnqualifiedEntity> page = unqualifiedService.statementRecordList(queryDTO);
        return success(page);
    }

    /**
     * 质量管理-质量报表-工位质检明细表
     */
    @GetMapping("/statement/record/default/export/template")
    public void statementRecordDefaultExportTemplate(HttpServletResponse response) {
        String fileName = "工位质检明细默认导出模板" + Constant.XLSX;
        ExcelTemplateExportUtils.downloadDefaultExportTemplate(response, fileName, StatementRecordExcelDTO.class);
    }

    /**
     * 质量管理-质量报表-工位质检明细表
     */
    @PostMapping("/statement/record/excel/exports")
    public void statementListCodeRecord(@RequestBody DefectRecordQueryDTO queryDTO,
                                        @RequestParam(required = false) Integer templateId,
                                        HttpServletResponse response) {
        Page<RecordWorkOrderUnqualifiedEntity> page = unqualifiedService.statementRecordList(queryDTO);
        List<RecordWorkOrderUnqualifiedEntity> records = page.getRecords();
        String jsonString = JSONObject.toJSONString(records);
        List<StatementRecordExcelDTO> dtos = JSONArray.parseArray(jsonString, StatementRecordExcelDTO.class);
        if (Objects.isNull(templateId)) {
            // 按默认模板下载
            ExcelTemplateExportUtils.downloadDefaultExportData(response, "工位质检明细" + Constant.XLSX, StatementRecordExcelDTO.class, dtos);
        } else {
            // 按模板下载
            ModelUploadFileEntity uploadFile = modelUploadFileService.getById(templateId);
            byte[] bytes = fastDfsClientService.getFileStream(uploadFile.getFileAddress());
            ExcelTemplateExportUtils.downloadExportData(response, new ByteArrayInputStream(bytes), uploadFile.getFileName(), StatementRecordExcelDTO.class, dtos);
        }
    }

    /**
     * 下载内置模板
     *
     * @return
     */
    @GetMapping("/record/default/template/export")
    public void exportRecordTemplate(HttpServletResponse response) {
        unqualifiedService.exportRecordTemplate(response);
    }

    /**
     * 用户上传质检记录模板
     *
     * @return
     */
    @PostMapping("/record/upload/template")
    public ResponseData uploadRecordTemplate(MultipartFile file) {
        String username = getUsername();
        unqualifiedService.uploadRecordTemplate(file, username);
        return success();
    }

    /**
     * 质检记录导出
     *
     * @return
     */
    @GetMapping("/record/export")
    public void exportRecordList(DefectRecordQueryDTO queryDTO, HttpServletResponse response) {
        queryDTO.setCurrent(1);
        queryDTO.setSize(Integer.MAX_VALUE);
        unqualifiedService.exportRecordList(queryDTO, response);
    }

    /**
     * 获取质检记录详情
     *
     * @param recordId
     * @return
     */
    @GetMapping("/record/detail/{recordId}")
    public ResponseData getRecordDetail(@PathVariable Integer recordId) {
        RecordWorkOrderUnqualifiedEntity entity = unqualifiedService.getDetail(recordId);
        return success(entity);
    }

    /**
     * 获取质检工位
     *
     * @return
     */
    @GetMapping("/record/fac")
    public ResponseData getFacilities() {
        ArrayList<FacDTO> list = unqualifiedService.getFacilities();
        return success(list);
    }

    /**
     * #####################质检报表#####################
     * 获取工单列表
     *
     * @return
     */
    @GetMapping("/work/order")
    public ResponseData getWorkOrders() {
        List<String> list = unqualifiedService.getWorkOrders();
        return success(list);
    }

    /**
     * 获取报表
     *
     * @return
     */
    @GetMapping("/statement/list")
    public ResponseData getStatement(@RequestParam(value = "workOrder", required = false) String workOrder,
                                     @RequestParam(value = "materialCode", required = false) String materialCode,
                                     @RequestParam(value = "factoryModel", required = false) String factoryModel,
                                     @RequestParam(value = "lineId", required = false) Integer lineId,
                                     @RequestParam(value = "start", required = false) String start,
                                     @RequestParam(value = "end", required = false) String end,
                                     @RequestParam(value = "current", required = false) Integer current,
                                     @RequestParam(value = "size", required = false) Integer size) {
        Page<RecordWorkOrderCountEntity> page = workOrderCountService.getQualityAnalysis(workOrder, materialCode, factoryModel,
                lineId, start, end, current, size);
        return success(page);
    }

    /**
     * 获取报表详情
     *
     * @return
     */
    @GetMapping("/statement/detail/{id}")
    public ResponseData getStatementDetail(@PathVariable Integer id) {
        RecordWorkOrderCountEntity entity = workOrderCountService.getCountDetail(id);
        return success(entity);
    }

    /**
     * 质检报表导出
     *
     * @return
     */
    @GetMapping("/statement/export")
    public void exportStatementList(@RequestParam(value = "workOrder", required = false) String workOrder,
                                    @RequestParam(value = "materialCode", required = false) String materialCode,
                                    @RequestParam(value = "factoryModel", required = false) String factoryModel,
                                    @RequestParam(value = "lineId", required = false) Integer lineId,
                                    @RequestParam(value = "start", required = false) String start,
                                    @RequestParam(value = "end", required = false) String end,
                                    HttpServletResponse response) throws IOException {
        workOrderCountService.export(workOrder, materialCode, factoryModel, lineId, start, end, response);
    }

    /**
     * 下载内置模板
     *
     * @return
     */
    @GetMapping("/statement/default/template/export")
    public void exportStatementTemplate(HttpServletResponse response) throws IOException {
        workOrderCountService.exportStatementTemplate(response);
    }

    /**
     * 用户上传质检报表模板
     *
     * @return
     */
    @PostMapping("/statement/upload/template")
    public ResponseData uploadStatementTemplate(MultipartFile file) {
        String username = getUsername();
        workOrderCountService.uploadStatementTemplate(file, username);
        return success();
    }

    /**
     * 质检小程序配置：质检提交是否支持批量上报数量
     *
     * @return
     */
    @GetMapping("/get/defect/config")
    public ResponseData getMaintainGridProportion() {
        return success(businessConfigValueService.getValue(ConfigConstant.QUALITY_INSPECTION_CONFIG_BATCH));
    }

    /**
     * 工序质检单品/工单、工位质检单品/工单、质检工位机获取是否允许变更工单状态
     *
     * @return
     */
    @GetMapping("/get/status/config")
    public ResponseData getChangeWorkOrderStatus(@RequestParam(value = "code") String code) {
        String value = businessConfigValueService.getValue(ConfigConstant.QUALITY_CHANGE_WORK_ORDER_STATUS_ENABLE);
        if (Constant.TRUES.equals(value)) {
            String value1 = businessConfigValueService.getValue(ConfigConstant.QUALITY_CHANGE_WORK_ORDER_STATUS_APPLETS);
            if (!Constant.MIDDLE_BRACKET.equals(value1)) {
                String substring = value1.substring(1, value1.length() - 1);
                List<String> collect = Arrays.stream(substring.split(Constant.SEP)).map(String::valueOf).collect(Collectors.toList());
                code = "\"" + code + "\"";
                if (collect.contains(code)) {
                    return ResponseData.success();
                }
            }
        }
        return ResponseData.fail("未开启生效状态变更工单状态");
    }

    /**
     * 工序质检单品/工单、工位质检单品/工单、质检工位机获取是否允许上报报工记录
     *
     * @return
     */
    @GetMapping("/get/report/config")
    public ResponseData getReportConfig(@RequestParam(value = "code") String code) {
        String value = businessConfigValueService.getValue(ConfigConstant.QUALITY_CHANGE_WORK_ORDER_NUMBERS_ENABLE);
        if (Constant.TRUES.equals(value)) {
            String value1 = businessConfigValueService.getValue(ConfigConstant.QUALITY_CHANGE_WORK_ORDER_NUMBERS_APPLETS);
            if (!Constant.MIDDLE_BRACKET.equals(value1)) {
                String substring = value1.substring(1, value1.length() - 1);
                List<String> collect = Arrays.stream(substring.split(Constant.SEP)).map(String::valueOf).collect(Collectors.toList());
                code = "\"" + code + "\"";
                if (collect.contains(code)) {
                    return ResponseData.success();
                }
            }
        }
        return ResponseData.fail();
    }

    /**
     * 获取工单的质检记录总数
     *
     * @return
     */
    @GetMapping("/get/work-order/defect-count")
    public ResponseData getWorkOrderDefectCount(@RequestParam String workOrderNumber) {
        List<DefectCountVO> result = unqualifiedService.getWorkOrderDefectCount(workOrderNumber);
        return success(result);
    }
}
