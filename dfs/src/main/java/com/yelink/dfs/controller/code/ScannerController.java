package com.yelink.dfs.controller.code;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yelink.dfs.common.BaseController;
import com.yelink.dfs.constant.RedisKeyPrefix;
import com.yelink.dfs.constant.product.MaterialMatchingEnum;
import com.yelink.dfs.entity.code.ProductFlowCodeEntity;
import com.yelink.dfs.entity.code.ProductFlowCodeRecordEntity;
import com.yelink.dfs.entity.code.dto.CodeRelevanceViewSelectDTO;
import com.yelink.dfs.entity.code.dto.MaintainSubmitDTO;
import com.yelink.dfs.entity.code.dto.ScannerDTO;
import com.yelink.dfs.entity.code.dto.ScannerReportDTO;
import com.yelink.dfs.entity.order.BomTileEntity;
import com.yelink.dfs.entity.order.WorkOrderEntity;
import com.yelink.dfs.entity.product.MaterialEntity;
import com.yelink.dfs.entity.product.ProcedureControllerConfigEntity;
import com.yelink.dfs.entity.product.dto.AddRecordListDTO;
import com.yelink.dfs.mapper.order.BomTileMapper;
import com.yelink.dfs.mapper.order.WorkOrderMapper;
import com.yelink.dfs.service.code.LogScanner;
import com.yelink.dfs.service.code.ProductFlowCodeService;
import com.yelink.dfs.service.code.ScannerService;
import com.yelink.dfs.service.feed.FeedRecordService;
import com.yelink.dfs.service.product.ProcedureControllerConfigService;
import com.yelink.dfscommon.constant.ResponseException;
import com.yelink.dfscommon.constant.dfs.code.ProductFlowCodeRecordStateEnum;
import com.yelink.dfscommon.constant.dfs.code.ProductFlowCodeTypeEnum;
import com.yelink.dfscommon.constant.dfs.order.WorkOrderStateEnum;
import com.yelink.dfscommon.dto.dfs.MaintainBatchDTO;
import com.yelink.dfscommon.entity.CommonType;
import com.yelink.dfscommon.entity.dfs.productFlowCode.AddFlowCodeDTO;
import com.yelink.dfscommon.entity.dfs.productFlowCode.GeneralSubmitDTO;
import com.yelink.dfscommon.entity.dfs.productFlowCode.ProcedureInspectionConfigEntity;
import com.yelink.dfscommon.pojo.ResponseData;
import com.yelink.dfscommon.utils.DateUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @Description 生产流水码
 * @Date 2022/3/31 14:36
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/kjr/location/machine")
public class ScannerController extends BaseController {

    private ScannerService scannerService;
    private ProductFlowCodeService productFlowCodeService;
    private RedisTemplate<String, Object> redisTemplate;
    private final ProcedureControllerConfigService procedureControllerConfigService;
    private WorkOrderMapper workOrderMapper;
    private BomTileMapper bomTileMapper;
    private FeedRecordService feedRecordService;
    /**
     * 一般工位机-扫码
     *
     * @param productFlowCodeEntity 生产流水号
     * @return
     */
    @PostMapping("/general/scanner")
    @LogScanner
    public ResponseData addProductFlowCode(@RequestBody ScannerDTO productFlowCodeEntity, @RequestParam Integer fid, @RequestParam(required = false) Integer craftProcedureId) {
        //分布式锁
        Boolean lockStatus = redisTemplate.opsForValue().setIfAbsent(RedisKeyPrefix.DFS_PRODUCT_FLOW_CODE + productFlowCodeEntity.getProductFlowCode() + fid + craftProcedureId, new Date(), 1, TimeUnit.MINUTES);
        if (lockStatus == null || !lockStatus) {
            throw new ResponseException("并发占用，稍后再试");
        }
        try {
            if (StringUtils.isBlank(productFlowCodeEntity.getProductFlowCode())) {
                throw new ResponseException("请输入流水码");
            }
            //默认自动添加条码
            if (productFlowCodeEntity.getAutoAddCode() == null) {
                productFlowCodeEntity.setAutoAddCode(true);
            }
            AddFlowCodeDTO addFlowCodeDTO = AddFlowCodeDTO.builder()
                    .productFlowCode(productFlowCodeEntity.getProductFlowCode())
                    .fid(fid)
                    .userName(getUsername())
                    .state(ProductFlowCodeRecordStateEnum.COMPLETE.getCode())
                    .craftProcedureId(craftProcedureId)
                    .workOrderNumber(productFlowCodeEntity.getRelationNumber())
                    .autoAddCode(productFlowCodeEntity.getAutoAddCode())
                    .isEndTime(true).build();

            return success(scannerService.addCodeRecordGeneral(addFlowCodeDTO));
        } finally {
            redisTemplate.delete(RedisKeyPrefix.DFS_PRODUCT_FLOW_CODE + productFlowCodeEntity.getProductFlowCode() + fid + craftProcedureId);
        }
    }

    /**
     * 一般工位机-扫码获取工单
     *
     * @param productFlowCodeEntity 生产流水号
     * @return
     */
    @PostMapping("/get/work/order/number")
    @LogScanner
    public ResponseData getWorkOrderNumber(@RequestBody ScannerDTO productFlowCodeEntity, @RequestParam Integer fid, @RequestParam(required = false) Integer craftProcedureId) {
        if (StringUtils.isBlank(productFlowCodeEntity.getProductFlowCode())) {
            throw new ResponseException("请输入流水码");
        }
        //默认自动添加条码
        if (productFlowCodeEntity.getAutoAddCode() == null) {
            productFlowCodeEntity.setAutoAddCode(true);
        }
        AddFlowCodeDTO addFlowCodeDTO = AddFlowCodeDTO.builder()
                .productFlowCode(productFlowCodeEntity.getProductFlowCode())
                .fid(fid)
                .userName(getUsername())
                .state(ProductFlowCodeRecordStateEnum.COMPLETE.getCode())
                .craftProcedureId(craftProcedureId)
                .workOrderNumber(productFlowCodeEntity.getRelationNumber())
                .autoAddCode(productFlowCodeEntity.getAutoAddCode())
                .isEndTime(true).build();

        return success(scannerService.getWorkOrderNumber(addFlowCodeDTO));
    }

    /**
     * 流水码检查
     *
     * @param generalSubmitDTO 生产流水号
     * @return
     */
    @PostMapping("/scanner/check")
    public ResponseData scannerCheck(@RequestBody GeneralSubmitDTO generalSubmitDTO) {
        //分布式锁
        Boolean lockStatus = redisTemplate.opsForValue().setIfAbsent(RedisKeyPrefix.DFS_PRODUCT_FLOW_CODE + generalSubmitDTO.getProductFlowCode() + generalSubmitDTO.getFid(), new Date(), 1, TimeUnit.MINUTES);
        if (lockStatus == null || !lockStatus) {
            throw new ResponseException("并发占用，稍后再试");
        }
        try {
            if (StringUtils.isBlank(generalSubmitDTO.getProductFlowCode())) {
                throw new ResponseException("请输入流水码");
            }
            AddFlowCodeDTO addFlowCodeDTO = AddFlowCodeDTO.builder()
                    .productFlowCode(generalSubmitDTO.getProductFlowCode())
                    .fid(generalSubmitDTO.getFid())
                    .workOrderNumber(generalSubmitDTO.getWorkOrderNumber())
                    .craftProcedureId(generalSubmitDTO.getCraftProcedureId())
                    .state(ProductFlowCodeRecordStateEnum.COMPLETE.getCode())
                    .isEndTime(true)
                    .build();
            return success(scannerService.checkAddCodeRecord(addFlowCodeDTO));
        }finally {
            redisTemplate.delete(RedisKeyPrefix.DFS_PRODUCT_FLOW_CODE + generalSubmitDTO.getProductFlowCode() + generalSubmitDTO.getFid());
        }
    }

    /**
     * 上料工位机 - 扫码物料检查
     */
    @PostMapping("/feed/scanner/check/material")
    public ResponseData feedScannerCheckMaterial(@RequestBody GeneralSubmitDTO generalSubmitDTO) {
        MaterialEntity materialEntity = feedRecordService.feedScannerCheckMaterial(generalSubmitDTO.getCheckType(),
                generalSubmitDTO.getMaterialCodes(), generalSubmitDTO.getWorkOrderNumber(), generalSubmitDTO.getCraftProcedureId());
        return ResponseData.success(materialEntity);
    }

    /**
     * 添加过站记录，只做追溯
     *
     * @param generalSubmitDTO 生产流水号
     * @return
     */
    @PostMapping("/only/record")
    @LogScanner
    public ResponseData addOnlyRecord(@RequestBody GeneralSubmitDTO generalSubmitDTO) {
        //分布式锁
        Boolean lockStatus = redisTemplate.opsForValue().setIfAbsent(RedisKeyPrefix.DFS_PRODUCT_FLOW_CODE + generalSubmitDTO.getProductFlowCode() + generalSubmitDTO.getFid(), new Date(), 1, TimeUnit.MINUTES);
        if (lockStatus == null || !lockStatus) {
            throw new ResponseException("并发占用，稍后再试");
        }
        try {
            if (StringUtils.isBlank(generalSubmitDTO.getProductFlowCode())) {
                throw new ResponseException("请输入流水码");
            }
            generalSubmitDTO.setUserName(getUsername());
            generalSubmitDTO.setIsCheck(false);
            return success(scannerService.addRecordAndSubmit(generalSubmitDTO));
        }finally {
            redisTemplate.delete(RedisKeyPrefix.DFS_PRODUCT_FLOW_CODE + generalSubmitDTO.getProductFlowCode() + generalSubmitDTO.getFid());
        }
    }

    /**
     * 扫码工位机提交
     *
     * @param generalSubmitDTO 生产流水号
     * @return
     */
    @PostMapping("/add/submit")
    @LogScanner
    public ResponseData addAndSubmit(@RequestBody GeneralSubmitDTO generalSubmitDTO) {
        //分布式锁
        Boolean lockStatus = redisTemplate.opsForValue().setIfAbsent(RedisKeyPrefix.DFS_PRODUCT_FLOW_CODE + generalSubmitDTO.getProductFlowCode() + generalSubmitDTO.getFid(), new Date(), 1, TimeUnit.MINUTES);
        if (lockStatus == null || !lockStatus) {
            throw new ResponseException("并发占用，稍后再试");
        }
        try {
            if (StringUtils.isBlank(generalSubmitDTO.getProductFlowCode())) {
                throw new ResponseException("请输入流水码");
            }
            generalSubmitDTO.setUserName(getUsername());
            generalSubmitDTO.setIsCheck(true);
            ProductFlowCodeEntity productFlowCodeEntity = productFlowCodeService.getByProductFlowCode(generalSubmitDTO.getProductFlowCode());
            if(productFlowCodeEntity==null){
                throw new ResponseException("系统找不到对应条码号");
            }
            if(ProductFlowCodeTypeEnum.PRODUCT_FLOW_CODE.getCode()!=productFlowCodeEntity.getType()){
                throw new ResponseException("只支持工单流水码");
            }
            generalSubmitDTO.setWorkOrderNumber(productFlowCodeEntity.getRelationNumber());
            return success(scannerService.addRecordAndSubmit(generalSubmitDTO));
        }finally {
            redisTemplate.delete(RedisKeyPrefix.DFS_PRODUCT_FLOW_CODE + generalSubmitDTO.getProductFlowCode() + generalSubmitDTO.getFid());
        }
    }

    /**
     * 一般工位机 - 扫码物料检查枚举
     */
    @GetMapping("/check/material/types")
    public ResponseData checkMaterialTypes() {
        List<CommonType> states = new ArrayList<>();
        for (MaterialMatchingEnum value : MaterialMatchingEnum.values()) {
            states.add(CommonType.builder().code(value.getType()).name(value.getName()).build());
        }
        return ResponseData.success(states);
    }

    /**
     * 一般工位机 - 扫码物料检查
     */
    @PostMapping("/general/scanner/check/material")
    public ResponseData scannerCheckMaterial(@RequestParam String checkType, @RequestParam String materialCode,
                                             @RequestParam String workOrderNumber,
                                             @RequestParam Integer craftProcedureId) {
        //拿到工艺工序控制配置信息
        ProcedureControllerConfigEntity procedureControllerConfigEntity = procedureControllerConfigService.getDetail(craftProcedureId);
        //是否开启物料检查
        boolean materialCheck = procedureControllerConfigEntity != null && procedureControllerConfigEntity.getMaterialCheck();
        feedRecordService.scannerCheckMaterial(materialCheck, checkType, materialCode, workOrderNumber, craftProcedureId);
        return ResponseData.success();
    }

    /**
     * 单品码绑工单工位机-扫码
     *
     * @param productFlowCodeEntity 生产流水号
     * @return
     */
    @PostMapping("/building/scanner")
    public ResponseData buildingAndAdd(@RequestBody ScannerDTO productFlowCodeEntity, @RequestParam Integer fid, @RequestParam Integer craftProcedureId) {
        //分布式锁
        Boolean lockStatus = redisTemplate.opsForValue().setIfAbsent(RedisKeyPrefix.DFS_PRODUCT_FLOW_CODE + productFlowCodeEntity.getProductFlowCode() + fid + craftProcedureId, new Date(), 1, TimeUnit.MINUTES);
        if (lockStatus == null || !lockStatus) {
            throw new ResponseException("当前方法已被占用，稍后再试");
        }
        try {
            if (StringUtils.isBlank(productFlowCodeEntity.getProductFlowCode())) {
                throw new ResponseException("请输入流水码");
            }
            if (StringUtils.isBlank(productFlowCodeEntity.getRelationNumber())) {
                throw new ResponseException("请输入工单号");
            }
            return success(scannerService.buildingAndAdd(productFlowCodeEntity.getProductFlowCode(), productFlowCodeEntity.getRelationNumber(), fid, craftProcedureId, getUsername()));
        } finally {
            redisTemplate.delete(RedisKeyPrefix.DFS_PRODUCT_FLOW_CODE + productFlowCodeEntity.getProductFlowCode() + fid + craftProcedureId);
        }
    }


    /**
     * 质检工位机-扫码
     *
     * @param productFlowCodeEntity 生产流水号
     * @return
     */
    @PostMapping("/quality/scanner")
    public ResponseData addProductFlowCodeQualityInspection(@RequestBody ScannerDTO
                                                                    productFlowCodeEntity, @RequestParam Integer fid, @RequestParam Integer craftProcedureId) {
        //分布式锁
        Boolean lockStatus = redisTemplate.opsForValue().setIfAbsent(RedisKeyPrefix.DFS_PRODUCT_FLOW_CODE + productFlowCodeEntity.getProductFlowCode() + fid + craftProcedureId, new Date(), 1, TimeUnit.MINUTES);
        if (lockStatus == null || !lockStatus) {
            throw new ResponseException("当前方法已被占用，稍后再试");
        }
        try {
            if (StringUtils.isBlank(productFlowCodeEntity.getProductFlowCode())) {
                throw new ResponseException("请输入流水码");
            }
            AddFlowCodeDTO addFlowCodeDTO = AddFlowCodeDTO.builder()
                    .productFlowCode(productFlowCodeEntity.getProductFlowCode())
                    .fid(fid)
                    .userName(getUsername())
                    .craftProcedureId(craftProcedureId)
                    .state(ProductFlowCodeRecordStateEnum.COMPLETE.getCode())
                    .isEndTime(true)
                    .autoAddCode(true)
                    .workOrderNumber(productFlowCodeEntity.getRelationNumber())
                    .isUnqualified(false).build();
            return success(scannerService.addCodeRecordQuality(addFlowCodeDTO));
        } finally {
            redisTemplate.delete(RedisKeyPrefix.DFS_PRODUCT_FLOW_CODE + productFlowCodeEntity.getProductFlowCode() + fid + craftProcedureId);
        }
    }

    /**
     * 一般工位机-提交
     *
     * @param generalSubmitDTO
     * @return
     */
    @PostMapping("/general/submit")
    public ResponseData generalSubmit(@RequestBody GeneralSubmitDTO generalSubmitDTO) {
        //分布式锁
        Boolean lockStatus = redisTemplate.opsForValue().setIfAbsent(RedisKeyPrefix.DFS_PRODUCT_FLOW_CODE + generalSubmitDTO.getCodeRecordId(), new Date(), 1, TimeUnit.MINUTES);
        if (lockStatus == null || !lockStatus) {
            throw new ResponseException("当前方法已被占用，稍后再试");
        }
        try {
            generalSubmitDTO.setUserName(this.getUsername());
            scannerService.generalSubmit(generalSubmitDTO);
            return success();
        } finally {
            redisTemplate.delete(RedisKeyPrefix.DFS_PRODUCT_FLOW_CODE + generalSubmitDTO.getCodeRecordId());
        }
    }

    /**
     * 获取工位绑定设备指标
     *
     * @param fid
     * @return
     */
    @GetMapping("/get/target")
    public ResponseData getTargetValue(@RequestParam Integer fid, @RequestParam Integer craftProcedureId) {
        return success(scannerService.listProcedureInspection(craftProcedureId, fid));
    }

    /**
     * 判断检测项是否合格
     *
     * @param procedureInspectionConfigEntity
     * @return
     */
    @PostMapping("/target/check")
    public ResponseData targetCheck(@RequestBody ProcedureInspectionConfigEntity procedureInspectionConfigEntity) {
        return success(scannerService.checkItem(procedureInspectionConfigEntity));
    }

    /**
     * 维修工位机-扫码
     *
     * @param maintainSubmitDTO 生产流水号
     * @return
     */
    @PostMapping("/maintain/scanner")
    public ResponseData scannerMaintain(@RequestBody MaintainSubmitDTO maintainSubmitDTO) {
        if (StringUtils.isBlank(maintainSubmitDTO.getProductFlowCode())) {
            throw new ResponseException("请输入条码");
        }
        return success(scannerService.scannerMaintain(maintainSubmitDTO.getProductFlowCode(),maintainSubmitDTO.getFid()));
    }

    /**
     * 维修工位机-提交
     *
     * @param maintainSubmitDTO
     * @return
     */
    @PostMapping("/maintain/submit")
    public ResponseData maintainSubmit(@RequestBody MaintainSubmitDTO maintainSubmitDTO) {
        //分布式锁
        Boolean lockStatus = redisTemplate.opsForValue().setIfAbsent(RedisKeyPrefix.DFS_PRODUCT_FLOW_CODE + maintainSubmitDTO.getProductFlowCode(), new Date(), 1, TimeUnit.MINUTES);
        if (lockStatus == null || !lockStatus) {
            throw new ResponseException("当前方法已被占用，稍后再试");
        }
        try {
            maintainSubmitDTO.setUserName(getUsername());
            scannerService.maintainSubmit(maintainSubmitDTO);
            return success();
        } finally {
            redisTemplate.delete(RedisKeyPrefix.DFS_PRODUCT_FLOW_CODE + maintainSubmitDTO.getProductFlowCode());
        }
    }

    /**
     * 老化工位机-扫码
     *
     * @param productFlowCodeEntity 生产流水号
     * @param fid                   工位号
     * @return
     */
    @PostMapping("/old/scanner")
    public ResponseData addProductFlowCodeOld(@RequestBody ScannerDTO
                                                      productFlowCodeEntity, @RequestParam Integer fid, @RequestParam Integer craftProcedureId) {
        //分布式锁
        Boolean lockStatus = redisTemplate.opsForValue().setIfAbsent(RedisKeyPrefix.DFS_PRODUCT_FLOW_CODE + productFlowCodeEntity.getProductFlowCode() + fid + craftProcedureId, new Date(), 1, TimeUnit.MINUTES);
        if (lockStatus == null || !lockStatus) {
            throw new ResponseException("当前方法已被占用，稍后再试");
        }
        try {
            if (StringUtils.isBlank(productFlowCodeEntity.getProductFlowCode())) {
                throw new ResponseException("请输入流水码");
            }
            AddFlowCodeDTO addFlowCodeDTO = AddFlowCodeDTO.builder().productFlowCode(productFlowCodeEntity.getProductFlowCode()).fid(fid)
                    .userName(getUsername()).state(ProductFlowCodeRecordStateEnum.CREAT.getCode()).craftProcedureId(craftProcedureId).isEndTime(false).build();
            return success(scannerService.addCodeRecordGeneral(addFlowCodeDTO));
        } finally {
            redisTemplate.delete(RedisKeyPrefix.DFS_PRODUCT_FLOW_CODE + productFlowCodeEntity.getProductFlowCode() + fid + craftProcedureId);
        }
    }


    /**
     * 包装工位机-提交
     *
     * @param productFlowCodeEntity 生产流水号
     * @param craftProcedureId      工序id
     * @param fid                   工位号
     * @return
     */
    @PostMapping("/packaging/scanner")
    public ResponseData addProductFlowCodePackaging(@RequestBody ScannerDTO productFlowCodeEntity,
                                                    @RequestParam(required = false) Integer craftProcedureId, @RequestParam(required = false) Integer fid) {

        //分布式锁
        Boolean lockStatus = redisTemplate.opsForValue().setIfAbsent(RedisKeyPrefix.DFS_PRODUCT_FLOW_CODE + productFlowCodeEntity.getProductFlowCode() + fid + craftProcedureId, new Date(), 1, TimeUnit.MINUTES);
        if (lockStatus == null || !lockStatus) {
            throw new ResponseException("当前方法已被占用，稍后再试");
        }
        try {
            if (StringUtils.isBlank(productFlowCodeEntity.getProductFlowCode())) {
                throw new ResponseException("请输入流水码");
            }
            if (StringUtils.isBlank(productFlowCodeEntity.getFinishedProductCode())) {
                throw new ResponseException("请输入成品码");
            }
            scannerService.addProductFlowCodePackaging(productFlowCodeEntity.getProductFlowCode(),productFlowCodeEntity.getFinishedProductCode(),fid,craftProcedureId,getUsername());
            return success();
        } finally {
            redisTemplate.delete(RedisKeyPrefix.DFS_PRODUCT_FLOW_CODE + productFlowCodeEntity.getProductFlowCode() + fid + craftProcedureId);
        }
    }


    /**
     * 获取老化工位机的老化列表
     *
     * @param fid
     * @return
     */
    @GetMapping("/get/old/list")
    public ResponseData getOldList(@RequestParam Integer fid) {
        return success(scannerService.getOldList(fid));
    }

    /**
     * 提交老化工位机的老化列表
     *
     * @param id
     * @return
     */
    @GetMapping("/submit/old/record")
    public ResponseData submitOldRecord(@RequestParam Integer id, @RequestParam String dateIn) {
        Date date = DateUtil.formatToDate(DateUtil.parse(dateIn, DateUtil.DATETIME_FORMAT), DateUtil.DATETIME_FORMAT);
        scannerService.submitOldRecord(id, date);
        return success();
    }

    /**
     * 获取老化检验工位机的老化列表
     *
     * @param productFlowCodeEntity 生产流水号
     * @param isAutoCommit          是否自动提交
     * @return
     */
    @PostMapping("/old/check/scanner")
    public ResponseData oldCheckScanner(@RequestBody ProductFlowCodeEntity productFlowCodeEntity, @RequestParam Boolean isAutoCommit) {
        if (StringUtils.isBlank(productFlowCodeEntity.getProductFlowCode())) {
            throw new ResponseException("请输入流水码");
        }
        return success(scannerService.listOldCheckRecord(productFlowCodeEntity.getProductFlowCode(), isAutoCommit));
    }

    /**
     * 老化检验工位机-提交
     *
     * @param generalSubmitDTO
     * @return
     */
    @PostMapping("/old/check/submit")
    public ResponseData oldCheckSubmit(@RequestBody GeneralSubmitDTO generalSubmitDTO) {
        //分布式锁
        Boolean lockStatus = redisTemplate.opsForValue().setIfAbsent(RedisKeyPrefix.DFS_PRODUCT_FLOW_CODE + generalSubmitDTO.getFid(), new Date(), 1, TimeUnit.MINUTES);
        if (lockStatus == null || !lockStatus) {
            throw new ResponseException("当前方法已被占用，稍后再试");
        }
        try {
            scannerService.oldCheckSubmit(generalSubmitDTO, getUsername());
            return success();
        } catch (Exception e) {
            throw e;
        } finally {
            redisTemplate.delete(RedisKeyPrefix.DFS_PRODUCT_FLOW_CODE +  generalSubmitDTO.getFid());
        }
    }

    /**
     * 维修工位获取维修可返回工序
     *
     * @param productFlowCodeEntity
     * @return
     */
    @PostMapping("/get/maintain/back/procedure")
    public ResponseData getMaintainBackProcedures(@RequestBody ScannerDTO productFlowCodeEntity) {
        return success(scannerService.getMaintainBackProcedures(productFlowCodeEntity.getProductFlowCode(), productFlowCodeEntity.getRelationNumber()));
    }

    /**
     * 获取工单最近10条绑定记录
     *
     * @param productFlowCodeEntity
     * @return
     */
    @PostMapping("/list/building/code")
    public ResponseData listBuildingCode(@RequestBody ScannerDTO productFlowCodeEntity) {
        if (StringUtils.isBlank(productFlowCodeEntity.getRelationNumber())) {
            throw new ResponseException("请输入工单号");
        }
        return success(scannerService.listProductFlowCodeByWorkOrder(productFlowCodeEntity.getRelationNumber()));
    }

    /**
     * 撤销工单绑定条码记录
     *
     * @param productFlowCodeEntity
     * @return
     */
    @PostMapping("/repeal/building/code")
    public ResponseData repealBuildingCode(@RequestBody ScannerDTO
                                                   productFlowCodeEntity, @RequestParam Integer fid) {
        if (StringUtils.isBlank(productFlowCodeEntity.getProductFlowCode())) {
            throw new ResponseException("请输入流水码号");
        }
        scannerService.repealBuildingCode(productFlowCodeEntity.getProductFlowCode(), fid, getUsername());
        return success();
    }

    /**
     * 根据物料获取正在投产工单(包括子项物料)
     *
     * @param productFlowCodeRecordEntity
     * @return
     */
    @PostMapping("/select/work/order/by/material")
    public ResponseData selectWorkOrderByMaterial(@RequestBody ProductFlowCodeRecordEntity productFlowCodeRecordEntity) {
        LambdaQueryWrapper<WorkOrderEntity> lambdaQueryWrapper = new LambdaQueryWrapper();
        lambdaQueryWrapper.eq(WorkOrderEntity::getState, WorkOrderStateEnum.INVESTMENT.getCode()).eq(WorkOrderEntity::getLineId, productFlowCodeRecordEntity.getLineId());
        List<WorkOrderEntity> workOrderEntityList = workOrderMapper.selectList(lambdaQueryWrapper);
        if (CollectionUtils.isEmpty(workOrderEntityList)) {
            throw new ResponseException("物料" + productFlowCodeRecordEntity.getMaterialCode() + "无对应投产工单");
        }
        List<WorkOrderEntity> workOrderEntityListResult = new ArrayList<>();
        for (WorkOrderEntity workOrderEntity : workOrderEntityList) {
            if (workOrderEntity.getMaterialCode().equals(productFlowCodeRecordEntity.getMaterialCode())) {
                workOrderEntityListResult.add(workOrderEntity);
                continue;
            }
            LambdaQueryWrapper<BomTileEntity> lambdaQueryWrapper1 = new LambdaQueryWrapper<>();
            lambdaQueryWrapper1.eq(BomTileEntity::getBomMaterialCode, workOrderEntity.getMaterialCode()).eq(BomTileEntity::getMaterialCode, productFlowCodeRecordEntity.getMaterialCode());
            Long count = bomTileMapper.selectCount(lambdaQueryWrapper1);
            if (count > 0) {
                workOrderEntityListResult.add(workOrderEntity);
            }
        }
        if (CollectionUtils.isEmpty(workOrderEntityListResult)) {
            throw new ResponseException("物料" + productFlowCodeRecordEntity.getMaterialCode() + "无对应投产工单");
        }
        if (workOrderEntityListResult.size() > 1) {
            throw new ResponseException("物料" + productFlowCodeRecordEntity.getMaterialCode() + "对应多个投产工单");
        }
        return success(workOrderEntityListResult.get(0));
    }


    /**
     * 批量添加条码记录
     *
     * @param addRecordListDTO
     * @return
     */
    @PostMapping("/add/code/record/list")
    public ResponseData addCodeRecordList(@RequestBody AddRecordListDTO addRecordListDTO) {
        scannerService.addCodeRecordList(addRecordListDTO);
        return success();
    }

    /**
     * 批量返工
     *
     * @param maintainBatchDTO
     * @return
     */
    @PostMapping("/maintain/batch")
    public ResponseData maintainBatch(@RequestBody MaintainBatchDTO maintainBatchDTO) {
        return success(scannerService.maintainBatch(maintainBatchDTO));
    }

    /**
     * 扫码工位机-扫码
     *
     * @param productFlowCode 生产流水号
     * @return
     */
    @GetMapping("/scanner")
    public ResponseData scanner(@RequestParam String productFlowCode, @RequestParam(required = false) Integer procedureId) {
        return success(scannerService.scanner(productFlowCode, procedureId, getUsername()));
    }

    /**
     * 扫码工位机-报工
     *
     * @param dto 生产流水号
     * @return
     */
    @PostMapping("/scanner/report")
    public ResponseData scannerReport(@RequestBody ScannerReportDTO dto) {
        dto.setUserName(getUsername());
        scannerService.scannerReport(dto);
        return success();
    }



    /**
     * 条码关联关系（树状）
     *
     * @param code 生产流水号
     * @return
     */
    @PostMapping("/code/relevance/tree")
    public ResponseData testTree(@RequestParam String code) {
        return success(productFlowCodeService.getRelevanceCodesTree(code));
    }

    /**
     * 条码关联视图
     * @param dto
     * @return
     */
    @PostMapping("/code/relevance/view")
    public ResponseData codeRelevanceView(@RequestBody CodeRelevanceViewSelectDTO dto) {
        return success(productFlowCodeService.codeRelevanceView(dto));
    }

}
