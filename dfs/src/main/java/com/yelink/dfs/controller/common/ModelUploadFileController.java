package com.yelink.dfs.controller.common;

import com.yelink.dfs.common.BaseController;
import com.yelink.dfs.entity.common.ModelUploadFileEntity;
import com.yelink.dfs.service.common.ModelUploadFileService;
import com.yelink.dfscommon.pojo.ResponseData;
import com.yelink.dfscommon.utils.ExcelUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/2/22 11:29
 */
@Slf4j
@RestController
@RequestMapping("/upload/file")
public class ModelUploadFileController extends BaseController {
    @Resource
    private ModelUploadFileService modelUploadFileService;

    /**
     * 获取上传单据模板列表
     */
    @GetMapping("/list")
    public ResponseData list(@RequestParam Integer modelType) {
        List<ModelUploadFileEntity> list = modelUploadFileService.listByType(modelType);
        return ResponseData.success(list);
    }

    /**
     * 删除上传的单据模板
     */
    @DeleteMapping("/delete")
    public ResponseData deleteTemplate(@RequestParam Integer id) {
        modelUploadFileService.removeFileById(id);
        return ResponseData.success();
    }

    /**
     * 上传
     */
    @PostMapping("/upload")
    public ResponseData uploadPurchaseTemplate(MultipartFile file, @RequestParam Integer modelType) {
        ExcelUtil.checkImportExcelFile(file);
        String username = getUsername();
        modelUploadFileService.uploadReferencedFile(file, modelType, username);
        return ResponseData.success();
    }

    /**
     * 获取上传模板的信息
     */
    @GetMapping("/detail")
    public ResponseData uploadPurchaseTemplate(Integer templateId) {
        ModelUploadFileEntity byId = modelUploadFileService.getById(templateId);
        return ResponseData.success(byId);
    }

    /**
     * 下载上传的文件
     */
    @GetMapping("/download")
    public void fileDownload(@RequestParam Integer id, HttpServletResponse response) throws IOException {
        modelUploadFileService.fileDownload(id, response);
    }

}
