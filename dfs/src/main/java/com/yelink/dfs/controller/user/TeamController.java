package com.yelink.dfs.controller.user;

import cn.hutool.core.util.RandomUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yelink.dfs.common.BaseController;
import com.yelink.dfs.constant.ImportTypeEnum;
import com.yelink.dfs.constant.OperationType;
import com.yelink.dfs.constant.RedisKeyPrefix;
import com.yelink.dfs.entity.order.WorkOrderTeamEntity;
import com.yelink.dfs.entity.user.SysUserEntity;
import com.yelink.dfs.open.v1.user.dto.SysTeamPageDTO;
import com.yelink.dfs.service.common.ImportDataRecordService;
import com.yelink.dfs.service.user.SysTeamService;
import com.yelink.dfscommon.annotation.OperLog;
import com.yelink.dfscommon.constant.CommonEnum;
import com.yelink.dfscommon.constant.Constant;
import com.yelink.dfscommon.constant.ParamException;
import com.yelink.dfscommon.constant.TeamRoleEnum;
import com.yelink.dfscommon.entity.dfs.SysTeamEntity;
import com.yelink.dfscommon.entity.dfs.TeamTypeDefEntity;
import com.yelink.dfscommon.pojo.ResponseData;
import com.yelink.dfscommon.utils.DateUtil;
import com.yelink.dfscommon.utils.ExcelUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;


/**
 * @description: 班组信息
 * @author: zhenghengqiu
 * @time: 2022/5/31 16:37
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/teams")
public class TeamController extends BaseController {

    private SysTeamService sysTeamService;
    private ImportDataRecordService importDataRecordService;

    /**
     * 班组列表
     *
     * @param teamType 班组类型
     * @return
     */
    @GetMapping("/list")
    public ResponseData list(@RequestParam(value = "teamType", required = false) Integer teamType,
                             @RequestParam(value = "current", defaultValue = "1") Integer current,
                             @RequestParam(value = "size", defaultValue = "10") Integer size,
                             @RequestParam(required = false) String fullTypeDefName) {
        SysTeamPageDTO dto = SysTeamPageDTO.builder().teamType(teamType).current(current).size(size).fullTypeDefName(fullTypeDefName).build();
        Page<SysTeamEntity> list = sysTeamService.list(dto);
        return success(list);
    }

    /**
     * 班组列表
     *
     * @param teamType 班组类型
     * @return
     */
    @GetMapping("/lists")
    public ResponseData getList(@RequestParam(value = "teamType", required = false) Integer teamType,
                                @RequestParam(value = "isMaintain", required = false) Integer isMaintain) {
        List<SysTeamEntity> list = sysTeamService.getList(teamType, isMaintain);
        return success(list);
    }

    /**
     * 添加班组
     *
     * @param teamEntity
     * @param bindingResult
     * @return
     */
    @PostMapping("/insert")
    @OperLog(module = "班组管理", type = OperationType.ADD, desc = "新增了班组编号为#{teamCode}的班组")
    public ResponseData add(@RequestBody @Valid SysTeamEntity teamEntity, BindingResult bindingResult) {
        if (bindingResult.hasErrors()) {
            throw new ParamException(bindingResult);
        }
        teamEntity.setCreateBy(getUsername());
        teamEntity.setCreateTime(new Date());
        teamEntity.setUpdateBy(getUsername());
        teamEntity.setUpdateTime(new Date());
        if (sysTeamService.saveEntity(teamEntity)) {
            return success(teamEntity);
        }
        return fail();
    }

    /**
     * 班组添加组员
     *
     * @param teamId
     * @return
     */
    @PostMapping("/bind/{teamId}")
    public ResponseData bind(@PathVariable("teamId") Integer teamId, @RequestParam(required = false) String userIds) {
        sysTeamService.bind(teamId, userIds);
        return success();
    }

    /**
     * 修改信息
     *
     * @param teamEntity
     * @return
     */
    @PutMapping("/update")
    @OperLog(module = "班组管理", type = OperationType.UPDATE, desc = "修改了班组编号为#{teamCode}的班组")
    public ResponseData update(@RequestBody SysTeamEntity teamEntity) {
        teamEntity.setUpdateTime(new Date());
        teamEntity.setUpdateBy(getUsername());
        sysTeamService.updateEntity(teamEntity);
        return ResponseData.success(teamEntity);
    }

    /**
     * 班组导入:下载默认导入模板
     */
    @GetMapping("/down/default/template")
    public void downDefaultTemplate(HttpServletResponse response) throws Exception {
        sysTeamService.downloadDefaultTemplate("classpath:template/teamImportTemplate.xlsx", response, "班组导入模板" + Constant.XLSX);
    }

    /**
     * 班组导入
     */
    @PostMapping("/import")
    public ResponseData importData(MultipartFile file) throws Exception {
        //1、判断导入文件的合法性
        ExcelUtil.checkImportExcelFile(file);
        //异步数据导入
        String importProgressKey = RedisKeyPrefix.TEAM_IMPORT_PROGRESS + DateUtil.format(new Date(), DateUtil.yyyyMMddHHmmss_FORMAT) + "_" + RandomUtil.randomString(2);
        sysTeamService.sycImportData(file.getOriginalFilename(), file.getInputStream(), getUsername(), importProgressKey);
        return ResponseData.success(importProgressKey);
    }

    /**
     * 班组导入:查询导入进度
     */
    @GetMapping("/import/progress")
    public ResponseData getImportProgress() {
        return success(sysTeamService.importProgress());
    }

    /**
     * 班组导入-查询导入记录列表
     */
    @RequestMapping("/import/record")
    public ResponseData recordList(@RequestParam(required = false) String fileName,
                                   @RequestParam(required = false) String startTime,
                                   @RequestParam(required = false) String endTime,
                                   @RequestParam(required = false, defaultValue = "1") Integer current,
                                   @RequestParam(required = false, defaultValue = "10") Integer size) {
        return success(importDataRecordService.getList(ImportTypeEnum.TEAM_IMPORT.getType(), fileName, startTime, endTime, current, size));
    }

    /**
     * 通过Id删除班组
     *
     * @param id
     * @return
     */
    @DeleteMapping("/delete/{id}")
    @OperLog(module = "班组管理", type = OperationType.DELETE, desc = "删除了班组编号为#{teamCode}的班组")
    public ResponseData deleteById(@PathVariable Integer id) {
        SysTeamEntity one = sysTeamService.getById(id);
        if (sysTeamService.deleteById(id)) {
            return success(one);
        }
        return fail();
    }

    /**
     * 通过Id查询班组
     *
     * @param id
     * @return
     */
    @GetMapping("/detail/{id}")
    public ResponseData detail(@PathVariable Integer id) {
        return success(sysTeamService.detailById(id));
    }

    /**
     * 获取该班组对应的组员列表
     *
     * @param id 班组id
     */
    @GetMapping("/members/{id}")
    public ResponseData getMemberList(@PathVariable(value = "id") Integer id) {
        List<SysUserEntity> list = sysTeamService.getMemberList(id);
        return success(list);
    }

    /**
     * 获取班组类型列表
     *
     * @return
     */
    @GetMapping("/type/list")
    public ResponseData getTeamTypeList(@RequestParam(required = false) String typeDefName,
                                        @RequestParam(required = false) Integer current,
                                        @RequestParam(required = false) Integer size) {
        return success(sysTeamService.getTeamTypeList(typeDefName, current, size));
    }

    /**
     * 新增班组类型定义
     *
     * @param
     * @return
     */
    @PostMapping("/type/add")
    public ResponseData addTeamType(@RequestBody TeamTypeDefEntity entity) {
        entity.setCreateBy(getUsername());
        sysTeamService.addTeamType(entity);
        return success();
    }

    /**
     * 更新班组类型定义
     *
     * @param
     * @return
     */
    @PutMapping("/type/update")
    public ResponseData updateTeamType(@RequestBody TeamTypeDefEntity entity) {
        entity.setUpdateBy(getUsername());
        sysTeamService.updateTeamType(entity);
        return success();
    }

    /**
     * 删除班组类型定义
     *
     * @param
     * @return
     */
    @DeleteMapping("/type/delete/{id}")
    public ResponseData deleteTeamType(@PathVariable(value = "id") Integer id) {
        sysTeamService.deleteTeamType(id);
        return success();
    }

    /**
     * 查询班组类型详情
     *
     * @return
     */
    @GetMapping("/type/detail/{id}")
    public ResponseData getTeamTypeDetail(@PathVariable(value = "id") Integer id) {
        return success(sysTeamService.getTeamTypeDetail(id));
    }


    /**
     * 班组角色列表
     *
     * @return
     */
    @GetMapping("/role/list")
    public ResponseData teamRoleList() {
        return success(Arrays.stream(TeamRoleEnum.values())
                .map(o -> CommonEnum.builder().code(o.getCode()).name(o.getName()).build()).collect(Collectors.toList()));
    }

    /**
     * 获取投产班组的详请
     *
     * @param teamIds     班组ids
     * @param userIds     组员和临时组员ids
     * @param leaderIds   班组长ids
     * @param orderNumber 工单号/作业工单号
     * @param isOperation 是否有开启作业工单
     */
    @GetMapping("/personnel/list")
    public ResponseData getPersonnelList(@RequestParam String teamIds,
                                         @RequestParam String userIds,
                                         @RequestParam String leaderIds,
                                         @RequestParam(required = false) String orderNumber,
                                         @RequestParam(value = "isOperation", required = false) Boolean isOperation) {
        List<WorkOrderTeamEntity> list = sysTeamService.getPersonnelList(teamIds, userIds, leaderIds, orderNumber, isOperation);
        return success(list);
    }

    /**
     * 获取默认投产班组的详请
     *
     * @param teamIds 班组ids
     */
    @GetMapping("/default/personnel/list")
    public ResponseData getDefaultPersonnelList(@RequestParam String teamIds) {
        List<WorkOrderTeamEntity> list = sysTeamService.getDefaultPersonnelList(teamIds);
        return success(list);
    }
}
