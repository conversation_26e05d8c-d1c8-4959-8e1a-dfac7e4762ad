package com.yelink.dfs.controller.weight.dto;

import lombok.Data;

/**
 * @description: 分页查询参数
 * @author: shuang
 * @time: 2022/7/15
 */
@Data
public class WeightRecordPageDto {

    /**
     * 页数
     */
    private Integer current;
    /**
     * 分页大小
     */
    private Integer size;
    /**
     * 关联的生产工工单编号
     */
    private String workOrderCode;
    /**
     * 产品编码
     */
    private String productCode;
    /**
     * 产品名称
     */
    private String productName;
    /**
     * 物料编码
     */
    private String materialCode;
    /**
     * 物料名称
     */
    private String materialName;
    /**
     * 物料类型
     */
    private Integer materialType;
    /**
     * 上报开始时间 yyyy-MM-dd HH:mm:ss
     */
    private String operatorStartTime;
    /**
     * 上报结束时间 "yyyy-MM-dd HH:mm:ss"
     */
    private String operatorEndTime;
    /**
     * 上报/称重类型
     */
    private String weightType;
}
