package com.yelink.dfs.controller.user;

import com.yelink.dfs.common.BaseController;
import com.yelink.dfs.constant.Constant;
import com.yelink.dfs.constant.OperationType;
import com.yelink.dfs.entity.common.UploadEntity;
import com.yelink.dfs.entity.user.SysUserSignatureEntity;
import com.yelink.dfs.entity.user.dto.SignatureDTO;
import com.yelink.dfs.provider.FastDfsClientService;
import com.yelink.dfs.service.user.SysUserSignatureService;
import com.yelink.dfscommon.annotation.OperLog;
import com.yelink.dfscommon.constant.RedisKeyPrefix;
import com.yelink.dfscommon.constant.ResponseException;
import com.yelink.dfscommon.entity.common.ZipFileDTO;
import com.yelink.dfscommon.pojo.ResponseData;
import com.yelink.dfscommon.utils.ExcelTemplateImportUtil;
import com.yelink.dfscommon.utils.JacksonUtil;
import com.yelink.dfscommon.utils.ZipAndRarUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.io.Resource;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.core.io.support.ResourcePatternResolver;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.Date;
import java.util.List;


/**
 * @description: 签名信息
 * @author: zhenghengqiu
 * @time: 2022/5/31 16:37
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/user/signatures")
public class SysUserSignatureController extends BaseController {

    private SysUserSignatureService userSignatureService;
    private RedisTemplate redisTemplate;
    private FastDfsClientService fastDfsClientService;

    /**
     * 列表
     *
     * @return
     */
    @GetMapping("/list")
    public ResponseData list(@RequestParam(value = "isSignature", required = false) Boolean isSignature) {
        List<SysUserSignatureEntity> list = userSignatureService.list(isSignature);
        return success(list);
    }

    /**
     * 详细信息
     *
     * @param id
     * @return
     */
    @GetMapping("/select/{id}")
    public ResponseData selectById(@PathVariable Integer id) {
        SysUserSignatureEntity entity = userSignatureService.selectById(id);
        return success(entity);
    }

    /**
     * 添加
     *
     * @param entity
     * @return
     */
    @PostMapping("/insert")
    public ResponseData add(@RequestBody SysUserSignatureEntity entity) {
        SysUserSignatureEntity signatureEntity = userSignatureService.lambdaQuery().eq(SysUserSignatureEntity::getUserId, entity.getUserId()).one();
        if (signatureEntity != null) {
            if (StringUtils.isBlank(signatureEntity.getSignatureUrl())) {
                throw new ResponseException("已存在待签名记录");
            } else {
                throw new ResponseException("签名已存在");
            }
        }
        entity.setCreateBy(getUsername());
        entity.setCreateTime(new Date());
        entity.setUpdateBy(getUsername());
        entity.setUpdateTime(new Date());
        userSignatureService.save(entity);
        return success(entity);
    }

    /**
     * 修改签名记录
     *
     * @param entity
     * @return
     */
    @PutMapping("/update")
    public ResponseData update(@RequestBody SysUserSignatureEntity entity) {
        entity.setUpdateTime(new Date());
        entity.setUpdateBy(getUsername());
        userSignatureService.updateById(entity);
        return ResponseData.success();
    }

    /**
     * 通过Id删除签名记录
     *
     * @param id
     * @return
     */
    @DeleteMapping("/delete/{id}")
    public ResponseData deleteById(@PathVariable Integer id) {
        userSignatureService.removeById(id);
        return ResponseData.success();
    }

    /**
     * 导入默认模板下载
     *
     * @param response
     */
    @GetMapping("/import/default/template")
    public void downloadFileDefaultTemplate(HttpServletResponse response) throws IOException {
        ResourcePatternResolver resolver = new PathMatchingResourcePatternResolver();
        Resource resource = resolver.getResource("classpath:template/userSignatureFileTemplate.zip");
        String templateName = URLEncoder.encode("fileTemplate" + com.yelink.dfscommon.constant.Constant.ZIP, StandardCharsets.UTF_8.name());
        ExcelTemplateImportUtil.responseToClient(response, resource.getInputStream(), templateName);
    }

    /**
     * 签名导入
     */
    @PostMapping("/import")
    public ResponseData importExcel(MultipartFile file) throws IOException {
        // 校验压缩文件
        ZipFileDTO dto = ZipAndRarUtil.checkFile(file, Constant.USER_SIGNATURE_FILE_EXCEL_NAME);
        userSignatureService.importExcel(dto.getUnzip(), dto.getExcelFile(), getUsername());
        return success();
    }

    /**
     * 获取用户签名导入进度
     *
     * @return
     */
    @GetMapping("/import/progress")
    public ResponseData getImportExcelProgress() {
        Object o = redisTemplate.opsForValue().get(RedisKeyPrefix.USER_SIGNATURE_FILE_IMPORT_PROGRESS);
        if (o != null) {
            return success(o);
        }
        return fail();
    }

    /**
     * 上传签名
     *
     * @return
     */
    @PostMapping("/upload/signature")
    @OperLog(module = "工位机管理", type = OperationType.ADD, desc = "使用设备IP为#{deviceIp}的设备上传了签名，链接为#{url}")
    public ResponseData uploadSignature(MultipartFile file, @RequestParam(value = "deviceIp",required = false) String deviceIp) {
        UploadEntity uploadEntity = fastDfsClientService.uploadFile(file, getUsername(), null);
        SignatureDTO dto = JacksonUtil.convertObject(uploadEntity, SignatureDTO.class);
        dto.setDeviceIp(deviceIp);
        return success(dto);
    }

}
