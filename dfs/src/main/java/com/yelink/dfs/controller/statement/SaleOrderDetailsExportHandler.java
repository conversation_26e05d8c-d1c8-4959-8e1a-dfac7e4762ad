package com.yelink.dfs.controller.statement;

import com.asyncexcel.core.DataParam;
import com.asyncexcel.core.ExcelContext;
import com.asyncexcel.core.ExportPage;
import com.asyncexcel.core.annotation.ExcelHandle;
import com.asyncexcel.core.exporter.DataExportParam;
import com.asyncexcel.core.exporter.ExportContext;
import com.asyncexcel.core.exporter.ExportHandler;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yelink.dfs.entity.statement.dto.StatementProductionDTO;
import com.yelink.dfs.service.common.CommonService;
import com.yelink.dfs.service.statement.StatementService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * @description: 当前订单明细数据导出
 * @author: shuang
 * @time: 2023/9/21
 */
@Slf4j
@ExcelHandle
@RequiredArgsConstructor
public class SaleOrderDetailsExportHandler implements ExportHandler<StatementProductionDTO.SaleOrderDetailVO> {


    private final CommonService commonService;
    private final StatementService statementService;
    /**
     * 导出数据最大不能超过100万行
     */
    public static final long EXPORT_MAX_ROWS = 1000000;


    @Override
    public void init(ExcelContext ctx, DataParam param) {
        ExportContext context = (ExportContext) ctx;
        StatementProductionDTO.SaleOrderDetailSelectDTO saleOrderDetailSelectDTO = (StatementProductionDTO.SaleOrderDetailSelectDTO) param.getParameters().get(StatementProductionDTO.SaleOrderDetailSelectDTO.class.getName());
        String cleanSheetNames = (String) param.getParameters().get("cleanSheetNames");
        commonService.initExcelContext(saleOrderDetailSelectDTO.getTemplateId(), saleOrderDetailSelectDTO.getTemplateSheetName(),cleanSheetNames, context, StatementProductionDTO.SaleOrderDetailVO.class, null);
    }

    @Override
    public ExportPage<StatementProductionDTO.SaleOrderDetailVO> exportData(int startPage, int limit, DataExportParam param) {
        StatementProductionDTO.SaleOrderDetailSelectDTO saleOrderDetailSelectDTO = (StatementProductionDTO.SaleOrderDetailSelectDTO) param.getParameters().get(StatementProductionDTO.SaleOrderDetailSelectDTO.class.getName());
        saleOrderDetailSelectDTO.setCurrent(startPage);
        saleOrderDetailSelectDTO.setSize(limit);
        Page<StatementProductionDTO.SaleOrderDetailVO> page = statementService.listSaleOrderDetail(saleOrderDetailSelectDTO);
        ExportPage<StatementProductionDTO.SaleOrderDetailVO> result = new ExportPage<>();
        result.setTotal(Math.min(page.getTotal(), EXPORT_MAX_ROWS));
        result.setCurrent((long) startPage);
        result.setSize((long) limit);
        result.setRecords(page.getRecords());
        return result;
    }


    @Override
    public void beforePerPage(ExportContext ctx, DataExportParam param) {
        log.info("开始查询第{}页", (long) Math.ceil(ctx.getSuccessCount() + ctx.getFailCount()) / ctx.getLimit());
    }

    @Override
    public void afterPerPage(List<StatementProductionDTO.SaleOrderDetailVO> list, ExportContext ctx, DataExportParam param) {
        log.info("已完成数量：{}", ctx.getSuccessCount());
    }

    @Override
    public void callBack(ExcelContext ctx, DataParam param) {
        ExportContext context = (ExportContext) ctx;
        log.info("导出完成：{}", context.getFailMessage());
    }
}



