package com.yelink.dfs.controller.task;

import com.yelink.dfs.common.BaseController;
import com.yelink.dfs.service.task.TaskRoleConfigService;
import com.yelink.dfs.entity.task.dto.TaskRoleConfigSelectDTO;
import com.yelink.dfs.entity.task.dto.TaskRoleConfigUpdateDTO;
import com.yelink.dfscommon.pojo.ResponseData;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * 任务配置
 * <AUTHOR>
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/task/role/config")
public class TaskRoleConfigController extends BaseController {

    private TaskRoleConfigService taskRoleConfigService;

    /**
     * 任务角色配置
     */
    @PostMapping("/list")
    public ResponseData list(@RequestBody TaskRoleConfigSelectDTO dto) {
        return ResponseData.success(taskRoleConfigService.listConfig(dto));
    }

    /**
     * 更新任务角色配置
     */
    @PostMapping("/update")
    public ResponseData update(@RequestBody @Validated TaskRoleConfigUpdateDTO dto) {
        taskRoleConfigService.updateConfig(dto);
        return ResponseData.success();
    }
}
