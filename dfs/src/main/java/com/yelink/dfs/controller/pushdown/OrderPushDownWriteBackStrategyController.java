package com.yelink.dfs.controller.pushdown;

import com.yelink.dfs.service.common.config.OrderPushDownWriteBackStrategyService;
import com.yelink.dfscommon.constant.dfs.pushdown.PushDownItemTypeEnum;
import com.yelink.dfscommon.constant.dfs.pushdown.PushDownOrderStateEnum;
import com.yelink.dfscommon.dto.pushdown.strategy.PushDownWriteBackStrategyAddDTO;
import com.yelink.dfscommon.dto.pushdown.strategy.PushDownWriteBackStrategyEditDTO;
import com.yelink.dfscommon.entity.CommonType;
import com.yelink.dfscommon.pojo.ResponseData;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

import static com.yelink.dfscommon.pojo.ResponseData.success;

/**
 * 单据下推回写策略
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/order_push_downs/write_back_strategy")
public class OrderPushDownWriteBackStrategyController {

    @Resource
    private OrderPushDownWriteBackStrategyService writeBackStrategyService;

    @PostMapping("/list")
    public ResponseData list() {
        return success(writeBackStrategyService.list());
    }

    @PostMapping("/add")
    public ResponseData add(@RequestBody @Valid PushDownWriteBackStrategyAddDTO dto) {
        writeBackStrategyService.add(dto);
        return success();
    }

    @PostMapping("/edit")
    public ResponseData edit(@RequestBody @Valid PushDownWriteBackStrategyEditDTO dto) {
        writeBackStrategyService.edit(dto);
        return success();
    }
    @PostMapping("/delete")
    public ResponseData edit(@RequestParam Integer id) {
        writeBackStrategyService.removeById(id);
        return success();
    }

    @GetMapping("/order_state_types")
    public ResponseData itemTypes() {
        return success(CommonType.covertToList(PushDownOrderStateEnum.class));
    }
}
