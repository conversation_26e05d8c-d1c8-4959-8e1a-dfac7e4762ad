package com.yelink.dfs.controller.product;

import com.yelink.dfs.common.BaseController;
import com.yelink.dfscommon.entity.dfs.MaterialTypeConfigAttributeEntity;
import com.yelink.dfs.service.product.MaterialTypeConfigAttributeService;
import com.yelink.dfscommon.pojo.ResponseData;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @description 物料表
 * @Date 2021/4/14
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/material/type/config/attributes")
public class MaterialTypeConfigAttributeController extends BaseController {

    private MaterialTypeConfigAttributeService materialTypeConfigAttributeService;

    /**
     * 查询物料属性配置
     *
     * @return
     */
    @GetMapping("/list/{materialTypeId}")
    public ResponseData typeList(@PathVariable Integer materialTypeId) {
        List<MaterialTypeConfigAttributeEntity> list = materialTypeConfigAttributeService.getList(materialTypeId);
        return success(list);
    }

}
