package com.yelink.dfs.controller.trace;

import com.yelink.dfs.common.BaseController;
import com.yelink.dfs.constant.Constant;
import com.yelink.dfs.constant.ReportFormConstant;
import com.yelink.dfs.service.trace.WorkOrderTraceService;
import com.yelink.dfscommon.dto.ExportBillBatchDTO;
import com.yelink.dfscommon.utils.ExcelTemplateImportUtil;
import com.yelink.dfscommon.utils.FileUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;

/**
 * <AUTHOR> @description 工单追溯
 * @Date 2021/05/24
 */
@Slf4j
@RestController
@RequestMapping("/work/order/trace")
@RequiredArgsConstructor
public class WorkOrderTraceController extends BaseController {

    private final WorkOrderTraceService workOrderTraceService;

    /**
     * 下载默认打印模板
     */
    @GetMapping("/default/template")
    public void exportDailyTemplate(HttpServletResponse response) throws IOException {
        byte[] bytes = workOrderTraceService.downloadDefaultExportTemplate("classpath:template" + File.separator + ReportFormConstant.DEFAULT_WORK_ORDER_TRACE_TEMPLATE + Constant.XLSX);
        ExcelTemplateImportUtil.responseToClient(response, bytes,"订单追溯默认模板" + Constant.XLSX);
    }

    /**
     * 下载/打印pdf
     */
    @PostMapping("/print/pdf")
    public void printPdf(@RequestBody ExportBillBatchDTO batchVO, HttpServletResponse response) throws IOException {
        File pdfFile = workOrderTraceService.asposePrintDataToPdf(batchVO);
        try {
            FileUtil.exportPdfFile(response, pdfFile, batchVO.getFileName());
        } finally {
            FileUtils.deleteQuietly(pdfFile);
        }
    }

    /**
     * 导出excel
     */
    @PostMapping("/export/excel")
    public void exportExcel(@RequestBody ExportBillBatchDTO batchVO, HttpServletResponse response) throws IOException {
        File excelFile = workOrderTraceService.asposePrintDataToExcel(batchVO);
        // 工单追溯因为导出不能选多个工单,所以只有一个
        File[] files = excelFile.listFiles();
        try(FileInputStream fileInputStream = new FileInputStream(files[0]);) {
            ExcelTemplateImportUtil.responseToClient(response, fileInputStream, batchVO.getFileName());
        } finally {
            FileUtils.deleteQuietly(excelFile);
        }
    }


}
