package com.yelink.dfs.controller.common;

import com.yelink.dfs.common.BaseController;
import com.yelink.dfs.constant.OperationType;
import com.yelink.dfs.entity.common.DeleteSelectDto;
import com.yelink.dfs.service.common.DeleteRecordService;
import com.yelink.dfscommon.annotation.OperLog;
import com.yelink.dfscommon.constant.RedisKeyPrefix;
import com.yelink.dfscommon.constant.dfs.ModuleEnum;
import com.yelink.dfscommon.dto.dfs.DeleteRecordDetailDto;
import com.yelink.dfscommon.entity.CommonType;
import com.yelink.dfscommon.pojo.ResponseData;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @Date 2022/5/12 11:16
 */
@RestController
@Slf4j
@AllArgsConstructor
@RequestMapping("/delete_records")
public class DeleteRecordController extends BaseController {

    private DeleteRecordService deleteRecordService;
    private RedisTemplate redisTemplate;

    /**
     * 分组查询删除的单据记录信息
     *
     * @param selectDto 查询条件
     * @return
     */
    @PostMapping("/list")
    public ResponseData getList(@RequestBody DeleteSelectDto selectDto) {
        return success(deleteRecordService.getList(selectDto));
    }
    /**
     * 单据类型
     */
    @GetMapping("/typeEnum")
    public ResponseData typeEnum() {
        List<CommonType> results = Stream.of(ModuleEnum.MATERIAL, ModuleEnum.BOM, ModuleEnum.CRAFT, ModuleEnum.WORK_ORDER).map(e -> CommonType.builder()
                .code(e.getCode())
                .name(e.getName())
                .build()
        ).collect(Collectors.toList());
        return success(results);
    }

    /**
     * 获取回退单据的删除记录
     *
     * @param detailDto 查询条件
     * @return
     */
    @PostMapping("/detail")
    public ResponseData getDetail(@RequestBody DeleteRecordDetailDto detailDto) {
        return success(deleteRecordService.getDetail(detailDto));
    }

    /**
     * 单据回退
     *
     * @param detailDto 单据
     * @return
     */
    @PostMapping("/return")
    @OperLog(module = "现场管理", type = OperationType.RETURN, desc = "回退了单据号为#{orderNumber}的单据")
    public ResponseData returnOrder(@RequestBody DeleteRecordDetailDto detailDto) {
        deleteRecordService.returnOrder(detailDto);
        return success(detailDto);
    }

    /**
     * 单据回退进度
     *
     * @return
     */
    @PostMapping("/return/process")
    public ResponseData returnOrderProcess(@RequestBody DeleteRecordDetailDto detailDto) {
        String process = (String) redisTemplate.opsForValue().get(RedisKeyPrefix.RETURN_ORDER_KEY + detailDto.getOrderType() + detailDto.getOrderNumber() + detailDto.getCreateTime());
        return success(process);
    }


}
