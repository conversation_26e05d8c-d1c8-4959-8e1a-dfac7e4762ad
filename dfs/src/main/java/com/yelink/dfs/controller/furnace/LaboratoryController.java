package com.yelink.dfs.controller.furnace;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yelink.dfs.common.BaseController;
import com.yelink.dfs.constant.OperationType;
import com.yelink.dfs.entity.furnace.ConfigOffsetEntity;
import com.yelink.dfs.entity.furnace.LaboratoryReportEntity;
import com.yelink.dfs.entity.furnace.dto.LaboratoryReportDto;
import com.yelink.dfs.service.furnace.ConfigOffsetService;
import com.yelink.dfs.service.furnace.FurnaceCommonService;
import com.yelink.dfs.service.furnace.FurnaceService;
import com.yelink.dfs.service.furnace.LaboratoryReportService;
import com.yelink.dfscommon.annotation.OperLog;
import com.yelink.dfscommon.constant.RespCodeEnum;
import com.yelink.dfscommon.constant.ResponseException;
import com.yelink.dfscommon.pojo.ResponseData;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 化验控制器
 *
 * <AUTHOR>
 * @Date 2021-08-03 12:02
 */

@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/laboratory")
public class LaboratoryController extends BaseController {
    private LaboratoryReportService laboratoryReportService;
    private FurnaceService furnaceService;
    private ConfigOffsetService configOffsetService;
    private FurnaceCommonService furnaceCommonService;

    /**
     * 获取状态可用的化验样次
     *
     * @return
     * <AUTHOR>
     */
    @GetMapping("/specimen/list")
    public ResponseData listSpecimen() {
        return success(laboratoryReportService.listSpecimen());
    }

    /**
     * 获取炉次历史化验数据
     *
     * @return
     */
    @GetMapping("/specimen/history/list")
    public ResponseData listSpecimenHistory(@RequestParam(value = "furnaceCode") String furnaceCode) {
        return success(laboratoryReportService.listSpecimenHistory(furnaceCode));
    }

    /**
     * 获取工位提交的历史化验数据
     *
     * @return
     */
    @GetMapping("/history/list/fac")
    public ResponseData reportHistoryListByReportFid(@RequestParam(value = "reportFid") Integer reportFid,
                                                     @RequestParam(value = "current", required = false) Integer current,
                                                     @RequestParam(value = "size", required = false) Integer size) {
        Page<LaboratoryReportEntity> page = laboratoryReportService.reportHistoryListByReportFid(reportFid, current, size);
        return success(page);
    }

    /**
     * 获取元素表头
     *
     * @return
     * <AUTHOR>
     */
    @GetMapping("/element/list")
    public ResponseData listElementTitle() {
        return success(laboratoryReportService.listElement());
    }


    /**
     * 选中某一次的化验样次后,返回修正前与修正后的数据
     *
     * @param number
     * @return
     * <AUTHOR>
     */
    @GetMapping("/specimen/select")
    public ResponseData selectOneSpecimen(@RequestParam(value = "number") String number) {
        String username = getUsername();
        return success(laboratoryReportService.selectOneSpecimen(number, username));

    }

    /**
     * 化验提交
     *
     * @param number
     * @param furnaceCode
     * @param sequence
     * @return
     * <AUTHOR>
     */
    @GetMapping("/specimen/submit")
    public ResponseData submit(@RequestParam(value = "number") String number,
                               @RequestParam(value = "furnaceCode") String furnaceCode,
                               @RequestParam(value = "sequence") Integer sequence,
                               @RequestParam(value = "fid") Integer fid,
                               @RequestParam(value = "reportFid") Integer reportFid) {
        String username = getUsername();
        furnaceCommonService.submit(number, furnaceCode, sequence, fid, username, reportFid);
        //修改化验

        //给工位机发送最新化验信息的websocket消息
        LaboratoryReportDto laboratoryReportDto = laboratoryReportService.getNewestSpecimen(furnaceCode, fid);
        //1.12.1 websocket发送消息使用新的
        laboratoryReportService.submitMessage(laboratoryReportDto, fid, furnaceCode);
        return success();
    }


    /**
     * 查找正在生产状态的炉次
     *
     * @return
     * <AUTHOR>
     */
    @GetMapping("/furnace/list")
    public ResponseData listFurnaceInState(@RequestParam(value = "fid") Integer fid) {
        return success(furnaceService.listFurnaceInState(fid));
    }


    /**
     * 查找当前工位正在生产状态的炉次
     *
     * @return
     * <AUTHOR>
     */
    @GetMapping("/furnace/get")
    public ResponseData listFurnaceInStateByFid(@RequestParam(value = "fid") Integer fid) {
        return success(furnaceService.listFurnaceInStateByFid(fid));
    }


    /**
     * 根据炉次获取工位名称列表
     *
     * @param furnaceCode
     * @return
     * <AUTHOR>
     */
    @GetMapping("/fname/list/{furnaceCode}")
    public ResponseData listFname(@PathVariable(value = "furnaceCode") String furnaceCode) {
        return success(furnaceService.listFname(furnaceCode));
    }

    /**
     * 获取工位炉次的化验数+1
     *
     * @param furnaceCode
     * @param fid
     * @return
     * <AUTHOR>
     */
    @GetMapping("/sequence")
    public ResponseData getSequence(@RequestParam(value = "furnaceCode", required = false) String furnaceCode,
                                    @RequestParam(value = "fid") String fid) {
        return success(laboratoryReportService.getSequence(furnaceCode, fid));
    }


    /**
     * 获取元素列表
     *
     * @return
     * <AUTHOR>
     */
    @GetMapping("/offset/list")

    public ResponseData listOffset() {
        LambdaQueryWrapper<ConfigOffsetEntity> configOffsetWrapper = new LambdaQueryWrapper<>();
        configOffsetWrapper.orderByAsc(ConfigOffsetEntity::getSequence);
        List<ConfigOffsetEntity> list = configOffsetService.list(configOffsetWrapper);
        return success(list);
    }

    /**
     * 修改元素偏差值
     *
     * @param list
     * @return
     * <AUTHOR>
     */
    @PutMapping("/offset/edit")
    public ResponseData editOffset(@RequestBody List<ConfigOffsetEntity> list) {
        String userName = getUsername();
        Date date = new Date();
        list.forEach(entity -> {
            entity.setUpdateBy(userName);
            entity.setUpdateDate(date);
            configOffsetService.updateById(entity);
        });
        return success();
    }

    /**
     * 删除展示元素
     *
     * @param id
     * <AUTHOR>
     */
    @OperLog(module = "化验元素管理", type = OperationType.DELETE, desc = "删除了元素名为#{elementName}元素信息")
    @DeleteMapping("/offset/delete/{id}")
    public ResponseData deleteOffset(@PathVariable(value = "id") Integer id) {
        return success(configOffsetService.removeById(id));

    }

    /**
     * 新增展示元素
     *
     * @param configOffsetEntity
     * <AUTHOR>
     */
    @OperLog(module = "化验元素管理", type = OperationType.ADD, desc = "新增了元素名为#{elementName}元素信息")
    @PostMapping("/offset/add")
    public ResponseData addOffset(@RequestBody ConfigOffsetEntity configOffsetEntity) {
        //判断元素是否重复
        LambdaQueryWrapper<ConfigOffsetEntity> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(ConfigOffsetEntity::getElementName, configOffsetEntity.getElementName());
        List<ConfigOffsetEntity> list = configOffsetService.list(lambdaQueryWrapper);
        if (!CollectionUtils.isEmpty(list)) {
            throw new ResponseException(RespCodeEnum.ELEMENT_REPEAT);
        }

        String userName = getUsername();
        configOffsetEntity.setCreateBy(userName);
        configOffsetEntity.setCreateDate(new Date());
        return success(configOffsetService.save(configOffsetEntity));

    }

    /**
     * 模糊查询某个元素
     *
     * @return
     * <AUTHOR>
     */
    @GetMapping("/offset/select/{elementName}")
    public ResponseData selectOffset(@PathVariable(value = "elementName") String elementName) {
        LambdaQueryWrapper<ConfigOffsetEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.like(ConfigOffsetEntity::getElementName, elementName);
        List<ConfigOffsetEntity> list = configOffsetService.list(wrapper);
        return success(list);
    }

    /**
     * 废弃化验数据
     *
     * @param list
     * <AUTHOR>
     */
    @DeleteMapping("/specimen/abandon")
    public ResponseData abandonSpecimen(@RequestBody List<String> list) {
        String username = getUsername();
        list.forEach(number -> {
            laboratoryReportService.abandonSpecimen(number, username);
        });
        return success();
    }

    /**
     * 添加化验记录
     *
     * @param list
     * @return
     */
    @PostMapping("/specimen/add")
    public ResponseData manualAddSpecimen(@RequestParam(value = "number", required = false) String number,
                                          @RequestBody List<ConfigOffsetEntity> list) {
        String username = getUsername();
        Map<String, Double> elementList = new HashMap<>();
        for (ConfigOffsetEntity configOffsetEntity : list) {
            elementList.put(configOffsetEntity.getElementName(), configOffsetEntity.getValue());
        }
        if (StringUtils.isEmpty(number)) {
            JSONObject sensorValue = JSON.parseObject(JSON.toJSONString(elementList));
            String newNumber = laboratoryReportService.addSpecimen(sensorValue, true, 1, username);
            return success(laboratoryReportService.getSpecimenValueList(newNumber));
        }
        laboratoryReportService.updateSpecimenValues(number, elementList);
        return success(laboratoryReportService.getSpecimenValueList(number));
    }

    /**
     * 修改元素校验值
     *
     * @param number
     * @param elementName
     * @param reviseValue
     * @return
     */
    @PutMapping("/specimen/value/edit")
    public ResponseData editSpecimenValue(@RequestParam(value = "number") String number,
                                          @RequestParam(value = "elementName") String elementName,
                                          @RequestParam(value = "reviseValue") String reviseValue) {
        if (StringUtils.isEmpty(number) || StringUtils.isEmpty(elementName)) {
            throw new ResponseException();
        }
        return success(laboratoryReportService.editSpecimenValue(number, elementName, reviseValue));
    }

    /**
     * 按时间导出excel
     *
     * @param startTime
     * @param endTime
     */
    @GetMapping("/specimen/export")
    public void exportExcel(@RequestParam(value = "startTime", required = false) String startTime,
                            @RequestParam(value = "endTime", required = false) String endTime
            , HttpServletResponse response) {
        try {
            laboratoryReportService.exportExcel(null, startTime, endTime, response);
        } catch (IOException e) {
            throw new ResponseException(RespCodeEnum.FILE_EXPORT_FAILED);
        }
    }


    /**
     * 获取最新化验结果
     *
     * @param furnaceCode
     * @param fid
     * @return
     */
    @GetMapping("/specimen/newest")
    public ResponseData getNewestSpecimen(@RequestParam(value = "furnaceCode", required = false) String furnaceCode,
                                          @RequestParam(value = "fid", required = false) Integer fid) {
        return success(laboratoryReportService.getNewestSpecimen(furnaceCode, fid));
    }
}
