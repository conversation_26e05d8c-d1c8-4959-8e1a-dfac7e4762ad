package com.yelink.dfs.controller.weight;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yelink.dfs.common.BaseController;
import com.yelink.dfs.controller.weight.dto.WeightRecordPageDto;
import com.yelink.dfs.entity.PullDownSelectorGeneralEntity;
import com.yelink.dfs.entity.user.SysUserEntity;
import com.yelink.dfs.entity.weight.WeightRecordEntity;
import com.yelink.dfs.entity.weight.WeightRecordExtendEntity;
import com.yelink.dfs.entity.weight.vo.WeightRecordVO;
import com.yelink.dfs.service.user.SysUserService;
import com.yelink.dfs.service.weight.WeightRecordService;
import com.yelink.dfscommon.pojo.ResponseData;
import com.yelink.dfscommon.utils.EasyExcelUtil;
import com.yelink.dfscommon.utils.PageData;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @description: 称重接口层
 * @author: shuang
 * @time: 2022/7/14
 */
@AllArgsConstructor
@RestController
@RequestMapping("/weight/records")
public class WeightRecordController extends BaseController {

    private final WeightRecordService weightRecordService;
    private final SysUserService sysUserService;

    /**
     * 条件分页查询上料记录以及关联的工单信息
     *
     * @return
     */
    @GetMapping("/relation/work/order/page")
    public ResponseData page(@ModelAttribute WeightRecordPageDto weightRecordPageDto) {
        Assert.notNull(weightRecordPageDto.getCurrent(), "current不能为null");
        Assert.notNull(weightRecordPageDto.getSize(), "size不能为null");
        Page<WeightRecordExtendEntity> page = weightRecordService.pageBy(weightRecordPageDto);
        return ResponseData.success(page);
    }

    /**
     * 按条件导出上料记录以及关联的工单信息
     */
    @GetMapping("/export")
    public void export(@ModelAttribute WeightRecordPageDto weightRecordPageDto, HttpServletResponse response) throws IOException {
        weightRecordPageDto.setCurrent(1);
        weightRecordPageDto.setSize(Integer.MAX_VALUE);
        List<WeightRecordExtendEntity> records = weightRecordService.pageBy(weightRecordPageDto).getRecords();
        EasyExcelUtil.export(response, "weightRecord", "称重记录", records, WeightRecordExtendEntity.class);
    }

    /**
     * 分页查询上料记录
     *
     * @param size
     * @param current
     * @param weightType    操作类型
     * @param workOrderCode 关联的工单编号
     * @return
     */
    @GetMapping("/page")
    public ResponseData page(@RequestParam(value = "size", required = false, defaultValue = "10") Integer size,
                             @RequestParam(value = "current", required = false, defaultValue = "1") Integer current,
                             @RequestParam(value = "weightType", required = false) String weightType,
                             @RequestParam(value = "workOrderCode", required = false) String workOrderCode) {
        Page<WeightRecordEntity> page = weightRecordService.lambdaQuery()
                .eq(weightType != null, WeightRecordEntity::getWeightType, weightType)
                .eq(StringUtils.isNotBlank(workOrderCode), WeightRecordEntity::getWorkOrderCode, workOrderCode)
                .page(new Page<>(current, size));
        // 設置操作人名字
        if (!CollectionUtils.isEmpty(page.getRecords())) {
            Set<String> usernames = page.getRecords().stream().map(WeightRecordEntity::getOperator).collect(Collectors.toSet());
            List<SysUserEntity> sysUserEntities = sysUserService.selectByUsernames(usernames.toArray(new String[1]));
            Map<String, String> usernameNickMap = sysUserEntities.stream().collect(Collectors.toMap(SysUserEntity::getUsername, SysUserEntity::getNickname));
            page.getRecords().forEach(record -> record.setOperator(usernameNickMap.get(record.getOperator())));
        }
        // 总重量
        QueryWrapper<WeightRecordEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("IFNULL(SUM(weight_value),0) as totalWeightValue");
        queryWrapper.lambda()
                .eq(weightType != null, WeightRecordEntity::getWeightType, weightType)
                .eq(StringUtils.isNotBlank(workOrderCode), WeightRecordEntity::getWorkOrderCode, workOrderCode);
        Map<String, Object> map = weightRecordService.getMap(queryWrapper);
        WeightRecordVO vo = WeightRecordVO.builder()
                .totalWeightValue((Double) map.get("totalWeightValue"))
                .pageData(new PageData<>(current, size, Long.valueOf(page.getTotal()).intValue(), page.getRecords()))
                .build();
        return ResponseData.success(vo);
    }


    /**
     * 通过id查询详情信息
     *
     * @param id
     * @return
     */
    @GetMapping("/detail/{id}")
    public ResponseData detailById(@PathVariable Integer id) {
        WeightRecordExtendEntity weightRecordExtendEntity = weightRecordService.detailById(id);
        return ResponseData.success(weightRecordExtendEntity);
    }

    /**
     * 增加称重记录
     *
     * @param weightRecordEntity
     * @return
     */
    @PostMapping("/add")
    public ResponseData add(@RequestBody @Validated WeightRecordEntity weightRecordEntity, BindingResult bindingResult) {
        if (bindingResult.hasErrors()) {
            ResponseData.fail(bindingResult.getFieldErrors());
        }
        Date date = new Date();
        weightRecordEntity.setCreateTime(date);
        weightRecordEntity.setUpdateTime(date);
        weightRecordEntity.setCreateBy(getUsername());
        weightRecordEntity.setUpdateBy(getUsername());
        if (weightRecordEntity.getOperator() == null) {
            weightRecordEntity.setOperator(getUsername());
        }
        weightRecordEntity.setOperatorTime(date);
        weightRecordService.saveCalculationWeightRecordEntity(weightRecordEntity);
        return ResponseData.success(true);
    }


    /**
     * 获取称重类型下拉数据
     *
     * @return
     */
    @GetMapping("/type/list")
    public ResponseData weightTypeList() {
        List<PullDownSelectorGeneralEntity> list = weightRecordService.weightTypeList();
        return ResponseData.success(list);
    }

}
