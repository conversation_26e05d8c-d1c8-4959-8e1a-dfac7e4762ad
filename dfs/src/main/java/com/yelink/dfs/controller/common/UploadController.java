package com.yelink.dfs.controller.common;

import com.yelink.dfs.common.BaseController;
import com.yelink.dfs.provider.FastDfsClientService;
import com.yelink.dfs.service.common.UploadService;
import com.yelink.dfscommon.pojo.ResponseData;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;


/**
 * @description: 上传接口
 * @author: zengfu
 * @create: 2020-06-11 11:36
 **/
@Slf4j
@RestController()
@RequiredArgsConstructor
@RequestMapping("/resource")
public class UploadController extends BaseController {


    private final FastDfsClientService fastDfsClientService;

    private final UploadService uploadService;

    /**
     * 上传文件
     * @param file
     * @return
     * @throws Exception
     */

    @PostMapping("/add")
    public ResponseData upload(MultipartFile file, @RequestParam(value = "imageCode",required = false) String imageCode) {
        String username = getUsername();
        return ResponseData.success(fastDfsClientService.uploadFile(file, username,imageCode));
    }


    /**
     * 上传未标记文件，超时后会被清除，需要业务处理标记记录
     * @param file
     * @return
     */
    @PostMapping("/add/file")
    public ResponseData uploadFile(MultipartFile file) {
        String username = getUsername();
        return ResponseData.success(uploadService.uploadReferencedFile(file, username));
    }
}
