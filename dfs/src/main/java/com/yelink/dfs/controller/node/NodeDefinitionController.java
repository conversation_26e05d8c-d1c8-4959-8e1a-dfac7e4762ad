package com.yelink.dfs.controller.node;

import com.yelink.dfs.common.BaseController;
import com.yelink.dfs.entity.node.NodeDefinitionEntity;
import com.yelink.dfs.service.node.NodeDefinitionService;
import com.yelink.dfscommon.pojo.ResponseData;
import com.yelink.dfscommon.utils.DateUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;

/**
 * <AUTHOR>
 * 节点定义
 * @Date 2022/4/22 10:41
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/node/definition")
public class NodeDefinitionController extends BaseController {
    private NodeDefinitionService nodeDefinitionService;


    /**
     * 列表
     *
     * @param size
     * @param current
     * @param nodeDefinitionName
     * @param reportType
     * @param state
     * @param approver
     * @return
     */
    @GetMapping("/list")
    public ResponseData list(@RequestParam(value = "size", required = false) Integer size,
                             @RequestParam(value = "current", required = false) Integer current,
                             @RequestParam(value = "nodeDefinitionName", required = false) String nodeDefinitionName,
                             @RequestParam(value = "reportType", required = false) String reportType,
                             @RequestParam(value = "state", required = false) Integer state,
                             @RequestParam(value = "approver", required = false) String approver) {
        return ResponseData.success(nodeDefinitionService.getList(size, current, nodeDefinitionName, reportType, state, approver));
    }

    /**
     * 新增
     *
     * @param entity
     * @return
     * @throws Exception
     */
    @PostMapping("/insert")
    public ResponseData add(@RequestBody NodeDefinitionEntity entity) {
        entity.setCreateBy(getUsername());
        entity.setCreateTime(DateUtil.formatToDate(new Date(), DateUtil.DATETIME_FORMAT));
        Boolean result = nodeDefinitionService.add(entity);
        if (result) {
            return ResponseData.success();
        }
        return ResponseData.fail();
    }

    /**
     * 新增
     *
     * @param entity
     * @return
     * @throws Exception
     */
    @PostMapping("/insert/released")
    public ResponseData addReleasedDefinition(@RequestBody NodeDefinitionEntity entity) {
        String username = getUsername();
        entity.setCreateBy(username);
        entity.setUpdateBy(username);
        entity.setCreateTime(new Date());
        entity.setUpdateTime(new Date());
        nodeDefinitionService.addReleasedDefinition(entity);
        return ResponseData.success();
    }

    /**
     * 修改
     *
     * @param entity
     * @return
     * @throws Exception
     */
    @PutMapping("/update")
    public ResponseData edit(@RequestBody NodeDefinitionEntity entity) {
        entity.setUpdateBy(getUsername());
        entity.setUpdateTime(DateUtil.formatToDate(new Date(), DateUtil.DATETIME_FORMAT));
        Boolean result = nodeDefinitionService.edit(entity);
        if (result) {
            return ResponseData.success();
        }
        return ResponseData.fail();
    }

    /**
     * 删除
     *
     * @param id
     * @return
     */
    @DeleteMapping("/delete/{id}")
    public ResponseData remove(@PathVariable(value = "id") Integer id) {
        Boolean result = nodeDefinitionService.delete(id);
        if (result) {
            return ResponseData.success();
        }
        return ResponseData.fail();
    }

    /**
     * 详情
     *
     * @param id
     * @return
     */
    @GetMapping("/detail/{id}")
    public ResponseData detail(@PathVariable(value = "id") Integer id) {
        return ResponseData.success(nodeDefinitionService.detail(id));
    }


    /**
     * 审批
     */
    @GetMapping("/examine/approve")
    public ResponseData examineOrApprove(@RequestParam(value = "approvalStatus") Integer approvalStatus,
                                         @RequestParam(value = "approvalSuggestion", required = false) String approvalSuggestion,
                                         @RequestParam(value = "id") Integer id) {
        nodeDefinitionService.examineOrApprove(approvalStatus, approvalSuggestion, id, getUsername());
        return ResponseData.success();
    }

    /**
     * 获取上报类型
     */
    @GetMapping("/type")
    public ResponseData getReportTypeList() {
        return ResponseData.success(nodeDefinitionService.getReportTypeList());
    }

    /**
     * 获取状态列表
     */
    @GetMapping("/state")
    public ResponseData getStateList() {
        return ResponseData.success(nodeDefinitionService.getStateList());
    }

}