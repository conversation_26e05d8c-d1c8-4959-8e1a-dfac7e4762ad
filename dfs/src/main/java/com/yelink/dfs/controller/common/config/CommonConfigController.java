package com.yelink.dfs.controller.common.config;

import com.yelink.dfs.common.BaseController;
import com.yelink.dfs.constant.common.config.ProductOrderMaterialListSmartConfigEnum;
import com.yelink.dfs.entity.common.CommonType;
import com.yelink.dfs.entity.common.config.dto.AutoReportAfterFinishWorkConfigDTO;
import com.yelink.dfs.entity.common.config.dto.BomDefaultConfigDTO;
import com.yelink.dfs.entity.common.config.dto.BusinessUnitConfDTO;
import com.yelink.dfs.entity.common.config.dto.CommonBooleanConfigDTO;
import com.yelink.dfs.entity.common.config.dto.CraftDefaultConfigDTO;
import com.yelink.dfs.entity.common.config.dto.CraftRequiredConfDTO;
import com.yelink.dfs.entity.common.config.dto.JurisdictionConfigDTO;
import com.yelink.dfs.entity.common.config.dto.ListConfigDTO;
import com.yelink.dfs.entity.common.config.dto.MaterialLossRateConfDTO;
import com.yelink.dfs.entity.common.config.dto.ProductOrderBatchConfigDTO;
import com.yelink.dfs.entity.common.config.dto.ProductOrderMaterialListConfigDTO;
import com.yelink.dfs.entity.common.config.dto.PurchaseOrderConfigDTO;
import com.yelink.dfs.entity.common.config.dto.ScanAutoGoWorkOrderPageConfDTO;
import com.yelink.dfs.entity.common.config.dto.ShowConfigDTO;
import com.yelink.dfs.entity.common.config.dto.VerReportNumConfigDTO;
import com.yelink.dfs.entity.common.config.dto.VerWorkOrderCompletedConfigDTO;
import com.yelink.dfs.entity.common.config.dto.WorkOrderAssignmentConfigDTO;
import com.yelink.dfs.entity.common.config.dto.WorkOrderBatchConfigDTO;
import com.yelink.dfs.entity.common.config.dto.WorkOrderInvestAutoChoseProductBasicUnitConfDTO;
import com.yelink.dfs.entity.common.config.dto.WorkOrderInvestAutoClickOkConfDTO;
import com.yelink.dfs.entity.common.config.dto.WorkOrderMaterialChooseConfDTO;
import com.yelink.dfs.entity.common.config.dto.WorkOrderReportAutoPopUpConfigDTO;
import com.yelink.dfs.entity.common.config.dto.WorkOrderReportDefaultValueConfigDTO;
import com.yelink.dfs.service.common.config.BusinessConfigService;
import com.yelink.dfs.service.order.WorkOrderService;
import com.yelink.dfscommon.constant.dfs.config.ConfigConstant;
import com.yelink.dfscommon.dto.common.config.FullPathCodeDTO;
import com.yelink.dfscommon.pojo.ResponseData;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/11/1 15:01
 */
@RestController
@AllArgsConstructor
@RequestMapping("/config")
public class CommonConfigController extends BaseController {

    private final BusinessConfigService businessConfigService;
    private WorkOrderService workOrderService;

    /**
     * 业务配置-获取列表配置
     */
    @PostMapping("/list/config/get")
    public ResponseData getListConfig(@RequestBody FullPathCodeDTO dto) {
        ListConfigDTO config = businessConfigService.getValueDto(dto, ListConfigDTO.class);
        return success(config);
    }

    /**
     * 业务配置-获取仓储权限配置
     */
    @PostMapping("/list/config/getJurisdictionConfig")
    public ResponseData getJurisdictionConfig(@RequestBody FullPathCodeDTO dto) {
        JurisdictionConfigDTO config = businessConfigService.getValueDto(dto, JurisdictionConfigDTO.class);
        return success(config);
    }

    /**
     * 业务配置-获取工单报工数量校验配置
     */
    @GetMapping("/list/config/getVerReportNumConfig")
    public ResponseData getVerReportNumConfig() {
        FullPathCodeDTO dto = FullPathCodeDTO.builder()
                .fullPathCode(ConfigConstant.VER_REPORT_NUM_CONFIG).build();
        VerReportNumConfigDTO config = businessConfigService.getValueDto(dto, VerReportNumConfigDTO.class);
        return success(config);
    }

    /**
     * 业务配置-获取生产工单完工数量校验配置
     */
    @GetMapping("/list/work_order/completed/config")
    public ResponseData getWorkOrderCompletedConfig() {
        FullPathCodeDTO dto = FullPathCodeDTO.builder()
                .fullPathCode(ConfigConstant.VER_WORK_ORDER_COMPLETED_CONFIG).build();
        VerWorkOrderCompletedConfigDTO config = businessConfigService.getValueDto(dto, VerWorkOrderCompletedConfigDTO.class);
        return success(config);
    }

    /**
     * 业务配置-获取生产工单完工自动报工配置
     */
    @GetMapping("/list/work_order/auto/report/config")
    public ResponseData getWorkOrderAutoReportConfig(@RequestParam(value = "workOrderNumber") String workOrderNumber) {
        FullPathCodeDTO dto = FullPathCodeDTO.builder()
                .fullPathCode(ConfigConstant.AUTO_REPORT_AFTER_FINISH_WORK).build();
        AutoReportAfterFinishWorkConfigDTO config = businessConfigService.getValueDto(dto, AutoReportAfterFinishWorkConfigDTO.class);
        Boolean isAutoReportRecord = workOrderService.judgeIsAutoReportRecord(workOrderNumber);
        // 如果满足条件且需要弹窗确认
        if (isAutoReportRecord && config.getPopUpDisplayEnable()) {
            return success(true);
        }
        // 其他情况都返回否
        return success(false);
    }

    /**
     * 业务配置-获取作业工单报工数量校验配置
     */
    @GetMapping("/list/config/getAssignmentReportNumConfig")
    public ResponseData getAssignmentReportNumConfig() {
        FullPathCodeDTO dto = FullPathCodeDTO.builder()
                .fullPathCode(ConfigConstant.ASSIGNMENT_REPORT_NUM_CONFIG).build();
        VerReportNumConfigDTO config = businessConfigService.getValueDto(dto, VerReportNumConfigDTO.class);
        return success(config);
    }

    /**
     * 业务配置-获取列表配置
     */
    @PostMapping("/enable/config/get")
    public ResponseData getShowConfig(@RequestBody FullPathCodeDTO dto) {
        ShowConfigDTO config = businessConfigService.getValueDto(dto, ShowConfigDTO.class);
        return success(config);
    }

    /**
     * 业务配置-获取工单报工操作员是否多选配置
     */
    @GetMapping("/list/config/getMultipleOperatorConfig")
    public ResponseData getMultipleOperatorConfig() {
        FullPathCodeDTO dto = FullPathCodeDTO.builder()
                .fullPathCode(ConfigConstant.MULTIPLE_OPERATOR_CONFIG).build();
        ShowConfigDTO config = businessConfigService.getValueDto(dto, ShowConfigDTO.class);
        return success(config);
    }

    /**
     * 业务配置-获取工单报工投入数配置
     */
    @GetMapping("/list/config/getReportInputTotalConfig")
    public ResponseData getReportInputTotalConfig() {
        FullPathCodeDTO dto = FullPathCodeDTO.builder()
                .fullPathCode(ConfigConstant.REPORT_INPUT_TOTAL_CONFIG).build();
        ShowConfigDTO config = businessConfigService.getValueDto(dto, ShowConfigDTO.class);
        return success(config);
    }

    /**
     * 业务配置-采购订单-是否允许采购数量大于需求数量-查询
     */
    @GetMapping("/purchase/demand/quantity/config/get")
    public ResponseData getPurchaseDemandQuantityConfig() {
        FullPathCodeDTO dto = FullPathCodeDTO.builder()
                .fullPathCode(ConfigConstant.PURCHASE_DEMAND_QUANTITY_CONFIG).build();
        PurchaseOrderConfigDTO config = businessConfigService.getValueDto(dto, PurchaseOrderConfigDTO.class);
        return success(config);
    }

    /**
     * 业务配置-生产订单物料清单-是否启动智能推荐配置-获取
     */
    @GetMapping("/product/order/material/list/smart/config/get")
    public ResponseData getProductMaterialListSmartConfig() {
        FullPathCodeDTO dto = FullPathCodeDTO.builder()
                .fullPathCode(ConfigConstant.PRODUCT_MATERIAL_LIST_SMART_CONFIG).build();
        ProductOrderMaterialListConfigDTO config = businessConfigService.getValueDto(dto, ProductOrderMaterialListConfigDTO.class);
        return success(config);
    }

    /**
     * 业务配置-采购订单-智能推荐选项 -获取
     */
    @GetMapping("/product/order/material/list/smart/config/list")
    public ResponseData getProductMaterialListSmartConfigList() {
        List<CommonType> list = new ArrayList<>();
        for (ProductOrderMaterialListSmartConfigEnum anEnum : ProductOrderMaterialListSmartConfigEnum.values()) {
            list.add(CommonType.builder()
                    .code(anEnum.getTypeCode())
                    .name(anEnum.getTypeName())
                    .build());
        }
        return success(list);
    }

    /**
     * 业务配置-生产工单-派工配置--获取
     */
    @GetMapping("/work/order/assignment/config/get")
    public ResponseData getWorkOrderAssignmentConf() {
        FullPathCodeDTO dto = FullPathCodeDTO.builder()
                .fullPathCode(ConfigConstant.WORK_ORDER_ASSIGNMENT_CONFIG).build();
        WorkOrderAssignmentConfigDTO config = businessConfigService.getValueDto(dto, WorkOrderAssignmentConfigDTO.class);
        return success(config);
    }


    /**
     * 业务配置--销售订单编辑页--取消校验配置
     */
    @PostMapping("/list/config/get/order/editConfig/traceOrderList")
    public ResponseData getSaleOrderEditConf(@RequestBody FullPathCodeDTO dto) {
        List<String> strings = Arrays.asList(ConfigConstant.SALE_ORDER_EDIT_CONFIG_TRACE_ORDER_LIST,
                ConfigConstant.PRODUCTION_PRODUCT_ORDER_EDIT_CONFIG_TRACE_ORDER_LIST,
                ConfigConstant.PRODUCTION_WORK_ORDER_EDIT_CONFIG_TRACE_ORDER_LIST);
        if (strings.contains(dto.getFullPathCode())) {
            ShowConfigDTO config = businessConfigService.getValueDto(dto, ShowConfigDTO.class);
            return success(config);
        }
        return fail();
    }

    /**
     * 业务配置-生产批次配置
     */
    @GetMapping("/work/order/batch/config/get")
    public ResponseData getWorkOrderBatchConf() {
        FullPathCodeDTO dto = FullPathCodeDTO.builder()
                .fullPathCode(ConfigConstant.WORK_ORDER_BATCH_CONFIG).build();
        WorkOrderBatchConfigDTO config = businessConfigService.getValueDto(dto, WorkOrderBatchConfigDTO.class);
        return success(config);
    }

    /**
     * 业务配置-订单生产批次配置
     */
    @GetMapping("/product/order/batch/config/get")
    public ResponseData getProductOrderBatchConf() {
        FullPathCodeDTO dto = FullPathCodeDTO.builder()
                .fullPathCode(ConfigConstant.PRODUCT_ORDER_BATCH_CONFIG).build();
        ProductOrderBatchConfigDTO config = businessConfigService.getValueDto(dto, ProductOrderBatchConfigDTO.class);
        return success(config);
    }


    /**
     * 业务配置-生产工单报工默认值配置
     */
    @GetMapping("/work/order/report/default_value")
    public ResponseData getWorkOrderReportDefaultValueConf() {
        FullPathCodeDTO dto = FullPathCodeDTO.builder()
                .fullPathCode(ConfigConstant.WORK_ORDER_REPORT_DEFAULT_VALUE_CONFIG).build();
        WorkOrderReportDefaultValueConfigDTO config = businessConfigService.getValueDto(dto, WorkOrderReportDefaultValueConfigDTO.class);
        return success(config);
    }

    /**
     * 业务配置-投产时自动弹出报工配置
     */
    @GetMapping("/work/order/report/auto/pop_up")
    public ResponseData getWorkOrderReportAutoPopUpConf() {
        FullPathCodeDTO dto = FullPathCodeDTO.builder()
                .fullPathCode(ConfigConstant.WORK_ORDER_REPORT_AUTO_POP_UP_CONFIG).build();
        WorkOrderReportAutoPopUpConfigDTO config = businessConfigService.getValueDto(dto, WorkOrderReportAutoPopUpConfigDTO.class);
        return success(config);
    }

    /**
     * 业务配置-工艺默认值配置
     */
    @GetMapping("/craft/default_value")
    public ResponseData getCraftDefaultValueConf() {
        FullPathCodeDTO dto = FullPathCodeDTO.builder()
                .fullPathCode(ConfigConstant.CRAFT_DEFAULT_CONF).build();
        CraftDefaultConfigDTO config = businessConfigService.getValueDto(dto, CraftDefaultConfigDTO.class);
        return success(config);
    }

    /**
     * 业务配置-投产自动选生产基本单元  配置
     */
    @GetMapping("/invest/auto_chose/basic_product_unit")
    public ResponseData getInvestAutoChoseProductBasicUnitConf() {
        FullPathCodeDTO dto = FullPathCodeDTO.builder()
                .fullPathCode(ConfigConstant.WORK_ORDER_INVEST_AUTO_CHOSE_PRODUCT_BASIC_UNIT).build();
        WorkOrderInvestAutoChoseProductBasicUnitConfDTO config = businessConfigService.getValueDto(dto, WorkOrderInvestAutoChoseProductBasicUnitConfDTO.class);
        return success(config);
    }

    /**
     * 业务配置-生产工单自动点击投产  配置
     */
    @GetMapping("/invest/auto_click/ok")
    public ResponseData getInvestAutoClickOkConf() {
        FullPathCodeDTO dto = FullPathCodeDTO.builder()
                .fullPathCode(ConfigConstant.WORK_ORDER_INVEST_AUTO_CLICK_OK).build();
        WorkOrderInvestAutoClickOkConfDTO config = businessConfigService.getValueDto(dto, WorkOrderInvestAutoClickOkConfDTO.class);
        return success(config);
    }

    /**
     * 业务配置-扫订单号自动进工单页面  配置
     *
     */
    @GetMapping("/scan/auto_go/work_order_page")
    public ResponseData getScanProductOrderNumberAutoGoWorkOrderPageConf() {
        FullPathCodeDTO dto = FullPathCodeDTO.builder()
                .fullPathCode(ConfigConstant.SCAN_AUTO_GO_WORK_ORDER_PAGE).build();
        ScanAutoGoWorkOrderPageConfDTO config = businessConfigService.getValueDto(dto, ScanAutoGoWorkOrderPageConfDTO.class);
        return success(config);
    }

    /**
     * 业务配置-BOM默认值配置
     */
    @GetMapping("/bom/default_value")
    public ResponseData getBomDefaultValueConf() {
        FullPathCodeDTO dto = FullPathCodeDTO.builder()
                .fullPathCode(ConfigConstant.BOM_DEFAULT_CONF).build();
        BomDefaultConfigDTO config = businessConfigService.getValueDto(dto, BomDefaultConfigDTO.class);
        return success(config);
    }

    /**
     * 业务配置-物料损耗率算法配置
     */
    @GetMapping("/material/loss_rate/conf")
    public ResponseData getMaterialLossRateConf() {
        FullPathCodeDTO dto = FullPathCodeDTO.builder()
                .fullPathCode(ConfigConstant.MATERIAL_LOSS_RATE_CONF).build();
        MaterialLossRateConfDTO config = businessConfigService.getValueDto(dto, MaterialLossRateConfDTO.class);
        return success(config);
    }

    /**
     * 业务配置-生产工单物料选择配置
     */
    @GetMapping("/work_order/material_choose/conf")
    public ResponseData getWorkOrderMaterialChooseConf() {
        FullPathCodeDTO dto = FullPathCodeDTO.builder()
                .fullPathCode(ConfigConstant.WORK_ORDER_MATERIAL_CHOOSE_CONF).build();
        WorkOrderMaterialChooseConfDTO config = businessConfigService.getValueDto(dto, WorkOrderMaterialChooseConfDTO.class);
        return success(config);
    }

    /**
     * 业务配置-获取业务单元配置
     */
    @GetMapping("/business/unit/conf")
    public ResponseData getBusinessUnitConf() {
        FullPathCodeDTO dto = FullPathCodeDTO.builder()
                .fullPathCode(ConfigConstant.BUSINESS_UNIT_CONF).build();
        BusinessUnitConfDTO config = businessConfigService.getValueDto(dto, BusinessUnitConfDTO.class);
        return success(config);
    }

    /**
     * 业务配置-工艺参数的参数值通过业务配置来判断是否必填
     */
    @GetMapping("/craft/required/conf")
    public ResponseData getCraftRequiredConf() {
        FullPathCodeDTO dto = FullPathCodeDTO.builder()
                .fullPathCode(ConfigConstant.CRAFT_REQUIRED_CONF).build();
        CraftRequiredConfDTO config = businessConfigService.getValueDto(dto, CraftRequiredConfDTO.class);
        return success(config);
    }

    /**
     * 业务配置-工序定义关联工作中心为单选还是多选
     */
    @GetMapping("/procedure_def/relate/work_center/conf")
    public ResponseData getProcedureDefRelatedWorkCenterConf() {
        FullPathCodeDTO dto = FullPathCodeDTO.builder()
                .fullPathCode(ConfigConstant.PROCEDURE_DEF_RELATED_WORK_CENTER_CONF).build();
        CommonBooleanConfigDTO config = businessConfigService.getValueDto(dto, CommonBooleanConfigDTO.class);
        return success(config);
    }

}
