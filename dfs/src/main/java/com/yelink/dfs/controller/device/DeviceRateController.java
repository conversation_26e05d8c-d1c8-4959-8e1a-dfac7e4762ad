package com.yelink.dfs.controller.device;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yelink.dfs.common.BaseController;
import com.yelink.dfs.entity.order.WorkOrderEntity;
import com.yelink.dfs.entity.target.metrics.MetricsDeviceOeeEntity;
import com.yelink.dfs.entity.target.metrics.dto.DeviceOeeExportVO;
import com.yelink.dfs.entity.target.record.RecordDeviceDayUnionEntity;
import com.yelink.dfs.entity.target.record.RecordDeviceOrderUnionEntity;
import com.yelink.dfs.open.v1.device.dto.DeviceOeeChartDTO;
import com.yelink.dfs.service.order.WorkOrderService;
import com.yelink.dfs.service.target.record.RecordDeviceDayUnionService;
import com.yelink.dfs.service.target.record.RecordDeviceOeeService;
import com.yelink.dfs.service.target.record.RecordDeviceOrderUnionService;
import com.yelink.dfscommon.pojo.ResponseData;
import com.yelink.dfscommon.utils.EasyExcelUtil;
import com.yelink.dfscommon.utils.JacksonUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 设备OEE
 * @Date 2021/05/22
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/devices/oee")
public class DeviceRateController extends BaseController {

    private RecordDeviceOrderUnionService orderUnionService;
    private RecordDeviceDayUnionService dayUnionService;
    private RecordDeviceOeeService oeeService;
    private WorkOrderService workOrderService;


    /**
     * 获取设备联合表数据
     * 按天查看时，支持日期范围查询，分页分批次
     *
     * @param start
     * @param end
     * @param current
     * @param size
     * @return
     */
    @GetMapping("/day/list/{deviceId}")
    public ResponseData listByDay(@PathVariable(value = "deviceId") Integer deviceId,
                                  @RequestParam(value = "start", required = false) String start,
                                  @RequestParam(value = "end", required = false) String end,
                                  @RequestParam(value = "current", required = false) Integer current,
                                  @RequestParam(value = "size", required = false) Integer size) {
        Page<RecordDeviceDayUnionEntity> page = dayUnionService.getList(deviceId, start, end, current, size);
        List<RecordDeviceDayUnionEntity> records = page.getRecords();
        String orders = records.stream().map(RecordDeviceDayUnionEntity::getOrderNumber).collect(Collectors.joining(","));
        Map<String, String> stringMap = getWorkOrderMap(orders);
        for (RecordDeviceDayUnionEntity entity : records) {
            String orderNumber = entity.getOrderNumber();
            if (stringMap.containsKey(orderNumber)) {
                entity.setOrderName(stringMap.get(orderNumber));
            }
        }
        return success(page);
    }

    private Map<String, String> getWorkOrderMap(String orders) {
        //根据工单号获取所有的工单对象
        List<WorkOrderEntity> orderEntities = workOrderService.selectByNumbers(orders);
        //进行map集合(key：工单号，value：工单名称)
        return orderEntities.stream().collect(Collectors.toMap(WorkOrderEntity::getWorkOrderNumber, WorkOrderEntity::getWorkOrderName));
    }


    /**
     * 获取设备联合表数据
     * 按批次
     *
     * @param orders
     * @param current
     * @param size
     * @return
     */
    @GetMapping("/order/list/{deviceId}")
    public ResponseData listByOrder(@PathVariable(value = "deviceId") Integer deviceId,
                                    @RequestParam(value = "orders", required = false) String orders,
                                    @RequestParam(value = "current", required = false) Integer current,
                                    @RequestParam(value = "size", required = false) Integer size) {
        Page<RecordDeviceOrderUnionEntity> page = orderUnionService.getList(deviceId, orders, current, size);
        List<RecordDeviceOrderUnionEntity> records = page.getRecords();
        String collect = records.stream().map(RecordDeviceOrderUnionEntity::getOrderNumber).collect(Collectors.joining(","));
        Map<String, String> workOrderMap = getWorkOrderMap(collect);
        for (RecordDeviceOrderUnionEntity entity : records) {
            String orderNumber = entity.getOrderNumber();
            if (workOrderMap.containsKey(orderNumber)) {
                entity.setOrderName(workOrderMap.get(orderNumber));
            }
        }
        return success(page);
    }



    /**
     * 按批次查看OEE曲线
     *
     * @param orders
     * @return
     */
    @GetMapping("/order/line/{deviceId}")
    public ResponseData oeeLineByDay(@PathVariable(value = "deviceId") Integer deviceId,
                                     @RequestParam(value = "orders", required = false) String orders) {
        Map<String, RecordDeviceOrderUnionEntity> oeeLine = orderUnionService.getOEELine(deviceId, orders);
        Set<String> keySet = oeeLine.keySet();
        String join = String.join(",", keySet);
        Map<String, String> workOrderMap = getWorkOrderMap(join);
        LinkedHashMap<String, RecordDeviceOrderUnionEntity> linkedHashMap = new LinkedHashMap<>();
        for (String order : keySet) {
            if (workOrderMap.containsKey(order)) {
                linkedHashMap.put(workOrderMap.get(order), oeeLine.get(order));
            }
        }
        return success(linkedHashMap);
    }

    /**
     * 按天导出Excel
     *
     * @param start
     * @param end
     * @return
     */
    @GetMapping("/day/export")
    public void exportExcelByDay(@RequestParam(value = "deviceId") Integer deviceId,
                                 @RequestParam(value = "start", required = false) String start,
                                 @RequestParam(value = "end", required = false) String end,
                                 HttpServletResponse response) {
        dayUnionService.export(deviceId, start, end, response);
    }

    /**
     * 按批次导出Excel
     *
     * @return
     */
    @GetMapping("/order/export")
    public void exportExcelByOrder(@RequestParam(value = "deviceId") Integer deviceId,
                                   @RequestParam(value = "orders", required = false) String orders,
                                   HttpServletResponse response) {
        orderUnionService.export(deviceId, orders, response);
    }

    /**
     * 按天/周/月查看OEE曲线
     */
    @PostMapping("/time/chart")
    public ResponseData oeeChart(@RequestBody @Validated DeviceOeeChartDTO dto) {
        Map<String, MetricsDeviceOeeEntity> oeeLine = oeeService.getOeeChart(dto);
        return success(oeeLine);
    }

    /**
     * 查看设备OEE
     */
    @PostMapping("/list")
    public ResponseData oeeList(@RequestBody @Validated DeviceOeeChartDTO dto) {
        return success(oeeService.oeeList(dto));
    }
    /**
     * 查看设备OEE
     */
    @PostMapping("/list/export")
    public void oeeListExport(@RequestBody @Validated DeviceOeeChartDTO dto, HttpServletResponse response) throws IOException {
        List<MetricsDeviceOeeEntity> originList = oeeService.oeeList(dto).getRecords();
        EasyExcelUtil.export(response, "设备oee导出", "oee", JacksonUtil.convertArray(originList, DeviceOeeExportVO.class), DeviceOeeExportVO.class);
    }

}
