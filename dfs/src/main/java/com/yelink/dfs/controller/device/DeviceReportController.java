package com.yelink.dfs.controller.device;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yelink.dfs.common.BaseController;
import com.yelink.dfs.constant.OperationType;
import com.yelink.dfs.constant.device.DeviceStopTypeEnum;
import com.yelink.dfs.entity.device.dto.DeviceRunDateVo;
import com.yelink.dfs.entity.target.record.RecordDeviceDayRunEntity;
import com.yelink.dfs.entity.target.record.ReportDeviceOrderFinishEntity;
import com.yelink.dfs.service.target.record.RecordDeviceDayRunService;
import com.yelink.dfs.service.target.record.ReportDeviceOrderFinishService;
import com.yelink.dfscommon.annotation.OperLog;
import com.yelink.dfscommon.constant.ParamException;
import com.yelink.dfscommon.pojo.ResponseData;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description 设备运行记录、完成时间记录
 * @Date 2021/05/24
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/devices/report")
public class DeviceReportController extends BaseController {

    private ReportDeviceOrderFinishService orderFinishService;
    private RecordDeviceDayRunService dayRunService;

    /**
     * 获取完成时间记录列表
     *
     * @param start
     * @param end
     * @param current
     * @param size
     * @return
     */
    @GetMapping("/finish/list/{deviceId}")
    public ResponseData listFinishRecord(@PathVariable(value = "deviceId") Integer deviceId,
                                         @RequestParam(value = "start", required = false) String start,
                                         @RequestParam(value = "end", required = false) String end,
                                         @RequestParam(value = "current", required = false) Integer current,
                                         @RequestParam(value = "size", required = false) Integer size) {
        Page<ReportDeviceOrderFinishEntity> page = orderFinishService.getList(deviceId, start, end, current, size);
        return success(page);
    }

    /**
     * 查询停机类型列表
     *
     * @return
     */
    @GetMapping("/stop/type/list")
    public ResponseData getDeviceStopList() {
        List<String> list = new ArrayList<>();
        for (DeviceStopTypeEnum typeEnum : DeviceStopTypeEnum.values()) {
            list.add(typeEnum.getName());
        }
        return success(list);
    }

    /**
     * 获取设备运行时间记录列表
     *
     * @param start
     * @param end
     * @param current
     * @param size
     * @return
     */
    @GetMapping("/run/list/{deviceId}")
    public ResponseData listRunAndFinishByDay(@PathVariable(value = "deviceId") Integer deviceId,
                                              @RequestParam(value = "orderName", required = false) String orderName,
                                              @RequestParam(value = "start", required = false) String start,
                                              @RequestParam(value = "end", required = false) String end,
                                              @RequestParam(value = "current", defaultValue = "1") Integer current,
                                              @RequestParam(value = "size", defaultValue = "10") Integer size) {
        Page<DeviceRunDateVo> page = dayRunService.getRunAndFinishList(deviceId, orderName, start, end, current, size);
        return success(page);

    }


    /**
     * 添加完成时间记录【手动上报】
     * 暂时不用，废弃
     *
     * @param entity
     * @param bindingResult
     * @return
     */
    @PostMapping("/finish/add")
    public ResponseData addFinishRecord(@RequestBody ReportDeviceOrderFinishEntity entity,
                                        BindingResult bindingResult) {
        if (bindingResult.hasErrors()) {
            throw new ParamException(bindingResult);
        }
        String username = getUsername();
        entity.setCreateBy(username);
        entity.setCreateTime(new Date());
        orderFinishService.addFinishRecord(entity);
        return success();
    }

    /**
     * 修改完成时间记录
     * 暂时不用，废弃
     *
     * @param entity
     * @param bindingResult
     * @return
     */
    @PutMapping("/finish/update")
    public ResponseData updateFinishRecord(@RequestBody ReportDeviceOrderFinishEntity entity,
                                           BindingResult bindingResult) {
        if (bindingResult.hasErrors()) {
            throw new ParamException(bindingResult);
        }
        String username = getUsername();
        entity.setUpdateBy(username);
        entity.setUpdateTime(new Date());
        orderFinishService.updateFinishRecord(entity);
        return success();
    }

    /**
     * 获取完成时间记录详情
     *
     * @param id
     * @return
     */
    @GetMapping("/finish/detail/{id}")
    public ResponseData getFinishDetail(@PathVariable Integer id) {
        ReportDeviceOrderFinishEntity finishEntity = orderFinishService.getById(id);
        return success(finishEntity);
    }

    /**
     * 添加设备运行记录
     *
     * @param entity
     * @param bindingResult
     * @return
     */
    @PostMapping("/run/add")
    @OperLog(module = "设备管理", type = OperationType.ADD, desc = "新增了日期为#{recordDate}的设备设备运行记录")
    public ResponseData addRunRecord(@RequestBody RecordDeviceDayRunEntity entity,
                                     BindingResult bindingResult) {
        if (bindingResult.hasErrors()) {
            throw new ParamException(bindingResult);
        }
        String username = getUsername();
        entity.setCreateBy(username);
        entity.setCreateTime(new Date());
        dayRunService.addRunRecord(entity);
        return success(entity);
    }

    /**
     * 修改设备运行记录
     *
     * @param entity
     * @param bindingResult
     * @return
     */
    @PutMapping("/run/update")
    @OperLog(module = "设备管理", type = OperationType.UPDATE, desc = "修改了日期为#{recordDate}的设备设备运行记录")
    public ResponseData updateRunRecord(@RequestBody RecordDeviceDayRunEntity entity,
                                        BindingResult bindingResult) {
        if (bindingResult.hasErrors()) {
            throw new ParamException(bindingResult);
        }
        String username = getUsername();
        entity.setUpdateBy(username);
        entity.setUpdateTime(new Date());
        dayRunService.updateRunRecord(entity);
        return success(entity);
    }

    /**
     * 获取设备运行记录详情
     *
     * @param id
     * @return
     */
    @GetMapping("/run/detail/{id}")
    public ResponseData getRunRecordDetail(@PathVariable Integer id) {
        RecordDeviceDayRunEntity runEntity = dayRunService.getById(id);
        return success(runEntity);
    }

    /**
     * 删除设备运行记录
     *
     * @param id
     * @return
     */
    @DeleteMapping("/run/delete/{id}")
    @OperLog(module = "设备管理", type = OperationType.DELETE, desc = "删除了日期为#{recordDate}的设备设备运行记录")
    public ResponseData deleteRunRecord(@PathVariable Integer id) {
        RecordDeviceDayRunEntity entity = dayRunService.deleteRunRecord(id);
        return success(entity);
    }

}
