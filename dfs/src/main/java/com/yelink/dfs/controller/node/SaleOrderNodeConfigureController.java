package com.yelink.dfs.controller.node;

import com.yelink.dfs.common.BaseController;
import com.yelink.dfs.service.node.SaleOrderNodeConfigureService;
import com.yelink.dfscommon.pojo.ResponseData;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @Date 2022/4/24 16:08
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/sale/order/node/configure")
public class SaleOrderNodeConfigureController extends BaseController {
    private SaleOrderNodeConfigureService saleOrderNodeConfigureService;

    /**
     * 列表
     *
     * @param size
     * @param current
     * @param customerName    客户名称
     * @param saleOrderNumber 销售订单编号
     * @param materialCode    物料编号
     * @param materialName    物料名称
     * @param approvalStatus  审批状态
     * @param actualApprover  实际审批人
     * @return
     */
    @GetMapping("/list")
    public ResponseData list(@RequestParam(value = "size", required = false) Integer size,
                             @RequestParam(value = "current", required = false) Integer current,
                             @RequestParam(value = "customerName", required = false) String customerName,
                             @RequestParam(value = "saleOrderNumber", required = false) String saleOrderNumber,
                             @RequestParam(value = "materialCode", required = false) String materialCode,
                             @RequestParam(value = "materialName", required = false) String materialName,
                             @RequestParam(value = "approvalStatus", required = false) Integer approvalStatus,
                             @RequestParam(value = "actualApprover", required = false) String actualApprover) {
        return ResponseData.success(saleOrderNodeConfigureService.getList(size, current, customerName, saleOrderNumber,
                materialCode, materialName, approvalStatus, actualApprover));
    }


}
