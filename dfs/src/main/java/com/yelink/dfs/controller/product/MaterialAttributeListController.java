package com.yelink.dfs.controller.product;

import com.yelink.dfs.common.BaseController;
import com.yelink.dfscommon.entity.dfs.MaterialAttributeListEntity;
import com.yelink.dfs.entity.product.dto.MaterialAttributeListDTO;
import com.yelink.dfs.entity.product.dto.MaterialAttributeListDefaultConfigDTO;
import com.yelink.dfs.service.product.MaterialAttributeListService;
import com.yelink.dfscommon.constant.RedisKeyPrefix;
import com.yelink.dfscommon.constant.ResponseException;
import com.yelink.dfscommon.pojo.ResponseData;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @description
 * @Date 2021/4/14
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/material/attribute/lists")
public class MaterialAttributeListController extends BaseController {

    private final MaterialAttributeListService materialAttributeListService;
    private final RedisTemplate redisTemplate;

    /**
     * 查询物料属性清单
     *
     * @param
     * @return
     */
    @GetMapping("/list")
    public ResponseData getList(@RequestParam String materialCode) {
        List<MaterialAttributeListEntity> list = materialAttributeListService.getList(Stream.of(materialCode).collect(Collectors.toList()));
        return success(list);
    }

    /**
     * 根据物料类型查询物料属性清单
     *
     * @param
     * @return
     */
    @GetMapping("/list/by/type")
    public ResponseData getListByMaterialType(@RequestParam Integer materialType) {
        return success(materialAttributeListService.listByMaterialType(materialType));
    }

    /**
     * 保存物料属性清单
     *
     * @param
     * @return
     */
    @PostMapping("/save")
    public ResponseData save(@RequestBody MaterialAttributeListDTO dto) {
        dto.setUserName(getUsername());
        materialAttributeListService.save(dto);
        return success();
    }

    /**
     * 物料添加属性清单(默认项)
     *
     * @param
     * @return
     */
    @PostMapping("/add/default/config")
    public ResponseData addByProcedureDefaultConfig(@RequestBody MaterialAttributeListDefaultConfigDTO dto) {
        //分布式锁
        String key = RedisKeyPrefix.MATERIAL_ATTRIBUTE_LIST_DEFAULT_CONFIG + dto.getMaterialCode();
        Boolean lockStatus = redisTemplate.opsForValue().setIfAbsent(key, new Date(), 1, TimeUnit.MINUTES);
        if (lockStatus == null || !lockStatus) {
            throw new ResponseException("并发占用，稍后再试");
        }
        try {
            dto.setUserName(getUsername());
            materialAttributeListService.addDefaultConfig(dto);
        } finally {
            redisTemplate.delete(key);
        }
        return success();
    }

}
