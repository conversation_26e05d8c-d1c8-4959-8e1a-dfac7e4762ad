package com.yelink.dfs.controller.order;

import com.yelink.dfs.common.BaseController;
import com.yelink.dfs.entity.order.ProductionInspectionProjectTeamEntity;
import com.yelink.dfs.entity.order.dto.ProductionInspectionProjectTeamSelectDTO;
import com.yelink.dfs.service.order.ProductionInspectionProjectTeamService;
import com.yelink.dfscommon.pojo.ResponseData;
import com.yelink.dfscommon.utils.DateUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;

/**
 * 生产检查项目组表控制器
 *
 * <AUTHOR>
 * @Date 2022-04-06 18:14
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/production/inspection/project/team")
public class ProductionInspectionProjectTeamController extends BaseController {
    private ProductionInspectionProjectTeamService teamService;

    /**
     * 列表
     * 
     * @param selectDTO
     * @return
     */
    @GetMapping("/list")
    public ResponseData list(ProductionInspectionProjectTeamSelectDTO selectDTO) {
        return success(teamService.getList(selectDTO));
    }

    /**
     * 新增
     *
     * @param entity
     * @return
     * @throws Exception
     */
    @PostMapping("/insert")
    public ResponseData add(@RequestBody ProductionInspectionProjectTeamEntity entity) {
        entity.setCreateBy(getUsername());
        entity.setCreateTime(DateUtil.formatToDate(new Date(), DateUtil.DATETIME_FORMAT));
        Boolean result = teamService.add(entity);
        if (result) {
            return success(entity.getId());
        }
        return fail();
    }

    /**
     * 修改
     *
     * @param entity
     * @return
     * @throws Exception
     */
    @PutMapping("/update")
    public ResponseData edit(@RequestBody ProductionInspectionProjectTeamEntity entity) {
        entity.setUpdateBy(getUsername());
        entity.setUpdateTime(DateUtil.formatToDate(new Date(), DateUtil.DATETIME_FORMAT));
        Boolean result = teamService.edit(entity);
        if (result) {
            return success();
        }
        return fail();
    }

    /**
     * 删除
     *
     * @param id
     * @return
     */
    @DeleteMapping("/delete/{id}")
    public ResponseData remove(@PathVariable(value = "id") Integer id) {
        Boolean result = teamService.delete(id);
        if (result) {
            return success();
        }
        return fail();
    }

    /**
     * 获取详情
     *
     * @param id
     * @return
     */
    @GetMapping("/detail/{id}")
    public ResponseData getDetailById(@PathVariable(value = "id") Integer id) {
        return success(teamService.getEntityById(id));
    }
}
