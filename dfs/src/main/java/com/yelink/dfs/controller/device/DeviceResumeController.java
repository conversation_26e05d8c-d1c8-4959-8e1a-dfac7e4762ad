package com.yelink.dfs.controller.device;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yelink.dfs.common.BaseController;
import com.yelink.dfs.entity.device.dto.DeviceResumeEditDTO;
import com.yelink.dfs.entity.device.dto.DeviceResumeSelectDTO;
import com.yelink.dfs.entity.device.vo.DeviceResumeExcelVO;
import com.yelink.dfs.entity.device.vo.DeviceResumeVO;
import com.yelink.dfs.service.target.record.RecordDeviceResumeDailyService;
import com.yelink.dfscommon.pojo.ResponseData;
import com.yelink.dfscommon.utils.EasyExcelUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/devices/resume")
public class DeviceResumeController extends BaseController {

    @Resource
    private RecordDeviceResumeDailyService recordDeviceResumeDailyService;


    @GetMapping("/page")
    public ResponseData page(DeviceResumeSelectDTO dto) {
        return ResponseData.success(recordDeviceResumeDailyService.getPage(dto));
    }
    @GetMapping("/export")
    public void export(DeviceResumeSelectDTO dto, HttpServletResponse response) throws IOException {
        dto.setSize(Integer.MAX_VALUE);
        dto.setCurrent(1);
        Page<DeviceResumeVO> page = recordDeviceResumeDailyService.getPage(dto);
        List<DeviceResumeExcelVO> exportList = JSON.parseArray(JSON.toJSONString(page.getRecords()), DeviceResumeExcelVO.class);
        EasyExcelUtil.export(response, "deviceResumeFile", "deviceResumeSheet", exportList, DeviceResumeExcelVO.class);
    }
    @PostMapping("/edit")
    public ResponseData edit(@RequestBody @Validated DeviceResumeEditDTO dto) {
        recordDeviceResumeDailyService.edit(dto);
        return ResponseData.success();
    }
}
