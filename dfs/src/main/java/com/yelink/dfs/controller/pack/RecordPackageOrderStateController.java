package com.yelink.dfs.controller.pack;

import com.yelink.dfs.common.BaseController;
import com.yelink.dfs.entity.pack.RecordPackageOrderStateEntity;
import com.yelink.dfs.entity.pack.dto.PackageOrderStateDTO;
import com.yelink.dfs.service.pack.RecordPackageOrderStateService;
import com.yelink.dfscommon.pojo.ResponseData;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 包装工单
 *
 * <AUTHOR>
 * @Date 2021-11-16 17:58
 */
@Controller
@RestController
@RequestMapping("/record/package/order/states")
@AllArgsConstructor
public class RecordPackageOrderStateController extends BaseController {

    private RecordPackageOrderStateService packageOrderStateService;

    /**
     * 查询列表
     *
     * @param dto
     * @return
     */
    @PostMapping("/list")
    public ResponseData packageList(@RequestBody PackageOrderStateDTO dto) {
        List<RecordPackageOrderStateEntity> list = packageOrderStateService.getList(dto);
        return ResponseData.success(list);
    }
}
