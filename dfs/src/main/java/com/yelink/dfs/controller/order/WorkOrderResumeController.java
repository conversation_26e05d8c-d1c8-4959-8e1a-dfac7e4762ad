package com.yelink.dfs.controller.order;

import com.alibaba.fastjson.JSON;
import com.asyncexcel.core.exporter.DataExportParam;
import com.asyncexcel.core.model.ExcelTask;
import com.asyncexcel.springboot.ExcelService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yelink.dfs.common.BaseController;
import com.yelink.dfs.entity.order.dto.WorkOrderResumeSelectDTO;
import com.yelink.dfs.entity.order.vo.WorkOrderResumeExcelVO;
import com.yelink.dfs.entity.order.vo.WorkOrderResumeVO;
import com.yelink.dfs.service.order.RecordWorkOrderResumeService;
import com.yelink.dfs.service.statement.impl.WorkOrderResumeExportHandler;
import com.yelink.dfscommon.common.excel.BusinessCodeEnum;
import com.yelink.dfscommon.constant.dfs.order.WorkOrderStateEnum;
import com.yelink.dfscommon.entity.CommonType;
import com.yelink.dfscommon.pojo.ResponseData;
import com.yelink.dfscommon.utils.EasyExcelUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/work_order/resume")
public class WorkOrderResumeController extends BaseController {

    @Resource
    private RecordWorkOrderResumeService recordWorkOrderResumeService;

    @Resource
    private ExcelService excelService;

    @GetMapping("/states")
    public ResponseData page() {
        List<CommonType> r = Stream.of(WorkOrderStateEnum.INVESTMENT, WorkOrderStateEnum.HANG_UP).map(
                e -> CommonType.builder().code(e.getCode()).name(e.getName()).build()
        ).collect(Collectors.toList());
        return ResponseData.success(r);
    }

    @GetMapping("/page")
    public ResponseData page(WorkOrderResumeSelectDTO dto) {
        return ResponseData.success(recordWorkOrderResumeService.getPage(dto));
    }
    @GetMapping("/export")
    public void export(WorkOrderResumeSelectDTO dto, HttpServletResponse response) throws IOException {
        Page<WorkOrderResumeVO> page = recordWorkOrderResumeService.getPage(dto);
        List<WorkOrderResumeExcelVO> exportList = JSON.parseArray(JSON.toJSONString(page.getRecords()), WorkOrderResumeExcelVO.class);
        EasyExcelUtil.export(response, "workOrderResumeFile", "workOrderResumeSheet", exportList, WorkOrderResumeExcelVO.class);
    }
    /**
     * 默认模板
     */
    @GetMapping("/export/default_template")
    public void reporterRecordDefaultExportTemplate(HttpServletResponse response) throws IOException {
        WorkOrderResumeSelectDTO dto = new WorkOrderResumeSelectDTO();
        dto.setCurrent(1);
        dto.setSize(10);
        Page<WorkOrderResumeVO> page = recordWorkOrderResumeService.getPage(dto);
        List<WorkOrderResumeExcelVO> exportList = JSON.parseArray(JSON.toJSONString(page.getRecords()), WorkOrderResumeExcelVO.class);
        EasyExcelUtil.export(response, "工单状态履历默认导出模板", "数据源", exportList, WorkOrderResumeExcelVO.class);
    }

    /**
     * 生产工单状态履历导出 任务执行
     */
    @GetMapping("/export/excel")
    public ResponseData export(WorkOrderResumeSelectDTO dto) {
        dto.setCurrent(1);
        dto.setSize(Integer.MAX_VALUE);
        DataExportParam<WorkOrderResumeSelectDTO> param = new DataExportParam<>();
        param.setLimit(10000);
        param.setCreateUserCode(getUsername());
        param.setExportFileName(BusinessCodeEnum.WORK_ORDER_STATE_RESUME.getCodeName());
        param.setBusinessCode(BusinessCodeEnum.WORK_ORDER_STATE_RESUME.name());
        Map<String, Object> parameters = new HashMap<>(4);
        parameters.put(WorkOrderResumeSelectDTO.class.getName(), dto);
        parameters.put("cleanSheetNames", "数据源");
        parameters.put("templateId", dto.getTemplateId());
        param.setParameters(parameters);
        return ResponseData.success(excelService.doExport(param, WorkOrderResumeExportHandler.class));
    }

    /**
     * 导出日志：任务分页
     */
    @GetMapping("/export/task/page")
    public ResponseData taskPageRecord(@RequestParam(value = "currentPage", required = false, defaultValue = "1") Integer current,
                                       @RequestParam(value = "pageSize", required = false, defaultValue = "20") Integer size) {
        ExcelTask excelTask = new ExcelTask();
        excelTask.setBusinessCode(BusinessCodeEnum.WORK_ORDER_STATE_RESUME.name());
        return success(excelService.listPage(excelTask, current, size));
    }
}
