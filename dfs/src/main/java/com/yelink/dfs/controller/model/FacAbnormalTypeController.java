package com.yelink.dfs.controller.model;

import com.yelink.dfs.common.BaseController;
import com.yelink.dfs.constant.OperationType;
import com.yelink.dfs.entity.model.FacAbnormalTypeEntity;
import com.yelink.dfs.service.model.FacAbnormalTypeService;
import com.yelink.dfscommon.annotation.OperLog;
import com.yelink.dfscommon.constant.ParamException;
import com.yelink.dfscommon.pojo.ResponseData;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.Date;

/**
 * <AUTHOR>
 * @description: 工位不良类型接口
 * @time 2021/7/28 16:16
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/abnormal")
public class FacAbnormalTypeController extends BaseController {
    private FacAbnormalTypeService facAbnormalTypeService;

    @GetMapping("/list")
    public ResponseData list(@RequestParam(value = "gid") String modelId,
                             @RequestParam(value = "craftCode") String craftCode) {
        return success(facAbnormalTypeService.listType(modelId, craftCode));

    }

    @PostMapping("/insert")
    @OperLog(module = "不良品类型", type = OperationType.ADD, desc = "新增了名字为#{abnormalName}的不良品类型")
    public ResponseData add(@RequestBody @Valid FacAbnormalTypeEntity entity, BindingResult bindingResult) {
        if (bindingResult.hasErrors()) {
            throw new ParamException(bindingResult);
        }
        entity.setUpdateBy(getUsername());
        entity.setUpdateDate(new Date());
        return success(facAbnormalTypeService.save(entity));
    }

    /**
     * 通过Id删除厂区信息
     *
     * @param id
     * @return
     */
    @DeleteMapping("/delete/{id}")
    public ResponseData delete(@PathVariable Integer id) {
        return success(facAbnormalTypeService.removeById(id));
    }
}
