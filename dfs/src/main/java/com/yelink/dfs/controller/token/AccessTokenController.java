package com.yelink.dfs.controller.token;

import com.yelink.dfs.entity.terminal.dto.TokenDTO;
import com.yelink.dfs.service.keycloak.IAuthService;
import com.yelink.dfscommon.pojo.ResponseData;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.oauth2.common.OAuth2AccessToken;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Description: 获取keycloak客户端token的Controller
 * @Author: SQ
 * @Date: 2022/1/19 16:41
 * @Version:1.0
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/dfs")
public class AccessTokenController {


    private final IAuthService iAuthService;

    /**
     * 获取keycloak客户端token
     *
     * @return
     */
    @Deprecated
    @PostMapping("/oauth/token")
    public ResponseData getToken(@RequestBody @Validated TokenDTO tokenDTO) {
        OAuth2AccessToken token = iAuthService.getToken(tokenDTO.getGrant_type(), tokenDTO.getUsername(),
                tokenDTO.getPassword(), tokenDTO.getClient_id(), tokenDTO.getClient_secret(), tokenDTO.getRefresh_token());
        return ResponseData.success(token);
    }
}
