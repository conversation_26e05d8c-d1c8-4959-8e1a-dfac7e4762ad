package com.yelink.dfs.controller.feed;

import com.yelink.dfs.common.BaseController;
import com.yelink.dfs.entity.feed.FeedBackDefinitionEntity;
import com.yelink.dfs.service.feed.FeedBackDefinitionService;
import com.yelink.dfscommon.pojo.ResponseData;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;

/**
 * <AUTHOR>
 * @Description 品质反馈定义
 * @Date 2022/3/29 18:02
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/feed/back")
public class FeedBackDefinitionController extends BaseController {

    private FeedBackDefinitionService feedBackDefinitionService;

    /**
     * 获取品质反馈列表
     *
     * @param
     * @return
     */
    @PostMapping("/list")
    public ResponseData listPage(@RequestBody FeedBackDefinitionEntity feedBackDefinitionEntity) {
        return success(feedBackDefinitionService.getList(feedBackDefinitionEntity));
    }


    /**
     * 新增品质反馈定义
     *
     * @param
     * @return
     */
    @PostMapping("/add")
    public ResponseData add(@RequestBody FeedBackDefinitionEntity feedBackDefinitionEntity) {
        feedBackDefinitionEntity.setId(null);
        feedBackDefinitionEntity.setCreateBy(this.getUsername());
        feedBackDefinitionEntity.setCreateTime(new Date());
        feedBackDefinitionService.save(feedBackDefinitionEntity);
        return success();
    }

    /**
     * 修改品质反馈定义
     *
     * @param
     * @return
     */
    @PutMapping("/edit")
    public ResponseData edit(@RequestBody FeedBackDefinitionEntity feedBackDefinitionEntity) {
        feedBackDefinitionEntity.setUpdateBy(this.getUsername());
        feedBackDefinitionEntity.setUpdateTime(new Date());
        feedBackDefinitionService.updateById(feedBackDefinitionEntity);
        return success();
    }

    /**
     * 删除品质反馈定义
     *
     * @param
     * @return
     */
    @DeleteMapping("/del/{id}")
    public ResponseData edit(@PathVariable Integer id) {
        feedBackDefinitionService.removeById(id);
        return success();
    }


}
