package com.yelink.dfs.controller.order;

import com.yelink.dfs.common.BaseController;
import com.yelink.dfs.service.order.StaffPieceWorkTimeService;
import com.yelink.dfscommon.pojo.ResponseData;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @Date 2022/8/2 15:08
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/staff")
public class StaffPieceWorkTimeController extends BaseController {

    private StaffPieceWorkTimeService staffPieceWorkTimeService;


    /**
     * 列表
     *
     * @param size
     * @param current
     * @param startTime
     * @param endTime
     * @param staffName
     * @return
     */
    @GetMapping("/list")
    public ResponseData list(@RequestParam(value = "size", required = false) Integer size,
                             @RequestParam(value = "current", required = false) Integer current,
                             @RequestParam(value = "startTime", required = false) String startTime,
                             @RequestParam(value = "endTime", required = false) String endTime,
                             @RequestParam(value = "staffName", required = false) String staffName) {
        return success(staffPieceWorkTimeService.getList(size, current, startTime, endTime, staffName));
    }

    /**
     * 列表详情查询
     *
     * @param size
     * @param current
     * @param startTime
     * @param endTime
     * @param staffName
     * @return
     */
    @GetMapping("/detail/list")
    public ResponseData detailList(@RequestParam(value = "size", required = false) Integer size,
                                   @RequestParam(value = "current", required = false) Integer current,
                                   @RequestParam(value = "startTime", required = false) String startTime,
                                   @RequestParam(value = "endTime", required = false) String endTime,
                                   @RequestParam(value = "staffName", required = false) String staffName) {
        return success(staffPieceWorkTimeService.getDetailList(size, current, startTime, endTime, staffName));
    }
}
