package com.yelink.dfs.controller.model;

import cn.hutool.core.util.RandomUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yelink.dfs.common.BaseController;
import com.yelink.dfs.constant.OperationType;
import com.yelink.dfs.constant.RedisKeyPrefix;
import com.yelink.dfs.entity.common.ModelTypeDTO;
import com.yelink.dfs.entity.common.TargetGroupModelDTO;
import com.yelink.dfs.entity.device.DeviceEntity;
import com.yelink.dfs.entity.manufacture.ProductionLineEntity;
import com.yelink.dfs.entity.model.ModelEntity;
import com.yelink.dfs.entity.model.dto.ModelPageDTO;
import com.yelink.dfs.entity.model.dto.InstanceDTO;
import com.yelink.dfs.provider.constant.KafkaMessageTypeEnum;
import com.yelink.dfs.service.device.DeviceService;
import com.yelink.dfs.service.model.ModelService;
import com.yelink.dfscommon.annotation.OperLog;
import com.yelink.dfscommon.constant.Constants;
import com.yelink.dfscommon.constant.ModelEnum;
import com.yelink.dfscommon.constant.ParamException;
import com.yelink.dfscommon.constant.RespCodeEnum;
import com.yelink.dfscommon.constant.ResponseException;
import com.yelink.dfscommon.entity.dfs.DictEntity;
import com.yelink.dfscommon.pojo.ResponseData;
import com.yelink.dfscommon.provider.MessagePushToKafkaService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @description 模型类型数据接口（车间、产线、产线分支、工位）
 * @Date 2021/3/2
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/models")
public class ModelController extends BaseController {

    private ModelService modelService;
    private RedisTemplate redisTemplate;
    private DeviceService deviceService;
    private MessagePushToKafkaService messagePushToKafkaService;

    /**
     * 查询厂区类型模型列表
     */
    @GetMapping("/area/list")
    public ResponseData areaListByType() {
        return success(modelService.getListByType(ModelEnum.AREA));
    }

    /**
     * 查询产线类型模型列表
     */
    @GetMapping("/line/list")
    public ResponseData lineListByType() {
        return success(modelService.getListByType(ModelEnum.LINE));
    }

    /**
     * 查询所有的设备类型列表
     *
     * @return List<ModelEntity> 设备类型列表
     */
    @GetMapping("/device/list")
    public ResponseData getDeviceList() {
        return success(modelService.getDeviceList());
    }

    /**
     * 查询所有的工位类型列表
     *
     * @return List<ModelEntity> 工位类型列表
     */
    @GetMapping("/fac/list")
    public ResponseData getList() {
        return success(modelService.getFacList());
    }

    @PostMapping("/page")
    public ResponseData modelPage(@RequestBody @Validated ModelPageDTO dto) {
        return success(modelService.modelPage(dto));
    }

    /**
     * 查询所属类型下的模型列表（车间、产线、产线分支、工位）
     *
     * @param pid 模型父id
     * @return List<ModelEntity> 模型列表
     */
    @GetMapping("/type/{id}")
    public ResponseData getModelListByPid(@PathVariable("id") Integer pid) {
        return success(modelService.getModelListByPid(pid));
    }

    /**
     * 查询某节点以下的模型树
     * 如须获取全部节点的模型树，则传0
     *
     * @param pid 某节点的父ID
     * @return List<ModelTypeDTO> 模型树结构
     */
    @GetMapping("/tree/{id}")
    public ResponseData getTreeByPid(@PathVariable("id") Integer pid) {
        List<ModelTypeDTO> options = modelService.getTreeByPid(pid);
        return success(options);
    }

//    /**
//     * 获取指标树
//     */
//    @GetMapping("/tree/target")
//    public ResponseData getTreeByPid(@RequestParam("id") Integer id) {
//        if (Objects.isNull(id)) {
//            success(Collections.emptyList());
//        }
//        List<ModelTypeDTO> options = modelService.getTreeByPid(id);
//        return success(options);
//    }

    /**
     * 获取指标组树
     */
    @GetMapping("/tree/target_group")
    public ResponseData getTargetGroupTreeByPid(@RequestParam("pid") Integer pid,
                                                @RequestParam(value = "isFilterView", defaultValue = "false", required = false) Boolean isFilterView) {
        if (Objects.isNull(pid)) {
            success(Collections.emptyList());
        }
        List<TargetGroupModelDTO> options = modelService.getTargetGroupTreeByPid(pid, isFilterView);
        return success(options);
    }


    /**
     * 工程模型excel导入
     *
     * @param file 导入的文件
     * @throws IOException IO流传输异常
     */
    @RequestMapping(value = "/import", method = RequestMethod.POST)
    @OperLog(module = "工厂配置", type = OperationType.IMPORT, desc = "导入了#{name}模型")
    public ResponseData importExcel(MultipartFile file) throws IOException {
        modelService.importExcel(file, getUsername());
        //删除缓存中的指标，使其查询数据库，进行新增指标统计
        //controller移除缓存，service层未提交事务，移除缓存不生效
//        redisTemplate.delete(RedisKeyPrefix.TARGET_METHOD_LIST);
        redisTemplate.expire(RedisKeyPrefix.TARGET_METHOD_LIST, 1, TimeUnit.SECONDS);
        //使key过期触发回调
        redisTemplate.expire(RedisKeyPrefix.MODEL_LIST, 1, TimeUnit.SECONDS);
        return success();
    }

    /**
     * 获取所属工位类型的子父级ID
     *
     * @param id 模型id
     * @return List<Integer> 所属工位类型的子父级ID
     */
    @GetMapping("/list/{id}")
    public ResponseData getListByFacilityId(@PathVariable(value = "id") Integer id) {
        List<Integer> list = modelService.getListByFacilityId(id);
        return success(list);
    }

    /**
     * 删除模型
     *
     * @param id   模型id
     * @param type 模型类型
     * @return ModelEntity 模型对象
     */
    @DeleteMapping("/delete")
    @OperLog(module = "工厂配置", type = OperationType.DELETE, desc = "删除了#{name}模型")
    public ResponseData removeModel(@RequestParam(value = "id") Integer id,
                                    @RequestParam(value = "type") String type) {
        ModelEntity modelEntity = modelService.removeEntity(id, type);
        //controller移除缓存，service层未提交事务，移除缓存不生效
//        redisTemplate.delete(RedisKeyPrefix.TARGET_METHOD_LIST);
        redisTemplate.expire(RedisKeyPrefix.TARGET_METHOD_LIST, 1, TimeUnit.SECONDS);
        redisTemplate.expire(RedisKeyPrefix.MODEL_LIST, 1, TimeUnit.SECONDS);
        redisTemplate.delete(redisTemplate.keys(RedisKeyPrefix.getTargetModelEntity("*")));
        return ResponseData.success(modelEntity);
    }

    /**
     * 删除设备模型
     *
     * @param id
     * @return
     */
    @DeleteMapping("/device/delete/{id}")
    public ResponseData deleteModel(@PathVariable(value = "id") Integer id) {
        LambdaQueryWrapper<DeviceEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(DeviceEntity::getModelId, id);
        List<DeviceEntity> list = deviceService.list(wrapper);
        if (!CollectionUtils.isEmpty(list)) {
            throw new ResponseException(RespCodeEnum.MODEL_IS_NOT_DELETE);
        }
        ModelEntity modelEntity = modelService.getById(id);
        modelService.deleteModel(id);
        return success(modelEntity);
    }

    /**
     * 模型结果全量导出
     *
     * @param
     */
    @GetMapping("/excel/export")
    public void export(HttpServletResponse response) {
        try {
            modelService.export(response);
        } catch (Exception e) {
            log.error("导出excel异常", e);
        }
    }

    /**
     * 根据model名字拿到id
     *
     * @param modelName
     * @return
     */
    @GetMapping("/get/id/{modelName}")
    public ResponseData getIdByCode(@PathVariable(value = "modelName") String modelName) {
        LambdaQueryWrapper<ModelEntity> modelWrapper = new LambdaQueryWrapper<>();
        modelWrapper.eq(ModelEntity::getCode, modelName);
        ModelEntity modelEntity = modelService.getOne(modelWrapper);
        return ResponseData.success(modelEntity.getId());
    }

    /**
     * 获取设备模型列表
     *
     * @param currentPage
     * @param pageSize
     * @param code
     * @param name
     * @param pid
     * @return
     */
    @GetMapping("/list")
    public ResponseData list(@RequestParam(value = "currentPage", required = false) Integer currentPage,
                             @RequestParam(value = "pageSize", required = false) Integer pageSize,
                             @RequestParam(value = "code", required = false) String code,
                             @RequestParam(value = "name", required = false) String name,
                             @RequestParam(value = "pid", required = false) Integer pid,
                             @RequestParam(value = "type", required = false) String type) {
        if (StringUtils.isBlank(type)) {
            type = ModelEnum.DEVICE.getType();
        }

        return success(modelService.listByPage(currentPage, pageSize, code, name, pid, type, null, null));
    }

    /**
     * 获取设备模型详细信息
     *
     * @param id 设备id
     * @return
     */
    @GetMapping("/detail/{id}")
    public ResponseData getDetailById(@PathVariable(value = "id") Integer id) {
        ModelEntity entity = modelService.getEntityById(id);
        return success(entity);
    }


    /**
     * 新增模型
     *
     * @return ModelEntity 模型对象
     */
    @PostMapping(value = "/insert")
    @OperLog(module = "工厂配置", type = OperationType.ADD, desc = "新增了#{name}模型")
    public ResponseData insertModel(@RequestBody @Validated({ModelEntity.Insert.class}) ModelEntity modelEntity,
                                    BindingResult bindingResult) {
        if (bindingResult.hasErrors()) {
            throw new ParamException(bindingResult);
        }
        if (StringUtils.isBlank(modelEntity.getCode())) {
            modelEntity.setCode(RandomUtil.randomString(10));
        }
        modelEntity.setCreateTime(new Date());
        modelEntity.setCreateBy(getUsername());
        modelEntity.setUpdateTime(new Date());
        modelEntity.setUpdateBy(getUsername());
        if(ModelEnum.FACILITY.getType().equals(modelEntity.getType()) && modelEntity.getSeq() == null){
            int seq = modelService.lambdaQuery().eq(ModelEntity::getType, modelEntity.getType()).eq(ModelEntity::getPid, modelEntity.getPid()).list()
                    .stream().map(ModelEntity::getSeq).filter(Objects::nonNull).max(Comparator.comparing(Integer::intValue)).orElse(0);
            modelEntity.setSeq(seq + 1);
        }
        if (null != modelEntity.getSeq()) {
            modelService.addSeq(modelEntity.getSeq(), modelEntity.getPid());
        }
        // 模型的code只能为数字
        /*if (!StringJudgmentUtil.isFloat(modelEntity.getCode())) {
            throw new ResponseException(RespCodeEnum.MODEL_CODE_NOT_IS_NUMBER);
        }*/
        boolean save = modelService.save(modelEntity);
        //推送消息
        messagePushToKafkaService.pushNewMessage(modelEntity, Constants.KAFKA_MAIN_DATA_TOPIC, KafkaMessageTypeEnum.MODEL_ADD_MESSAGE);
        redisTemplate.expire(RedisKeyPrefix.MODEL_LIST, 1, TimeUnit.SECONDS);
        return save ? success() : fail("新增失败");
    }

    /**
     * 编辑模型
     *
     * @return ModelEntity 模型对象
     */
    @PostMapping(value = "/update")
    @OperLog(module = "工厂配置", type = OperationType.UPDATE, desc = "修改了#{name}模型")
    public ResponseData updateModel(@RequestBody @Validated({ModelEntity.Update.class}) ModelEntity modelEntity, BindingResult bindingResult) {
        if (bindingResult.hasErrors()) {
            throw new ParamException(bindingResult);
        }
        modelEntity.setUpdateTime(new Date());
        modelEntity.setUpdateBy(getUsername());
        modelService.updateModel(modelEntity);
        //使key过期触发回调
        redisTemplate.expire(RedisKeyPrefix.MODEL_LIST, 1, TimeUnit.SECONDS);
        return success(modelEntity);
    }

    /**
     * 获取所有车间的模型树
     *
     * @return ModelEntity 模型对象
     */
    @GetMapping("/getTree")
    public ResponseData getTree() {
        List<ModelTypeDTO> options = modelService.getTreeByPid(2);
        return success(options);
    }

    /**
     * 删除模型
     *
     * @param id   模型id
     * @param type 模型类型
     * @return ModelEntity 模型对象
     */
    @DeleteMapping("/DeleteTreeByType")
    @OperLog(module = "工厂配置", type = OperationType.DELETE, desc = "删除了#{name}模型")
    public ResponseData DeleteTreeByType(@RequestParam(value = "id") Integer id,
                                         @RequestParam(value = "type") String type) {
        ModelEntity modelEntity = modelService.deleteTreeByType(id, type);
        //controller移除缓存，service层未提交事务，移除缓存不生效
//        redisTemplate.delete(RedisKeyPrefix.TARGET_METHOD_LIST);
        redisTemplate.expire(RedisKeyPrefix.TARGET_METHOD_LIST, 1, TimeUnit.SECONDS);
        redisTemplate.delete(redisTemplate.keys(RedisKeyPrefix.getTargetModelEntity("*")));
        //使key过期触发回调
        redisTemplate.expire(RedisKeyPrefix.MODEL_LIST, 1, TimeUnit.SECONDS);
        return ResponseData.success(modelEntity);
    }

    /**
     * 添加设备模型
     *
     * @param modelEntity
     * @return
     */
    @PostMapping("/device/insert")
    public ResponseData add(@RequestBody ModelEntity modelEntity) {
        modelEntity.setCreateBy(getUsername());
        modelEntity.setCreateTime(new Date());
        modelService.addDeviceModel(modelEntity);
        return success(modelEntity);
    }

    /**
     * 根据产线模型id 获取产线
     *
     * @param modelId
     * @return
     */
    @GetMapping("/line/by/model")
    public ResponseData getLineByModel(@RequestParam(value = "modelId") Integer modelId) {
        List<ProductionLineEntity> list = modelService.getLineByModel(modelId);
        return success(list);
    }

    /**
     * 获取工厂模型实例
     *
     * @return
     */
    @GetMapping("/factory/instance")
    public ResponseData getFactoryModelInstance() {
        List<InstanceDTO> factoryModelInstance = modelService.getFactoryModelInstance();
        return success(factoryModelInstance);
    }

    /**
     * 获取产线模型-工位模型的树结构
     *
     * @param
     * @return
     */
    @GetMapping("/line/fac/model/tree")
    public ResponseData getLineFacModelTree() {
        return success(modelService.getLineFacModelTree());
    }

    /**
     * 根据工序ID获取绑定的产线-工位模型树
     *
     * @param
     * @return
     */
    @GetMapping("/line/fac/model/tree/{procedureId}")
    public ResponseData getLineFacModelTreeByProcedureId(@PathVariable(value = "procedureId") Integer procedureId) {
        return success(modelService.getLineFacModelTreeByProcedureId(procedureId));
    }

    /**
     * 添加工装类型
     * 20220916
     *
     * @param dictEntity
     * @return
     */
    @PostMapping("/process/insert")
    public ResponseData addProcessAssembly(@RequestBody DictEntity dictEntity) {
        dictEntity.setCreateBy(getUsername());
        dictEntity.setCreateTime(new Date());
        dictEntity.setUpdateBy(getUsername());
        dictEntity.setUpdateTime(new Date());
        modelService.addProcessModel(dictEntity);
        return success(dictEntity);
    }

    /**
     * 获取工装类型
     * 20220916
     *
     * @return
     */
    @GetMapping("/process/list")
    public ResponseData getProcessAssembly() {
        List<DictEntity> entities = modelService.getProcessAssembly();
        return success(entities);
    }

    /**
     * 获取工装类型分页
     * 20220916
     *
     * @return
     */
    @GetMapping("/process/list/page")
    public ResponseData getProcessAssembly(@RequestParam(value = "current", required = false) Integer current,
                                           @RequestParam(value = "size", required = false) Integer size,
                                           @RequestParam(value = "code") String code,
                                           @RequestParam(value = "name") String name) {
        Page<DictEntity> entity = modelService.getProcessAssemblyPage(current, size, code, name);
        return success(entity);
    }

    /**
     * 删除工装类型
     * 20220916
     *
     * @return
     */
    @DeleteMapping("/process/delete/{id}")
    public ResponseData deleteProcessAssembly(@PathVariable(value = "id") Integer id) {
        modelService.deleteDict(id);
        return success();
    }

    /**
     * 更新工装类型
     * 20220916
     *
     * @return
     */
    @PutMapping("/process/update")
    public ResponseData updateProcessAssembly(@RequestBody DictEntity dictEntity) {
        modelService.updateEntityById(dictEntity);
        return success();
    }

    @GetMapping("/list-all")
    public ResponseData listAll(@RequestParam String type) {
        return success(modelService.lambdaQuery().eq(ModelEntity::getType, type).list());
    }


}
