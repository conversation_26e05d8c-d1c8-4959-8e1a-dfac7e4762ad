package com.yelink.dfs.controller.code;

import com.yelink.dfs.common.BaseController;
import com.yelink.dfs.constant.code.CodeResolverTypeEnum;
import com.yelink.dfs.entity.code.CodeResolverRuleEntity;
import com.yelink.dfs.entity.code.dto.CodeResolverRuleTypeDTO;
import com.yelink.dfs.service.code.CodeResolverRuleService;
import com.yelink.dfscommon.pojo.ResponseData;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Description 扫码规则
 * @Date 2022/3/31 14:36
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/code/resolver/rule")
public class CodeResolverRuleController extends BaseController {

    private CodeResolverRuleService codeResolverRuleService;

    /**
     * 获取扫码规则列表
     *
     * @return
     */
    @GetMapping("/list")
    public ResponseData list(@RequestParam(value = "code", required = false) String code,
                             @RequestParam(value = "name", required = false) String name,
                             @RequestParam(value = "lineId", required = false) Integer lineId,
                             @RequestParam(value = "codePrefix", required = false) String codePrefix,
                             @RequestParam(value = "currentPage", required = false) Integer currentPage,
                             @RequestParam(value = "size", required = false) Integer size) {
        return success(codeResolverRuleService.list(code, name, codePrefix, lineId, currentPage, size));
    }

    /**
     * 获取质检关联类型
     *
     * @return
     */
    @GetMapping("/type/list")
    public ResponseData getRelatedTypes() {
        CodeResolverTypeEnum[] values = CodeResolverTypeEnum.values();
        List<CodeResolverRuleTypeDTO> list = new ArrayList<>(values.length);
        for (CodeResolverTypeEnum typeEnum : values) {
            list.add(CodeResolverRuleTypeDTO.builder().code(typeEnum.getCode()).name(typeEnum.getName()).build());
        }
        return success(list);
    }

    /**
     * 添加条扫码规则
     *
     * @param codeResolverRuleEntity
     * @return
     */
    @PostMapping("/add")
    public ResponseData add(@RequestBody CodeResolverRuleEntity codeResolverRuleEntity) {
        codeResolverRuleEntity.setId(null);
        codeResolverRuleEntity.setCreateBy(getUsername());
        codeResolverRuleEntity.setCreateTime(new Date());
        codeResolverRuleService.add(codeResolverRuleEntity);
        return success();
    }

    /**
     * 修改扫码规则
     *
     * @param codeResolverRuleEntity
     * @return
     */
    @PutMapping("edit")
    public ResponseData edit(@RequestBody CodeResolverRuleEntity codeResolverRuleEntity) {
        codeResolverRuleEntity.setUpdateBy(getUsername());
        codeResolverRuleEntity.setUpdateTime(new Date());
        codeResolverRuleService.edit(codeResolverRuleEntity);
        return success();
    }

    /**
     * 删除扫码规则
     *
     * @param id
     * @return
     */
    @DeleteMapping("delete/{id}")
    public ResponseData delete(@PathVariable Integer id) {
        codeResolverRuleService.removeById(id);
        return success();
    }

}
