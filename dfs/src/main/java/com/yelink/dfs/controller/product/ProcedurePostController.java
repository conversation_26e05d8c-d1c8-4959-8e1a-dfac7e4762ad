package com.yelink.dfs.controller.product;

import com.yelink.dfs.common.BaseController;
import com.yelink.dfs.constant.model.VersionModelIdEnum;
import com.yelink.dfs.entity.common.VersionChangeCraftProcedureDTO;
import com.yelink.dfs.entity.common.VersionChangeDTO;
import com.yelink.dfs.entity.product.ProcedurePostEntity;
import com.yelink.dfs.service.common.VersionChangeRecordService;
import com.yelink.dfs.service.product.ProcedurePostService;
import com.yelink.dfs.service.user.SysPostService;
import com.yelink.dfscommon.entity.dfs.SysPostEntity;
import com.yelink.dfscommon.pojo.ResponseData;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * @Description: 工序人力管理
 * @Author: shenzm
 * @Date: 2022/9/21
 */
@Slf4j
@RestController
@RequestMapping("/procedure/post")
public class ProcedurePostController extends BaseController {
    @Resource
    private ProcedurePostService procedurePostService;
    @Resource
    private SysPostService sysPostService;
    @Resource
    private VersionChangeRecordService versionChangeRecordService;

    /**
     * 根据工序id获取工序人力信息
     *
     * @return
     */
    @GetMapping("/getListById/{procedureId}")
    public ResponseData getListById(@PathVariable(value = "procedureId") Integer id) {
        List<ProcedurePostEntity> entities = procedurePostService.getListById(id);
        return success(entities);
    }

    /**
     * 根据工序id获取工序人力信息
     *
     * @return
     */
    @GetMapping("/detail/{id}")
    public ResponseData getDetail(@PathVariable(value = "id") Integer id) {
        ProcedurePostEntity entity = procedurePostService.getById(id);
        return success(entity);
    }

    /**
     * 新增工序人力信息
     *
     * @return
     */
    @PostMapping("/insert")
    public ResponseData insert(@RequestBody ProcedurePostEntity entity) {
        entity.setCreateBy(getUsername());
        entity.setUpdateBy(getUsername());
        entity.setCreateTime(new Date());
        entity.setUpdateTime(new Date());
        if (StringUtils.isNotEmpty(entity.getPostCode())) {
            SysPostEntity sysPost = sysPostService.lambdaQuery().eq(SysPostEntity::getPostCode, entity.getPostCode()).one();
            entity.setPostId(sysPost.getId());
        }
        boolean save = procedurePostService.saveEntity(entity);
        if (!save) {
            return fail();
        }
        versionChangeRecordService.changeVersionCraftProcedure(VersionChangeCraftProcedureDTO.builder()
                .description("人力 - 新增")
                .craftProcedureId(entity.getProcedureId())
                .build()
        );
        return success();
    }



    /**
     * 更新工序工序人力信息
     *
     * @return
     */
    @PutMapping("/update")
    public ResponseData update(@RequestBody ProcedurePostEntity entity) {
        entity.setUpdateBy(getUsername());
        entity.setUpdateTime(new Date());
        //前端没传postID,
        if (StringUtils.isNotEmpty(entity.getPostCode())) {
            SysPostEntity sysPost = sysPostService.lambdaQuery().eq(SysPostEntity::getPostCode, entity.getPostCode()).one();
            entity.setPostId(sysPost.getId());
        }
        boolean b = procedurePostService.updateById(entity);
        if (!b) {
            return fail();
        }
        versionChangeRecordService.changeVersionCraftProcedure(VersionChangeCraftProcedureDTO.builder()
                .description("人力 - 更新")
                .craftProcedureId(entity.getProcedureId())
                .build()
        );
        return success();
    }

    /**
     * 根据工序id删除对应工序人力信息
     *
     * @return
     */
    @DeleteMapping("/deleteByProcedureId/{procedureId}")
    public ResponseData deleteByProcedureId(@PathVariable(value = "procedureId") Integer id) {
        boolean b = procedurePostService.deleteByProcedureId(id);
        if (!b) {
            return fail();
        }
        return success();
    }

    /**
     * 根据工序质检id删除工序人力信息
     *
     * @return
     */
    @DeleteMapping("/delete/{id}")
    public ResponseData delete(@PathVariable(value = "id") Integer id) {
        ProcedurePostEntity entity = procedurePostService.getById(id);
        boolean b = procedurePostService.removeById(id);
        if (!b) {
            return fail();
        }
        versionChangeRecordService.changeVersionCraftProcedure(VersionChangeCraftProcedureDTO.builder()
                .description("人力 - 删除")
                .craftProcedureId(entity.getProcedureId())
                .build()
        );

        return success();
    }

    /**
     * 新增、修改、删除工序人力信息
     *
     * @return
     */
 /*   @PostMapping("/insertOrDelete")
    public ResponseData insertOrDelete(@RequestBody ProcedurePostEntity entity) {
        entity.setCreateBy(getUsername());
        entity.setUpdateBy(getUsername());
        entity.setCreateTime(new Date());
        entity.setUpdateTime(new Date());
        boolean b = procedurePostService.insertOrDelete(entity);
        if (!b) {
            return fail();
        }
        return success();
    }*/


}
