package com.yelink.dfs.controller.statement;


import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.databind.JsonNode;
import com.yelink.dfs.common.BaseController;
import com.yelink.dfs.constant.statement.SourceTypeEnum;
import com.yelink.dfs.entity.statement.ManageSourceEntity;
import com.yelink.dfs.entity.statement.dto.CodeNameDTO;
import com.yelink.dfs.service.statement.ManageSourceService;
import com.yelink.dfscommon.pojo.ResponseData;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 报表管理(DfsManageReport)表控制层
 *
 * <AUTHOR>
 * @since 2023-07-04 10:39:41
 */
@RestController
@AllArgsConstructor
@RequestMapping("/manage")
public class ManageSourceController extends BaseController {
    private final ManageSourceService manageSourceService;

    /**
     * 查询可以添加自定义数据源的自定义指标
     */
    @GetMapping("/report/list/target")
    public ResponseData listTarget(@RequestParam(required = false) String sourceType) {
        List<JSONObject> targets = manageSourceService.listTarget(sourceType);
        return ResponseData.success(targets);
    }
    /**
     * 来源类型枚举
     */
    @GetMapping("/report/list/source/type")
    public ResponseData listSourceType() {
        List<CodeNameDTO> states = new ArrayList<>();
        for (SourceTypeEnum typeEnum : SourceTypeEnum.values()) {
            states.add(CodeNameDTO.builder().code(typeEnum.getCode()).name(typeEnum.getName()).build());
        }
        return ResponseData.success(states);
    }
    /**
     * 新增 自定义数据源
     */
    @PostMapping("/report/add/data/source")
    public ResponseData addDataSource(@RequestBody List<ManageSourceEntity> sources) {
        manageSourceService.addDataSource2(sources);
        return ResponseData.success(true);
    }

    /**
     * 自定义数据源列表
     */
    @GetMapping("/report/list/data/source")
    public ResponseData listDataSource(@RequestParam Integer current,
                                       @RequestParam Integer size,
                                       @RequestParam(required = false) String sourceType) {
        Page<ManageSourceEntity> page = manageSourceService.listBySourceType(current, size, sourceType);
        return ResponseData.success(page);
    }
    /**
     * 编辑/更新自定义数据源
     */
    @PostMapping("/report/update/data/source")
    public ResponseData updateDataSource(@RequestBody ManageSourceEntity manageSource) {
        manageSourceService.updateDataSource(manageSource);
        return ResponseData.success(true);
    }
    /**
     * 自定义数据源详情
     */
    @GetMapping("/report/data/source/detail")
    public ResponseData dataSourceDetail(@RequestParam String sourceCode) {
        ManageSourceEntity source = manageSourceService.dataSourceDetail(sourceCode);
        return ResponseData.success(source);
    }
    /**
     * 删除自定义数据源
     */
    @PostMapping("/report/delete/data/source")
    public ResponseData deleteDataSource(@RequestBody List<String> sourceCodes) {
        manageSourceService.deleteDataSource(sourceCodes);
        return ResponseData.success(true);
    }

    /**
     * api自定义数据源 - 测试预览
     */
    @PostMapping("/source/test/transform")
    public ResponseData testTransform(@RequestBody ManageSourceEntity source) {
        List<JsonNode> excResult = manageSourceService.testTransform(source);
        return ResponseData.success(excResult);
    }

    /**
     * 添加的api数据集-执行
     */
    @GetMapping("/source/api/source/execute")
    public ResponseData apiSourceExecute(@RequestParam String sourceCode) {
        ManageSourceEntity manageSource = manageSourceService.lambdaQuery()
                .eq(ManageSourceEntity::getSourceCode, sourceCode)
                .one();
        List<JsonNode> excResult = new ArrayList<>();
        if (Objects.nonNull(manageSource) && StringUtils.isNotBlank(manageSource.getApiUrl())) {
            excResult = manageSourceService.testTransform(manageSource);
        }
        return ResponseData.success(excResult);
    }



}

