package com.yelink.dfs.controller.product;

import com.yelink.dfs.common.BaseController;
import com.yelink.dfs.entity.product.ProcedureDefectSchemeEntity;
import com.yelink.dfs.service.product.ProcedureDefectSchemeService;
import com.yelink.dfscommon.pojo.ResponseData;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;
import java.util.List;

/**
 * @Description: 工序质检管理
 * @Author: shenzm
 * @Date: 2022/9/20
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/procedure/defect/scheme")
public class ProcedureDefectSchemeController extends BaseController {

    private ProcedureDefectSchemeService procedureDefectSchemeService;

    /**
     * 根据工序id获取工序质检信息
     * @return
     */
    @GetMapping("/getListById/{procedureId}")
    public ResponseData getListById(@PathVariable(value = "procedureId") Integer id) {
        List<ProcedureDefectSchemeEntity> entities = procedureDefectSchemeService.getListById(id);
        return success(entities);
    }

    /**
     * 根据工序id获取工序质检信息
     *
     * @return
     */
    @GetMapping("/detail/{id}")
    public ResponseData getDetail(@PathVariable(value = "id") Integer id) {
        ProcedureDefectSchemeEntity entity = procedureDefectSchemeService.getById(id);
        return success(entity);
    }

    /**
     * 新增工序质检信息
     *
     * @return
     */
   /* @PostMapping("/insert")
    public ResponseData insert(@RequestBody ProcedureDefectSchemeEntity entity) {
        entity.setCreateBy(getUsername());
        entity.setUpdateBy(getUsername());
        entity.setCreateTime(new Date());
        entity.setUpdateTime(new Date());
        boolean save = procedureDefectSchemeService.save(entity);
        if (!save) {
            return fail();
        }
        return success();
    }*/

    /**
     * 更新工序工序质检信息
     *
     * @return
     */
    @PutMapping("/update")
    public ResponseData update(@RequestBody ProcedureDefectSchemeEntity entity) {
        entity.setUpdateBy(getUsername());
        entity.setUpdateTime(new Date());
        boolean b = procedureDefectSchemeService.updateById(entity);
        if (!b) {
            return fail();
        }
        return success();
    }

    /**
     * 根据工序id删除对应工序质检信息
     *
     * @return
     */
    @DeleteMapping("/deleteByProcedureId/{procedureId}")
    public ResponseData deleteByProcedureId(@PathVariable(value = "procedureId") Integer id) {
        boolean b = procedureDefectSchemeService.deleteByProcedureId(id);
        if (!b) {
            return fail();
        }
        return success();
    }

    /**
     * 根据工序质检id删除工序质检信息
     *
     * @return
     */
    @DeleteMapping("/delete/{id}")
    public ResponseData delete(@PathVariable(value = "id") Integer id) {
        boolean b = procedureDefectSchemeService.removeById(id);
        if (!b) {
            return fail();
        }
        return success();
    }

    /**
     * 新增、修改、删除工序质检信息
     *
     * @return
     */
    @PostMapping("/insertOrDelete")
    public ResponseData insertOrDelete(@RequestBody ProcedureDefectSchemeEntity entity) {
        entity.setCreateBy(getUsername());
        entity.setUpdateBy(getUsername());
        entity.setCreateTime(new Date());
        entity.setUpdateTime(new Date());
        boolean b = procedureDefectSchemeService.insertOrDelete(entity);
        if (!b) {
            return fail();
        }
        return success();
    }


}
