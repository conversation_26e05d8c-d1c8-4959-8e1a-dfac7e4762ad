package com.yelink.dfs.controller.task;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yelink.dfs.common.BaseController;
import com.yelink.dfs.service.task.TaskConfigService;
import com.yelink.dfscommon.entity.CommonType;
import com.yelink.dfs.entity.task.dto.TaskConfigSelectDTO;
import com.yelink.dfs.entity.task.dto.TaskConfigUpdateDTO;
import com.yelink.dfs.entity.task.vo.TaskConfigVO;
import com.yelink.dfscommon.pojo.ResponseData;
import com.yelink.sdk.task.constant.OrderCategoryEnum;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;


/**
 * 任务配置
 * <AUTHOR>
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/taskConfig")
public class TaskConfigController extends BaseController {

    private TaskConfigService taskConfigService;

    /**
     * 任务配置 分页
     */
    @PostMapping("/page")
    public ResponseData page(@RequestBody TaskConfigSelectDTO dto) {
        Page<TaskConfigVO> page =taskConfigService.pageConfig(dto);
        return ResponseData.success(page);
    }

    @GetMapping("/detail")
    public ResponseData detail(@RequestParam String orderCategory) {
        return ResponseData.success(taskConfigService.detail(orderCategory));
    }

    /**
     * 更新任务配置
     */
    @PostMapping("/update")
    public ResponseData update(@RequestBody @Validated TaskConfigUpdateDTO dto) {
        taskConfigService.updateConfig(dto);
        return ResponseData.success();
    }

    /**
     * 单据类型枚举
     */
    @GetMapping("/orderCategoryEnum")
    public ResponseData orderCategoryEnum() {
        List<CommonType> result = Arrays.stream(OrderCategoryEnum.values()).map(
                e -> CommonType.builder().code(e.getCode()).name(e.getName()).build()
        ).collect(Collectors.toList());
        return ResponseData.success(result);
    }
    @GetMapping("/orderCategoryColumn")
    public ResponseData orderCategoryColumn(@RequestParam String orderCategory) {
        return ResponseData.success(taskConfigService.orderCategoryColumn(orderCategory));
    }
}
