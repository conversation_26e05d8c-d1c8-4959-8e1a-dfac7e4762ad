package com.yelink.dfs.controller.common;

import com.yelink.dfs.common.BaseController;

import com.yelink.dfs.entity.common.ColumnConfigurationDetailEntity;
import com.yelink.dfs.entity.common.ColumnConfigurationEntity;
import com.yelink.dfs.service.common.ColumnConfigurationDetailService;
import com.yelink.dfs.service.common.ColumnConfigurationService;
import com.yelink.dfscommon.pojo.ResponseData;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR> @Date 2022/5/12 11:16
 */
@RestController
@Slf4j
@AllArgsConstructor
@RequestMapping("/column/configuration/")
public class ColumnConfigurationController extends BaseController {

    private ColumnConfigurationService columnConfigurationService;
    private ColumnConfigurationDetailService columnConfigurationDetailService;

    /**
     * 通过用户名和类型查找对应的方案列表
     * @param modularType
     * @return
     */
    @GetMapping("get/by/user")
    public ResponseData getConfigurationByUser(@RequestParam(value = "modularType") String modularType) {
        ColumnConfigurationEntity configurationByUser = columnConfigurationService.getConfigurationByUser(getUsername(), modularType);
        return ResponseData.success(configurationByUser);
    }

    /**
     * 首次进入Tab页通过用户名和类型查找指定使用的配置方案
     * 当isDefault为TRUE时使用前端的列配置
     * 当isDefault为FALSE则查询配置的列配置信息中开启isUser为TRUE的配置
     * 否则还是取前端配置
     * @return
     */
    @GetMapping("get/appoint/configuration")
    public ResponseData getAppointConfiguration(@RequestParam(value = "modularType") String modularType) {
        return ResponseData.success(columnConfigurationService.getAppointConfiguration(getUsername(), modularType));
    }

    /**
     * 通过id查询具体的列配置方案
     * @return
     */
    @GetMapping("get/configuration/detail/{id}")
    public ResponseData getConfigurationById(@PathVariable Integer id) {
        return ResponseData.success(columnConfigurationDetailService.getById(id));
    }


    /**
     * 保存列配置信息
     *
     * @return
     */
    @PostMapping("save/entity")
    public ResponseData saveEntity(@RequestBody ColumnConfigurationEntity columnConfigurationEntity) {
        columnConfigurationEntity.setUserName(getUsername());
        columnConfigurationService.saveEntity(columnConfigurationEntity);
        return ResponseData.success();
    }


    /**
     * 更新列配置信息
     *
     * @return
     */
    @PutMapping("update/entity")
    public ResponseData updateEntity(@RequestBody ColumnConfigurationEntity columnConfigurationEntity) {
        columnConfigurationService.updateEntity(columnConfigurationEntity);
        return ResponseData.success();
    }

    /**
     * 更新具体列配置信息
     *
     * @return
     */
    @PutMapping("update/entity/detail")
    public ResponseData updateEntityDetail(@RequestBody ColumnConfigurationDetailEntity columnConfigurationDetailEntity) {
        columnConfigurationService.updateEntityDetail(columnConfigurationDetailEntity);
        return ResponseData.success();
    }

    /**
     * 通过列配置详情id删除列配置
     * 如果列配置中不包含子项则需要把列配置删除
     *
     * @return
     */
    @DeleteMapping("delete/detail/{id}")
    public ResponseData deleteEntityByDetailId(@PathVariable Integer id) {
        columnConfigurationService.deleteEntityByDetailId(id);
        return ResponseData.success();
    }

}
