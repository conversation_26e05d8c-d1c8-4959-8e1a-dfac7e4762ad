package com.yelink.dfs.controller.model;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yelink.dfs.common.BaseController;
import com.yelink.dfs.constant.OperationType;
import com.yelink.dfs.entity.manufacture.ProductionLineEntity;
import com.yelink.dfs.entity.model.GridEntity;
import com.yelink.dfs.entity.user.MyAuthenticationUserDetail;
import com.yelink.dfs.open.v1.model.dto.GridPageDTO;
import com.yelink.dfs.service.manufacture.ProductionLineService;
import com.yelink.dfs.service.model.GridService;
import com.yelink.dfscommon.annotation.OperLog;
import com.yelink.dfscommon.constant.ParamException;
import com.yelink.dfscommon.constant.RespCodeEnum;
import com.yelink.dfscommon.pojo.ResponseData;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;
import java.util.Objects;


/**
 * @description: 车间管理接口
 * -- 接口数据验证待补充
 * @author: shuang
 * @time: 2020/12/9
 */
@Slf4j
@RestController
@RequestMapping("/grids")
@AllArgsConstructor
public class GridController extends BaseController {

    private GridService gridService;
    private ProductionLineService productionLineService;

    /**
     * 查询指定id车间详细信息
     *
     * @param id
     * @return
     */
    @GetMapping("/detail/{id}")
    public ResponseData detail(@PathVariable Integer id) {
        GridEntity gridEntity = gridService.getDetailById(id);
        if (gridEntity == null) {
            fail(RespCodeEnum.GRID_FAIL2SEL);
        }
        return success(gridEntity);
    }


    /**
     * 分页条件、模糊查询本厂区下的车间列表
     *
//     * @param aid         厂区ID
//     * @param gcode       车间编码
//     * @param gname       车间名称
//     * @param currentPage
//     * @param pageSize
     * @return
     */
    @GetMapping("/list")
    public ResponseData list(GridPageDTO dto) {
        return success(gridService.list(dto.getAid(), dto.getGcode(), dto.getGname(), dto.getCurrent(), dto.getSize(), dto.getGcodes(), dto.getGnames()));
    }

    /**
     * 查询厂区下车间列表
     *
     * @param aid
     * @param currentPage
     * @param pageSize
     * @return
     */
    @GetMapping("/list/area")
    public ResponseData gridByAid(
            @RequestParam(value = "aid") Integer aid,
            @RequestParam(value = "currentPage", defaultValue = "1") Integer currentPage,
            @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize) {
        Page<GridEntity> list = gridService.gridByAid(aid, currentPage, pageSize);
        return success(list);


    }


    /**
     * 添加车间并绑定员工（员工为负责人）
     *
     * @param gridEntity
     * @return
     */
    @PostMapping("/add")
    @OperLog(module = "工厂配置", type = OperationType.ADD, desc = "新增了车间编码为#{gcode}的车间信息")
    public ResponseData add(@RequestBody @Valid GridEntity gridEntity, BindingResult bindingResult) {
        if (bindingResult.hasErrors()) {
            throw new ParamException(bindingResult);
        }
        MyAuthenticationUserDetail userInfo = getUserInfo();
        int compId = userInfo.getCompId();
        gridEntity.setCid(compId);
        gridEntity.setCreateBy(userInfo.getNickname());
        gridEntity.setUpdateBy(userInfo.getNickname());
        gridService.add(gridEntity);
        return success(gridEntity);
    }

    /**
     * 通过车间id删除车间
     *
     * @param id
     * @return
     */
    @DeleteMapping("/delete/{id}")
    @OperLog(module = "工厂配置", type = OperationType.DELETE, desc = "删除了车间编码为#{gcode}，名称为#{gname}的车间信息")
    public ResponseData delete(@PathVariable Integer id) {
        return success(gridService.deleteGrid(id, getUsername()));
    }

    /**
     * 通过车间id更新车间信息
     *
     * @param gridEntity
     * @return
     */
    @PostMapping("/update")
    @OperLog(module = "工厂配置", type = OperationType.UPDATE, desc = "修改了车间编码为#{gcode}的车间信息")
    public ResponseData update(@RequestBody GridEntity gridEntity) {
        gridService.edit(gridEntity);
        return success(gridEntity);
    }

    /**
     * 查询本厂区下的车间列表
     */
    @GetMapping("/selectAll")
    public ResponseData list(@RequestParam(required = false) Integer aid) {
        List<GridEntity> girds = gridService.lambdaQuery()
                .eq(Objects.nonNull(aid), GridEntity::getAid, aid)
                .list();
        return success(girds);
    }

    /**
     * 根据id查询车间详情
     */
    @GetMapping("/select/detail")
    public ResponseData getDetailByid(@RequestParam("id") Integer id) {
        return success(gridService.getDetailById(id));
    }


    /**
     * 查询所有车间及其产线
     *
     * @return
     */
    @GetMapping("/list/with/lines")
    public ResponseData getGridWithLines() {
        List<GridEntity> list = gridService.getGridWithLines();
        return success(list);


    }

    /**
     * 查询车间可选制造单元列表
     */
    @GetMapping("/select/line")
    public ResponseData selectLine(@RequestParam(value = "id",required = false) Integer id) {
        LambdaQueryWrapper<ProductionLineEntity> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(ProductionLineEntity::getGid,id).or().isNull(ProductionLineEntity::getGid);
        return success(productionLineService.list(lambdaQueryWrapper));
    }

}
