package com.yelink.dfs.controller.furnace;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yelink.dfs.common.BaseController;
import com.yelink.dfs.constant.Constant;
import com.yelink.dfs.constant.OperationType;
import com.yelink.dfs.entity.furnace.ConfigDefValEntity;
import com.yelink.dfs.entity.furnace.FurnaceEntity;
import com.yelink.dfs.entity.furnace.dto.FurnaceDto;
import com.yelink.dfs.entity.furnace.dto.LaboratoryReportDto;
import com.yelink.dfs.entity.furnace.dto.SecondaryMaterialDto;
import com.yelink.dfs.entity.order.WorkOrderEntity;
import com.yelink.dfs.service.furnace.ConfigDefValService;
import com.yelink.dfs.service.furnace.FurnaceService;
import com.yelink.dfs.service.furnace.SecondaryMaterialService;
import com.yelink.dfscommon.annotation.OperLog;
import com.yelink.dfscommon.pojo.ResponseData;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;
import java.util.List;

/**
 * 控制器
 *
 * <AUTHOR>
 * @Date 2021-08-03 12:02
 * 炉次信息
 */

@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/furnaces")
public class FurnaceController extends BaseController {

    private FurnaceService furnaceService;
    private SecondaryMaterialService secondaryMaterialService;
    private ConfigDefValService configDefValService;

    /**
     * 炉次信息列表
     *
     * @param furnaceCode
     * @param deviceId
     * @param workOrderNumber
     * @param startTime
     * @param endTime
     * @return
     */
    @GetMapping("/list")
    public ResponseData getList(@RequestParam(value = "furnaceCode", required = false) String furnaceCode,
                                @RequestParam(value = "deviceId", required = false) Integer deviceId,
                                @RequestParam(value = "fid", required = false) Integer fid,
                                @RequestParam(value = "workOrderNumber", required = false) String workOrderNumber,
                                @RequestParam(value = "state", required = false) Integer state,
                                @RequestParam(value = "startTime", required = false) String startTime,
                                @RequestParam(value = "endTime", required = false) String endTime,
                                @RequestParam(value = "current", required = false) Integer current,
                                @RequestParam(value = "size", required = false) Integer size) {
        Page<FurnaceEntity> page = furnaceService.getList(furnaceCode, deviceId, fid, workOrderNumber, state, startTime, endTime, current, size);
        return ResponseData.success(page);
    }

    /**
     * 修改炉次确认信息
     *
     * @param entity
     * @return
     */
    @PutMapping("/edit")
    @OperLog(module = "炉次管理", type = OperationType.UPDATE, desc = "更新了炉次号#{furnaceCode}的炉次信息")
    public ResponseData editFurnace(@RequestBody @Validated({FurnaceEntity.Update.class}) FurnaceEntity entity) {
        String username = getUsername();
        entity.setUpdateBy(username);
        entity.setUpdateTime(new Date());
        FurnaceEntity result = furnaceService.editFurnace(entity);
        return ResponseData.success(result);
    }

    /**
     * 查询相对应的炉次详细信息
     *
     * @param fid
     * @return
     */
    @GetMapping("/detail")
    public ResponseData getDetail(@RequestParam(value = "fid") Integer fid) {
        FurnaceEntity entity = furnaceService.getFurnaceDetail(fid);
        return ResponseData.success(entity);
    }

    /**
     * 炉次追溯--化验结果
     *
     * @param
     * @return
     */
    @GetMapping("/trace/laboratory")
    public ResponseData getLaboratoryTrace(@RequestParam(value = "furnaceCode") String furnaceCode,
                                           @RequestParam(value = "fid") Integer fid) {
        List<LaboratoryReportDto> dto = secondaryMaterialService.getAllTestAndMaterial(furnaceCode, fid);
        return success(dto);
    }

    /**
     * 炉次追溯--累计辅料用量
     *
     * @param
     * @return
     */
    @GetMapping("/trace/secondary/material")
    public ResponseData getSecondaryMaterialTrace(@RequestParam(value = "furnaceCode") String furnaceCode,
                                                  @RequestParam(value = "fid") Integer fid) {
        List<SecondaryMaterialDto> list = furnaceService.getSecondaryMaterialTrace(furnaceCode, fid);
        return success(list);
    }

    /**
     * 获取当前冶炼中电炉信息（第几炉及对应吨数）
     * 仅电炉
     *
     * @param
     * @return
     */
    @GetMapping("/num/tonnage")
    public ResponseData getFurnaceNumAndTonnage() {
        FurnaceDto dto = furnaceService.getFurnaceNumAndTonnage();
        return success(dto);
    }

    /**
     * 获取当前冶炼中炉信息（第几炉及对应吨数）
     * 电炉/精炼炉
     *
     * @param
     * @return
     */
    @GetMapping("/num/tonnage/info")
    public ResponseData getFurnaceNumAndTonnage(@RequestParam(value = "furnaceCode") String furnaceCode,
                                                @RequestParam(value = "fid") Integer fid) {
        FurnaceDto dto = furnaceService.getFurnaceNumAndTonnage(furnaceCode, fid);
        return success(dto);
    }

    /**
     * 生成炉次号
     *
     * @param
     * @return
     */
    @GetMapping("/generate/furnace/num")
    public ResponseData generateFurnaceNum(@RequestParam(value = "fid") Integer fid) {
        String username = getUsername();
        // 生成炉次
        String furnaceNum = furnaceService.add(fid, username);
        return success(furnaceNum);
    }

    /**
     * 提交粗炼炉数据到精炼炉室
     *
     * @param furnaceCode 炉次号
     * @return
     */
    @GetMapping("/submit/refine/furnace")
    public ResponseData submitToRefiningFurnace(@RequestParam(value = "furnaceCode") String furnaceCode,
                                                @RequestParam(value = "sequence", required = false) Integer sequence) {
        furnaceService.submitToRefiningFurnace(furnaceCode, sequence, getUsername());
        return success();
    }

    /**
     * 绑定该工位 当天正在投产状态下的工单(只会存在一个工单)
     *
     * @param
     * @return
     */
    @GetMapping("/investment/order")
    public ResponseData getInvestmentOrder(@RequestParam(value = "fid") Integer fid) {
        WorkOrderEntity workOrderEntity = furnaceService.getInvestment(fid);
        return success(workOrderEntity == null ? null : workOrderEntity.getWorkOrderNumber());
    }

    /**
     * 获取工位出钢量缺省值
     *
     * @param fid 工位Id
     * @return
     */
    @GetMapping("/get/tapping/def/val")
    public ResponseData getTappingCapacityDefVal(@RequestParam(value = "fid") Integer fid) {
        ConfigDefValEntity entity = configDefValService.getTappingCapacityDefVal(fid);
        return success(entity);
    }

    /**
     * 新增/编辑工位出钢量缺省值
     *
     * @param
     * @return
     */
    @PutMapping("/update/tapping/def/val")
    public ResponseData updateTappingCapacityDefVal(@RequestBody ConfigDefValEntity entity) {
        entity.setCode(Constant.TAPPING_CAPACITY_EN);
        entity.setName(Constant.TAPPING_CAPACITY_CN);
        configDefValService.saveOrUpdateByFid(entity);
        return success();
    }

    /**
     * 获取辅料绑定的缺省值
     *
     * @param fid  工位ID
     * @param code 辅料编码
     * @return
     */
    @GetMapping("/get/material/def/val")
    public ResponseData getSecMaterialDefVal(@RequestParam(value = "fid") Integer fid,
                                             @RequestParam(value = "code") String code) {
        ConfigDefValEntity entity = configDefValService.getSecMaterialDefVal(fid, code);
        return success(entity);
    }

    /**
     * 新增/编辑辅料绑定的缺省值
     *
     * @param
     * @return
     */
    @PutMapping("/update/material/def/val")
    public ResponseData updateSecMaterialDefVal(@RequestBody ConfigDefValEntity entity) {
        configDefValService.saveOrUpdateByFidCode(entity);
        return success();
    }

}
