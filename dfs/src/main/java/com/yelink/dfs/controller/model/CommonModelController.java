package com.yelink.dfs.controller.model;

import cn.hutool.core.util.RandomUtil;
import com.yelink.dfs.common.UserAuthenService;
import com.spire.xls.Workbook;
import com.yelink.dfs.entity.model.vo.FileDetailVO;
import com.yelink.dfs.service.model.excel.CommonModelExcel;
import com.yelink.dfscommon.constant.CommonEnum;
import com.yelink.dfscommon.constant.ListDisplayFormatEnum;
import com.yelink.dfscommon.constant.ResponseException;
import com.yelink.dfscommon.pojo.ResponseData;
import com.yelink.dfscommon.utils.DateUtil;
import com.yelink.dfscommon.utils.ExcelUtil;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/common/model")
public class CommonModelController {
    private static final String IMPORT_LOCK = "MODEL_EXCEL_IMPORT_LOCK";
    private static final String IMPORT_PROGRESS = "MODEL_EXCEL_IMPORT_PROGRESS:";
    @Resource
    private CommonModelExcel commonModelExcel;
    @Resource
    private RedissonClient redissonClient;
    @Resource
    private UserAuthenService userAuthenService;

    @GetMapping("/export")
    public void exportExcel(HttpServletResponse response, @RequestParam(defaultValue = "false") Boolean isTemplate) throws IOException {
        commonModelExcel.exportExcel(response, isTemplate);
    }

    @PostMapping("/import")
    public ResponseData importExcel(MultipartFile file) throws IOException, InterruptedException {
        RLock lock = redissonClient.getLock(IMPORT_LOCK);
        int waitTime = 60;
        int leaseTime = 5 * 60;
        String progressKey = IMPORT_PROGRESS + DateUtil.format(new Date(), DateUtil.yyyyMMddHHmmss_FORMAT) + "_" + RandomUtil.randomString(2);
        try {
            if (lock.tryLock(waitTime, leaseTime, TimeUnit.SECONDS)) {
                ExcelUtil.checkImportExcelFile(file);
                InputStream sourceInputStream = file.getInputStream();
                //加载Excel文档
                Workbook wb = new Workbook();
                wb.loadFromStream(sourceInputStream);
                // 由于后面的操作是异步的，这里需要先将文件的信息保留下来，避免连接断开后，临时文件被框架删除而找不到
                FileDetailVO fileDetail = FileDetailVO.builder().originalFilename(file.getOriginalFilename()).contentType(file.getContentType()).build();
                commonModelExcel.importExcel(wb, fileDetail, userAuthenService.getUsername(), progressKey);

            }else {
                throw new ResponseException("当前已有任务在执行，请稍后再试");
            }
        } finally {
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
        return ResponseData.success(progressKey);
    }

    @GetMapping("/material/order/list")
    public ResponseData getMaterialOrderEnum()  {
        ListDisplayFormatEnum[] values = ListDisplayFormatEnum.values();
        List<CommonEnum> commonEnumList = new ArrayList<>();
        for (ListDisplayFormatEnum formatEnum : values) {
            commonEnumList.add(CommonEnum.builder()
                    .code(formatEnum.getKey())
                    .name(formatEnum.getVal())
                    .build());
        }
        return ResponseData.success(commonEnumList);
    }

    @GetMapping("/import/progress")
    public ResponseData getMaterialCompletenessProgress(@RequestParam String key) {
        return ResponseData.success(commonModelExcel.importProgress(key));
    }
}
