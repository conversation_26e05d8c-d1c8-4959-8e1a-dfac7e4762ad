package com.yelink.dfs.controller.product;

import cn.hutool.core.util.RandomUtil;
import com.asyncexcel.core.model.ExcelTask;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yelink.dfs.common.BaseController;
import com.yelink.dfs.constant.Constant;
import com.yelink.dfs.constant.OperationType;
import com.yelink.dfs.constant.RedisKeyPrefix;
import com.yelink.dfs.constant.product.BomItemTypeEnum;
import com.yelink.dfs.constant.product.ProductionStateEnum;
import com.yelink.dfs.constant.product.BomRawMaterialEnum;
import com.yelink.dfs.constant.product.BomStateEnum;
import com.yelink.dfs.entity.common.CommonState;
import com.yelink.dfs.entity.common.CommonType;
import com.yelink.dfs.entity.common.ModelUploadFileEntity;
import com.yelink.dfs.entity.product.BomEntity;
import com.yelink.dfs.entity.product.dto.BomCopyDTO;
import com.yelink.dfs.entity.product.dto.BomSelectByMaterialDetailDTO;
import com.yelink.dfs.entity.product.dto.BomSelectDTO;
import com.yelink.dfs.entity.product.dto.DetailBomExportDTO;
import com.yelink.dfs.entity.product.dto.ImpactAnalysisSelectDTO;
import com.yelink.dfs.entity.product.dto.MaterialRelateDataDTO;
import com.yelink.dfs.entity.product.dto.SimpleBomExportDTO;
import com.yelink.dfs.entity.product.vo.ReplaceSchemeRelateBomVO;
import com.yelink.dfs.open.v1.material.dto.MaterialDetailDTO;
import com.yelink.dfs.open.v1.production.dto.BomNumbersDTO;
import com.yelink.dfs.open.v1.production.dto.BomReplaceMaterialSelectDTO;
import com.yelink.dfs.provider.FastDfsClientService;
import com.yelink.dfs.service.common.ImportProgressService;
import com.yelink.dfs.service.common.ModelUploadFileService;
import com.yelink.dfs.service.product.BomService;
import com.yelink.dfscommon.annotation.OperLog;
import com.yelink.dfscommon.api.wms.StockInventoryDetailInterface;
import com.yelink.dfscommon.common.unit.config.UnitFormatMethod;
import com.yelink.dfscommon.constant.CommonEnum;
import com.yelink.dfscommon.constant.ParamException;
import com.yelink.dfscommon.constant.RespCodeEnum;
import com.yelink.dfscommon.constant.ResponseException;
import com.yelink.dfscommon.dto.ApproveBatchDTO;
import com.yelink.dfscommon.entity.ams.dto.PushDownMaterialDTO;
import com.yelink.dfscommon.entity.common.BatchChangeStateDTO;
import com.yelink.dfscommon.entity.wms.StockMaterialDetailEntity;
import com.yelink.dfscommon.pojo.ResponseData;
import com.yelink.dfscommon.utils.ColumnUtil;
import com.yelink.dfscommon.utils.DateUtil;
import com.yelink.dfscommon.utils.EasyExcelUtil;
import com.yelink.dfscommon.utils.ExcelTemplateImportUtil;
import com.yelink.dfscommon.utils.ExcelUtil;
import com.yelink.dfscommon.utils.JacksonUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description bom清单
 * @Date 2021/4/6
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/boms")
public class BomController extends BaseController {

    private BomService bomService;
    private StockInventoryDetailInterface inventoryDetailInterface;
    private ModelUploadFileService modelUploadFileService;
    private FastDfsClientService fastDfsClientService;
    private ImportProgressService importProgressService;

    /**
     * 查询BOM列表
     */
    @PostMapping("/list")
//    @UnitFormatMethod(UnitFormatMethod.MethodType.RESPONSE)
    public ResponseData list(@RequestBody BomSelectDTO selectDTO) {
        Page<BomEntity> list = bomService.list(selectDTO);
        return success(list);
    }

    /**
     * 新增BOM
     * (1.3.2版本为成品、半成品、原料)
     * (1.3.3版本为物品)，
     * 如果为成品/半成品//物品，则同时新增成品/半成品与原料//物品的绑定关系
     *
     * @param bomEntity     BOM对象
     * @param bindingResult
     * @return
     */
    @PostMapping("/insert")
//    @UnitFormatMethod
    @OperLog(module = "产品管理", type = OperationType.ADD, desc = "新增了BOM编码为#{bomNum}的BOM")
    public ResponseData add(@RequestBody @Valid BomEntity bomEntity, BindingResult bindingResult) {
        if (bindingResult.hasErrors()) {
            throw new ParamException(bindingResult);
        }
        bomEntity.setCreateBy(getUsername());
        bomEntity.setUpdateBy(getUsername());
        bomService.saveEntity(bomEntity);
        return success(bomEntity);
    }

    /**
     * 新增生效BOM
     *
     * @param bomEntity
     * @param bindingResult
     * @return
     */
    @PostMapping("/insert/released")
//    @UnitFormatMethod
    @OperLog(module = "产品管理", type = OperationType.ADD, desc = "新增了BOM编码为#{bomNum}的BOM")
    public ResponseData addReleasedBom(@RequestBody @Valid BomEntity bomEntity, BindingResult bindingResult) {
        if (bindingResult.hasErrors()) {
            throw new ParamException(bindingResult);
        }
        String username = getUsername();
        bomEntity.setCreateBy(username);
        bomEntity.setUpdateBy(username);
        bomEntity.setCreateTime(new Date());
        bomEntity.setUpdateTime(new Date());
        bomService.saveReleasedEntity(bomEntity);
        return success(bomEntity);
    }

    /**
     * 更新BOM，同时更新成品/半成品与原料的绑定关系
     *
     * @param bomEntity BOM对象
     * @return
     */
    @PutMapping("/update")
//    @UnitFormatMethod
    @OperLog(module = "产品管理", type = OperationType.UPDATE, desc = "修改了bom编码为#{bomNum}的BOM")
    public ResponseData update(@RequestBody @Validated BomEntity bomEntity) {
        bomEntity.setUpdateTime(new Date());
        bomEntity.setUpdateBy(getUsername());
        bomService.updateEntity(bomEntity);
        return success(bomEntity);
    }

    /**
     * 删除BOM对象
     *
     * @param id BOMID
     * @return
     */
    @DeleteMapping("/delete/{id}")
    @OperLog(module = "产品管理", type = OperationType.DELETE, desc = "删除了bom编码为#{bomNum}的BOM")
    public ResponseData delete(@PathVariable Integer id) {
        BomEntity entity = bomService.getById(id);
        bomService.removeEntityById(id);
        return success(entity);
    }

    /**
     * 批量删除BOM对象
     *
     * @param
     * @return
     */
    @PostMapping("/batch/delete")
    public ResponseData batchDelete(@RequestBody BomNumbersDTO bomNumbersDTO) {
        String code = RandomUtil.randomString(10);
        bomService.batchDelete(bomNumbersDTO, code, getUsername());
        return success(code, null);
    }

    /**
     * 查询BOM详细信息
     *
     * @param id BOMId
     * @return
     */
    @GetMapping("/detail/{id}")
//    @UnitFormatMethod(UnitFormatMethod.MethodType.RESPONSE)
    public ResponseData detail(@PathVariable(value = "id") Integer id) {
        return ResponseData.success(bomService.selectById(id));
    }

    /**
     * 查询BOM关联的替代物料列表
     *
     * @return
     */
    @PostMapping("/replace_material/list")
    public ResponseData getReplaceMaterialsByBomCode(@RequestBody BomReplaceMaterialSelectDTO selectDTO) {
        return ResponseData.success(bomService.getReplaceMaterialsByBomCode(selectDTO));
    }

    /**
     * 通过成品编码查询最新的bom
     *
     * @param code
     * @return
     */
    @GetMapping("/latest/{code}")
//    @UnitFormatMethod(UnitFormatMethod.MethodType.RESPONSE)
    public ResponseData getLatestBomByCode(@PathVariable(value = "code") String code,
                                           @RequestParam(required = false) Integer skuId) {
        return ResponseData.success(bomService.getLatestBomByCode(code, skuId));
    }


    /**
     * 通过成品编码查询最新的bom(批量)
     *
     * @param codeList
     * @return
     */
    @PostMapping("/latest/list")
//    @UnitFormatMethod(UnitFormatMethod.MethodType.RESPONSE)
    public ResponseData getLatestBomByCode(@RequestBody List<String> codeList) {
        List<BomEntity> bomEntities = new ArrayList<>();
        if (CollectionUtils.isEmpty(codeList)) {
            return  success(bomEntities);
        }
        codeList = codeList.stream().distinct().collect(Collectors.toList());
        for (String code : codeList) {
            BomEntity bomEntity = bomService.getLatestBomByCode(code, null);
            if(bomEntity!=null) {
                bomEntities.add(bomEntity);
            }
        }
        return ResponseData.success(bomEntities);
    }

    /**
     * 获取BOM状态
     *
     * @return
     */
    @GetMapping("/state")
    public ResponseData getBomState() {
        List<CommonState> list = new ArrayList<>();
        BomStateEnum[] values = BomStateEnum.values();
        for (BomStateEnum value : values) {
            CommonState type = new CommonState();
            type.setCode(value.getCode());
            type.setName(value.getName());
            list.add(type);
        }
        return success(list);
    }


    /**
     * 数据分析
     * 如果需要停用、废除某个BOM,需查询所有关联该BOM的数据
     * 相关模块：工单
     *
     * @param bomNum 物料单号
     */
    @GetMapping("/analysis")
    public ResponseData judgeIsExistRelateData(@RequestParam(value = "bomNum") String bomNum) {
        List<MaterialRelateDataDTO> dtos = bomService.getRelateData(bomNum);
        return success(dtos);
    }

    /**
     * 更新审核、批准状态
     */
    @PutMapping("/update/state/{state}")
    @OperLog(module = "产品管理", type = OperationType.UPDATE, desc = "修改了bom编码为#{bomNum}的BOM")
    public ResponseData updateState(@PathVariable(value = "state") Integer state,
                                    @RequestParam(value = "id") Integer id) {
        String username = getUsername();
        if (bomService.updateState(state, id, username)) {
            return success();
        }
        return fail();
    }

    /**
     * 审批
     */
    @GetMapping("/examine/approve")
    public ResponseData examineOrApprove(@RequestParam(value = "approvalStatus") Integer approvalStatus,
                                         @RequestParam(value = "approvalSuggestion", required = false) String approvalSuggestion,
                                         @RequestParam(value = "id") Integer id) {
        String username = getUsername();
        bomService.examineOrApprove(approvalStatus, approvalSuggestion, id, username);
        return success();
    }

    /**
     * 批量审批
     *
     * @return
     */
    @PostMapping("/approve/batch")
    public ResponseData approveBatch(@RequestBody ApproveBatchDTO dto) {
        dto.setActualApprover(getUsername());
        bomService.approveBatch(dto);
        return success();
    }

    /**
     * BOM批量编辑状态
     */
    @PostMapping("/batch/update/state")
    public ResponseData batchUpdate(@RequestBody BatchChangeStateDTO batchApprovalDTO) {
        if (batchApprovalDTO.getIds() == null) {
            return fail("编辑的单据id为空");
        }
        String username = getUsername();
        Boolean ret = bomService.batchUpdateState(batchApprovalDTO, username);
        return ResponseData.success(ret);
    }

    /**
     * 导入BOM模板到本地
     */
    @PostMapping("/import/template")
    @OperLog(module = "导入日志", type = OperationType.ADD, desc = "导入bom自定义模板")
    public ResponseData importTemplateExcel(MultipartFile file) {
        //bomService.importTemplateExcel(file);
        bomService.uploadCustomTemplate(file, this.getUsername());
        return success();
    }


    /**
     * 导入原始数据并处理
     */
    @PostMapping("/import")
    public ResponseData importExcel(MultipartFile file) throws IOException {
        //1、判断导入文件的合法性
        ExcelUtil.checkImportExcelFile(file);
        String importProgressKey = RedisKeyPrefix.BOM_IMPORT_PROGRESS + DateUtil.format(new Date(), DateUtil.yyyyMMddHHmmss_FORMAT) + "_" + RandomUtil.randomString(2);
        bomService.sycImportData(file.getOriginalFilename(), file.getInputStream(), getUsername(), importProgressKey);
        return success(importProgressKey);
    }

    /**
     * 获取bom导入的进度
     *
     * @return
     */
    @GetMapping("/import/progress")
    public ResponseData getMaterialCompletenessProgress() {
        return success(bomService.importProgress());
    }

    /**
     * 下载导入模板
     */
    @GetMapping("/export/template")
    public void exportTemplateExcel(HttpServletResponse response) throws IOException {
        //找到导入的模板
        byte[] bytes = bomService.downloadImportTemplate();
        ExcelTemplateImportUtil.responseToClient(response, bytes, "BOM数据导入模板" + Constant.XLSX);
    }

    /**
     * 导入原始数据并处理
     */
    @PostMapping("/copyImport")
    public ResponseData copyImport(MultipartFile file) throws IOException {
        //1、判断导入文件的合法性
        ExcelUtil.checkImportExcelFile(file);
        bomService.copyImport(file.getOriginalFilename(), file.getInputStream(), this.getUsername());
        return success();
    }
    @GetMapping("/copyImport/progress")
    public ResponseData getImportProgress() {
        return success(importProgressService.importProgress(RedisKeyPrefix.BOM_COPY_IMPORT_PROGRESS));
    }
    /**
     * bom默认复制导入：默认模板下载
     */
    @GetMapping("/copyImport/template")
    public void downloadMaterialUsedDefaultTemplate(HttpServletResponse response) throws IOException {
        //找到导出的模板
        byte[] bytes = ExcelUtil.exportDefaultExcel("classpath:template/bomCopyTemplate.xlsx");
        ExcelTemplateImportUtil.responseToClient(response, bytes, "bom默认复制导入模板" + Constant.XLSX);
    }

    /**
     * 下载导出默认模板
     */
    @GetMapping("/export/default/template")
    public void exportDefaultTemplateExcel(HttpServletResponse response) throws Exception {
        //找到导出的模板
        bomService.downloadDefaultTemplate("classpath:template/BomTemplate.zip", response, "BOM默认模板" + Constant.ZIP);
    }

    /**
     * 导入管理：下载自定义导入模板
     *
     * @param response
     */
    @GetMapping("/export/custom/template")
    public void exportCustomExcel(HttpServletResponse response) throws IOException {
        //找到导出的模板
        InputStream inputStream = bomService.downloadCustomImportTemplate();
        ExcelTemplateImportUtil.responseToClient(response, inputStream, "BOM自定义数据导入模板" + com.yelink.dfscommon.constant.Constant.XLSX);
    }

    /**
     * BOM基本信息列表导出 - 下载默认模板
     * 查询10条BOM基本信息数据导出数据源excel
     *
     * @param response
     * @throws IOException
     */
    @GetMapping("/simple/default/export/template")
    public void simpleListDefaultExportTemplate(HttpServletResponse response) throws IOException {
        // 获取最新的十条数据源
        BomSelectDTO selectDTO = new BomSelectDTO();
        selectDTO.setCurrent(1);
        selectDTO.setSize(10);
        Page<BomEntity> page = bomService.list(selectDTO);
        List<SimpleBomExportDTO> list = bomService.convertToSimpleBomExportDTO(page.getRecords());
        EasyExcelUtil.export(response, "Bom基本信息默认导出模板", "数据源", list, SimpleBomExportDTO.class);
    }

    /**
     * BOM基本信息列表导出 - 自定义模板上传
     * 接收文件并清空名称为数据源sheet的内容
     *
     * @param file
     * @return
     */
    @PostMapping("/simple/upload/list/export/template")
    public ResponseData uploadSimpleListExportTemplate(MultipartFile file) throws IOException {
        bomService.uploadSimpleListExportTemplate(file, getUsername());
        return success();
    }

    /**
     * BOM基本信息列表导出 - BOM基本信息自定义模板下载
     * 分页查询并填写数据源sheet的内容
     *
     * @param response
     * @return
     * @throws IOException
     */
    @PostMapping("/simple/download/list/export/template")
    public ResponseData downloadSimpleListExportTemplate(HttpServletResponse response, Integer id) throws IOException {
        ModelUploadFileEntity uploadFile = modelUploadFileService.getById(id);
        if (Objects.isNull(uploadFile)) {
            throw new ResponseException(RespCodeEnum.TEMPLATE_NOT_EXIST);
        }
        byte[] bytes = fastDfsClientService.getFileStream(uploadFile.getFileAddress());
        ExcelTemplateImportUtil.responseToClient(response, bytes, uploadFile.getFileName());
        return success();
    }

    /**
     * BOM基本信息导出  :异步
     *
     * @param selectDTO
     * @return
     * @throws IOException
     */
    @PostMapping("/simple/syc/exports")
    public ResponseData simpleListExportTask(@RequestBody BomSelectDTO selectDTO) {
        Long result = bomService.simpleListExportTask(selectDTO, getUsername());
        return success(result);
    }

    /**
     * 分页查询当前导出任务列表
     *
     * @param pageSize
     * @param currentPage
     * @return
     */
    @GetMapping("/simple/export/task/page")
    public ResponseData taskSimplePage(@RequestParam(required = false, defaultValue = "20") Integer pageSize,
                                       @RequestParam(required = false, defaultValue = "1") Integer currentPage,
                                       @RequestParam(required = false) Boolean exportMultiLevelBom) {
        IPage<ExcelTask> iPage = bomService.simpleTaskPage(currentPage, pageSize, exportMultiLevelBom);
        return success(iPage);
    }

    /**
     * 查询 BOM基本信息导入进度
     *
     * @return
     */
    @GetMapping("/simple/export/task/{taskId}")
    public ResponseData simpleListTaskById(@PathVariable Long taskId, @RequestParam(required = false) Boolean exportMultiLevelBom) {
        ExcelTask excelTask = bomService.simpleListTaskById(taskId, exportMultiLevelBom);
        return success(excelTask);
    }

    /**
     * BOM详细信息列表导出 （包括子物料） - 下载默认模板
     * 查询10条BOM详细信息数据导出数据源excel
     *
     * @param response
     * @throws IOException
     */
    @GetMapping("/default/export/template")
    public void detailListDefaultExportTemplate(HttpServletResponse response) throws IOException {
        // 获取最新的十条数据源
        BomSelectDTO selectDTO = new BomSelectDTO();
        selectDTO.setCurrent(1);
        selectDTO.setSize(10);
        Page<BomEntity> page = bomService.list(selectDTO);
        List<DetailBomExportDTO> list = bomService.convertToDetailBomExportDTO(page.getRecords(), true);
        EasyExcelUtil.export(response, "Bom详细信息默认导出模板", "数据源", list, DetailBomExportDTO.class);
    }

    /**
     * BOM详细信息列表导出 （包括子物料）- 自定义模板上传
     * 接收文件并清空名称为数据源sheet的内容
     *
     * @param file
     * @return
     */
    @PostMapping("/upload/list/export/template")
    public ResponseData uploadDetailListExportTemplate(MultipartFile file) throws IOException {
        bomService.uploadDetailListExportTemplate(file, getUsername());
        return success();
    }

    /**
     * BOM详细信息列表导出 （包括子物料） - BOM详细信息自定义模板下载
     * 分页查询并填写数据源sheet的内容
     *
     * @param response
     * @return
     * @throws IOException
     */
    @PostMapping("/download/list/export/template")
    public ResponseData downloadDetailListExportTemplate(HttpServletResponse response, Integer id) throws IOException {
        ModelUploadFileEntity uploadFile = modelUploadFileService.getById(id);
        if (Objects.isNull(uploadFile)) {
            throw new ResponseException(RespCodeEnum.TEMPLATE_NOT_EXIST);
        }
        byte[] bytes = fastDfsClientService.getFileStream(uploadFile.getFileAddress());
        ExcelTemplateImportUtil.responseToClient(response, bytes, uploadFile.getFileName());
        return success();
    }

    /**
     * BOM详细信息导出  :异步 （包括子物料）
     *
     * @param selectDTO
     * @return
     * @throws IOException
     */
    @PostMapping("/syc/exports")
    public ResponseData detailListExportTask(@RequestBody BomSelectDTO selectDTO) {
        Long result = bomService.detailListExportTask(selectDTO, getUsername());
        return success(result);
    }

    /**
     * 分页查询当前导出任务列表 （包括子物料）
     *
     * @param pageSize
     * @param currentPage
     * @return
     */
    @GetMapping("/export/task/page")
    public ResponseData taskDetailPage(@RequestParam(required = false, defaultValue = "20") Integer pageSize,
                                       @RequestParam(required = false, defaultValue = "1") Integer currentPage,
                                       @RequestParam(required = false) Boolean exportMultiLevelBom,
                                       @RequestParam(required = false) Boolean exportReplaceMaterial) {
        IPage<ExcelTask> iPage = bomService.detailTaskPage(currentPage, pageSize, exportMultiLevelBom, exportReplaceMaterial);
        return success(iPage);
    }

    /**
     * 查询 BOM详细信息导入进度 （包括子物料）
     *
     * @return
     */
    @GetMapping("/export/task/{taskId}")
    public ResponseData detailListTaskById(@PathVariable Long taskId,
                                           @RequestParam(required = false) Boolean exportMultiLevelBom,
                                           @RequestParam(required = false) Boolean exportReplaceMaterial) {
        ExcelTask excelTask = bomService.detailListTaskById(taskId, exportMultiLevelBom, exportReplaceMaterial);
        return success(excelTask);
    }


    /**
     * 获取多级BOM（树形结构）
     *
     * @param
     * @return
     */
    @GetMapping("/multi/level")
    @UnitFormatMethod(UnitFormatMethod.MethodType.RESPONSE)
    public ResponseData getMultiLevelBom(@RequestParam(value = "materialCode", required = false) String materialCode,
                                         @RequestParam(value = "bomCode", required = false) String bomCode,
                                         @RequestParam(required = false) Integer skuId,
                                         @RequestParam(required = false, defaultValue = "massProduction") String productionState) {
        return success(bomService.getMultiLevelBom(materialCode, skuId, bomCode, productionState));
    }


    /**
     * 根据物料查询Bom列表
     *
     * @param materialCode 物料编码
     * @return
     */
    @GetMapping("/list/by/code/{materialCode}")
    @UnitFormatMethod(UnitFormatMethod.MethodType.RESPONSE)
    public ResponseData getListByMaterialCode(@PathVariable(value = "materialCode") String materialCode) {
        List<BomEntity> bomEntityList = bomService.getListByMaterialCode(materialCode);
        return success(bomEntityList);
    }

    /**
     * 根据物料查询Bom列表
     *
     * @param materialCode 物料编码
     * @return
     */
    @GetMapping("/list/by/code")
    @UnitFormatMethod(UnitFormatMethod.MethodType.RESPONSE)
    public ResponseData listByCode(@RequestParam(value = "materialCode") String materialCode,
                                   @RequestParam(value = "skuId", required = false) Integer skuId,
                                   @RequestParam(value = "productionState", required = false, defaultValue = "massProduction") String productionState) {
        List<BomEntity> bomEntityList = bomService.getListByCodeAndSkuId(materialCode, skuId, productionState);
        return success(bomEntityList);
    }

    /**
     * 根据物料查询Bom列表
     *
     * @param materialCode 物料编码
     * @return
     */
    @GetMapping("/material/bom/list")
    @UnitFormatMethod(UnitFormatMethod.MethodType.RESPONSE)
    public ResponseData getMaterialBomList(@RequestParam(value = "materialCode") String materialCode,
                                           @RequestParam(value = "skuId", required = false) Integer skuId,
                                           @RequestParam(value = "salesQuantity") Double salesQuantity) {
        List<PushDownMaterialDTO> materialBomList = bomService.getMaterialBomList(materialCode, skuId, salesQuantity);
        // 设置库存数量
        if (!CollectionUtils.isEmpty(materialBomList)) {
            List<StockMaterialDetailEntity> materialDetails = new ArrayList<>();
            materialBomList.forEach(o -> materialDetails.add(StockMaterialDetailEntity.builder().productCode(o.getMaterialCode()).skuId(o.getSkuId()).build()));

            // 查询物料当前库存数量
            Map<String, BigDecimal> materialCodeStockMap = JacksonUtil.getResponseMap(inventoryDetailInterface.queryMaterialInventoryQuantity(materialDetails), new com.alibaba.fastjson.TypeReference<Map<String, BigDecimal>>() {
            },null);
            materialCodeStockMap = materialCodeStockMap == null ? new HashMap<>(8) : materialCodeStockMap;
            for (PushDownMaterialDTO dto : materialBomList) {
                dto.setInventoryQuantity(materialCodeStockMap.getOrDefault(
                        ColumnUtil.getMaterialSku(dto.getMaterialCode(), dto.getSkuId()), BigDecimal.valueOf(0)).doubleValue());
            }
        }
        return success(materialBomList);
    }

    /**
     * 复制BOM配置
     *
     * @param bomCopyDTO
     * @return
     */
    @PostMapping("/copyBom")
    public ResponseData copyCraft(@RequestBody BomCopyDTO bomCopyDTO, BindingResult bindingResult) {
        if (bindingResult.hasErrors()) {
            throw new ParamException(bindingResult);
        }
        String username = getUsername();
        Integer integer = bomService.copyBom(bomCopyDTO, username);
        return success(integer);
    }

    /**
     * 获取BOM子项类型
     *
     * @return
     */
    @GetMapping("/type")
    public ResponseData getBomType() {
        List<CommonState> list = new ArrayList<>();
        BomItemTypeEnum[] values = BomItemTypeEnum.values();
        for (BomItemTypeEnum value : values) {
            CommonState type = new CommonState();
            type.setCode(value.getCode());
            type.setName(value.getName());
            list.add(type);
        }
        return success(list);
    }


    /**
     * 影响分析 -- 获取bom关联的
     * 生产订单(生效)、
     *
     * @param
     * @return
     */
    @PostMapping("/impact/analysis")
    public ResponseData impactAnalysis(@RequestBody ImpactAnalysisSelectDTO selectDTO) {
        return success(bomService.impactAnalysis(selectDTO));
    }

    /**
     * 影响分析 -- 导出
     *
     * @param
     * @return
     */
    @PostMapping("/impact/analysis/export")
    public ResponseData impactAnalysisExport(@RequestBody ImpactAnalysisSelectDTO selectDTO,
                                             HttpServletResponse response) throws IOException {
        bomService.impactAnalysisExport(selectDTO, response);
        return success();
    }

    /**
     * 根据子物料查询Bom列表
     *
     * @param selectDTO 物料编码
     * @return
     */
    @PostMapping("/list/by/material/code")
    public ResponseData getBomListByMaterialCode(@RequestBody BomSelectByMaterialDetailDTO selectDTO) {
        Page<ReplaceSchemeRelateBomVO> replaceSchemeRelateBomVOS = bomService.getBomListByMaterialCode(selectDTO);
        return success(replaceSchemeRelateBomVOS);
    }

    /**
     * 获取BOM子项物料校验类型
     *
     * @return
     */
    @GetMapping("/raw/material/verification/type")
    public ResponseData getVerificationType() {
        List<CommonEnum> list = new ArrayList<>();
        BomRawMaterialEnum[] values = BomRawMaterialEnum.values();
        for (BomRawMaterialEnum value : values) {
            CommonEnum type = new CommonEnum();
            type.setCode(value.getCode());
            type.setName(value.getName());
            list.add(type);
        }
        return success(list);
    }


    /**
     * 通过物料编码获取对应BOM。并且获取所有子物料关联的物料信息
     *
     * @param materialCode 物料编码
     * @param skuId 特征参数ID
     * @param productionState 生产状态(testProduction-试产,massProduction-量产)，不传默认为量产
     * @return
     */
    @GetMapping("/get/relate/bom/material")
    public ResponseData getRelateBomMaterials(@RequestParam(value = "materialCode") String materialCode,
                                              @RequestParam(value = "skuId") Integer skuId,
                                              @RequestParam(value = "productionState", required = false) String productionState) {
        return success(bomService.getRelateBomMaterials(materialCode, skuId, productionState));
    }

    /**
     * 校验是否能废弃
     * @param bomId bomId
     * @return 进度条key
     */
    @GetMapping("/delete/check")
    public ResponseData deleteCheck(@RequestParam(value = "bomId") Integer bomId) {
        return success(bomService.deleteCheck(bomId));
    }

    /**
     * 废弃校验进度
     * @param key redisKey
     * @return 进度
     */
    @GetMapping("/delete/check/progress")
    public ResponseData deleteCheckProgress(@RequestParam(value = "key") String key) {
        return success(bomService.deleteCheckProgress(key));
    }

    /**
     * 反查BOM
     * 假设A的子物料为B，B的子物料为C
     * 则展示为
     * C
     * |_B
     *   |_A
     * @return 反查列表
     */
    @PostMapping("/reverse/check")
    public ResponseData reverseCheckBom(@RequestBody MaterialDetailDTO dto) {
        return success(bomService.reverseCheckBom(dto));
    }
}
