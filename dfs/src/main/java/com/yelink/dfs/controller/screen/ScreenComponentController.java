package com.yelink.dfs.controller.screen;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yelink.dfs.common.BaseController;
import com.yelink.dfs.constant.OperationType;
import com.yelink.dfs.constant.screen.ScreenConstant;
import com.yelink.dfs.entity.screen.ComponentEntity;
import com.yelink.dfs.entity.screen.ScreenComponentEntity;
import com.yelink.dfs.entity.screen.ScreenSensorEntity;
import com.yelink.dfs.entity.screen.dto.ScreenComponentDTO;
import com.yelink.dfs.service.screen.ComponentService;
import com.yelink.dfs.service.screen.ScreenComponentService;
import com.yelink.dfs.service.screen.ScreenDeviceTagService;
import com.yelink.dfs.service.screen.ScreenSensorService;
import com.yelink.dfscommon.annotation.OperLog;
import com.yelink.dfscommon.pojo.ResponseData;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @description: 大屏组件配置
 * @time 2021/10/20
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/screens/component")
public class ScreenComponentController extends BaseController {
    private ScreenComponentService screenComponentService;
    private ComponentService componentService;
    private ScreenSensorService screenSensorService;
    private ScreenDeviceTagService screenDeviceTagService;


    /**
     * 添加大屏和组件
     */
    @PostMapping("/add")
    @OperLog(module = "大屏组件", type = OperationType.ADD, desc = "新增了名称为#{screenName}的大屏")
    public ResponseData add(@RequestBody ScreenComponentDTO screenComponentDTO) {
        try {
            screenComponentService.add(screenComponentDTO);
            return success();
        } catch (Exception e) {
            log.error("添加组件异常", e);
            return fail();
        }
    }

    /**
     * 更新(同时更新大屏和组件信息)
     */
    @PostMapping("/update")
    @OperLog(module = "大屏管理", type = OperationType.UPDATE, desc = "修改了名称为#{screenName}的大屏")
    public ResponseData update(@RequestBody ScreenComponentDTO screenComponentDTO) {
        try {
            screenComponentService.updateBy(screenComponentDTO);
            return success();
        } catch (Exception e) {
            log.error("修改组件异常", e);
            return fail();
        }
    }

    /**
     * 获取大屏
     */
    @GetMapping("/list")
    public ResponseData getList(@RequestParam(value = "current", defaultValue = "1") Integer current,
                                @RequestParam(value = "size", defaultValue = "20") Integer size) {
        Page<ScreenComponentEntity> page = screenComponentService.getList(current, size);
        return success(page);
    }

    /**
     * 删除组件
     */
    @DeleteMapping("/delete")
    @OperLog(module = "大屏管理", type = OperationType.DELETE, desc = "删除了名称为#{screenName}的大屏")
    public ResponseData removeEntity(@RequestBody List<Integer> ids) {
        ids.forEach(id -> componentService.removeById(id));
        return success();
    }

    /**
     * 根据大屏获取组件详情
     */
    @GetMapping("/get/detail")
    public ResponseData detail(@RequestParam("id") Integer id) {
        return success(componentService.detail(id));
    }

    /**
     * 更新大屏信息
     */
    @PostMapping("/update/screen")
    public ResponseData updateScreen(@RequestBody ScreenComponentEntity screenComponentEntity) {
        screenComponentEntity.setUpdateTime(new Date());
        boolean b = screenComponentService.updateById(screenComponentEntity);
        return b ? success() : fail("更新失败");
    }

    /**
     * 返回只可显示在首页的大屏
     */
    @GetMapping("/get/condition")
    public ResponseData getCondition() {
        LambdaQueryWrapper<ScreenComponentEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ScreenComponentEntity::getIsShowHome, ScreenConstant.SHOWHOME);
        //排序
        wrapper.orderByDesc(ScreenComponentEntity::getTopSort).orderByAsc(ScreenComponentEntity::getCreateTime);
        List<ScreenComponentEntity> list = screenComponentService.list(wrapper);
        return success(list);
    }

    /**
     * 批量添加组件
     */
    @PostMapping("/add/batch")
    @OperLog(module = "大屏组件", type = OperationType.ADD, desc = "新增了名称为#{screenName}的组件")
    public ResponseData addBatch(@RequestBody List<ComponentEntity> list) {
        list.forEach(componentEntity -> {
            componentEntity.setCreateTime(new Date());
            componentService.save(componentEntity);
        });
        return success();
    }

    /**
     * 删除整个大屏数据
     */
    @PostMapping("/delete/screen")
    @OperLog(module = "大屏组件", type = OperationType.DELETE, desc = "删除了名称为#{screenName}的组件")
    public ResponseData deleteScreen(@RequestParam("id") Integer id) {
        screenComponentService.removeById(id);
        //解除与大屏绑定的组件
        LambdaQueryWrapper<ComponentEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ComponentEntity::getScreenId, id);
        componentService.remove(wrapper);
        //解除与大屏绑定的设备
        LambdaQueryWrapper<ScreenSensorEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ScreenSensorEntity::getScreenId, id);
        screenSensorService.remove(queryWrapper);
        QueryWrapper<ComponentEntity> query = new QueryWrapper<>();
        query.lambda().eq(ComponentEntity::getScreenId, id);
        List<ComponentEntity> list = componentService.list(query);
        if (!CollectionUtils.isEmpty(list)) {
            if (ScreenConstant.DEVICE.equals(list.get(0).getType())) {
                screenDeviceTagService.deleteRelationScreenTagDevices(id);
            }
        }
        return success();
    }

    /**
     * 根据关联设备id查询大屏
     */
    @GetMapping("/get/screen/mac")
    public ResponseData getScreenBySid(@RequestParam(value = "mac", required = false) String mac) {
        LambdaQueryWrapper<ScreenSensorEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ScreenSensorEntity::getMac, mac);
        List<ScreenSensorEntity> list = screenSensorService.list(queryWrapper);

        List<Integer> collect = list.stream().map(ScreenSensorEntity::getScreenId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(collect)) {
            //没传就返回所有列表
            return success(screenComponentService.list());
        }
        LambdaQueryWrapper<ScreenComponentEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(ScreenComponentEntity::getId, collect);
        return success(screenComponentService.list(wrapper));
    }

    /**
     * 置顶大屏数据
     */
    @GetMapping("/top/screen/{id}")
    public ResponseData topScreen(@PathVariable Integer id) {
        screenComponentService.topScreen(id);
        return success();
    }
}
