package com.yelink.dfs.controller.order;

import com.yelink.dfs.common.BaseController;
import com.yelink.dfs.constant.product.MaterialMatchingEnum;
import com.yelink.dfs.entity.order.WorkOrderEntity;
import com.yelink.dfs.entity.order.dto.BatchUpdateWorkOrderDTO;
import com.yelink.dfs.service.order.WorkOrderService;
import com.yelink.dfscommon.entity.CommonType;
import com.yelink.dfscommon.pojo.ResponseData;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 工单
 *
 * <AUTHOR>
 * @Date 2021-03-11 10:18
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/workorders/material/check")
public class WorkOrderMaterialCheckController extends BaseController {
    @Resource
    private WorkOrderService workOrderService;

    /**
     * 上料防错更新
     *
     * @param entity
     * @return
     */
    @PutMapping("/update")
    public ResponseData materialCheckUpdate(@RequestBody WorkOrderEntity entity) {
        String username = getUsername();
        entity.setUpdateBy(username);
        entity.setUpdateDate(new Date());
        return success(workOrderService.materialCheckUpdate(entity, username));
    }

    /**
     * 上料防错类型
     */
    @GetMapping("/types")
    public ResponseData materialCheckTypes() {
        List<CommonType> states = new ArrayList<>();
        for (MaterialMatchingEnum value : MaterialMatchingEnum.getMaterialCheckEnum()) {
            states.add(CommonType.builder().code(value.getType()).name(value.getName()).build());
        }
        return ResponseData.success(states);
    }

    /**
     * 通过工单Id查询工单上料防错物料信息
     *
     * @param workOrderId
     * @return
     */
    @GetMapping("/select/{workOrderId}")
    public ResponseData selectMaterialCheck(@PathVariable("workOrderId") Integer workOrderId) {
        WorkOrderEntity entity = workOrderService.selectMaterialCheck(workOrderId);
        return success(entity);
    }

    /**
     * 生产工单批量编辑
     *
     * @param
     * @return
     */
    @PutMapping("/batch/update")
    public ResponseData batchUpdate(@RequestBody BatchUpdateWorkOrderDTO entity) {
        String username = getUsername();
        workOrderService.batchUpdateWorkOrderMaterialCheck(entity, username);
        return success();
    }


    /**
     * 通过工单Id查询工单上料防错物料信息(小程序)
     *
     * @param workOrderId
     * @return
     */
    @GetMapping("/app/select")
    public ResponseData selectMaterialCheck(@RequestParam Integer workOrderId, @RequestParam Integer craftProcedureId) {
        WorkOrderEntity entity = workOrderService.selectMaterialCheck(workOrderId, craftProcedureId);
        return success(entity);
    }

}
