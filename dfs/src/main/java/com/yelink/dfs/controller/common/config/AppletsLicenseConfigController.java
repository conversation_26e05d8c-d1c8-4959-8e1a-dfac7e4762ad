package com.yelink.dfs.controller.common.config;

import com.yelink.dfs.common.BaseController;
import com.yelink.dfs.service.common.config.AppletsLicenseConfigService;
import com.yelink.dfscommon.pojo.ResponseData;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @description: 小程序license配置
 * 针对不同工厂，通过配置工厂功能license，在小程序功能上架或升级时，进行功能模块的开启或禁用
 * @author: shuang
 * @time: 2022/9/21
 */
@RestController
@AllArgsConstructor
@RequestMapping("/config/applets")
public class AppletsLicenseConfigController extends BaseController {

    private final AppletsLicenseConfigService appletsLicenseConfigService;

    //背景描述：当小程序上架之后，系统会根据小程序的唯一标识从权限全量表获取权限数据，并且将权限数据刷到给工厂所有的员工，如果工厂需要配置小程序给具体角色使用禁用可以通过权限模块进行配置
    //需求描述：由于不同工厂相同小程序可能存在功能共同存在的场景，也就是说一个小程序有几个功能是A工厂的，另外几个功能是B工厂的，那么升级后希望针对不同工厂可以通过配置license进行权限加载
    //         同时，也不影响历史的逻辑。同时支持产品升级前配置，以及升级后配置
    //实现方式：在全量权限表获取指定小程序的全量权限后通过license过滤掉该工厂不需要的权限码
    /**
     * 获取json脚本
     * @return
     */
    @GetMapping("/licenses/list")
    public ResponseData list(){
        return ResponseData.success(appletsLicenseConfigService.listBy());
    }

}
