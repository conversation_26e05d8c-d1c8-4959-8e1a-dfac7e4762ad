package com.yelink.dfs.controller.model;

import com.yelink.dfs.common.BaseController;
import com.yelink.dfs.constant.model.BusinessUnitStateEnum;
import com.yelink.dfs.entity.model.dto.BusinessUnitAddDTO;
import com.yelink.dfs.entity.model.dto.BusinessUnitSelectDTO;
import com.yelink.dfs.entity.model.dto.BusinessUnitUpdateDTO;
import com.yelink.dfs.service.model.BusinessUnitService;
import com.yelink.dfscommon.entity.CommonType;
import com.yelink.dfscommon.pojo.ResponseData;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * 业务主体
 * <AUTHOR>
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/businessUnit")
public class BusinessUnitController extends BaseController {

    private BusinessUnitService businessUnitService;

    @GetMapping("/stateList")
    public ResponseData stateList() {
        return success(CommonType.covertToList(BusinessUnitStateEnum.class));
    }

    @PostMapping("/list")
    public ResponseData list(@RequestBody BusinessUnitSelectDTO dto) {
        return success(businessUnitService.selectList(dto));
    }
    @PostMapping("/page")
    public ResponseData page(@RequestBody BusinessUnitSelectDTO dto) {
        return success(businessUnitService.selectPage(dto));
    }

    @PostMapping("/add")
    public ResponseData add(@RequestBody @Valid BusinessUnitAddDTO dto) {
        businessUnitService.add(dto);
        return success();
    }


    @PutMapping("/update")
    public ResponseData update(@RequestBody @Valid BusinessUnitUpdateDTO dto) {
        businessUnitService.update(dto);
        return success();
    }


    @DeleteMapping("/delete")
    public ResponseData delete(@RequestParam Integer id) {
        businessUnitService.delete(id);
        return success();
    }
}
