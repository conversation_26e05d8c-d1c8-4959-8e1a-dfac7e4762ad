package com.yelink.dfs.controller.trace;

import com.alibaba.fastjson.JSONObject;
import com.yelink.dfs.common.BaseController;
import com.yelink.dfs.entity.order.WorkOrderEntity;
import com.yelink.dfs.service.barcode.BarCodeAnalysisService;
import com.yelink.dfscommon.pojo.ResponseData;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @description 条码追溯
 * @Date 2021/05/24
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/bar/code/trace")
public class BarCodeTraceController extends BaseController {

    private BarCodeAnalysisService barCodeAnalysis;


    /**
     * 追溯采购信息
     *
     * @param barCode
     * @return
     */
    @GetMapping("/purchase/{barCode}")
    public ResponseData getPurchaseInfo(@PathVariable String barCode) {
        JSONObject purchaseInfo = barCodeAnalysis.getPurchaseInfo(barCode);
        return success(purchaseInfo);
    }

    /**
     * 追溯工单
     *
     * @param barCode
     * @return
     */
    @GetMapping("/work/{barCode}")
    public ResponseData getWorkOrderInfo(@PathVariable String barCode) {
        WorkOrderEntity workOrderEntity = barCodeAnalysis.getWorkOrderInfo(barCode);
        return success(workOrderEntity);
    }

}
