package com.yelink.dfs.controller.print;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yelink.dfs.common.BaseController;
import com.yelink.dfs.constant.print.PrintTemplateTypeEnum;
import com.yelink.dfs.entity.print.PrintTemplateEntity;
import com.yelink.dfs.service.print.PrintTemplateService;
import com.yelink.dfscommon.constant.ResponseException;
import com.yelink.dfscommon.entity.CommonType;
import com.yelink.dfscommon.pojo.ResponseData;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> @description: 打印模板
 * @time 2021/5/21 15:49
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/print/template")
public class PrintTemplateController extends BaseController {
    private PrintTemplateService printTemplateService;

    /**
     * 查询列表
     *
     * @param current
     * @param size
     * @return
     */
    @GetMapping("/list")
    public ResponseData list(@RequestParam(value = "current", defaultValue = "1") Integer current,
                             @RequestParam(value = "size", defaultValue = "20") Integer size) {
        Page<PrintTemplateEntity> page = printTemplateService.getList(current, size);
        return success(page);
    }

    /**
     * 通过id查询详情
     *
     * @param id
     * @return
     */
    @GetMapping("/select/{id}")
    public ResponseData selectById(@PathVariable("id") Integer id) {
        PrintTemplateEntity entity = printTemplateService.getById(id);
        return success(entity);
    }

    /**
     * 通过类型获取列表
     *
     * @param templateType
     * @return
     */
    @GetMapping("/get/by/type")
    public ResponseData getListByType(@RequestParam(value = "templateType") String templateType) {
        List<PrintTemplateEntity> list = printTemplateService.getListByType(templateType);
        return success(list);
    }

    /**
     * 模板类型列表
     *
     * @return
     */
    @GetMapping("/type/list")
    public ResponseData templateList() {
        List<CommonType> list = new ArrayList<>();
        PrintTemplateTypeEnum[] values = PrintTemplateTypeEnum.values();
        for (PrintTemplateTypeEnum value : values) {
            CommonType type = new CommonType();
            type.setName(value.getTypeName());
            type.setCode(value.getType());
            list.add(type);
        }
        return success(list);
    }

    /**
     * 新增打印模板
     *
     * @param entity
     * @return
     */
    @PostMapping("/insert")
    public ResponseData insert(@RequestBody PrintTemplateEntity entity) {
        PrintTemplateTypeEnum typeEnum = PrintTemplateTypeEnum.getByType(entity.getTemplateType());
        if (typeEnum==null){
            throw new ResponseException("模板类型有误");
        }
        entity.setCreateBy(getUsername());
        entity.setCreateTime(new Date());
        printTemplateService.addEntity(entity);
        return success();
    }

    /**
     * 修改打印模板
     *
     * @param entity
     * @return
     */
    @PutMapping("/update")
    public ResponseData update(@RequestBody PrintTemplateEntity entity) {
        PrintTemplateTypeEnum typeEnum = PrintTemplateTypeEnum.getByType(entity.getTemplateType());
        if (typeEnum==null){
            throw new ResponseException("模板类型有误");
        }
        entity.setUpdateBy(getUsername());
        entity.setUpdateTime(new Date());
        printTemplateService.updateEntity(entity);
        return success();
    }

    /**
     * 通过Id删除班次配置
     *
     * @param id
     * @return
     */
    @DeleteMapping("/delete/{id}")
    public ResponseData delete(@PathVariable Integer id) {
        printTemplateService.removeById(id);
        return success();
    }

}

