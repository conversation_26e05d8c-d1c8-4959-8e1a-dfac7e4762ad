package com.yelink.dfs.controller.alarm;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yelink.dfs.common.BaseController;
import com.yelink.dfs.constant.alarm.NoticeEnum;
import com.yelink.dfs.constant.alarm.NoticeStateEnum;
import com.yelink.dfs.constant.target.TargetAlarmLevelEnum;
import com.yelink.dfs.entity.alarm.AlarmDefinitionEntity;
import com.yelink.dfs.entity.alarm.AlarmNotificationEntity;
import com.yelink.dfs.entity.alarm.dto.AlarmNotificationSelectDTO;
import com.yelink.dfs.entity.alarm.dto.UserVo;
import com.yelink.dfs.entity.device.DeviceEntity;
import com.yelink.dfs.service.alarm.AlarmNotificationService;
import com.yelink.dfscommon.entity.CommonType;
import com.yelink.dfscommon.pojo.ResponseData;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;


/**
 * <AUTHOR>
 * @Date 2021/3/6 17:36
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/alarm/notice")
public class AlarmNotificationController extends BaseController {
    private AlarmNotificationService notificationService;

    /**
     * 查询告警通知规则列表
     *
     */
    @GetMapping("/list")
    public ResponseData getList(AlarmNotificationSelectDTO dto) {
        Page<AlarmNotificationEntity> page = notificationService.getList(dto);
        return success(page);
    }

    /**
     * 获取告警通知设置详情
     *
     * @param
     * @return
     */
    @GetMapping("/detail/{id}")
    public ResponseData getNotification(@PathVariable Integer id) {
        AlarmNotificationEntity entity = notificationService.getNotificationById(id);
        return success(entity);
    }

    /**
     * 获取告警通知规则状态列表
     *
     * @param
     * @return
     */
    @GetMapping("/states")
    public ResponseData getNotificationStates() {
        List<CommonType> list = Stream.of(NoticeStateEnum.values()).map(
                o -> CommonType.builder()
                        .code(o.getCode())
                        .name(o.getName())
                        .build()
        ).collect(Collectors.toList());
        return success(list);
    }

    /**
     * 获取告警通知规则通知方法列表
     *
     * @param
     * @return
     */
    @GetMapping("/methods")
    public ResponseData getNoticeMethods() {
        List<CommonType> list = Stream.of(NoticeEnum.values()).map(
                o -> CommonType.builder()
                        .code(o.getCode())
                        .name(o.getName())
                        .build()
        ).collect(Collectors.toList());
        return success(list);
    }

    /**
     * 获取告警等级列表
     *
     * @param
     * @return
     */
    @GetMapping("/levels")
    public ResponseData getAlarmLevels() {
        List<CommonType> list = Stream.of(TargetAlarmLevelEnum.values()).map(
                o -> CommonType.builder()
                        .code(o.getLevelCode())
                        .name(o.getLevelName())
                        .build()
        ).collect(Collectors.toList());
        return success(list);
    }

    /**
     * 通知获取设备列表
     *
     * @param
     * @return
     */
    @GetMapping("/devices")
    public ResponseData getDeviceByModelIds(@RequestParam(value = "deviceModelId") Integer deviceModelId) {
        List<DeviceEntity> list = notificationService.getDevices(deviceModelId);
        return success(list);
    }


    /**
     * 通知获取告警定义列表
     *
     * @param
     * @return
     */
    @GetMapping("/alarm/definitions")
    public ResponseData geAlarmDefinitions() {
        List<AlarmDefinitionEntity> list = notificationService.geAlarmDefinitions();
        return success(list);
    }

    /**
     * 通知获取告警定义列表
     *
     * @param
     * @return
     */
    @GetMapping("/user")
    public ResponseData getUserList() {
        List<UserVo> list = notificationService.getUserList();
        return success(list);
    }

    /**
     * 新增告警通知设置
     *
     * @param
     * @return
     */
    @PostMapping("/add")
    public ResponseData addNotification(@RequestBody AlarmNotificationEntity entity) {
        entity.setCreateBy(getUsername());
        notificationService.addNotification(entity);
        return success();
    }

    /**
     * 修改告警通知设置
     *
     * @param
     * @return
     */
    @PutMapping("/update")
    public ResponseData updateNotification(@RequestBody AlarmNotificationEntity entity) {
        entity.setUpdateBy(getUsername());
        notificationService.updateNotification(entity);
        return success();
    }

    /**
     * 删除告警通知设置
     *
     * @param
     * @return
     */
    @DeleteMapping("/delete/{id}")
    public ResponseData removeNotification(@PathVariable Integer id) {
        notificationService.removeNotificationById(id);
        return success();
    }
    @GetMapping("/test")
    public ResponseData testNotice(@RequestParam Integer alarmId) {
        return success(notificationService.testNoticeList(alarmId));
    }


}
