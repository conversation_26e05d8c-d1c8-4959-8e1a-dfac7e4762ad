package com.yelink.dfs.controller.product;

import cn.hutool.core.util.RandomUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yelink.dfs.common.BaseController;
import com.yelink.dfs.constant.OperationType;
import com.yelink.dfs.constant.product.AuxiliartAttrValueStateEnum;
import com.yelink.dfs.constant.product.AuxiliaryAttrStateEnum;
import com.yelink.dfs.entity.common.CommonState;
import com.yelink.dfs.entity.product.AuxiliaryAttrSelectDTO;
import com.yelink.dfs.entity.product.dto.AuxiliaryAttrValueSelectDTO;
import com.yelink.dfs.service.product.AuxiliaryAttrService;
import com.yelink.dfs.service.product.AuxiliaryAttrValueService;
import com.yelink.dfscommon.annotation.OperLog;
import com.yelink.dfscommon.constant.Constant;
import com.yelink.dfscommon.constant.ParamException;
import com.yelink.dfscommon.constant.RedisKeyPrefix;
import com.yelink.dfscommon.dto.ApproveBatchDTO;
import com.yelink.dfscommon.entity.dfs.AuxiliaryAttrEntity;
import com.yelink.dfscommon.entity.dfs.AuxiliaryAttrValueEntity;
import com.yelink.dfscommon.pojo.ResponseData;
import com.yelink.dfscommon.utils.DateUtil;
import com.yelink.dfscommon.utils.ExcelTemplateImportUtil;
import com.yelink.dfscommon.utils.ExcelUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description 物料属性
 * @Date 2021/4/14
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/auxiliary/attr")
public class AuxiliaryAttrController extends BaseController {

    private AuxiliaryAttrService auxiliaryAttrService;
    private AuxiliaryAttrValueService auxiliaryAttrValueService;

    /**
     * 查询特征参数列表
     *
     * @param
     * @return
     */
    @PostMapping("/list")
    public ResponseData getList(@RequestBody AuxiliaryAttrSelectDTO selectDTO) {
        Page<AuxiliaryAttrEntity> page = auxiliaryAttrService.getList(selectDTO);
        return success(page);
    }

    /**
     * 新增特征参数
     *
     * @param
     * @return
     */
    @PostMapping("/add")
    public ResponseData add(@RequestBody AuxiliaryAttrEntity entity) {
        String username = getUsername();
        entity.setCreateBy(username);
        entity.setUpdateBy(username);
        auxiliaryAttrService.add(entity);
        return success();
    }

    /**
     * 新增生效的特征参数
     *
     * @param
     * @return
     */
    @PostMapping("/add/release")
    public ResponseData addRelease(@RequestBody AuxiliaryAttrEntity entity) {
        String username = getUsername();
        entity.setCreateBy(username);
        entity.setUpdateBy(username);
        auxiliaryAttrService.addRelease(entity);
        return success();
    }

    /**
     * 更新特征参数
     *
     * @param
     * @return
     */
    @PutMapping("/update")
    public ResponseData update(@RequestBody AuxiliaryAttrEntity entity) {
        String username = getUsername();
        entity.setUpdateBy(username);
        auxiliaryAttrService.update(entity);
        return success();
    }

    /**
     * 删除特征参数
     *
     * @param
     * @return
     */
    @DeleteMapping("/delete")
    public ResponseData delete(@RequestParam(value = "auxiliaryAttrCode") String auxiliaryAttrCode) {
        auxiliaryAttrService.delete(auxiliaryAttrCode);
        return success();
    }

    /**
     * 根据ID删除特征参数
     *
     * @param
     * @return
     */
    @DeleteMapping("/deleteById/{id}")
    public ResponseData deleteById(@PathVariable(value = "id") Integer id) {
        auxiliaryAttrService.delete(getAuxiliaryAttrCode(id));
        return success();
    }

    /**
     * 获取详情
     *
     * @param
     * @return
     */
    @GetMapping("/detail")
    public ResponseData detail(@RequestParam(value = "auxiliaryAttrCode") String auxiliaryAttrCode) {
        return success(auxiliaryAttrService.detail(auxiliaryAttrCode));
    }

    /**
     * 根据ID获取详情
     *
     * @param
     * @return
     */
    @GetMapping("/detailById/{id}")
    public ResponseData detailById(@PathVariable(value = "id") Integer id) {
        return success(auxiliaryAttrService.detail(getAuxiliaryAttrCode(id)));
    }

    private String getAuxiliaryAttrCode(Integer id) {
        AuxiliaryAttrEntity entity = auxiliaryAttrService.lambdaQuery().select(AuxiliaryAttrEntity::getAuxiliaryAttrCode)
                .eq(AuxiliaryAttrEntity::getId, id).one();
        if (entity == null) {
            return null;
        }
        return entity.getAuxiliaryAttrCode();
    }

    /**
     * 审批
     */
    @GetMapping("/examine/approve")
    public ResponseData examineOrApprove(@RequestParam(value = "approvalStatus") Integer approvalStatus,
                                         @RequestParam(value = "approvalSuggestion", required = false) String approvalSuggestion,
                                         @RequestParam(value = "id") Integer id) {
        String username = getUsername();
        auxiliaryAttrService.examineOrApprove(approvalStatus, approvalSuggestion, id, username);
        return success();
    }

    /**
     * 批量审批
     *
     * @return
     */
    @PostMapping("/approve/batch")
    public ResponseData approveBatch(@RequestBody ApproveBatchDTO dto) {
        dto.setActualApprover(getUsername());
        auxiliaryAttrService.approveBatch(dto);
        return success();
    }


    /**
     * 通过特征参数编号 查询特征值列表
     *
     * @param valueDTO
     * @return
     */
    @PostMapping("/value/list")
    public ResponseData getValueList(@RequestBody @Valid AuxiliaryAttrValueSelectDTO valueDTO, BindingResult bindingResult) {
        if (bindingResult.hasErrors()) {
            throw new ParamException(bindingResult);
        }
        List<AuxiliaryAttrValueEntity> list = auxiliaryAttrValueService.getList(valueDTO);
        return success(list);
    }

    /**
     * 获取特征参数状态枚举
     *
     * @param
     * @return
     */
    @GetMapping("/state")
    public ResponseData getState() {
        List<CommonState> list = new ArrayList<>();
        AuxiliaryAttrStateEnum[] values = AuxiliaryAttrStateEnum.values();
        for (AuxiliaryAttrStateEnum value : values) {
            CommonState type = new CommonState();
            type.setCode(value.getCode());
            type.setName(value.getName());
            list.add(type);
        }
        return success(list);
    }

    /**
     * 获取特征值状态枚举
     *
     * @param
     * @return
     */
    @GetMapping("/value/state")
    public ResponseData getValueState() {
        List<CommonState> list = new ArrayList<>();
        AuxiliartAttrValueStateEnum[] values = AuxiliartAttrValueStateEnum.values();
        for (AuxiliartAttrValueStateEnum value : values) {
            CommonState type = new CommonState();
            type.setCode(value.getCode());
            type.setName(value.getName());
            list.add(type);
        }
        return success(list);
    }

    /**
     * 物料特征参数导入：默认导入模板下载
     */
    @GetMapping("/down/default/template")
    public void downloadDefaultTemplate(HttpServletResponse response) throws Exception {
        auxiliaryAttrService.downloadDefaultTemplate("classpath:template/auxiliaryAttrTemplate.xlsx", response, "物料特征参数默认导入模板" + Constant.XLSX);
    }

    /**
     * 物料特征参数导入：导入自定义模板
     */
    @PostMapping("/import/template")
    @OperLog(module = "导入日志", type = OperationType.ADD, desc = "导入物料特征参数自定义模板")
    public ResponseData importTemplateExcel(MultipartFile file) {
        auxiliaryAttrService.uploadCustomTemplate(file, this.getUsername());
        return success();
    }

    /**
     * 物料特征参数导入：下载自定义导入模板
     */
    @GetMapping("/export/custom/template")
    public void exportCustomExcel(HttpServletResponse response) throws IOException {
        //找到导出的模板
        InputStream inputStream = auxiliaryAttrService.downloadCustomImportTemplate();
        ExcelTemplateImportUtil.responseToClient(response, inputStream, "物料特征参数导入模板" + Constant.XLSX);
    }

    /**
     * 物料特征参数导入：导出导入模板
     */
    @GetMapping("/export/template")
    public void exportTemplateExcel(HttpServletResponse response) throws IOException {
        //找到导出的模板
        byte[] bytes = auxiliaryAttrService.downloadImportTemplate();
        ExcelTemplateImportUtil.responseToClient(response, bytes, "物料特征参数导入模板" + Constant.XLSX);
    }

    /**
     * 物料特征参数导入：导入数据
     */
    @PostMapping("/import/data")
    public ResponseData importData(MultipartFile file) throws Exception {
        ExcelUtil.checkImportExcelFile(file);
        String importProgressKey = RedisKeyPrefix.AUXILIARY_ATTR_IMPORT_PROGRESS + DateUtil.format(new Date(), DateUtil.yyyyMMddHHmmss_FORMAT) + "_" + RandomUtil.randomString(2);
        auxiliaryAttrService.sycImportData(file.getOriginalFilename(), file.getInputStream(), getUsername(), importProgressKey);
        return success(importProgressKey);
    }

    /**
     * 物料特征参数导入：查询导入进度
     */
    @GetMapping("/import/progress")
    public ResponseData getImportProgress() {
        return success(auxiliaryAttrService.importProgress());
    }

}
