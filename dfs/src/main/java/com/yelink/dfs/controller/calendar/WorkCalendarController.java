package com.yelink.dfs.controller.calendar;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yelink.dfs.common.BaseController;
import com.yelink.dfs.constant.work.calendar.WeekEnum;
import com.yelink.dfs.entity.model.dto.InstanceDTO;
import com.yelink.dfs.entity.work.calendar.WorkCalendarDownEntity;
import com.yelink.dfs.entity.work.calendar.WorkCalendarEntity;
import com.yelink.dfs.entity.work.calendar.dto.CalendarTableDTO;
import com.yelink.dfs.entity.work.calendar.dto.DateDTO;
import com.yelink.dfs.service.common.DictService;
import com.yelink.dfs.service.model.ModelService;
import com.yelink.dfs.service.work.calendar.WorkCalendarDownService;
import com.yelink.dfs.service.work.calendar.WorkCalendarService;
import com.yelink.dfscommon.pojo.ResponseData;
import com.yelink.dfscommon.utils.DateUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 工作日历
 * @Date 2021/08/19
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/work/calendar")
public class WorkCalendarController extends BaseController {

    private ModelService modelService;
    private WorkCalendarService calendarService;
    private WorkCalendarDownService calendarDownService;
    private DictService dictService;


    /**
     * 获取实例树
     *
     * @param
     * @param
     * @return
     */
    @GetMapping("/tree")
    public ResponseData getTree(@RequestParam(value = "modelType", required = false) String modelType) {
        List<InstanceDTO> instanceTree = modelService.getInstanceTree(modelType);
        return success(instanceTree);
    }

    /**
     * 获取周一到周日
     *
     * @return
     */
    @GetMapping("/weeks")
    public ResponseData getWeeks() {
        List<DateDTO> list = Arrays.stream(WeekEnum.values())
                .map(weekEnum -> DateDTO.builder()
                        .date(weekEnum.getCode())
                        .name(weekEnum.getName())
                        .build())
                .collect(Collectors.toList());
        return success(list);
    }

    /**
     * 根据模型类型、实例id获取列表
     *
     * @return
     */
    @GetMapping("/list")
    public ResponseData getCalendarList(@RequestParam(value = "modelType") String modelType,
                                        @RequestParam(value = "instanceId") Integer instanceId,
                                        @RequestParam(value = "current", required = false) Integer current,
                                        @RequestParam(value = "size", required = false) Integer size) {
        Page<WorkCalendarEntity> page = calendarService.getList(modelType, instanceId, current, size);
        return success(page);
    }


    /**
     * 通过id获取详情
     *
     * @return
     */
    @GetMapping("/detail/{calendarId}")
    public ResponseData getDetail(@PathVariable Integer calendarId) {
        WorkCalendarEntity entity = calendarService.getDetailById(calendarId);
        return success(entity);
    }

    /**
     * 新增工作日历
     *
     * @return
     */
    @PostMapping("/add")
    public ResponseData addCalendar(@RequestBody WorkCalendarEntity entity) {
        entity.setCreateBy(getUsername());
        entity.setCreateTime(new Date());
        calendarService.addCalendar(entity);
        return success();
    }

    /**
     * 修改工作日历
     *
     * @return
     */
    @PutMapping("/update")
    public ResponseData updateCalendar(@RequestBody WorkCalendarEntity entity) {
        String username = getUsername();
        entity.setUpdateBy(username);
        entity.setUpdateTime(new Date());
        calendarService.updateCalendar(entity);
        return success();
    }

    /**
     * 删除工作日历
     *
     * @return
     */
    @DeleteMapping("/delete/{calendarId}")
    public ResponseData deleteCalendar(@PathVariable Integer calendarId) {
        return success(calendarService.remove(calendarId));
    }

    //######################停产计划的增删改查#######################

    /**
     * 根据模型类型、实例id获取计划停产列表
     *
     * @return
     */
    @GetMapping("/down/list")
    public ResponseData getCalendarDownList(@RequestParam(value = "modelType") String modelType,
                                            @RequestParam(value = "instanceId") Integer instanceId,
                                            @RequestParam(value = "current", required = false) Integer current,
                                            @RequestParam(value = "size", required = false) Integer size) {
        Page<WorkCalendarDownEntity> page = calendarDownService.getList(modelType, instanceId, current, size);
        return success(page);
    }


    /**
     * 通过id获取详情
     *
     * @return
     */
    @GetMapping("/down/detail/{downId}")
    public ResponseData getDownDetail(@PathVariable Integer downId) {
        WorkCalendarDownEntity entity = calendarDownService.getDetailById(downId);
        return success(entity);
    }

    /**
     * 新增停产计划
     *
     * @return
     */
    @PostMapping("/down/add")
    public ResponseData addCalendarDown(@RequestBody WorkCalendarDownEntity entity) {
        String username = getUsername();
        entity.setCreateBy(username);
        entity.setCreateTime(new Date());
        calendarDownService.addCalendarDown(entity);
        return success();
    }

    /**
     * 修改停产计划
     *
     * @return
     */
    @PutMapping("/down/update")
    public ResponseData updateCalendarDown(@RequestBody WorkCalendarDownEntity entity) {
        String username = getUsername();
        entity.setUpdateBy(username);
        entity.setUpdateTime(new Date());
        calendarDownService.updateCalendarDown(entity);
        return success();
    }

    /**
     * 删除停产计划
     *
     * @return
     */
    @DeleteMapping("/down/delete/{downId}")
    public ResponseData deleteCalendarDown(@PathVariable Integer downId) {
        calendarDownService.remove(downId);
        return success();
    }


    //################班次汇总##############

    /**
     * 获取班次汇总
     *
     * @return
     */
    @GetMapping("/table")
    public ResponseData getCalendarSummary(@RequestParam(value = "modelType") String modelType,
                                           @RequestParam(value = "instanceId") Integer instanceId,
                                           @RequestParam(value = "yearAndMonth", required = false) String yearAndMonth) {
        CalendarTableDTO calendarTableDTO = calendarService.getCalendarSummary(modelType, instanceId, yearAndMonth);
        return success(calendarTableDTO);
    }

    /**
     * 根据工厂首班开始时间返回前端大屏时间段
     *
     * @return
     */
    @GetMapping("/begin/range")
    public ResponseData timeRange(@RequestParam(value = "date", required = false) String date) {
        Date dateNow = new Date();
        if (StringUtils.isNotBlank(date)) {
            dateNow = DateUtil.parse(date, DateUtil.DATETIME_FORMAT);
        }
        Date startDate = dictService.getDayOutputBeginTime(dateNow);
        Date endDate = DateUtil.addDate(startDate, 1);
        Map<String, String> map = new HashMap<>(2);
        map.put("startDate", DateUtil.format(startDate, DateUtil.DATETIME_FORMAT));
        map.put("endDate", DateUtil.format(endDate, DateUtil.DATETIME_FORMAT));
        return success(map);
    }


    /**
     * 获取工作日历列表
     *
     * @return
     */
    @GetMapping("/list/all")
    public ResponseData getCalendarList() {
        List<WorkCalendarEntity> list = calendarService.getAvailableList();
        return success(list);
    }


}
