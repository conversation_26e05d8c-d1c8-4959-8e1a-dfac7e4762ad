package com.yelink.dfs.controller.order;

import com.asyncexcel.core.model.ExcelTask;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yelink.dfs.common.BaseController;
import com.yelink.dfs.constant.Constant;
import com.yelink.dfs.constant.OperationType;
import com.yelink.dfs.constant.RedisKeyPrefix;
import com.yelink.dfs.entity.model.dto.ScheduleBasicUnitDTO;
import com.yelink.dfs.entity.model.dto.ScheduleBasicUnitDTOV2;
import com.yelink.dfs.entity.model.dto.ScheduleDTO;
import com.yelink.dfs.entity.model.dto.ScheduleWorkOrderDTO;
import com.yelink.dfs.entity.order.WorkOrderEntity;
import com.yelink.dfs.entity.order.dto.OrderWorkOrderScheduleDTO;
import com.yelink.dfs.entity.order.dto.WorkOrderCreateDTO;
import com.yelink.dfs.entity.order.dto.WorkOrderScheduleDTO;
import com.yelink.dfs.entity.order.dto.WorkOrderScheduleExportDTO;
import com.yelink.dfs.entity.order.dto.WorkOrderScheduleSelectDTO;
import com.yelink.dfs.entity.order.dto.WorkOrderScheduleTotalDTO;
import com.yelink.dfs.entity.order.dto.WorkOrderScheduleTotalExportDTO;
import com.yelink.dfs.entity.order.dto.WorkOrderScheduleUpdateDTO;
import com.yelink.dfs.entity.order.tmp.TmpMaterialReadinessInspectionEntity;
import com.yelink.dfs.entity.order.vo.WorkOrderScheduleSumVO;
import com.yelink.dfs.entity.user.SysUserColumnRecordEntity;
import com.yelink.dfs.service.manufacture.ProductionLineService;
import com.yelink.dfs.service.order.ConfigScheduleReverseMaterialDetailService;
import com.yelink.dfs.service.order.TmpWorkOrderScheduleService;
import com.yelink.dfs.service.order.WorkOrderScheduleService;
import com.yelink.dfs.service.order.WorkOrderService;
import com.yelink.dfs.service.shift.ShiftService;
import com.yelink.dfs.service.user.SysUserColumnRecordService;
import com.yelink.dfscommon.annotation.OperLog;
import com.yelink.dfscommon.constant.ExcelExportFormEnum;
import com.yelink.dfscommon.constant.ParamException;
import com.yelink.dfscommon.constant.RespCodeEnum;
import com.yelink.dfscommon.constant.ResponseException;
import com.yelink.dfscommon.dto.SheetExportDataDTO;
import com.yelink.dfscommon.entity.ams.dto.ReverseMaterialDTO;
import com.yelink.dfscommon.pojo.ResponseData;
import com.yelink.dfscommon.utils.DateUtil;
import com.yelink.dfscommon.utils.EasyExcelUtil;
import com.yelink.dfscommon.utils.ExcelUtil;
import com.yelink.dfscommon.utils.MathUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Date 2021/8/20 10:02 工单排程
 */
@Slf4j
@RestController
@RequestMapping("/work/order/schedules")
@Api(tags = "工作中心/V1/工单排程接口")
@RequiredArgsConstructor
public class WorkOrderScheduleController extends BaseController {

    private final ProductionLineService productionLineService;

    private final RedisTemplate redisTemplate;

    private final SysUserColumnRecordService sysUserColumnRecordService;

    private final ConfigScheduleReverseMaterialDetailService scheduleReverseMaterialDetailService;

    private final TmpWorkOrderScheduleService tmpWorkOrderScheduleService;

    private final WorkOrderService workOrderService;

    private final WorkOrderScheduleService workOrderScheduleService;

    private final ShiftService shiftService;

    /**
     * 导入生产订单(导入指定车间的未发货的生产订单) 新增功能：导入后通过关联查询将订单-工单整合生成一个临时表
     *
     * @param gridCodes            车间编码
     * @param isConcludeInComplete 订单是否齐套
     */
    @GetMapping("/import")
    public ResponseData importOrderList(@RequestParam(value = "gridCodes", required = false) String gridCodes,
            @RequestParam(value = "isConcludeInComplete", required = false) Boolean isConcludeInComplete) {
        return success(tmpWorkOrderScheduleService.getBindGridList(gridCodes, isConcludeInComplete));
    }

    /**
     * 工单排程下发
     */
    @PostMapping("/issue")
    @OperLog(module = "工单管理", type = OperationType.UPDATE, desc = "下发了工单号为#{workOrderNumber}的工单")
    public ResponseData issue(@RequestBody @Validated({WorkOrderEntity.Update.class}) WorkOrderEntity entity,
            BindingResult bindingResult) {
        if (bindingResult.hasErrors()) {
            throw new ParamException(bindingResult);
        }
        WorkOrderEntity workOrderEntity = workOrderService.getById(entity.getWorkOrderId());
        if (Objects.isNull(workOrderEntity)) {
            throw new ResponseException(RespCodeEnum.WORK_ORDER_ID_SELECT_FAIL.fmtDes(entity.getWorkOrderId()));
        }
        if (workOrderEntity.getIssue()) {
            throw new ResponseException(RespCodeEnum.CAN_NOT_ISSUE_AGAIN);
        }
        String username = getUsername();
        workOrderEntity.setUpdateBy(username);
        workOrderEntity.setUpdateDate(new Date());
        tmpWorkOrderScheduleService.issue(workOrderEntity);
        return success(workOrderEntity);
    }


    /**
     * 批量新增工单排程（新增为创建状态的工单）
     *
     * @param list
     * @param bindingResult
     * @return
     */
    @PostMapping("/batch/insert/{workOrderNumber}")
    @OperLog(module = "工单管理", type = OperationType.ADD, desc = "批量新增了工单号为#{workOrderNumber}的工单")
    public ResponseData scheduleInsert(
            @RequestBody @Validated({WorkOrderEntity.Insert.class}) List<WorkOrderEntity> list,
            @PathVariable(value = "workOrderNumber") String workOrderNumber, BindingResult bindingResult) {
        if (bindingResult.hasErrors()) {
            throw new ParamException(bindingResult);
        }
        String username = getUsername();
        tmpWorkOrderScheduleService.scheduleInsert(list, username, workOrderNumber);
        return success();
    }

    @PostMapping("/batch/insert")
    @OperLog(module = "工单管理", type = OperationType.ADD, desc = "批量新增了工单号为#{workOrderNumber}的工单")
    public ResponseData scheduleInsert2(
            @RequestBody @Validated({WorkOrderEntity.Insert.class}) List<WorkOrderEntity> list,
            @RequestParam String workOrderNumber, BindingResult bindingResult) {
        return scheduleInsert(list, workOrderNumber, bindingResult);
    }

    /**
     * 修改工单排程
     */
    @PutMapping("/update")
    @OperLog(module = "工单管理", type = OperationType.UPDATE, desc = "修改了工单号为#{workOrderNumber}的工单")
    public ResponseData scheduleUpdate(@RequestBody @Validated({WorkOrderEntity.Update.class}) WorkOrderEntity entity,
            BindingResult bindingResult) {
        if (bindingResult.hasErrors()) {
            throw new ParamException(bindingResult);
        }
        String username = getUsername();
        entity.setUpdateBy(username);
        entity.setUpdateDate(new Date());
        tmpWorkOrderScheduleService.scheduleUpdate(entity);
        return success();
    }

    /**
     * 获取工单排程列表（排序，条件查询）
     *
     * @param dto
     * @return
     */
    @PostMapping("/list")
    public ResponseData getWorkOrderScheduleList(@RequestBody OrderWorkOrderScheduleDTO dto,
            @RequestParam(value = "username", required = false) String username) {
        return success(tmpWorkOrderScheduleService.getWorkOrderScheduleList(dto, username));
    }

    /**
     * 工单排程--求和
     *
     * @param
     * @return
     */
    @PostMapping("/sum")
    public ResponseData getWorkOrderScheduleSum(@RequestBody OrderWorkOrderScheduleDTO dto) {
        return success(tmpWorkOrderScheduleService.getWorkOrderScheduleSum(dto));
    }

    /**
     * 获取数字字段
     *
     * @param
     * @return
     */
    @GetMapping("/number/field")
    public ResponseData getNumberField() {
        return success(WorkOrderScheduleSumVO.builder().build());
    }

    /**
     * 当前指定车间下的产线（可多个车间）
     */
    @GetMapping("/line")
    public ResponseData getLineList(@RequestParam(value = "gridCode") String gridCode) {
        if (StringUtils.isBlank(gridCode)) {
            throw new ResponseException(RespCodeEnum.GRID_CODE_IS_BLANK);
        }
        return success(productionLineService.getLineList(gridCode));
    }

    /**
     * 导出工单排程
     */
    @GetMapping("/export")
    public void scheduleExport(@RequestParam(value = "gridCodes") String gridCodes, HttpServletResponse response) {
        tmpWorkOrderScheduleService.scheduleExport(gridCodes, response);
    }

    /**
     * 物料齐备性检查
     */
    @GetMapping("/inspect")
    public ResponseData materialInspection() {
        // 重置进度
        redisTemplate.opsForValue().set(RedisKeyPrefix.SCHEDULE_MATERIAL_COMPLETENESS_PROGRESS, Constant.ZERO_VALUE);
        tmpWorkOrderScheduleService.materialInspection();
        return success();
    }

    /**
     * 查看物料齐套详情
     */
    @GetMapping("/inspect/details")
    public ResponseData inspectDetails(@RequestParam(value = "workOrderNumber") String workOrderNumber,
            @RequestParam(value = "current", required = false) Integer current,
            @RequestParam(value = "size", required = false) Integer size) {
        Page<TmpMaterialReadinessInspectionEntity> list = tmpWorkOrderScheduleService.inspectDetails(workOrderNumber,
                current, size);
        return ResponseData.success(list);
    }


    /**
     * 产能负荷
     */
    @GetMapping("/capacity/load")
    public ResponseData getCapacityLoad() {
        return ResponseData.success(tmpWorkOrderScheduleService.getCapacityLoad());
    }

    /**
     * 工单排程-获取齐套详情计算的进度
     *
     * @param
     * @return
     */
    @GetMapping("/material/completeness/progress")
    public ResponseData getMaterialCompletenessProgress() {
        String progress = tmpWorkOrderScheduleService.getMaterialCompletenessProgress();
        if (!MathUtil.isNumber(progress)) {
            throw new ResponseException(progress);
        }
        return success(progress);
    }

    /**
     * 工单排程-获取日期树
     *
     * @param date 前端传递的时间字段
     * @return
     */
    @PostMapping("/all/date/list/{date}")
    public ResponseData getAllDate(@RequestBody OrderWorkOrderScheduleDTO dto,
            @PathVariable(value = "date") String date) {
        return success(tmpWorkOrderScheduleService.getDateTree(dto, date));
    }

    /**
     * 工单排程-获取客户名称
     */
    @PostMapping("/order/cus")
    public ResponseData getOrderCusNames(@RequestBody OrderWorkOrderScheduleDTO dto) {
        return success(tmpWorkOrderScheduleService.getOrderCusNames(dto));
    }

    /**
     * 工单排程-获取包装方式
     */
    @PostMapping("/order/packing/method")
    public ResponseData getOrderPackingMethod(@RequestBody OrderWorkOrderScheduleDTO dto) {
        return success(tmpWorkOrderScheduleService.getOrderPackingMethod(dto));
    }

    /**
     * 工单排程-获取产线名称
     */
    @PostMapping("/all/line/name")
    public ResponseData getAllLineName(@RequestBody OrderWorkOrderScheduleDTO dto) {
        return success(tmpWorkOrderScheduleService.getAllLineName(dto));
    }

    /**
     * 工单排程-获取物料名称列表
     */
    @PostMapping("/material/name/list")
    public ResponseData getMaterialNameList(@RequestBody OrderWorkOrderScheduleDTO dto) {
        return success(tmpWorkOrderScheduleService.getMaterialNameList(dto));
    }

    /**
     * 工单排程-获取物料编码列表
     */
    @PostMapping("/material/code/list")
    public ResponseData getMaterialCodeList(@RequestBody OrderWorkOrderScheduleDTO dto) {
        return success(tmpWorkOrderScheduleService.getMaterialCodeList(dto));
    }

    /**
     * 工单排程-按用户保存列配置
     *
     * @param
     * @return
     */
    @PostMapping("/update/user/column")
    public ResponseData saveOrUpdateWorkOrderUserColumn(@RequestBody SysUserColumnRecordEntity entity) {
        entity.setUserName(getUsername());
        entity.setModularType(com.yelink.dfs.constant.order.Constant.SCHEDULE_WORK_ORDER);
        LambdaUpdateWrapper<SysUserColumnRecordEntity> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(SysUserColumnRecordEntity::getUserName, entity.getUserName())
                .eq(SysUserColumnRecordEntity::getModularType,
                        com.yelink.dfs.constant.order.Constant.SCHEDULE_WORK_ORDER);
        return success(sysUserColumnRecordService.saveOrUpdate(entity, updateWrapper));
    }

    /**
     * 工单排程-获取用户列配置
     *
     * @param
     * @return
     */
    @GetMapping("/get/user/column")
    public ResponseData getWorkOrderUserColumn() {
        LambdaQueryWrapper<SysUserColumnRecordEntity> qw = new LambdaQueryWrapper<>();
        qw.eq(SysUserColumnRecordEntity::getUserName, getUsername());
        qw.eq(SysUserColumnRecordEntity::getModularType, com.yelink.dfs.constant.order.Constant.SCHEDULE_WORK_ORDER);
        return success(sysUserColumnRecordService.getOne(qw));
    }

    /**
     * 工单倒排配置--物料配置
     *
     * @param
     * @return
     */
    @PostMapping("/reverse/material/conf")
    public ResponseData materialConfInReverse(@RequestBody List<ReverseMaterialDTO> dtos) {
        // 重置进度
        redisTemplate.opsForValue().set(RedisKeyPrefix.SCHEDULE_MATERIAL_REVERSE_PROGRESS, Constant.ZERO_VALUE);
        tmpWorkOrderScheduleService.scheduleMaterialConfInReverse(dtos);
        return success();
    }

    /**
     * 工单倒排配置--物料配置计算的进度
     *
     * @param
     * @return
     */
    @GetMapping("/reverse/material/progress")
    public ResponseData getScheduleReverseMaterialProgress() {
        String progress = tmpWorkOrderScheduleService.getScheduleReverseMaterialProgress();
        if (!MathUtil.isNumber(progress)) {
            throw new ResponseException(progress);
        }
        return success(progress);
    }

    /**
     * 工单排程--获取倒排的拓展字段（中文名和英文名）
     *
     * @param
     * @return
     */
    @GetMapping("/reverse/extend/field")
    public ResponseData getScheduleReverseExtendFieldData() {
        return success(tmpWorkOrderScheduleService.getScheduleReverseExtendFieldData());
    }

    /**
     * 工单排程--获取某倒排列的物料名称列表（用于过滤）
     *
     * @param
     * @return
     */
    @PostMapping("/reverse/material/name/list/{fieldName}")
    public ResponseData getReverseMaterialNameList(@RequestBody OrderWorkOrderScheduleDTO dto,
            @PathVariable(value = "fieldName") String fieldName) {
        return success(tmpWorkOrderScheduleService.getScheduleReverseMaterialNameList(dto, fieldName));
    }

    /**
     * 工单排程--获取倒排配置表
     *
     * @param
     * @return
     */
    @GetMapping("/reverse/material/list")
    public ResponseData getReverseMaterialList() {
        return success(scheduleReverseMaterialDetailService.list());
    }

    /**
     * 工单排程--获取物料的当前库存、可用库存（只有通过物料齐备性计算的物料才有可用库存）
     *
     * @param
     * @return
     */
    @GetMapping("/material/inventory")
    public ResponseData getMaterialInventory(@RequestParam(value = "workOrderNumber") String workOrderNumber,
            @RequestParam(value = "materialName") String materialName) {
        return success(tmpWorkOrderScheduleService.getScheduleMaterialInventory(workOrderNumber, materialName));
    }


    /**
     * 获取工作中心列表（统计待排产、已排程数量）
     *
     * @return
     */
    @PostMapping("/work/center/list")
    public ResponseData listWorkCenter() {
        return success(workOrderScheduleService.listWorkCenter());
    }

    /**
     * 获取工作中心下的生产基本单元列表
     *
     * @return
     */
    @PostMapping("/work/center/basic/unit")
    @ApiOperation(value = "获取工作中心下的生产基本单元列表", notes = "根据工作中心ID、计划时间和生产资源类型获取生产基本单元")
    public ResponseData listWorkCenterBasicUnit(
            @ApiParam(value = "工作中心ID", required = true) @RequestParam Integer workCenterId,
            @ApiParam(value = "生产资源类型") @RequestParam(value = "productionResourceType", required = false) String productionResourceType,
            @ApiParam(value = "计划开始时间", required = true, example = "2025-03-20 00:00:00") @RequestParam String planStartTime,
            @ApiParam(value = "计划结束时间", required = true, example = "2025-03-29 00:00:00") @RequestParam String planEndTime) {
        return success(workOrderScheduleService.listWorkCenterBasicUnit(workCenterId, planStartTime, planEndTime,
                productionResourceType));
    }

    /**
     * 导入生产工单排程
     *
     * @return
     */
    @PostMapping("/import/schedule")
    public ResponseData importSchedule(@RequestParam Integer workCenterId) {
        workOrderScheduleService.importSchedule(workCenterId);
        return success();
    }


    /**
     * 获取工作中心下待排产工单列表
     *
     * @return
     */
    @PostMapping("/work/order/list/pending")
    public ResponseData listWorkOrderPending(@RequestParam Integer workCenterId) {
        return success(workOrderScheduleService.listWorkOrderPending(
                WorkOrderScheduleSelectDTO.builder().workCenterId(workCenterId).build()));
    }

    /**
     * 获取生产基本单元下已排产工单列表
     *
     * @return
     */
    @PostMapping("/work/order/list/already")
    public ResponseData listWorkOrderAlready(@RequestBody List<ScheduleBasicUnitDTO> scheduleBasicUnitDTOS,
            @RequestParam String planStartTime, @RequestParam String planEndTime) {
        return success(
                workOrderScheduleService.listWorkOrderAlready(scheduleBasicUnitDTOS, planStartTime, planEndTime, null));
    }

    /**
     * 获取生产基本单元下已排产工单列表
     *
     * @return
     */
    @PostMapping("/work/order/list/already/v2")
    @ApiOperation(value = "获取生产基本单元下已排产工单列表V2")
    public ResponseData listWorkOrderAlreadyV2(@RequestBody ScheduleWorkOrderDTO scheduleBasicUnitDTOS,
            @ApiParam(value = "计划开始时间", required = true) @RequestParam String planStartTime,
            @ApiParam(value = "计划结束时间", required = true)@RequestParam String planEndTime) {
        return success(
                workOrderScheduleService.listWorkOrderAlreadyV2(scheduleBasicUnitDTOS, planStartTime, planEndTime));
    }


    /**
     * 工单排程修改
     *
     * @return
     */
    @PostMapping("/work/order/update")
    @ApiOperation(value = "排产确认计划")
    public ResponseData workOrderUpdate(@RequestBody List<WorkOrderScheduleUpdateDTO> list) {
        workOrderScheduleService.workOrderUpdate(list, getUsername());
        return success();
    }

    /**
     * 工单排程修改
     *
     * @return
     */
    @PostMapping("/work/order/test")
    public ResponseData workOrderUpdate(@RequestParam String date) {

        return success(shiftService.getWorkShitByTime(DateUtil.parse(date, DateUtil.DATETIME_FORMAT),
                DateUtil.parse(date, DateUtil.DATETIME_FORMAT), 1));
    }

    /**
     * 工单排程状态列表
     *
     * @return
     */
    @GetMapping("/all/state")
    public ResponseData getAllState() {
        return success(workOrderScheduleService.getAllState());
    }

    /**
     * 工单拆分 - 创建/生效状态 * *
     *
     * @param workOrderCreateDTOS
     * @param bindingResult
     * @return
     */
    @OperLog(module = "工单管理", type = OperationType.ADD, desc = "拆分了工单号为#{workOrderNumber}的工单")
    @PostMapping("/create/from/self")
    public ResponseData createFromWorkOrder(@RequestBody @Validated List<WorkOrderCreateDTO> workOrderCreateDTOS,
            BindingResult bindingResult) {
        if (bindingResult.hasErrors()) {
            throw new ParamException(bindingResult);
        }
        return success(workOrderScheduleService.createFromWorkOrder(workOrderCreateDTOS, getUsername()));
    }

    /**
     * 工单取消拆分
     *
     * @param workOrderId
     * @return
     */
    @OperLog(module = "工单管理", type = OperationType.ADD, desc = "取消拆分了工单号为#{workOrderNumber}的工单")
    @DeleteMapping("/cancel/split/{workOrderId}")
    public ResponseData cancelSplit(@PathVariable Integer workOrderId) {
        return success(workOrderScheduleService.cancelSplit(workOrderId, getUsername()));
    }

    /**
     * 已排产时间列表
     *
     * @return
     */
    @GetMapping("/list/already")
    public ResponseData getAllState(@RequestParam Integer workCenterId) {
        return success(workOrderScheduleService.listTimeAlready(workCenterId));
    }

    /**
     * 智能排程列表导出 - 下载默认模板 查询最新的10条数据导出数据源excel
     *
     * @param response
     */
    @PostMapping("/default/export/template")
    @Deprecated
    public void listDefaultExportTemplate(@RequestBody ScheduleDTO scheduleBasicUnitDTOS,
            HttpServletResponse response) {
        WorkOrderScheduleSelectDTO workOrderScheduleSelectDTO = WorkOrderScheduleSelectDTO.builder().current(1).size(10)
                .workCenterId(scheduleBasicUnitDTOS.getWorkCenterId()).build();
        List<WorkOrderScheduleDTO> workOrderScheduleDTOS = workOrderScheduleService.listWorkOrderPending(
                workOrderScheduleSelectDTO);
        List<WorkOrderScheduleExportDTO> workOrderScheduleExportDTOS = WorkOrderScheduleExportDTO.convertToDTO(
                workOrderScheduleDTOS);
        List<WorkOrderScheduleTotalDTO> workOrderScheduleTotalDTOS = workOrderScheduleService.listWorkOrderAlready(
                scheduleBasicUnitDTOS.getScheduleBasicUnitDTOS(), scheduleBasicUnitDTOS.getPlanStartTime(),
                scheduleBasicUnitDTOS.getPlanEndTime(), scheduleBasicUnitDTOS.getScheduleList());
        List<WorkOrderScheduleTotalExportDTO> workOrderScheduleExportDTOS1 = WorkOrderScheduleTotalExportDTO.convertToDTO(
                workOrderScheduleTotalDTOS);
        List<SheetExportDataDTO> sheetExportDataList = new ArrayList<>();
        sheetExportDataList.add(SheetExportDataDTO.builder().sheetName("未排产").data(workOrderScheduleExportDTOS)
                .tClass(WorkOrderScheduleExportDTO.class)
                .fullPathCode(ExcelExportFormEnum.WORK_SCHEDULE_PRODUCT_TO.getFullPathCode()).build());
        sheetExportDataList.add(SheetExportDataDTO.builder().sheetName("已排产").data(workOrderScheduleExportDTOS1)
                .tClass(WorkOrderScheduleTotalExportDTO.class)
                .fullPathCode(ExcelExportFormEnum.WORK_SCHEDULE_PRODUCT_LIST.getFullPathCode()).build());
        EasyExcelUtil.writeToExcelTemplate(response, "工单排产导出模板" + ExcelUtil.XLSX, sheetExportDataList);
    }

    /**
     * 导出  :异步
     *
     * @return
     * @throws IOException
     */
    @PostMapping("/syc/exports")
    @Deprecated
    public ResponseData export(@RequestBody ScheduleDTO scheduleBasicUnitDTOS) {
        Long result = workOrderScheduleService.exportTask(scheduleBasicUnitDTOS, getUsername());
        return success(result);
    }

    /**
     * 分页查询当前导出任务列表
     *
     * @param size
     * @param current
     * @return
     */
    @GetMapping("/export/task/page")
    @Deprecated
    public ResponseData taskPage(@RequestParam(value = "pageSize", required = false, defaultValue = "20") Integer size,
            @RequestParam(value = "currentPage", required = false, defaultValue = "1") Integer current) {
        IPage<ExcelTask> iPage = workOrderScheduleService.taskPage(current, size);
        return success(iPage);
    }


    /**
     * 查询 导出进度
     *
     * @return
     */
    @GetMapping("/export/task/{taskId}")
    @Deprecated
    public ResponseData exportExcel(@PathVariable Long taskId) {
        ExcelTask excelTask = workOrderScheduleService.taskById(taskId);
        return success(excelTask);
    }

    /**
     * 列表导出 - 自定义模板上传 接收文件并清空名称为数据源sheet的内容
     *
     * @param file
     * @return
     */
    @PostMapping("/upload/list/export/template")
    @Deprecated
    public ResponseData uploadListExportTemplate(MultipartFile file) throws IOException {
        workOrderScheduleService.uploadListExportTemplate(file, getUsername());
        return success();
    }

}
