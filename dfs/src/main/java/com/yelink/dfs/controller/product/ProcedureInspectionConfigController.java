package com.yelink.dfs.controller.product;

import com.yelink.dfs.common.BaseController;
import com.yelink.dfs.constant.product.InspectDefectJudgeEnum;
import com.yelink.dfs.entity.common.VersionChangeCraftProcedureDTO;
import com.yelink.dfs.entity.product.ProcedureInspectionEntity;
import com.yelink.dfs.entity.product.dto.CraftProcedureDefaultConfigDTO;
import com.yelink.dfs.entity.product.dto.InspectCompareDTO;
import com.yelink.dfs.service.common.VersionChangeRecordService;
import com.yelink.dfs.service.product.ProcedureInspectionConfigService;
import com.yelink.dfscommon.constant.CommonEnum;
import com.yelink.dfscommon.constant.RedisKeyPrefix;
import com.yelink.dfscommon.constant.ResponseException;
import com.yelink.dfscommon.constant.dfs.CompatorStateEnumDTO;
import com.yelink.dfscommon.entity.dfs.productFlowCode.ProcedureInspectionConfigEntity;
import com.yelink.dfscommon.pojo.ResponseData;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2022/5/6 11:43
 */
@Slf4j
@RestController
@RequestMapping("/procedure/inspect/config")
public class ProcedureInspectionConfigController extends BaseController {

    @Resource
    private ProcedureInspectionConfigService procedureInspectionConfigService;
    @Resource
    private RedisTemplate redisTemplate;
    @Resource
    private VersionChangeRecordService versionChangeRecordService;

    /**
     * 获取工序检验配置的检验项目和代号的下拉接口(生效状态的、配置表中没有新增过的)
     *
     * @return
     */
    @GetMapping("/list")
    public ResponseData getList(@RequestParam(value = "craftProcedureId") Integer craftProcedureId) {
        List<ProcedureInspectionEntity> list = procedureInspectionConfigService.getList(craftProcedureId);
        return ResponseData.success(list);
    }

    /**
     * 获取数据类型获取比较符号
     *
     * @return
     */
    @GetMapping("/type/{dataType}")
    public ResponseData getTypeList(@PathVariable String dataType) {
        List<InspectCompareDTO> list = procedureInspectionConfigService.getTypeList(dataType);
        return success(list);
    }

    /**
     * 新增工序检验配置
     *
     * @param entity
     * @return
     */
    @PostMapping("/add")
    public ResponseData addProcedureInspectionConfig(@RequestBody ProcedureInspectionConfigEntity entity) {
        entity.setCreateBy(getUsername());
        entity.setCreateTime(new Date());
        procedureInspectionConfigService.saveProcedureInspectionConfig(entity);
        return ResponseData.success();
    }


    /**
     * 修改工序检验配置
     *
     * @param entity
     * @return
     */
    @PostMapping("/update")
    public ResponseData updateProcedureInspectionConfig(@RequestBody ProcedureInspectionConfigEntity entity) {
        entity.setUpdateBy(getUsername());
        entity.setUpdateTime(new Date());
        procedureInspectionConfigService.updateProcedureInspectionConfig(entity);
        return success();
    }


    /**
     * 删除工序检验配置
     *
     * @param id
     * @returnq
     */
    @DeleteMapping("/remove/{id}")
    public ResponseData removeProcedureInspectionConfig(@PathVariable Integer id) {
        ProcedureInspectionConfigEntity one = procedureInspectionConfigService.getById(id);
        boolean exist = one != null;
        if(exist){
            procedureInspectionConfigService.removeById(id);
            // 版本变更记录
            versionChangeRecordService.changeVersionCraftProcedure(VersionChangeCraftProcedureDTO.builder()
                    .description("检验项 - 删除")
                    .craftProcedureId(one.getCraftProcedureId())
                    .build()
            );
        }
        return success(exist);
    }


    /**
     * 获取布尔类型的下拉接口
     *
     * @retur
     */
    @GetMapping("/boolean/data")
    public ResponseData getBooleanList() {
        List<CompatorStateEnumDTO> booleanList = procedureInspectionConfigService.getBooleanList();
        return success(booleanList);
    }


    /**
     * 获取工序检验配置的详情
     *
     * @param id
     * @return
     */
    @GetMapping("/detail/{id}")
    public ResponseData getDetail(@PathVariable Integer id) {
        ProcedureInspectionConfigEntity entity = procedureInspectionConfigService.getProcedureInspectionConfigDetail(id);
        return success(entity);
    }

    /**
     * 工序检验添加工序定义的配置(默认项)
     *
     * @param dto
     * @return
     */
    @PostMapping("/add/default/config")
    public ResponseData addByProcedureDefaultConfig(@RequestBody CraftProcedureDefaultConfigDTO dto) {
        dto.setCreateBy(getUsername());
        //分布式锁
        String key = RedisKeyPrefix.PROCEDURE_DEFAULT_CONFIG + dto.getCraftProcedureId();
        Boolean lockStatus = redisTemplate.opsForValue().setIfAbsent(key, new Date(), 1, TimeUnit.MINUTES);
        if (lockStatus == null || !lockStatus) {
            throw new ResponseException("并发占用，稍后再试");
        }
        try {
            procedureInspectionConfigService.addByProcedureDefaultConfig(dto);
        } finally {
            redisTemplate.delete(key);
        }
        return success();
    }

    /**
     * 获取不良判断类型
     *
     * @return
     */
    @GetMapping("/defect/judge/type")
    public ResponseData getDefectJudgeType() {
        List<CommonEnum> list = Arrays.stream(InspectDefectJudgeEnum.values())
                .map(procedureInspectDataTypeEnum -> CommonEnum.builder()
                        .code(procedureInspectDataTypeEnum.getCode())
                        .name(procedureInspectDataTypeEnum.getName())
                        .build()).collect(Collectors.toList());
        return success(list);
    }

}
