package com.yelink.dfs.controller.common.config;


import com.yelink.dfs.common.BaseController;
import com.yelink.dfs.service.common.config.OrderTypeConfigService;
import com.yelink.dfs.service.common.config.OrderTypeItemService;
import com.yelink.dfscommon.dto.OrderTypeItemSelectDTO;
import com.yelink.dfscommon.dto.common.config.OrderTypeFullPathCodeDTO;
import com.yelink.dfscommon.dto.common.config.OrderTypeInfoVO;
import com.yelink.dfscommon.dto.common.config.OrderTypeItemAddDTO;
import com.yelink.dfscommon.dto.common.config.OrderTypeItemUpdateDTO;
import com.yelink.dfscommon.entity.dfs.OrderTypeConfigEntity;
import com.yelink.dfscommon.pojo.ResponseData;
import com.yelink.dfscommon.pojo.Result;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 统一配置,单据类型配置
 *
 * <AUTHOR>
 * @since 2024-12-2 16:31:08
 */
@RestController
@AllArgsConstructor
@RequestMapping("/config/order_type")
public class OrderTypeConfigController extends BaseController {

    private final OrderTypeConfigService orderTypeConfigService;
    private final OrderTypeItemService orderTypeItemService;

    /**
     * 查询单据类型树
     *
     * @param dto 某节点的全路径编码
     * @return List<BusinessConfigEntity> 配置树结构
     */
    @PostMapping("/tree")
    public ResponseData getTreeByFullPathCode(@RequestBody OrderTypeFullPathCodeDTO dto) {
        List<OrderTypeConfigEntity> options = orderTypeConfigService.getTreeByFullPathCode(dto);
        return success(options);
    }

    /**
     * 查询单据类型实例列表
     *
     * @param
     * @return
     */
    @PostMapping("/item/list")
    public ResponseData itemList(@RequestBody OrderTypeItemSelectDTO selectDTO) {
        return success(orderTypeConfigService.getPage(selectDTO));
    }

    /**
     * 查询单据类型详情
     *
     * @param
     * @return
     */
    @GetMapping("/item/detail")
    public ResponseData itemDetail(@RequestParam(value = "id") Integer id) {
        return success(orderTypeConfigService.getOrderTypeDetail(id));
    }

    /**
     * 添加单据类型
     *
     * @param
     * @return
     */
    @PostMapping("/item/add")
    public ResponseData itemAdd(@RequestBody OrderTypeItemAddDTO addDTO) {
        orderTypeConfigService.add(addDTO);
        return success();
    }

    /**
     * 更新单据类型
     *
     * @param
     * @return
     */
    @PostMapping("/item/update")
    public ResponseData itemUpdate(@RequestBody OrderTypeItemUpdateDTO updateDTO) {
        orderTypeConfigService.update(updateDTO);
        return success();
    }

    /**
     * 删除单据类型
     *
     * @param
     * @return
     */
    @DeleteMapping("/item/delete")
    public ResponseData itemUpdate(@RequestParam(value = "id") Integer id) {
        orderTypeItemService.removeById(id);
        return success();
    }

    /**
     * 查询对应单据大类下的单据类型列表（平铺单据类型）
     *
     * @param categoryCode 单据大类编码
     * @return
     */
    @GetMapping("/list/category")
    public Result<List<OrderTypeInfoVO>> tileOrderTypeListByOrderCategory(@RequestParam(value = "categoryCode") String categoryCode) {
        return Result.success(orderTypeConfigService.tileOrderTypeListByOrderCategory(categoryCode));
    }

}

