package com.yelink.dfs.controller.aql;

import com.yelink.dfs.common.BaseController;
import com.yelink.dfs.service.aql.AqlService;
import com.yelink.dfscommon.pojo.ResponseData;
import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @Date 2022/1/5 16:13
 */
@Slf4j
@RestController
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@RequestMapping("/aql")
public class AqlController extends BaseController {
    private AqlService aqlService;

    /**
     * 获取AQL对应的Ac、Re值
     *
     * @param scopeNum        批次数量
     * @param unqualified     产品检测不良数 (如果不填 只返回ac、re值)
     * @param inspectionLevel 检查水平枚举（s1、s2、s3、s4、g1、g2、g3）defaultValue = "g2"
     * @param aqlValue        AQL参考值枚举（0.01~1000）defaultValue = "1.0"
     * @param type            方案标准类型 0-正常 1-加严 2-放宽 defaultValue = "0"
     * @param batch           采样批次 1-一次 2-二次 3-多次 defaultValue = "1"
     * @return
     */
    @GetMapping("/judge/pass")
    public ResponseData list(@RequestParam(value = "scopeNum") Integer scopeNum,
                             @RequestParam(value = "unqualified", required = false) Integer unqualified,
                             @RequestParam(value = "inspectionLevel", defaultValue = "g2") String inspectionLevel,
                             @RequestParam(value = "aqlValue", defaultValue = "1.0") Double aqlValue,
                             @RequestParam(value = "type", defaultValue = "0") Integer type,
                             @RequestParam(value = "batch", defaultValue = "1") Integer batch) {
        Assert.notNull(scopeNum, "批次数量不能为空");
        return ResponseData.success(aqlService.judgePass(scopeNum, unqualified, inspectionLevel, aqlValue, type, batch));
    }
}
