package com.yelink.dfs.controller.model;

import com.yelink.dfs.common.BaseController;
import com.yelink.dfs.constant.OperationType;
import com.yelink.dfs.entity.barcode.dto.RuleTypeDTO;
import com.yelink.dfs.entity.model.dto.WorkCenterEditDTO;
import com.yelink.dfs.entity.model.dto.WorkCenterSelectDTO;
import com.yelink.dfs.entity.user.MyAuthenticationUserDetail;
import com.yelink.dfs.service.model.WorkCenterService;
import com.yelink.dfscommon.annotation.OperLog;
import com.yelink.dfscommon.constant.ParamException;
import com.yelink.dfscommon.constant.model.WorkCenterTypeEnum;
import com.yelink.dfscommon.dto.dfs.ProcedureDeviceVO;
import com.yelink.dfscommon.entity.CommonType;
import com.yelink.dfscommon.entity.dfs.WorkCenterEntity;
import com.yelink.dfscommon.pojo.ResponseData;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;


/**
 * @description: 工作中心管理接口
 * -- 接口数据验证待补充
 * @author: shuang
 * @time: 2020/12/9
 */
@Slf4j
@RestController
@RequestMapping("/work/center")
@AllArgsConstructor
public class WorkCenterController extends BaseController {

    private WorkCenterService workCenterService;

    /**
     * 分页条件、模糊查询本厂区下的工作中心列表
     *
     * @return
     */
    @GetMapping("/list")
    public ResponseData list(WorkCenterSelectDTO dto) {
        if (StringUtils.isBlank(dto.getUsername())) {
            dto.setUsername(getUsername());
        }
        return success(workCenterService.list(dto));
    }

    /**
     * 分页条件、模糊查询本厂区下的工作中心列表
     *
     * @return
     */
    @PostMapping("/page")
    public ResponseData page(@RequestBody WorkCenterSelectDTO dto) {
        if (StringUtils.isBlank(dto.getUsername())) {
            dto.setUsername(getUsername());
        }
        return success(workCenterService.list(dto));
    }

    /**
     * 根据ids获取工作中心列表
     *
     * @param ids
     * @return
     */
    @GetMapping("/list/by/ids")
    public ResponseData listByIds(@RequestParam(value = "ids") String ids) {
        return success(workCenterService.listByIds(ids));
    }

    /**
     * 工作中心列表
     *
     * @return
     */
    @GetMapping("/lists")
    public ResponseData getList() {
        List<WorkCenterEntity> list = workCenterService.list();
        return success(list);
    }

    /**
     * 添加工作中心并绑定员工（员工为负责人）
     *
     * @param workCenterEntity
     * @return
     */
    @PostMapping("/add")
    @OperLog(module = "工厂配置", type = OperationType.ADD, desc = "新增了工作中心编码为#{code}的工作中心信息")
    public ResponseData add(@RequestBody @Valid WorkCenterEntity workCenterEntity, BindingResult bindingResult) {
        if (bindingResult.hasErrors()) {
            throw new ParamException(bindingResult);
        }
        MyAuthenticationUserDetail userInfo = getUserInfo();
        int compId = userInfo.getCompId();
        workCenterEntity.setCid(compId);
        workCenterEntity.setCreateBy(userInfo.getNickname());
        workCenterEntity.setUpdateTime(new Date());
        workCenterService.add(workCenterEntity);
        return success(workCenterEntity);
    }


    /**
     * 通过工作中心id更新工作中心信息
     *
     * @param workCenterEditDTO
     * @return
     */
    @PutMapping("/update")
    @OperLog(module = "工厂配置", type = OperationType.UPDATE, desc = "修改了工作中心编码为#{code}的工作中心信息")
    public ResponseData update(@RequestBody WorkCenterEditDTO workCenterEditDTO) {
        workCenterEditDTO.getWorkCenterEntity().setCid(getUserInfo().getCompId());
        workCenterEditDTO.getWorkCenterEntity().setUpdateBy(getUsername());
        workCenterEditDTO.getWorkCenterEntity().setUpdateTime(new Date());
        workCenterService.edit(workCenterEditDTO);
        return success(workCenterEditDTO.getWorkCenterEntity());
    }


    /**
     * 获取工作中心详情
     *
     * @param id
     * @return
     */
    @GetMapping("/detail/{id}")
    public ResponseData detail(@PathVariable Integer id) {
        WorkCenterEntity workCenterEntity = workCenterService.detail(id);
        return success(workCenterEntity);
    }

    /**
     * 删除工作中心
     *
     * @param id
     * @return
     */
    @DeleteMapping("/delete/{id}")
    @OperLog(module = "工厂配置", type = OperationType.DELETE, desc = "删除了工作中心编码为#{code}的工作中心信息")
    public ResponseData delete(@PathVariable Integer id) {
        WorkCenterEntity one = workCenterService.getById(id);
        workCenterService.delete(id);
        return success(one);
    }

    /**
     * 获取工作中心可选的制造单元
     *
     * @param id
     * @return
     */
    @GetMapping("/list/line")
    public ResponseData listLine(@RequestParam(required = false) Integer id) {
        return success(workCenterService.listProductLine(id));
    }

    /**
     * 获取工作中心可选的班组
     *
     * @return
     */
    @GetMapping("/list/team")
    public ResponseData listTeam(@RequestParam(required = false) Integer workCenterId) {
        return success(workCenterService.listTeam(workCenterId));
    }

    /**
     * 获取工作中心可选的设备
     *
     * @return
     */
    @GetMapping("/list/device")
    public ResponseData listDevice(@RequestParam(required = false) Integer workCenterId) {
        return success(workCenterService.listDevice(workCenterId));
    }

    /**
     * 获取工作中心关联资源
     * @param relevanceType 关联资源类型
     * @return
     */
    @GetMapping("/list/relevance")
    public ResponseData listRelevanceResource(@RequestParam(value = "relevanceType") String relevanceType) {
        return success(workCenterService.listRelevanceResource(relevanceType));
    }


    /**
     * 获取工作中心可关联资源制造单元
     *
     * @return
     */
    @GetMapping("/list/relevance/line")
    public ResponseData listRelevanceLine() {
        return success(workCenterService.listRelevanceLine());
    }

    /**
     * 获取工作中心类型列表
     *
     * @return
     */
    @GetMapping("/types")
    public ResponseData getCodeTypeList() {
        List<RuleTypeDTO> list = Arrays.stream(WorkCenterTypeEnum.values())
                .map(workCenterTypeEnum -> RuleTypeDTO.builder()
                        .type(workCenterTypeEnum.getCode())
                        .name(workCenterTypeEnum.getName())
                        .build()).collect(Collectors.toList());
        return success(list);
    }

    /**
     * 获取工作中心关联制造单元列表
     *
     * @return
     */
    @GetMapping("/list/line/entity/{id}")
    public ResponseData listLineEntity(@PathVariable Integer id,
                                       @RequestParam(required = false) Boolean isIsolation,
                                       @RequestParam(required = false) String craftProcedureIds) {
        return success(workCenterService.listLineByCenterId(id, isIsolation, getUsername(), craftProcedureIds));
    }

    /**
     * 获取工作中心关联班组列表
     *
     * @return
     */
    @GetMapping("/list/team/entity/{id}")
    public ResponseData listTeamEntity(@PathVariable Integer id, @RequestParam(required = false) Boolean isIsolation) {
        return success(workCenterService.listTeamByCenterId(id, isIsolation, getUsername()));
    }

    /**
     * 获取工作中心关联设备列表
     *
     * @return
     */
    @GetMapping("/list/device/entity/{id}")
    public ResponseData listDeviceEntity(@PathVariable Integer id,
                                         @RequestParam(required = false) Boolean isIsolation,
                                         @RequestParam(required = false) String craftProcedureIds) {
        return success(workCenterService.listDeviceByCenterId(id, isIsolation, getUsername(), craftProcedureIds));
    }

    /**
     * 获取工作中心工单可选关联设备资源
     *
     * @return
     */
    @GetMapping("/list/device/for/work/order/{id}")
    public ResponseData listDeviceForWork(@PathVariable Integer id,
                                          @RequestParam(required = false) String craftProcedureIds) {
        return success(workCenterService.getRelevanceDeviceForWorkOrder(id, craftProcedureIds));
    }

    /**
     * 获取工作中心工单可选关联制造单元资源
     *
     * @return
     */
    @GetMapping("/list/line/for/work/order/{id}")
    public ResponseData listLineForWork(@PathVariable Integer id,
                                        @RequestParam(required = false) String craftProcedureIds) {
        return success(workCenterService.getRelevanceLineForWorkOrder(id, craftProcedureIds));
    }

    /**
     * 获取工作中心工单可选关联班组资源
     *
     * @return
     */
    @GetMapping("/list/team/for/work/order/{id}")
    public ResponseData listTeamForWork(@PathVariable Integer id) {
        return success(workCenterService.getRelevanceTeamForWorkOrder(id));
    }

    /**
     * 根据工单id获取 工作中心关联班组人员
     *
     * @param workOrderId 工单id
     * @param nickname    用户名 扫码搜索
     * @return
     */
    @GetMapping("/user/list/{workOrderId}")
    public ResponseData getWorkCenterUserList(@PathVariable Integer workOrderId, @RequestParam(required = false) String nickname, @RequestParam(required = false) Integer teamId) {
        return success(workCenterService.getUserListByWorkCenter(workOrderId, nickname, teamId));
    }

    /**
     * 获取产线类型的工作中心
     *
     * @return
     */
    @GetMapping("/line/type/list")
    public ResponseData getLineTypeCenters() {
        List<WorkCenterEntity> workCenterEntities = workCenterService.lambdaQuery()
                .eq(WorkCenterEntity::getType, WorkCenterTypeEnum.LINE.getCode())
                .list();
        return success(workCenterEntities);
    }

    /**
     * 查询工作中心类型及对应的实例类型树
     *
     * @param
     * @return
     */
    @GetMapping("/tree")
    public ResponseData getWorkCenterTree() {
        List<CommonType> entity = workCenterService.getWorkCenterTree();
        return success(entity);
    }

    /**
     * 根据工作中心查询关联的设备类型
     * 1、如果类型为班组，则查询关联资源为设备类型的数据
     * 2、如果类型为设备，则直接查询关联的设备类型
     *
     * @param
     * @return
     */
    @GetMapping("/device_model/list")
    public ResponseData getDeviceModelListByWorkCenter(@RequestParam(value = "workCenterIds") String workCenterIds) {
        List<ProcedureDeviceVO> list = workCenterService.getDeviceModelListByWorkCenter(workCenterIds);
        return success(list);
    }


}
