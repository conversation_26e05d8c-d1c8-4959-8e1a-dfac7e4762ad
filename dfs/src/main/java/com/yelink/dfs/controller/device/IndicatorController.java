package com.yelink.dfs.controller.device;

import com.yelink.dfs.common.BaseController;
import com.yelink.dfs.entity.device.dto.IndicatorEntityDTO;
import com.yelink.dfs.service.device.IndicatorService;
import com.yelink.dfscommon.pojo.ResponseData;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @description:
 * @time 2021/5/26 15:02
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/indicator")
public class IndicatorController extends BaseController {
    private IndicatorService indicatorService;

    //    @GetMapping("/list")
//    public ResponseData listData(@RequestParam(value = "tableName", required = false) String tableName,
//                                 @RequestParam(value = "fieldName", required = false) String fieldName) {
//        List<IndicatorEntity> list = indicatorService.getDateList(tableName, fieldName,92);
//        return success(list);
//
//    }
    @GetMapping("add")
    public ResponseData add(@RequestParam(value = "tableName", required = false) String tableName,
                            @RequestParam(value = "fieldName", required = false) String fieldName) {
//        int x = indicatorService.addIndicator(tableName, fieldName, 92, "203", new Date());
//        if (x > 0) {
//            return success();
//        } else {
//            return fail();
//        }
        return null;
    }


    @GetMapping("list")
    public ResponseData list() {
        Map<String, IndicatorEntityDTO> list = new HashMap<String, IndicatorEntityDTO>(10);
        IndicatorEntityDTO indicatorEntityDTO = IndicatorEntityDTO.builder()
                .id("1")
                .dataVal("18")
                .targetName("洗瓶机速度").build();
        list.put("c", indicatorEntityDTO);
        return success(list);

    }

}
