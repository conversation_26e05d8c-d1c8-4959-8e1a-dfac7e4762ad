package com.yelink.dfs.controller.user;

import cn.hutool.core.util.RandomUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yelink.dfs.common.BaseController;
import com.yelink.dfs.constant.OperationType;
import com.yelink.dfs.entity.order.CustomerEntity;
import com.yelink.dfs.entity.order.dto.CustomerExportDTO;
import com.yelink.dfs.entity.user.MyAuthenticationUserDetail;
import com.yelink.dfs.entity.user.SysUserEntity;
import com.yelink.dfs.entity.user.dto.ResetPasswordDTO;
import com.yelink.dfs.entity.user.dto.SysUserPrintSelectDTO;
import com.yelink.dfs.entity.user.dto.UserExportDTO;
import com.yelink.dfs.entity.user.dto.UserSelectDTO;
import com.yelink.dfs.mapper.user.enums.EnabledEnum;
import com.yelink.dfs.open.v1.production.dto.CustomerSelectDTO;
import com.yelink.dfs.service.user.SysUserService;
import com.yelink.dfs.service.user.UserInfoService;
import com.yelink.dfs.utils.ExtApiUtil;
import com.yelink.dfscommon.annotation.OperLog;
import com.yelink.dfscommon.constant.Constant;
import com.yelink.dfscommon.constant.ParamException;
import com.yelink.dfscommon.constant.RedisKeyPrefix;
import com.yelink.dfscommon.constant.RespCodeEnum;
import com.yelink.dfscommon.constant.ResponseException;
import com.yelink.dfscommon.dto.ImportProgressDTO;
import com.yelink.dfscommon.entity.dfs.barcode.PrintDTO;
import com.yelink.dfscommon.pojo.ResponseData;
import com.yelink.dfscommon.utils.DateUtil;
import com.yelink.dfscommon.utils.EasyExcelUtil;
import com.yelink.dfscommon.utils.ExcelTemplateImportUtil;
import com.yelink.dfscommon.utils.ExcelUtil;
import com.yelink.dfscommon.utils.JacksonUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.io.Resource;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.core.io.support.ResourcePatternResolver;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;


/**
 * @description: 用户信息
 * @author: shuang
 * @time: 2020/5/26
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/users")
public class UserController extends BaseController {

    private final SysUserService sysUserService;
    private final UserInfoService userInfoService;
    private final RedisTemplate redisTemplate;

    /**
     * 对外接口:单位内角色添加用户:用户名，手机号码，以及绑定的角色都不能为空,角色可以多个
     * 2.22版本后，用户解绑角色、部门
     *
     * @param sysUserEntity
     * @return
     */
    @PostMapping("/insert")
    @OperLog(module = "基础信息", type = OperationType.ADD, desc = "新增了姓名为#{nickname}，账号为#{username}的人员信息")
//    @Transactional(rollbackFor = Exception.class)
    public ResponseData insert(@RequestBody SysUserEntity sysUserEntity) {
        Optional optional = Optional.ofNullable(sysUserEntity).filter(u -> !StringUtils.isEmpty(sysUserEntity.getUsername()))
                .filter(u -> !StringUtils.isEmpty(sysUserEntity.getNickname()));
        //可通过当前用户角色等级来判定当前用户是否有添加该角色的权限，角色等级从oAuth2Authentication中获取
        //错误的请求参数
        if (!optional.isPresent()) {
            throw new ParamException("字段为空或不合法");
        }
        MyAuthenticationUserDetail userInfo = getUserInfo();
        sysUserEntity.setCompId(userInfo.getCompId());
        String handelName = getUsername();
        sysUserEntity.setUpdateBy(handelName);
        sysUserEntity.setCreateBy(handelName);
        String token = ExtApiUtil.getTokenBasedOnContext();
        sysUserService.add(sysUserEntity, sysUserEntity.getRoleId(), handelName, token);
        return success(sysUserEntity, "操作成功");
    }

    /**
     * 更新用户基本信息
     *
     * @param sysUserEntity
     * @return
     */
    @PutMapping("/update")
    @OperLog(module = "基础信息", type = OperationType.UPDATE, desc = "修改了姓名为#{nickname}，账号为#{username}的人员信息")
    public ResponseData update(@RequestBody SysUserEntity sysUserEntity) {
        Optional optional = Optional.ofNullable(sysUserEntity).filter(u -> !StringUtils.isEmpty(sysUserEntity.getUsername()))
                .filter(u -> !StringUtils.isEmpty(sysUserEntity.getNickname()));
        //错误的请求
        if (!optional.isPresent()) {
            throw new ParamException("字段为空不合法");
        }
        sysUserEntity.setUpdateBy(getUsername());
        String token = ExtApiUtil.getTokenBasedOnContext();
        sysUserService.update(sysUserEntity, token);
        return success(sysUserEntity);
    }

    /**
     * 禁用/启用用户
     *
     * @param disOrEnable 取值 disable/enable
     * @param userId      用户id
     * @return
     */
    @PutMapping("/disable/{disOrEnable}/{userId}")
    @OperLog(module = "基础信息", type = OperationType.ADD, desc = "#{disEnableLog}: #{username}的账号")
    public ResponseData disableUser(@PathVariable String disOrEnable, @PathVariable String userId) {
        if (StringUtils.isEmpty(disOrEnable) || StringUtils.isEmpty(userId)) {
            throw new ParamException("字段信息不能为空");
        }
        return success(sysUserService.updateDisableById(disOrEnable, userId, getUsername()));
    }

    /**
     * 删除用户
     * tips: 该操作为软删除，只有管理员角色才能删除用户
     *
     * @param username 用户名
     * @return
     */
    @DeleteMapping("/delete")
    public ResponseData deleteUser(@RequestParam(value = "username") String username) {
        if (StringUtils.isEmpty(username)) {
            throw new ParamException("用户信息不能为空");
        }
        sysUserService.deleteUser(username);
        return success();
    }

    /**
     * 查询启用的用户列表
     *
     * @param username 真实姓名模糊查询
     * @param key      排序字段  createTime-创建时间  updateTime-更新时间
     * @param sort     asc-升序  desc-降序
     * @return
     */
    @GetMapping("/list/company/enabled")
    public ResponseData selectByEnabled(@RequestParam(value = "username", required = false) String username,
                                        @RequestParam(value = "nickname", required = false) String nickname,
                                        @RequestParam(value = "roleName", required = false) String roleName,
                                        @RequestParam(value = "roleNames", required = false) List<String> roleNames,
                                        @RequestParam(value = "key", defaultValue = "updateTime") String key,
                                        @RequestParam(value = "sort", defaultValue = "desc") String sort,
                                        @RequestParam(value = "current", required = false) Integer current,
                                        @RequestParam(value = "size", required = false) Integer size,
                                        @RequestParam(value = "mobile", required = false) String mobile,
                                        @RequestParam(value = "email", required = false) String email,
                                        @RequestParam(value = "jobNumber", required = false) String jobNumber,
                                        @RequestParam(value = "post", required = false) String post,
                                        @RequestParam(value = "departmentName", required = false) String departmentName) {
        if (current == null || size == null) {
            current = 1;
            size = Integer.MAX_VALUE;
        }
        Page<SysUserEntity> page = sysUserService.selectPageNotIncludeYelinkOncall(username, nickname, roleName, key, sort, new Page<>(current, size),
                mobile, email, jobNumber, post, departmentName, null, EnabledEnum.ENABLE.getCode(), roleNames, null);
        return success(page);
    }


    /**
     * 查询所有用户
     *
     * @return
     */
    @PostMapping("/list")
    public ResponseData selectList(@RequestBody UserSelectDTO selectDTO) {
        selectDTO.setCurrent(selectDTO.getCurrent() == null ? 1 : selectDTO.getCurrent());
        selectDTO.setSize(selectDTO.getSize() == null ? Integer.MAX_VALUE : selectDTO.getSize());
        Page<SysUserEntity> page = sysUserService.selectList(selectDTO);
        return success(page);
    }


    /**
     * 查询用户列表
     *
     * @param
     * @return
     */
    @GetMapping("/list")
    public ResponseData selectUserList() {
        return success(sysUserService.selectList());
    }

    /**
     * 根据用户id查询用户详细信息
     *
     * @param id
     * @return
     */
    @GetMapping("/select/{id}")
    public ResponseData selectById(@PathVariable Integer id) {
        SysUserEntity sysUserEntity = sysUserService.selectById(id);
        if (sysUserEntity == null) {
            throw new ResponseException("数据不存在");
        }
        return success(sysUserEntity);
    }


    /**
     * 查询当前登录用户的信息
     */
    @GetMapping("/info")
    public ResponseData info() {
        SysUserEntity sysUserEntity = sysUserService.selectRoleByUsername(getUsername());
        if (sysUserEntity == null) {
            throw new ResponseException("用户已被禁用或删除");
        }
        return success(sysUserEntity);
    }

    /**
     * 修改密码
     *
     * @param oldPassword
     * @param newPassword
     * @return
     */
    @PostMapping("/update/password")
    @OperLog(module = "基础信息", type = OperationType.UPDATE, desc = "修改了账号为#{username}的密码")
    public ResponseData updatePassword(@RequestParam String oldPassword,
                                       @RequestParam String newPassword) {
        SysUserEntity userEntity = sysUserService.updatePassword(getUsername(), oldPassword, newPassword);
        return success(userEntity);
    }


    /**
     * 分页查询指定角色的用户
     */
    @GetMapping("/list/by/roleCode")
    public ResponseData selectUserListByRoleCode(@RequestParam(value = "roleCode", required = false) String roleCode,
                                                 @RequestParam(value = "current", required = false) Integer current,
                                                 @RequestParam(value = "size", required = false) Integer size) {
        if (current == null || size == null) {
            current = 1;
            size = Integer.MAX_VALUE;
        }
        Page<SysUserEntity> page = sysUserService.selectUserListByRoleCode(roleCode, current, size);
        return success(page);
    }

    /**
     * 查询当前的单位的用户列表---(pad)
     */
    @GetMapping("/list/by/company")
    public ResponseData selectUser() {
        MyAuthenticationUserDetail userInfo = getUserInfo();
        List<SysUserEntity> list = sysUserService.lambdaQuery()
                .eq(SysUserEntity::getEnabled, Constant.ENABLE)
                .eq(SysUserEntity::getIsDelete, false)
                .eq(SysUserEntity::getCompId, userInfo.getCompId())
                .list();
        return success(list);
    }


    /**
     * 查询当前登录用户的厂区、单位信息——（pad）
     */
    @GetMapping("/info/area")
    public ResponseData userInfo() {
        String username = getUsername();
        SysUserEntity sysUserEntity = sysUserService.selectUserArea(username);
        return success(sysUserEntity);
    }


    /**
     * 模糊查询用户列表
     *
     * @param nickname
     * @param current
     * @param size
     * @return
     */
    @GetMapping("/all/user")
    public ResponseData selectAllUser(@RequestParam(value = "nickname", required = false) String nickname,
                                      @RequestParam(value = "current", required = false) Integer current,
                                      @RequestParam(value = "size", required = false) Integer size) {
        Page<SysUserEntity> page = sysUserService.selectAllUser(nickname, current, size);
        return success(page);
    }

    /**
     * 模糊查询用户列表
     *
     * @param nickname 昵称
     * @return 用户列表
     */
    @GetMapping("/list/keyword")
    public ResponseData listKeyword(@RequestParam(value = "nickname", required = false) String nickname) {
        List<SysUserEntity> list = sysUserService.listKeyword(nickname);
        return success(list);
    }

    /**
     * 管理员重置用户密码
     */
    @PutMapping("/admin/reset/password")
    @OperLog(module = "基础信息", type = OperationType.UPDATE, desc = "修改了账号为#{username}的密码")
    public ResponseData adminResetPassword(@RequestBody ResetPasswordDTO resetPasswordDTO) {
        sysUserService.adminResetPassword(getUsername(), resetPasswordDTO);
        return success(SysUserEntity.builder().username(resetPasswordDTO.getUserAccount()).build());
    }

    /**
     * 下载用户信息默认导入模板
     */
    @GetMapping("/down/default/template")
    public void downUserDefaultTemplate(HttpServletResponse response) throws Exception {
        ResourcePatternResolver resolver = new PathMatchingResourcePatternResolver();
        Resource resource = resolver.getResource("classpath:template/userTemplate.xlsx");
        String templateName = URLEncoder.encode("用户数据导入模板" + Constant.XLSX, StandardCharsets.UTF_8.name());
        ExcelTemplateImportUtil.responseToClient(response, resource.getInputStream(), templateName);
    }


    /**
     * 用户数据模板批量导入
     *
     * @param file
     * @return
     */
    @PostMapping("/batch/import")
    public ResponseData importUserData(MultipartFile file) throws Exception {
        //1、判断导入文件的合法性
        ExcelUtil.checkImportExcelFile(file);
        String token = ExtApiUtil.getTokenBasedOnContext();
        //5、异步数据导入
        String importProgressKey = RedisKeyPrefix.USER_IMPORT_PROGRESS + DateUtil.format(new Date(), DateUtil.yyyyMMddHHmmss_FORMAT) + "_" + RandomUtil.randomString(2);
        userInfoService.importUserData(file.getInputStream(), file.getOriginalFilename(), getUsername(), getUserInfo().getCompId(), token, importProgressKey);
        return ResponseData.success(importProgressKey);
    }


    /**
     * 查询导入进度
     *
     * @return
     */
    @GetMapping("/import/progress")
    public ResponseData getImportProgress(@RequestParam(value = "key") String key) {
        Object obj = redisTemplate.opsForValue().get(key);
        return success(obj);
    }

    /**
     * 用户打印
     *
     * @return
     */
    @OperLog(module = "员工标签", type = OperationType.PRINT, desc = "打印了用户名为#{printCodes}的用户")
    @PostMapping("/print")
    public ResponseData print(@RequestBody SysUserPrintSelectDTO selectDTO) {
        log.info("{}调用了打印接口：{}", getUsername(), JacksonUtil.toJSONString(selectDTO));
        PrintDTO print = sysUserService.print(selectDTO);
        return success(print);
    }

    /**
     * 查询临时用户
     *
     * @return
     */
    @GetMapping("/temp/user/detail")
    public ResponseData selectTempUser(@RequestParam String userName) {
        if (StringUtils.isBlank(userName)) {
            throw new ResponseException("用户名不能为空");
        }
        SysUserEntity entity = sysUserService.selectTempUser(userName);
        return success(entity);
    }

    /**
     * 用户信息导出
     *
     * @return
     */
    @PostMapping("/export")
    public void exportUserList(@RequestBody UserSelectDTO selectDTO,
                               HttpServletResponse response) throws IOException {
        selectDTO.setCurrent(1);
        selectDTO.setSize(Integer.MAX_VALUE);
        Page<SysUserEntity> page = sysUserService.selectList(selectDTO);
        List<SysUserEntity> records = page.getRecords();
        List<UserExportDTO> exports = records.stream().map(UserExportDTO::convertToExport).collect(Collectors.toList());

        EasyExcelUtil.export(response, "用户数据导出", "用户", exports, UserExportDTO.class);
    }

}
