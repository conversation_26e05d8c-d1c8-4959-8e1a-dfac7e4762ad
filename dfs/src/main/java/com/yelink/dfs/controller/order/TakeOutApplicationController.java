package com.yelink.dfs.controller.order;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yelink.dfs.common.BaseController;
import com.yelink.dfs.constant.OperationType;
import com.yelink.dfs.constant.order.TakeOutApplicationStateEnum;
import com.yelink.dfs.entity.order.TakeOutApplicationEntity;
import com.yelink.dfs.entity.order.dto.TakeOutApplicationSelectDTO;
import com.yelink.dfs.service.order.TakeOutApplicationService;
import com.yelink.dfscommon.annotation.OperLog;
import com.yelink.dfscommon.constant.ParamException;
import com.yelink.dfscommon.dto.ApproveBatchDTO;
import com.yelink.dfscommon.entity.CommonType;
import com.yelink.dfscommon.pojo.ResponseData;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;


/**
 * @Description: 生产领料单
 * @Author: zengzhengfu
 * @Date: 2021/4/21
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/take/out/application")
public class TakeOutApplicationController extends BaseController {

    private TakeOutApplicationService takeOutApplicationService;


    /**
     * 查询生产领料单列表
     */
    @GetMapping("/list")
    public ResponseData takeOutApplicationList(TakeOutApplicationSelectDTO selectDTO) {
        Page<TakeOutApplicationEntity> list = takeOutApplicationService.getTakeOutApplicationList(selectDTO);
        return success(list);
    }

    /**
     * 查询生产领料单详情
     *
     * @param id
     * @return
     */
    @GetMapping("/detail/{id}")
    public ResponseData takeOutApplicationList(@PathVariable(value = "id") Integer id) {
        TakeOutApplicationEntity entity = takeOutApplicationService.getTakeOutApplication(id);
        return success(entity);
    }

    /**
     * 创建生产领料单
     *
     * @param entity
     * @return
     */
    @PostMapping("/add")
    @OperLog(module = "工单管理", type = OperationType.ADD, desc = "创建了单号为#{applicationNum}的生产领料单")
    public ResponseData createTakeOutApplication(@RequestBody @Validated({TakeOutApplicationEntity.Insert.class}) TakeOutApplicationEntity entity,
                                                 BindingResult bindingResult) {
        if (bindingResult.hasErrors()) {
            throw new ParamException(bindingResult);
        }
        String username = getUsername();
        entity.setUpdateBy(username);
        entity.setCreateBy(username);
        return success(takeOutApplicationService.createTakeOutApplication(entity));
    }

    /**
     * 创建生产领料单
     *
     * @param entity
     * @return
     */
    @PostMapping("/add/released")
    @OperLog(module = "工单管理", type = OperationType.ADD, desc = "创建了单号为#{applicationNum}的生产领料单")
    public ResponseData addReleasedApplication(@RequestBody @Validated({TakeOutApplicationEntity.Insert.class}) TakeOutApplicationEntity entity,
                                               BindingResult bindingResult) {
        if (bindingResult.hasErrors()) {
            throw new ParamException(bindingResult);
        }
        String username = getUsername();
        entity.setUpdateBy(username);
        entity.setCreateBy(username);
        TakeOutApplicationEntity applicationEntity = takeOutApplicationService.addReleasedApplication(entity, username);
        return success(applicationEntity);
    }

    /**
     * 更新生产领料单
     *
     * @param entity
     * @return
     */
    @PutMapping("/update")
    @OperLog(module = "工单管理", type = OperationType.UPDATE, desc = "更新了单号为#{applicationNum}的生产领料单")
    public ResponseData updateTakeOutApplication(@RequestBody @Validated({TakeOutApplicationEntity.Update.class}) TakeOutApplicationEntity entity,
                                                 BindingResult bindingResult) {
        if (bindingResult.hasErrors()) {
            throw new ParamException(bindingResult);
        }
        entity.setUpdateBy(getUsername());
        return success(takeOutApplicationService.updateTakeOutApplication(entity, getUsername()));
    }

    /**
     * 删除生产领料单
     *
     * @param id
     * @return
     */
    @DeleteMapping("/delete/{id}")
    @OperLog(module = "工单管理", type = OperationType.DELETE, desc = "删除了单号为#{applicationNum}的生产领料单")
    public ResponseData deleteTakeOutApplication(@PathVariable("id") Integer id) {
        return success(takeOutApplicationService.deleteTakeOutApplication(id));
    }

    /**
     * 获取领料单状态
     *
     * @return
     */
    @GetMapping("/type")
    public ResponseData getAlarmType() {
        List<CommonType> list = new ArrayList<>();
        TakeOutApplicationStateEnum[] values = TakeOutApplicationStateEnum.values();
        for (TakeOutApplicationStateEnum value : values) {
            CommonType type = new CommonType();
            type.setCode(value.getCode());
            type.setName(value.getName());
            list.add(type);
        }
        return success(list);
    }

    /**
     * 审批
     */
    @GetMapping("/examine/approve")
    public ResponseData examineOrApprove(@RequestParam(value = "approvalStatus") Integer approvalStatus,
                                         @RequestParam(value = "approvalSuggestion", required = false) String approvalSuggestion,
                                         @RequestParam(value = "id") Integer id) {
        String username = getUsername();
        takeOutApplicationService.examineOrApprove(approvalStatus, approvalSuggestion, id, username);
        return success();
    }

    /**
     * 批量审批
     *
     * @return
     */
    @PostMapping("/approve/batch")
    public ResponseData approveBatch(@RequestBody ApproveBatchDTO dto) {
        dto.setActualApprover(getUsername());
        takeOutApplicationService.approveBatch(dto);
        return success();
    }

    /**
     * 生产领料单新增前的判断：
     * 1、生产工单关联的生产领料单的数量之和(包括当前的生产领料单) > 生产工单BOM的计划数量，给前端提示
     *
     * @param entity 生产领料单
     * @return
     */
    @PostMapping("/insert/judge")
    public ResponseData judgeBeforeInsert(@RequestBody TakeOutApplicationEntity entity) {
        return success(takeOutApplicationService.judgeBeforeInsert(entity));
    }

}
