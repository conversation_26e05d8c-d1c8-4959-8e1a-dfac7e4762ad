package com.yelink.dfs.controller.task;

import com.yelink.dfs.common.BaseController;
import com.yelink.dfs.entity.task.dto.OrderRelationSelectDTO;
import com.yelink.dfs.service.task.OrderRelationService;
import com.yelink.dfscommon.constant.ParamException;
import com.yelink.dfs.entity.task.OrderRelationEntity;
import com.yelink.dfs.entity.task.dto.OrderRelationDeleteDTO;
import com.yelink.dfs.entity.task.dto.OrderRelationInsertDTO;
import com.yelink.dfs.entity.task.dto.OrderRelationUpdateDTO;
import com.yelink.dfscommon.pojo.ResponseData;
import com.yelink.dfscommon.utils.JacksonUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


/**
 * @Description:
 * @Author: zengzhengfu
 * @Date: 2024/12/5
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/order_relation")
public class OrderRelationController extends BaseController {

    private OrderRelationService orderRelationService;

    /**
     * 查询单据关系
     *
     */
    @PostMapping("/list")
    public ResponseData getFormRelation(@RequestBody OrderRelationSelectDTO dto) {
        List<OrderRelationEntity> list = orderRelationService.getList(dto);
        return ResponseData.success(list);
    }

    /**
     * 新增单据关系
     *
     * @param dto
     * @return
     */
    @PostMapping("/add")
    public ResponseData addFromRelation(@RequestBody @Validated OrderRelationInsertDTO dto, BindingResult bindingResult) {
        if (bindingResult.hasErrors()) {
            throw new ParamException(bindingResult);
        }
        OrderRelationEntity entity = JacksonUtil.convertObject(dto, OrderRelationEntity.class);
        orderRelationService.add(entity);
        return ResponseData.success(entity);
    }

    /**
     * 更新单据关系
     *
     * @param dto
     * @return
     */
    @PostMapping("/update")
    public ResponseData updateFromRelation(@RequestBody @Validated OrderRelationUpdateDTO dto, BindingResult bindingResult) {
        if (bindingResult.hasErrors()) {
            throw new ParamException(bindingResult);
        }
        OrderRelationEntity entity = JacksonUtil.convertObject(dto, OrderRelationEntity.class);
        orderRelationService.updateByCondition(entity);
        return ResponseData.success(entity);
    }

    /**
     * 删除单据关系
     *
     * @param dto
     * @return
     */
    @PostMapping("/delete")
    public ResponseData deleteFromRelation(@RequestBody OrderRelationDeleteDTO dto) {
        orderRelationService.delete(dto);
        return ResponseData.success();
    }
}
