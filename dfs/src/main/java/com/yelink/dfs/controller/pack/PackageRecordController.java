package com.yelink.dfs.controller.pack;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yelink.dfs.common.BaseController;
import com.yelink.dfs.constant.pack.PackageRecordTypeEnum;
import com.yelink.dfs.entity.management.PackageRecordEntity;
import com.yelink.dfs.service.management.PackageRecordService;
import com.yelink.dfscommon.entity.CommonType;
import com.yelink.dfscommon.pojo.ResponseData;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;

/**
 * 控制器
 *
 * <AUTHOR>
 * @Date 2021-11-16 17:58
 */
@Controller
@RestController
@RequestMapping("/packages")
@AllArgsConstructor
public class PackageRecordController extends BaseController {
    private PackageRecordService packageRecordService;

    /**
     * 查询列表
     *
     * @param workOrderNumber 工单编号
     * @param barCode         批次号
     * @param materialName    物料名称
     * @param materialCode    物料编号
     * @param reportStartTime 上报开始时间
     * @param reportEndTime   上报结束时间
     * @param current
     * @param size
     * @return
     */
    @GetMapping("/list")
    public ResponseData packageList(@RequestParam(value = "workOrderNumber", required = false) String workOrderNumber,
                                    @RequestParam(value = "barCode", required = false) String barCode,
                                    @RequestParam(value = "materialName", required = false) String materialName,
                                    @RequestParam(value = "materialCode", required = false) String materialCode,
                                    @RequestParam(value = "deviceId", required = false) Integer deviceId,
                                    @RequestParam(value = "reportStartTime", required = false) String reportStartTime,
                                    @RequestParam(value = "reportEndTime", required = false) String reportEndTime,
                                    @RequestParam(value = "current", required = false) Integer current,
                                    @RequestParam(value = "size", required = false) Integer size,
                                    @RequestParam(value = "packageLevel", required = false) Integer packageLevel,
                                    @RequestParam(value = "productSn", required = false) String productSn,
                                    @RequestParam(value = "packType", required = false) String packType,
                                    @RequestParam(value = "containerSn", required = false) String containerSn,
                                    @RequestParam(value = "operatorName", required = false) String operatorName) {
        Page<PackageRecordEntity> list = packageRecordService.getList(workOrderNumber, barCode, materialName,
                materialCode, deviceId, reportStartTime, reportEndTime, current, size, packageLevel, productSn, packType, containerSn, operatorName);
        return ResponseData.success(list);


    }


    /**
     * 导出列表
     *
     * @param workOrderNumber 工单编号
     * @param barCode         批次号
     * @param materialName    物料名称
     * @param materialCode    物料编号
     * @param reportStartTime 上报开始时间
     * @param reportEndTime   上报结束时间
     * @param response
     */
    @GetMapping("/export")
    public void export(@RequestParam(value = "workOrderNumber", required = false) String workOrderNumber,
                       @RequestParam(value = "barCode", required = false) String barCode,
                       @RequestParam(value = "materialName", required = false) String materialName,
                       @RequestParam(value = "materialCode", required = false) String materialCode,
                       @RequestParam(value = "deviceId", required = false) Integer deviceId,
                       @RequestParam(value = "reportStartTime", required = false) String reportStartTime,
                       @RequestParam(value = "reportEndTime", required = false) String reportEndTime,
                       @RequestParam(value = "packageLevel", required = false) Integer packageLevel,
                       @RequestParam(value = "productSn", required = false) String productSn,
                       @RequestParam(value = "packType", required = false) String packType,
                       @RequestParam(value = "containerSn", required = false) String containerSn,
                       @RequestParam(value = "operator", required = false) String operator,
                       HttpServletResponse response) throws IOException {
        packageRecordService.export(workOrderNumber, barCode, materialName, materialCode, deviceId, reportStartTime,
                reportEndTime, packageLevel, productSn, packType, containerSn, operator, response);
    }

    /**
     * 获取包装类型列表
     *
     * @return
     */
    @GetMapping("/record/type")
    public ResponseData getRecordTypeList() {
        ArrayList<CommonType> commonStates = new ArrayList<>();
        PackageRecordTypeEnum[] values = PackageRecordTypeEnum.values();
        for (PackageRecordTypeEnum value : values) {
            CommonType build = CommonType.builder().type(value.getCode()).name(value.getName()).build();
            commonStates.add(build);
        }
        return ResponseData.success(commonStates);
    }
}
