package com.yelink.dfs.controller.settings;

import com.yelink.dfs.common.BaseController;
import com.yelink.dfs.entity.settings.WhiteCardSettingsEntity;
import com.yelink.dfs.service.settings.WhiteCardSettingsService;
import com.yelink.dfscommon.pojo.ResponseData;
import lombok.AllArgsConstructor;
import org.keycloak.adapters.springsecurity.token.KeycloakAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Description:白牌设置
 * @Author: SQ
 * @Date: 2022/3/17 18:24
 * @Version:1.0
 */
@RestController
@AllArgsConstructor
@RequestMapping("/whitecard")
public class WhiteCardSettingsController extends BaseController {

    private final WhiteCardSettingsService whiteCardSettingsService;

    /**
     * 新增/修改白牌设置
     *
     * @param entity
     * @return
     */
    @PostMapping("/update")
    public ResponseData add(@RequestBody WhiteCardSettingsEntity entity) {
        whiteCardSettingsService.saveOrUpdateWhiteCard(entity, getUsername());
        return ResponseData.success();
    }


    /**
     * 查询默认的白牌设置
     *
     * @return
     */
    @GetMapping("/get")
    public ResponseData getWhiteCardSetting() {
        String username;
        try {
            KeycloakAuthenticationToken principe = (KeycloakAuthenticationToken) SecurityContextHolder.getContext().getAuthentication();
            username = principe.getName();
        } catch (Exception e) {
            // 未登录 用户名为空 错误不用抛出
            username = null;
        }
        WhiteCardSettingsEntity whiteCardSettingsEntity = whiteCardSettingsService.get(username);
        return ResponseData.success(whiteCardSettingsEntity);
    }

}
