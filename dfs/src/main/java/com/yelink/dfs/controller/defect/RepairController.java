package com.yelink.dfs.controller.defect;

import com.alibaba.fastjson.JSONObject;
import com.asyncexcel.core.exporter.DataExportParam;
import com.asyncexcel.core.model.ExcelTask;
import com.asyncexcel.springboot.ExcelService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yelink.dfs.common.BaseController;
import com.yelink.dfs.constant.code.MaintainTypeEnum;
import com.yelink.dfs.constant.defect.FaultTypeEnum;
import com.yelink.dfs.constant.maintain.MaintainDefineStateEnum;
import com.yelink.dfs.constant.maintain.MaintainRelatedTypeEnum;
import com.yelink.dfs.constant.maintain.MaintainSchemeStatusEnum;
import com.yelink.dfs.constant.maintain.MaintainSchemeTypeEnum;
import com.yelink.dfs.constant.maintain.MaintainTypeDictEnum;
import com.yelink.dfs.entity.common.CommonState;
import com.yelink.dfs.entity.defect.dto.DefineBatchUpdateDTO;
import com.yelink.dfs.entity.defect.dto.FacDTO;
import com.yelink.dfs.entity.defect.dto.StateEnumDTO;
import com.yelink.dfs.entity.defect.dto.TypeEnumDTO;
import com.yelink.dfs.entity.maintain.MaintainDefineEntity;
import com.yelink.dfs.entity.maintain.MaintainRecordEntity;
import com.yelink.dfs.entity.maintain.MaintainSchemeEntity;
import com.yelink.dfs.entity.maintain.dto.MaintainDefineExcelDTO;
import com.yelink.dfs.entity.maintain.dto.MaintainGridProportionDTO;
import com.yelink.dfs.entity.maintain.dto.MaintainRecordExcelDTO;
import com.yelink.dfs.entity.maintain.dto.MaintainRecordQueryDTO;
import com.yelink.dfs.entity.maintain.dto.MaintainStatementDTO;
import com.yelink.dfs.service.common.config.BusinessConfigValueService;
import com.yelink.dfs.service.defect.BaseTypeService;
import com.yelink.dfs.service.feed.MaintainMaterialRecordService;
import com.yelink.dfs.service.impl.maintain.MaintainRecordExportHandler;
import com.yelink.dfs.service.maintain.MaintainDefineService;
import com.yelink.dfs.service.maintain.MaintainRecordService;
import com.yelink.dfs.service.maintain.MaintainSchemeService;
import com.yelink.dfscommon.common.excel.BusinessCodeEnum;
import com.yelink.dfscommon.constant.dfs.config.ConfigConstant;
import com.yelink.dfscommon.pojo.ResponseData;
import com.yelink.dfscommon.utils.EasyExcelUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 设备日历
 * @Date 2021/05/24
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/maintain")
public class RepairController extends BaseController {

    private BaseTypeService baseTypeService;
    private MaintainDefineService defineService;
    private MaintainSchemeService schemeService;
    private MaintainRecordService recordService;
    private MaintainMaterialRecordService maintainMaterialRecordService;
    private BusinessConfigValueService businessConfigValueService;
    private ExcelService excelService;

    /**
     * #####################维修定义#####################
     * 获取维修定义列表
     *
     * @param current
     * @param size
     * @return
     */
    @GetMapping("/define/list")
    public ResponseData getDefineList(@RequestParam(value = "maintainName", required = false) String maintainName,
                                      @RequestParam(value = "maintainType", required = false) String maintainType,
                                      @RequestParam(value = "current", required = false) Integer current,
                                      @RequestParam(value = "size", required = false) Integer size,
                                      @RequestParam(value = "states", required = false) String states,
                                      @RequestParam(value = "description", required = false) String description) {
        Page<MaintainDefineEntity> page = defineService.getList(maintainName, maintainType, current, size, states, description);
        return success(page);
    }

    /**
     * 获取生效的维修定义列表
     *
     * @return
     */
    @GetMapping("/released/define/list")
    public ResponseData getReleasedDefineList() {
        List<MaintainDefineEntity> list = defineService.getReleasedList();
        return success(list);
    }

    /**
     * 维修定义导出
     *
     * @return
     */
    @GetMapping("/define/export")
    public void exportDefineList(@RequestParam(value = "maintainName", required = false) String maintainName,
                                 @RequestParam(value = "maintainType", required = false) String maintainType,
                                 HttpServletResponse response) throws IOException {
        defineService.exportDefineList(maintainName, maintainType, response);
    }

    /**
     * 维修定义导入前预览
     *
     * @return
     */
    @PostMapping("/define/preview")
    public ResponseData previewDefineList(MultipartFile file) {
        JSONObject jsonObject = defineService.previewBeforeImport(file);
        return success(jsonObject);
    }

    /**
     * 保存导入的维修定义
     *
     * @return
     */
    @PostMapping("/define/import")
    public ResponseData importDefineList(@RequestBody List<MaintainDefineExcelDTO> dtos) {
        String username = getUsername();
        defineService.saveImport(dtos, username);
        return success();
    }

    /**
     * 获取维修定义详情
     *
     * @param defectId
     * @return
     */
    @GetMapping("/define/detail/{defectId}")
    public ResponseData getDefineDetail(@PathVariable Integer defectId) {
        MaintainDefineEntity entity = defineService.getDetailById(defectId);
        return success(entity);
    }

    /**
     * 获取维修类型列表
     *
     * @return
     */
    @GetMapping("/type/list")
    public ResponseData getMaintainTypes(@RequestParam(value = "name", required = false) String name) {
        List<TypeEnumDTO> list = baseTypeService.getTypeList(MaintainTypeDictEnum.MAINTAIN_TYPE.getCode(), name);
        return success(list);
    }

    /**
     * 添加维修类型
     *
     * @return
     */
    @PostMapping("/type/add")
    public ResponseData addMaintainType(@RequestBody TypeEnumDTO dto) {
        String username = getUsername();
        baseTypeService.addType(MaintainTypeDictEnum.MAINTAIN_TYPE.getCode(), dto, username);
        return success();
    }

    /**
     * 修改维修类型
     *
     * @return
     */
    @PutMapping("/type/update")
    public ResponseData updateMaintainType(@RequestBody TypeEnumDTO dto) {
        String username = getUsername();
        baseTypeService.updateType(MaintainTypeDictEnum.MAINTAIN_TYPE.getCode(), dto, username);
        return success();
    }

    /**
     * 删除维修类型
     *
     * @return
     */
    @DeleteMapping("/type/remove/{code}")
    public ResponseData removeMaintainType(@PathVariable String code) {
        defineService.removeMaintainTypeByCode(code);
        return success();
    }

    /**
     * 获取故障类型列表
     *
     * @return
     */
    @GetMapping("/fault/type/list")
    public ResponseData getFaultTypes() {
        FaultTypeEnum[] values = FaultTypeEnum.values();
        ArrayList<TypeEnumDTO> list = new ArrayList<>(values.length);
        for (FaultTypeEnum typeEnum : values) {
            list.add(TypeEnumDTO.builder().code(typeEnum.getCode()).name(typeEnum.getName()).build());
        }
        return success(list);
    }

    /**
     * 获取维修定义状态
     *
     * @return
     */
    @GetMapping("/define/state/list")
    public ResponseData getMaintainStates() {
        MaintainDefineStateEnum[] values = MaintainDefineStateEnum.values();
        ArrayList<StateEnumDTO> list = new ArrayList<>(values.length);
        for (MaintainDefineStateEnum typeEnum : values) {
            list.add(StateEnumDTO.builder().code(typeEnum.getCode()).name(typeEnum.getName()).build());
        }
        return success(list);
    }

    /**
     * 添加维修定义
     *
     * @param entity
     * @return
     */
    @PostMapping("/add/define")
    public ResponseData addDefine(@RequestBody MaintainDefineEntity entity) {
        entity.setCreateBy(getUsername());
        entity.setCreateTime(new Date());
        defineService.addDefine(entity);
        return success();
    }

    /**
     * 修改维修定义
     *
     * @param entity
     * @return
     */
    @PutMapping("/update/define")
    public ResponseData updateDefine(@RequestBody MaintainDefineEntity entity) {
        entity.setUpdateBy(getUsername());
        entity.setUpdateTime(new Date());
        defineService.updateByMaintainId(entity);
        return success();
    }

    /**
     * 删除维修定义
     *
     * @param maintainId
     * @return
     */
    @DeleteMapping("/remove/define/{maintainId}")
    public ResponseData removeDefine(@PathVariable Integer maintainId) {
        defineService.removeDefineById(maintainId);
        return success();
    }

    /**
     * 保存并生效不良定义
     *
     * @param entity
     * @return
     */
    @PostMapping("/add/released/define")
    public ResponseData addReleasedDefine(@RequestBody MaintainDefineEntity entity) {
        String username = getUsername();
        entity.setCreateBy(username);
        entity.setUpdateBy(username);
        entity.setCreateTime(new Date());
        entity.setUpdateTime(new Date());
        defineService.addReleasedDefine(entity);
        return ResponseData.success();
    }

    /**
     * 批量编辑维修定义
     *
     * @param
     * @return
     */
    @PutMapping("/batch/update/define")
    public ResponseData updateDefine(@RequestBody DefineBatchUpdateDTO batchUpdateDTO) {
        batchUpdateDTO.setUsername(getUsername());
        defineService.batchUpdateDefine(batchUpdateDTO);
        return success();
    }


    /**
     * #####################维修方案#####################
     * 获取维修方案列表
     *
     * @param current
     * @param size
     * @return
     */
    @GetMapping("/scheme/list")
    public ResponseData getSchemeList(@RequestParam(value = "schemeName", required = false) String schemeName,
                                      @RequestParam(value = "schemeType", required = false) String schemeType,
                                      @RequestParam(value = "status", required = false) Integer status,
                                      @RequestParam(value = "current", required = false) Integer current,
                                      @RequestParam(value = "size", required = false) Integer size) {
        Page<MaintainSchemeEntity> page = schemeService.getList(schemeName, schemeType, status, current, size);
        return success(page);
    }

    /**
     * 获取维修方案详情
     *
     * @param schemeId
     * @return
     */
    @GetMapping("/scheme/detail/{schemeId}")
    public ResponseData getSchemeDetail(@PathVariable Integer schemeId) {
        MaintainSchemeEntity entity = schemeService.getSchemeDetail(schemeId);
        return success(entity);
    }

    /**
     * 获取维修方案状态
     *
     * @return
     */
    @GetMapping("/scheme/state/list")
    public ResponseData getSchemeStates() {
        MaintainSchemeStatusEnum[] values = MaintainSchemeStatusEnum.values();
        ArrayList<StateEnumDTO> list = new ArrayList<>(values.length);
        for (MaintainSchemeStatusEnum typeEnum : values) {
            list.add(StateEnumDTO.builder().code(typeEnum.getCode()).name(typeEnum.getName()).build());
        }
        return success(list);
    }

    /**
     * 获取维修关联类型
     *
     * @return
     */
    @GetMapping("/scheme/type/list")
    public ResponseData getSchemeTypes() {
        MaintainSchemeTypeEnum[] values = MaintainSchemeTypeEnum.values();
        ArrayList<TypeEnumDTO> list = new ArrayList<>(values.length);
        for (MaintainSchemeTypeEnum typeEnum : values) {
            list.add(TypeEnumDTO.builder().code(typeEnum.getCode()).name(typeEnum.getName()).build());
        }
        return success(list);
    }

    /**
     * 获取维修关联类型
     *
     * @return
     */
    @GetMapping("/related/type/list")
    public ResponseData getRelatedTypes() {
        MaintainRelatedTypeEnum[] values = MaintainRelatedTypeEnum.values();
        ArrayList<TypeEnumDTO> list = new ArrayList<>(values.length);
        for (MaintainRelatedTypeEnum typeEnum : values) {
            list.add(TypeEnumDTO.builder().code(typeEnum.getCode()).name(typeEnum.getName()).build());
        }
        return success(list);
    }

    /**
     * 获取所有生效的方案
     *
     * @return
     */
    @GetMapping("/scheme/released/list")
    public ResponseData getReleasedSchemeList() {
        List<MaintainSchemeEntity> list = schemeService.getReleasedList();
        return success(list);
    }

    /**
     * 获取所有工位
     *
     * @return
     */
    @GetMapping("/scheme/fac/list")
    public ResponseData getFacList() {
        ArrayList<FacDTO> facList = schemeService.getFacList();
        return success(facList);
    }

    /**
     * 添加维修方案
     *
     * @param entity
     * @return
     */
    @PostMapping("/add/scheme")
    public ResponseData addScheme(@RequestBody MaintainSchemeEntity entity) {
        entity.setCreateBy(getUsername());
        entity.setCreateTime(new Date());
        schemeService.addScheme(entity);
        return success();
    }

    /**
     * 修改维修方案
     *
     * @param entity
     * @return
     */
    @PostMapping("/update/scheme")
    public ResponseData updateScheme(@RequestBody MaintainSchemeEntity entity) {
        entity.setUpdateBy(getUsername());
        entity.setUpdateTime(new Date());
        schemeService.updateScheme(entity);
        return success();
    }

    /**
     * 删除维修方案
     *
     * @param schemeId
     * @return
     */
    @DeleteMapping("/remove/scheme/{schemeId}")
    public ResponseData removeScheme(@PathVariable Integer schemeId) {
        schemeService.removeSchemeById(schemeId);
        return success();
    }

    /**
     * #####################维修记录#####################
     * 获取维修记录列表
     *
     * @return
     */
    @GetMapping("/record/list")
    public ResponseData getRecordList(MaintainRecordQueryDTO dto) {
        Page<MaintainRecordEntity> page = recordService.getList(dto);
        return success(page);
    }

    /**
     * 下载返工记录模板
     */
    @GetMapping("/record/template/export")
    public void exportDailyTemplate(HttpServletResponse response) throws IOException {
        MaintainRecordQueryDTO selectDTO = new MaintainRecordQueryDTO();
        selectDTO.setCurrent(1);
        selectDTO.setSize(10);
        Page<MaintainRecordEntity> page = recordService.getList(selectDTO);
        List<MaintainRecordExcelDTO> dtos = page.getRecords().stream().map(MaintainRecordExcelDTO::convertToDTO).collect(Collectors.toList());
        EasyExcelUtil.export(response, "质检返工默认导出模板", "数据源", dtos, MaintainRecordExcelDTO.class);
    }

    /**
     * 上传自定义返工记录模板
     */
    @PostMapping("record/template/upload")
    public ResponseData uploadRecordTemplate(MultipartFile file) throws IOException {
        recordService.uploadRecordTemplate(getUsername(), file);
        return success();
    }

    /**
     * 维修记录导出
     *
     * @return
     */
    @PostMapping("/record/export")
    public ResponseData exportDefineList(@RequestBody MaintainRecordQueryDTO dto, @RequestParam(required = false) Integer templateId) {
        DataExportParam<MaintainRecordQueryDTO> dataExportParam = new DataExportParam<>();
        dataExportParam.setExportFileName(BusinessCodeEnum.QUALITY_INSPECTION_REWORK.getCodeName());
        dataExportParam.setLimit(10000);
        dataExportParam.setBusinessCode(BusinessCodeEnum.QUALITY_INSPECTION_REWORK.name());
        dataExportParam.setCreateUserCode(getUsername());
        Map<String, Object> parameters = new HashMap<>();
        parameters.put(MaintainRecordQueryDTO.class.getName(), dto);
        parameters.put("templateId", templateId);
        parameters.put("templateSheetName", "数据源");
        parameters.put("cleanSheetNames", "数据源");
        dataExportParam.setParameters(parameters);
        Long taskId = excelService.doExport(dataExportParam, MaintainRecordExportHandler.class);
        return ResponseData.success(taskId);
    }

    /**
     * 销售报表-当前订单明细：分页查询当前导出任务列表
     *
     * @param size
     * @param current
     * @return
     */
    @GetMapping("/export/task/page")
    public ResponseData saleOrderDetailTaskPage(@RequestParam(value = "pageSize", required = false, defaultValue = "20") Integer size,
                                                @RequestParam(value = "currentPage", required = false, defaultValue = "1") Integer current) {
        ExcelTask excelTask = new ExcelTask();
        excelTask.setBusinessCode(BusinessCodeEnum.QUALITY_INSPECTION_REWORK.name());
        return ResponseData.success(excelService.listPage(excelTask, current, size));
    }


    /**
     * 获取维修工位
     *
     * @return
     */
    @GetMapping("/record/fac")
    public ResponseData getFacilities() {
        ArrayList<FacDTO> list = recordService.getFacilities();
        return success(list);
    }

    /**
     * 获取质检返工的关联故障工位
     */
    @GetMapping("/record/fault-fac")
    public ResponseData getFaultFacilities() {
        List<FacDTO> list = recordService.getFaultFacilities();
        return success(list);
    }

    /**
     * 获取维修记录详情
     *
     * @param recordId
     * @return
     */
    @GetMapping("/record/detail/{recordId}")
    public ResponseData getRecordDetail(@PathVariable Integer recordId) {
        MaintainRecordEntity entity = recordService.getDetail(recordId);
        return success(entity);
    }

    /**
     * 添加维修方案
     *
     * @param entity
     * @return
     */
    @PostMapping("/add/record")
    public ResponseData updateScheme(@RequestBody MaintainRecordEntity entity) {
        entity.setCreateBy(getUsername());
        entity.setUpdateTime(new Date());
        recordService.save(entity);
        return success();
    }

    /**
     * #####################质检报表#####################
     * 获取工单列表
     *
     * @return
     */
    @GetMapping("/work/order")
    public ResponseData getWorkOrders() {
        List<String> list = recordService.getWorkOrders();
        return success(list);
    }

    /**
     * 下载返工报表模板
     */
    @GetMapping("/statement/template/export")
    public void exportStatementTemplate(@RequestParam(value = "isDefault", defaultValue = "true") Boolean isDefault,
                                        HttpServletResponse response) {
        recordService.exportStatementTemplate(isDefault, response);
    }

    /**
     * 上传自定义返工报表模板
     */
    @PostMapping("statement/template/upload")
    public ResponseData uploadStatementTemplate(MultipartFile file) {
        recordService.uploadStatementTemplate(file);
        return success();
    }

    /**
     * 获取报表
     *
     * @return
     */
    @GetMapping("/statement/list")
    public ResponseData getStatement(@RequestParam(value = "workOrder", required = false) String workOrder,
                                     @RequestParam(value = "fid", required = false) Integer fid,
                                     @RequestParam(value = "materialCode", required = false) String materialCode,
                                     @RequestParam(value = "factoryModel", required = false) String factoryModel,
                                     @RequestParam(value = "start", required = false) String start,
                                     @RequestParam(value = "end", required = false) String end,
                                     @RequestParam(value = "current", required = false) Integer current,
                                     @RequestParam(value = "size", required = false) Integer size) {
        Page<MaintainStatementDTO> page = recordService.getStatement(workOrder, materialCode, factoryModel, fid, start, end, current, size);
        return success(page);
    }

    /**
     * 维修报表导出
     *
     * @return
     */
    @GetMapping("/statement/export")
    public void exportStatementList(@RequestParam(value = "workOrder", required = false) String workOrder,
                                    @RequestParam(value = "fid", required = false) Integer fid,
                                    @RequestParam(value = "materialCode", required = false) String materialCode,
                                    @RequestParam(value = "factoryModel", required = false) String factoryModel,
                                    @RequestParam(value = "start", required = false) String start,
                                    @RequestParam(value = "end", required = false) String end,
                                    HttpServletResponse response) throws IOException {
        recordService.exportStatementList(workOrder, materialCode, factoryModel, fid, start, end, response);
    }

    /**
     * 根据质检id获取质检材料说明记录
     *
     * @return
     */
    @GetMapping("/material/record")
    public ResponseData exportStatementList(@RequestParam(value = "recordId") Integer recordId) {
        return success(maintainMaterialRecordService.listByMaintainId(recordId));
    }

    /**
     * 获取产线组（车间）的维修项目占比
     *
     * @param gid 产线组id
     * @return
     */
    @GetMapping("/grid/proportion")
    public ResponseData getMaintainGridProportion(@RequestParam(value = "gid", required = false) Integer gid) {
        List<MaintainGridProportionDTO> r = recordService.getMaintainGridProportion(gid);
        return success(r);
    }

    /**
     * 维修小程序：维修提交是否支持批量上报数量
     *
     * @return
     */
    @GetMapping("/get/maintain/config")
    public ResponseData getMaintainGridProportion() {
        return success(businessConfigValueService.getValue(ConfigConstant.MAINTAIN_BATCH_QUANTITY_REPORT_ENABLE));
    }


    /**
     * 获取维修类型列表
     *
     * @return
     */
    @GetMapping("/result/type/list")
    public ResponseData getMaintainResultTypes() {
        List<CommonState> list = new ArrayList<>();
        for (MaintainTypeEnum maintainTypeEnum : MaintainTypeEnum.values()) {
            list.add(CommonState.builder().code(maintainTypeEnum.getCode()).name(maintainTypeEnum.getName()).build());
        }
        return success(list);
    }

}
