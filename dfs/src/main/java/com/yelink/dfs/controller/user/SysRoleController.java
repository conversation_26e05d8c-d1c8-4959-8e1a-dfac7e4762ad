package com.yelink.dfs.controller.user;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yelink.dfs.common.BaseController;
import com.yelink.dfs.constant.OperationType;
import com.yelink.dfs.entity.terminal.SysPadEntity;
import com.yelink.dfs.entity.user.ModuleRoleConfEntity;
import com.yelink.dfs.entity.user.SysRoleEntity;
import com.yelink.dfs.entity.user.SysUserEntity;
import com.yelink.dfs.entity.user.dto.ModuleRoleSelectDTO;
import com.yelink.dfs.entity.user.dto.ModuleRoleUpdateDTO;
import com.yelink.dfs.entity.user.vo.RoleUserTreeVO;
import com.yelink.dfs.service.user.ModuleRoleConfService;
import com.yelink.dfs.service.user.SysRoleService;
import com.yelink.dfs.utils.ExtApiUtil;
import com.yelink.dfscommon.annotation.OperLog;
import com.yelink.dfscommon.constant.ParamException;
import com.yelink.dfscommon.constant.RespCodeEnum;
import com.yelink.dfscommon.pojo.ResponseData;
import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;


/**
 * @Description: 角色管理接口
 * @Author: yejiarun
 * @Date: 2020/12/07
 */
@Slf4j
@RestController
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@RequestMapping("/roles")
public class SysRoleController extends BaseController {

    private SysRoleService sysRoleService;
    private ModuleRoleConfService moduleRoleConfService;

    /**
     * @param name    角色名称
     * @param code    角色代号
     * @param current
     * @param size
     * @return
     */
    @GetMapping("/list")
    public ResponseData list(@RequestParam(value = "name", required = false) String name,
                             @RequestParam(value = "code", required = false) String code,
                             @RequestParam(value = "current", required = false) Integer current,
                             @RequestParam(value = "size", required = false) Integer size) {
        current = current == null ? 1 : current;
        size = size == null ? Integer.MAX_VALUE : size;
        Page<SysRoleEntity> list = sysRoleService.list(new Page<>(current, size), name, code);
        return success(list);
    }

    /**
     * 通过ID查询角色
     *
     * @param id
     * @return
     */
    @GetMapping("/select/{id}")
    public ResponseData selectById(@PathVariable Integer id) {
        if (sysRoleService.roleIdIsExist(id)) {
            return fail(RespCodeEnum.ROLE_NOEXIST);
        } else {
            SysRoleEntity entity = sysRoleService.selectById(id);
            return success(entity);
        }
    }

    /**
     * 添加角色
     *
     * @param entity
     * @return
     */
    @PostMapping("/insert")
    @OperLog(module = "基础信息", type = OperationType.ADD, desc = "新增了角色名为#{name}的角色")
    public ResponseData add(@RequestBody @Valid SysRoleEntity entity, BindingResult bindingResult) {
        String username = getUsername();
        if (bindingResult.hasErrors()) {
            throw new ParamException(bindingResult);
        }
        sysRoleService.insertEntity(entity, username);
        return success(entity);
    }

    /**
     * 修改角色信息
     *
     * @param entity
     * @return
     */
    @PutMapping("/update")
    @OperLog(module = "基础信息", type = OperationType.UPDATE, desc = "修改了角色名为#{name}的角色")
    public ResponseData update(@RequestBody SysRoleEntity entity) {
        String username = getUsername();
        if (sysRoleService.roleIdIsExist(entity.getId())) {
            return fail(RespCodeEnum.ROLE_NOEXIST);
        } else {
            sysRoleService.updateById(entity, username);
            return success(entity);
        }
    }

    /**
     * 通过Id删除角色
     *
     * @param id
     * @return
     */
    @DeleteMapping("/delete/{id}")
    @OperLog(module = "基础信息", type = OperationType.DELETE, desc = "删除了角色名为#{name}的角色")
    public ResponseData deleteById(@PathVariable Integer id) {
        String username = getUsername();
        if (sysRoleService.roleIdIsExist(id)) {
            return fail(RespCodeEnum.ROLE_NOEXIST);
        } else {
            SysRoleEntity sysRoleEntity = sysRoleService.deleteByRoleId(id, username);
            return success(sysRoleEntity);
        }

    }

    /**
     * 角色绑定用户
     *
     * @param roleId
     * @return
     */
    @PostMapping("/bind/{roleId}")
    public ResponseData add(@PathVariable("roleId") Integer roleId, String userIds, String removeUserIds) {
        String username = getUsername();
        String token = ExtApiUtil.getTokenBasedOnContext();
        sysRoleService.bind(roleId, userIds, removeUserIds, username, token);
        return success();
    }

    /**
     * 角色绑定Pad
     *
     * @param roleId
     * @return
     */
    @PostMapping("/bind/pad/{roleId}")
    public ResponseData addPad(@PathVariable("roleId") Integer roleId, String padIds, String removePadIds) {
        String username = getUsername();
        String token = ExtApiUtil.getTokenBasedOnContext();
        sysRoleService.bindPadRole(roleId, padIds, removePadIds, username, token);
        return success();
    }

    /**
     * 获取该角色对应的用户列表
     *
     * @param roleId 角色id
     */
    @GetMapping("/members/{id}")
    public ResponseData getMemberList(@PathVariable(value = "id") Integer roleId) {
        List<SysUserEntity> list = sysRoleService.getMemberList(Stream.of(roleId).collect(Collectors.toList()));
        return success(list);
    }

    /**
     * 获取该角色对应的Pad列表
     *
     * @param id 角色id
     */
    @GetMapping("/pad/members/{id}")
    public ResponseData getPadMemberList(@PathVariable(value = "id") String id) {
        List<SysPadEntity> list = sysRoleService.getPadMemberList(id);
        return success(list);
    }

    /**
     * 是否开启全选产线
     *
     * @param roleId
     * @return
     */
    @GetMapping("/whether/all/line/{roleId}")
    public ResponseData whetherAllLine(@PathVariable(value = "roleId") Integer roleId) {
        return success(sysRoleService.whetherAllLine(roleId));
    }

    /**
     * 获取模块角色表
     *
     * @return
     */
    @PostMapping("/module/list")
    public ResponseData getModuleRoleList(@RequestBody ModuleRoleSelectDTO selectDTO) {
        Page<ModuleRoleConfEntity> list = moduleRoleConfService.list(selectDTO);
        return success(list);
    }

    /**
     * 更新模块角色表
     *
     * @return
     */
    @PostMapping("/module/update")
    public ResponseData updateModuleRole(@RequestBody ModuleRoleUpdateDTO updateDTO) {
        moduleRoleConfService.updateModuleRole(updateDTO);
        return success();
    }
    @GetMapping("/roleUser/tree")
    public ResponseData roleUserTree() {
        List<RoleUserTreeVO> trees = sysRoleService.roleUserTree();
        return success(trees);
    }


}
