package com.yelink.dfs.controller.common;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yelink.dfs.common.BaseController;
import com.yelink.dfs.entity.common.OrderChangeLogEntity;
import com.yelink.dfs.service.common.OrderChangeLogService;
import com.yelink.dfscommon.pojo.ResponseData;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;


/**
 * @Description: 单据操作记录
 * @Author: Chensn
 * @Date: 2022/07/08
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/order/change/log")
public class OrderChangeLogController extends BaseController {

    private OrderChangeLogService orderChangeLogService;


    /**
     * 操作历史列表
     *
     * @param current
     * @param size
     * @return
     */
    @GetMapping("/list")
    public ResponseData changeLogList(
            @RequestParam(value = "orderNum", required = false) String orderNum,
            @RequestParam(value = "title", required = false) String title,
            @RequestParam(value = "module", required = false) String module,
            @RequestParam(value = "current", required = false) Integer current,
            @RequestParam(value = "size", required = false) Integer size) {
        Page<OrderChangeLogEntity> page = orderChangeLogService.changeLogList(orderNum, title, module, current, size);
        return success(page);
    }


}
