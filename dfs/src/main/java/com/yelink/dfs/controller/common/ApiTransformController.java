package com.yelink.dfs.controller.common;

import com.yelink.dfs.entity.common.ApiTransFormAddDTO;
import com.yelink.dfs.entity.common.ApiTransFormSelectDTO;
import com.yelink.dfs.entity.common.ApiTransFormUpdateDTO;
import com.yelink.dfs.entity.common.ApiTransformInvoker;
import com.yelink.dfs.entity.common.ApiTransformTriggerDTO;
import com.yelink.dfs.service.target.ApiTransformService;
import com.yelink.dfscommon.constant.ApiTransFormStateEnum;
import com.yelink.dfscommon.entity.CommonType;
import com.yelink.dfscommon.pojo.ResponseData;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/api_transform")
public class ApiTransformController {

    @Resource
    private ApiTransformService apiTransformService;

    @PostMapping("/page")
    public ResponseData page(@RequestBody ApiTransFormSelectDTO dto) {
        return ResponseData.success(apiTransformService.getPage(dto));
    }
    @PostMapping("/list")
    public ResponseData list(@RequestBody ApiTransFormSelectDTO dto) {
        return ResponseData.success(apiTransformService.getList(dto));
    }
    @PostMapping("/add")
    public ResponseData add(@RequestBody @Validated ApiTransFormAddDTO dto) {
        apiTransformService.add(dto);
        return ResponseData.success();
    }

    @PostMapping("/update")
    public ResponseData update(@RequestBody @Validated ApiTransFormUpdateDTO dto) {
        apiTransformService.update(dto);
        return ResponseData.success();
    }

    @GetMapping("/detail")
    public ResponseData detail(@RequestParam Integer id) {
        return ResponseData.success(apiTransformService.detail(id));
    }
    @GetMapping("/detail/code")
    public ResponseData detail(@RequestParam String code) {
        return ResponseData.success(apiTransformService.detail(code));
    }
    @DeleteMapping("/delete")
    public ResponseData delete(@RequestParam Integer id) {
        return ResponseData.success(apiTransformService.removeById(id));
    }
    /**
     * 预览
     */
    @PostMapping("/preview")
    public ResponseData transform(@RequestBody @Validated ApiTransformInvoker invoker) {
        return ResponseData.success(invoker.invoke());
    }

    @PostMapping("/trigger")
    public ResponseData trigger(@RequestBody @Validated ApiTransformTriggerDTO dto) {
        return ResponseData.success(apiTransformService.trigger(dto));
    }

    /**
     * 获取 api 结果列表的字段
     *  1. 若api返回的是数组对象，则列出对象的每个字段；
     *  2. 否则报错
     */
    @GetMapping("/showColumn")
    public ResponseData showColumn(@RequestParam Integer id) {
        return ResponseData.success(apiTransformService.showColumn(id));
    }

    @GetMapping("/stateEnum")
    public ResponseData stateEnum() {
        return ResponseData.success(CommonType.covertToList(ApiTransFormStateEnum.class));
    }
}
