package com.yelink.dfs.controller.reset;

import com.yelink.dfs.service.reset.ResetService;
import com.yelink.dfscommon.pojo.ResponseData;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.io.BufferedReader;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.file.Files;
import java.nio.file.StandardCopyOption;

/**
 * <AUTHOR>
 * @description: 恢复出厂设置接口
 * @time 2021/7/19 18:46
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/reset")
public class ResetController {

    private ResetService resetService;

    /**
     *  重置dfs
     * @param isBackUserConfig 是否备份用户数据
     * @param isBackDevelopmentConfig 是否备份开发数据
     * @return
     */
    @DeleteMapping("/dfs")
    public ResponseData resetDfs(@RequestParam Boolean isBackUserConfig,@RequestParam Boolean isBackDevelopmentConfig) {
        resetService.resetDfsAsync(isBackUserConfig,isBackDevelopmentConfig);
        return ResponseData.success();
    }


    /**
     * 恢复备份数据
     *
     * @param isBackUserConfig        是否备份用户数据
     * @param isBackDevelopmentConfig 是否备份开发数据
     * @return
     */
    @DeleteMapping("/reset/config")
    public ResponseData resetConfig(@RequestParam Boolean isBackUserConfig,@RequestParam Boolean isBackDevelopmentConfig) {
        resetService.resetDfsAsync(isBackUserConfig,isBackDevelopmentConfig);
        return ResponseData.success();
    }

    /**
     * 重置dfs相关数据
     */
    @DeleteMapping("/others")
    public ResponseData resetOthers(@RequestParam Boolean isBackUserConfig,@RequestParam Boolean isBackDevelopmentConfig) {
        resetService.resetOthersAsync(isBackUserConfig,isBackDevelopmentConfig);
        return ResponseData.success();
    }

}
