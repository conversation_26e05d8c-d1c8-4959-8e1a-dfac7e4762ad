package com.yelink.dfs.controller.product;

import com.asyncexcel.core.DataParam;
import com.asyncexcel.core.ExcelContext;
import com.asyncexcel.core.ExportPage;
import com.asyncexcel.core.annotation.ExcelHandle;
import com.asyncexcel.core.exporter.DataExportParam;
import com.asyncexcel.core.exporter.ExportContext;
import com.asyncexcel.core.exporter.ExportHandler;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yelink.dfs.entity.product.ProcedureInspectRecordEntity;
import com.yelink.dfs.entity.product.dto.ProcedureInspectRecordExportDTO;
import com.yelink.dfs.entity.product.dto.ProcedureInspectRecordSelectDTO;
import com.yelink.dfs.service.common.CommonService;
import com.yelink.dfs.service.product.ProcedureInspectRecordService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.List;


/**
 * 工序检验记录列表数据导出
 *
 * <AUTHOR>
 * @Date 2023/7/5 11:18
 */
@Slf4j
@ExcelHandle
@RequiredArgsConstructor
public class ProcedureInspectRecordListExportHandler implements ExportHandler<ProcedureInspectRecordExportDTO> {

    private final ProcedureInspectRecordService inspectRecordService;
    private final CommonService commonService;

    /**
     * 导出数据最大不能超过100万行
     */
    public static final long EXPORT_MAX_ROWS = 1000000;

    @Override
    public void init(ExcelContext ctx, DataParam param) {
        ExportContext context = (ExportContext) ctx;
        ProcedureInspectRecordSelectDTO selectDTO = (ProcedureInspectRecordSelectDTO) param.getParameters().get(ProcedureInspectRecordSelectDTO.class.getName());
        String cleanSheetNames = (String) param.getParameters().get("cleanSheetNames");
        commonService.initExcelContext(selectDTO.getTemplateId(), selectDTO.getTemplateSheetName(), cleanSheetNames, context, ProcedureInspectRecordExportDTO.class, null);
    }


    @Override
    public ExportPage<ProcedureInspectRecordExportDTO> exportData(int startPage, int limit, DataExportParam param) {
        ProcedureInspectRecordSelectDTO selectDTO = (ProcedureInspectRecordSelectDTO) param.getParameters().get(ProcedureInspectRecordSelectDTO.class.getName());
        selectDTO.setCurrent(startPage);
        selectDTO.setSize(limit);
        Page<ProcedureInspectRecordEntity> page = inspectRecordService.getList(selectDTO);
        List<ProcedureInspectRecordExportDTO> list = inspectRecordService.convertToExportDTO(page.getRecords());
        ExportPage<ProcedureInspectRecordExportDTO> result = new ExportPage<>();
        result.setTotal(Math.min(page.getTotal(), EXPORT_MAX_ROWS));
        result.setCurrent((long) startPage);
        result.setSize((long) limit);
        result.setRecords(list);
        return result;
    }


    @Override
    public void beforePerPage(ExportContext ctx, DataExportParam param) {
        log.info("开始查询第{}页", (long) Math.ceil(ctx.getSuccessCount() + ctx.getFailCount()) / ctx.getLimit());
    }

    @Override
    public void afterPerPage(List<ProcedureInspectRecordExportDTO> list, ExportContext ctx, DataExportParam param) {
        log.info("已完成数量：{}", ctx.getSuccessCount());
    }

    @Override
    public void callBack(ExcelContext ctx, DataParam param) {
        ExportContext context = (ExportContext) ctx;
        log.info("导出完成：{}", context.getFailMessage());
    }
}
