package com.yelink.dfs.controller.feed;

import com.asyncexcel.core.exporter.DataExportParam;
import com.asyncexcel.core.model.ExcelTask;
import com.asyncexcel.springboot.ExcelService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yelink.dfs.common.BaseController;
import com.yelink.dfs.constant.RedisKeyPrefix;
import com.yelink.dfs.entity.code.dto.BarCodeScanDTO;
import com.yelink.dfs.entity.code.dto.FeedRecordImportDTO;
import com.yelink.dfs.entity.feed.FeedRecordEntity;
import com.yelink.dfs.entity.feed.dto.FeedRecordDto;
import com.yelink.dfs.entity.order.WorkOrderEntity;
import com.yelink.dfs.open.v1.feed.dto.FeedRecordSelectDTO;
import com.yelink.dfs.service.common.DictService;
import com.yelink.dfs.service.feed.FeedRecordService;
import com.yelink.dfs.service.impl.feed.FeedRecordExportHandler;
import com.yelink.dfscommon.common.excel.BusinessCodeEnum;
import com.yelink.dfscommon.constant.ResponseException;
import com.yelink.dfscommon.constant.dfs.code.FeedOperationTypeEnum;
import com.yelink.dfscommon.constant.dfs.code.FeedStateEnum;
import com.yelink.dfscommon.entity.CommonType;
import com.yelink.dfscommon.pojo.ResponseData;
import com.yelink.dfscommon.utils.DateUtil;
import com.yelink.dfscommon.utils.EasyExcelUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @Description 上料记录
 * @Date 2022/3/29 18:02
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/feed/record")
public class FeedRecordController extends BaseController {

    private FeedRecordService feedRecordService;
    private DictService dictService;
    private RedisTemplate redisTemplate;
    private ExcelService excelService;

    /**
     * 上料记录列表
     *
     * @return
     */
    @PostMapping("/list")
    public ResponseData list(@RequestBody FeedRecordSelectDTO feedRecordSelectDTO) {
        return success(feedRecordService.listRecord(feedRecordSelectDTO));
    }

    /**
     * 按照条件进行导出
     *
     * @param feedRecordSelectDTO
     * @param response
     */
    @PostMapping("/excel/export")
    public void exportExcel(@RequestBody FeedRecordSelectDTO feedRecordSelectDTO,
                            HttpServletResponse response) throws IOException {
        //分布式锁
        Boolean lockStatus = redisTemplate.opsForValue().setIfAbsent(RedisKeyPrefix.DFS_FEED_RECORD_EXCEL_EXPORT, new Date(), 1, TimeUnit.MINUTES);
        if (lockStatus == null || !lockStatus) {
            throw new ResponseException("并发占用，稍后再试");
        }
        try {
            feedRecordService.exportExcel(feedRecordSelectDTO, response);
        } finally {
            redisTemplate.delete(RedisKeyPrefix.DFS_FEED_RECORD_EXCEL_EXPORT);
        }
    }

    /**
     * 默认导出模板
     */
    @GetMapping("/default/export/template")
    public void downloadSaleOrderDefaultExportTemplate(HttpServletResponse response) throws IOException {
        FeedRecordSelectDTO selectDTO = new FeedRecordSelectDTO();
        selectDTO.setCurrent(1);
        selectDTO.setSize(10);
        IPage<FeedRecordEntity> page = feedRecordService.listRecord(selectDTO);
        List<FeedRecordImportDTO> list = feedRecordService.convertToExportDTO(page.getRecords());
        EasyExcelUtil.export(response, "上料记录默认导出模板", "数据源", list, FeedRecordImportDTO.class);
    }

    /**
     * 异步导出
     *
     * @param dto
     * @return
     */
    @PostMapping("/syc/export")
    public ResponseData exportExcel(@RequestBody FeedRecordSelectDTO dto) {
        DataExportParam<FeedRecordSelectDTO> dataExportParam = new DataExportParam<>();
        dataExportParam.setExportFileName(BusinessCodeEnum.FEED_RECORD.getCodeName());
        dataExportParam.setLimit(10000);
        dataExportParam.setBusinessCode(BusinessCodeEnum.FEED_RECORD.name());
        dataExportParam.setCreateUserCode(getUsername());
        dto.setTemplateSheetName("数据源");
        Map<String, Object> parameters = new HashMap<>(8);
        parameters.put(FeedRecordSelectDTO.class.getName(), dto);
        parameters.put("cleanSheetNames", "数据源");
        dataExportParam.setParameters(parameters);
        Long result = excelService.doExport(dataExportParam, FeedRecordExportHandler.class);
        return success(result);
    }

    /**
     * 分页查询当前导出任务列表
     *
     * @param pageSize
     * @param currentPage
     * @return
     */
    @GetMapping("/export/task/page")
    public ResponseData exportExcel(@RequestParam(required = false, defaultValue = "20") Integer pageSize,
                                    @RequestParam(required = false, defaultValue = "1") Integer currentPage) {
        ExcelTask excelTask = new ExcelTask();
        excelTask.setBusinessCode(BusinessCodeEnum.FEED_RECORD.name());
        return success(excelService.listPage(excelTask, currentPage, pageSize));
    }

    /**
     * 查询导出进度
     *
     * @return
     */
    @GetMapping("/export/task/{taskId}")
    public ResponseData exportExcel(@PathVariable Long taskId) {
        ExcelTask excelTask = new ExcelTask();
        excelTask.setId(taskId);
        excelTask.setBusinessCode(BusinessCodeEnum.FEED_RECORD.name());
        IPage<ExcelTask> excelTaskIPage = excelService.listPage(excelTask, 1, 1);
        List<ExcelTask> records = excelTaskIPage.getRecords();
        if (CollectionUtils.isEmpty(records)) {
            log.info("任务id错误");
            return fail();
        }
        return success(records.get(0));
    }

    /**
     * 工单追溯---物料追溯（上料记录信息）
     *
     * @param workOrderId
     * @return
     */
    @GetMapping("/trace/material")
    public ResponseData getFeedRecordInfoTrace(@RequestParam(value = "workOrderId") Integer workOrderId, @RequestParam(value = "facId", required = false) Integer facId, @RequestParam(value = "startDate", required = false) Long startDate,
                                               @RequestParam(value = "endDate", required = false) Long endDate) {
        return success(feedRecordService.getFeedRecordInfoTrace(workOrderId, facId, startDate, endDate));
    }

    /**
     * 修改上料记录
     *
     * @param feedRecordEntity
     * @return
     */
    @PutMapping("/edit")
    public ResponseData editFeedRecord(@RequestBody FeedRecordEntity feedRecordEntity) {
        feedRecordService.updateById(feedRecordEntity);
        return success(feedRecordEntity);
    }

    /**
     * 保存移动端上料记录提交的数据
     *
     * @return
     */
    @PostMapping("/save/record")
    public ResponseData saveRecord(@RequestBody List<FeedRecordDto> feedRecordDtoList,
                                   @RequestParam(value = "workOrderId") Integer workOrderId,
                                   @RequestParam(value = "reportFacId") Integer facId) {
        String username = getUsername();
        feedRecordService.saveFeedRecord(feedRecordDtoList, workOrderId, facId, username);
        return success();
    }

    /**
     * 上料记录小程序中通过批次获取批次信息
     *
     * @param
     * @return
     */
    @GetMapping("/bar/code")
    public ResponseData getBarCodeByCode(@RequestParam(value = "barCode") String barCode) {
        BarCodeScanDTO barCodeScanDTO = feedRecordService.getBarCodeByCode(barCode);
        return success(barCodeScanDTO);
    }

    /**
     * 添加上料记录并增加出库记录
     *
     * @return
     */
    @PostMapping("/save/record/and/out/delivery")
    public ResponseData saveRecord(@RequestBody FeedRecordEntity feedRecordEntity) {
        String username = getUsername();
        feedRecordEntity.setFeedBy(username);
        feedRecordEntity.setFeedTime(new Date());
        if (feedRecordEntity.getIsWaste()) {
            //废料不需要出库
            feedRecordService.addFeedRecord(feedRecordEntity);
        } else {
            feedRecordService.addFeedRecordAndOutDelivery(feedRecordEntity);
        }
        return success();
    }


    /**
     * 拿到工单上料总重量,和有上料的天数
     *
     * @param workOrderEntity
     * @return
     */
    @PostMapping("/feed/value/sum")
    public ResponseData getFeedRecordInfoTrace(@RequestBody WorkOrderEntity workOrderEntity) {
        if (StringUtils.isBlank(workOrderEntity.getWorkOrderNumber())) {
            throw new ResponseException("请传入workOrderNumber");
        }
        Date date = dictService.getRecordDate(new Date());
        LambdaQueryWrapper<FeedRecordEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(FeedRecordEntity::getWorkOrderNum, workOrderEntity.getWorkOrderNumber());
        List<FeedRecordEntity> list = feedRecordService.list(queryWrapper);
        Set<String> set = new HashSet<>();
        set.add(DateUtil.format(date, DateUtil.DATE_FORMAT));
        set.add(DateUtil.format(new Date(), DateUtil.DATE_FORMAT));
        for (FeedRecordEntity feedRecordEntity : list) {
            set.add(DateUtil.format(feedRecordEntity.getFeedTime(), DateUtil.DATE_FORMAT));
        }
        Double result = list.stream().mapToDouble(FeedRecordEntity::getMaterialNum).sum();
        Map<String, Object> map = new HashMap<>();
        map.put("numSum", result);
        map.put("dateList", set);
        return success(map);
    }

    /**
     * 根据物料原始code获取相关信息
     *
     * @param code
     * @return
     */
    @PostMapping("/get/feed/record/by/code")
    public ResponseData getFeedRecordByCode(@RequestParam(value = "code") String code) {
        FeedRecordEntity feedRecordEntity = new FeedRecordEntity();
        feedRecordService.buildFeedRecordMaterialProp(feedRecordEntity, code);
        return success(feedRecordEntity);
    }

    /**
     * 上料记录操作类型枚举列表
     *
     * @return
     */
    @GetMapping("/operation/type/list")
    public ResponseData getOperationTypeList() {
        return success(CommonType.covertToList(FeedOperationTypeEnum.class));
    }

    /**
     * 状态枚举列表
     *
     * @return
     */
    @GetMapping("/state/list")
    public ResponseData getStateList() {
        return success(CommonType.covertToList(FeedStateEnum.class));
    }

}
