package com.yelink.dfs.controller.device;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yelink.dfs.common.BaseController;
import com.yelink.dfs.entity.device.dto.DeviceProgressDetailSelectDTO;
import com.yelink.dfs.entity.device.dto.DeviceProgressSelectDTO;
import com.yelink.dfs.entity.device.vo.DeviceProgressParamDetailExcelVO;
import com.yelink.dfs.entity.device.vo.DeviceProgressParamDetailVO;
import com.yelink.dfs.entity.device.vo.DeviceProgressParamExcelVO;
import com.yelink.dfs.entity.device.vo.DeviceProgressParamVO;
import com.yelink.dfs.service.impl.device.DeviceProgressService;
import com.yelink.dfscommon.pojo.ResponseData;
import com.yelink.dfscommon.utils.EasyExcelUtil;
import com.yelink.dfscommon.utils.JacksonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR>
 * 单品的设备加工参数
 */
@Slf4j
@RestController
@RequestMapping("/devices/progress")
public class DeviceProgressController extends BaseController {

    @Resource
    private DeviceProgressService deviceProgressService;

    @PostMapping("/param/page")
    public ResponseData page(@RequestBody DeviceProgressSelectDTO dto) {
        Page<DeviceProgressParamVO> page = deviceProgressService.page(dto);
        return success(page);
    }
    @PostMapping("/param/page-export")
    public ResponseData pageExport(@RequestBody DeviceProgressSelectDTO dto, HttpServletResponse response) throws IOException {
        Page<DeviceProgressParamVO> page = deviceProgressService.page(dto);
        List<DeviceProgressParamExcelVO> result = JacksonUtil.convertArray(page.getRecords(), DeviceProgressParamExcelVO.class);
        EasyExcelUtil.export(response, "单品的设备加工参数", "sheet", result, DeviceProgressParamExcelVO .class);
        return success(page);
    }

    @PostMapping("/param/detail")
    public ResponseData detailPage(@RequestBody DeviceProgressDetailSelectDTO dto) {
        Page<DeviceProgressParamDetailVO> page = deviceProgressService.detail(dto);
        return success(page);
    }
    @PostMapping("/param/detail-export")
    public ResponseData detailPageExport(@RequestBody DeviceProgressDetailSelectDTO dto, HttpServletResponse response) throws IOException {
        Page<DeviceProgressParamDetailVO> page = deviceProgressService.detail(dto);
        List<DeviceProgressParamDetailExcelVO> result = JacksonUtil.convertArray(page.getRecords(), DeviceProgressParamDetailExcelVO.class);
        EasyExcelUtil.export(response, "单品的设备加工参数明细", "sheet", result, DeviceProgressParamDetailExcelVO .class);
        return success(page);
    }


}
