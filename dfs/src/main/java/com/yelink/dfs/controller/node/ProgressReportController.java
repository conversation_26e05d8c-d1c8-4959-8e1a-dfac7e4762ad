package com.yelink.dfs.controller.node;

import com.yelink.dfs.common.BaseController;
import com.yelink.dfs.constant.node.NodeReportStateEnum;
import com.yelink.dfs.entity.node.NodeDefinitionEntity;
import com.yelink.dfs.entity.node.NodeProcessReportRecordEntity;
import com.yelink.dfs.entity.node.dto.OrderListDTO;
import com.yelink.dfs.service.node.NodeProcessReportRecordService;
import com.yelink.dfs.service.node.SaleOrderNodeConfigureService;
import com.yelink.dfscommon.constant.CommonEnum;
import com.yelink.dfscommon.pojo.ResponseData;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;


/**
 * <AUTHOR>
 * 进度上报
 * @Date 2022/4/22 10:41
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/progress/report")
public class ProgressReportController extends BaseController {

    private SaleOrderNodeConfigureService saleOrderNodeConfigureService;
    private NodeProcessReportRecordService reportRecordService;

    /**
     * 获取订单列表
     * (分成正常订单、异常订单、订单追溯)
     *
     * @param customerName
     * @param materialName
     * @param start
     * @param end
     * @return
     */
    @GetMapping("/order/list")
    public ResponseData list(@RequestParam(value = "customerName", required = false) String customerName,
                             @RequestParam(value = "materialName", required = false) String materialName,
                             @RequestParam(value = "remark", required = false) String remark,
                             @RequestParam(value = "start", required = false) String start,
                             @RequestParam(value = "end", required = false) String end) {
        OrderListDTO dto = saleOrderNodeConfigureService.getListForApp(customerName, materialName, remark, start, end);
        return ResponseData.success(dto);
    }

    /**
     * 获取订单物料的节点明细列表
     * (包含订单物料信息,节点最新记录)
     *
     * @param order
     * @param materialCode
     * @param nodeConfigureCode
     * @return
     */
    @GetMapping("/order/detail")
    public ResponseData orderDetail(@RequestParam(value = "order") String order,
                                    @RequestParam(value = "materialCode") String materialCode,
                                    @RequestParam(value = "nodeConfigureCode") String nodeConfigureCode) {
        List<NodeDefinitionEntity> list = saleOrderNodeConfigureService.orderDetail(order, materialCode, nodeConfigureCode);
        return ResponseData.success(list);
    }

    /**
     * 获取上报记录状态列表
     *
     * @return
     */
    @GetMapping("/record/states")
    public ResponseData getReportStates() {
        List<CommonEnum> enums = Stream.of(NodeReportStateEnum.values())
                .map(o -> CommonEnum.builder().code(o.getCode()).name(o.getName()).build()).collect(Collectors.toList());
        return ResponseData.success(enums);
    }

    /**
     * 新增节点记录
     *
     * @param entity
     * @return
     */
    @PostMapping("/record/add")
    public ResponseData addRecord(@RequestBody NodeProcessReportRecordEntity entity) {
        entity.setCreateBy(getUsername());
        reportRecordService.addEntity(entity);
        return ResponseData.success();
    }

    /**
     * 修改节点记录
     *
     * @param entity
     * @return
     */
    @PutMapping("/record/update")
    public ResponseData updateRecord(@RequestBody NodeProcessReportRecordEntity entity) {
        entity.setUpdateBy(getUsername());
        reportRecordService.updateEntity(entity);
        return ResponseData.success();
    }

    /**
     * 获取订单物料的节点上报记录
     *
     * @param order
     * @param materialCode
     * @param nodeDefinitionCode
     * @return
     */
    @GetMapping("/record/list")
    public ResponseData getReportRecord(@RequestParam(value = "order", required = false) String order,
                                        @RequestParam(value = "materialCode", required = false) String materialCode,
                                        @RequestParam(value = "nodeDefinitionCode", required = false) String nodeDefinitionCode) {
        List<NodeProcessReportRecordEntity> records = reportRecordService.getReportRecord(order, materialCode, nodeDefinitionCode);
        return ResponseData.success(records);
    }

    /**
     * 查询配置代号绑定的节点配置信息
     *
     * @param saleOrderNumber   销售订单号
     * @param materialCode      物料编码
     * @param nodeConfigureCode 配置代号
     * @return
     */
    @GetMapping("/node/configure/list")
    public ResponseData getNodeConfigureList(@RequestParam(value = "saleOrderNumber", required = false) String saleOrderNumber,
                                             @RequestParam(value = "materialCode", required = false) String materialCode,
                                             @RequestParam(value = "nodeConfigureCode", required = false) String nodeConfigureCode) {
        return ResponseData.success(reportRecordService.getNodeConfigureList(saleOrderNumber, materialCode, nodeConfigureCode));
    }

    /**
     * 查询节点上报列表信息
     *
     * @param saleOrderNumber
     * @param materialCode
     * @param nodeDefinitionCode
     * @return
     */
    @GetMapping("/node/report/list")
    public ResponseData getNodeReportList(@RequestParam(value = "saleOrderNumber", required = false) String saleOrderNumber,
                                          @RequestParam(value = "materialCode", required = false) String materialCode,
                                          @RequestParam(value = "nodeDefinitionCode", required = false) String nodeDefinitionCode) {
        return ResponseData.success(reportRecordService.getNodeReportList(saleOrderNumber, materialCode, nodeDefinitionCode));
    }

}
