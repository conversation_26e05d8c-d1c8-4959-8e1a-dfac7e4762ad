package com.yelink.dfs.controller.product;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yelink.dfs.common.BaseController;
import com.yelink.dfs.constant.Constant;
import com.yelink.dfs.constant.OperationType;
import com.yelink.dfs.constant.product.ProcedureInspectTypeEnum;
import com.yelink.dfs.constant.product.ProcedureStateEnum;
import com.yelink.dfs.constant.product.ProcessParameterInputModeEnum;
import com.yelink.dfs.constant.product.ProcessParameterTypeEnum;
import com.yelink.dfs.entity.common.CommonState;
import com.yelink.dfs.entity.model.ModelEntity;
import com.yelink.dfs.entity.product.ProcedureEntity;
import com.yelink.dfs.entity.product.dto.ProcedureSelectDTO;
import com.yelink.dfs.entity.product.vo.ProcedureExportVO;
import com.yelink.dfs.entity.supplier.SupplierEntity;
import com.yelink.dfs.service.common.ImportProgressService;
import com.yelink.dfs.service.product.ProcedureService;
import com.yelink.dfscommon.annotation.OperLog;
import com.yelink.dfscommon.constant.ParamException;
import com.yelink.dfscommon.constant.RedisKeyPrefix;
import com.yelink.dfscommon.constant.RespCodeEnum;
import com.yelink.dfscommon.constant.dfs.OutsourcingProcedureEnum;
import com.yelink.dfscommon.constant.model.WorkCenterTypeEnum;
import com.yelink.dfscommon.dto.ApproveBatchDTO;
import com.yelink.dfscommon.entity.CommonType;
import com.yelink.dfscommon.entity.common.BatchChangeStateDTO;
import com.yelink.dfscommon.pojo.ResponseData;
import com.yelink.dfscommon.utils.EasyExcelUtil;
import com.yelink.dfscommon.utils.ExcelTemplateImportUtil;
import com.yelink.dfscommon.utils.ExcelUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 工序
 * @Date 2021/3/9
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/procedures")
public class ProcedureController extends BaseController {

    private ProcedureService procedureService;
    private ImportProgressService importProgressService;

    /**
     * 委外工序枚举：委外工序 0：否、1：完全、2：部分
     */
    @GetMapping("/outsourcing/procedure/enum")
    public ResponseData outsourcingProcedureEnum() {
        OutsourcingProcedureEnum[] values = OutsourcingProcedureEnum.values();
        List<CommonType> states = new ArrayList<>();
        for (OutsourcingProcedureEnum anEnum : values) {
            states.add(CommonType.builder()
                    .code(anEnum.getCode())
                    .name(anEnum.getName())
                    .build());
        }
        return ResponseData.success(states);
    }

    /**
     * 分页、模糊查询
     */
    @GetMapping("/list")
    public ResponseData list(ProcedureSelectDTO selectDTO) {
        Page<ProcedureEntity> list = procedureService.list(selectDTO);
        return success(list);
    }

    @GetMapping("/export")
    public ResponseData export(ProcedureSelectDTO selectDTO, HttpServletResponse response) throws IOException {
        List<ProcedureEntity> records = procedureService.list(selectDTO).getRecords();
        List<ProcedureExportVO> result = records.stream().map(ProcedureExportVO::convertToExport).collect(Collectors.toList());
        EasyExcelUtil.export(response, EasyExcelUtil.getMarkFields(ProcedureExportVO.class),
                "procedure", "procedureSheet", result, ProcedureExportVO.class);
        return ResponseData.success();
    }

    @GetMapping("/common/list")
    public ResponseData list() {
        return success(procedureService.lambdaQuery().eq(ProcedureEntity::getState, ProcedureStateEnum.RELEASED.getCode()).list());
    }

    /**
     * 通过Id查询工序信息
     *
     * @param id
     * @return
     */
    @GetMapping("/select/{id}")
    public ResponseData selectById(@PathVariable Integer id) {
        ProcedureEntity entity = procedureService.getEntityById(id);
        if (entity == null) {
            return fail(RespCodeEnum.PRODUCTION_FAIL2SEL);
        }
        return success(entity);
    }


    /**
     * 添加工序信息
     *
     * @param
     * @return
     */
    @PostMapping("/insert")
    @OperLog(module = "产品定义", type = OperationType.ADD, desc = "新增了编码为#{procedureCode}的工序")
    public ResponseData add(@RequestBody @Valid ProcedureEntity procedure, BindingResult bindingResult) {
        if (bindingResult.hasErrors()) {
            throw new ParamException(bindingResult);
        }
        String username = getUsername();
        procedure.setCreateBy(username);
        procedure.setUpdateBy(username);
        procedureService.saveEntity(procedure);
        return ResponseData.success(procedure);
    }

    /**
     * 添加生效工序信息
     *
     * @param
     * @return
     */
    @PostMapping("/insert/released")
    @OperLog(module = "产品定义", type = OperationType.ADD, desc = "新增了编码为#{procedureCode}的工序")
    public ResponseData addReleasedEntity(@RequestBody @Valid ProcedureEntity procedure, BindingResult bindingResult) {
        if (bindingResult.hasErrors()) {
            throw new ParamException(bindingResult);
        }
        String username = getUsername();
        procedure.setCreateBy(username);
        procedure.setUpdateBy(username);
        procedure.setCreateTime(new Date());
        procedure.setUpdateTime(new Date());
        procedureService.saveReleasedEntity(procedure);
        return ResponseData.success(procedure);
    }


    /**
     * 查询工序下绑定的工位类型列表
     *
     * @param procedureId
     * @return
     */
    @GetMapping("/model/type")
    public ResponseData getModelTypeList(@RequestParam(value = "procedureId") Integer procedureId) {
        List<ModelEntity> list = procedureService.getFacModelList(procedureId);
        return success(list);
    }

    /**
     * 查询工艺-工序关联的制造单元模型(若不传工序id，查询全部的制造单元模型)
     *
     * @param procedureId
     * @return
     */
    @GetMapping("/select/manufacturing/unit")
    public ResponseData getManufacturingUnitList(@RequestParam(value = "procedureId", required = false) Integer procedureId) {
        List<ModelEntity> list = procedureService.getManufacturingUnitList(procedureId);
        return success(list);
    }

    /**
     * 获取类型是委外供应商的供应商
     *
     * @return
     */
    @GetMapping("/supplier/name")
    public ResponseData getSupplierName() {
        List<SupplierEntity> list = procedureService.getSupplierName();
        return success(list);
    }

    /**
     * 修改工序信息
     *
     * @param procedure
     * @return
     */
    @PutMapping("/update")
    @OperLog(module = "产品定义", type = OperationType.UPDATE, desc = "修改了编码为#{procedureCode}的工序")
    public ResponseData update(@RequestBody ProcedureEntity procedure) {
        procedure.setUpdateTime(new Date());
        procedure.setUpdateBy(getUsername());
        boolean update = procedureService.updateEntityById(procedure);
        if (update) {
            return success(procedure);
        }
        return fail(RespCodeEnum.PROCEDURE_FAIL2UP);
    }

    @PostMapping("/batch/update/state")
    public ResponseData batchUpdate(@RequestBody BatchChangeStateDTO batchApprovalDTO) {
        procedureService.batchUpdateState(batchApprovalDTO);
        return ResponseData.success();
    }

    /**
     * 删除工序定义前的判断
     *
     * @param procedureCode 工序定义编码
     * @return
     */
    @GetMapping("/delete/before/judge")
    public ResponseData deleteBeforeJudge(@RequestParam(value = "procedureCode") String procedureCode) {
        return success(procedureService.removeBeforeJudge(procedureCode));
    }

    /**
     * 通过Id删除工序信息
     *
     * @param id
     * @return
     */
    @DeleteMapping("/delete/{id}")
//    @OperLog(module = "产品定义", type = OperationType.DELETE, desc = "删除了id为#{id}的工序")
    public ResponseData delete(@PathVariable Integer id) {
        procedureService.removeEntityById(id);
        return success();
    }

    /**
     * 获取BOM状态
     *
     * @return
     */
    @GetMapping("/state")
    public ResponseData getProcedureState() {
        List<CommonState> list = new ArrayList<>();
        ProcedureStateEnum[] values = ProcedureStateEnum.values();
        for (ProcedureStateEnum value : values) {
            CommonState type = new CommonState();
            type.setCode(value.getCode());
            type.setName(value.getName());
            list.add(type);
        }

        return success(list);
    }

    /**
     * 审批
     */
    @GetMapping("/examine/approve")
    public ResponseData examineOrApprove(@RequestParam(value = "approvalStatus") Integer approvalStatus,
                                         @RequestParam(value = "approvalSuggestion", required = false) String approvalSuggestion,
                                         @RequestParam(value = "id") Integer id) {
        String username = getUsername();
        procedureService.examineOrApprove(approvalStatus, approvalSuggestion, id, username);
        return success();
    }

    /**
     * 批量审批
     *
     * @return
     */
    @PostMapping("/approve/batch")
    public ResponseData approveBatch(@RequestBody ApproveBatchDTO dto) {
        dto.setActualApprover(getUsername());
        procedureService.approveBatch(dto);
        return success();
    }

    /**
     * 工序定义导入：下载默认导入模板
     */
    @GetMapping("/export/template")
    public void exportExcelTemplate(HttpServletResponse response) throws IOException {
        byte[] inputStream = ExcelUtil.exportDefaultExcel("classpath:template/procedureTemplate.xlsx");
        ExcelTemplateImportUtil.responseToClient(response, inputStream, "工序定义导入模板" + Constant.XLSX);
    }

    /**
     * 工序定义导入：导入excel
     */
    @PostMapping("/import")
    public ResponseData importExcel(MultipartFile file) throws IOException {
        //1、判断导入文件的合法性
        ExcelUtil.checkImportExcelFile(file);
        procedureService.importExcel(file.getOriginalFilename(), file.getInputStream(), getUsername());
        return success();
    }

    /**
     * 工序定义导入：获取导入的进度
     */
    @GetMapping("/import/progress")
    public ResponseData getImportProgress() {
        return success(importProgressService.importProgress(RedisKeyPrefix.PROCEDURE_IMPORT_PROGRESS));
    }


    /**
     * 获取工序定义可选工作中心
     *
     * @return
     */
    @GetMapping("/list/work/center/{id}")
    public ResponseData listWorkCenter(@PathVariable Integer id) {
        return success(procedureService.listWorkCenter(id));
    }

    /**
     * 获取检验类型枚举
     *
     * @param
     * @return
     */
    @GetMapping("/inspect/type/enum")
    public ResponseData getInspectTypeEnum() {
        List<CommonType> list = new ArrayList<>();
        ProcedureInspectTypeEnum[] values = ProcedureInspectTypeEnum.values();
        for (ProcedureInspectTypeEnum value : values) {
            CommonType type = new CommonType();
            type.setCode(value.getCode());
            type.setName(value.getName());
            list.add(type);
        }
        return success(list);
    }

    /**
     * 获取生产基本单元
     *
     * @param
     * @return
     */
    @GetMapping("/get/work/center/type")
    public ResponseData getWorkCenterTypeEnum() {
        List<CommonType> list = new ArrayList<>();
        WorkCenterTypeEnum[] values = WorkCenterTypeEnum.values();
        for (WorkCenterTypeEnum value : values) {
            CommonType type = new CommonType();
            type.setCode(value.getCode());
            type.setName(value.getName());
            list.add(type);
        }
        return success(list);
    }

    /**
     * 获取工序工艺参数类型枚举
     *
     * @param
     * @return
     */
    @GetMapping("/process/parameter/type/enum")
    public ResponseData getProcessParameterTypeEnum() {
        List<CommonType> list = new ArrayList<>();
        ProcessParameterTypeEnum[] values = ProcessParameterTypeEnum.values();
        for (ProcessParameterTypeEnum value : values) {
            CommonType type = new CommonType();
            type.setCode(value.getCode());
            type.setName(value.getName());
            list.add(type);
        }
        return success(list);
    }

    /**
     * 获取工序工艺参数输入方式枚举
     *
     * @param
     * @return
     */
    @GetMapping("/process/parameter/input/mode/enum")
    public ResponseData getProcessParameterInputModeEnum() {
        List<CommonType> list = new ArrayList<>();
        ProcessParameterInputModeEnum[] values = ProcessParameterInputModeEnum.values();
        for (ProcessParameterInputModeEnum value : values) {
            CommonType type = new CommonType();
            type.setCode(value.getCode());
            type.setName(value.getName());
            list.add(type);
        }
        return success(list);
    }

    /**
     * 获取工序工艺参数输入方式枚举
     *
     * @param
     * @return
     */
    @GetMapping("/get/procedure/list")
    public ResponseData getProcedureLists() {
        return success(procedureService.lambdaQuery().select(ProcedureEntity::getProcedureId, ProcedureEntity::getProcedureCode, ProcedureEntity::getName).list());
    }
}
