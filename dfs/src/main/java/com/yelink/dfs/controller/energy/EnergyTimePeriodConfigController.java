package com.yelink.dfs.controller.energy;

import com.yelink.dfs.common.BaseController;
import com.yelink.dfs.entity.energy.manage.EnergyTimePeriodConfigEntity;
import com.yelink.dfs.entity.energy.manage.EnergyTimePeriodEntity;
import com.yelink.dfs.service.energy.manage.EnergyTimePeriodConfigService;
import com.yelink.dfscommon.pojo.ResponseData;
import com.yelink.dfscommon.utils.DateUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;

/**
 * 用电时间段管理
 *
 * <AUTHOR>
 * @Date 2022/7/5 14:17
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/energy/time/period")
public class EnergyTimePeriodConfigController extends BaseController {
    private final EnergyTimePeriodConfigService energyTimePeriodConfigService;

    /**
     * 【用电时段管理】创建
     */
    @PostMapping("")
    public ResponseData save(@RequestBody EnergyTimePeriodConfigEntity timePeriodConfig) {
        timePeriodConfig.setCreateBy(getUsername());
        return ResponseData.success(energyTimePeriodConfigService.saveTimePeriodConfig(timePeriodConfig));
    }

    /**
     * 【用电时段管理】编辑
     */
    @PutMapping("")
    public ResponseData edit(@RequestBody EnergyTimePeriodConfigEntity timePeriodConfig) {
        Assert.notNull(timePeriodConfig.getId(), "id不能为null");

        timePeriodConfig.setUpdateBy(getUsername());
        return ResponseData.success(energyTimePeriodConfigService.updateTimePeriodConfig(timePeriodConfig));
    }

    /**
     * 【用电时段管理】删除
     */
    @DeleteMapping("{id}")
    public ResponseData delete(@PathVariable("id") Integer timePeriodConfigId) {
        return ResponseData.success(energyTimePeriodConfigService.deleteTimePeriodConfig(timePeriodConfigId));
    }

    /**
     * 【用电时段管理】列表
     */
    @GetMapping("/list")
    public ResponseData list(@RequestParam Integer current,
                             @RequestParam Integer size,
                             @RequestParam(required = false) String description) {
        return ResponseData.success(energyTimePeriodConfigService.listTimePeriodConfig(current, size, description));
    }

    /**
     * 【用电时段管理】获取下面的4个时段配置
     */
    @GetMapping("/configDetail")
    public ResponseData configDetail(@RequestParam("id") Integer timePeriodConfigId) {
        return ResponseData.success(energyTimePeriodConfigService.timePeriodConfigDetail(timePeriodConfigId));
    }

    /**
     * 【用电时段管理】时段配置-编辑
     */
    @PutMapping("/editConfigDetail")
    public ResponseData editTimePeriod(@RequestBody EnergyTimePeriodEntity energyTimePeriod) {
        Assert.notNull(energyTimePeriod.getId(), "id不能为null");
        Assert.notNull(energyTimePeriod.getConfigId(), "configId不能为null");

        return ResponseData.success(energyTimePeriodConfigService.editTimePeriod(energyTimePeriod));
    }

    /**
     * 测试接口
     */
    @GetMapping("/test/judgeTimePeriod")
    public ResponseData judgeTimePeriod(String dateStr) {
        Date energyTimePeriod = DateUtil.parse(dateStr, DateUtil.DATETIME_FORMAT);
        return ResponseData.success(energyTimePeriodConfigService.judgeTimePeriod(energyTimePeriod));
    }

}
