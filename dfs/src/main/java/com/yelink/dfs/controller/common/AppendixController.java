package com.yelink.dfs.controller.common;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yelink.dfs.common.BaseController;
import com.yelink.dfs.constant.AppendixTypeEnum;
import com.yelink.dfs.provider.FastDfsClientService;
import com.yelink.dfs.service.common.AppendixService;
import com.yelink.dfscommon.entity.AppendixEntity;
import com.yelink.dfscommon.pojo.ResponseData;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;

/**
 * @Description:附件controller
 * @Author: SQ
 * @Date: 2022/3/28 11:02
 * @Version:1.0
 */
@Slf4j
@RestController()
@AllArgsConstructor
@RequestMapping("/appendix")
public class AppendixController extends BaseController {

    private final AppendixService appendixService;
    private final FastDfsClientService fastDfsClientService;


    /**
     * 预览附件
     *
     * @param appendixId 附件Id
     * @return
     */
    @GetMapping("/file/preview")
    public void previewFile(Integer appendixId, HttpServletResponse response) throws IOException {
        //找到导出的模板
        ServletOutputStream outputStream = response.getOutputStream();
        AppendixEntity appendixEntity = appendixService.getById(appendixId);
        byte[] content = fastDfsClientService.downloadFile(appendixEntity.getFilePath());
        String fileName = URLEncoder.encode(appendixEntity.getFileName(), StandardCharsets.UTF_8.name());
        response.setHeader("Content-disposition", "attachment;filename=\"" + fileName +"\"; filename*=utf-8'zh_cn'"+ fileName );
        response.setContentType("application/pdf");
        response.setCharacterEncoding(StandardCharsets.UTF_8.name());
        ByteArrayInputStream byteArrayInputStream = new ByteArrayInputStream(content);
        //设置缓冲区
        int len;
        byte[] by = new byte[1024];
        //将input流写到缓冲区，然后经过outputStream返回客户端
        while ((len = byteArrayInputStream.read(by)) != -1) {
            outputStream.write(by, 0, len);
        }
        outputStream.flush();
        byteArrayInputStream.close();
        outputStream.close();
    }

    /**
     * 删除附件
     *
     * @param id 附件Id
     * @return
     */
    @DeleteMapping("/file/{id}")
    public ResponseData deleteFile(@PathVariable(value = "id") Integer id) {
        appendixService.deleteFile(id);
        return success();
    }

    /**
     * 根据关联id查询附件
     *
     * @return
     */
    @GetMapping("/files")
    public ResponseData getFilesBy(@RequestParam(value = "relateId") String relateId,
                                   @RequestParam(value = "isUsed") boolean isUsed,
                                   @RequestParam(value = "type") String type,
                                   @RequestParam(value = "currentPage", required = false) Integer currentPage,
                                   @RequestParam(value = "pageSize", required = false) Integer pageSize) {
        Page<AppendixEntity> page = appendixService.getFiles(relateId, type,currentPage, pageSize, isUsed);
        return ResponseData.success(page);
    }


    /**
     * 获取附件类型枚举
     *
     * @return
     */
    @GetMapping("/enums")
    public ResponseData getAppendixEnums() {
        return success(AppendixTypeEnum.toList());
    }


    /**
     * 删除附件
     *
     * @param id 附件Id
     * @return
     */
    @DeleteMapping("delete/file/{id}")
    public ResponseData deleteFileById(@PathVariable(value = "id") Integer id) {
        appendixService.deleteFileById(id);
        return success();
    }
}
