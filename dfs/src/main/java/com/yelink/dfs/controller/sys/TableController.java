package com.yelink.dfs.controller.sys;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yelink.dfs.constant.OperationType;
import com.yelink.dfs.constant.sys.TableFieldTypeEnum;
import com.yelink.dfs.constant.sys.TableIndexTypeEnum;
import com.yelink.dfs.entity.common.TableEntity;
import com.yelink.dfs.entity.sys.dto.SelectSqlDTO;
import com.yelink.dfs.entity.sys.dto.TableCreateOrUpdateDTO;
import com.yelink.dfs.entity.sys.dto.TableDeleteDTO;
import com.yelink.dfs.entity.sys.dto.TableFieldTypeDTO;
import com.yelink.dfs.entity.sys.dto.TableIndexTypeDTO;
import com.yelink.dfs.entity.sys.dto.TableSelectDTO;
import com.yelink.dfs.service.sys.TableService;
import com.yelink.dfscommon.annotation.OperLog;
import com.yelink.dfscommon.dto.CommonTableDTO;
import com.yelink.dfscommon.dto.dfs.TargetFieldOptionDTO;
import com.yelink.dfscommon.pojo.ResponseData;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 数据库表操作处理
 * @Date 2021/4/25
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/sys")
public class TableController {

    private TableService tableService;

    /**
     * 查询表结构列表
     *
     * @return
     */
    @PostMapping("/table/conf/list")
    public ResponseData getTableConfList(@RequestBody TableSelectDTO selectDTO) {
        Page<TableEntity> page = tableService.getTableConfList(selectDTO);
        return ResponseData.success(page);
    }

    /**
     * 获取表字段类型
     *
     * @return
     */
    @GetMapping("/table_field/type")
    public ResponseData getTableFieldType() {
        List<TableFieldTypeDTO> fieldTypeDTOS = Arrays.stream(TableFieldTypeEnum.values())
                .map(
                        o -> TableFieldTypeDTO.builder()
                                .type(o.getType())
                                .defaultLength(o.getDefaultLength())
                                .defaultPoint(o.getDefaultPoint())
                                .build()
                ).collect(Collectors.toList());
        return ResponseData.success(fieldTypeDTOS);
    }

    /**
     * 获取表字段索引类型
     *
     * @return
     */
    @GetMapping("/table_index/type")
    public ResponseData getTableIndexType() {
        List<TableIndexTypeDTO> indexTypeDTOS = Arrays.stream(TableIndexTypeEnum.values()).map(o -> TableIndexTypeDTO.builder()
                .code(o.getCode())
                .name(o.getName())
                .build()).collect(Collectors.toList());
        return ResponseData.success(indexTypeDTOS);
    }

    /**
     * 建表
     *
     * @return
     */
    @PostMapping("/table/create")
    @OperLog(module = "系统管理", type = OperationType.ADD, desc = "创建了所属数据库为：#{tableSchema}的表：#{tableName}")
    public ResponseData createTableStructure(@RequestBody TableCreateOrUpdateDTO dto) {
        dto.setUsername(dto.getUsername());
        tableService.createTableStructure(dto);
        // 添加表结构定义
        tableService.saveTableConf(dto);
        return ResponseData.success(dto);
    }

    /**
     * 更新表结构
     *
     * @return
     */
    @PostMapping("/table/update")
    @OperLog(module = "系统管理", type = OperationType.UPDATE, desc = "修改了所属数据库为：#{tableSchema}的表：#{tableName}")
    public ResponseData updateTableStructure(@RequestBody TableCreateOrUpdateDTO dto) {
        dto.setUsername(dto.getUsername());
        tableService.updateTableStructure(dto);
        return ResponseData.success(dto);
    }

    /**
     * 删除表结构
     *
     * @return
     */
    @PostMapping("/table/delete")
    @OperLog(module = "系统管理", type = OperationType.DELETE, desc = "删除了所属数据库为：#{tableSchema}的表：#{tableName}")
    public ResponseData deleteTableStructure(@RequestBody TableDeleteDTO deleteDTO) {
        deleteDTO.setUsername(deleteDTO.getUsername());
        tableService.deleteTableStructure(deleteDTO);
        return ResponseData.success(deleteDTO);
    }

    /**
     * 检验查询sql的合法性
     *
     * @return
     */
    @PostMapping("/judge/sql/valid")
    public ResponseData judgeSqlValid(@RequestBody SelectSqlDTO dto) {
        return ResponseData.success(tableService.sqlIsValid(dto));
    }

    /**
     * sql数据预览，预览5条数据
     *
     * @return
     */
    @PostMapping("/data/preview")
    public ResponseData dataPreview(@RequestBody SelectSqlDTO dto) {
        return ResponseData.success(tableService.dataPreview(dto));
    }

    /**
     * 设置表字段的取值配置
     *
     * @return
     */
    @PostMapping("/field/value/set")
    public ResponseData setFieldValue(@RequestBody TargetFieldOptionDTO dto) {
        tableService.setFieldValue(dto);
        return ResponseData.success();
    }

    /**
     * 获取表字段的取值配置
     *
     * @return
     */
    @GetMapping("/field/value/detail/{id}")
    public ResponseData getFieldValueDetail(@PathVariable(value = "id") Integer id) {
        return ResponseData.success(tableService.getFieldValueDetail(id));
    }


    /**
     * 分页查询dfs相关数据库信息表
     *
     * @return
     */
    @GetMapping("/table/list")
    public ResponseData getDFSTableList(@RequestParam(value = "currentPage", defaultValue = "1") Integer currentPage,
                                        @RequestParam(value = "size", defaultValue = "10") Integer size,
                                        @RequestParam(value = "tableName", required = false) String tableName,
                                        @RequestParam(value = "tableComment", required = false) String tableComment) {
        Page<CommonTableDTO> page = tableService.getDfsTableList(new Page(currentPage, size), tableName, tableComment);
        return ResponseData.success(page);
    }

    /**
     * 获取dfs相关数据库信息表
     *
     * @return
     */
    @GetMapping("/table/all")
    public ResponseData getAllDfsTable() {
        List<CommonTableDTO> list = tableService.getAllDfsTable();
        return ResponseData.success(list);
    }

    /**
     * 清理数据
     *
     * @param tableName 数据库表名
     * @param time      时间
     */
    @DeleteMapping("/table/clean")
    public ResponseData cleanDfsData(@RequestParam(value = "tableName") String tableName,
                                     @RequestParam(value = "time") String time) {
        tableService.cleanDfsData(tableName, time);
        return ResponseData.success("开始清理数据");
    }

}
