package com.yelink.dfs.controller.video;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yelink.dfs.common.BaseController;
import com.yelink.dfs.entity.video.VideoEntity;
import com.yelink.dfs.entity.video.VideoPlayEntity;
import com.yelink.dfs.entity.video.dto.YelinkVideoDeviceStreamInfoDTO;
import com.yelink.dfs.entity.video.vo.VideoDeviceTreeVO;
import com.yelink.dfs.service.video.VideoService;
import com.yelink.dfscommon.constant.Constant;
import com.yelink.dfscommon.constant.ResponseException;
import com.yelink.dfscommon.pojo.ResponseData;
import com.yelink.dfscommon.utils.DateUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.Assert;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 视频相关请求
 * <AUTHOR>
 * @Date 2021/7/16 9:57
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/videos")
public class YelinkVideoController extends BaseController {

    private final VideoService videoService;


    /**
     * 添加
     * @return
     */
    @PostMapping("/add")
    public ResponseData add(@RequestBody @Validated VideoEntity videoEntity){
        videoEntity.setAddTime(new Date());
        videoEntity.setUpdateTime(new Date());
        videoEntity.setCreateBy(getUsername());
        videoEntity.setUpdateBy(getUsername());
        videoService.saveVideoEntity(videoEntity);
        return ResponseData.success();
    }

    /**
     * 修改
     */
    @PostMapping("/update")
    public ResponseData update(@RequestBody @Validated VideoEntity videoEntity){
        videoEntity.setUpdateTime(new Date());
        videoEntity.setUpdateBy(getUsername());
        videoService.updateVideoEntityById(videoEntity);
        return ResponseData.success();
    }

    /**
     * 删除
     * @param id
     * @return
     */
    @PutMapping("/delete/{id}")
    public ResponseData delete(@PathVariable String id){
        if(StringUtils.isNumeric(id)){
            videoService.removeVideoEntityById(Integer.parseInt(id));
        }
        return ResponseData.success();
    }


    /**
     * 分页查询
     */
    @GetMapping("/list")
    public ResponseData list(@RequestParam(value = "deviceName", required = false) String deviceName,
                             @RequestParam(value = "deviceCode", required = false) String deviceCode,
                             @RequestParam(value = "currentPage", required = false , defaultValue = "1") Integer current,
                             @RequestParam(value = "pageSize", required = false,defaultValue = "10") Integer size){
        IPage<VideoEntity> page = videoService.page(deviceName, deviceCode, current, size);
        return ResponseData.success(page);
    }

    /**
     * 查询全部视频设备
     */
    @GetMapping("/all")
    public ResponseData all(){
        return ResponseData.success(videoService.list());
    }

    /**
     * 按工厂模型树返回视频设备
     */
    @GetMapping("/tree/list")
    public ResponseData treeList() {
        List<VideoDeviceTreeVO> res = videoService.treeList();
        return ResponseData.success(res);
    }

    /**
     * 通过设备code查询设备
     * @param deviceCode
     * @return
     */
    @GetMapping("/select/{deviceCode}")
    public ResponseData selectByDeviceCode(@PathVariable String deviceCode){
        VideoEntity videoEntity = videoService.selectByDeviceCode(deviceCode);
        return ResponseData.success(videoEntity);
    }

    /**
     * 获取设备流信息, 多个累加
     */
    @GetMapping("/device/stream/info")
    public ResponseData getDeviceStreamInfo(@RequestParam String deviceCodes){
        String[] deviceCodeArr = deviceCodes.split(Constant.SEP);
        Assert.notEmpty(deviceCodeArr, "设备编号不能为空");
        YelinkVideoDeviceStreamInfoDTO resp = YelinkVideoDeviceStreamInfoDTO.builder()
                .onlineNums(0)
                .recvBandwidth(0)
                .sendBandwidth(0)
                .build();
        for (String deviceCode : deviceCodeArr) {
            YelinkVideoDeviceStreamInfoDTO deviceStreamInfo = videoService.getYelinkVideoDeviceStreamInfo(deviceCode);
            if (Objects.nonNull(deviceStreamInfo)) {
                resp.setOnlineNums(resp.getOnlineNums() + deviceStreamInfo.getOnlineNums());
                resp.setRecvBandwidth(resp.getRecvBandwidth() + deviceStreamInfo.getRecvBandwidth());
                resp.setSendBandwidth(resp.getSendBandwidth() + deviceStreamInfo.getSendBandwidth());
            }
        }
        return ResponseData.success(resp);
    }


    /**
     * 视频回放
     * @param netIden 网络标识
     * @param startTime
     * @param endTime
     * @param deviceCode
     * @return
     */
    @GetMapping("/back/play")
    public ResponseData getVideoPlayback(@RequestHeader(value = "Request-Network-Source",required = false) String netIden,
                                         @RequestParam(value = "startTime") String startTime,
                                         @RequestParam(value = "endTime") String endTime,
                                         @RequestParam(value = "deviceCode") String deviceCode)  {
        // 比较开始时间与当前时间
        Date nowDate = new Date();
        Date start = DateUtil.parse(startTime, DateUtil.DATETIME_FORMAT);
        if (nowDate.compareTo(start) < 0) {
            throw new ResponseException("开始时间超过当前时间");
        }
        VideoPlayEntity videoPlayEntity = videoService.playback(netIden,startTime,endTime, deviceCode);
        return ResponseData.success(videoPlayEntity);
    }

    /**
     * 直播
     * @param deviceCode
     * @return
     */
    @GetMapping("/live/play")
    public ResponseData getVideoPlayback(@RequestHeader(value = "Request-Network-Source",required = false) String netIden,
                                         @RequestParam(value = "deviceCode") String deviceCode)  {
        VideoPlayEntity videoPlayEntity = videoService.playLive(netIden,deviceCode);
        return ResponseData.success(videoPlayEntity);
    }

    /**
     * 云台控制
     */
    @GetMapping("/control")
    public ResponseData control(@RequestParam String deviceCode,
                                @RequestParam String posX,
                                @RequestParam String posY,
                                @RequestParam String zoom) {
        videoService.control(deviceCode, posX, posY, zoom);
        return ResponseData.success();
    }


}
