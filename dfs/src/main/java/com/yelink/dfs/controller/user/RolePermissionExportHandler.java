package com.yelink.dfs.controller.user;

import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.asyncexcel.core.DataParam;
import com.asyncexcel.core.ExcelContext;
import com.asyncexcel.core.ExportPage;
import com.asyncexcel.core.annotation.ExcelHandle;
import com.asyncexcel.core.exporter.DataExportParam;
import com.asyncexcel.core.exporter.ExportContext;
import com.asyncexcel.core.exporter.ExportHandler;
import com.yelink.dfs.constant.Constant;
import com.yelink.dfs.entity.user.dto.PermissionExportDTO;
import com.yelink.dfs.entity.user.dto.PermissionExportSelectDTO;
import com.yelink.dfs.service.user.SysPermissionService;
import com.yelink.dfscommon.utils.ExcelUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;


/**
 * 生产工单列表数据导出
 *
 * <AUTHOR>
 * @Date 2023/7/5 11:18
 */
@Slf4j
@ExcelHandle
@RequiredArgsConstructor
public class RolePermissionExportHandler implements ExportHandler<PermissionExportDTO> {

    private final SysPermissionService sysPermissionService;

    /**
     * 导出数据最大不能超过100万行
     */
    public static final long EXPORT_MAX_ROWS = 1000000;

    @Override
    public void init(ExcelContext ctx, DataParam param) {
        ExportContext context = (ExportContext) ctx;
        ExcelUtil.cleanSheet(context.getExcelWriter(),"角色操作权限");
        WriteSheet sheet = EasyExcelFactory.writerSheet( "角色操作权限").head(PermissionExportDTO.class).build();
        context.setWriteSheet(sheet);
    }

    @Override
    public ExportPage<PermissionExportDTO> exportData(int startPage, int limit, DataExportParam param) {
        PermissionExportSelectDTO selectDTO = (PermissionExportSelectDTO) param.getParameters().get(PermissionExportSelectDTO.class.getName());
        List<Integer> roleList = Arrays.stream(selectDTO.getRoleIds().split(Constant.SEP)).map(Integer::valueOf).collect(Collectors.toList());
        List<PermissionExportDTO> permissions = sysPermissionService.getPermissions(roleList);
        ExportPage<PermissionExportDTO> result = new ExportPage<>();
        result.setTotal(Math.min(permissions.size(), EXPORT_MAX_ROWS));
        result.setCurrent((long) startPage);
        result.setSize((long) limit);
        result.setRecords(permissions);
        return result;
    }


    @Override
    public void beforePerPage(ExportContext ctx, DataExportParam param) {
        log.info("开始查询第{}页", (long) Math.ceil(ctx.getSuccessCount() + ctx.getFailCount()) / ctx.getLimit());
    }

    @Override
    public void afterPerPage(List<PermissionExportDTO> list, ExportContext ctx, DataExportParam param) {
        log.info("已完成数量：{}", ctx.getSuccessCount());
    }

    @Override
    public void callBack(ExcelContext ctx, DataParam param) {
        ExportContext context = (ExportContext) ctx;
        log.info("导出完成：{}", context.getFailMessage());
    }
}
