package com.yelink.dfs.controller.common.config;

import com.yelink.dfs.common.BaseController;
import com.yelink.dfs.entity.common.CommonType;
import com.yelink.dfs.service.common.config.FormConfigService;
import com.yelink.dfs.service.common.config.FormFieldModuleConfigService;
import com.yelink.dfs.service.common.config.FormFieldRuleConfigService;
import com.yelink.dfscommon.constant.dfs.form.FormFormulaTypeEnum;
import com.yelink.dfscommon.constant.InputTypeEnum;
import com.yelink.dfscommon.constant.dfs.form.OverallFormFieldMenuEnum;
import com.yelink.dfscommon.dto.common.config.FormFieldOptionDTO;
import com.yelink.dfscommon.dto.common.config.FormFieldRuleSycnDTO;
import com.yelink.dfscommon.dto.common.config.FormFieldUpdateDTO;
import com.yelink.dfscommon.dto.common.config.FormOverallInsertDTO;
import com.yelink.dfscommon.dto.common.config.FormOverallUpdateDTO;
import com.yelink.dfscommon.dto.common.config.FullPathCodeDTO;
import com.yelink.dfscommon.dto.common.config.FullRouteCodeDTO;
import com.yelink.dfscommon.dto.dfs.FormFieldRuleConfigUpdateDTO;
import com.yelink.dfscommon.entity.dfs.DictEntity;
import com.yelink.dfscommon.entity.dfs.FormConfigEntity;
import com.yelink.dfscommon.entity.dfs.FormOverallFieldConfigEntity;
import com.yelink.dfscommon.pojo.ResponseData;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/11/1 15:01
 */
@RestController
@AllArgsConstructor
@RequestMapping("/form/config")
public class FormConfigController extends BaseController {

    private FormConfigService formConfigService;
    private FormFieldRuleConfigService formFieldRuleConfigService;
    private FormFieldModuleConfigService formFieldModuleConfigService;

    /**
     * 查询表单菜单
     *
     * @param type       查询字段规则配置树（app-小程序  web-后台）
     * @param isNameTree 是否为字段命名树（true-字段命名树  false-字段规则树）
     * @return List<FormConfigEntity> 表单菜单树结构
     */
    @GetMapping("/tree")
    public ResponseData getTreeByFullPathCode(@RequestParam(value = "type") String type,
                                              @RequestParam(value = "isNameTree", required = false) Boolean isNameTree) {
        List<FormConfigEntity> options = formConfigService.getTreeByFullPathCode(type, isNameTree);
        return success(options);
    }

    /**
     * 根据全路径编号查询表单命名详情
     *
     * @return
     */
    @PostMapping("/field/detail")
    public ResponseData detailByFullPathCode(@RequestBody FullPathCodeDTO dto) {
        return success(formConfigService.getFormFieldDetailList(dto.getFullPathCode(), dto.getModuleCode()));
    }

    /**
     * 更新表单字段命名名称
     *
     * @return
     */
    @PostMapping("/field/update")
    public ResponseData updateField(@RequestBody FormFieldUpdateDTO updateDTO) {
        formConfigService.updateField(updateDTO);
        return success();
    }

    /**
     * 获取表单命名字段类型列表
     *
     * @param type 可以为language语言、industry行业
     * @return
     */
    @GetMapping("/field/list")
    public ResponseData getFieldList(@RequestParam(value = "type", required = false) String type) {
        return success(formConfigService.getFieldList(type));
    }

    /**
     * 获取系统全局命名字段配置列表（返回3个dict配置，用于更新数据）
     *
     * @param
     * @return
     */
    @GetMapping("/field/global/detail")
    public ResponseData getFieldGlobalConf() {
        return success(formConfigService.getFieldGlobalConf());
    }

    /**
     * 更新系统全局命名字段配置
     *
     * @param
     * @return
     */
    @PostMapping("/field/global/update")
    public ResponseData updateFieldGlobalConf(@RequestBody List<DictEntity> dictEntities) {
        formConfigService.updateFieldGlobalConf(dictEntities);
        return success();
    }

    /**
     * 查询表单字段规则详情
     *
     * @return
     */
    @PostMapping("/field/rule/detail")
    public ResponseData detailRuleByFullPathCode(@RequestBody FullRouteCodeDTO dto) {
        dto.setUserName(getUsername());
        return success(formFieldRuleConfigService.detailRuleByFullPathCode(dto));
    }

    /**
     * 更新表单字段规则
     *
     * @return
     */
    @PostMapping("/field/rule/update")
    public ResponseData updateRuleByFullPathCode(@RequestBody List<FormFieldRuleConfigUpdateDTO> list) {
        formFieldRuleConfigService.updateRuleByFullPathCode(list);
        return success();
    }

    /**
     * 获取输入类型枚举
     *
     * @param
     * @return
     */
    @GetMapping("/input/type")
    public ResponseData getInputTypeEnum() {
        InputTypeEnum[] values = InputTypeEnum.values();
        List<CommonType> inputTypes = new ArrayList<>();
        for (InputTypeEnum typeEnum : values) {
            inputTypes.add(CommonType.builder().type(typeEnum.getType()).name(typeEnum.getTypeName()).build());
        }
        return success(inputTypes);
    }

    /**
     * 获取输入类型枚举
     *
     * @param
     * @return
     */
    @GetMapping("/formula/type")
    public ResponseData getFormulaTypeEnum() {
        FormFormulaTypeEnum[] values = FormFormulaTypeEnum.values();
        List<CommonType> types = new ArrayList<>();
        for (FormFormulaTypeEnum typeEnum : values) {
            types.add(CommonType.builder().type(typeEnum.getType()).name(typeEnum.getTypeName()).build());
        }
        return success(types);
    }

    /**
     * 获取输入类型枚举
     *
     * @param
     * @return
     */
    @GetMapping("/overall/field/menu")
    public ResponseData getOverallFieldMenuEnum() {
        OverallFormFieldMenuEnum[] values = OverallFormFieldMenuEnum.values();
        List<CommonType> types = new ArrayList<>();
        for (OverallFormFieldMenuEnum typeEnum : values) {
            types.add(CommonType.builder().type(typeEnum.getType()).name(typeEnum.getTypeName()).build());
        }
        return success(types);
    }

    /**
     * 获取日期时间选择器-时间类型枚举
     *
     * @param
     * @return
     */
    @GetMapping("/time/format/type")
    public ResponseData getTimeFormatTypeEnum() {
        return success(formFieldRuleConfigService.getTimeFormatTypeEnum());
    }

    /**
     * 表单字段规则选项配置
     *
     * @param
     * @return
     */
    @PostMapping("/field/option/conf")
    public ResponseData setOptionConf(@RequestBody FormFieldOptionDTO optionDTO) {
        formFieldRuleConfigService.setOptionConf(optionDTO);
        return success();
    }

    /**
     * 获取表单字段规则选项配置
     *
     * @param
     * @return
     */
    @GetMapping("/field/option/conf/get/{id}")
    public ResponseData getOptionConf(@PathVariable(value = "id") Integer id) {
        return success(formFieldRuleConfigService.getOptionConf(id));
    }

    /**
     * 同步字段选项值 到 根目录下-所有子目录的同字段选项值上
     *
     * @param
     * @return
     */
    @PostMapping("/field/option/conf/sycn")
    public ResponseData sycnOptionConf(@RequestBody FormFieldRuleSycnDTO sycnDTO) {
        formFieldRuleConfigService.sycnOptionConf(sycnDTO);
        return success();
    }

    /**
     * 获取全局表单配置列表
     *
     * @param
     * @return
     */
    @PostMapping("/overall/list")
    public ResponseData getOverallFormList() {
        return success(formFieldRuleConfigService.getOverallList());
    }

    /**
     * 获取全局表单配置详情
     *
     * @param
     * @return
     */
    @GetMapping("/overall/detail/{id}")
    public ResponseData getOverallFormDetail(@PathVariable(value = "id") Integer id) {
        return success(formFieldRuleConfigService.getOverallDetail(id));
    }

    /**
     * 新增全局表单配置
     *
     * @param
     * @return
     */
    @PostMapping("/overall/add")
    public ResponseData addOverallForm(@RequestBody FormOverallInsertDTO insertDTO) {
        formFieldRuleConfigService.addOverallForm(insertDTO);
        return success();
    }

    /**
     * 更新全局表单配置
     *
     * @param
     * @return
     */
    @PutMapping("/overall/update")
    public ResponseData updateOverallForm(@RequestBody FormOverallUpdateDTO updateDTO) {
        formFieldRuleConfigService.updateOverallForm(updateDTO);
        return success();
    }

    /**
     * 删除全局表单配置
     *
     * @param
     * @return
     */
    @DeleteMapping("/overall/delete/{id}")
    public ResponseData deleteOverallForm(@PathVariable(value = "id") Integer id) {
        formFieldRuleConfigService.deleteOverallForm(id);
        return success();
    }

    /**
     * 查询当前可映射的单据类型
     *
     * @return
     */
    @GetMapping("/overall/order_type/list")
    public ResponseData getOverallOrderTypeList() {
        return success(formFieldRuleConfigService.getOverallOrderTypeList());
    }

    /**
     * 查询单据类型对应的可下推的字段
     *
     * @param orderType 单据类型
     * @return
     */
    @GetMapping("/overall/order_mapping/push/list")
    public ResponseData getOverallOrderMappingPushList(@RequestParam(value = "orderType") String orderType) {
        return success(formFieldRuleConfigService.getOverallOrderMappingPushList(orderType));
    }

    /**
     * 将全局字段配置应用到表单配置中
     * @param isClearHistoryConf 是否清除对应单据中原有拓展字段配置
     * @return
     */
    @PostMapping("/overall/apply")
    public ResponseData applyToForm(@RequestBody List<FormOverallFieldConfigEntity> list,
                                    @RequestParam(value = "isClearHistoryConf", defaultValue = "true") Boolean isClearHistoryConf) {
        formFieldRuleConfigService.applyToForm(list, isClearHistoryConf);
        return success();
    }

    /**
     * 查询表单模块列表
     *
     * @return
     */
    @PostMapping("/field/module/list")
    public ResponseData moduleListByFullPathCode(@RequestBody FullPathCodeDTO dto) {
        return success(formFieldModuleConfigService.moduleListByFullPathCode(dto));
    }

}
