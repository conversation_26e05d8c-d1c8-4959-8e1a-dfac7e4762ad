package com.yelink.dfs.controller.order;

import com.alibaba.fastjson.JSON;
import com.asyncexcel.core.DataParam;
import com.asyncexcel.core.ExcelContext;
import com.asyncexcel.core.ExportPage;
import com.asyncexcel.core.annotation.ExcelHandle;
import com.asyncexcel.core.exporter.DataExportParam;
import com.asyncexcel.core.exporter.ExportContext;
import com.asyncexcel.core.exporter.ExportHandler;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yelink.dfs.entity.model.dto.ScheduleDTO;
import com.yelink.dfs.entity.order.WorkOrderEntity;
import com.yelink.dfs.entity.order.dto.WorkOrderExportDTO;
import com.yelink.dfs.entity.order.dto.WorkOrderScheduleDTO;
import com.yelink.dfs.entity.order.dto.WorkOrderScheduleExportDTO;
import com.yelink.dfs.entity.order.dto.WorkOrderScheduleSelectDTO;
import com.yelink.dfs.entity.order.dto.WorkOrderSelectDTO;
import com.yelink.dfs.service.common.CommonService;
import com.yelink.dfs.service.order.WorkOrderScheduleService;
import com.yelink.dfs.service.order.WorkOrderService;
import com.yelink.dfscommon.constant.ExcelExportFormEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;


/**
 * 生产工单列表数据导出
 *
 * <AUTHOR>
 * @Date 2023/7/5 11:18
 */
@Slf4j
@ExcelHandle
@RequiredArgsConstructor
public class WorkOrderScheduleExport1Handler implements ExportHandler<WorkOrderScheduleExportDTO> {

    private final WorkOrderScheduleService workOrderScheduleService;
    private final CommonService commonService;

    /**
     * 导出数据最大不能超过100万行
     */
    public static final long EXPORT_MAX_ROWS = 1000000;

    @Override
    public void init(ExcelContext ctx, DataParam param) {
        ExportContext context = (ExportContext) ctx;
        ScheduleDTO selectDTO = (ScheduleDTO) param.getParameters().get(ScheduleDTO.class.getName());
        String sheetNames = (String) param.getParameters().get("notSchedule");
        String cleanSheetNames = (String) param.getParameters().get("clearSheetNames");
        commonService.initExcelContext(selectDTO.getTemplateId(), sheetNames,cleanSheetNames, context, WorkOrderScheduleExportDTO.class,ExcelExportFormEnum.WORK_SCHEDULE_PRODUCT_TO.getFullPathCode());
    }


    @Override
    public ExportPage<WorkOrderScheduleExportDTO> exportData(int startPage, int limit, DataExportParam param) {
        // 需要兼容列表导出、报表管理的导出
        ScheduleDTO workOrderSelectDTO = (ScheduleDTO) param.getParameters().get(ScheduleDTO.class.getName());
        List<WorkOrderScheduleExportDTO> workOrderScheduleExportDTOS = new ArrayList<>();
        if (!(null == workOrderSelectDTO.getNoScheduleList())) {
            WorkOrderScheduleSelectDTO workOrderScheduleSelectDTO = WorkOrderScheduleSelectDTO.builder()
                    .current(startPage)
                    .size(limit)
                    .workCenterId(workOrderSelectDTO.getWorkCenterId())
                    .workOrderIds(workOrderSelectDTO.getNoScheduleList())
                    .build();
            List<WorkOrderScheduleDTO> workOrderScheduleDTOS = workOrderScheduleService.listWorkOrderPending(workOrderScheduleSelectDTO);
            workOrderScheduleExportDTOS = WorkOrderScheduleExportDTO.convertToDTO(workOrderScheduleDTOS);
        }
        ExportPage<WorkOrderScheduleExportDTO> result = new ExportPage<>();
        result.setTotal(Math.min(workOrderScheduleExportDTOS.size(), EXPORT_MAX_ROWS));
        result.setCurrent((long) startPage);
        result.setSize((long) limit);
        result.setRecords(workOrderScheduleExportDTOS);
        return result;
    }


    @Override
    public void beforePerPage(ExportContext ctx, DataExportParam param) {
        log.info("开始查询第{}页", (long) Math.ceil(ctx.getSuccessCount() + ctx.getFailCount()) / ctx.getLimit());
    }

    @Override
    public void afterPerPage(List<WorkOrderScheduleExportDTO> list, ExportContext ctx, DataExportParam param) {
        log.info("已完成数量：{}", ctx.getSuccessCount());
    }

    @Override
    public void callBack(ExcelContext ctx, DataParam param) {
        ExportContext context = (ExportContext) ctx;
        log.info("导出完成：{}", context.getFailMessage());
    }
}
