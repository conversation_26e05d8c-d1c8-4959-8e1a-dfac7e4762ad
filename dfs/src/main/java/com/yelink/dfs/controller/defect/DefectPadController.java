package com.yelink.dfs.controller.defect;

import com.yelink.dfs.common.BaseController;
import com.yelink.dfs.entity.defect.DefectDefineEntity;
import com.yelink.dfs.entity.defect.dto.DefectRecordPadDTO;
import com.yelink.dfs.entity.order.WorkOrderEntity;
import com.yelink.dfs.entity.order.dto.ProcedureWorkOrderSelectDTO;
import com.yelink.dfs.service.code.ProductFlowCodeService;
import com.yelink.dfs.service.defect.DefectSchemeService;
import com.yelink.dfs.service.iot.WorkOrderBarcodeService;
import com.yelink.dfs.service.order.RecordWorkOrderCountService;
import com.yelink.dfs.service.order.RecordWorkOrderUnqualifiedService;
import com.yelink.dfs.service.order.WorkOrderProcedureRelationService;
import com.yelink.dfs.service.order.WorkOrderService;
import com.yelink.dfscommon.constant.RespCodeEnum;
import com.yelink.dfscommon.dto.dfs.DefectRecordDTO;
import com.yelink.dfscommon.pojo.ResponseData;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 设备日历
 * @Date 2021/05/24
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/defect")
public class DefectPadController extends BaseController {

    private DefectSchemeService schemeService;
    private RecordWorkOrderUnqualifiedService unqualifiedService;
    private RecordWorkOrderCountService recordWorkOrderCountService;
    private WorkOrderBarcodeService workOrderBarcodeService;
    private WorkOrderService workOrderService;
    private ProductFlowCodeService productFlowCodeService;
    private WorkOrderProcedureRelationService workOrderProcedureRelationService;

    /**
     * 根据工位id查询质检方案
     *
     * @param craftProcedureId 工艺工序id，如果传了优先拿工序质检方案（质检工位机才会传工艺工序id，工位机只传工位拿工位关联的质检方案）
     *                         2.18.2 饶艳生变更：通过工位id、工艺工序id、工序id获取对应的质检方案，当三者都存在质检方案时由用户选择具体使用哪种方案，
     *                         只有其中一者关联质检方案时直接展示；
     *                         2.19.1  变更（通过工位id、工艺工序id、获取对应的质检方案，当两者都存在质检方案时工艺工序优先级最高，
     * @return
     */
    @GetMapping("/get/scheme/{fid}")
    public ResponseData getSchemeByFid(@PathVariable Integer fid, @RequestParam(required = false) Integer craftProcedureId) {
        if (Objects.isNull(craftProcedureId)) {
            Map<String, List<DefectDefineEntity>> map = schemeService.getSchemeByFid(fid);
            return success(map);
        }else {
            Map<String ,Map<String, List<DefectDefineEntity>>> map = schemeService.getDefects(fid, craftProcedureId);
            return success(map);
        }
    }

    /**
     * 根据工单&工序获取质检方案
     * @param workOrderNumber 工单号
     * @param procedureId 工序id
     * @return 方案  2.18.2  饶艳生：查询出工序及工艺工序对应的质检方案，如果两者都存在则由用户选择具体使用哪个方案，只有一者含有则直接返回
     * 2.19.1 查询出工序及工艺工序对应的质检方案，如果两者工艺工序优先级更高，只有一者含有则直接返回
     */
    @GetMapping("/get/by-order-procedure/scheme")
    public ResponseData getSchemeByWorkOrderAndProcedure(@RequestParam String workOrderNumber, @RequestParam Integer procedureId) {
        Map<String,Map<String, List<DefectDefineEntity>>> map = schemeService.getSchemeByWorkOrderAndProcedure(workOrderNumber, procedureId);
        return success(map);
    }

    /**
     * 根据条码获取质检记录
     *
     * @return
     */
    @GetMapping("/get/record/{barCode}")
    public ResponseData getSchemeByFid(@PathVariable String barCode,@RequestParam(required = false) Integer fid) {
        DefectRecordPadDTO dto = unqualifiedService.getPadDTOByBarCode(barCode,fid);
        return success(dto);
    }

    /**
     * 根据条码获取质检记录(支持特殊字符)
     *
     * @return
     */
    @PostMapping("/get/record/post")
    public ResponseData getSchemeByFidPost(@RequestBody DefectRecordDTO defectRecordDTO,@RequestParam(required = false) Integer fid) {
        DefectRecordPadDTO dto = unqualifiedService.getPadDTOByBarCode(defectRecordDTO.getBarCode(),fid);
        return success(dto);
    }

    /**
     * 保存移动端提交的记录
     *
     * @return
     */
    @PostMapping("/save/record")
    public ResponseData saveRecord(@RequestBody DefectRecordDTO dto) {
        String username = getUsername();
        unqualifiedService.saveRecord(dto, username);
        return success();
    }

    /**
     * 修改工单已检查数
     *
     * @return
     */
    @PostMapping("/update/checked")
    public ResponseData updateCheckedQuantity(@RequestParam(value = "workOrder") String workOrder,
                                              @RequestParam(value = "fid", required = false) Integer fid,
                                              @RequestParam(value = "checkedQuantity") Integer checkedQuantity) {
        String username = getUsername();
        recordWorkOrderCountService.updateWorkOrderCheckedQuantity(workOrder, fid, null, checkedQuantity, username);
        return success();
    }

    /**
     * 通过条码获取工单号
     *
     * @param barcode
     * @return
     */
    @GetMapping("/get/work/order/{barcode}")
    public ResponseData getCountRecord(@PathVariable(value = "barcode") String barcode,@RequestParam(required = false) Integer fid) {
        WorkOrderEntity workOrderEntity=productFlowCodeService.getByProductFlowCodeAndFid(barcode,fid);
        if (workOrderEntity==null) {
            return ResponseData.fail(RespCodeEnum.WORK_ORDER_NOT_FOUND);
        }
        return ResponseData.success((Object) workOrderEntity.getWorkOrderNumber());
    }
    @GetMapping("/get/by-barcode/work/order")
    public ResponseData getRecordByBarcode(@RequestParam(value = "barcode") String barcode, @RequestParam(value = "procedureId") Integer procedureId) {
        List<WorkOrderEntity> workOrderList = workOrderProcedureRelationService.getProcedureOrderList(
                ProcedureWorkOrderSelectDTO.builder().inputIsWorkOrder(false).inputNumber(barcode).procedureId(procedureId).build()
        );
        if (CollectionUtils.isEmpty(workOrderList)) {
            return ResponseData.fail(RespCodeEnum.WORK_ORDER_NOT_FOUND);
        }
        return ResponseData.success(workOrderList);
    }

    /**
     * 判断扫码的工单号是否存在与该工位下
     *
     * @param workOrder
     * @return
     */
    @GetMapping("/judge/work/order")
    public ResponseData ifWorkOrder(@RequestParam(value = "workOrder") String workOrder,
                                    @RequestParam(value = "fid") Integer fid) {
        //查询工位下对应的工单列表
        List<String> workOrderList = workOrderService.getWorkOrderNumberByState(fid, workOrder);
        List<String> workOrderLists = workOrderList.stream().filter(o -> o.equals(workOrder)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(workOrderLists)) {
            return ResponseData.fail(RespCodeEnum.WORK_ORDER_NOT_FAC);
        }
        return ResponseData.success((Object) workOrder);
    }
    @GetMapping("/judge/work/order-procedure")
    public ResponseData ifWorkOrderWithProcedure(@RequestParam(value = "workOrder") String workOrder,
                                                 @RequestParam(value = "procedureId") Integer procedureId) {
        //查询工位下对应的工单列表
        List<WorkOrderEntity> workOrderList = workOrderProcedureRelationService.getProcedureOrderList(
                ProcedureWorkOrderSelectDTO.builder().inputIsWorkOrder(true).inputIsLike(false).inputNumber(workOrder).procedureId(procedureId).build()
        );
        if (CollectionUtils.isEmpty(workOrderList)) {
            return ResponseData.fail("该工单不是此工序下的");
        }
        return ResponseData.success((Object) workOrder);
    }

}
