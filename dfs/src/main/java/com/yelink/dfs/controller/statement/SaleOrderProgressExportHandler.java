package com.yelink.dfs.controller.statement;

import com.asyncexcel.core.DataParam;
import com.asyncexcel.core.ExcelContext;
import com.asyncexcel.core.ExportPage;
import com.asyncexcel.core.annotation.ExcelHandle;
import com.asyncexcel.core.exporter.DataExportParam;
import com.asyncexcel.core.exporter.ExportContext;
import com.asyncexcel.core.exporter.ExportHandler;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yelink.dfs.entity.statement.dto.StatementProductionDTO;
import com.yelink.dfs.service.common.CommonService;
import com.yelink.dfs.service.statement.StatementService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * @description: 销售订单进度导出
 * @author: shuang
 * @time: 2023/9/21
 */
@Slf4j
@ExcelHandle
@RequiredArgsConstructor
public class SaleOrderProgressExportHandler implements ExportHandler<StatementProductionDTO.SaleOrderProgressVO> {


    private final CommonService commonService;
    private final StatementService statementService;
    /**
     * 导出数据最大不能超过100万行
     */
    public static final long EXPORT_MAX_ROWS = 1000000;

    @Override
    public void init(ExcelContext ctx, DataParam param) {
        ExportContext context = (ExportContext) ctx;
        StatementProductionDTO.SaleOrderProgressSelectDTO saleOrderProgressSelectDTO = (StatementProductionDTO.SaleOrderProgressSelectDTO) param.getParameters().get(StatementProductionDTO.SaleOrderProgressSelectDTO.class.getName());
        String cleanSheetNames = (String) param.getParameters().get("cleanSheetNames");
        commonService.initExcelContext(saleOrderProgressSelectDTO.getTemplateId(), saleOrderProgressSelectDTO.getTemplateSheetName(),cleanSheetNames, context, StatementProductionDTO.SaleOrderDetailVO.class, null);
    }


    @Override
    public ExportPage<StatementProductionDTO.SaleOrderProgressVO> exportData(int startPage, int limit, DataExportParam param) {
        StatementProductionDTO.SaleOrderProgressSelectDTO saleOrderProgressSelectDTO = (StatementProductionDTO.SaleOrderProgressSelectDTO) param.getParameters().get(StatementProductionDTO.SaleOrderProgressSelectDTO.class.getName());
        saleOrderProgressSelectDTO.setCurrent(startPage);
        saleOrderProgressSelectDTO.setSize(limit);
        Page<StatementProductionDTO.SaleOrderProgressVO> page = statementService.listSaleOrderProgress(saleOrderProgressSelectDTO);
        ExportPage<StatementProductionDTO.SaleOrderProgressVO> result = new ExportPage<>();
        result.setTotal(Math.min(page.getTotal(), EXPORT_MAX_ROWS));
        result.setCurrent((long) startPage);
        result.setSize((long) limit);
        result.setRecords(page.getRecords());
        return result;
    }

    @Override
    public void beforePerPage(ExportContext ctx, DataExportParam param) {
        log.info("开始查询第{}页", (long) Math.ceil(ctx.getSuccessCount() + ctx.getFailCount()) / ctx.getLimit());
    }

    @Override
    public void afterPerPage(List<StatementProductionDTO.SaleOrderProgressVO> list, ExportContext ctx, DataExportParam param) {
        log.info("已完成数量：{}", ctx.getSuccessCount());
    }

    @Override
    public void callBack(ExcelContext ctx, DataParam param) {
        ExportContext context = (ExportContext) ctx;
        log.info("导出完成：{}", context.getFailMessage());
    }
}
