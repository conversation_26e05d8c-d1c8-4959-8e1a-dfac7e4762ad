package com.yelink.dfs.controller.screen;

import com.yelink.dfs.common.BaseController;
import com.yelink.dfs.entity.screen.ComponentDataEntity;
import com.yelink.dfs.service.screen.ComponentDataService;
import com.yelink.dfscommon.dto.ImportProgressDTO;
import com.yelink.dfscommon.pojo.ResponseData;
import com.yelink.dfscommon.utils.ExcelUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;


/**
 * <AUTHOR>
 * @description: 组件数据接口
 * @time 2021/10/20
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/component/datas")
public class ComponentDataController extends BaseController {

    private ComponentDataService componentDataService;

    /**
     * 假数据导入
     */
    @PostMapping("/import")
    public ResponseData importExcel(MultipartFile file, ComponentDataEntity dataEntity) throws IOException {
        //1、判断导入文件的合法性
        ExcelUtil.checkImportExcelFile(file);
        componentDataService.sycImportData(file.getOriginalFilename(), file.getInputStream(), getUsername(), dataEntity);
        return ResponseData.success();
    }

    /**
     * 获取导入的进度
     */
    @GetMapping("/import/progress")
    public ResponseData getProductionOrderImportProgress(String appId, String componentId) {
        ImportProgressDTO importProgressDTO = componentDataService.importProgress(appId, componentId);
        return success(importProgressDTO);
    }

    /**
     * 假数据查询
     */
    @GetMapping("/list")
    public ResponseData getList(String appId, String componentId) {
        List<ComponentDataEntity> list = componentDataService.lambdaQuery().eq(ComponentDataEntity::getAppId, appId)
                .eq(ComponentDataEntity::getComponentId, componentId).orderByAsc(ComponentDataEntity::getId).list();
        return success(list);
    }

}
