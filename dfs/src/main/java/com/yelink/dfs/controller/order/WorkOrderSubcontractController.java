package com.yelink.dfs.controller.order;

import com.yelink.dfs.common.BaseController;
import com.yelink.dfs.entity.order.WorkOrderRemarkEntity;
import com.yelink.dfs.entity.order.dto.WorkOrderRemarkInsertDTO;
import com.yelink.dfs.entity.order.dto.WorkOrderRemarkUpdateDTO;
import com.yelink.dfs.entity.order.dto.WorkOrderSubcontractCountDTO;
import com.yelink.dfs.entity.order.dto.WorkOrderSubcontractInsertDTO;
import com.yelink.dfs.service.order.WorkOrderRemarkService;
import com.yelink.dfs.service.order.WorkOrderSubcontractService;
import com.yelink.dfscommon.pojo.ResponseData;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 工单委外
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/work-orders/subcontract")
public class WorkOrderSubcontractController extends BaseController {

    @Resource
    private WorkOrderSubcontractService workOrderSubcontractService;


    @PostMapping("/add")
    public ResponseData add(@RequestBody @Validated WorkOrderSubcontractInsertDTO dto) {
        workOrderSubcontractService.add(dto);
        return success();
    }

    @PostMapping("/orderCount")
    public ResponseData orderCount(@RequestBody WorkOrderSubcontractCountDTO dto) {
        return success(workOrderSubcontractService.orderCount(dto));
    }

    @GetMapping("/getSupplierByWorkOrderNumber")
    public ResponseData getSupplierByWorkOrderNumber(@RequestParam String workOrderNumber) {
        return success(workOrderSubcontractService.getSupplierByWorkOrderNumber(workOrderNumber));
    }

}
