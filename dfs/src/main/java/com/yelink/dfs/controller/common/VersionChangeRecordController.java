package com.yelink.dfs.controller.common;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yelink.dfs.common.BaseController;
import com.yelink.dfs.entity.common.VersionChangeRecordEntity;
import com.yelink.dfs.entity.common.VersionChangeRecordSelectDTO;
import com.yelink.dfs.provider.FastDfsClientService;
import com.yelink.dfs.service.common.UploadService;
import com.yelink.dfs.service.common.VersionChangeRecordService;
import com.yelink.dfscommon.pojo.ResponseData;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;


/**
 * 版本变更记录
 *
 * <AUTHOR>
@Slf4j
@RestController()
@RequestMapping("/versionChangeRecord")
public class VersionChangeRecordController extends BaseController {


    @Resource
    private VersionChangeRecordService versionChangeRecordService;


    /**
     * 上传未标记文件，超时后会被清除，需要业务处理标记记录
     */
    @PostMapping("/page")
    public ResponseData page(@RequestBody VersionChangeRecordSelectDTO dto) {
        Page<VersionChangeRecordEntity> page = versionChangeRecordService.selectPage(dto);
        return ResponseData.success(page);
    }
}
