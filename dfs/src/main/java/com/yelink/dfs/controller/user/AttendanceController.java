package com.yelink.dfs.controller.user;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yelink.dfs.constant.Constant;
import com.yelink.dfs.entity.attendance.dto.AttendanceCheckDTO;
import com.yelink.dfs.entity.attendance.dto.AttendanceCheckExitTodayDTO;
import com.yelink.dfs.entity.attendance.dto.AttendanceDownDTO;
import com.yelink.dfs.entity.attendance.dto.AttendanceLineDTO;
import com.yelink.dfs.entity.attendance.dto.AttendanceQrCodeInfoDTO;
import com.yelink.dfs.entity.attendance.dto.AttendanceRecordDTO;
import com.yelink.dfs.entity.attendance.dto.AttendanceScanUserDTO;
import com.yelink.dfs.entity.attendance.dto.AttendanceSupplementaryAdminDTO;
import com.yelink.dfs.entity.attendance.dto.AttendanceUserDTO;
import com.yelink.dfs.entity.attendance.vo.AttendanceExcelVO;
import com.yelink.dfs.entity.attendance.vo.AttendanceLineVO;
import com.yelink.dfs.entity.attendance.vo.AttendanceRecentVO;
import com.yelink.dfs.entity.attendance.vo.AttendanceRecordExportVO;
import com.yelink.dfs.entity.attendance.vo.AttendanceRecordVO;
import com.yelink.dfs.entity.attendance.vo.AttendanceUserVO;
import com.yelink.dfs.entity.common.ModelUploadFileEntity;
import com.yelink.dfs.entity.order.WorkOrderEntity;
import com.yelink.dfs.entity.order.dto.WorkOrderExportDTO;
import com.yelink.dfs.provider.FastDfsClientService;
import com.yelink.dfs.service.attendance.AttendanceRecordService;
import com.yelink.dfs.service.attendance.AttendanceService;
import com.yelink.dfs.service.common.ModelUploadFileService;
import com.yelink.dfs.utils.ExcelTemplateExportUtils;
import com.yelink.dfscommon.pojo.ResponseData;
import com.yelink.dfscommon.utils.EasyExcelUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 人员打卡考勤
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/attendance")
public class AttendanceController {

    @Resource
    private AttendanceService attendanceService;
    @Resource
    private ModelUploadFileService modelUploadFileService;
    @Resource
    private FastDfsClientService fastDfsClientService;
    @Resource
    private AttendanceRecordService attendanceRecordService;

    /**
     * 扫描的二维码， 获取上/下机的对应的参数
     * @param dto
     * @return
     */
    @PostMapping("/scan/qr-code")
    public ResponseData scanQrCode(@RequestBody @Validated AttendanceQrCodeInfoDTO dto) {
        return ResponseData.success(attendanceService.scanQrCode(dto));
    }

    /**
     * 进入打卡页面，需要校验是否有未下班/漏打卡
     * @return
     */
    @GetMapping("/check")
    public ResponseData check() {
        return ResponseData.success(attendanceService.check());
    }

    /**
     * 当日上机打卡
     * @param dto
     * @return
     */
    @PostMapping("/submit/up")
    public ResponseData submitUp(@RequestBody @Validated AttendanceCheckDTO dto) {
        return ResponseData.success(attendanceService.submitUp(dto, false));
    }

    /**
     * 当日下机打卡
     * @param dto
     * @return
     */
    @PostMapping("/submit/down")
    public ResponseData submitDown(@RequestBody @Validated AttendanceDownDTO dto) {
        return ResponseData.success(attendanceService.submitDown(dto));
    }

    /**
     * 下机补卡：员工
     * @param dto
     * @return
     */
    @PostMapping("/supplementary/down")
    public ResponseData supplementaryDown(@RequestBody @Validated AttendanceDownDTO dto) {
        return ResponseData.success(attendanceService.supplementaryDown(dto));
    }

    /**
     * 工时补录：主管
     * @param dto
     * @return
     */
    @PostMapping("/supplementary/admin")
    public ResponseData supplementaryAdmin(@RequestBody @Validated AttendanceSupplementaryAdminDTO dto) {
        attendanceService.submitSupplementary(dto);
        return ResponseData.success();
    }

    /**
     * 获取打卡记录
     * @param dto
     * @return
     */
    @PostMapping("/record/list")
    public ResponseData recordList(@RequestBody @Validated AttendanceRecordDTO dto) {
        dto.setSmartCal(true);
        return ResponseData.success(attendanceRecordService.recordList(dto));
    }

    /**
     * 打卡记录导出
     * @param dto
     * @param response
     * @return
     * @throws IOException
     */
    @PostMapping("/record/list-export")
    public ResponseData recordListExport(@RequestBody @Validated AttendanceRecordDTO dto, HttpServletResponse response) throws IOException {
        dto.setSmartCal(true);
        Page<AttendanceRecordVO> page = attendanceRecordService.recordList(dto);
        List<AttendanceRecordExportVO> result = JSONObject.parseArray(JSON.toJSONString(page.getRecords()), AttendanceRecordExportVO.class);
        EasyExcelUtil.export(response, "attendanceRecord", "attendanceRecordSheet", result, AttendanceRecordExportVO.class);
        return ResponseData.success();
    }

    /**
     * 当日扫码上机打卡
     * @param dto
     * @return
     */
    @PostMapping("/scan/up")
    public ResponseData scanUp(@RequestBody @Validated AttendanceCheckDTO dto) {
        return ResponseData.success(attendanceService.submitUp(dto, true));
    }

    /**
     * 当日扫码下机打卡
     * @param dto
     * @return
     */
    @PostMapping("/scan/down")
    public ResponseData scanDown(@RequestBody @Validated AttendanceScanUserDTO dto) {
        return ResponseData.success(attendanceService.scanDown(dto));
    }

    /**
     * 打卡记录默认导出模板
     */
    @GetMapping("/default/export/template")
    public void downloadSaleOrderDefaultExportTemplate(HttpServletResponse response) throws IOException {
        Page<AttendanceRecordVO> page = attendanceRecordService.recordList(AttendanceRecordDTO.builder()
                .smartCal(true)
                .current(1)
                .size(10)
                .build()
        );
        List<AttendanceRecordExportVO> list = JSONObject.parseArray(JSON.toJSONString(page.getRecords()), AttendanceRecordExportVO.class);
        EasyExcelUtil.export(response, "打卡记录默认导出模板", "数据源", list, AttendanceRecordExportVO.class);
    }

    /**
     * 打卡记录导出
     */
    @PostMapping("/excel/exports")
    public void export(@RequestParam(required = false) Integer templateId,
                       @RequestBody AttendanceRecordDTO dto, HttpServletResponse response) {
        dto.setSmartCal(true);
        Page<AttendanceRecordVO> page = attendanceRecordService.recordList(dto);
        // 封装成DTO
        List<AttendanceExcelVO> data = page.getRecords().stream().map(AttendanceExcelVO::convertToDTO).collect(Collectors.toList());

        if (Objects.isNull(templateId)) {
            // 按默认模板下载
            ExcelTemplateExportUtils.downloadDefaultExportData(response, "打卡记录" + Constant.XLSX, AttendanceExcelVO.class, data);
        } else {
            // 按模板下载
            ModelUploadFileEntity uploadFile = modelUploadFileService.getById(templateId);
            byte[] bytes = fastDfsClientService.getFileStream(uploadFile.getFileAddress());
            ExcelTemplateExportUtils.downloadExportData(response, new ByteArrayInputStream(bytes), uploadFile.getFileName(), AttendanceExcelVO.class, data);
        }
    }

    /**
     * 对用户, 检查某个工单当日是否有未下机的记录
     * @param dto
     * @return
     */
    @PostMapping("/check-exit-today")
    public ResponseData checkExitToday(@RequestBody @Validated AttendanceCheckExitTodayDTO dto) {
        return ResponseData.success(attendanceService.checkExitToday(dto));
    }

    /**
     * 最近打卡时间(仅显示一个月内的人员;一人最多一条)
     * @param lineId 产线id
     */
    @GetMapping("/recent-check-in")
    public ResponseData recentCheckIn(@RequestParam Integer lineId) {
        AttendanceRecentVO vo = attendanceRecordService.recentCheckIn(lineId);
        return ResponseData.success(vo);
    }

    /**
     * 今日打卡详情
     */
    @PostMapping("/line/today/check-in")
    public ResponseData lineTodayCheckIn(@RequestBody @Validated AttendanceLineDTO dto) {
        List<AttendanceLineVO> vo = attendanceRecordService.lineTodayCheckIn(dto);
        return ResponseData.success(vo);
    }

    /**
     * 今日打卡详情
     */
    @PostMapping("/user/today/check-in")
    public ResponseData userTodayCheckIn(@RequestBody @Validated AttendanceUserDTO dto) {
        List<AttendanceUserVO> vo = attendanceRecordService.userTodayCheckIn(dto);
        return ResponseData.success(vo);
    }
}
