package com.yelink.dfs.provider.impl;

import com.alibaba.fastjson.JSONObject;
import com.yelink.dfs.manager.YelinkCcuInterface;
import com.yelink.dfs.provider.CcuService;
import com.yelink.dfscommon.pojo.ResponseData;
import com.yelink.dfscommon.utils.JacksonUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * @description: ccu服务业务处理
 * @author: shuang
 * @time: 2022/11/2
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CcuServiceImpl implements CcuService {


    private final YelinkCcuInterface yelinkCcuInterface;

    private static final String USERNAME = "admin";
    private static final String PASSWORD = "admin";

    private static final int DEMO_TASK_ID = 14;


    @Override
    public void startAutoDemo(int times) {
        ResponseData responseData = yelinkCcuInterface.getToken(USERNAME, PASSWORD);
        String token;
        if(responseData.getCode().equals(ResponseData.SUCCESS_CODE)){
            token = JacksonUtil.getResponseObject(responseData, JSONObject.class).getString("accessToken");
        }else{
            log.error("调用ccu获取token失败{}",responseData.getMessage());
            return;
        }

        Map<String,Object> params = new HashMap<>(8);
        params.put("tempId",DEMO_TASK_ID);
        for (int i = 0; i < times; i++) {
            ResponseData responseData1 =  yelinkCcuInterface.taskAdd(token,params);
            if(responseData1.getCode().equals(ResponseData.SUCCESS_CODE)){
                log.info("调用ccu新建任务接口成功");
                continue;
            }
            log.error("调用ccu获取token失败{}",responseData1.getMessage());
        }
    }

}
