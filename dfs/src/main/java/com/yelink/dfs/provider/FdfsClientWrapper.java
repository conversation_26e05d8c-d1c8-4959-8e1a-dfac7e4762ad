package com.yelink.dfs.provider;

import com.yelink.dfs.config.MinioConfig;
import com.yelink.dfscommon.constant.Constants;
import com.yelink.dfscommon.constant.ResponseException;
import io.minio.GetObjectArgs;
import io.minio.GetObjectResponse;
import io.minio.MinioClient;
import io.minio.PutObjectArgs;
import io.minio.RemoveObjectArgs;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.FastByteArrayOutputStream;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * FastDFS客户端包装类
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class FdfsClientWrapper {


    private final MinioClient minioClient;
    private final MinioConfig prop;

    private final SimpleDateFormat format = new SimpleDateFormat("yyyyMMddHHmmssSSS");
    private static final Map<String, String> MIME_TYPES = new HashMap<>();
    static {
        MIME_TYPES.put("txt", "text/plain");
        MIME_TYPES.put("html", "text/html");
        MIME_TYPES.put("css", "text/css");
        MIME_TYPES.put("js", "application/javascript");
        MIME_TYPES.put("json", "application/json");
        MIME_TYPES.put("xml", "application/xml");
        MIME_TYPES.put("jpg", "image/jpeg");
        MIME_TYPES.put("jpeg", "image/jpeg");
        MIME_TYPES.put("png", "image/png");
        MIME_TYPES.put("gif", "image/gif");
        MIME_TYPES.put("bmp", "image/bmp");
        MIME_TYPES.put("pdf", "application/pdf");
        MIME_TYPES.put("xlsx", "application/vnd.ms-excel");
        // 添加更多扩展名和 Content-Type 对应关系
    }

//    public String upload(MultipartFile file) throws IOException {
//        if (file != null) {
//            byte[] bytes = file.getBytes();
//            String extension = FilenameUtils.getExtension(file.getOriginalFilename());
//            return this.upload(bytes, extension);
//        }
//        return null;
//    }
    public String upload(MultipartFile file) throws IOException {
        if (file != null) {
            byte[] bytes = file.getBytes();
            String extension = FilenameUtils.getExtension(file.getOriginalFilename());
            return this.upload(bytes, extension);
        }
        return null;
    }

    /**
     * 文件上传
     *
     * @param bytes     文件字节
     * @param extension 文件扩展名
     * @return 返回文件路径（卷名和文件名）
     */
//    public String upload(byte[] bytes, String extension) {
//        ByteArrayInputStream stream = new ByteArrayInputStream(bytes);
//        // 元数据
//        return this.uploadFile(stream, bytes.length, extension);
//    }
    public String upload(byte[] bytes, String extension) {
        ByteArrayInputStream stream = new ByteArrayInputStream(bytes);
        // 元数据
        return this.uploadFile(stream, extension);
    }


//    public String uploadFile(InputStream inputStream, long fileSize, String fileExtName) {
//        return fastFileStorageClient.uploadFile(inputStream, fileSize, fileExtName, null).getFullPath();
//    }
    public String uploadFile(InputStream in, String fileExtName) {
        try {
            String objectName = "dfs" + Constants.DIAGONAL_LINE + format.format(new Date()) + "." + fileExtName;
            PutObjectArgs objectArgs = PutObjectArgs.builder()
                    .bucket(prop.getBucketName())
                    .object(objectName)
                    .stream(in, in.available(), -1)
                    .contentType(getContentType(fileExtName))
                    .build();
            minioClient.putObject(objectArgs);
            return prop.getBucketName() + Constants.DIAGONAL_LINE + objectName;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }finally {
            IOUtils.closeQuietly(in);
        }
    }


    /**
     * 下载文件
     *
     * @param filePath 文件路径
     * @return 文件字节
     * @throws IOException
     */
//    public byte[] download(String filePath) {
//        byte[] bytes = null;
//        if (StringUtils.isNotBlank(filePath)) {
//            String group = filePath.substring(0, filePath.indexOf("/"));
//            String path = filePath.substring(filePath.indexOf("/") + 1);
//            DownloadByteArray byteArray = new DownloadByteArray();
//
//            bytes = fastFileStorageClient.downloadFile(group, path, byteArray);
//        }
//        return bytes;
//    }
    public byte[] download(String filePath) {
        if (StringUtils.isNotBlank(filePath) && filePath.contains(Constants.DIAGONAL_LINE)) {
            String bucketName = filePath.substring(0, filePath.indexOf(Constants.DIAGONAL_LINE));
            // 带路径的
            String fileName = filePath.substring(filePath.indexOf(Constants.DIAGONAL_LINE) + 1);
            GetObjectArgs objectArgs = GetObjectArgs.builder().bucket(bucketName)
                    .object(fileName).build();
            GetObjectResponse response;
            try{
                response = minioClient.getObject(objectArgs);
            } catch (Exception e) {
                e.printStackTrace();
                throw new ResponseException("minio客户端下载文件异常, msg:{}" + e.getMessage());
            }
            byte[] buf = new byte[1024];
            int len;
            try (FastByteArrayOutputStream os = new FastByteArrayOutputStream()){
                while ((len=response.read(buf))!=-1){
                    os.write(buf,0, len);
                }
                os.flush();
                return os.toByteArray();
            }catch (IOException e) {
                e.printStackTrace();
                throw new ResponseException("输出文件流异常, msg:{}" + e.getMessage());
            }
        }
        return null;
    }

    /**
     * 删除文件
     *
     * @param filePath 文件路径
     */
//    public void delete(String filePath) {
//        if (StringUtils.isNotBlank(filePath)) {
//            fastFileStorageClient.deleteFile(filePath);
//        }
//    }
    public void delete(String filePath){
        if (StringUtils.isNotBlank(filePath) && filePath.contains(Constants.DIAGONAL_LINE)) {
            String bucketName = filePath.substring(0, filePath.indexOf(Constants.DIAGONAL_LINE));
            // 带路径的
            String fileName = filePath.substring(filePath.indexOf(Constants.DIAGONAL_LINE) + 1);
            try {
                minioClient.removeObject(RemoveObjectArgs.builder().bucket(bucketName).object(fileName).build());
            }catch (Exception e){
                e.printStackTrace();
                throw new ResponseException("minio客户端删除文件异常, msg:{}" + e.getMessage());
            }
        }
    }




    /**
     * 根据文件扩展名获取 Content-Type
     * 不使用MultipartFile.getContentType()，是因为它基于客户端传输的content_type，不一定准确
     *
     * @param extension 文件名后缀
     * @return MIME 类型
     */
    private String getContentType(String extension) {
        return MIME_TYPES.getOrDefault(extension, "application/octet-stream");
    }

}
