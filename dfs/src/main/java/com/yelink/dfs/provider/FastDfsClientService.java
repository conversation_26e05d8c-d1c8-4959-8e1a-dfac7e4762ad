package com.yelink.dfs.provider;


import com.yelink.dfs.entity.common.UploadEntity;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;

/**
 * @Description:
 * @Author: zhengfu
 * @Date: 2020/6/11
 */
public interface FastDfsClientService {

    /**
     * 上传文件
     *
     * @param file
     * @param username
     * @return
     * @throws Exception
     */
    UploadEntity uploadFile(MultipartFile file, String username);

    /**
     * 上传文件
     *
     * @param file
     * @param username
     * @param fileCode 文件代号，调用getUrlByFileCode方法可以获取文件地址
     * @return
     */
    UploadEntity uploadFile(MultipartFile file, String username, String fileCode);

    /**
     * 根据url删除文件
     *
     * @param fileUrl
     * @return
     * @throws IOException
     */
    boolean deleteFile(String fileUrl);

    /**
     * 通过code获取上传的文件地址
     *
     * @param fileCode
     * @return
     */
    @Deprecated
    String getUrlByFileCode(String fileCode);

    /**
     * 获取文件
     *
     * @param url
     * @return
     */
    @Deprecated
    byte[] getFileStream(String url);


    /**
     * 上传文件:文件默认标记为生效
     *
     * @param bytes
     * @param username
     * @return
     */
    @Deprecated
    String uploadFileByBytes(byte[] bytes, String username);

    /**
     * 上传文件
     *
     * @param bytes
     * @param fileExtName
     * @param username
     * @return
     */
    @Deprecated
    String uploadFileByBytes(byte[] bytes, String fileExtName, String username);

    /**
     * 下载文件
     *
     * @return
     */
    @Deprecated
    byte[] downloadFile(String url);


    /**
     * 将数据转成excel并上传到文件服务器
     *
     * @param easyExcelDatas
     * @param headerClass
     * @param opUserName
     * @return 文件下载地址
     */
    @Deprecated
    <T> String listToExcelUploadTo(List<T> easyExcelDatas, Class<T> headerClass, String opUserName);


}
