package com.yelink.dfs.provider.impl;

import com.alibaba.excel.EasyExcelFactory;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yelink.dfs.entity.common.UploadEntity;
import com.yelink.dfs.mapper.common.UploadMapper;
import com.yelink.dfs.provider.FastDfsClientService;
import com.yelink.dfs.provider.FdfsClientWrapper;
import com.yelink.dfs.service.user.SysUserService;
import com.yelink.dfscommon.constant.RespCodeEnum;
import com.yelink.dfscommon.constant.ResponseException;
import com.yelink.dfscommon.utils.ExcelUtil;
import com.yelink.dfscommon.utils.PathUtils;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Date;
import java.util.List;
import java.util.UUID;

/**
 * @Description:
 * @Author: zhengfu
 * @Date: 2020/6/11
 */
@Slf4j
@Component
@AllArgsConstructor
public class FastDfsClientServiceImpl extends ServiceImpl<UploadMapper, UploadEntity> implements FastDfsClientService {

    private FdfsClientWrapper fdfsClientWrapper;
    private SysUserService sysUserService;

    private static final String UNIT_K = "k";

    private final Object lock = new Object();

    @Override
    public UploadEntity uploadFile(MultipartFile file, String username) {
        return uploadFile(file, username, null);
    }

    /**
     * 上传文件
     *
     * @param file 文件对象
     * @return 文件访问地址
     * @throws IOException
     */
    @Override
    public UploadEntity uploadFile(MultipartFile file, String username, String imageCode) {
        UploadEntity uploadEntity = uploadFileBy(file, username);
        if (StringUtils.isNotBlank(imageCode)) {
            uploadEntity.setImageCode(imageCode);
            uploadEntity.setIsUsed(true);
            UploadEntity oldUploadEntity = getOneByImageCode(imageCode);
            //已经有上传信息，直接更新
            if (oldUploadEntity != null) {
                deleteFile(oldUploadEntity.getUrl());
                updateByImageCode(imageCode, uploadEntity);
                return uploadEntity;
            }
        }
        log.info("上传成功,{}", uploadEntity);
        uploadEntity.setFileName(file.getOriginalFilename());
        uploadEntity.setCreateUserName(sysUserService.getNicknameByUsername(uploadEntity.getCreateBy()));
        this.save(uploadEntity);
        return uploadEntity;
    }

    private UploadEntity uploadFileBy(MultipartFile file, String username) {
        if (file == null || file.isEmpty()) {
            throw new ResponseException(RespCodeEnum.FILE_NOT_EMPTY);
        }
        String fileName = file.getOriginalFilename();
        if (StringUtils.isBlank(fileName)) {
            throw new ResponseException(RespCodeEnum.FILE_IS_NULL);
        }
        //对文文件的全名进行截取然后在后缀名进行删选。
        int begin = fileName.lastIndexOf(".") + 1;
        int last = fileName.length();
        //获得文件后缀名
        String fileType = fileName.substring(begin, last);
        StringBuilder sb = new StringBuilder("开始文件上传，文件信息\n\r");
        sb.append("文件类型:").append(fileType).append("\n\r");

        String storePath;
        //获取文件大小
        Long len = file.getSize();
        String size = String.format("%.1f", len.doubleValue() / 1024);
        sb.append("文件大小：").append(size).append(UNIT_K);
        log.info(sb.toString());
        synchronized (lock) {
            try {
                storePath = fdfsClientWrapper.upload(file);
            } catch (Exception e) {
                log.error("上传异常", e);
                throw new ResponseException(RespCodeEnum.FILE_SERVICE_NOT_READING);
            }
        }
        Integer height = null;
        Integer width = null;

        //如果是图片格式,则计算宽高
        BufferedImage image = null;
        try {
            image = ImageIO.read(file.getInputStream());
            if (image != null) {
                height = image.getHeight();
                width = image.getWidth();
                sb.append("分辨率：").append(width).append("×").append(height).append("\n\r");
            }
        } catch (IOException e) {
            log.error("计算图片宽高错误", e);
        }
        return UploadEntity.builder()
                .height(height)
                .width(width)
                .type(fileType)
                .size(size + UNIT_K)
                .url(storePath)
                .createBy(username)
                .createTime(new Date()).build();
    }


    /**
     * 通过图片code更新上传信息
     *
     * @param imageCode
     * @param uploadEntity
     */
    private void updateByImageCode(String imageCode, UploadEntity uploadEntity) {
        UpdateWrapper<UploadEntity> updateWrapper = new UpdateWrapper<>();
        updateWrapper.lambda().eq(UploadEntity::getImageCode, imageCode);
        this.update(uploadEntity, updateWrapper);
    }


    /**
     * 删除文件
     *
     * @param fileUrl 文件访问地址
     * @return
     */
    @Override
    public boolean deleteFile(String fileUrl) {
        if (StringUtils.isBlank(fileUrl)) {
            log.info("文件路径不能为空");
            return false;
        }
        try {
            fdfsClientWrapper.delete(fileUrl);
            return true;
        } catch (Exception e) {
            log.error("删除文件出错", e);
        }
        return false;
    }

    @Override
    public String getUrlByFileCode(String fileCode) {
        UploadEntity uploadEntity = getOneByImageCode(fileCode);
        if (uploadEntity != null) {
            return uploadEntity.getUrl();
        }
        return null;
    }

    @Override
    public byte[] getFileStream(String url) {
        return fdfsClientWrapper.download(url);
    }

    @Override
    public String uploadFileByBytes(byte[] bytes, String username) {
        String storePath;
        synchronized (lock) {
            try {
                storePath = fdfsClientWrapper.upload(bytes, "xlsx");
            } catch (Exception e) {
                throw new ResponseException(RespCodeEnum.FILE_SERVICE_NOT_READING);
            }
        }
        UploadEntity uploadEntity = UploadEntity.builder()
                .createBy(username)
                .createTime(new Date())
                .url(storePath)
                .isUsed(true)
                .build();
        this.save(uploadEntity);
        return storePath;
    }

    /**
     * 上传文件
     *
     * @param bytes       字节数组
     * @param fileExtName 文件扩展名
     * @param username    用户名
     * @return
     */
    @Override
    public String uploadFileByBytes(byte[] bytes, String fileExtName, String username) {
        String storePath;
        synchronized (lock) {
            try {
                storePath = fdfsClientWrapper.upload(bytes, fileExtName);
            } catch (Exception e) {
                throw new ResponseException(RespCodeEnum.FILE_SERVICE_NOT_READING);
            }
        }
        UploadEntity uploadEntity = UploadEntity.builder()
                .url(storePath)
                .isUsed(true)
                .createBy(username)
                .createTime(new Date())
                .build();
        this.save(uploadEntity);
        return storePath;
    }

    @Override
    public byte[] downloadFile(String url) {
        return fdfsClientWrapper.download(url);
    }


    @Override
    public <T> String listToExcelUploadTo(List<T> easyExcelDatas, Class<T> headerClass, String opUserName) {
        InputStream tempInputStream = null;
        String filePath = PathUtils.getAbsolutePath(this.getClass()) + "/temp/" + UUID.randomUUID() + ExcelUtil.XLSX;
        File file = new File(filePath);
        try {
            if (!file.getParentFile().exists()) {
                file.mkdirs();
            }
            EasyExcelFactory.write(filePath)
                    .head(headerClass)
                    .sheet("导入结果")
                    .doWrite(easyExcelDatas);
            tempInputStream = new FileInputStream(filePath);
            byte[] bytes = new byte[tempInputStream.available()];
            int read = tempInputStream.read(bytes);
            return this.uploadFileByBytes(bytes, opUserName);
        } catch (Exception e) {
            log.error("验证结果上传文件服务器失败", e);
        } finally {
            IOUtils.closeQuietly(tempInputStream);
            FileUtils.deleteQuietly(file);
        }
        return null;
    }

    /**
     * 通过图片code查询上传信息
     *
     * @param imageCode
     * @return
     */
    @Deprecated
    private UploadEntity getOneByImageCode(String imageCode) {
        QueryWrapper<UploadEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(UploadEntity::getImageCode, imageCode);
        return getOne(queryWrapper);
    }

}
