package com.yelink.dfs.schedule;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yelink.dfs.constant.Constant;
import com.yelink.dfs.constant.RedisKeyPrefix;
import com.yelink.dfscommon.constant.dfs.order.WorkOrderStateEnum;
import com.yelink.dfs.constant.screen.ComponentMethodEnum;
import com.yelink.dfs.entity.manufacture.ProductionLineEntity;
import com.yelink.dfs.entity.order.WorkOrderEntity;
import com.yelink.dfs.entity.screen.ComponentEntity;
import com.yelink.dfs.entity.screen.RecordComponentDataEntity;
import com.yelink.dfs.entity.screen.ScreenComponentEntity;
import com.yelink.dfs.service.common.DictService;
import com.yelink.dfs.service.manufacture.ProductionLineService;
import com.yelink.dfs.service.order.RecordWorkOrderDayCountService;
import com.yelink.dfs.service.screen.ComponentService;
import com.yelink.dfs.service.screen.ProductionService;
import com.yelink.dfs.service.screen.RecordComponentDataService;
import com.yelink.dfs.service.screen.RecordOutputService;
import com.yelink.dfs.service.screen.ScreenComponentService;
import com.yelink.dfscommon.entity.RecordOutputEntity;
import com.yelink.dfscommon.utils.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.SchedulingConfigurer;
import org.springframework.scheduling.config.ScheduledTaskRegistrar;
import org.springframework.scheduling.support.CronTrigger;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @description: 计算产能定时任务
 * @time 2021/8/25 16:58
 */
@Lazy(false)
@Component
@EnableScheduling
@Slf4j
public class OutPutTask implements SchedulingConfigurer {

    private String cron = Constant.DEFAULT_CRON;
    @Autowired
    private DictService dictService;
    @Autowired
    private RedisTemplate redisTemplate;
    @Autowired
    private ProductionService productionService;
    @Autowired
    private RecordWorkOrderDayCountService recordWorkOrderDayCountService;
    @Autowired
    private RecordComponentDataService recordComponentDataService;
    @Autowired
    private ScreenComponentService screenComponentService;
    @Autowired
    private ComponentService componentService;
    @Autowired
    private ProductionLineService productionLineService;
    @Autowired
    private RecordOutputService recordOutputService;
    @Override
    public void configureTasks(ScheduledTaskRegistrar taskRegistrar) {
        taskRegistrar.addTriggerTask(() -> {
            //分布式锁
            Boolean lockStatus = redisTemplate.opsForValue().setIfAbsent(RedisKeyPrefix.RECORD_OUT_PUT, new Date(), 1, TimeUnit.MINUTES);
            if (lockStatus == null || !lockStatus) {
                return;
            }
            log.info("产能计算定时任务{}", new Date());
            recordOutput();
            log.info("大屏组件记录定时任务{}", new Date());
            recordComponentData();
        }, triggerContext -> {
            //根据工厂最早开始时间拿到cron表达式
            cron = this.getCronString();
            CronTrigger trigger;
            try {
                trigger = new CronTrigger(cron);
                return trigger.nextExecutionTime(triggerContext);
            } catch (Exception e) {
                //如果格式有问题就按默认的
                trigger = new CronTrigger(Constant.DEFAULT_CRON);
                return trigger.nextExecutionTime(triggerContext);
            }
        });
    }

    /**
     * 记录所有产线产能及整个工厂产能
     */
    private void recordOutput() {
        recordOutputLine(null);
        List<ProductionLineEntity> list = productionLineService.list();
        for (ProductionLineEntity productionLineEntity : list) {
            recordOutputLine(productionLineEntity.getProductionLineId());
        }
    }

    /**
     * 记录产线产能
     *
     * @param lineId
     */
    private void recordOutputLine(Integer lineId) {
        Date now = new Date();
        Date recordDate = dictService.getRecordDate(now);
        //Date date = dictService.getDayOutputBeginTime(dateNow);
        //获取今天预计生产的工单
        String lineIds = null;
        if (lineId != null) {
            lineIds = lineId.toString();
        }
        List<WorkOrderEntity> workOrderEntityList = productionService.getWorkOrderInPlan(lineIds, null);
        if (CollectionUtils.isEmpty(workOrderEntityList)) {
            return;
        }
        //Integer completed = 0;
        //Integer planQuantity = 0;
        // 工单已完成数量
        int orderCompleted = 0;
        // 工单总数量
        int orderTotal = 0;

        //拿到所有工单集合，里面包括父工单和子工单集合
        for (WorkOrderEntity entity : workOrderEntityList) {
            //如果是独立工单
            orderTotal++;
            //统计完成状态数量
            if (WorkOrderStateEnum.FINISHED.getCode().equals(entity.getState())) {
                orderCompleted++;
            }
        }

        //总计划数,总完成数
        Integer planQuantityTotal = productionService.getPlanQuantityTotal(null);
        //今日已生产数量（合格数）
        int completeTotal = recordWorkOrderDayCountService.getCountByDate(recordDate, null).intValue();

        RecordOutputEntity recordOutputEntityToInsert = RecordOutputEntity.builder()
                .completed((double) completeTotal)
                .planQuantity(Double.valueOf(planQuantityTotal))
                .orderCompleted((double) orderCompleted)
                .orderPlanQuantity((double) orderTotal)
                .time(recordDate)
                .lineId(lineId)
                .build();
        recordOutputService.insertRecordOutPut(recordOutputEntityToInsert);

    }

    /**
     * 根据工厂最早班时间拿到cron表达式
     *
     * @return
     */
    private String getCronString() {
        //根据工厂最早开始时间拿到cron表达式
        Date beginDate = dictService.getDayOutputBeginTime(new Date());
        //提前五分钟
        Date date = DateUtil.addMin(beginDate, -5);
        //截取分钟和小时
        String hour = date.toString().substring(11, 13);
        String min = date.toString().substring(14, 16);
        return Constant.CRON.replaceAll("min", min).replaceAll("hour", hour);
    }

    /**
     * 记录大屏组件每日数据
     */
    private void recordComponentData() {
        Date dateNow = new Date();
        Date date = dictService.getRecordDate(dateNow);
        //找到需要记录数据的大屏，再找到属于大屏的所有组件
        LambdaQueryWrapper<ScreenComponentEntity> screenComponentWrapper = new LambdaQueryWrapper<>();
//        screenComponentWrapper.eq(ScreenComponentEntity::getIsRecordData, true);
        List<ScreenComponentEntity> screenComponentEntityList = screenComponentService.list(screenComponentWrapper);
        for (ScreenComponentEntity screenComponentEntity : screenComponentEntityList) {
            LambdaQueryWrapper<ComponentEntity> componentWrapper = new LambdaQueryWrapper<>();
            componentWrapper.eq(ComponentEntity::getScreenId, screenComponentEntity.getId());
            List<ComponentEntity> componentEntityList = componentService.list(componentWrapper);
            for (ComponentEntity componentEntity : componentEntityList) {
                ComponentMethodEnum componentMethodEnum = ComponentMethodEnum.getEnumByComponentName(componentEntity.getName());
                if (componentMethodEnum == null) {
                    log.info("{}组件未配置枚举", componentEntity.getName());
                    continue;
                }
                try {
                    String data = recordComponentDataService.getComponentData(componentMethodEnum, new Date());
                    RecordComponentDataEntity recordComponentDataEntity = RecordComponentDataEntity.builder()
                            .name(componentEntity.getName()).data(data).time(date).build();
                    LambdaQueryWrapper<RecordComponentDataEntity> recordComponentDataWrapper = new LambdaQueryWrapper<>();
                    recordComponentDataWrapper.eq(RecordComponentDataEntity::getTime, date)
                            .eq(RecordComponentDataEntity::getName, componentEntity.getName());
                    RecordComponentDataEntity recordComponentDataEntityTemp = recordComponentDataService.getOne(recordComponentDataWrapper);
                    recordComponentDataWrapper.eq(RecordComponentDataEntity::getTime, date);
                    if (recordComponentDataEntityTemp != null) {
                        recordComponentDataEntity.setId(recordComponentDataEntityTemp.getId());
                    }
                    recordComponentDataService.saveOrUpdate(recordComponentDataEntity);
                } catch (Exception e) {
                    log.error("组件:{},日期：{},数据保存失败{}", componentEntity.getName(), date, e);
                }
            }
        }
    }
}
