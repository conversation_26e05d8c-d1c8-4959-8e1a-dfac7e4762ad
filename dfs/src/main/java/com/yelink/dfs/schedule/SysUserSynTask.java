package com.yelink.dfs.schedule;

import com.yelink.dfs.entity.user.SysUserEntity;
import com.yelink.dfs.mapper.user.SysRoleMapper;
import com.yelink.dfs.service.impl.common.WorkPropertise;
import com.yelink.dfs.service.user.SysUserService;
import com.yelink.dfscommon.utils.DateUtil;
import com.yelink.inner.common.AppsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

import static com.yelink.dfs.constant.Constant.KEY_CLOAK;

/**
 * @Description: 定时更新同步精制用户
 * @Author: SQ
 * @Date: 2022/1/13 16:37
 * @Version:1.0
 */
@Slf4j
@Service
@ConditionalOnProperty(value = "oauthModel.type", havingValue = KEY_CLOAK, matchIfMissing = true)
public class SysUserSynTask {
    @Autowired
    RedisTemplate redisTemplate;
    @Autowired
    WorkPropertise workPropertise;
    @Autowired
    AppsService appsService;
    @Autowired
    SysUserService sysUserService;
    @Autowired
    SysRoleMapper sysRoleMapper;

    /**
     * 每分钟同步用户数据(IM用户名)
     */
//    @Scheduled(cron = "0 */1 * * * ?")
    public void synUserData() {
        //如果返回false，则说明该key值存在，已经有程序在使用这个key值，从而实现了分布式加锁的功能
        Boolean lockStatus = redisTemplate.opsForValue().setIfAbsent(workPropertise.getJzMark(), new Date(), 5, TimeUnit.MINUTES);
        if (lockStatus == null || !lockStatus) {
            return;
        }
        // 查询数据库中同步到数据
        List<SysUserEntity> list = sysUserService.lambdaQuery().isNotNull(SysUserEntity::getKeycloakId).isNull(SysUserEntity::getImUserName).list();
        if (!CollectionUtils.isEmpty(list)) {
            try {
                for (SysUserEntity sysUserEntity : list) {
                    log.debug("{}，定时同步IM用户:{}任务到精制IM用户", DateUtil.format(new Date(), DateUtil.DATETIME_FORMAT), sysUserEntity.getUsername());
//                    appsService.addUserByJz(sysUserEntity);
                }
            } catch (Exception e) {
                log.error("定时任务执行同步精制IM用户异常:", e);
            } finally {
                redisTemplate.delete(workPropertise.getJzMark());
            }
        }
    }
}

