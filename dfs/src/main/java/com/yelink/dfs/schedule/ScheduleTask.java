package com.yelink.dfs.schedule;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yelink.dfs.constant.RedisKeyPrefix;
import com.yelink.dfs.constant.statement.ReportStateEnum;
import com.yelink.dfs.entity.statement.ManageReportTaskEntity;
import com.yelink.dfs.entity.target.TargetDictEntity;
import com.yelink.dfs.entity.video.VideoEntity;
import com.yelink.dfs.entity.video.dto.YelinkVideoDeviceDTO;
import com.yelink.dfs.event.PerMinute;
import com.yelink.dfs.migration.HistoryDataMigration;
import com.yelink.dfs.service.common.UploadService;
import com.yelink.dfs.service.furnace.FurnaceService;
import com.yelink.dfs.service.impl.target.TargetGeneratorService;
import com.yelink.dfs.service.statement.ManageReportService;
import com.yelink.dfs.service.statement.ManageReportTaskService;
import com.yelink.dfs.service.target.TargetDictService;
import com.yelink.dfs.service.user.SysPermissionService;
import com.yelink.dfs.service.video.VideoService;
import com.yelink.dfscommon.utils.DateUtil;
import com.yelink.notice.constant.TopicEnum;
import com.yelink.notice.service.SocketSubscribeService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.context.ApplicationContext;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * @description: 定时任务
 * @author: zengfu
 * @create: 2021-02-01 17:22
 **/
@Slf4j
@Service
@AllArgsConstructor
public class ScheduleTask implements ApplicationRunner {

    private RedisTemplate<String, Object> redisTemplate;
    private TargetDictService targetDictService;
    private FurnaceService furnaceService;
    private UploadService uploadService;
    private SysPermissionService sysPermissionService;
    private TargetGeneratorService targetGeneratorService;
    private SocketSubscribeService socketSubscribeService;
    private final VideoService videoService;
    private final ManageReportService manageReportService;
    private final ManageReportTaskService manageReportTaskService;
    private HistoryDataMigration historyDataMigration;
    private ApplicationContext applicationContext;

    /**
     * 定时分页同步云平台的摄像头在线状态。暂定为5分钟同步一次，同步完成了再同步下一次（10s数据量太多会比较消耗性能，不会频繁上下线，所以不需要这么频繁）
     * （双机）
     */
    @Scheduled(cron = "0 0/5 * * * ?")
    public void syncVideoDeviceOnlineState() {
        String key = RedisKeyPrefix.DFS_SCHEDULE_TASK + "syncVideoDeviceOnlineState";
        long increment = redisTemplate.opsForValue().increment(key, 1);
        if (increment != 1) {
            return;
        }
        redisTemplate.expire(key, 2, TimeUnit.MINUTES);
        log.info("执行定时同步云平台的摄像头在线状态...");
        List<VideoEntity> list = videoService.lambdaQuery()
                .list();
        for (VideoEntity video : list) {
            YelinkVideoDeviceDTO videoDevice = videoService.getYelinkVideoDevice(video.getDeviceCode());
            boolean isOnline = Objects.nonNull(videoDevice) ? videoDevice.getOnline() : false;
            videoService.updateById(VideoEntity.builder().id(video.getId()).online(isOnline).build());
        }
    }

    /**
     * 执行报表管理的定时任务
     * <p>
     * 每小时执行一次
     */
    @Scheduled(cron = "0 0 0/1 * * ?")
    public void manageReportTask() {
        log.info("执行报表管理的定时任务...");
        Date nowDate = new Date();
        Date preNowDate = DateUtil.addMin(nowDate, -5);
        Date afterNowDate = DateUtil.addMin(nowDate, 5);

        for (int current = 0; current < 1000; current++) {
            // 查询可以执行的报表定时任务
            Page<ManageReportTaskEntity> pageData = manageReportTaskService.lambdaQuery()
                    .eq(ManageReportTaskEntity::getState, ReportStateEnum.RELEASED.getCode())
                    .ge(ManageReportTaskEntity::getExecutionEndTime, nowDate)
                    .orderByDesc(ManageReportTaskEntity::getCreateTime)
                    .orderByDesc(ManageReportTaskEntity::getTaskId)
                    .page(new Page<>(current, 100));
            if (CollectionUtils.isEmpty(pageData.getRecords())) {
                break;
            }
            for (ManageReportTaskEntity task : pageData.getRecords()) {
                // 执行定时任务
                manageReportService.executionManageReportTask(task, nowDate, preNowDate, afterNowDate);
            }
        }
        // 任务到期后自动失效且不可再次生效
        manageReportTaskService.autoInvalidTask();
    }

    /**
     * 每晚零点执行
     */
    //@Scheduled(cron = "0 0 0 * * ?")
    public void execEveryDay() {
        clearUploadResource();
    }

    /**
     * 每晚3点数据迁移
     */
    @Scheduled(cron = "0 0 3 * * ?")
    public void migrant() {
        historyDataMigration.historyDataMigration();
    }

    /**
     * 清理上传文件
     */
    private void clearUploadResource() {
        try {
            uploadService.clearUploadResource();
        } catch (Exception e) {
            log.error("清理上传文件异常：", e);
        }
    }

    /**
     * 触发器
     * 每秒触发一次
     */
    @Scheduled(cron = "* * * * * ?")
    public void trigger() {
        //触发指标生产
        targetGeneratorService.generateTarget();
    }

    /**
     * 每分钟执行一次
     */
    @Scheduled(cron = "0 0/1 * * * ?")
    public void perMinute() {
        applicationContext.publishEvent(new PerMinute(this));
    }
    /*@Scheduled(cron = "0 0/1 * * * ?")
    public void diskCapacity() {
        try {
            // 查询Linux总磁盘空间
//            double totalDiskSpace = FileSystemUtils.freeSpaceKb("/");
            // 查询Linux可用的磁盘空间
            File diskPartition = new File("/");
            double totalDiskSpace = diskPartition.getTotalSpace() / (1024 * 1024 * 1024);
            double usableSpace = diskPartition.getUsableSpace() / (1024 * 1024 * 1024);
            double usedSpace = totalDiskSpace - usableSpace;
            // 计算磁盘占用比
            double occupyRate = MathUtil.divideDouble(usedSpace, totalDiskSpace, 2);
            log.debug("usableSpace{}", usableSpace);
            log.debug("totalDiskSpace{}", totalDiskSpace);
            log.debug("occupyRate{}", occupyRate);
            // 获取磁盘使用率阈值
            Double diskUsage = workPropertise.getDiskUsage();
            if (occupyRate > diskUsage) {
                List<SensorValueTemplate> list = new ArrayList<>();
                list.add(SensorValueTemplate.builder().name("已使用容量").ename("usedSpace").value(String.valueOf(usedSpace)).unit("GB").build());
                list.add(SensorValueTemplate.builder().name("磁盘总容量").ename("totalDiskSpace").value(String.valueOf(totalDiskSpace)).unit("GB").build());
                list.add(SensorValueTemplate.builder().name("磁盘占用率").ename("occupyRate").value(String.valueOf(occupyRate * 100)).unit("%").build());
                alarmService.dealDiskCapacityAlarm(JSON.toJSONString(list));
            }
        } catch (Exception e) {
            log.error("监控磁盘容量异常", e);
        }
    }*/

    /**
     * 每五分钟执行一次
     * 储存所有的指标记录五分钟平均值
     */
    /*@Scheduled(cron = "0 0/5 * * * ?")
    public void targetRecordFiVeMin() {
        if (Boolean.FALSE.equals(redisTemplate.opsForValue().setIfAbsent(RedisKeyPrefix.TARGET_RECORD_FIVE_MIN, new Date(), 1, TimeUnit.MINUTES))) {
            return;
        }
        Date time = DateUtil.formatToDate(new Date(), DateUtil.DATETIME_FORMAT_MIN);

        //获取所有的device指标列表
        List<TargetDictEntity> targetDictList = getTargetDictList();
        //TODO将所有除记录状态的指标记录生成一条五分钟平均值的记录
        indicatorService.addIndicatorFiveMin(targetDictList, time);
        redisTemplate.delete(RedisKeyPrefix.TARGET_RECORD_FIVE_MIN);
    }*/

    /**
     * 每二十分钟执行一次
     * 储存所有的指标记录二十分钟平均值
     */
    /*@Scheduled(cron = "0 0/20 * * * ?")
    public void targetRecordTwentyMin() {
        if (Boolean.FALSE.equals(redisTemplate.opsForValue().setIfAbsent(RedisKeyPrefix.TARGET_RECORD_TWENTY_MIN, new Date(), 1, TimeUnit.MINUTES))) {
            return;
        }
        Date time = DateUtil.formatToDate(new Date(), DateUtil.DATETIME_FORMAT_MIN);
        //获取所有的device指标列表
        List<TargetDictEntity> targetDictList = getTargetDictList();
        //TODO将所有除记录状态的指标记录生成一条五分钟平均值的记录
        indicatorService.addIndicatorTwentyMin(targetDictList, time);
    }*/

    /**
     * 每一小时执行一次
     * 储存所有的指标记录一小时平均值
     */
//    @Scheduled(cron = "0 0 */1 * * ?")
    /*public void targetRecordOneHour() {
        if (Boolean.FALSE.equals(redisTemplate.opsForValue().setIfAbsent(RedisKeyPrefix.TARGET_RECORD_ONE_HOUR, new Date(), 1, TimeUnit.MINUTES))) {
            return;
        }
        Date time = DateUtil.formatToDate(new Date(), DateUtil.DATETIME_FORMAT_HOUR);
        //获取所有的device指标列表
        List<TargetDictEntity> targetDictList = getTargetDictList();
        //TODO将所有除记录状态的指标记录生成一条一小时平均值的记录
        indicatorService.addIndicatorOneHour(targetDictList, time);
    }*/

    /**
     * 每天执行一次，零点晚一分钟，等待小时记录完成
     * 储存所有的指标记录每天平均值
     */
//    @Scheduled(cron = "0 2 0 */1 * ?")
    /*public void targetRecordDay() {
        if (Boolean.FALSE.equals(redisTemplate.opsForValue().setIfAbsent(RedisKeyPrefix.TARGET_RECORD_ONE_DAY, new Date(), 1, TimeUnit.MINUTES))) {
            return;
        }
        Date endTime = DateUtil.formatToDate(new Date(), DateUtil.DATETIME_FORMAT_ZERO);
        Date startTime = DateUtil.addDate(endTime, -1);
        //获取所有的device指标列表
        List<TargetDictEntity> targetDictList = getTargetDictList();
        //TODO将所有除记录状态的指标记录生成一条五分钟平均值的记录
        indicatorService.addIndicatorOneDay(targetDictList, endTime, startTime);
    }*/

    /**
     * 每七天执行一次 每周一零点五分执行，晚五分钟等待天数记录完成
     * 储存所有的指标记录七天平均值
     */
    /*@Scheduled(cron = "0 5 0 ? * MON")
    public void targetRecordWeek() {
        if (Boolean.FALSE.equals(redisTemplate.opsForValue().setIfAbsent(RedisKeyPrefix.TARGET_RECORD_WEEK, new Date(), 1, TimeUnit.MINUTES))) {
            return;
        }
        Date endTime = DateUtil.formatToDate(new Date(), DateUtil.DATETIME_FORMAT_HOUR);
        Date startTime = DateUtil.addDate(endTime, -7);
        //获取所有的device指标列表
        List<TargetDictEntity> targetDictList = getTargetDictList();
        //TODO将所有除记录状态的指标记录生成一条七天平均值的记录
        indicatorService.addIndicatorWeek(targetDictList, endTime, startTime);
    }*/

    /**
     * 每月执行一次，晚五分钟等待天数记录完成
     * 储存所有的指标记录每月平均值
     */
    /*@Scheduled(cron = "0 5 0 1 1/1 ?")
    public void targetRecorMoth() {
        if (Boolean.FALSE.equals(redisTemplate.opsForValue().setIfAbsent(RedisKeyPrefix.TARGET_RECORD_MONTH, new Date(), 1, TimeUnit.MINUTES))) {
            return;
        }
        Date endTime = DateUtil.formatToDate(new Date(), DateUtil.DATETIME_FORMAT_ZERO);
        Date startTime = DateUtil.addMonths(endTime, -1);
        //获取所有的device指标列表
        List<TargetDictEntity> targetDictList = getTargetDictList();
        //TODO将所有除记录状态的指标记录生成一条五分钟平均值的记录
        indicatorService.addIndicatorMouth(targetDictList, endTime, startTime);
    }*/

    /**
     * 获取所有的device指标列表
     *
     * @return
     */
    private List<TargetDictEntity> getTargetDictList() {
        //获取所有的device指标列表
        List<TargetDictEntity> targetDictList = JSON.parseArray((String) redisTemplate.opsForValue().get(RedisKeyPrefix.TARGET_DEVICE_LIST), TargetDictEntity.class);
        if (CollectionUtils.isEmpty(targetDictList)) {
            QueryWrapper<TargetDictEntity> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().eq(TargetDictEntity::getModelType, "device");
            targetDictList = targetDictService.list(queryWrapper);
            redisTemplate.opsForValue().set(RedisKeyPrefix.TARGET_DEVICE_LIST, JSON.toJSONString(targetDictList), 1, TimeUnit.DAYS);
        }
        return targetDictList;
    }


    /**
     * 定时查询到socket订阅的工位信息：根据定位的工位id查询到工位信息进行推送
     * 这个无需同步锁，因为发送消息是根据订阅的用户而来
     */
//    @Scheduled(cron = "0 0/1 * * * ?")
    protected void sendFurnaceMessage() {
        Pattern pattern = Pattern.compile("^" + TopicEnum.FURNANCE_CODE_TOPIC.getTopic().replace("+", "[0-9a-zA-Z]+") + "$");
        List<String> topics = socketSubscribeService.subTopicList().stream().filter(s -> pattern.matcher(s).matches()).collect(Collectors.toList());
        for (String topic : topics) {
            // 获取fid
            int begin = topic.lastIndexOf("/") + 1;
            int last = topic.length();
            //获得文件后缀名
            String fid = topic.substring(begin, last);
            if (StringUtils.isNotBlank(fid)) {
                socketSubscribeService.sendMessage(topic, furnaceService.getFurnaceDetail(Integer.parseInt(fid)), null);
            }
        }
    }


    /**
     * 每5分钟执行一次 更新当前企业小程序权限 精制发kafka消息触发
     */
//    @Scheduled(cron = "0 0/5 * * * ?")
//    public void updateAppPermissions() {
//        sysPermissionService.judgeAppButtonPermission();
//        sysPermissionService.updateAppPermission();
//    }

    /**
     * 启动后执行 更新当前企业小程序权限
     *
     * @param args
     * @throws Exception
     */
    @Override
    public void run(ApplicationArguments args) throws Exception {
        try {
            sysPermissionService.judgeAppButtonPermission();
            sysPermissionService.updateAppPermission();
        } catch (Exception e) {
            log.error("ECM同步小程序失败", e);
        }
    }
}
