package com.yelink.dfs.schedule;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.yelink.dfs.constant.RedisKeyPrefix;
import com.yelink.dfs.entity.capacity.dto.CapacityUtilizationDayDTO;
import com.yelink.dfs.entity.screen.dto.StateOverviewDTO;
import com.yelink.dfs.entity.target.record.RecordLineDayUnionEntity;
import com.yelink.dfs.entity.work.calendar.WorkCalendarEntity;
import com.yelink.dfs.provider.constant.KafkaMessageTypeEnum;
import com.yelink.dfs.service.screen.ProductionService;
import com.yelink.dfs.service.target.record.RecordLineDayUnionService;
import com.yelink.dfs.service.work.calendar.WorkCalendarService;
import com.yelink.dfscommon.constant.Constants;
import com.yelink.dfscommon.provider.MessagePushToKafkaService;
import com.yelink.dfscommon.utils.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * @Description:同步产能数据
 * @Author: SQ
 * @Date: 2022/2/16 10:30
 * @Version:1.0
 */
@Component
@EnableScheduling
@Slf4j
public class SynCapacityScheduleTask {

    @Autowired
    private ProductionService productionService;
    @Autowired
    private RecordLineDayUnionService recordLineDayUnionService;
    @Autowired
    private WorkCalendarService workCalendarService;
    @Autowired
    private MessagePushToKafkaService messagePushToKafkaService;

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    /**
     * 生产状态数据推送:保证数据的实时性，每1分钟进行数据的推送(希望修改成 通过事件触发进行同步)
     */
    @Scheduled(cron = "0 */1 * * * ?")
    public void pushProductMessage() {
        Boolean lockStatus = redisTemplate.opsForValue().setIfAbsent(RedisKeyPrefix.TASK_LOCK_PUSH_PRODUCT_STATUS_DATA + DateUtil.convertJavaDateToSqlDate(new Date()), "生产状态数据定时任务锁", 1, TimeUnit.HOURS);
        if (lockStatus == null || !lockStatus) {
            return;
        }
        // 查询生产状态
        StateOverviewDTO stateOverviewDTO = productionService.workOrderInformation(null, new Date());
        if (stateOverviewDTO != null) {
            //推送到doc模块
            messagePushToKafkaService.pushNewMessage(stateOverviewDTO, Constants.KAFKA_MAIN_DATA_TOPIC, KafkaMessageTypeEnum.WORK_ORDER_PRODUCT_MESSAGE);
        }
    }


    /**
     * 每天凌晨1点统计每天的产能利用率
     */
    @Scheduled(cron = "0 0 1 * * ?")
    public void pushOeeDay() {
        Boolean lockStatus = redisTemplate.opsForValue().setIfAbsent(RedisKeyPrefix.TASK_LOCK_PUSH_OEE_DAY + DateUtil.convertJavaDateToSqlDate(new Date()), "产能利用率定时任务锁", 1, TimeUnit.HOURS);
        if (lockStatus == null || !lockStatus) {
            return;
        }
        // 查询当天的所有的产线oee
        QueryWrapper<RecordLineDayUnionEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .select(RecordLineDayUnionEntity::getProductionLineId, RecordLineDayUnionEntity::getOee)
                .eq(RecordLineDayUnionEntity::getTime, DateUtil.getDayBegin(DateUtil.addDate(new Date(), -1)));
        List<RecordLineDayUnionEntity> list = recordLineDayUnionService.list(queryWrapper);
        // 计算当天所有产线的Oee-是否是所有车间的产线
        double oee;
        if (!CollectionUtils.isEmpty(list)) {
            oee = list.stream().mapToDouble(RecordLineDayUnionEntity::getOee).summaryStatistics().getSum();
        } else {
            oee = 0.0;
        }
        // 计算当天排产的产线数
        QueryWrapper<WorkCalendarEntity> query = new QueryWrapper<>();
        query.lambda().isNotNull(WorkCalendarEntity::getValidityStart)
                .isNotNull(WorkCalendarEntity::getValidityEnd)
                .le(WorkCalendarEntity::getValidityStart, DateUtil.formatToDate(DateUtil.addDate(new Date(), -1), DateUtil.DATE_FORMAT))
                .ge(WorkCalendarEntity::getValidityEnd, DateUtil.formatToDate(DateUtil.addDate(new Date(), -1), DateUtil.DATE_FORMAT));
        List<WorkCalendarEntity> workCalendarEntities = workCalendarService.list(query);
        int lines = workCalendarEntities.size();
        double capacityUtilization;
        // 公式：每天所有的产线oee总和/产线工作日历排产的产线数量
        if (lines == 0) {
            capacityUtilization = 0.0;
        } else {
            capacityUtilization = oee / lines;
        }
        CapacityUtilizationDayDTO capacityUtilizationDayDTO = CapacityUtilizationDayDTO
                .builder()
                .capacityUtilization(capacityUtilization)
                .currentDay(DateUtil.addDate(new Date(), -1))
                .build();
        messagePushToKafkaService.pushNewMessage(capacityUtilizationDayDTO, Constants.KAFKA_MAIN_DATA_TOPIC, KafkaMessageTypeEnum.CAPACITY_UTILIZATION_ANYLYSE_MESSAGE);
    }

}
