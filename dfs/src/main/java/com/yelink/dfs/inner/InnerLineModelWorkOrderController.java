package com.yelink.dfs.inner;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.yelink.dfs.constant.device.DevicesStateEnum;
import com.yelink.dfs.constant.model.ProductionLineStateEnum;
import com.yelink.dfs.constant.order.WorkOrderCirculationStateEnum;
import com.yelink.dfs.constant.order.WorkOrderPlanStateEnum;
import com.yelink.dfscommon.constant.dfs.order.WorkOrderStateEnum;
import com.yelink.dfs.entity.device.DeviceEntity;
import com.yelink.dfs.entity.manufacture.ProductionLineEntity;
import com.yelink.dfs.entity.order.OrderWorkOrderEntity;
import com.yelink.dfs.entity.order.RecordWorkOrderDayCountEntity;
import com.yelink.dfs.entity.order.RecordWorkOrderLineDayCountEntity;
import com.yelink.dfs.entity.order.WorkOrderEntity;
import com.yelink.dfs.entity.order.WorkOrderProcedureRelationEntity;
import com.yelink.dfs.entity.product.CraftProcedureEntity;
import com.yelink.dfs.entity.product.dto.CraftProcedureWorkOrderDTO;
import com.yelink.dfs.entity.target.record.RecordLineDayUnionEntity;
import com.yelink.dfs.entity.target.record.ReportLineEntity;
import com.yelink.dfs.open.v1.aksk.ams.ExtProductOrderInterface;
import com.yelink.dfs.service.common.DictService;
import com.yelink.dfs.service.device.DeviceService;
import com.yelink.dfs.service.manufacture.ProductionLineService;
import com.yelink.dfs.service.order.OrderWorkOrderService;
import com.yelink.dfs.service.order.RecordWorkOrderDayCountService;
import com.yelink.dfs.service.order.RecordWorkOrderLineDayCountService;
import com.yelink.dfs.service.order.WorkOrderProcedureRelationService;
import com.yelink.dfs.service.order.WorkOrderService;
import com.yelink.dfs.service.product.CraftProcedureService;
import com.yelink.dfs.service.target.record.RecordLineDayUnionService;
import com.yelink.dfs.service.target.record.ReportLineService;
import com.yelink.dfs.service.user.SysUserService;
import com.yelink.dfs.service.work.calendar.WorkCalendarService;
import com.yelink.dfscommon.api.assignment.AssignmentInterface;
import com.yelink.dfscommon.api.dfs.LineModelWorkOrderInterface;
import com.yelink.dfscommon.constant.Constants;
import com.yelink.dfscommon.constant.ModelEnum;
import com.yelink.dfscommon.constant.OrderNumTypeEnum;
import com.yelink.dfscommon.dto.OperationDayCountDTO;
import com.yelink.dfscommon.dto.ReportCountDTO;
import com.yelink.dfscommon.dto.screen.LineModelProductDataDTO;
import com.yelink.dfscommon.dto.screen.LineModelTodayDataDTO;
import com.yelink.dfscommon.dto.screen.LineProductDataDTO;
import com.yelink.dfscommon.dto.screen.OperationOrderStateDTO;
import com.yelink.dfscommon.dto.screen.PreviousProcedureDTO;
import com.yelink.dfscommon.dto.screen.ProcedureDTO;
import com.yelink.dfscommon.entity.ams.ProductOrderEntity;
import com.yelink.dfscommon.pojo.ResponseData;
import com.yelink.dfscommon.provider.MessagePushToKafkaService;
import com.yelink.dfscommon.utils.JacksonUtil;
import com.yelink.dfscommon.utils.MathUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR> @date 2022-08-11
 */
@Slf4j
@AllArgsConstructor
@RestController
public class InnerLineModelWorkOrderController implements LineModelWorkOrderInterface {

    private ProductionLineService lineService;
    private DeviceService deviceService;
    private DictService dictService;
    private WorkOrderService workOrderService;
    private RecordLineDayUnionService lineDayUnionService;
    private RecordWorkOrderDayCountService workOrderDayCountService;
    private ReportLineService reportLineService;
    private AssignmentInterface assignmentInterface;
    private WorkCalendarService calendarService;
    private SysUserService userService;
    private WorkOrderProcedureRelationService procedureRelationService;
    private OrderWorkOrderService orderWorkOrderService;
    private CraftProcedureService craftProcedureService;
    private MessagePushToKafkaService messagePushToKafkaService;
    private ExtProductOrderInterface extProductOrderInterface;
    private RecordWorkOrderLineDayCountService recordWorkOrderLineDayCountService;

    /**
     * 产线模型当天数据
     *
     * @param lineModelId
     * @return
     */
    @Override
    public ResponseData getTodayData(Integer lineModelId) {
        Date recordDate = dictService.getRecordDate(new Date());
        //完成数
        Double finishCount = getFinishCount(recordDate, lineModelId);
        //完成单数
        Long finishOrderCount = getFinishOrderCount(recordDate, lineModelId);
        //待生产数
        Double planCount = getPlanCount(lineModelId);
        //待生产单数
        Long planOrderCount = getPlanOrderCount(lineModelId);
        //待排产数
        Double toBePlanCount = getToBePlanCount(lineModelId);
        //待排产单数
        Long toBePlanOrderCount = getToBePlanOrderCount(lineModelId);
        //总时长与空闲时长
        LineModelTodayDataDTO dto = getFreeDuration(lineModelId, recordDate);
        dto.setFinishCount(finishCount);
        dto.setFinishOrderCount(finishOrderCount);
        dto.setPlanCount(planCount);
        dto.setPlanOrderCount(planOrderCount);
        dto.setToBePlanCount(toBePlanCount);
        dto.setToBePlanOrderCount(toBePlanOrderCount);
        return ResponseData.success(dto);
    }

    /**
     * 待排产单数：该产线类型下，状态为生效，工单的排产状态为可排产，这些工单的个数；
     *
     * @return
     */
    private Long getToBePlanOrderCount(Integer lineModelId) {
        Long count = workOrderService.lambdaQuery()
                .eq(WorkOrderEntity::getWorkCenterId, lineModelId)
                .eq(WorkOrderEntity::getPlanState, WorkOrderPlanStateEnum.ENABE.getCode())
                .eq(WorkOrderEntity::getState, WorkOrderStateEnum.RELEASED.getCode())
                .count();
        return count;
    }

    /**
     * 待排产数量：该产线类型下，状态为生效，工单的排产状态为可排产，只取这些生产工单的计划数量之和；（未指定产线）
     *
     * @param lineModelId
     * @return
     */
    private Double getToBePlanCount(Integer lineModelId) {
        QueryWrapper<WorkOrderEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("IFNULL(sum(plan_quantity),0) AS plan_quantity");
        LambdaQueryWrapper<WorkOrderEntity> wrapper = queryWrapper.lambda();
        wrapper.eq(WorkOrderEntity::getWorkCenterId, lineModelId)
                .eq(WorkOrderEntity::getPlanState, WorkOrderPlanStateEnum.ENABE.getCode())
                .eq(WorkOrderEntity::getState, WorkOrderStateEnum.RELEASED.getCode());
        WorkOrderEntity workOrderEntity = workOrderService.getOne(wrapper);
        if (workOrderEntity != null) {
            return workOrderEntity.getPlanQuantity();
        }
        return 0.0;
    }

    /**
     * 获取当日完成数
     * 已完成数量：该产线类型，所有产线(工单/作业工单)当天的完成数量之和；（生产工单/作业工单分开计算，可直接统计完成数）
     *
     * @param recordDate
     * @return
     */
    private Double getFinishCount(Date recordDate, Integer lineModelId) {
        //获取生产工单当天数据
        List<RecordWorkOrderDayCountEntity> dayCountEntities = workOrderDayCountService.lambdaQuery()
                .select(RecordWorkOrderDayCountEntity::getWorkOrderNumber, RecordWorkOrderDayCountEntity::getCount)
                .eq(RecordWorkOrderDayCountEntity::getTime, recordDate).list();
        HashMap<String, Double> map = new HashMap<>();
        for (RecordWorkOrderDayCountEntity countEntity : dayCountEntities) {
            Double count = countEntity.getCount() == null ? 0.0 : countEntity.getCount();
            map.merge(countEntity.getWorkOrderNumber(), count, Double::sum);
        }
        //for (OperationDayCountDTO dto : assignments) {
        //    Double count = dto.getCount() == null ? 0.0 : dto.getCount();
        //    map.merge(dto.getWorkOrderNumber(), count, Double::sum);
        //}
        // 筛选出为本产线模型的数据
        List<WorkOrderEntity> workOrderEntities = workOrderService.lambdaQuery().select(WorkOrderEntity::getWorkOrderNumber)
                .eq(WorkOrderEntity::getWorkCenterId, lineModelId).list();
        Double total = 0.0;
        for (WorkOrderEntity orderEntity : workOrderEntities) {
            String workOrderNumber = orderEntity.getWorkOrderNumber();
            Double count = map.get(workOrderNumber);
            if (count != null) {
                total += count;
            }
        }
        return total;
    }


    /**
     * 待生产数量：该产线类型下,状态为生效,工单的排产状态为已排产，并且有作业工单(或有产线名称)，这些工单(作业工单)的计划数量之和;
     * （工单状态：生效,投产,挂起;已排产;计算：计划数-完成数（不一定是当天）;只取生产工单计算;已确认作业工单会同步完成数到工单）
     *
     * @param lineModelId
     * @return
     */
    private Double getPlanCount(Integer lineModelId) {
        List<WorkOrderEntity> workOrderEntities = workOrderService.lambdaQuery()
                .select(WorkOrderEntity::getPlanQuantity, WorkOrderEntity::getFinishCount)
                .eq(WorkOrderEntity::getWorkCenterId, lineModelId)
                .eq(WorkOrderEntity::getPlanState, WorkOrderPlanStateEnum.PLANED.getCode())
                .in(WorkOrderEntity::getState, WorkOrderStateEnum.RELEASED.getCode(),
                        WorkOrderStateEnum.INVESTMENT.getCode(),
                        WorkOrderStateEnum.HANG_UP.getCode())
                .list();
        Double planCount = 0.0;
        Double finishCount = 0.0;
        for (WorkOrderEntity orderEntity : workOrderEntities) {
            Double planQuantity = orderEntity.getPlanQuantity();
            Double entityFinishCount = orderEntity.getFinishCount() == null ? 0.0 : orderEntity.getFinishCount();
            planCount += planQuantity;
            finishCount += entityFinishCount;
        }
        return planCount - finishCount;
    }

    /**
     * 获取当天完成单数
     * 已完成单数：该产线类型，工单(作业工单)实际完成时间为当天的工单个数；（完成时间在当天，工单/作业工单分开计算）
     *
     * @param recordDate
     * @param lineModelId
     * @return
     */
    private Long getFinishOrderCount(Date recordDate, Integer lineModelId) {
        //作业工单数
        List<OperationDayCountDTO> assignments = new ArrayList<>();
        try {
            ResponseData responseData = assignmentInterface.getDayFinishOrderCount(recordDate.getTime());
            assignments = JacksonUtil.getResponseArray(responseData, OperationDayCountDTO.class);
        } catch (Exception e) {
            log.error("请求assignment服务错误", e);
        }
        Map<String, Long> assignmentMap = assignments.stream().collect(Collectors.groupingBy(OperationDayCountDTO::getWorkOrderNumber, Collectors.counting()));
        Long assignmentCount = 0L;
        if (!CollectionUtils.isEmpty(assignmentMap)) {
            List<WorkOrderEntity> orderEntities = workOrderService.lambdaQuery()
                    .select(WorkOrderEntity::getWorkOrderNumber)
                    .eq(WorkOrderEntity::getWorkCenterId, lineModelId)
                    .in(WorkOrderEntity::getWorkOrderNumber, assignmentMap.keySet())
                    .list();
            for (WorkOrderEntity entity : orderEntities) {
                String workOrderNumber = entity.getWorkOrderNumber();
                if (assignmentMap.containsKey(workOrderNumber)) {
                    Long aLong = assignmentMap.get(workOrderNumber);
                    assignmentCount += aLong;
                }
            }
        }
        //生产工单数
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(recordDate);
        calendar.add(Calendar.DATE, 1);
        calendar.add(Calendar.SECOND, -1);
        Date time = calendar.getTime();
        Long count = workOrderService.lambdaQuery()
                .eq(WorkOrderEntity::getWorkCenterId, lineModelId)
                .between(WorkOrderEntity::getActualEndDate, recordDate, time)
                //除去作业工单关联生产工单,否则会出现有作业工单的生产工单也被统计的情况
                .notIn(!CollectionUtils.isEmpty(assignmentMap), WorkOrderEntity::getWorkOrderNumber, assignmentMap.keySet())
                .count();
        return assignmentCount + count;
    }

    /**
     * 待生产单数：该产线类型下，状态为生效，工单的排产状态为已排产，并且有作业工单(或有产线名称)，这些工单(作业工单)的个数；
     * （工单状态：生效，投产，挂起；已排产的工单个数；工单/作业工单分开计算）
     *
     * @param lineModelId
     * @return
     */
    private Long getPlanOrderCount(Integer lineModelId) {
        //作业工单数
        List<OperationDayCountDTO> assignments = new ArrayList<>();
        try {
            ResponseData responseData = assignmentInterface.getPlanOrderCount(lineModelId);
            assignments = JacksonUtil.getResponseArray(responseData, OperationDayCountDTO.class);
        } catch (Exception e) {
            log.error("请求assignment服务错误", e);
        }
        Map<String, Long> assignmentMap = assignments.stream().filter(o -> StringUtils.isNotBlank(o.getWorkOrderNumber()))
                .collect(Collectors.groupingBy(OperationDayCountDTO::getWorkOrderNumber, Collectors.counting()));
        Long assignmentCount = 0L;
        if (!CollectionUtils.isEmpty(assignmentMap)) {
            List<WorkOrderEntity> orderEntities = workOrderService.lambdaQuery()
                    .select(WorkOrderEntity::getWorkOrderNumber)
                    .eq(WorkOrderEntity::getWorkCenterId, lineModelId)
                    .in(WorkOrderEntity::getWorkOrderNumber, assignmentMap.keySet())
                    .eq(WorkOrderEntity::getPlanState, WorkOrderPlanStateEnum.PLANED.getCode())
                    .in(WorkOrderEntity::getState, WorkOrderStateEnum.RELEASED.getCode(),
                            WorkOrderStateEnum.INVESTMENT.getCode(),
                            WorkOrderStateEnum.HANG_UP.getCode())
                    .list();
            for (WorkOrderEntity entity : orderEntities) {
                String workOrderNumber = entity.getWorkOrderNumber();
                if (assignmentMap.containsKey(workOrderNumber)) {
                    Long aLong = assignmentMap.get(workOrderNumber);
                    assignmentCount += aLong;
                }
            }
        }
        Long count = workOrderService.lambdaQuery()
                .select(WorkOrderEntity::getWorkOrderNumber)
                .eq(WorkOrderEntity::getWorkCenterId, lineModelId)
                .eq(WorkOrderEntity::getPlanState, WorkOrderPlanStateEnum.PLANED.getCode())
                .in(WorkOrderEntity::getState, WorkOrderStateEnum.RELEASED.getCode(),
                        WorkOrderStateEnum.INVESTMENT.getCode(),
                        WorkOrderStateEnum.HANG_UP.getCode())
                //除去作业工单关联生产工单,否则会出现有作业工单的生产工单也被统计的情况
                .notIn(!CollectionUtils.isEmpty(assignmentMap), WorkOrderEntity::getWorkOrderNumber, assignmentMap.keySet())
                .count();
        return assignmentCount + count;
    }

    /**
     * 空闲时长：该产线类型下，各个产线的当天空闲时长之和(单个产线如果空闲为负数，按0计算)；
     * (当天日历时长 – 已生产时长 – 当前工单剩余时长 – 待生产时长)
     * 已生产时长:当前时间 – 当天首班时间点;(产线维度,按工作日历统计)
     * 当前所有工单的预计剩余时长:(当前工单的计划数量 -当前工单的已完成数量) * 工单的工序的加工时长,注意单位换算为小时;(工单维度,只统计投产状态生产工单/作业工单)
     * 待生产的工单，该制造单元下:∑(生效的工单计划工时) + ∑((挂起的工单的计划数量 – 已完成数量) *工单工序的加工工时), 注意单位换算为小时;（工单维度;只统计生产工单）
     * 待生产的工单：生效，挂起；已排产的工单
     *
     * @param lineModelId
     * @return
     */
    private LineModelTodayDataDTO getFreeDuration(Integer lineModelId, Date recordDate) {
        //作业工单
        Map<Integer, Map<Integer, Set<String>>> lineMap = getAssignmentsMap(lineModelId);
        Date now = new Date();
        List<ProductionLineEntity> lineEntities = lineService.lambdaQuery()
                .select(ProductionLineEntity::getProductionLineId)
                .eq(ProductionLineEntity::getModelId, lineModelId)
                .list();
        Double freeTime = 0.0;
        Double totalhours = 0.0;
        for (ProductionLineEntity lineEntity : lineEntities) {
            Integer lineId = lineEntity.getProductionLineId();
            //日历时长
            Double workDuration = calendarService.traceWorkDuration(lineId, ModelEnum.LINE.getType(), recordDate);
            workDuration = workDuration == null ? 24 : workDuration;
            //已生产时长
            Double workDurationUpToNow = calendarService.getWorkDurationUpToNow(lineId, ModelEnum.LINE.getType(), now);
            List<String> releasedOrders = new ArrayList<>();
            List<String> investmentOrders = new ArrayList<>();
            List<String> hangUpOrders = new ArrayList<>();
            if (lineMap.containsKey(lineId)) {
                Map<Integer, Set<String>> map = lineMap.get(lineId);
                //此处用工单的状态去获取作业工单不可取,但作业工单与生产工单的状态枚举相同,先凑合了
                Integer releasedCode = WorkOrderStateEnum.RELEASED.getCode();
                if (map.containsKey(releasedCode)) {
                    Set<String> releasedOrderSet = map.get(releasedCode);
                    releasedOrders = new ArrayList<>(releasedOrderSet);
                }
                Integer investmentCode = WorkOrderStateEnum.INVESTMENT.getCode();
                if (map.containsKey(investmentCode)) {
                    Set<String> investmentOrderSet = map.get(investmentCode);
                    investmentOrders = new ArrayList<>(investmentOrderSet);
                }
                Integer hangUpCode = WorkOrderStateEnum.HANG_UP.getCode();
                if (map.containsKey(hangUpCode)) {
                    Set<String> hangUpOrderSet = map.get(hangUpCode);
                    hangUpOrders = new ArrayList<>(hangUpOrderSet);
                }
            }
            //预计剩余时长
            Double hoursOfInvestment = getWorkOrderProductTime(lineId, investmentOrders, WorkOrderStateEnum.INVESTMENT.getCode());
            //挂起状态的待生产时长
            Double hoursOfHang = getWorkOrderProductTime(lineId, hangUpOrders, WorkOrderStateEnum.HANG_UP.getCode());
            //生效状态的待生产时长:工单计划工时
            //如果工单有作业工单,是不会指定产线的,所以还需要通过产线找到[生效状态的作业工单]->关联的生产工单
            List<WorkOrderEntity> orderEntities = workOrderService.lambdaQuery()
                    .select(WorkOrderEntity::getWorkOrderNumber)
                    .eq(WorkOrderEntity::getLineId, lineId)
                    .eq(WorkOrderEntity::getState, WorkOrderStateEnum.RELEASED.getCode())
                    .eq(WorkOrderEntity::getPlanState, WorkOrderPlanStateEnum.PLANED.getCode())
                    .list();
            List<String> orders = orderEntities.stream().map(WorkOrderEntity::getWorkOrderNumber).collect(Collectors.toList());
            //生产工单+作业工单关联的生产工单
            orders.addAll(releasedOrders);
            //统计工单的计划工时
            Double releasedOrderHours = getWorkOrderPlanWorkHours(orders);

            //当天日历时长 – 已生产时长 – 当前工单剩余时长-挂起状态的待生产时长-生效状态的待生产时长
            double v = workDuration - workDurationUpToNow - hoursOfInvestment - hoursOfHang - releasedOrderHours;
            if (v > 0) {
                freeTime += v;
            }
            totalhours += workDuration;
        }
        ////待生产时长,作业工单的生效装态与生产工单是同步的，可只查询生产工单
        ////生效,已排产的工单时间
        //List<WorkOrderEntity> orderEntities = workOrderService.lambdaQuery()
        //        .select(WorkOrderEntity::getWorkOrderNumber,
        //                WorkOrderEntity::getPlannedWorkingHours)
        //        .eq(WorkOrderEntity::getGid, gid)
        //        .eq(WorkOrderEntity::getState, WorkOrderStateEnum.RELEASED.getCode())
        //        .eq(WorkOrderEntity::getPlanState, WorkOrderPlanStateEnum.PLANED.getCode())
        //        .list();
        //Double releasedOrderDuration = 0.0;
        //for (WorkOrderEntity orderEntity : orderEntities) {
        //    Double duration = orderEntity.getPlannedWorkingHours();
        //    if (duration != null) {
        //        releasedOrderDuration += duration;
        //    }
        //}
        //BigDecimal released = BigDecimal.valueOf(releasedOrderDuration).divide(BigDecimal.valueOf(60), 2, BigDecimal.ROUND_HALF_UP);
        //剩余的-生效状态的待生产时长
        //freeTime = freeTime - releasedOrderDuration;
        return LineModelTodayDataDTO.builder()
                .totalDuration(totalhours)
                .freeDuration(MathUtil.round(freeTime, 1))
                .build();
    }

    private Double getWorkOrderProductTime(Integer lineId, List<String> workOrderOfOperation, Integer state) {
        double hours = 0.0;
        //查找作业工单,作业工单数据不可靠,只统计生产工单的计划数、完成数,所以可能会有重复计算的情况
        //计算公式:∑((挂起的工单的计划数量 – 已完成数量) *工单工序的加工工时)
        Double workOrderDuration = workOrderService.getProcessingHoursOfWorkOrder(workOrderOfOperation);
        if (workOrderDuration != null) {
            BigDecimal decimal = BigDecimal.valueOf(workOrderDuration).divide(BigDecimal.valueOf(60), 2, BigDecimal.ROUND_HALF_UP);
            hours += decimal.doubleValue();
        }
        //查找生产工单的数据
        //此处传入的工单集合是将这些工单的数据排除在外,以免重复统计
        Double theRestOfDuration = workOrderService.getTheRestOfProductionTime(lineId, state, workOrderOfOperation);
        if (theRestOfDuration != null) {
            BigDecimal decimal1 = BigDecimal.valueOf(theRestOfDuration).divide(BigDecimal.valueOf(60), 2, BigDecimal.ROUND_HALF_UP);
            hours += decimal1.doubleValue();
        }
        return hours;
    }

    /**
     * 总时长：该产线类型下，各个产线的当天日历时长之和；
     *
     * @param recordDate
     * @param lineModelId
     * @return
     */
    private Double getTotalDuration(Date recordDate, Integer lineModelId) {
        List<ProductionLineEntity> lineEntities = lineService.lambdaQuery()
                .select(ProductionLineEntity::getProductionLineId)
                .eq(ProductionLineEntity::getModelId, lineModelId)
                .list();
        Double totalDuration = 0.0;
        for (ProductionLineEntity lineEntity : lineEntities) {
            Double workDuration = calendarService.traceWorkDuration(lineEntity.getProductionLineId(), ModelEnum.LINE.getType(), recordDate);
            if (workDuration != null) {
                totalDuration += workDuration;
            }
        }
        return MathUtil.round(totalDuration, 1);
    }

    /**
     * 获取报工记录
     *
     * @param lineModelId
     * @return
     */
    @Override
    public ResponseData getTodayReportData(Integer lineModelId) {
        Date recordDate = dictService.getRecordDate(new Date());
        //统计作业工单报工
        List<OperationDayCountDTO> assignments = new ArrayList<>();
        try {
            ResponseData responseData = assignmentInterface.getFinishCountGroupByUser(lineModelId, recordDate.getTime());
            assignments = JacksonUtil.getResponseArray(responseData, OperationDayCountDTO.class);
        } catch (Exception e) {
            log.error("请求assignment服务错误", e);
        }
        List<ReportLineEntity> reportLineEntities = reportLineService.getFinishCountGroupByUser(lineModelId, recordDate);
        HashMap<String, Double> map = new HashMap<>();
        for (OperationDayCountDTO assignment : assignments) {
            String userName = assignment.getOperationNumber();
            if (StringUtils.isBlank(userName)) {
                continue;
            }
            Double count = assignment.getCount();
            map.merge(userName, count, Double::sum);
        }
        for (ReportLineEntity lineEntity : reportLineEntities) {
            String userName = lineEntity.getOperator();
            if (StringUtils.isBlank(userName)) {
                continue;
            }
            Double finishCount = lineEntity.getFinishCount();
            map.merge(userName, finishCount, Double::sum);
        }
        List<ReportCountDTO> dtos = new ArrayList<>();
        Set<Map.Entry<String, Double>> entries = map.entrySet();
        for (Map.Entry<String, Double> entry : entries) {
            String key = entry.getKey();
            String[] usernameList = key.split(Constants.SEP);
            List<String> nicknameList = new ArrayList<>();
            for (String username : usernameList) {
                String userNickname = userService.getNicknameByUsername(username);
                nicknameList.add(userNickname);
            }
            String userNicknames = String.join(Constants.SEP, nicknameList);
            dtos.add(ReportCountDTO.builder()
                    .userNickname(userNicknames)
                    .finishCount(entry.getValue())
                    .build());
        }
        dtos.sort(Comparator.comparing(ReportCountDTO::getFinishCount).reversed());
        return ResponseData.success(dtos);
    }

    /**
     * 产线查出信息
     *
     * @param lineModelId
     * @return
     */
    @Override
    public ResponseData getLineProductData(Integer lineModelId) {
        Date recordDate = dictService.getRecordDate(new Date());
        List<LineProductDataDTO> dtos = getFreeDurationByLineId(lineModelId, recordDate);
        List<Integer> lineIds = dtos.stream().map(LineProductDataDTO::getLineId).collect(Collectors.toList());
        //状态
        Map<Integer, Integer> stateMap = getStateByLineId(lineIds);
        int runCount = 0;
        int suspendCount = 0;
        int stopCount = 0;
        for (LineProductDataDTO dto : dtos) {
            Integer state = stateMap.get(dto.getLineId());
            dto.setState(state);
            if (state.equals(ProductionLineStateEnum.PRODUCING.getCode())) {
                runCount++;
            }
            if (state.equals(ProductionLineStateEnum.SUSPEND.getCode())) {
                suspendCount++;
            }
            if (state.equals(ProductionLineStateEnum.UNUSED.getCode())) {
                stopCount++;
            }
        }
        LineModelProductDataDTO build = LineModelProductDataDTO.builder()
                .lineModelId(lineModelId)
                .dtos(dtos)
                .runCount(runCount)
                .suspendCount(suspendCount)
                .stopCount(stopCount)
                .build();
        return ResponseData.success(build);
    }

    /**
     * 获取各产线状态
     *
     * @param lineIds
     * @return
     */
    private Map<Integer, Integer> getStateByLineId(List<Integer> lineIds) {
        Map<Integer, Integer> lineStateCount = new HashMap<>();
        if (CollectionUtils.isEmpty(lineIds)) {
            return lineStateCount;
        }
        //获取所有设备状态
        List<DeviceEntity> deviceEntities = deviceService.lambdaQuery()
                .select(DeviceEntity::getProductionLineId, DeviceEntity::getState)
                .in(DeviceEntity::getProductionLineId, lineIds)
                .list();
        Map<Integer, List<Integer>> stateMap = deviceEntities.stream().collect(Collectors.groupingBy(DeviceEntity::getProductionLineId
                , Collectors.mapping(DeviceEntity::getState, Collectors.toList())));
        for (Integer lineId : lineIds) {
            if (!stateMap.containsKey(lineId)) {
                lineStateCount.put(lineId, ProductionLineStateEnum.PRODUCING.getCode());
                continue;
            }
            List<Integer> states = stateMap.get(lineId);
            if (states.contains(DevicesStateEnum.STOP.getCode())) {
                lineStateCount.put(lineId, ProductionLineStateEnum.UNUSED.getCode());
            } else if (states.contains(DevicesStateEnum.SUSPEND.getCode())) {
                lineStateCount.put(lineId, ProductionLineStateEnum.SUSPEND.getCode());
            } else {
                lineStateCount.put(lineId, ProductionLineStateEnum.PRODUCING.getCode());
            }
        }
        return lineStateCount;
    }


    /**
     * 空闲时长:当天日历时长-已生产时长-当前工单预计剩余时长-待生产的工单的时长
     * 当前所有工单的预计剩余时长:(当前工单的计划数量 -当前工单的已完成数量) * 工单的工序的加工时长,注意单位换算为小时;(工单维度,只统计投产状态生产工单/作业工单)
     * 待生产的工单，该制造单元下:∑(生效的工单计划工时) + ∑((挂起的工单的计划数量 – 已完成数量) *工单工序的加工工时), 注意单位换算为小时;（工单维度;只统计生产工单）
     * 待生产的工单：生效，挂起；已排产的工单
     *
     * @param lineModelId
     * @return
     */
    private List<LineProductDataDTO> getFreeDurationByLineId(Integer lineModelId, Date recordDate) {
        List<ProductionLineEntity> lineEntities = lineService.lambdaQuery()
                .select(ProductionLineEntity::getProductionLineId, ProductionLineEntity::getName)
                .eq(ProductionLineEntity::getModelId, lineModelId)
                .list();
        if (CollectionUtils.isEmpty(lineEntities)) {
            return new ArrayList<>();
        }

        //作业工单<产线id,<作业工单状态,生产工单>>
        Map<Integer, Map<Integer, Set<String>>> lineMap = getAssignmentsMap(lineModelId);
        //完成数求和，计划数求和
        Map<Integer, List<WorkOrderEntity>> planAndFinishCountMap = getPlanAndFinishCountMap(lineModelId);
        //作业工单当天完成数<产线id,完成数>
        Map<Integer, List<OperationDayCountDTO>> dayFinishCountMap = getDayFinishCountOfLine(lineModelId, recordDate);
        //作业工单当天完成个数<产线id,作业工单数>
        Map<Integer, Integer> dayFinishOrderCountMap = getDayFinishOrderCountOfLine(lineModelId, recordDate);
        //生效，挂起工单(完成数-计划数)求和<产线id,(差值求和,工单数统计)>
        Map<Integer, List<WorkOrderEntity>> sumOfPlanAndFinishMap = getSumOfPlanAndFinishByLineId(lineModelId);
        //投产状态的工单生产时长<产线id,[(计划工时,实际工时)]>
        Map<Integer, List<WorkOrderEntity>> actualWorkHourMap = getActualWorkHours(lineModelId);

        Date now = new Date();
        List<LineProductDataDTO> dtos = new ArrayList<>();
        for (ProductionLineEntity lineEntity : lineEntities) {
            Integer lineId = lineEntity.getProductionLineId();
            String lineName = lineEntity.getName();
            //日历时长
            Double totalDuration = calendarService.traceWorkDuration(lineId, ModelEnum.LINE.getType(), recordDate);
            totalDuration = totalDuration == null ? 24 : totalDuration;
            //已生产时长
            Double workDurationUpToNow = calendarService.getWorkDurationUpToNow(lineId, ModelEnum.LINE.getType(), now);

            List<String> releasedOrders = new ArrayList<>();
            List<String> investmentOrders = new ArrayList<>();
            List<String> hangUpOrders = new ArrayList<>();
            if (lineMap.containsKey(lineId)) {
                Map<Integer, Set<String>> map = lineMap.get(lineId);
                //此处用工单的状态去获取作业工单不可取,但作业工单与生产工单的状态枚举相同,先凑合了
                Integer releasedCode = WorkOrderStateEnum.RELEASED.getCode();
                if (map.containsKey(releasedCode)) {
                    Set<String> releasedOrderSet = map.get(releasedCode);
                    releasedOrders = new ArrayList<>(releasedOrderSet);
                }
                Integer investmentCode = WorkOrderStateEnum.INVESTMENT.getCode();
                if (map.containsKey(investmentCode)) {
                    Set<String> investmentOrderSet = map.get(investmentCode);
                    investmentOrders = new ArrayList<>(investmentOrderSet);
                }
                Integer hangUpCode = WorkOrderStateEnum.HANG_UP.getCode();
                if (map.containsKey(hangUpCode)) {
                    Set<String> hangUpOrderSet = map.get(hangUpCode);
                    hangUpOrders = new ArrayList<>(hangUpOrderSet);
                }
            }
            //预计剩余时长
            Double hoursOfInvestment = getWorkOrderProductTime(lineId, investmentOrders, WorkOrderStateEnum.INVESTMENT.getCode());
            //挂起状态的待生产时长
            Double hoursOfHang = getWorkOrderProductTime(lineId, hangUpOrders, WorkOrderStateEnum.HANG_UP.getCode());
            //生效状态的待生产时长:工单计划工时
            //如果工单有作业工单,是不会指定产线的,所以还需要通过产线找到[生效状态的作业工单]->关联的生产工单
            List<WorkOrderEntity> orderEntities = workOrderService.lambdaQuery()
                    .select(WorkOrderEntity::getWorkOrderNumber)
                    .eq(WorkOrderEntity::getLineId, lineId)
                    .eq(WorkOrderEntity::getState, WorkOrderStateEnum.RELEASED.getCode())
                    .eq(WorkOrderEntity::getPlanState, WorkOrderPlanStateEnum.PLANED.getCode())
                    .list();
            List<String> orders = orderEntities.stream().map(WorkOrderEntity::getWorkOrderNumber).collect(Collectors.toList());
            //生产工单+作业工单关联的生产工单
            orders.addAll(releasedOrders);
            //统计工单的计划工时
            Double releasedOrderHours = getWorkOrderPlanWorkHours(orders);
            double freeDuration = totalDuration - workDurationUpToNow - hoursOfInvestment - releasedOrderHours - hoursOfHang;

            //投产状态的计划数求和,完成数求和
            Double totalPlanCount = 0.0;
            Double totalFinishCount = 0.0;
            if (planAndFinishCountMap.containsKey(lineId)) {
                List<WorkOrderEntity> workOrderEntities = planAndFinishCountMap.get(lineId);
                for (WorkOrderEntity entity : workOrderEntities) {
                    totalPlanCount += entity.getPlanQuantity();
                    if (entity.getFinishCount() != null) {
                        totalFinishCount += entity.getFinishCount();
                    }
                    //去掉作业工单关联工单的重复数据
                    investmentOrders.remove(entity.getWorkOrderNumber());
                }
            }
            //[投产状态的作业工单]->关联的生产工单的计划数求和,完成数求和
            WorkOrderEntity orderEntity = getPlanAndFinishCount(investmentOrders);
            if (orderEntity != null) {
                totalPlanCount += orderEntity.getPlanQuantity();
                totalFinishCount += orderEntity.getFinishCount();
            }
            //生产工单的当天完成数,完成单个数
            List<String> excluded = new ArrayList<>();
            //作业工单当天完成数
            Double todayFinishCount = 0.0;
            if (dayFinishCountMap.containsKey(lineId)) {
                List<OperationDayCountDTO> dtoList = dayFinishCountMap.get(lineId);
                for (OperationDayCountDTO dto : dtoList) {
                    Double count = dto.getCount();
                    todayFinishCount += count;
                    excluded.add(dto.getWorkOrderNumber());
                }
            }
            Double finishCount1 = getTodayFinishCount(lineId, recordDate, excluded);
            todayFinishCount += finishCount1;
            //完成单个数
            Long todayFinishOrderCount = getFinishOrderCountByLineId(lineId, recordDate);
            if (dayFinishOrderCountMap.containsKey(lineId)) {
                Integer orderCount = dayFinishOrderCountMap.get(lineId);
                todayFinishOrderCount += orderCount;
            }
            //生效，挂起；已排产的生产工单；计算：计划数求和-完成数求和（不一定是当天）,这些生产工单的个数
            long toBePlanOrderCount = 0L;
            Double toBePlanCount = 0.0;
            releasedOrders.addAll(hangUpOrders);
            if (sumOfPlanAndFinishMap.containsKey(lineId)) {
                List<WorkOrderEntity> workOrderEntities = sumOfPlanAndFinishMap.get(lineId);
                toBePlanOrderCount += workOrderEntities.size();
                for (WorkOrderEntity entity : workOrderEntities) {
                    toBePlanCount += entity.getPlanQuantity();
                    Double finishCount = entity.getFinishCount();
                    if (finishCount != null) {
                        toBePlanCount -= finishCount;
                    }
                    //避免重复
                    releasedOrders.remove(entity.getWorkOrderNumber());
                }
            }
            //作业工单的
            WorkOrderEntity byOrder = getSumOfPlanAndFinishByOrder(releasedOrders);
            if (byOrder != null) {
                Double orderCount1 = byOrder.getPlanQuantity();
                Double quantity1 = byOrder.getFinishCount();
                toBePlanOrderCount += orderCount1.intValue();
                toBePlanCount += quantity1;
            }
            //"延"字：投产状态工单中，存在生产时长>计划时长；就出现这个字
            boolean isDelay = false;
            if (actualWorkHourMap.containsKey(lineId)) {
                List<WorkOrderEntity> workOrderEntities = actualWorkHourMap.get(lineId);
                for (WorkOrderEntity workOrderEntity : workOrderEntities) {
                    Double workingHours = workOrderEntity.getActualWorkingHours();
                    Double planHours = workOrderEntity.getPlannedWorkingHours();
                    if (workingHours == null || planHours == null) {
                        continue;
                    }
                    isDelay = workingHours > planHours;
                    if (isDelay) {
                        break;
                    }
                }
            }
            if (!isDelay) {
                //作业工单关联的生产工单
                List<WorkOrderEntity> workOrderEntities = getActualWorkHourByOrders(investmentOrders);
                for (WorkOrderEntity workOrderEntity : workOrderEntities) {
                    Double planHours = workOrderEntity.getPlannedWorkingHours();
                    Double workingHours = workOrderEntity.getActualWorkingHours();
                    if (workingHours == null || planHours == null) {
                        continue;
                    }
                    isDelay = workingHours > planHours;
                    if (isDelay) {
                        break;
                    }
                }
            }

            //OEE
            RecordLineDayUnionEntity unionEntity = lineDayUnionService.lambdaQuery()
                    .select(RecordLineDayUnionEntity::getOee)
                    .eq(RecordLineDayUnionEntity::getTime, recordDate)
                    .eq(RecordLineDayUnionEntity::getProductionLineId, lineId)
                    .last("limit 1").one();
            double oee = 0.0;
            if (unionEntity != null) {
                oee = unionEntity.getOee();
            }
            toBePlanCount = BigDecimal.valueOf(toBePlanCount).setScale(2, RoundingMode.HALF_UP).doubleValue();
            //计算占比
            //蓝色:已生产时长
            Double workDurationRate = MathUtil.divideDouble(workDurationUpToNow, totalDuration, 2, RoundingMode.HALF_UP);
            //绿色:预计剩余时长
            Double remainingDurationRate = MathUtil.divideDouble(hoursOfInvestment, totalDuration, 2, RoundingMode.HALF_UP);
            //白色:待生产时长
            Double planDurationRate = MathUtil.divideDouble(releasedOrderHours + hoursOfHang, totalDuration, 2, RoundingMode.HALF_UP);
            //黄色:空闲时长
            Double freeDurationRate = MathUtil.divideDouble(freeDuration, totalDuration, 2, RoundingMode.HALF_UP);
            //组装数据
            LineProductDataDTO dto = LineProductDataDTO.builder()
                    .lineId(lineId)
                    //产线名称
                    .lineName(lineName)
                    //oee
                    .oee(oee)
                    //总时长
                    .totalDuration(totalDuration)
                    //生产时长
                    .workDuration(workDurationUpToNow)
                    .workDurationRate(workDurationRate)
                    //预计剩余时长
                    .remainingDuration(hoursOfInvestment)
                    .remainingDurationRate(remainingDurationRate)
                    //待生产时长
                    .planDuration(releasedOrderHours + hoursOfHang)
                    .planDurationRate(planDurationRate)
                    //空闲时长
                    .freeDuration(freeDuration)
                    .freeDurationRate(freeDurationRate)
                    //总完成数
                    .totalFinishCount(totalFinishCount)
                    //总计划数
                    .totalPlanCount(totalPlanCount)
                    //当天完成数
                    .todayFinishCount(todayFinishCount)
                    //当天完成单数
                    .todayFinishOrderCount(todayFinishOrderCount)
                    //待排产数
                    .toBePlanCount(toBePlanCount)
                    //待排产单数
                    .toBePlanOrderCount(toBePlanOrderCount)
                    //是否延迟
                    .isDelay(isDelay)
                    .build();
            dtos.add(dto);
        }
        return dtos;
    }

    private Map<Integer, List<WorkOrderEntity>> getPlanAndFinishCountMap(Integer lineModelId) {
        List<WorkOrderEntity> workOrders = workOrderService.lambdaQuery()
                .select(WorkOrderEntity::getWorkOrderNumber,
                        WorkOrderEntity::getLineId,
                        WorkOrderEntity::getPlanQuantity,
                        WorkOrderEntity::getFinishCount)
                .eq(WorkOrderEntity::getState, WorkOrderStateEnum.INVESTMENT.getCode())
                .eq(WorkOrderEntity::getWorkCenterId, lineModelId)
                .isNotNull(WorkOrderEntity::getLineId)
                .list();
        return workOrders.stream().collect(Collectors.groupingBy(WorkOrderEntity::getLineId));
    }

    /**
     * 统计工单的计划工时
     *
     * @param orders
     * @return
     */
    private Double getWorkOrderPlanWorkHours(List<String> orders) {
        if (CollectionUtils.isEmpty(orders)) {
            return 0.0;
        }
        QueryWrapper<WorkOrderEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("IFNULL(SUM(planned_working_hours),0) AS planned_working_hours ");
        LambdaQueryWrapper<WorkOrderEntity> wrapper = queryWrapper.lambda();
        wrapper.in(WorkOrderEntity::getWorkOrderNumber, orders)
                .eq(WorkOrderEntity::getPlanState, WorkOrderPlanStateEnum.PLANED.getCode());
        WorkOrderEntity one = workOrderService.getOne(wrapper);
        if (one != null) {
            return one.getPlannedWorkingHours();
        }
        return 0.0;
    }

    /**
     * 获取工单的计划工时、实际工时
     *
     * @param investmentOrders
     * @return
     */
    private List<WorkOrderEntity> getActualWorkHourByOrders(List<String> investmentOrders) {
        if (CollectionUtils.isEmpty(investmentOrders)) {
            return new ArrayList<>();
        }
        return workOrderService.lambdaQuery()
                .select(WorkOrderEntity::getPlannedWorkingHours,
                        WorkOrderEntity::getActualWorkingHours)
                .in(WorkOrderEntity::getWorkOrderNumber, investmentOrders)
                .isNull(WorkOrderEntity::getLineId)
                .list();
    }

    /**
     * 投产状态的工单生产时长
     *
     * @param lineModelId
     * @return
     */
    private Map<Integer, List<WorkOrderEntity>> getActualWorkHours(Integer lineModelId) {
        List<WorkOrderEntity> orderEntities = workOrderService.lambdaQuery()
                .select(WorkOrderEntity::getWorkOrderNumber,
                        WorkOrderEntity::getLineId,
                        WorkOrderEntity::getPlannedWorkingHours,
                        WorkOrderEntity::getActualWorkingHours)
                .eq(WorkOrderEntity::getWorkCenterId, lineModelId)
                .eq(WorkOrderEntity::getState, WorkOrderStateEnum.INVESTMENT.getCode())
                .isNotNull(WorkOrderEntity::getLineId)
                .list();
        return orderEntities.stream().collect(Collectors.groupingBy(WorkOrderEntity::getLineId));
    }


    /**
     * 获取产线的生效，挂起工单的完成数-计划数的和
     *
     * @param lineModelId
     * @return
     */
    private Map<Integer, List<WorkOrderEntity>> getSumOfPlanAndFinishByLineId(Integer lineModelId) {
        List<WorkOrderEntity> list = workOrderService.lambdaQuery()
                .select(WorkOrderEntity::getWorkOrderNumber,
                        WorkOrderEntity::getLineId,
                        WorkOrderEntity::getPlanQuantity,
                        WorkOrderEntity::getFinishCount)
                .eq(WorkOrderEntity::getWorkCenterId, lineModelId)
                .eq(WorkOrderEntity::getPlanState, WorkOrderPlanStateEnum.PLANED.getCode())
                .in(WorkOrderEntity::getState, WorkOrderStateEnum.RELEASED.getCode(), WorkOrderStateEnum.HANG_UP.getCode())
                .isNotNull(WorkOrderEntity::getLineId)
                .list();
        return list.stream().collect(Collectors.groupingBy(WorkOrderEntity::getLineId));
    }

    /**
     * 获取工单的完成数-计划数的和
     *
     * @param orders
     * @return
     */
    private WorkOrderEntity getSumOfPlanAndFinishByOrder(List<String> orders) {
        if (CollectionUtils.isEmpty(orders)) {
            return null;
        }
        QueryWrapper<WorkOrderEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("IFNULL(SUM((plan_quantity-finish_count)),0) AS finish_count, "
                + "COUNT(work_order_number) AS plan_quantity");
        LambdaQueryWrapper<WorkOrderEntity> wrapper = queryWrapper.lambda();
        wrapper.in(WorkOrderEntity::getWorkOrderNumber, orders);
        return workOrderService.getOne(wrapper);
    }

    private Map<Integer, Integer> getDayFinishOrderCountOfLine(Integer lineModelId, Date recordDate) {
        //作业工单数
        List<OperationDayCountDTO> assignments = new ArrayList<>();
        try {
            ResponseData responseData = assignmentInterface.getDayFinishOrderCountOfLine(lineModelId, recordDate.getTime());
            assignments = JacksonUtil.getResponseArray(responseData, OperationDayCountDTO.class);
        } catch (Exception e) {
            log.error("请求assignment服务错误", e);
        }
        Map<Integer, Integer> map = assignments.stream().collect(Collectors.toMap(OperationDayCountDTO::getLineId, o -> o.getCount().intValue()));
        return map;
    }

    private Map<Integer, List<OperationDayCountDTO>> getDayFinishCountOfLine(Integer lineModelId, Date recordDate) {
        //作业工单数
        List<OperationDayCountDTO> assignments = new ArrayList<>();
        try {
            ResponseData responseData = assignmentInterface.getDayFinishCountOfLine(lineModelId, recordDate.getTime());
            assignments = JacksonUtil.getResponseArray(responseData, OperationDayCountDTO.class);
        } catch (Exception e) {
            log.error("请求assignment服务错误", e);
        }
        Map<Integer, List<OperationDayCountDTO>> map = assignments.stream().collect(Collectors.groupingBy(OperationDayCountDTO::getLineId));
        return map;
    }

    private Long getFinishOrderCountByLineId(Integer lineId, Date recordDate) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(recordDate);
        calendar.add(Calendar.DATE, 1);
        calendar.add(Calendar.SECOND, -1);
        Date endTime = calendar.getTime();
        Long count1 = workOrderService.lambdaQuery()
                .eq(WorkOrderEntity::getLineId, lineId)
                .between(WorkOrderEntity::getActualEndDate, recordDate, endTime)
                .count();
        return count1;
    }

    /**
     * 获取作业工单关联的生产工单
     *
     * @param lineModelId
     * @return
     */
    private Map<Integer, Map<Integer, Set<String>>> getAssignmentsMap(Integer lineModelId) {
        List<OperationOrderStateDTO> assignments = new ArrayList<>();
        try {
            ResponseData responseData = assignmentInterface.getStatesOrder(lineModelId);
            assignments = JacksonUtil.getResponseArray(responseData, OperationOrderStateDTO.class);
        } catch (Exception e) {
            log.error("请求assignment服务错误", e);
        }
        //根据产线、作业工单状态分组生产订单
        return assignments.stream().filter(o -> o.getLineId() != null)
                .collect(Collectors.groupingBy(OperationOrderStateDTO::getLineId,
                        Collectors.groupingBy(OperationOrderStateDTO::getState,
                                Collectors.mapping(OperationOrderStateDTO::getWorkOrderNumber, Collectors.toSet()))));
    }


    /**
     * 获取生产工单的计划数求和,完成数求和
     *
     * @param workOrders
     * @return
     */
    private WorkOrderEntity getPlanAndFinishCount(List<String> workOrders) {
        if (CollectionUtils.isEmpty(workOrders)) {
            return null;
        }
        QueryWrapper<WorkOrderEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("IFNULL(sum(plan_quantity),0) AS plan_quantity, " +
                "IFNULL(sum(finish_count),0) AS finish_count");
        LambdaQueryWrapper<WorkOrderEntity> wrapper = queryWrapper.lambda();
        wrapper.in(WorkOrderEntity::getWorkOrderNumber, workOrders);
        return workOrderService.getOne(wrapper);
    }

    /**
     * 当天完成数
     *
     * @param lineId
     * @param recordDate
     * @return
     */
    private Double getTodayFinishCount(Integer lineId, Date recordDate, List<String> excluded) {
        //工单的完成数
        QueryWrapper<RecordWorkOrderLineDayCountEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("IFNULL(sum(count),0) AS count");
        LambdaQueryWrapper<RecordWorkOrderLineDayCountEntity> wrapper = queryWrapper.lambda();
        wrapper.eq(RecordWorkOrderLineDayCountEntity::getLineId, lineId)
                .eq(RecordWorkOrderLineDayCountEntity::getTime, recordDate)
                .notIn(!CollectionUtils.isEmpty(excluded), RecordWorkOrderLineDayCountEntity::getWorkOrderNumber, excluded);
        RecordWorkOrderLineDayCountEntity one = recordWorkOrderLineDayCountService.getOne(wrapper);
        if (one != null) {
            return one.getCount();
        }
        return 0.0;
    }


    /**
     * 上工序流转超时
     * <p>
     * 1.工单的订单号
     * 2.上工序名称
     * 3.当前工单流转时长
     * 4.上工序工单完成数
     * 5.当前工单数
     * <p>
     * 有流转时长的工单理应是有上工序的，所以流转超时一般都是有上工序
     *
     * @param lineModelId
     * @return
     */
    @Override
    public ResponseData getPreviousProcedureOvertimeData(Integer lineModelId) {
        //查找可排产、可流转工单
        List<WorkOrderEntity> workOrderEntities = workOrderService.lambdaQuery()
                .select(WorkOrderEntity::getWorkOrderId,
                        WorkOrderEntity::getWorkOrderNumber,
                        WorkOrderEntity::getCirculationDuration)
                .eq(WorkOrderEntity::getWorkCenterId, lineModelId)
                .eq(WorkOrderEntity::getPlanState, WorkOrderPlanStateEnum.ENABE.getCode())
                .eq(WorkOrderEntity::getCirculationState, WorkOrderCirculationStateEnum.OVER.getCode())
                .list();
        if (CollectionUtils.isEmpty(workOrderEntities)) {
            return ResponseData.success(PreviousProcedureDTO.builder().lineModelId(lineModelId)
                    .finishCount(0.0)
                    .orderCount(0)
                    .dtos(new ArrayList<>())
                    .build());
        }
        List<Integer> workOrderIds = workOrderEntities.stream().map(WorkOrderEntity::getWorkOrderId).collect(Collectors.toList());
        //订单与工单关联表
        Map<Integer, Integer> orderMap = getOrderWorkOrderMap(workOrderIds);
        //通过工单查找同一个工序组的工艺工序
        Map<String, WorkOrderEntity> workOrderMap = workOrderEntities.stream().collect(Collectors.toMap(WorkOrderEntity::getWorkOrderNumber, o -> o));
        List<String> workOrderNumbers = new ArrayList<>(workOrderMap.keySet());
        //查找工单的上级工序<工单号，上工艺工序id>
        HashMap<String, Integer> map = getPreviousProcedureMap(workOrderNumbers);
        //工单的上工序找到了->map
        //找出上工序名称，上工序工单完成数
        ArrayList<ProcedureDTO> dtos = new ArrayList<>();
        Double finishCount = 0.0;
        if (!CollectionUtils.isEmpty(map)) {
            //查询上工序的生产工单
            List<Integer> collect = map.values().stream().filter(Objects::nonNull).collect(Collectors.toList());
            //<上工序,[生产工单]>
            Map<Integer, List<WorkOrderProcedureRelationEntity>> map1 = new HashMap<>();
            List<WorkOrderProcedureRelationEntity> list = new ArrayList<>();
            if (!CollectionUtils.isEmpty(collect)) {
                list = procedureRelationService.lambdaQuery()
                        .in(WorkOrderProcedureRelationEntity::getCraftProcedureId, collect)
                        .list();
                map1 = list.stream().collect(Collectors.groupingBy(WorkOrderProcedureRelationEntity::getCraftProcedureId));
            }
            Set<Map.Entry<String, Integer>> entries = map.entrySet();
            for (Map.Entry<String, Integer> entry : entries) {
                String workOrderNumber = entry.getKey();
                Integer preCpId = entry.getValue();
                //上工序无工单
                if (preCpId == null || !map1.containsKey(preCpId)) {
                    continue;
                }
                //上工序的名称、工单
                List<WorkOrderProcedureRelationEntity> entities = map1.get(preCpId);
                //流转时长
                WorkOrderEntity entity = workOrderMap.get(workOrderNumber);
                Double circulationDuration = entity.getCirculationDuration();
                //订单号
                Integer orderId = orderMap.get(entity.getWorkOrderId());
                String orderNumber = getOrderNumberById(orderId);
                for (WorkOrderProcedureRelationEntity relationEntity : entities) {
                    Integer workOrderId = relationEntity.getWorkOrderId();
                    //上工序工单要与当前工单为同一个生产订单
                    OrderWorkOrderEntity one = orderWorkOrderService.lambdaQuery()
                            .eq(OrderWorkOrderEntity::getOrderId, orderId)
                            .eq(OrderWorkOrderEntity::getWorkOrderId, workOrderId)
                            .one();
                    if (one == null) {
                        continue;
                    }
                    dtos.add(ProcedureDTO.builder()
                            .orderNumber(orderNumber)
                            //上级工艺工序名称
                            .procedureName(relationEntity.getCraftProcedureName())
                            .duration(circulationDuration)
                            .build());
                }
            }
            //上工序工单完成数
            List<Integer> workIds = list.stream().map(WorkOrderProcedureRelationEntity::getWorkOrderId).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(workIds)) {
                QueryWrapper<WorkOrderEntity> wrapper = new QueryWrapper<>();
                wrapper.select("IFNULL(sum(finish_count),0) AS finish_count");
                wrapper.lambda().in(WorkOrderEntity::getWorkOrderId, workIds);
                WorkOrderEntity workOrderEntity = workOrderService.getOne(wrapper);
                if (workOrderEntity != null) {
                    finishCount = workOrderEntity.getFinishCount();
                }
            }
        }
        PreviousProcedureDTO build = PreviousProcedureDTO.builder()
                .lineModelId(lineModelId)
                .finishCount(finishCount)
                .orderCount(dtos.size())
                .dtos(dtos)
                .build();
        return ResponseData.success(build);
    }

    /**
     * 获取订单号
     *
     * @param orderId
     * @return
     */
    private String getOrderNumberById(Integer orderId) {
        String orderNumber = null;
        if (orderId == null) {
            return null;
        }
        try {
            ProductOrderEntity orderEntity = extProductOrderInterface.selectProductOrderById(orderId);
            if (orderEntity != null) {
                orderNumber = orderEntity.getProductOrderNumber();
            }
        } catch (Exception e) {
            log.error("访问ams模块错误", e);
        }
        return orderNumber;
    }

    /**
     * 排序
     *
     * @param dtos
     * @return
     */
    private List<Integer> sortCraftProcedures(List<CraftProcedureWorkOrderDTO> dtos) {
        Map<Integer, CraftProcedureWorkOrderDTO> dtoMap = dtos.stream()
                .collect(Collectors.toMap(CraftProcedureWorkOrderDTO::getId, o -> o));
        CraftProcedureWorkOrderDTO head = null;
        for (CraftProcedureWorkOrderDTO dto : dtos) {
            String id = dto.getSupProcedureId();
            if (StringUtils.isNotBlank(id) && dtoMap.containsKey(Integer.valueOf(id))) {
                //存在父级
                CraftProcedureWorkOrderDTO parent = dtoMap.get(Integer.valueOf(id));
                parent.setChild(dto);
                continue;
            }
            //不存在父级,头部
            head = dto;
        }
        List<Integer> cpIds = new ArrayList<>();
        if (head != null) {
            addToList(head, cpIds);
        }
        Collections.reverse(cpIds);
        return cpIds;
    }

    private void addToList(CraftProcedureWorkOrderDTO head, List<Integer> cpIds) {
        if (head.getChild() != null) {
            addToList(head.getChild(), cpIds);
        }
        cpIds.add(head.getId());
    }


    /**
     * 上工序流转未超时
     * <p>
     * 1.工单的订单号
     * 2.上工序名称
     * 3.上工序工单完成数
     * 4.上工序工单完成数求和
     * 5.当前工单数
     *
     * @param lineModelId
     * @return
     */
    @Override
    public ResponseData getPreviousProcedureNormalData(Integer lineModelId) {
        List<WorkOrderEntity> workOrderEntities = workOrderService.lambdaQuery()
                .select(WorkOrderEntity::getWorkOrderId,
                        WorkOrderEntity::getWorkOrderNumber,
                        WorkOrderEntity::getCirculationDuration)
                .eq(WorkOrderEntity::getWorkCenterId, lineModelId)
                .eq(WorkOrderEntity::getPlanState, WorkOrderPlanStateEnum.ENABE.getCode())
                .eq(WorkOrderEntity::getCirculationState, WorkOrderCirculationStateEnum.ENABE.getCode())
                .list();
        if (CollectionUtils.isEmpty(workOrderEntities)) {
            return ResponseData.success(PreviousProcedureDTO.builder().lineModelId(lineModelId)
                    .finishCount(0.0)
                    .orderCount(0)
                    .dtos(new ArrayList<>())
                    .build());
        }
        List<Integer> workOrderIds = workOrderEntities.stream().map(WorkOrderEntity::getWorkOrderId).collect(Collectors.toList());
        //订单与工单关联表
        Map<Integer, Integer> orderMap = getOrderWorkOrderMap(workOrderIds);
        //通过工单查找同一个工序组的工艺工序
        Map<String, WorkOrderEntity> workOrderMap = workOrderEntities.stream().collect(Collectors.toMap(WorkOrderEntity::getWorkOrderNumber, o -> o));
        List<String> workOrderNumbers = new ArrayList<>(workOrderMap.keySet());
        //查找工单的上级工序<工单号，上工艺工序id>
        HashMap<String, Integer> map = getPreviousProcedureMap(workOrderNumbers);
        //找出上工序名称，上工序工单完成数
        List<ProcedureDTO> dtos = new ArrayList<>();
        Double finishCount = 0.0;
        if (!CollectionUtils.isEmpty(map)) {
            //上工序与工单的关联关系
            List<Integer> collect = map.values().stream().filter(Objects::nonNull).collect(Collectors.toList());
            Map<Integer, List<WorkOrderProcedureRelationEntity>> map1 = new HashMap<>();
            if (!CollectionUtils.isEmpty(collect)) {
                List<WorkOrderProcedureRelationEntity> list = procedureRelationService.lambdaQuery()
                        .in(WorkOrderProcedureRelationEntity::getCraftProcedureId, collect)
                        .list();
                map1 = list.stream().collect(Collectors.groupingBy(WorkOrderProcedureRelationEntity::getCraftProcedureId));
            }
            //遍历当前所有工单的上工序
            Set<Map.Entry<String, Integer>> entries = map.entrySet();
            for (Map.Entry<String, Integer> entry : entries) {
                String workOrderNumber = entry.getKey();
                Integer preCpId = entry.getValue();
                //订单号
                WorkOrderEntity entity = workOrderMap.get(workOrderNumber);
                Integer orderId = orderMap.get(entity.getWorkOrderId());
                String orderNumber = getOrderNumberById(orderId);
                //没有上工序的话，工序名称,流转数量(完成数)留空
                if (preCpId == null) {
                    dtos.add(ProcedureDTO.builder()
                            .orderNumber(orderNumber)
                            .build());
                    continue;
                }
                //上工序无工单
                if (!map1.containsKey(preCpId)) {
                    continue;
                }
                //上工序的名称、工单
                List<WorkOrderProcedureRelationEntity> entities = map1.get(preCpId);
                //上工序工单完成数
                for (WorkOrderProcedureRelationEntity relationEntity : entities) {
                    Integer workOrderId = relationEntity.getWorkOrderId();
                    //上工序工单要与当前工单为同一个生产订单
                    OrderWorkOrderEntity one = orderWorkOrderService.lambdaQuery()
                            .eq(OrderWorkOrderEntity::getOrderId, orderId)
                            .eq(OrderWorkOrderEntity::getWorkOrderId, workOrderId)
                            .one();
                    if (one == null) {
                        continue;
                    }

                    String perWorkNumber = relationEntity.getWorkOrderNumber();
                    WorkOrderEntity workOrderEntity = workOrderService.getSimpleWorkOrderByNumber(perWorkNumber);
                    Double entityFinishCount = 0.0;
                    if (workOrderEntity != null) {
                        entityFinishCount = workOrderEntity.getFinishCount();
                    }
                    dtos.add(ProcedureDTO.builder()
                            .orderNumber(orderNumber)
                            //上级工艺工序名称
                            .procedureName(relationEntity.getCraftProcedureName())
                            .finishCount(entityFinishCount)
                            .build());
                    finishCount += entityFinishCount;
                }
            }
        }
        PreviousProcedureDTO build = PreviousProcedureDTO.builder()
                .lineModelId(lineModelId)
                .finishCount(finishCount)
                .orderCount(dtos.size())
                .dtos(dtos)
                .build();
        return ResponseData.success(build);
    }

    /**
     * 获取上工序map<工单号，上工艺工序id>
     *
     * @param workOrderNumbers
     * @return
     */
    private HashMap<String, Integer> getPreviousProcedureMap(List<String> workOrderNumbers) {
        //通过工单号获取工序组
        List<CraftProcedureWorkOrderDTO> craftProcedures = craftProcedureService.getCraftProcedureGroupByWorkOrder(workOrderNumbers);
        //按工单分组
        Map<String, List<CraftProcedureWorkOrderDTO>> workOrderCpGroupMap = craftProcedures.stream().collect(Collectors.groupingBy(CraftProcedureWorkOrderDTO::getWorkOrderNumber));
        //获取工单与工艺工序的关联关系<工单号，[工艺工序id]>
        Map<String, List<Integer>> workOrderCpMap = getWorkOrderCpMap(workOrderNumbers);
        //一个工单绑定的多个工序都是同一组,同一个组的工序都是连续的
        HashMap<String, Integer> map = new HashMap<>();
        for (String workOrderNumber : workOrderNumbers) {
            //工单关联的工艺工序id
            if (!workOrderCpMap.containsKey(workOrderNumber)) {
                continue;
            }
            List<Integer> cpIds = workOrderCpMap.get(workOrderNumber);
            List<CraftProcedureWorkOrderDTO> dtos = workOrderCpGroupMap.get(workOrderNumber);
            //1.对工序组排序
            List<Integer> dtoList = sortCraftProcedures(dtos);
            Map<Integer, Integer> indexMap = dtoList.stream().collect(Collectors.toMap(o -> o, dtoList::indexOf));
            //工单的上工序
            cpIds.sort(Comparator.comparingInt(dtoList::indexOf));
            Integer cpId = cpIds.get(0);
            Integer index = indexMap.get(cpId);
            //如果不是第一个,则返回前一个
            if (index != 0) {
                Integer preCpId = dtoList.get(index - 1);
                map.put(workOrderNumber, preCpId);
                continue;
            }
            //如果是第一个，则需要找上一级工序
            CraftProcedureEntity craftProcedureEntity = craftProcedureService.getById(cpId);
            if (craftProcedureEntity != null) {
                if (StringUtils.isNumeric(craftProcedureEntity.getSupProcedureId())) {
                    map.put(workOrderNumber, Integer.valueOf(craftProcedureEntity.getSupProcedureId()));
                    continue;
                }
                //上级工序为空，工序名称留空
                map.put(workOrderNumber, null);
            }
            //如果没有找到工艺工序，则不处理，因为有流转时长的工单理应是有上工序的，所以流转超时一般都是有上工序
        }
        return map;
    }

    /**
     * 获取工单与工艺工序的关联关系<工单号，[工艺工序id]>
     *
     * @param workOrderNumbers
     * @return
     */
    private Map<String, List<Integer>> getWorkOrderCpMap(List<String> workOrderNumbers) {
        //找出工单与工艺工序的关联关系
        List<WorkOrderProcedureRelationEntity> relationEntities = procedureRelationService.lambdaQuery()
                .select(WorkOrderProcedureRelationEntity::getWorkOrderNumber,
                        WorkOrderProcedureRelationEntity::getCraftProcedureId)
                .in(WorkOrderProcedureRelationEntity::getWorkOrderNumber, workOrderNumbers)
                .list();
        return relationEntities.stream().collect(Collectors
                .groupingBy(WorkOrderProcedureRelationEntity::getWorkOrderNumber,
                        Collectors.mapping(WorkOrderProcedureRelationEntity::getCraftProcedureId, Collectors.toList())));
    }

    /**
     * 订单与工单关联表
     *
     * @param workOrderIds
     * @return
     */
    private Map<Integer, Integer> getOrderWorkOrderMap(List<Integer> workOrderIds) {
        //订单与工单关联表
        List<OrderWorkOrderEntity> orderWorkOrders = orderWorkOrderService.lambdaQuery()
                .in(OrderWorkOrderEntity::getWorkOrderId, workOrderIds)
                .eq(OrderWorkOrderEntity::getOrderType, OrderNumTypeEnum.PRODUCT_ORDER.getTypeCode())
                .list();
        //一个工单只有一个生产订单
        return orderWorkOrders.stream()
                .collect(Collectors.toMap(OrderWorkOrderEntity::getWorkOrderId, OrderWorkOrderEntity::getOrderId));
    }


    /**
     * 下工序流转超时
     * <p>
     * 1.已排产工单的订单号；
     * 2.下工序工单工序名称；
     * 3.下工序工单的流转时长(有多个下工序工单时，取时间最短的)
     * 右上角：已排产工单完成数求和；
     * 左上角：已排产工单个数
     *
     * @param lineModelId
     * @return
     */
    @Override
    public ResponseData getNextProcedureOvertimeData(Integer lineModelId) {
        List<WorkOrderEntity> workOrderEntities = workOrderService.lambdaQuery()
                .select(WorkOrderEntity::getWorkOrderId,
                        WorkOrderEntity::getWorkOrderNumber,
                        WorkOrderEntity::getFinishCount)
                .eq(WorkOrderEntity::getWorkCenterId, lineModelId)
                .eq(WorkOrderEntity::getPlanState, WorkOrderPlanStateEnum.PLANED.getCode())
                .list();
        if (CollectionUtils.isEmpty(workOrderEntities)) {
            return ResponseData.success(PreviousProcedureDTO.builder().lineModelId(lineModelId)
                    .finishCount(0.0)
                    .orderCount(0)
                    .dtos(new ArrayList<>())
                    .build());
        }
        Map<String, WorkOrderEntity> orderEntityMap = workOrderEntities.stream().collect(Collectors.toMap(WorkOrderEntity::getWorkOrderNumber, o -> o));
        //获取工单的下工序
        HashMap<String, List<Integer>> nextProcedureMap = getNextProcedureMap(new ArrayList<>(orderEntityMap.keySet()));
        //找出下工序的工单，用于筛选当前的工单
        ArrayList<String> workOrders = new ArrayList<>();
        if (!CollectionUtils.isEmpty(nextProcedureMap)) {
            Set<Map.Entry<String, List<Integer>>> entries = nextProcedureMap.entrySet();
            for (Map.Entry<String, List<Integer>> entry : entries) {
                //下工序
                List<Integer> nextCpIds = entry.getValue();
                if (CollectionUtils.isEmpty(nextCpIds)) {
                    continue;
                }
                //通过下工序获取流转超时的工单
                Integer count = procedureRelationService.getWorkOrderCountByCirculationState(nextCpIds, WorkOrderCirculationStateEnum.OVER.getCode());
                if (count != null && count > 0) {
                    String workOrderNumber = entry.getKey();
                    workOrders.add(workOrderNumber);
                }
            }
        }
        //过滤后符合超时的工单
        List<ProcedureDTO> dtos = new ArrayList<>();
        Double finishCount = 0.0;
        for (String workOrderNumber : workOrders) {
            WorkOrderEntity workOrderEntity = orderEntityMap.get(workOrderNumber);
            //当前工单完成数
            Double entityFinishCount = workOrderEntity.getFinishCount();
            if (entityFinishCount != null) {
                finishCount += entityFinishCount;
            }
            //订单与工单关联表
            //一个工单只有一个生产订单
            //OrderWorkOrderEntity orderWorkOrder = orderWorkOrderService.lambdaQuery()
            //        .eq(OrderWorkOrderEntity::getWorkOrderId, workOrderEntity.getWorkOrderId())
            //        .eq(OrderWorkOrderEntity::getOrderType, OrderNumTypeEnum.PRODUCT_ORDER.getTypeCode())
            //        .one();
            //订单号
            List<ProductOrderEntity> productOrderEntities = orderWorkOrderService.productOrderListByWorkId(workOrderEntity);
            String orderNumber = null;
            Integer orderId = null;
            if (!CollectionUtils.isEmpty(productOrderEntities)) {
                ProductOrderEntity one = productOrderEntities.get(0);
                orderNumber = one.getProductOrderNumber();
                orderId = one.getProductOrderId();
            }

            List<Integer> nextCpIds = nextProcedureMap.get(workOrderNumber);
            for (Integer nextCpId : nextCpIds) {
                //获取下工序最短流转时长
                WorkOrderEntity minCirculationDuration = procedureRelationService.getMinCirculationDuration(nextCpId, orderId);
                Double circulationDuration = minCirculationDuration.getCirculationDuration();
                //下工序名称
                String procedureName = minCirculationDuration.getProcedureName();
                dtos.add(ProcedureDTO.builder()
                        .duration(circulationDuration)
                        .procedureName(procedureName)
                        .orderNumber(orderNumber)
                        .build());
            }
        }
        PreviousProcedureDTO build = PreviousProcedureDTO.builder()
                .lineModelId(lineModelId)
                .orderCount(workOrders.size())
                .finishCount(finishCount)
                .dtos(dtos)
                .build();
        return ResponseData.success(build);
    }

    /**
     * 获取工单的下工序
     *
     * @param workOrderNumbers
     * @return
     */
    private HashMap<String, List<Integer>> getNextProcedureMap(List<String> workOrderNumbers) {
        //获取同组工艺工序
        List<CraftProcedureWorkOrderDTO> craftProcedures = craftProcedureService.getCraftProcedureGroupByWorkOrder(workOrderNumbers);
        //按工单分组
        //一个工单绑定的多个工序都是同一组,同一个组的工序都是连续的
        Map<String, List<CraftProcedureWorkOrderDTO>> cpWorkOrderMap = craftProcedures.stream().collect(Collectors.groupingBy(CraftProcedureWorkOrderDTO::getWorkOrderNumber));
        //找出工单与工艺工序的关联关系
        Map<String, List<Integer>> listMap = getWorkOrderCpMap(workOrderNumbers);
        HashMap<String, List<Integer>> map = new HashMap<>();
        for (String workOrderNumber : workOrderNumbers) {
            //工单关联的工艺工序id
            List<Integer> cpIds = listMap.get(workOrderNumber);
            List<CraftProcedureWorkOrderDTO> dtos = cpWorkOrderMap.get(workOrderNumber);
            //1.对工序组排序
            List<Integer> dtoList = sortCraftProcedures(dtos);
            Map<Integer, Integer> indexMap = dtoList.stream().collect(Collectors.toMap(o -> o, dtoList::indexOf));
            //工单的上工序
            cpIds.sort(Comparator.comparingInt(dtoList::indexOf));
            Integer last = cpIds.get(cpIds.size() - 1);
            Integer index = indexMap.get(last);
            //如果不是最后一个,则返回后一个
            if (index != dtoList.size() - 1) {
                Integer nextCpId = dtoList.get(index + 1);
                map.put(workOrderNumber, Collections.singletonList(nextCpId));
                continue;
            }
            //如果是最后一个，则需要找下一级工序，可能会有多个下工序
            List<CraftProcedureEntity> list = craftProcedureService.lambdaQuery()
                    .eq(CraftProcedureEntity::getSupProcedureId, last)
                    .list();
            List<Integer> nextCpIds = list.stream().map(CraftProcedureEntity::getId).collect(Collectors.toList());
            //如果没有找到下级工艺工序，则不处理
            if (!CollectionUtils.isEmpty(nextCpIds)) {
                map.put(workOrderNumber, nextCpIds);
            }
        }
        return map;
    }

    /**
     * 下工序未超时
     * <p>
     * 1.已排产工单的订单号；
     * 2.下工序工单工序名称；
     * 3.本工单完成数量（只取生产工单）
     * 右上角：已排产工单完成数求和；
     * 左上角：已排产工单个数
     *
     * @param lineModelId
     * @return
     */
    @Override
    public ResponseData getNextProcedureNormalData(Integer lineModelId) {
        List<WorkOrderEntity> workOrderEntities = workOrderService.lambdaQuery()
                .select(WorkOrderEntity::getWorkOrderId,
                        WorkOrderEntity::getWorkOrderNumber,
                        WorkOrderEntity::getFinishCount)
                .eq(WorkOrderEntity::getWorkCenterId, lineModelId)
                .eq(WorkOrderEntity::getPlanState, WorkOrderPlanStateEnum.PLANED.getCode())
                .list();
        if (CollectionUtils.isEmpty(workOrderEntities)) {
            return ResponseData.success(PreviousProcedureDTO.builder().lineModelId(lineModelId)
                    .finishCount(0.0)
                    .orderCount(0)
                    .dtos(new ArrayList<>())
                    .build());
        }
        Map<String, WorkOrderEntity> orderEntityMap = workOrderEntities.stream().collect(Collectors.toMap(WorkOrderEntity::getWorkOrderNumber, o -> o));
        //获取工单的下工序
        HashMap<String, List<Integer>> nextProcedureMap = getNextProcedureMap(new ArrayList<>(orderEntityMap.keySet()));
        //找出下工序的工单，用于筛选当前的工单
        List<String> workOrders = new ArrayList<>();
        if (!CollectionUtils.isEmpty(nextProcedureMap)) {
            Set<Map.Entry<String, List<Integer>>> entries = nextProcedureMap.entrySet();
            for (Map.Entry<String, List<Integer>> entry : entries) {
                //下工序
                List<Integer> nextCpIds = entry.getValue();
                if (CollectionUtils.isEmpty(nextCpIds)) {
                    continue;
                }
                //通过下工序获取流转未超时的工单
                Integer count = procedureRelationService.getWorkOrderCountByCirculationState(nextCpIds, WorkOrderCirculationStateEnum.ENABE.getCode());
                if (count != null && count > 0) {
                    String workOrderNumber = entry.getKey();
                    workOrders.add(workOrderNumber);
                }
            }
        }
        //过滤后符合未超时的工单
        List<ProcedureDTO> dtos = new ArrayList<>();
        Double finishCount = 0.0;
        for (String workOrderNumber : workOrders) {
            WorkOrderEntity workOrderEntity = orderEntityMap.get(workOrderNumber);
            //当前工单完成数
            Double entityFinishCount = workOrderEntity.getFinishCount();
            if (entityFinishCount != null) {
                finishCount += entityFinishCount;
            }
            //订单与工单关联表
            //一个工单只有一个生产订单
            //OrderWorkOrderEntity orderWorkOrder = orderWorkOrderService.lambdaQuery()
            //        .eq(OrderWorkOrderEntity::getWorkOrderId, workOrderEntity.getWorkOrderId())
            //        .eq(OrderWorkOrderEntity::getOrderType, OrderNumTypeEnum.PRODUCT_ORDER.getTypeCode())
            //        .one();
            //订单号
            List<ProductOrderEntity> productOrderEntities = orderWorkOrderService.productOrderListByWorkId(workOrderEntity);
            String orderNumber = null;
            if (!CollectionUtils.isEmpty(productOrderEntities)) {
                ProductOrderEntity one = productOrderEntities.get(0);
                orderNumber = one.getProductOrderNumber();
            }
            List<Integer> nextCpIds = nextProcedureMap.get(workOrderNumber);
            for (Integer nextCpId : nextCpIds) {
                CraftProcedureEntity procedureEntity = craftProcedureService.lambdaQuery()
                        .select(CraftProcedureEntity::getProcedureName)
                        .eq(CraftProcedureEntity::getId, nextCpId)
                        .one();
                if (procedureEntity != null) {
                    //下工序名称
                    String procedureName = procedureEntity.getProcedureName();
                    dtos.add(ProcedureDTO.builder()
                            .finishCount(entityFinishCount)
                            .procedureName(procedureName)
                            .orderNumber(orderNumber)
                            .build());
                }
            }
        }
        PreviousProcedureDTO build = PreviousProcedureDTO.builder()
                .lineModelId(lineModelId)
                .orderCount(workOrders.size())
                .finishCount(finishCount)
                .dtos(dtos)
                .build();
        return ResponseData.success(build);
    }

    private String getProductStatus(List<WorkOrderEntity> list) {
        if (CollectionUtils.isEmpty(list)) {
            return WorkOrderStateEnum.RELEASED.getName();
        }
        List<Integer> states = list.stream().map(WorkOrderEntity::getState).distinct().collect(Collectors.toList());
        if (states.contains(WorkOrderStateEnum.INVESTMENT.getCode())) {
            return WorkOrderStateEnum.INVESTMENT.getName();
        }
        if (states.contains(WorkOrderStateEnum.HANG_UP.getCode())) {
            return WorkOrderStateEnum.HANG_UP.getName();
        }
        if (states.contains(WorkOrderStateEnum.FINISHED.getCode())
                || states.contains(WorkOrderStateEnum.CLOSED.getCode())) {
            return WorkOrderStateEnum.FINISHED.getName();
        }
        if (states.contains(WorkOrderStateEnum.RELEASED.getCode())) {
            return WorkOrderStateEnum.RELEASED.getName();
        }
        return WorkOrderStateEnum.RELEASED.getName();
    }


}

