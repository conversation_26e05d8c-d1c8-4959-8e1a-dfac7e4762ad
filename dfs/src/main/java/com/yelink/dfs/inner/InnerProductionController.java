package com.yelink.dfs.inner;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yelink.dfs.constant.screen.TimeRangeEnum;
import com.yelink.dfs.entity.device.DeviceEntity;
import com.yelink.dfs.entity.manufacture.ProductionLineEntity;
import com.yelink.dfs.entity.model.FacilitiesEntity;
import com.yelink.dfs.entity.model.GridEntity;
import com.yelink.dfs.entity.order.RecordWorkOrderDayCountEntity;
import com.yelink.dfs.entity.order.RecordWorkOrderLineDayCountEntity;
import com.yelink.dfs.entity.order.RecordWorkOrderUnqualifiedEntity;
import com.yelink.dfs.entity.order.WorkOrderEntity;
import com.yelink.dfs.entity.order.WorkOrderPlanEntity;
import com.yelink.dfs.entity.product.MaterialEntity;
import com.yelink.dfs.entity.screen.dto.DaysOutputDTO;
import com.yelink.dfs.entity.screen.dto.DeviceStopDTO;
import com.yelink.dfs.entity.screen.dto.FacilitiesBeatDTO;
import com.yelink.dfs.entity.screen.dto.GridDayDataDTO;
import com.yelink.dfs.entity.screen.dto.GridOutputDTO;
import com.yelink.dfs.entity.screen.dto.LineDayDataDTO;
import com.yelink.dfs.entity.screen.dto.LineDeviceFaultDTO;
import com.yelink.dfs.entity.screen.dto.LineFaultDTO;
import com.yelink.dfs.entity.screen.dto.LineOutputDTO;
import com.yelink.dfs.entity.screen.dto.MaterialOutputDTO;
import com.yelink.dfs.entity.screen.dto.UnqualifiedCountDTO;
import com.yelink.dfs.entity.screen.dto.WorkOrderCountDTO;
import com.yelink.dfs.entity.target.record.RecordLineDayUnionEntity;
import com.yelink.dfs.mapper.product.MaterialMapper;
import com.yelink.dfs.service.common.DictService;
import com.yelink.dfs.service.device.DeviceService;
import com.yelink.dfs.service.manufacture.ProductionLineService;
import com.yelink.dfs.service.model.FacilitiesService;
import com.yelink.dfs.service.model.GridService;
import com.yelink.dfs.service.order.RecordWorkOrderDayCountService;
import com.yelink.dfs.service.order.RecordWorkOrderLineDayCountService;
import com.yelink.dfs.service.order.RecordWorkOrderUnqualifiedService;
import com.yelink.dfs.service.order.WorkOrderPlanService;
import com.yelink.dfs.service.order.WorkOrderService;
import com.yelink.dfs.service.product.MaterialService;
import com.yelink.dfs.service.screen.ProductionService;
import com.yelink.dfs.service.screen.RecordOutputService;
import com.yelink.dfs.service.work.calendar.WorkCalendarService;
import com.yelink.dfscommon.api.dfs.ProductionInterface;
import com.yelink.dfscommon.api.maintain.MaintenanceInterface;
import com.yelink.dfscommon.constant.Constants;
import com.yelink.dfscommon.constant.ModelEnum;
import com.yelink.dfscommon.constant.dfs.order.WorkOrderStateEnum;
import com.yelink.dfscommon.dto.ProductionReqDTO;
import com.yelink.dfscommon.dto.YieldRecordVO;
import com.yelink.dfscommon.entity.RecordOutputEntity;
import com.yelink.dfscommon.pojo.ResponseData;
import com.yelink.dfscommon.utils.DateUtil;
import com.yelink.dfscommon.utils.JacksonUtil;
import com.yelink.dfscommon.utils.MathUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.RestController;

import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @description: 产量接口
 * @time 2021/7/9 12:01
 */
@Slf4j
@AllArgsConstructor
@RestController
public class InnerProductionController implements ProductionInterface {

    private RecordOutputService recordOutputService;
    private ProductionService productionService;
    private GridService gridService;
    private ProductionLineService lineService;
    private DeviceService deviceService;
    private RecordWorkOrderDayCountService recordWorkOrderDayCountService;
    private RecordWorkOrderUnqualifiedService workOrderUnqualifiedService;
    private DictService dictService;
    private WorkCalendarService workCalendarService;
    private MaintenanceInterface maintenanceInterface;
    private MaterialMapper materialMapper;
    private final WorkOrderPlanService workOrderPlanService;
    private final WorkOrderService workOrderService;
    private final MaterialService materialService;
    private final FacilitiesService facilitiesService;
    private RecordWorkOrderLineDayCountService recordWorkOrderLineDayCountService;

    /**
     * 获取记录时间
     *
     * @return
     */
    @Override
    public void saveOrUpdateRecordOutPut(Double count, Long date) {
        recordOutputService.insertRecordOutPut(RecordOutputEntity.builder()
                .completed(count).time(new Date(date)).build());
    }

    /**
     * 获取正在生产工单
     *
     * @param lineId
     * @return
     */
    @Override
    public ResponseData getLineCurrentWorkOrder(Integer lineId) {
        WorkOrderEntity entity = productionService.getWorkOrderOneInWork(lineId);
        return ResponseData.success(entity);
    }

    /**
     * 获取工序节拍列表
     */
    @Override
    public ResponseData listFacBeat(Integer lineId) {
        List<FacilitiesBeatDTO> dtos = productionService.listFacilitiesBeat(lineId);
        return ResponseData.success(dtos);
    }

    /**
     * 产线今日数据
     *
     * @param lineId
     * @return
     */
    @Override
    public ResponseData getLineTodayData(Integer lineId) {
        LineDayDataDTO lineTodayData = productionService.getLineTodayData(lineId);
        List<DeviceStopDTO> stopAndTimes = deviceService.getDevicesStopAndTimes(lineId);
        int count = stopAndTimes.stream().filter(o -> o.getStopCount() != null).mapToInt(DeviceStopDTO::getStopCount).sum();
        double times = stopAndTimes.stream().filter(o -> o.getStopTime() != null).mapToDouble(DeviceStopDTO::getStopTime).sum();
        lineTodayData.setStopCount(count);
        lineTodayData.setStopTime(times);
        return ResponseData.success(lineTodayData);
    }

    /**
     * 车间今日数据
     * 当天的数据：(1)当天的计划达成率(当天车间已完成数量/当天需完成数量)
     * (2) 当前车间所有产线OEE的平均，产线当天所有生产设备累计故障次数、累计停机时长
     * (3) 生产良率：
     *
     * @param gridId
     * @return
     */
    @Override
    public ResponseData getGridTodayData(Integer gridId) {
        //查询产线
        List<ProductionLineEntity> list = getLineByGrid(gridId);
        List<LineDayDataDTO> dtos = new ArrayList<>();
        double stopTime = 0.0;
        int stopCount = 0;
        for (ProductionLineEntity entity : list) {
            Integer lineId = entity.getProductionLineId();
            LineDayDataDTO lineTodayData = productionService.getLineTodayData(lineId);
            List<DeviceStopDTO> stopAndTimes = deviceService.getDevicesStopAndTimes(lineId);
            stopCount += stopAndTimes.stream().filter(dto -> dto.getStopCount() != null).mapToInt(DeviceStopDTO::getStopCount).sum();
            stopTime += stopAndTimes.stream().filter(dto -> dto.getStopTime() != null).mapToDouble(DeviceStopDTO::getStopTime).sum();
            dtos.add(lineTodayData);
        }
        if (CollectionUtils.isEmpty(dtos)) {
            return ResponseData.success(GridDayDataDTO.builder().build());
        }
        //完成量
        int completed = dtos.stream().filter(o -> o.getCompleted() != null).mapToInt(LineDayDataDTO::getCompleted).sum();
        //计划量
        int planned = dtos.stream().filter(o -> o.getPlanned() != null).mapToInt(LineDayDataDTO::getPlanned).sum();
        double completionRate = planned == 0 ? 1.0 : MathUtil.divideDouble(completed, planned, 4);
        //oee
        double oee = dtos.stream().filter(o -> o.getOee() != null).mapToDouble(LineDayDataDTO::getOee).average().orElse(1.0);
        oee = MathUtil.round(oee, 4);
        //不良数
        double unqualified = dtos.stream().filter(o -> o.getUnqualified() != null).mapToDouble(LineDayDataDTO::getUnqualified).sum();
        //良率
        Double yield = completed != 0 && unqualified != 0 ? MathUtil.divideDouble(completed, completed + unqualified, 4) : 1.0;
        //停机时间处理成分钟
        GridDayDataDTO build = GridDayDataDTO.builder()
                .completed(completed)
                .planned(planned)
                .completionRate(completionRate)
                .oee(oee)
                .yield(yield)
                .unqualified(unqualified)
                .stopCount(stopCount)
                .stopTime(MathUtil.round(stopTime, 2))
                .lineCount(list.size())
                .build();
        return ResponseData.success(build);
    }

    /**
     * 通过车间id获取产线(只查询了id和name字段)
     *
     * @param gridId
     * @return
     */
    private List<ProductionLineEntity> getLineByGrid(Integer gridId) {
        //查询产线
        LambdaQueryWrapper<ProductionLineEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.select(ProductionLineEntity::getProductionLineId, ProductionLineEntity::getName)
                .eq(ProductionLineEntity::getGid, gridId);
        return lineService.list(wrapper);
    }

    /**
     * 通过车间id获取产线计划完成率
     *
     * @param gridId
     * @return
     */
    @Override
    public ResponseData getGridCompletionRate(Integer gridId) {
        List<ProductionLineEntity> list = getLineByGrid(gridId);
        //获取首班时间
        Date date = dictService.getRecordDate(new Date());
        List<LineDayDataDTO> dtos = new ArrayList<>();
        for (ProductionLineEntity lineEntity : list) {
            Integer lineId = lineEntity.getProductionLineId();
            //今日计划数
            Integer planned = productionService.getPlanQuantityTotal(lineId);
            //今日已生产数量（合格数）
            Double completed = recordWorkOrderDayCountService.getCountByDate(date, lineId);
            double completionRate = completed != null && planned != null && planned != 0 ? MathUtil.divideDouble(completed, planned, 4) : 1.0;
            dtos.add(LineDayDataDTO.builder()
                    .lineName(lineEntity.getName())
                    .completed(completed == null ? null : completed.intValue())
                    .planned(planned)
                    .completionRate(completionRate)
                    .build());
        }
        return ResponseData.success(dtos);
    }

    /**
     * 通过车间id获取产线OEE、良率
     *
     * @param gridId
     * @return
     */
    @Override
    public ResponseData getOeeListByGridId(Integer gridId) {
        //查询产线
        List<ProductionLineEntity> list = getLineByGrid(gridId);
        //获取首班时间
        Date date = dictService.getRecordDate(new Date());
        List<LineDayDataDTO> dtos = new ArrayList<>();
        for (ProductionLineEntity lineEntity : list) {
            Integer lineId = lineEntity.getProductionLineId();
            Double oee = productionService.getLineOeeByDate(lineId, date);
            //获取生产良率
            Double yield = MathUtil.round(recordWorkOrderDayCountService.getQualifiedRateCountByDate(date, lineId), 4);
            dtos.add(LineDayDataDTO.builder()
                    .lineName(lineEntity.getName())
                    .oee(oee)
                    .yield(yield)
                    .build());
        }
        return ResponseData.success(dtos);
    }

    /**
     * 通过车间id获取产线停机次数、时长
     *
     * @param gridId
     * @return
     */
    @Override
    public ResponseData getLineStopCountAndTime(Integer gridId) {
        //查询产线
        List<ProductionLineEntity> list = getLineByGrid(gridId);
        List<LineDayDataDTO> dtos = new ArrayList<>();
        for (ProductionLineEntity lineEntity : list) {
            Integer lineId = lineEntity.getProductionLineId();
            //获取停机次数和时长
            List<DeviceStopDTO> stopAndTimes = deviceService.getDevicesStopAndTimes(lineId);
            int count = stopAndTimes.stream().filter(o -> o.getStopCount() != null).mapToInt(DeviceStopDTO::getStopCount).sum();
            double times = stopAndTimes.stream().filter(o -> o.getStopTime() != null).mapToDouble(DeviceStopDTO::getStopTime).sum();
            dtos.add(LineDayDataDTO.builder()
                    .lineName(lineEntity.getName())
                    .stopCount(count)
                    .stopTime(times)
                    .build());
        }
        return ResponseData.success(dtos);
    }

    /**
     * 获取本月成品的产出top
     */
    @Override
    public ResponseData getOutputTop(Integer grid) {
        LocalDate now = LocalDate.now();
        LocalDate firstDayOfMonth = now.with(TemporalAdjusters.firstDayOfMonth());
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(DateUtil.DATETIME_FORMAT);
        String end = LocalDateTime.of(now, LocalTime.MIN).format(formatter);
        String start = LocalDateTime.of(firstDayOfMonth, LocalTime.MIN).format(formatter);

        List<ProductionLineEntity> lines = lineService.listLineByGid(grid);
        List<Integer> lineIds = lines.stream().map(ProductionLineEntity::getProductionLineId).collect(Collectors.toList());
        List<RecordWorkOrderLineDayCountEntity> recordWorkOrderDayCounts = new ArrayList<>();
        if (!CollectionUtils.isEmpty(lineIds)) {
            //获取本月数据
            recordWorkOrderDayCounts = recordWorkOrderLineDayCountService.lambdaQuery()
                    .in(!ObjectUtils.isEmpty(grid), RecordWorkOrderLineDayCountEntity::getLineId, lineIds)
                    .between(RecordWorkOrderLineDayCountEntity::getTime, start, end)
                    .list();
        }
        //根据物料编号查询名称
        List<String> materialCodes = recordWorkOrderDayCounts.stream().map(RecordWorkOrderLineDayCountEntity::getMaterialCode)
                .filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
        Map<String, String> materialCodeNameMap = new HashMap<>(materialCodes.size());
        if (!CollectionUtils.isEmpty(materialCodes)) {
            LambdaQueryWrapper<MaterialEntity> materialWrapper = new LambdaQueryWrapper<>();
            materialWrapper.select(MaterialEntity::getName, MaterialEntity::getCode)
                    .in(MaterialEntity::getCode, materialCodes);
            List<MaterialEntity> materialEntities = materialMapper.selectList(materialWrapper);
            materialEntities.forEach(o -> materialCodeNameMap.put(o.getCode(), o.getName()));
        }
        List<MaterialOutputDTO> outputDtos = recordWorkOrderDayCounts.stream().map(o -> MaterialOutputDTO.builder()
                .lineId(o.getLineId())
                .materialCode(o.getMaterialCode())
                .materialName(materialCodeNameMap.get(o.getMaterialCode()))
                .count(o.getCount())
                .build()).collect(Collectors.toList());

        Map<String, Double> sumMap = outputDtos.stream().collect(Collectors.groupingBy(MaterialOutputDTO::getMaterialCode,
                Collectors.summingDouble(MaterialOutputDTO::getCount)));

        List<MaterialOutputDTO> dtos = sumMap.entrySet().stream()
                .map(entry -> MaterialOutputDTO.builder().materialCode(entry.getKey()).materialName(materialCodeNameMap.get(entry.getKey())).count(entry.getValue()).build())
                .collect(Collectors.toList());
        //获取产量最高的5条数据
        List<MaterialOutputDTO> list = dtos.stream().sorted(Comparator.comparing(MaterialOutputDTO::getCount).reversed()).limit(5).collect(Collectors.toList());
        return ResponseData.success(list);
    }

    @Override
    public ResponseData getOutputTypeTop(Integer grid) {
        // 当月
        Date dateMonthEnd = new Date();
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        Date dateMonthStart = calendar.getTime();

        List<ProductionLineEntity> lines = lineService.listLineByGid(grid);
        if (CollectionUtils.isEmpty(lines)) {
            return ResponseData.success(new ArrayList<>());
        }
        List<Integer> lineIds = lines.stream().map(ProductionLineEntity::getProductionLineId).collect(Collectors.toList());

        //获取本月数据
        List<RecordWorkOrderLineDayCountEntity> recordWorkOrderDayCounts = recordWorkOrderLineDayCountService.lambdaQuery()
                .in(RecordWorkOrderLineDayCountEntity::getLineId, lineIds)
                .between(RecordWorkOrderLineDayCountEntity::getTime, dateMonthStart, dateMonthEnd)
                .list();
        //根据物料编号查询名称
        Set<String> materialCodes = recordWorkOrderDayCounts.stream().map(RecordWorkOrderLineDayCountEntity::getMaterialCode).collect(Collectors.toSet());
        Map<String, String> materialCodeTypeNameMap = new HashMap<>(materialCodes.size());
        if (!CollectionUtils.isEmpty(materialCodes)) {
            List<MaterialEntity> materialEntities = materialService.lambdaQuery()
                    .in(MaterialEntity::getCode, materialCodes)
                    .list();
            // 设置物料类型名称
            materialService.setMaterialTypeName(materialEntities);
            materialEntities.forEach(o -> materialCodeTypeNameMap.put(o.getCode(), o.getTypeName()));
        }

        Map<String, List<RecordWorkOrderLineDayCountEntity>> typeNameWorkOrderDayCountsMap = recordWorkOrderDayCounts.stream()
                .collect(Collectors.groupingBy(res -> materialCodeTypeNameMap.get(res.getMaterialCode())));
        List<MaterialOutputDTO.MaterialTypeOutput> rets = new ArrayList<>();
        for (Map.Entry<String, List<RecordWorkOrderLineDayCountEntity>> entry : typeNameWorkOrderDayCountsMap.entrySet()) {
            String typeName = entry.getKey();
            double count = entry.getValue().stream().mapToDouble(RecordWorkOrderLineDayCountEntity::getCount).sum();
            rets.add(MaterialOutputDTO.MaterialTypeOutput.builder()
                    .materialTypeName(typeName)
                    .count(count)
                    .build());
        }
        //获取产量最高的5条数据
        rets = rets.stream().sorted((t1, t2) -> t2.getCount().compareTo(t1.getCount())).limit(5).collect(Collectors.toList());

        return ResponseData.success(rets);
    }

    /**
     * 获取各个产线，当月的故障时长小时数
     */
    @Override
    public ResponseData getAllLineStopTime(Integer grid) {
        Calendar calendar = Calendar.getInstance();
        Date now = calendar.getTime();
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        Date firstDayOfMonth = calendar.getTime();
        firstDayOfMonth = dictService.getDayOutputBeginTime(firstDayOfMonth);
        //查询产线
        List<ProductionLineEntity> list = lineService.listLineByGid(grid);
        List<LineDayDataDTO> dtos = new ArrayList<>();
        for (ProductionLineEntity lineEntity : list) {
            Integer lineId = lineEntity.getProductionLineId();
            //获取停机次数和时长
            List<DeviceStopDTO> stopAndTimes = deviceService.getDeviceStopDtos(lineId, firstDayOfMonth, now);
            int count = stopAndTimes.stream().filter(o -> o.getStopCount() != null).mapToInt(DeviceStopDTO::getStopCount).sum();
            double times = stopAndTimes.stream().filter(o -> o.getStopTime() != null).mapToDouble(DeviceStopDTO::getStopTime).sum();
            dtos.add(LineDayDataDTO.builder()
                    .lineName(lineEntity.getName())
                    .stopCount(count)
                    .stopTime(times)
                    .build());
        }
        return ResponseData.success(dtos);
    }

    /**
     * 所有产线当天的需完成数、已完成数、当天的完成率、当天的不良率
     *
     * @return
     */
    @Override
    public ResponseData getAllLineTodayData() {
        List<ProductionLineEntity> list = lineService.list();
        List<LineDayDataDTO> dtos = new ArrayList<>();
        for (ProductionLineEntity entity : list) {
            Integer lineId = entity.getProductionLineId();
            LineDayDataDTO lineTodayData = productionService.getLineTodayData(lineId);
            dtos.add(lineTodayData);
        }
        if (CollectionUtils.isEmpty(dtos)) {
            return ResponseData.success(GridDayDataDTO.builder().build());
        }
        //今日完成数
        int completed = dtos.stream().filter(o -> o.getCompleted() != null).mapToInt(LineDayDataDTO::getCompleted).sum();
        //今日计划数
        //和其他组件统计的今日计划数保持统一,放弃这个方法,copy一个新方法
        List<Integer> lineIds = list.stream().map(ProductionLineEntity::getProductionLineId).collect(Collectors.toList());
        Date date = dictService.getRecordDate(new Date());
        int planned = getTodayPlanQuantity(lineIds, date);
        //int planned = dtos.stream().filter(o -> o.getPlanned() != null).mapToInt(LineDayDataDTO::getPlanned).sum();
        //今日完成率
        double completionRate = planned == 0 ? 1.0 : MathUtil.divideDouble(completed, planned, 4);
        //今日不良数
        double unqualified = dtos.stream().filter(o -> o.getUnqualified() != null).mapToDouble(LineDayDataDTO::getUnqualified).sum();
        //今日不良率
        double unqualifiedRate = completed == 0 ? 0 : MathUtil.divideDouble(unqualified, completed + unqualified, 4);
        GridDayDataDTO dto = GridDayDataDTO.builder()
                .completed(completed)
                .planned(planned)
                .completionRate(completionRate)
                .unqualified(unqualified)
                .unqualifiedRate(unqualifiedRate)
                .build();
        return ResponseData.success(dto);
    }

    /**
     * 查询今日计划数, 独立工单 + 父工单
     */
    private int getTodayPlanQuantity(List<Integer> lineIds, Date date) {
        // 今日订单计划数
        int todayPlanQuantity = 0;
        // 查询今日的所有计划数
        List<WorkOrderPlanEntity> todayWorkOrderPlans = workOrderPlanService.lambdaQuery()
                .select(WorkOrderPlanEntity::getWorkOrderNumber, WorkOrderPlanEntity::getPlanQuantity, WorkOrderPlanEntity::getTime)
                .eq(WorkOrderPlanEntity::getTime, date)
                .list();
        List<String> planWorkOrderNumbers = todayWorkOrderPlans.stream().map(WorkOrderPlanEntity::getWorkOrderNumber).collect(Collectors.toList());
        // 排除其中的子订单, 只计算 独立工单 + 父工单 的计划数量
        // 查询对应子订单
        if (!CollectionUtils.isEmpty(planWorkOrderNumbers)) {
            List<String> subWorkOrderPlans = workOrderService.lambdaQuery()
                    .select(WorkOrderEntity::getWorkOrderNumber)
                    .in(WorkOrderEntity::getWorkOrderNumber, planWorkOrderNumbers)
                    .eq(WorkOrderEntity::getIsPid, true)
                    .list().stream().map(WorkOrderEntity::getWorkOrderNumber).collect(Collectors.toList());
            for (WorkOrderPlanEntity workOrderPlan : todayWorkOrderPlans) {
                if (!subWorkOrderPlans.contains(workOrderPlan.getWorkOrderNumber())) {
                    todayPlanQuantity += workOrderPlan.getPlanQuantity();
                }
            }
        }
        return todayPlanQuantity;
    }

    /**
     * 今日各产线产出
     */
    @Override
    public ResponseData getAllLineTodayCompleted(Integer grid) {
        List<ProductionLineEntity> list = lineService.listLineByGid(grid);
        //获取当前首班时间
        Date date = dictService.getRecordDate(new Date());
        List<LineDayDataDTO> dtos = new ArrayList<>();
        for (ProductionLineEntity entity : list) {
            Integer lineId = entity.getProductionLineId();
            //今日已生产数量（合格数）
            Double completed = recordWorkOrderDayCountService.getCountByDate(date, lineId);
            dtos.add(LineDayDataDTO.builder()
                    .lineName(entity.getName())
                    .completed(completed == null ? null : completed.intValue())
                    .build());
        }
        return ResponseData.success(dtos);
    }

    /**
     * 今日各产线的OEE、不良率、故障次数、故障时长
     *
     * @return
     */
    @Override
    public ResponseData getAllLineOee(Integer grid) {
        List<ProductionLineEntity> list = lineService.listLineByGid(grid);
        List<LineDayDataDTO> dtos = new ArrayList<>();
        for (ProductionLineEntity entity : list) {
            Integer lineId = entity.getProductionLineId();
            LineDayDataDTO lineTodayData = productionService.getLineTodayData(lineId);
            List<DeviceStopDTO> stopAndTimes = deviceService.getDevicesStopAndTimes(lineId);
            int stopCount = stopAndTimes.stream().filter(dto -> dto.getStopCount() != null).mapToInt(DeviceStopDTO::getStopCount).sum();
            double stopTime = stopAndTimes.stream().filter(dto -> dto.getStopTime() != null).mapToDouble(DeviceStopDTO::getStopTime).sum();
            lineTodayData.setStopTime(stopTime);
            lineTodayData.setStopCount(stopCount);
            lineTodayData.setLineName(entity.getName());
            dtos.add(lineTodayData);
        }
        return ResponseData.success(dtos);
    }

    /**
     * 工厂所有产线的当月产出数
     *
     * @return
     */
    @Override
    public ResponseData getAllLineMonthCompleted(Integer grid) {
        //获取本月产量数据
        LocalDate now = LocalDate.now();
        LocalDate firstDayOfMonth = now.with(TemporalAdjusters.firstDayOfMonth());
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(DateUtil.DATETIME_FORMAT);
        String end = LocalDateTime.of(now, LocalTime.MIN).format(formatter);
        String start = LocalDateTime.of(firstDayOfMonth, LocalTime.MIN).format(formatter);
        List<RecordWorkOrderLineDayCountEntity> countByDays = recordWorkOrderLineDayCountService.getDayCountByDays(start, end);
        Map<Integer, Double> countMap = countByDays.stream().filter(o -> o.getLineId() != null)
                .collect(Collectors.groupingBy(RecordWorkOrderLineDayCountEntity::getLineId,
                        Collectors.summingDouble(RecordWorkOrderLineDayCountEntity::getCount)));

        // Y轴为产线名称，从上至下，按照产线创建的先后顺序进行排序；
        List<ProductionLineEntity> list = lineService.lambdaQuery()
                .select(ProductionLineEntity::getProductionLineId, ProductionLineEntity::getName)
                .eq(Objects.nonNull(grid), ProductionLineEntity::getGid, grid)
                .orderByAsc(ProductionLineEntity::getCreateDate)
                .list();

        List<LineDayDataDTO> dtos = new ArrayList<>();
        for (ProductionLineEntity entity : list) {
            Integer lineId = entity.getProductionLineId();
            Double count = countMap.getOrDefault(lineId, 0.0);
            dtos.add(LineDayDataDTO.builder()
                    .lineName(entity.getName())
                    .completed(count.intValue())
                    .build());
        }
        return ResponseData.success(dtos);
    }

    /**
     * 工厂所有产线的当月故障次数占比
     *
     * @return
     */
    @Override
    public ResponseData getAllLineMonthStopCount(Integer grid) {
        Calendar calendar = Calendar.getInstance();
        Date now = calendar.getTime();
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        Date firstDayOfMonth = calendar.getTime();
        firstDayOfMonth = dictService.getDayOutputBeginTime(firstDayOfMonth);
        //查询产线
        List<ProductionLineEntity> list = lineService.listLineByGid(grid);
        List<LineDayDataDTO> dtos = new ArrayList<>();
        for (ProductionLineEntity lineEntity : list) {
            Integer lineId = lineEntity.getProductionLineId();
            //获取停机次数和时长
            List<DeviceStopDTO> stopAndTimes = deviceService.getDeviceStopDtos(lineId, firstDayOfMonth, now);
            int count = stopAndTimes.stream().filter(o -> o.getStopCount() != null).mapToInt(DeviceStopDTO::getStopCount).sum();
            dtos.add(LineDayDataDTO.builder()
                    .lineName(lineEntity.getName())
                    .stopCount(count)
                    .build());
        }
        return ResponseData.success(dtos);

    }

    /**
     * 今日各不良项的占比,top5 ()
     * 指定周期不良分项的占比,top5
     *
     * @return
     */
    @Override
    public ResponseData getUnqualifiedTop(Integer grid, Integer countDay) {
        // 查询车间对应的工位
        List<Integer> facIds = null;
        if (Objects.nonNull(grid)) {
            List<FacilitiesEntity> facList = facilitiesService.lambdaQuery()
                    .select(FacilitiesEntity::getFid, FacilitiesEntity::getFname, FacilitiesEntity::getSeq)
                    .eq(FacilitiesEntity::getGid, grid)
                    .list();
            facIds = facList.stream().map(FacilitiesEntity::getFid).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(facList)) {
                return ResponseData.success(new ArrayList<>());
            }
        }

        //获取当前首班时间
        Date date = dictService.getRecordDate(new Date());
        int count = Objects.isNull(countDay) ? 0 : -countDay;
        Date countDate = DateUtil.addDate(date, count);
        SimpleDateFormat format = new SimpleDateFormat(DateUtil.DATE_FORMAT);
        String dateStr = format.format(date);
        String countStr = format.format(countDate);
        List<RecordWorkOrderUnqualifiedEntity> list = workOrderUnqualifiedService.lambdaQuery()
                .between(RecordWorkOrderUnqualifiedEntity::getRecordDate, countStr, dateStr)
                .in(Objects.nonNull(grid), RecordWorkOrderUnqualifiedEntity::getFid, facIds)
                .list();
        Map<String, Long> map = list.stream().collect(Collectors.groupingBy(RecordWorkOrderUnqualifiedEntity::getAbnormalName, Collectors.counting()));
        List<UnqualifiedCountDTO> dtos = new ArrayList<>();
        map.forEach((key, value) -> dtos.add(UnqualifiedCountDTO.builder().abnormalName(key).count(value.intValue()).build()));
        List<UnqualifiedCountDTO> dtos1 = dtos.stream().sorted(Comparator.comparing(UnqualifiedCountDTO::getCount).reversed()).limit(5).collect(Collectors.toList());
        int sum = dtos1.stream().mapToInt(UnqualifiedCountDTO::getCount).sum();
        if (sum != 0) {
            for (UnqualifiedCountDTO dto : dtos1) {
                dto.setRate(MathUtil.divideDouble(dto.getCount(), sum, 4));
            }
        }
        return ResponseData.success(dtos1);
    }

    /**
     * 统计七天内产量
     *
     * @return
     */
    @Override
    public ResponseData getDaysOutput() {
        LocalDate now = LocalDate.now();
        LocalDate sevenDaysAgo = now.minusDays(7);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(DateUtil.DATETIME_FORMAT);
        String end = LocalDateTime.of(now, LocalTime.MIN).format(formatter);
        String start = LocalDateTime.of(sevenDaysAgo, LocalTime.MIN).format(formatter);
        //获取最近七天数据
        List<RecordWorkOrderDayCountEntity> countByDays = recordWorkOrderDayCountService.getDayCountByDays(start, end);
        List<DaysOutputDTO> dtos = new ArrayList<>();
        if (CollectionUtils.isEmpty(countByDays)) {
            return ResponseData.success(dtos);
        }
        SimpleDateFormat format = new SimpleDateFormat(DateUtil.MONTH_DAY_FORMAT_SLASH);
        Map<String, Double> map = countByDays.stream().collect(Collectors.groupingBy(o -> format.format(o.getTime()),
                Collectors.summingDouble(RecordWorkOrderDayCountEntity::getCount)));
        DateTimeFormatter dateFormat = DateTimeFormatter.ofPattern(DateUtil.MONTH_DAY_FORMAT_SLASH);
        while (sevenDaysAgo.isBefore(now)) {
            sevenDaysAgo = sevenDaysAgo.plusDays(1);
            String format1 = sevenDaysAgo.format(dateFormat);
            Integer completed = map.containsKey(format1) ? map.get(format1).intValue() : 0;
            dtos.add(DaysOutputDTO.builder().dateStr(format1).completed(completed).build());
        }
        dtos.sort(Comparator.comparing(DaysOutputDTO::getDateStr));
        return ResponseData.success(dtos);
    }

    @Override
    @Cacheable(cacheNames = "screen_inflation_compliance_rate", key = "'lineId=' + #lineId")
    public ResponseData getInflationComplianceRate(Integer lineId, String targetName) {
        //1.16.1版本直接给默认值 产品经理说后续数采数据上来之后再修改算法
        return ResponseData.success(0.98);
    }

    /**
     * 通过车间id和时间范围获取获取产线产出
     *
     * @param gridId
     * @param timeRange
     * @return
     */
    @Override
    @Cacheable(cacheNames = "screen_line_output_by_grid", key = "'gridId=' + #gridId")
    public ResponseData getLineOutputByGrid(Integer gridId, String timeRange) {
        GridEntity gridEntity = gridService.getById(gridId);
        if (gridEntity == null || StringUtils.isBlank(timeRange)) {
            return ResponseData.success();
        }
        //处理时间范围
        Map<String, Date> rangeDate = getRangeDate(timeRange);
        //获取车间的产线
        LambdaQueryWrapper<ProductionLineEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProductionLineEntity::getGid, gridId);
        List<ProductionLineEntity> list = lineService.list(wrapper);
        List<Integer> lineIds = list.stream().map(ProductionLineEntity::getProductionLineId).collect(Collectors.toList());
        //获取产线数据
        SimpleDateFormat format = new SimpleDateFormat(DateUtil.DATETIME_FORMAT);
        Date dayStart = rangeDate.get("dayStart");
        Date dayEnd = rangeDate.get("dayEnd");
        String start = format.format(dayStart);
        String end = format.format(dayEnd);
        String dateStr = getDateStr(dayStart, dayEnd);
        List<RecordLineDayUnionEntity> entities = productionService.getOeeByLinesAndDays(lineIds, start, end);
        Map<Integer, List<RecordLineDayUnionEntity>> listMap = entities.stream().collect(Collectors.groupingBy(RecordLineDayUnionEntity::getProductionLineId));
        //求上一周期的环比
        Date lastCycleStart = rangeDate.get("lastCycleStart");
        Date lastCycleEnd = rangeDate.get("lastCycleEnd");
        String lastStart = format.format(lastCycleStart);
        String lastend = format.format(lastCycleEnd);
        List<RecordLineDayUnionEntity> lastCycleList = productionService.getOeeByLinesAndDays(lineIds, lastStart, lastend);
        Map<Integer, List<RecordLineDayUnionEntity>> lastMap = lastCycleList.stream().collect(Collectors.groupingBy(RecordLineDayUnionEntity::getProductionLineId));
        Date lastDay = DateUtil.addDate(dayEnd, -1);
        List<LineOutputDTO> dtos = new ArrayList<>();
        for (ProductionLineEntity lineEntity : list) {
            Integer lineId = lineEntity.getProductionLineId();
            List<RecordLineDayUnionEntity> unionEntities = listMap.get(lineId);
            if (CollectionUtils.isEmpty(unionEntities)) {
                dtos.add(LineOutputDTO.builder().lineName(lineEntity.getName()).build());
                continue;
            }
            //获取每天工时 (原话：系统定义的每天的开工时间到当前时间，去除非工作时间。)
            double hours = 0.0;
            Date firstDay = dayStart;
            while (firstDay.before(lastDay)) {
                Double workDuration = workCalendarService.traceWorkDuration(lineId, ModelEnum.LINE.getType(), firstDay);
                hours = workDuration == null ? hours : hours + workDuration;
                firstDay = DateUtil.addDate(firstDay, 1);
            }
            //当天的工时
            Double workDuration = workCalendarService.getWorkDurationUpToNow(lineId, ModelEnum.LINE.getType(), dayEnd);
            hours = workDuration == null ? hours : hours + workDuration;
            double count = unionEntities.stream().mapToDouble(RecordLineDayUnionEntity::getCount).sum();
            double unqualified = unionEntities.stream().mapToDouble(RecordLineDayUnionEntity::getUnqualified).sum();
            double total = count + unqualified;
            double yeild = total == 0 ? 1 : MathUtil.divideDouble(count, total, 2);
            double oee = unionEntities.stream().mapToDouble(RecordLineDayUnionEntity::getOee).average().orElse(1);
            Double completedPerHour = hours == 0 ? null : MathUtil.divideDouble(count, hours, 2);
            //求上一周期的环比
            double lastHours = 0.0;
            while (lastCycleStart.before(lastCycleEnd)) {
                Double lastTime = workCalendarService.traceWorkDuration(lineId, ModelEnum.LINE.getType(), lastCycleStart);
                lastHours = lastTime == null ? lastHours : lastHours + lastTime;
                lastCycleStart = DateUtil.addDate(lastCycleStart, 1);
            }
            LineOutputDTO build = LineOutputDTO.builder()
                    .lineName(lineEntity.getName())
                    .completed(count)
                    .completedPerHour(completedPerHour != null ? completedPerHour.intValue() : 0)
                    .yield(yeild)
                    .oee(oee)
                    .build();
            List<RecordLineDayUnionEntity> lastlist = lastMap.get(lineId);
            if (!CollectionUtils.isEmpty(lastlist)) {
                double lastCount = lastlist.stream().mapToDouble(RecordLineDayUnionEntity::getCount).sum();
                double lastUnqualified = lastlist.stream().mapToDouble(RecordLineDayUnionEntity::getUnqualified).sum();
                double lastTotal = lastCount + lastUnqualified;
                double latYeild = lastTotal == 0 ? 1 : MathUtil.divideDouble(lastCount, lastTotal, 2);
                double lastOee = lastlist.stream().mapToDouble(RecordLineDayUnionEntity::getOee).average().orElse(1);
                Double lastCompletedPerHour = lastHours == 0 ? null : MathUtil.divideDouble(lastCount, lastHours, 2);
                Double hourChain = completedPerHour != null && lastCompletedPerHour != null && lastCompletedPerHour != 0
                        ? MathUtil.divideDouble(completedPerHour - lastCompletedPerHour, lastCompletedPerHour, 2) : null;
                Double yieldChain = latYeild == 0 ? null : MathUtil.divideDouble(yeild - latYeild, latYeild, 2);
                Double oeeChain = lastOee == 0 ? null : MathUtil.divideDouble(oee - lastOee, lastOee, 2);
                build.setHourChain(hourChain);
                build.setYieldChain(yieldChain);
                build.setOeeChain(oeeChain);
            }
            dtos.add(build);
        }
        GridOutputDTO build = GridOutputDTO.builder()
                .gridName(gridEntity.getGname())
                .dateStr(dateStr)
                .lineOutput(dtos)
                .build();
        return ResponseData.success(build);
    }

    /**
     * 时间范围
     *
     * @param timeRange
     * @return
     */
    private Map<String, Date> getRangeDate(String timeRange) {
        Calendar calendar = Calendar.getInstance();
        Date dayEnd = calendar.getTime();
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        if (TimeRangeEnum.WEEK.getCode().equals(timeRange)) {
            calendar.setFirstDayOfWeek(Calendar.MONDAY);
            calendar.set(Calendar.DAY_OF_WEEK, Calendar.MONDAY);
        }
        if (TimeRangeEnum.MONTH.getCode().equals(timeRange)) {
            calendar.set(Calendar.DAY_OF_MONTH, 1);
        }
        Date dayStart = calendar.getTime();
        //获取上一周期时间范围
        if (TimeRangeEnum.DAY.getCode().equals(timeRange)) {
            calendar.add(Calendar.DATE, -1);
        }
        if (TimeRangeEnum.WEEK.getCode().equals(timeRange)) {
            calendar.add(Calendar.WEEK_OF_YEAR, -1);
        }
        if (TimeRangeEnum.MONTH.getCode().equals(timeRange)) {
            calendar.add(Calendar.MONTH, -1);
        }
        Date lastCycleStart = calendar.getTime();
        Date lastCycleEnd = DateUtil.addMin(dayStart, -1);
        HashMap<String, Date> map = new HashMap<>();
        map.put("dayStart", dayStart);
        map.put("dayEnd", dayEnd);
        map.put("lastCycleStart", lastCycleStart);
        map.put("lastCycleEnd", lastCycleEnd);
        return map;
    }

    @Override
    @Cacheable(cacheNames = "screen_line_device_fault", key = "'lineId=' + #lineId")
    public ResponseData getLineDeviceFaults(Integer lineId, String timeRange) {
        ProductionLineEntity lineEntity = lineService.getById(lineId);
        if (lineEntity == null || StringUtils.isBlank(timeRange)) {
            return ResponseData.success();
        }
        //处理时间范围
        Map<String, Date> rangeDate = getRangeDate(timeRange);
        SimpleDateFormat format = new SimpleDateFormat(DateUtil.DATETIME_FORMAT);
        Date dayStart = rangeDate.get("dayStart");
        Date dayEnd = rangeDate.get("dayEnd");
        String dateStr = getDateStr(dayStart, dayEnd);
        String start = format.format(dayStart);
        String end = format.format(dayEnd);
        List<RecordLineDayUnionEntity> entities = productionService.getOeeByLineIdAndDays(lineId, start, end);
        double count = entities.stream().mapToDouble(RecordLineDayUnionEntity::getCount).sum();
        //相差天数
        int days = (int) ((dayEnd.getTime() - dayStart.getTime()) / (1000 * 3600 * 24));
        double hours = 0.0;
        Date lastDay = DateUtil.addDate(dayEnd, -1);
        while (dayStart.before(lastDay)) {
            Double workDuration = workCalendarService.traceWorkDuration(lineId, ModelEnum.LINE.getType(), dayStart);
            hours = workDuration == null ? hours : hours + workDuration;
            dayStart = DateUtil.addDate(dayStart, 1);
        }
        //当天的工时
        Double workDuration = workCalendarService.getWorkDurationUpToNow(lineId, ModelEnum.LINE.getType(), dayEnd);
        hours = workDuration == null ? hours : hours + workDuration;
        //日均产量
        Double countPerDay = days == 0 ? count : MathUtil.divideDouble(count, days, 2);
        //小时产量
        Double countPerHour = hours == 0 ? null : MathUtil.divideDouble(count, hours, 2);
        //上一周期的数据
        Date lastCycleStart = rangeDate.get("lastCycleStart");
        Date lastCycleEnd = rangeDate.get("lastCycleEnd");
        String lastStart = format.format(lastCycleStart);
        String lastend = format.format(lastCycleEnd);
        List<RecordLineDayUnionEntity> lastCycleList = productionService.getOeeByLineIdAndDays(lineId, lastStart, lastend);
        double lastCount = lastCycleList.stream().mapToDouble(RecordLineDayUnionEntity::getCount).sum();
        double lastHours = 0.0;
        while (lastCycleStart.before(lastCycleEnd)) {
            Double lastworkDuration = workCalendarService.traceWorkDuration(lineId, ModelEnum.LINE.getType(), dayStart);
            lastHours = lastworkDuration == null ? lastHours : lastHours + lastworkDuration;
            lastCycleStart = DateUtil.addDate(lastCycleStart, 1);
        }
        //日均产量
        Double lastCountPerDay = days == 0 ? lastCount : MathUtil.divideDouble(lastCount, days, 2);
        //小时产量
        Double lastCountPerHour = lastHours == 0 ? null : MathUtil.divideDouble(lastCount, lastHours, 2);
        //获取产线下所有设备
        List<DeviceEntity> deviceEntities = deviceService.deviceByLineId(lineId);
        if (CollectionUtils.isEmpty(deviceEntities)) {
            LineFaultDTO build = LineFaultDTO.builder()
                    .lineName(lineEntity.getName())
                    .dateStr(dateStr)
                    .countPerDay(countPerDay)
                    .countPerHour(countPerHour)
                    .lastCountPerDay(lastCountPerDay)
                    .lastCountPerHour(lastCountPerHour)
                    .build();
            return ResponseData.success(build);
        }
        Map<String, String> deviceMap = deviceEntities.stream().collect(Collectors.toMap(DeviceEntity::getDeviceCode, DeviceEntity::getDeviceName));
        String deviceCodes = String.join(",", deviceMap.keySet());
        List<LineDeviceFaultDTO> dtos = new ArrayList<>();
        List<LineDeviceFaultDTO> lastDtos = new ArrayList<>();
        try {
            ResponseData data = maintenanceInterface.getMaintenanceRecord(deviceCodes, start, end);
            ResponseData lastData = maintenanceInterface.getMaintenanceRecord(deviceCodes, lastStart, lastend);
            dtos = JacksonUtil.getResponseArray(data, LineDeviceFaultDTO.class);
            lastDtos = JacksonUtil.getResponseArray(lastData, LineDeviceFaultDTO.class);
        } catch (Exception e) {
            log.error("访问maintain服务出现错误", e);
        }
        int totalCount = dtos.stream().filter(o -> o.getFaultCount() != null).mapToInt(LineDeviceFaultDTO::getFaultCount).sum();
        double totalDuration = dtos.stream().filter(o -> o.getRepairDuration() != null).mapToDouble(LineDeviceFaultDTO::getRepairDuration).sum();
        int lastTotalCount = lastDtos.stream().filter(o -> o.getFaultCount() != null).mapToInt(LineDeviceFaultDTO::getFaultCount).sum();
        double lastTotalDuration = lastDtos.stream().filter(o -> o.getRepairDuration() != null).mapToDouble(LineDeviceFaultDTO::getRepairDuration).sum();
        Map<String, LineDeviceFaultDTO> map = dtos.stream().collect(Collectors.toMap(LineDeviceFaultDTO::getDeviceCode, o -> o));
        Map<String, LineDeviceFaultDTO> lastMap = lastDtos.stream().collect(Collectors.toMap(LineDeviceFaultDTO::getDeviceCode, o -> o));
        ArrayList<LineDeviceFaultDTO> faultDTOS = new ArrayList<>();
        for (DeviceEntity deviceEntity : deviceEntities) {
            String deviceCode = deviceEntity.getDeviceCode();
            if (!map.containsKey(deviceCode)) {
                faultDTOS.add(LineDeviceFaultDTO.builder()
                        .deviceName(deviceEntity.getDeviceName())
                        .deviceCode(deviceCode)
                        .build());
                continue;
            }
            LineDeviceFaultDTO dto = map.get(deviceCode);
            Integer faultCount = dto.getFaultCount();
            //故障率占比
            double faultRatio = faultCount == null || totalCount == 0 ? 0 : MathUtil.divideDouble(faultCount, totalCount, 2);
            LineDeviceFaultDTO faultDTO = LineDeviceFaultDTO.builder()
                    .deviceName(deviceEntity.getDeviceName())
                    .deviceCode(deviceCode)
                    .faultCount(faultCount)
                    .faultRatio(faultRatio)
                    .build();
            if (lastMap.containsKey(deviceCode)) {
                //求环比
                LineDeviceFaultDTO lastDto = lastMap.get(deviceCode);
                Integer lastFaultCount = lastDto.getFaultCount();
                double lastFaultCountRatio = lastFaultCount == null || lastTotalCount == 0 ? 0 : MathUtil.divideDouble(lastFaultCount, lastTotalCount, 2);
                Double faultCountChain = faultCount == null || lastFaultCount == null || lastFaultCount == 0 ? null :
                        MathUtil.divideDouble(faultCount - lastFaultCount, lastFaultCount, 2);
                Double faultRatioChain = lastFaultCountRatio == 0 ? null : MathUtil.divideDouble(faultRatio - lastFaultCountRatio, lastFaultCountRatio, 2);
                faultDTO.setFaultCountChain(faultCountChain);
                faultDTO.setFaultRatioChain(faultRatioChain);
            }
            faultDTOS.add(faultDTO);
        }
        LineFaultDTO build = LineFaultDTO.builder()
                .lineName(lineEntity.getName())
                .dateStr(dateStr)
                .countPerDay(countPerDay)
                .countPerHour(countPerHour)
                .faultCount(totalCount)
                .repairDuration(totalDuration)
                .lastCountPerDay(lastCountPerDay)
                .lastCountPerHour(lastCountPerHour)
                .lastFaultCount(lastTotalCount)
                .lastRepairDuration(lastTotalDuration)
                .dtos(faultDTOS)
                .build();
        return ResponseData.success(build);
    }

    /**
     * 处理时间字符串
     *
     * @param dayStart
     * @param dayEnd
     * @return
     */
    private String getDateStr(Date dayStart, Date dayEnd) {
        SimpleDateFormat format = new SimpleDateFormat(DateUtil.DATE_FORMAT);
        String start = format.format(dayStart);
        String end = format.format(dayEnd);
        return start.equals(end) ? start : start + " 至 " + end;
    }

    @Override
    public ResponseData getWorkOrderDayCountList(ProductionReqDTO dto) {
        //获取首班时间
        Date date = dictService.getRecordDate(new Date());
        List<RecordWorkOrderDayCountEntity> list = recordWorkOrderDayCountService.getByDateAndLineId(date, dto.getLineId());
        return ResponseData.success(list);
    }

    @Override
    public ResponseData getPlanQuantity(ProductionReqDTO dto) {
        Integer planQuantity = productionService.getPlanQuantity(dto.getWorkOrderNumber(), dto.getDate());
        return ResponseData.success(planQuantity);
    }

    @Override
    public ResponseData getCompletedByDate(ProductionReqDTO dto) {
        Date date = dto.getDate();
        Integer lineId = dto.getLineId();
        if (date == null) {
            date = new Date();
        }
        Date recordDate = dictService.getRecordDate(date);
        List<RecordWorkOrderDayCountEntity> unionEntities = recordWorkOrderDayCountService.getByDateAndLineId(recordDate, lineId);
        double count = unionEntities.stream().mapToDouble(RecordWorkOrderDayCountEntity::getCount).sum();
        return ResponseData.success(count);
    }


    @Override
    public ResponseData getGridOutputAfterDate(Integer gridId, Long dateAfter) {
        if (dateAfter == null) {
            return ResponseData.success(new ArrayList<>());
        }
        List<ProductionLineEntity> lineEntities = lineService.listLineByGid(gridId);
        List<Integer> lineIds = lineEntities.stream().map(ProductionLineEntity::getProductionLineId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(lineIds)) {
            return ResponseData.success(new ArrayList<>());
        }
        Date date = new Date(dateAfter);
        Date recordDate = dictService.getRecordDate(date);
        LambdaQueryWrapper<RecordOutputEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.ge(RecordOutputEntity::getTime, recordDate)
                .in(RecordOutputEntity::getLineId, lineIds);
        List<RecordOutputEntity> list = recordOutputService.list(wrapper);
        return ResponseData.success(list);
    }

    /**
     * 获取指定日期之后的OEE记录
     *
     * @param gridId
     * @param dateAfter
     * @return
     */
    @Override
    public ResponseData getGridOeeAfterDate(Integer gridId, Long dateAfter) {
        if (dateAfter == null) {
            return ResponseData.success(new ArrayList<>());
        }
        List<ProductionLineEntity> lineEntities = lineService.listLineByGid(gridId);
        List<Integer> lineIds = lineEntities.stream().map(ProductionLineEntity::getProductionLineId).collect(Collectors.toList());
        List<RecordLineDayUnionEntity> list = productionService.getLineOeeByIdsAndDate(lineIds, new Date(dateAfter));
        return ResponseData.success(list);
    }

    @Override
    public ResponseData getYield(Integer gridId) {
        Date endTimeDate = new Date();
        // yyyy-MM-dd 00:00:00
        Date startTimeDate = DateUtil.formatToDate(DateUtil.addDate(endTimeDate, -6), DateUtil.DATETIME_FORMAT_ZERO);

        List<ProductionLineEntity> lines = lineService.listLineByGid(gridId);
        List<Integer> lineIds = lines.stream()
                .map(ProductionLineEntity::getProductionLineId)
                .collect(Collectors.toList());

        SimpleDateFormat sdf = new SimpleDateFormat(DateUtil.DATE_FORMAT);
        HashMap<String, Double> dayCountMap = new HashMap<>(16), dayUnqualifiedMap = new HashMap<>(16);

        List<RecordWorkOrderLineDayCountEntity> list = new ArrayList<>();
        if (!CollectionUtils.isEmpty(lineIds)) {
            list = recordWorkOrderLineDayCountService.lambdaQuery()
                    .in(RecordWorkOrderLineDayCountEntity::getLineId, lineIds)
                    .ge(RecordWorkOrderLineDayCountEntity::getTime, startTimeDate)
                    .list();
        }
        Map<String, List<RecordWorkOrderLineDayCountEntity>> countMap = list.stream().collect(Collectors.groupingBy(o -> sdf.format(o.getTime())));
        for (Map.Entry<String, List<RecordWorkOrderLineDayCountEntity>> entry : countMap.entrySet()) {
            //每天产量
            double dayCount = entry.getValue().stream().filter(record -> record.getCount() != null).mapToDouble(RecordWorkOrderLineDayCountEntity::getCount).sum();
            double dayUnqualified = entry.getValue().stream().filter(record -> record.getUnqualified() != null).mapToDouble(RecordWorkOrderLineDayCountEntity::getUnqualified).sum();
            dayCountMap.put(entry.getKey(), dayCount);
            dayUnqualifiedMap.put(entry.getKey(), dayUnqualified);
        }
        List<YieldRecordVO> rets = new ArrayList<>();
        //近一周的时间列表（包括当天）
        List<Date> dateList = DateUtil.getBetweenDate(startTimeDate, endTimeDate);
        for (Date date : dateList) {
            String dayTime = sdf.format(date);
            //每日产量
            double dayCount = dayCountMap.getOrDefault(dayTime, 0.0);
            double unqualifiedQuantity = dayUnqualifiedMap.getOrDefault(dayTime, 0.0);

            Double directRate = (dayCount + unqualifiedQuantity) == 0.0 ? null : MathUtil.divideDouble(dayCount, dayCount + unqualifiedQuantity, 3);

            rets.add(YieldRecordVO.builder().time(dayTime).directRate(directRate).build());
        }
        return ResponseData.success(rets);
    }

    /**
     * 产线数据在后台进行配置，包括产线展示顺序
     * 1.最左侧为产线名称；
     * 2.第二列为对应产线目前所有工单（生效，投产，挂起，完成）的完成数与总计划产品数（取产线上工单计划数量取和）占比；
     * 3.计划数：分配到当前产线的今日所有工单（生效，投产，挂起，完成）计划数据：
     * 4.投入数：今日对应产线投入量计数（统计产线特定工位的投入计算）
     * 5.在制数：投入数-产出数
     * 6.产出数：今日对应产线产出量计数（统计产下特定工位的产出计算）
     * 7.投产工单数：今日产线所有投产工单集合
     * 8.挂起工单数：今日产线所有挂起工单集合
     * 9.今日良率：产出数/（产出数+今日产线不良数）✖100%
     */
    @Override
    public ResponseData gridLineTodayData(Integer gridId, String lineIds) {
        if (StringUtils.isBlank(lineIds)) {
            return ResponseData.success(new ArrayList<>());
        }
        // 需要按顺序 lineIds 返回响应数据
        List<Integer> lineIdList = Arrays.stream(lineIds.split(Constants.SEP)).map(Integer::parseInt).collect(Collectors.toList());
        List<ProductionLineEntity> lines = lineService.lambdaQuery()
                .select(ProductionLineEntity::getProductionLineId, ProductionLineEntity::getName)
                .eq(ProductionLineEntity::getGid, gridId)
                .in(ProductionLineEntity::getProductionLineId, lineIdList)
                .list();
        Map<Integer, String> lineIdNameMap = lines.stream().collect(Collectors.toMap(ProductionLineEntity::getProductionLineId, ProductionLineEntity::getName));
        Set<Integer> lineIdSet = lineIdNameMap.keySet();
        lineIdList = lineIdList.stream().filter(lineIdSet::contains).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(lineIdList)) {
            return ResponseData.success(new ArrayList<>());
        }

        // yyyy-MM-dd 00:00:00
        Date nowDate = DateUtil.formatToDate(new Date(), DateUtil.DATETIME_FORMAT_ZERO);
        // 产线总完成数 和 总计划数
        List<WorkOrderEntity> allWorkOrders = workOrderService.lambdaQuery()
                .select(WorkOrderEntity::getWorkOrderNumber, WorkOrderEntity::getState, WorkOrderEntity::getPlanQuantity, WorkOrderEntity::getLineId)
                .in(WorkOrderEntity::getLineId, lineIdList)
                .in(WorkOrderEntity::getState, Stream.of(WorkOrderStateEnum.RELEASED.getCode(), WorkOrderStateEnum.INVESTMENT.getCode(), WorkOrderStateEnum.HANG_UP.getCode(), WorkOrderStateEnum.FINISHED.getCode()).collect(Collectors.toList()))
                .list();
        Map<Integer, List<WorkOrderEntity>> lineIdWorkOrdersMap = allWorkOrders.stream().collect(Collectors.groupingBy(WorkOrderEntity::getLineId));

        List<RecordWorkOrderLineDayCountEntity> workOrderDayCounts = recordWorkOrderLineDayCountService.lambdaQuery()
                .in(RecordWorkOrderLineDayCountEntity::getLineId, lineIdList)
                .list();
        Map<Integer, List<RecordWorkOrderLineDayCountEntity>> lineIdDayCountMap = workOrderDayCounts.stream().collect(Collectors.groupingBy(RecordWorkOrderLineDayCountEntity::getLineId));

        // 计划数：分配到当前产线的今日所有工单（生效，投产，挂起，完成）计划数据
        List<WorkOrderPlanEntity> todayWorkOrderPlans = workOrderPlanService.lambdaQuery()
                .eq(WorkOrderPlanEntity::getTime, nowDate)
                .list();

        List<WorkOrderCountDTO> rets = new ArrayList<>();
        double allPlanQuantity, allCompleted, todayPlanQuantity, todayInput, todayCount, todayYield;
        long investmentStateCount, hangUpStateCount;
        for (Integer lineId : lineIdList) {
            // 总计划数
            List<WorkOrderEntity> lineAllWorkOrders = lineIdWorkOrdersMap.getOrDefault(lineId, new ArrayList<>());
            allPlanQuantity = lineAllWorkOrders.stream().mapToDouble(WorkOrderEntity::getPlanQuantity).sum();
            List<String> temWorkOrderNumbers = lineAllWorkOrders.stream().map(WorkOrderEntity::getWorkOrderNumber).collect(Collectors.toList());
            // 产线总完成数
            List<RecordWorkOrderLineDayCountEntity> lineAllWorkOrderDayCounts = lineIdDayCountMap.getOrDefault(lineId, new ArrayList<>());
            lineAllWorkOrderDayCounts = lineAllWorkOrderDayCounts.stream().filter(res -> temWorkOrderNumbers.contains(res.getWorkOrderNumber())).collect(Collectors.toList());
            allCompleted = lineAllWorkOrderDayCounts.stream().mapToDouble(RecordWorkOrderLineDayCountEntity::getCount).sum();
            // 计划数：分配到当前产线的今日所有工单（生效，投产，挂起，完成）计划数据
            List<WorkOrderPlanEntity> todayLineWorkPlans = todayWorkOrderPlans.stream().filter(res -> temWorkOrderNumbers.contains(res.getWorkOrderNumber())).collect(Collectors.toList());
            todayPlanQuantity = todayLineWorkPlans.stream().mapToDouble(WorkOrderPlanEntity::getPlanQuantity).sum();

            // 投入数：今日对应产线投入量计数（统计产线特定工位的投入计算）
            // 产出数：今日对应产线产出量计数（统计产下特定工位的产出计算）
            // 在制数：投入数-产出数
            List<RecordWorkOrderLineDayCountEntity> todayLineWorkOrderDayCounts = lineAllWorkOrderDayCounts.stream().filter(res -> res.getTime().equals(nowDate)).collect(Collectors.toList());
            todayInput = todayLineWorkOrderDayCounts.stream().mapToDouble(RecordWorkOrderLineDayCountEntity::getInput).sum();
            todayCount = todayLineWorkOrderDayCounts.stream().mapToDouble(RecordWorkOrderLineDayCountEntity::getCount).sum();
            double todayUnqualified = todayLineWorkOrderDayCounts.stream().mapToDouble(RecordWorkOrderLineDayCountEntity::getUnqualified).sum();

            // 投产工单数：产线所有投产状态工单数量
            investmentStateCount = lineAllWorkOrders.stream().filter(res -> WorkOrderStateEnum.INVESTMENT.getCode().equals(res.getState())).count();
            // 挂起工单数：产线所有挂起状态工单数量
            hangUpStateCount = lineAllWorkOrders.stream().filter(res -> WorkOrderStateEnum.HANG_UP.getCode().equals(res.getState())).count();

            // 今日良率：产出数 /（产出数+今日产线不良数）✖100%
            double sum = MathUtil.add(todayCount, todayUnqualified);
            todayYield = sum == 0 ? 0.0 : MathUtil.divideDouble(todayCount, sum, 4);

            rets.add(WorkOrderCountDTO.builder()
                    .lineId(lineId)
                    .lineName(lineIdNameMap.get(lineId))
                    .allCompleted(allCompleted)
                    .allPlanQuantity(allPlanQuantity)
                    .todayPlanQuantity(todayPlanQuantity)
                    .todayInput(todayInput)
                    .todayOutput(todayCount)
                    .todayProcess(MathUtil.sub(todayInput, todayCount))
                    .todayUnqualified(todayUnqualified)
                    .investmentStateCount(investmentStateCount)
                    .hangUpStateCount(hangUpStateCount)
                    .todayYield(todayYield)
                    .build());
        }
        return ResponseData.success(rets);
    }


}
