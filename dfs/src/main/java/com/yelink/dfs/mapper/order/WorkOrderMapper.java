package com.yelink.dfs.mapper.order;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yelink.dfs.entity.order.WorkOrderEntity;
import com.yelink.dfs.entity.order.dto.ExtendFieldDTO;
import com.yelink.dfs.entity.order.vo.PrintSourceOrderVO;
import com.yelink.dfs.entity.screen.vo.WorkOrderEntityVO;
import com.yelink.dfs.entity.statement.dto.WorkOrderTodayPlanSelectDTO;
import com.yelink.dfs.entity.statement.vo.WorkOrderDayPlanVO;
import com.yelink.dfscommon.dto.CommonTableDTO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * WorkOrderMapper
 *
 * <AUTHOR>
 * @Date 2021-03-11 10:18
 */
@Repository
public interface WorkOrderMapper extends BaseMapper<WorkOrderEntity> {

    /**
     * 工单管理列表获取父工单以及独立工单
     *
     * @param page
     * @param lineIds
     * @return
     */
    Page<WorkOrderEntity> getAppointWorkOrders(Page page, int[] lineIds);

    /**
     * 获取六个月内完成状态的工单
     *
     * @param productionLineId
     * @return
     */
    List<WorkOrderEntity> getFinishWorkOrder(Integer productionLineId);


    /**
     * 通过单号名称获取工单信息
     *
     * @param workOrderName
     * @return
     */
    WorkOrderEntity getWorkOrderByName(String workOrderName);

    /**
     * 通过工单号获取工单
     *
     * @param workOrderNumber
     * @return
     */
    WorkOrderEntity getByWorkOrderNumber(@Param("workOrderNumber") String workOrderNumber);

    /**
     * 通过工单号模糊查询工单号
     *
     * @param workOrderNumber
     * @return
     */
    List<PrintSourceOrderVO> getNumberListByWorkOrderNumber(@Param("workOrderNumber") String workOrderNumber);

    /**
     * 获取当月最后一条工单
     *
     * @return
     */
    WorkOrderEntity getMonthNewRecord();

    /**
     * 获取某状态、计划开始时间为当天的数据
     *
     * @return
     */
    List<WorkOrderEntity> getToDayRecords(Integer lineId, Integer state, String workOrderNumber,
                                          String workOrderName, String materialCode, String materialName);

    /**
     * 获取当前时间未来一周的数据
     *
     * @return
     */
    List<WorkOrderEntity> getFutureSevenDaysList();

    /**
     * 工单排程--删除倒排物料表
     */
    void dropScheduleReverseMaterialTable();

    /**
     * 工单排程--创建初始化倒排物料表
     */
    void createScheduleInitReverseMaterialTable();

    /**
     * 工单排程--创建初始化倒排物料表
     */
    void initScheduleMaterialCodeData(List<String> materialCodes);

    /**
     * 工单排程--倒排表新增字段
     */
    void addColumnToScheduleMaterialTable(String fieldName, String columnName);

    /**
     * 获取属于bom编码的原料编码，如果存在则按逗号拼接
     */
    String getScheduleFieldData(String bomCode, String fieldName);

    /**
     * 将原料编码更新到倒排表的新增字段中
     *
     * @param bomCode      bom编码
     * @param fieldName    新增的字段名
     * @param materialCode 原料编码
     */
    void updateScheduleFieldData(String bomCode, String fieldName, String materialCode);

    /**
     * 工单排程--获取倒排的拓展字段（中文名和英文名）
     *
     * @param tableSchema 数据库名
     * @return
     */
    List<CommonTableDTO> getScheduleReverseExtendFieldData(String tableSchema);

    /**
     * 获取生产工单删除时关联的dfs所有表
     *
     * @param tableSchema 数据库名
     * @return
     */
    List<CommonTableDTO> getRelatedDataTableByWorkOrder(String tableSchema);

    /**
     * 删除生产工单删除时关联的dfs所有表
     *
     * @param tableName 表名
     * @param columnName 字段名
     * @param value 值
     * @return
     */
    void deleteRelatedDataByWorkOrder(String tableName, String columnName, Object value);

    /**
     * 获取拓展字段（原料字段）
     *
     * @param
     * @return
     */
    List<Map<String, String>> getScheduleReverseMaterialConf(List<String> materialCodes);

    /**
     * 获取拓展列过滤后的物料编码列表
     */
    List<String> getScheduleReverseMaterialCode(@Param("extendMaterialList") List<ExtendFieldDTO> extendMaterialList);

    /**
     * *   // 分页获取设备的生产工单列表
     * *         // 条件如下：
     * *         // 1. 生产工单状态为生效、投产、挂起；
     * *         // 2. 生产工单状态为完成，完成时间为当天(0-24);
     *
     * @param objectPage
     * @param gridId
     * @return
     */
    Page<WorkOrderEntityVO> workOrderByPage(Page<Object> objectPage, Integer gridId, Date nowTime);

    /**
     * 统计设备下的生产工单数量情况：待生产
     * 统计条件如下：
     * 1. 生产工单状态为生效；
     *
     * @param gridId
     * @return
     */
    Integer producedTotal(Integer gridId);

    /**
     * 统计设备下的生产工单数量情况：生产中
     * 统计条件如下：
     * 1. 生产工单状态为投产；
     *
     * @param gridId
     * @return
     */
    Integer producingTotal(Integer gridId);

    /**
     * 统计设备下的生产工单数量情况：生产中、已完成
     * 统计条件如下：
     * 生产工单状态为完成，完成时间为当天(0-24);
     *
     * @param gridId
     * @return
     */
    Integer completedTotal(Integer gridId, Date nowTime);

    /**
     * 分页获取工单列表 --产线报工
     *
     * @param objectPage       分页对象
     * @param lineId           产线id
     * @param workCenterId     产线模型id
     * @param state            工单状态
     * @param workOrderNumber  工单编号
     * @param workOrderName    工单名称
     * @param materialCode     物料编码
     * @param materialName     物料名称
     * @param workOrderNumbers
     * @return
     */
    Page<WorkOrderEntity> selectListWorkOrderByPage(Page<Object> objectPage, Integer lineId, Integer teamId, Integer deviceId, Integer workCenterId, Integer state,
                                                    String workOrderNumber, String workOrderName, String materialCode, String materialName, String workCenterType, String assignmentState, Boolean isSubcontract, List<String> workOrderNumbers);

    /**
     * 分页获取工单列表 --订单报工
     *
     * @param objectPage  分页对象
     * @param procedureId 工序id
     * @return
     */
    Page<WorkOrderEntity> selectPageByProcedureId(Page<Object> objectPage, Integer procedureId, List<String> isolationIds, List<String> workCenterIds);

    /**
     * 统计产线的生产工单预计剩余加工时间
     *
     * @param lineId
     * @param state
     * @param excludeOrders
     * @return
     */
    Double getTheRestOfProductionTime(Integer lineId, Integer state, List<String> excludeOrders);

    /**
     * 获取工单的工艺工序的加工时长
     *
     * @param workOrderNumbers
     * @return
     */
    Double getProcessingHoursOfWorkOrder(List<String> workOrderNumbers);


    /**
     * 查询在这个时间段内有日计划的工单信息
     *
     * @param selectDTO
     * @param objectPage
     * @return
     */
    Page<WorkOrderDayPlanVO> workOrderDayPlan(@Param("selectDTO") WorkOrderTodayPlanSelectDTO selectDTO,
                                              Page<WorkOrderDayPlanVO> objectPage);

    /**
     * 工单直接关联设备 或 工单关联资源为设备(可以关联多个设备) 的工单
     *
     * @param states
     * @param deviceIds
     * @param page
     * @return
     */
    Page<WorkOrderEntity> pageRelateDeviceWorkOrder(@Param("states") List<Integer> states,
                                                    @Param("deviceIds") List<Integer> deviceIds,
                                                    Page<WorkOrderEntity> page);
    /**
     * 工单直接关联设备 或 工单关联资源为设备(可以关联多个设备) 的工单
     *
     * @param states 工单状态集合
     * @param deviceIds 设备id集合
     * @return 工单列表
     */
    List<WorkOrderEntity> pageRelateDeviceWorkOrder(@Param("states") List<Integer> states,
                                                    @Param("deviceIds") List<Integer> deviceIds);

    /**
     * 获取工单的metrics基础数据
     * @return
     */
    List<WorkOrderEntity> listMetrics();

    /**
     * 根据ID查询工单（不走mybatis plus缓存）
     * @param workOrderId
     * @return
     */
    WorkOrderEntity selectByIdNoCache(Integer workOrderId);

    Page<WorkOrderEntity> getList(String sql, Page<WorkOrderEntity> page);
//    Page<WorkOrderEntity> getList(WorkOrderQuerySqlDTO sqlDTO, Page<WorkOrderEntity> page);
}
