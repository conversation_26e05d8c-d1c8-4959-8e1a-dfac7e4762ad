package com.yelink.dfs.constant.event;

import lombok.Getter;

/**
 * 默认的告警分类
 * <AUTHOR>
 */
public enum DefaultEventDefinitionEnum {
    /**
     * 未知的类型
     */
    UNKNOWN("UNKNOWN", "未知类型", DefaultEventClassifyEnum.UNKNOWN)
    ;
    DefaultEventDefinitionEnum(String definitionCode, String definitionName, DefaultEventClassifyEnum defaultClassifyEnum) {
        this.definitionCode = definitionCode;
        this.definitionName = definitionName;
        this.defaultClassifyEnum = defaultClassifyEnum;
    }
    @Getter
    private final String definitionCode;
    @Getter
    private final String definitionName;
    @Getter
    private final DefaultEventClassifyEnum defaultClassifyEnum;
}
