package com.yelink.dfs.constant.product;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Description 工序工艺参数输入方式
 * @Date 2023/2/1 14:40
 */
public enum ProcessParameterInputModeEnum {

    /**
     * 工序检测项类型
     * DropDownAnOption--下拉单选、下拉多选--DropDownMultipleSelection、 manualFilling--输入
     */
    DROP_DOWN_AN_OPTION("dropDownAnOption", "下拉单选"),
    DROP_DOWN_MULTIPLE_SELECTION("dropDownMultipleSelection", "下拉多选"),
    MANUAL_FILLING("manualFilling", "输入"),
    ;

    /**
     * 字段code
     */
    private final String code;

    /**
     * 字段名称
     */
    private final String name;

    ProcessParameterInputModeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }


    public static String getNameByCode(String code) {
        if (code == null) {
            return null;
        }
        for (ProcessParameterInputModeEnum stateEnum : ProcessParameterInputModeEnum.values()) {
            if (stateEnum.code.equals(code)) {
                return stateEnum.name;
            }
        }
        return null;
    }

    public static String getCodeByName(String name) {
        if(name == null){
            return null;
        }
        for (ProcessParameterInputModeEnum stateEnum : ProcessParameterInputModeEnum.values()) {
            if (name.equals(stateEnum.name)) {
                return stateEnum.code;
            }
        }
        return null;
    }

    public static List<String> getNames() {
        ArrayList<String> names = new ArrayList<>();
        for (ProcessParameterInputModeEnum stateEnum : ProcessParameterInputModeEnum.values()) {
            names.add(stateEnum.name);
        }
        return names;
    }


}
