package com.yelink.dfs.constant.alarm;

/**
 * @Description: 通知方式
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021/3/6
 */
public enum NoticeStateEnum {

    /**
     * 类型
     */
    CREATE(1, "创建"),
    RELEASED(2, "生效"),
    DISCARD(3, "废除"),
    ;

    private Integer code;
    private String name;


    NoticeStateEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static String getNameByCode(Integer code) {
        for (NoticeStateEnum noticeEnum : NoticeStateEnum.values()) {
            if (noticeEnum.code.equals(code)) {
                return noticeEnum.getName();
            }
        }
        return null;
    }
}
