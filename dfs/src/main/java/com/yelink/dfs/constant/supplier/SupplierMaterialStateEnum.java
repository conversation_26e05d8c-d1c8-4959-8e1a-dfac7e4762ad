package com.yelink.dfs.constant.supplier;

public enum SupplierMaterialStateEnum {

    /**
     * 供应商物料状态
     * 1-创建 2-生效 3-停用 4-废弃
     */
    CREATE(1, "创建"),
    RELEASED(2, "生效"),
    UNABLE(3, "停用"),
    ABANDON(4, "废弃"),

    ;

    private int code;
    private String name;

    SupplierMaterialStateEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }

    public int getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static String getNameByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (SupplierMaterialStateEnum stateEnum : SupplierMaterialStateEnum.values()) {
            if (stateEnum.code == code) {
                return stateEnum.name;
            }
        }
        return null;
    }

    public static Integer getCodeByName(String name) {
        if (name == null) {
            return null;
        }
        for (SupplierMaterialStateEnum stateEnum : SupplierMaterialStateEnum.values()) {
            if (stateEnum.name.equals(name)) {
                return stateEnum.code;
            }
        }
        return null;
    }

    public static SupplierMaterialStateEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (SupplierMaterialStateEnum stateEnum : SupplierMaterialStateEnum.values()) {
            if (stateEnum.code == code) {
                return stateEnum;
            }
        }
        return null;
    }
}
