package com.yelink.dfs.constant.alarm;

import com.yelink.dfscommon.constant.ResponseException;
import lombok.Getter;

/**
 * 异常管理枚举
 * <AUTHOR>
 */
public enum ExceptionManagerEnum {
    /**
     * 告警
     */
    ALARM("alarm"),
    EVENT("event"),
    ;


    @Getter
    private final String name;

    ExceptionManagerEnum(String name) {
        this.name = name;
    }

    public static ExceptionManagerEnum getCodeByName(String name) {
        for (ExceptionManagerEnum e: ExceptionManagerEnum.values()) {
            if (name.equals(e.name)) {
                return e;
            }
        }
        throw new ResponseException("异常管理的枚举值有误");
    }
}
