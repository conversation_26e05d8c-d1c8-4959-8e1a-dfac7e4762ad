package com.yelink.dfs.constant.defect;


/**
 * @Description: 质检方案关联类型枚举
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2020/12/5
 */
public enum DefectRelatedTypeEnum {

    /**
     * 关联类型枚举
     */
    STATION("station", "工位"),
    DEVICE("device", "设备"),
    TEAM("team", "班组"),

    ;

    private String code;
    private String name;

    DefectRelatedTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static String getNameByCode(String code) {
        if (code == null) {
            return null;
        }
        for (DefectRelatedTypeEnum stateEnum : DefectRelatedTypeEnum.values()) {
            if (stateEnum.code.equals(code)) {
                return stateEnum.name;
            }
        }
        return null;
    }

    public static String getCodeByName(String name) {
        for (DefectRelatedTypeEnum stateEnum : DefectRelatedTypeEnum.values()) {
            if (name.equals(stateEnum.name)) {
                return stateEnum.code;
            }
        }
        return null;
    }
}
