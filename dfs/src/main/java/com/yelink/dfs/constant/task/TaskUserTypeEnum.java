package com.yelink.dfs.constant.task;


/**
 * @Description: 任务中心的用户类型表
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2024/12/16
 */
public enum TaskUserTypeEnum {

    PLAYER("player", "参与人"),
    DIRECTOR("director", "负责人"),
    ;

    private final String code;
    private final String name;

    TaskUserTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static String getNameByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (TaskUserTypeEnum categoryEnum : TaskUserTypeEnum.values()) {
            if (categoryEnum.code.equals(code)) {
                return categoryEnum.name;
            }
        }
        return null;
    }

    public static String getCodeByName(String name) {
        if(name == null){
            return null;
        }
        for (TaskUserTypeEnum categoryEnum : TaskUserTypeEnum.values()) {
            if (name.equals(categoryEnum.name)) {
                return categoryEnum.code;
            }
        }
        return null;
    }
}



