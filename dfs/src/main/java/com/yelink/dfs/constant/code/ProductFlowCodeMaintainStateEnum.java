package com.yelink.dfs.constant.code;

/**
 * <AUTHOR>
 * @Date 2022/4/1 9:50
 */
public enum ProductFlowCodeMaintainStateEnum {
    /**
     * 维修状态编码及描述
     */
    TRUE(0,"维修-合格"),
    FALSE(1,"维修-报废")
    ;

    private Integer code;
    private String name;

    ProductFlowCodeMaintainStateEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }

    public int getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static String getNameByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (ProductFlowCodeMaintainStateEnum stateEnum : ProductFlowCodeMaintainStateEnum.values()) {
            if (stateEnum.code.equals(code)) {
                return stateEnum.name;
            }
        }
        return null;
    }

    public static Integer getCodeByName(String name) {
        for (ProductFlowCodeMaintainStateEnum stateEnum : ProductFlowCodeMaintainStateEnum.values()) {
            if (name.equals(stateEnum.name)) {
                return stateEnum.code;
            }
        }
        return null;
    }
}
