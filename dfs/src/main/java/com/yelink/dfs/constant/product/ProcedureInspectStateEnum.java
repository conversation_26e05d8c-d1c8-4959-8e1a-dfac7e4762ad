package com.yelink.dfs.constant.product;

/**
 * <AUTHOR>
 * @Description 工序检测项的状态
 * @Date 2022/4/19 14:40
 */
public enum ProcedureInspectStateEnum {

    /**
     * 工序检测项状态
     * 1-创建 2-生效
     */
    CREATE("1", "创建"),
    RELEASED("2", "生效"),
    STOP_USING("3", "停用"),
    ABANDON("4", "废弃"),
    ;


    /**
     * 字段名
     */
    private String fieldEname;
    /**
     * 字段名称
     */
    private String fieldName;

    ProcedureInspectStateEnum(String fieldEname, String fieldName) {
        this.fieldEname = fieldEname;
        this.fieldName = fieldName;
    }

    public String getFieldEname() {
        return fieldEname;
    }

    public String getFieldName() {
        return fieldName;
    }


    public static String getNameByCode(String fieldEname) {
        if (fieldEname == null) {
            return null;
        }
        for (ProcedureInspectStateEnum stateEnum : ProcedureInspectStateEnum.values()) {
            if (stateEnum.fieldEname.equals(fieldEname)) {
                return stateEnum.fieldName;
            }
        }
        return null;
    }

    public static String getCodeByName(String fieldName) {
        for (ProcedureInspectStateEnum stateEnum : ProcedureInspectStateEnum.values()) {
            if (fieldName.equals(stateEnum.fieldName)) {
                return stateEnum.fieldEname;
            }
        }
        return null;
    }

}
