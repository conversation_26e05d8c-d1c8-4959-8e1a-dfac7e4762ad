package com.yelink.dfs.constant.model;


import org.apache.commons.lang3.StringUtils;

/**
 * @description: 模型ID
 * @author: zhuangwq
 * @create: 2021-06-01 17:09
 **/
public enum ModelIdEnum {


    /*##################################杨子江设备&产线#####################################################*/
    /**
     * 杨子江---设备模型ID
     */
    WASHING(6172, "bottleWashingMachine", "洗瓶机"),
    BAKING(6173, "oven", "烘箱"),
    CANNED(6174, "fillingMachine", "灌装机"),
    BLENDING_SYSTEM(6175, "liquidDistributionSystem", "配液系统"),
    STERILIZATION(6176, "waterBathSterilizationCabinet", "水浴灭菌柜"),
    LAMP_INSPECTION(6177, "lampInspectionMachine", "灯检机"),
    LABELING(6178, "labelingMachine", "贴标机"),
    LIQUID(6179, "liquidDistributionSystem1500", "配液系统1500"),
    BUBBLE_CAP_PACKING(6181, "blisterPackagingMachine", "泡罩包装机"),
    PACKING(6182, "strappingMachine", "捆包机"),
    AIR_STERILIZATION(6183, "ventilationSterilizationCabinet", "通风灭菌柜"),
    LEAK_DETECTOR(6184, "leakDetector", "捡漏机"),
    ELECTRONIC_TAG_CODE(6185, "electronicSupervisionCode", "电子监管码"),
    CARTON_MACHINE(6186, "cartonMachine", "装盒机"),
    PACKING_MACHINE(6187, "packingMachine", "装箱机"),
    AIR_CONDITIONING_SYSTEM(6188, "airConditioningSystem", "空调系统"),
//    /**
//     * 产线模型ID  测试库配置
//     */
//    WASHING_BAKING_CANNED_LINE(6186,,"洗烘灌产线"),
//    STERILIZATION_LINE(6191,,"灭菌产线"),
//    LAMP_INSPECTION_LINE(6194,,"灯检产线"),
//    PACKING_LINE(6198,,"包装产线"),
//    BLENDING_SYSTEM_LINE(6204,,"配液系统产线"),

    /**
     * 产线模型ID  开发库配置---扬子江
     */
    BLENDING_SYSTEM_LINE(6260, "pycx", "配液产线"),
    WASHING_BAKING_CANNED_LINE(6263, "xhgcx", "洗烘灌产线"),
    STERILIZATION_LINE(6268, "mjcx", "灭菌产线"),
    LAMP_INSPECTION_LINE(6271, "djcx", "灯检产线"),
    PACKING_LINE(6276, "bzcx", "包装产线"),

    FRONT_LINE(6349, "pfxhgfmj", "前道工序"),
    AFTER_LINE_LAMPEXAM(6356, "djjltc", "后道灯检"),
    AFTER_LINE_PACKING(6361, "bzcx", "后道包装"),

    /*##################################浙江LCD设备&产线#####################################################*/

    /**
     * 浙江LCD-----设备模型ID
     */
    LCD_AUTOMATIC_PULL_UP_MACHINE(6209, "automaticPullUpMachine", "LCD自动上料机"),
    LCD_TERMINAL_CLEANING_MACHINE(6210, "terminalCleaningMachine", "LCD端子清洗机"),
    FULL_AUTOMATIC_COG_EQUIPMENT(6211, "automaticCogEquipment", "全自动COG设备"),
    FULL_AUTOMATIC_FOG_EQUIPMENT(6327, "automaticFogEquipment", "全自动FOG设备"),
    ET_FOG_ELECTRICAL_MEASUREMENT(6212, "etFogElectricalMeasurement", "ET FOG电测"),
    AUTOMATIC_THREE_POINT_IN_ONE_DISPENSING_MACHINE(6213, "automaticDispensingMachine", "全自动三点合一点胶机"),
    AUTOMATIC_DRYING_MACHINE(6214, "automaticDryingMachine", "全自动晾干机"),
    BL_ASSEMBLY(6215, "blAssembly", "BL组装"),
    AUTOMATIC_PASTING_YELLOW_GLUE_AND_EASY_TEARING_PASTING_MACHINE(6216, "easyTearingPastingMachine", "自动贴黄胶+易撕贴机"),
    ELECTRICAL_MEASUREMENT_OF_ET_FINISHED_PRODUCTS(6217, "etFinishedProducts", "ET成品电测"),
    QC_APPEARANCE_INSPECTION(6218, "qcAppearanceInspection", "QC外观检测"),


    /**
     * ################粤深钢设备类型##########
     */
    ELECTRIC_FURNACE_OLD(6383, "electricFurnaceOld", "旧电炉设备"),
    ELECTRIC_FURNACE_NEW(6383, "electricFurnaceNew", "新电炉设备"),
    ELECTRONIC_SCALE(6384, "electronicScale", "电子秤"),
    PARALLEL_FEEDING(6385, "parallelFeeding", "吹氧喷粉"),
    OXYGEN_BLOW_POWDER_SPRAY(6386, "oxygenBlowPowderSpray", "平行送料"),
    MOLTEN_STEEL_THERMOMETER(6387, "moltenSteelThermometer", "钢水测温仪"),

    /**
     * ################奥德##########
     */
    INFRARED_COUNTER(6339, "infraredCounter", "红外计数器"),


    /**
     * 浙江LCD-----产线模型id
     */
    LCD_LINE(6492, "", "单颗IC线"),


    /**
     * ############奥维斯#####################
     */

    WEIGHING_MACHINE(6331, "weighingMachine", "称重设备");
    private int id;
    private String code;
    private String modelName;

    ModelIdEnum(int id, String code, String modelName) {
        this.id = id;
        this.code = code;
        this.modelName = modelName;
    }

    public int getId() {
        return id;
    }

    public String getModelName() {
        return modelName;
    }

    public String getCode() {
        return code;
    }

    public static String getModelNameById(Integer id) {
        if (id == null) {
            return null;
        }
        for (ModelIdEnum modelIdEnum : ModelIdEnum.values()) {
            if (modelIdEnum.id == id) {
                return modelIdEnum.modelName;
            }
        }
        return null;
    }

    public static String getModelNameByCode(String code) {
        if (StringUtils.isEmpty(code)) {
            return null;
        }
        for (ModelIdEnum modelIdEnum : ModelIdEnum.values()) {
            if (modelIdEnum.code == code) {
                return modelIdEnum.modelName;
            }
        }
        return null;
    }


}
