package com.yelink.dfs.constant.defect;


/**
 * @Description: 故障类型枚举
 * @Author: zheng<PERSON>
 * @Date: 2020/12/5
 */
public enum FaultTypeEnum {

    /**
     * 故障类型枚举
     */
    CRAFT("craft", "工艺"),
    MATERIAL("material", "材料"),
    ATTACH("attach", "贴片"),
    STRUCTURE("structure", "结构"),
    OTHER("other", "其他");

    private String code;
    private String name;

    FaultTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static String getNameByCode(String code) {
        if (code == null) {
            return null;
        }
        for (FaultTypeEnum stateEnum : FaultTypeEnum.values()) {
            if (stateEnum.code.equals(code)) {
                return stateEnum.name;
            }
        }
        return null;
    }

    public static String getCodeByName(String name) {
        for (FaultTypeEnum stateEnum : FaultTypeEnum.values()) {
            if (name.equals(stateEnum.name)) {
                return stateEnum.code;
            }
        }
        return null;
    }
}
