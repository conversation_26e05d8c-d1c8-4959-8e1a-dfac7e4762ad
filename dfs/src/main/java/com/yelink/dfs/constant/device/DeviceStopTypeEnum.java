package com.yelink.dfs.constant.device;

import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * @description: 设备停机类型
 * @author: shuang
 * @time: 2020/6/22
 */
public enum DeviceStopTypeEnum {


    /**
     *
     */
    DEVICE_STOP_EAT("planned", "eat", "停机吃饭"),
    DEVICE_STOP_REST("planned", "rest", "停机休息"),
    DEVICE_STOP_MAINTAIN("planned", "maintain", "维护保养"),
    DEVICE_STOP_POWER_MAINTAIN("planned", "powerMaintain", "动力保养"),
    DEVICE_STOP_MEETING("planned", "meeting", "计划会议"),
    DEVICE_STOP_TRAIN("planned", "train", "培训"),
    DEVICE_STOP_CRAFT_WAIT("planned", "craftWait", "工艺等待"),
    DEVICE_STOP_UNPLANNED("unplanned", "unplanned", "计划外部因素停机"),
    DEVICE_STOP_CRAFT_FAULT("fault", "fault", "故障停机"),
    DEVICE_STOP_CRAFT_BROKEN("broken", "broken", "损坏停机"),
    DEVICE_STOP_CRAFT_ADJUST("adjust", "adjust", "调整停机");


    private String type;
    private String code;
    private String name;

    DeviceStopTypeEnum(String type, String code, String name) {
        this.type = type;
        this.code = code;
        this.name = name;
    }

    public String getType() {
        return type;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static String getNameByType(String code) {
        if (StringUtils.isBlank(code)) {
            return null;
        }
        for (DeviceStopTypeEnum typeEnum : DeviceStopTypeEnum.values()) {
            if (typeEnum.code.equals(code)) {
                return typeEnum.name;
            }
        }
        return null;
    }

    public static List<DeviceStopTypeEnum> getPlanned() {
        List<DeviceStopTypeEnum> enums = new ArrayList<>();
        for (DeviceStopTypeEnum typeEnum : DeviceStopTypeEnum.values()) {
            if ("planned".equals(typeEnum.type)) {
                enums.add(typeEnum);
            }
        }
        return enums;
    }

    public static List<DeviceStopTypeEnum> getUnplanned() {
        List<DeviceStopTypeEnum> enums = new ArrayList<>();
        for (DeviceStopTypeEnum typeEnum : DeviceStopTypeEnum.values()) {
            if ("unplanned".equals(typeEnum.type)) {
                enums.add(typeEnum);
            }
        }
        return enums;
    }
}
