package com.yelink.dfs.constant.node;


/**
 * <AUTHOR>
 * 节点状态
 * @Date 2022/4/24 10:00
 */
public enum NodeReportStateEnum {

    /**
     * 节点状态
     * 1-创建 2-生效 3-作废
     */
//    NOT_STARTED(1, "未开始"),
    IN_PROGRESS(1, "正常进行中"),
    DELAY_IN_PROGRESS(2, "延期进行中"),
    COMPLETED(3, "正常完成"),
    DELAY_COMPLETED(4, "延期完成"),
    
    ;

    private Integer code;
    private String name;

    NodeReportStateEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static String getNameByCode(Integer code) {
        for (NodeReportStateEnum stateEnum : NodeReportStateEnum.values()) {
            if (stateEnum.code.equals(code)) {
                return stateEnum.name;
            }
        }
        return null;
    }

    public static Integer getCodeByName(String name) {
        for (NodeReportStateEnum stateEnum : NodeReportStateEnum.values()) {
            if (stateEnum.name.equals(name)) {
                return stateEnum.code;
            }
        }
        return null;
    }
}
