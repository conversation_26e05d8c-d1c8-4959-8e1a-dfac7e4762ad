package com.yelink.dfs.constant.product;


import com.baomidou.mybatisplus.annotation.EnumValue;
import org.apache.commons.lang3.StringUtils;

/**
 * @Description: 特征参数状态枚举
 * @Author: zhuangwq
 * @Date: 2021/04/08
 */
public enum AuxiliaryAttrStateEnum {

    /**
     * 物料/bom状态
     * 1-创建 2-生效 3-停用 4-废弃
     */
    CREATE(1, "创建"),
    RELEASED(2, "生效"),
    // 该版本暂时注释该状态，待 数据分析 功能开发后开放
//    STOP_USING(3, "停用"),
//    ABANDON(4, "废弃")
    ;

    @EnumValue
    private int code;
    private String name;

    AuxiliaryAttrStateEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }

    public int getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static String getNameByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (AuxiliaryAttrStateEnum stateEnum : AuxiliaryAttrStateEnum.values()) {
            if (stateEnum.code == code) {
                return stateEnum.name;
            }
        }
        return null;
    }

    public static Integer getCodeByName(String name) {
        if (StringUtils.isBlank(name)) {
            return null;
        }
        for (AuxiliaryAttrStateEnum stateEnum : AuxiliaryAttrStateEnum.values()) {
            if (stateEnum.name.equals(name)) {
                return stateEnum.code;
            }
        }
        return null;
    }

}
