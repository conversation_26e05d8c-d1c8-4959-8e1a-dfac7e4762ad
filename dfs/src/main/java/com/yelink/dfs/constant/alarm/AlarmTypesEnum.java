package com.yelink.dfs.constant.alarm;

import com.yelink.dfscommon.entity.CommonEnumInterface;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @description 告警恢复类型枚举
 * @Date 2021/11/2 10:44
 */
public enum AlarmTypesEnum implements CommonEnumInterface {

    /**
     * 告警类型
     */
    FAULT_ALARM("faultAlarm", "故障告警"),
    RECOVERY_ALARM("recoveryAlarm", "恢复告警");


    private final String code;
    private final String name;

    AlarmTypesEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    @Override
    public String getCode() {
        return code;
    }

    @Override
    public String getName() {
        return name;
    }

    public static String getNameByCode(String code) {
        if (StringUtils.isBlank(code)) {
            return null;
        }
        for (AlarmTypesEnum alarmTypesEnum : AlarmTypesEnum.values()) {
            if (alarmTypesEnum.getCode().equals(code)) {
                return alarmTypesEnum.getName();
            }
        }
        return null;
    }

    public static String getCodeByName(String name) {
        for (AlarmTypesEnum alarmTypesEnum : AlarmTypesEnum.values()) {
            if (name.equals(alarmTypesEnum.name)) {
                return alarmTypesEnum.code;
            }
        }
        return null;
    }
}
