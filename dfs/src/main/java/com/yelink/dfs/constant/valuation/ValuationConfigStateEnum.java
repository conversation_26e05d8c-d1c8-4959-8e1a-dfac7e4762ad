package com.yelink.dfs.constant.valuation;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.yelink.dfscommon.entity.CommonEnumInterface;
import lombok.Getter;

/**
 * 工资配置 状态
 * <AUTHOR>
 */
public enum ValuationConfigStateEnum implements CommonEnumInterface {


    /**
     *
     */
    USED(1, "生效"),
    FAIL(0, "失效"),
    ;
    @Getter
    @EnumValue
    private final Integer code;
    @Getter
    private final String name;

    ValuationConfigStateEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }
    public static ValuationConfigStateEnum fromCode(Integer code) {
        for(ValuationConfigStateEnum e : ValuationConfigStateEnum.values()) {
            if(e.code.equals(code)) {
                return e;
            }
        }
        return null;
    }
}
