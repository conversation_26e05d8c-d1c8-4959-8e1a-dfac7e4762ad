package com.yelink.dfs.constant.node;

import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * 上报单据
 * @Date 2022/4/24 10:02
 */
public enum ReportBillTypeEnum {

    /**
     * 类型
     */
    PRODUCTION_ORDER("productionOrder", "生产订单"),
    SALE_ORDER("saleOrder", "销售订单"),
    WORK_ORDER("workOrder", "生产工单"),
    PURCHASE_ORDER("purchaseOrder", "采购订单"),
    WAREHOUSING_ORDER("warehousingOrder", "入库单"),
    INCOMING_INSPECTION("incomingInspection", "来料检验单"),
    INVOICE("invoice", "发货单"),


    ;

    private String type;
    private String name;


    ReportBillTypeEnum(String type, String name) {
        this.type = type;
        this.name = name;
    }

    public String getName() {
        return name;
    }

    public String getType() {
        return type;
    }

    public static String getNameByCode(String type) {
        if (StringUtils.isBlank(type)) {
            return null;
        }
        for (ReportBillTypeEnum reportNodeTypeEnum : ReportBillTypeEnum.values()) {
            if (type.equals(reportNodeTypeEnum.type)) {
                return reportNodeTypeEnum.getName();
            }
        }
        return null;
    }
}
