package com.yelink.dfs.constant.common;

/**
 * 我的方案默认方案名称
 * <AUTHOR>
 * @date 2022-09-29
 */
public enum ColumnConfigurationTypeEnum {
    /**
     * 我的方案默认方案名称
     */
    PLANNING_PERSONNEL("planningPersonnel", "计划人员"),
    PRODUCTION_PERSONNEL("productionPersonnel", "生产人员"),

            ;

    private String code;
    private String name;

    ColumnConfigurationTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getType() {
        return name;
    }

    public static String getNameByCode(String code) {
        if (code == null) {
            return null;
        }
        for (ColumnConfigurationTypeEnum en : ColumnConfigurationTypeEnum.values()) {
            if (en.code.equals(code)) {
                return en.name;
            }
        }
        return null;
    }

    public static String getCodeByName(String name) {
        if(name == null){
            return null;
        }
        for (ColumnConfigurationTypeEnum en : ColumnConfigurationTypeEnum.values()) {
            if (name.equals(en.name)) {
                return en.code;
            }
        }
        return null;
    }

}
