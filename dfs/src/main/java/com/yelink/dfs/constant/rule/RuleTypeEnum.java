package com.yelink.dfs.constant.rule;

import com.yelink.dfs.entity.rule.RuleTypeConfigEntity;
import com.yelink.dfs.service.rule.RuleTypeConfigService;
import com.yelink.dfs.utils.SpringUtil;
import lombok.Getter;

import java.util.List;

/**
 * 编码规则--模块枚举
 * 注意：此类已改为从数据库获取配置，不再硬编码
 */
@Getter
public class RuleTypeEnum {

    private String moduleName;
    private String type;

    private RuleTypeEnum(String moduleName, String type) {
        this.moduleName = moduleName;
        this.type = type;
    }

    /**
     * 根据类型获取枚举
     */
    public static RuleTypeEnum getEnumByType(String type) {
        if (type == null) {
            return null;
        }
        RuleTypeConfigService service = SpringUtil.getBean(RuleTypeConfigService.class);
        RuleTypeConfigEntity config = service.getByType(type);
        if (config == null) {
            return null;
        }
        return new RuleTypeEnum(config.getModuleName(), config.getType());
    }

    /**
     * 根据类型获取模块名称
     */
    public static String getNameByType(String type) {
        if (type == null) {
            return null;
        }
        RuleTypeConfigService service = SpringUtil.getBean(RuleTypeConfigService.class);
        return service.getModuleNameByType(type);
    }

    /**
     * 获取所有规则类型
     */
    public static List<RuleTypeConfigEntity> values() {
        RuleTypeConfigService service = SpringUtil.getBean(RuleTypeConfigService.class);
        return service.getAllRuleTypes();
    }
}
