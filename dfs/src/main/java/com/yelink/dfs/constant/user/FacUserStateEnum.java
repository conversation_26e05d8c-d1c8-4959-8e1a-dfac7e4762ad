package com.yelink.dfs.constant.user;

/**
 * <AUTHOR>
 * 工位人员状态
 * @Date 2021/4/27 10:33
 */
public enum FacUserStateEnum {
    /**
     * 状态编码及描述
     * 状态（创建1-批准)
     */
    START(0, "开工"),
    FINISH(1, "完工");

    private int code;
    private String name;

    FacUserStateEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }

    public int getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static String getNameByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (FacUserStateEnum stateEnum : FacUserStateEnum.values()) {
            if (stateEnum.code == code) {
                return stateEnum.name;
            }
        }
        return null;
    }
}
