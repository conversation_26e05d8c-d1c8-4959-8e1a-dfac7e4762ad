package com.yelink.dfs.constant.notice;

/**
 * @description: 消息通知触发方式
 * @author: zhuang
 * @time: 2021/5/28
 */
public enum NoticeTriggerModeEnum {

    /**
     * 消息通知触发方式
     */
    MANUAL("manual", "手动"),
    AUTO("auto", "自动");

    private String code;
    private String name;

    NoticeTriggerModeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static String getNameByCode(String code) {
        for (NoticeTriggerModeEnum alarmDealStateEnum : NoticeTriggerModeEnum.values()) {
            if (alarmDealStateEnum.code.equals(code)) {
                return alarmDealStateEnum.name;
            }
        }
        return null;
    }


}
