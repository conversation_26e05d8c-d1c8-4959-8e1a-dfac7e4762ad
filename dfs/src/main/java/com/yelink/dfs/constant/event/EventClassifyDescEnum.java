package com.yelink.dfs.constant.event;


/**
 * <AUTHOR>
 * @description 事件分类详细信息字段枚举
 * @Date 2021/11/2 10:58
 */
public enum EventClassifyDescEnum {

    /**
     * 事件详细信息字段
     */

    GRID_ID("grid", "gridId", "车间Id"),
    GRID_CODE("grid", "gridCode", "车间编号"),
    GRID_NAME("grid", "gridName", "车间名称"),
    WORK_CENTER_NAME("workCenter", "workCenterName", "工作中心名称"),
    WORK_CENTER_CODE("workCenter", "workCenterCode", "工作中心编号"),
    TEAM_NAME("team", "teamName", "班组名称"),
    TEAM_CODE("team", "teamCode", "班组编号"),
    LINE_ID("line", "lineId", "制造单元Id"),
    LINE_NAME("line", "lineName", "制造单元名称"),
    LINE_CODE("line", "lineCode", "制造单元编号"),
    DEVICE_ID("device", "deviceId", "设备Id"),
    DEVICE_CODE("device", "deviceCode", "设备编号"),
    DEVICE_NAME("device", "deviceName", "设备名称"),
    WORK_ORDER_ID("workOrder", "workOrderId", "工单Id"),
    WORK_ORDER_NUMBER("workOrder", "workOrderNumber", "工单编号"),
    WORK_ORDER_NAME("workOrder", "workOrderName", "工单名称"),
    INFLUENCE_WORK_HOUR("workOrder", "influenceWorkHour", "影响工时"),
    PRODUCT_ORDER_NUMBER("productOrder", "productOrderNumber", "生产订单编号"),
    SALE_ORDER_NUMBER("saleOrder", "saleOrderNumber", "销售订单编号"),
    MATERIAL_ID("product", "materialId", "物料Id"),
    MATERIAL_NAME("product", "materialName", "物料名称"),
    MATERIAL_CODE("product", "materialCode", "物料编号"),
    MATERIAL_STANDARD("product", "materialStandard", "物料规格"),
    MATERIAL_BATCH("product", "materialBatch", "物料批次"),
    ABNORMAL_INFORMATION("abnormalInformation", "abnormalInformation", "异常信息"),
    REMARK("remarks", "remarks", "备注"),
    ;


    private String model;
    private String ename;
    private String name;


    EventClassifyDescEnum(String model, String ename, String name) {
        this.model = model;
        this.ename = ename;
        this.name = name;
    }

    public String getEname() {
        return ename;
    }

    public String getModel() {
        return model;
    }

    public String getName() {
        return name;
    }


    public static EventClassifyDescEnum getEnumByEname(String ename) {
        for (EventClassifyDescEnum alarmTypeDescEnum : EventClassifyDescEnum.values()) {
            if (alarmTypeDescEnum.getEname().equals(ename)) {
                return alarmTypeDescEnum;
            }
        }
        return null;
    }

    public static EventClassifyDescEnum getEnumByName(String name) {
        for (EventClassifyDescEnum alarmTypeDescEnum : EventClassifyDescEnum.values()) {
            if (alarmTypeDescEnum.getName().equals(name)) {
                return alarmTypeDescEnum;
            }
        }
        return null;
    }

    public static String getModelByEname(String ename) {
        for (EventClassifyDescEnum alarmTypeDescEnum : EventClassifyDescEnum.values()) {
            if (alarmTypeDescEnum.getEname().equals(ename)) {
                return alarmTypeDescEnum.getModel();
            }
        }
        return null;
    }
}
