package com.yelink.dfs.constant.common.config;

import org.apache.commons.lang3.StringUtils;

/**
 * 下推类型
 *
 * <AUTHOR>
 * @Date 2022/9/20 17:20
 */
public enum PushDownTypeEnum {
    /**
     * 跳转页面、按bom下推、合单下推、按bom合单下推、按工艺路线下推、按工艺路线合单下推
     */
    JUMP_PAGE("jumpPage", "跳转新页面方式"),
    JUMP_PAGE_OUTPUT_PICKING_PRODUCT("jumpPageOutputPickingProduct", "生产工单-生产出入库_生产领料出库单跳转新页面方式"),
    JUMP_PAGE_WORK_ORDER_SUPPLEMENT("jumpPageWorkOrderSupplement", "生产工单-生产出入库_生产补料出库单跳转新页面方式"),
    JUMP_PAGE_PRODUCTION_RETURN_RECEIPT("jumpPageProductionReturnReceipt", "生产工单-生产出入库_生产退料入库单跳转新页面方式"),
    JUMP_PAGE_PRODUCTION_IN("jumpPageProductionIn", "生产工单-生产出入库_生产入库单跳转新页面方式"),

    JUMP_PAGE_PURCHASE_IN("jumpPagePurchaseIn", "采购收货-采购出入库_采购入库单跳转新页面方式"),
    JUMP_PAGE_RETURN_ORDER("jumpPageReturnOrder", "采购收货-采购退料_采购采购退料单跳转新页面方式"),

    JUMP_PAGE_SALE_OUT("jumpPageSaleOut", "销售出入库-销售出库单_跳转新页面方式"),
    JUMP_PAGE_SALES_RETURN_RECEIPT("jumpPageSalesReturnReceipt", "销售出入库-销售退货入库单_跳转新页面方式"),
    BOM("bom", "按bom下推"),
    MERGE_ORDER("mergeOrder", "合单下推"),
    BOM_MERGE_ORDER("bomMergeOrder", "按bom合单下推"),
    CRAFT_ROUTE("craftRoute", "按工艺路线下推"),
    CRAFT_ROUTE_MERGE_ORDER("craftRouteMergeOrder", "按工艺路线合单下推"),
    BOM_AUTO("bomAuto", "按BOM自动下推"),
    CRAFT_ROUTE_AUTO("craftRouteAuto", "按工艺路线自动下推"),
    BOM_BATCH("bomBatch", "按BOM批量下推"),
    CRAFT_ROUTE_BATCH("craftRouteBatch", "按工艺路线批量下推"),

    ;

    private final String typeCode;
    private final String typeName;

    PushDownTypeEnum(String typeCode, String typeName) {
        this.typeCode = typeCode;
        this.typeName = typeName;
    }

    public String getTypeCode() {
        return typeCode;
    }

    public String getTypeName() {
        return typeName;
    }


    public static String getNameByCode(String code) {
        if (StringUtils.isNotBlank(code)) {
            for (PushDownTypeEnum typeEnum : PushDownTypeEnum.values()) {
                if (typeEnum.getTypeCode().equals(code)) {
                    return typeEnum.getTypeName();
                }
            }
        }
        return null;
    }

    public static String getCodeByName(String name) {
        if (StringUtils.isNotBlank(name)) {
            for (PushDownTypeEnum typeEnum : PushDownTypeEnum.values()) {
                if (typeEnum.getTypeName().equals(name)) {
                    return typeEnum.getTypeCode();
                }
            }
        }
        return null;
    }
}
