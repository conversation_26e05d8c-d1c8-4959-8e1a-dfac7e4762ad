package com.yelink.dfs.constant.furnace;

/**
 * <AUTHOR>
 * @Date 2022/3/29 16:44
 */
public enum FurnaceExportReportTypeEnum {

    /**
     * 炉次报表导出
     */
    SPECTROGRAPH("spectrograph", "光谱仪"),
    ELECTRIC_FURNACE("electricFurnace", "电炉"),
    REFINING_FURNACE("refiningFurnace", "精炼炉"),
    ;

    private String exportType;
    private String typeName;

    FurnaceExportReportTypeEnum(String exportType, String typeName) {
        this.exportType = exportType;
        this.typeName = typeName;
    }

    public String getExportType() {
        return exportType;
    }

    public String getTypeName() {
        return typeName;
    }

    public static String getTypeNameByType(String exportType) {
        for (FurnaceExportReportTypeEnum typeEnum : FurnaceExportReportTypeEnum.values()) {
            if (typeEnum.exportType.equals(exportType)) {
                return typeEnum.typeName;
            }
        }
        return null;
    }


}
