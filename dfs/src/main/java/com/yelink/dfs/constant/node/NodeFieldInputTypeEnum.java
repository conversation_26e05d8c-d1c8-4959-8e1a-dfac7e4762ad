package com.yelink.dfs.constant.node;

import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * 节点字段输入框类型
 * @Date 2022/4/24 10:00
 */
public enum NodeFieldInputTypeEnum {

    /**
     * 节点字段数据源
     */
    TEXT("text", "文本输入"),
    SELECT("select", "下拉选择"),
    DATE("date", "日期选择"),
    TIME("time", "时间选择"),
    DATETIME("datetime", "日期时间选择"),

    ;

    private String code;
    private String name;

    NodeFieldInputTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static String getNameByCode(String code) {
        if (code == null) {
            return null;
        }
        for (NodeFieldInputTypeEnum stateEnum : NodeFieldInputTypeEnum.values()) {
            if (stateEnum.code.equals(code)) {
                return stateEnum.name;
            }
        }
        return null;
    }

    public static String getCodeByName(String name) {
        if (StringUtils.isBlank(name)) {
            return null;
        }
        for (NodeFieldInputTypeEnum stateEnum : NodeFieldInputTypeEnum.values()) {
            if (stateEnum.name.equals(name)) {
                return stateEnum.code;
            }
        }
        return null;
    }
}
