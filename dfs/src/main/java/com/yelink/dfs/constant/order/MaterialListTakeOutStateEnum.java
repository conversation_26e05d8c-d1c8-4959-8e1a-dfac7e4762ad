package com.yelink.dfs.constant.order;


import java.util.Arrays;

/**
 * @Description: 生产订单用料清单领料状态枚举
 * @Author: zheng<PERSON>
 * @Date: 2020/12/5
 */
public enum MaterialListTakeOutStateEnum {

    /**
     * 状态编码及描述
     * 1-未领料，2-部分领料 3-已领料
     */
    UNPICKED_MATERIALS(1, "未领料"),
    PARTIAL_PICKED_MATERIALS(2, "部分领料"),
    PICKED_MATERIALS(3, "已领料");

    private int code;
    private String name;

    MaterialListTakeOutStateEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }

    public int getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
    public static Object getByName(String stateName) {
        if(stateName == null){
            return null;
        }
        return Arrays.stream(MaterialListTakeOutStateEnum.values()).filter(v -> stateName.equals(v.getName())).findFirst().orElse(null);
    }

    public static String getNameByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (MaterialListTakeOutStateEnum stateEnum : MaterialListTakeOutStateEnum.values()) {
            if (stateEnum.code == code) {
                return stateEnum.name;
            }
        }
        return null;
    }

    public static Integer getCodeByName(String name) {
        if(name == null){
            return null;
        }
        for (MaterialListTakeOutStateEnum stateEnum : MaterialListTakeOutStateEnum.values()) {
            if (name.equals(stateEnum.name)) {
                return stateEnum.code;
            }
        }
        return null;
    }
}
