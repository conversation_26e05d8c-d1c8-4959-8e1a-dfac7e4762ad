package com.yelink.dfs.constant.alarm;

/**
 * @description: 告警相关数据来源枚举
 * @author: zhuang
 * @time: 2021/5/28
 */
public enum AlarmAddTypeEnum {

    /**
     * 告警数据来源
     */
    MANUAL("manual", "手工录入"),
    AUTO("auto", "系统生成");

    private String code;
    private String name;

    AlarmAddTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static AlarmAddTypeEnum getNameByCode(String code) {
        for (AlarmAddTypeEnum alarmDealStateEnum : AlarmAddTypeEnum.values()) {
            if (alarmDealStateEnum.code.equals(code)) {
                return alarmDealStateEnum;
            }
        }
        return null;
    }


}
