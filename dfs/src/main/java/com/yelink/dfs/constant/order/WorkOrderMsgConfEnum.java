package com.yelink.dfs.constant.order;

import com.yelink.dfs.entity.order.WorkOrderEntity;
import com.yelink.dfs.open.v1.workOrder.dto.WorkOrderDetailDTO;
import org.apache.commons.lang3.StringUtils;

import java.util.List;


/**
 * @Description: 工单发送消息枚举
 * @Author: zengzhengfu
 * @Date: 2024/7/29
 */
public enum WorkOrderMsgConfEnum {

    SALE_ORDER("saleOrder", "销售订单") {
        @Override
        public void removeMsg(WorkOrderEntity entity) {
            entity.setSaleOrderList(null);
        }

        @Override
        public void removeCondition(WorkOrderDetailDTO dto, List<String> allRemove) {
            if (allRemove.contains(SALE_ORDER.code) && allRemove.contains(PRODUCT_ORDER.code)) {
                dto.setIsShowRelateOrder(false);
            }
        }
    },

    PRODUCT_ORDER("productOrder", "生产订单") {
        @Override
        public void removeMsg(WorkOrderEntity entity) {
            entity.setProductOrderList(null);
        }

        @Override
        public void removeCondition(WorkOrderDetailDTO dto, List<String> allRemove) {
            if (allRemove.contains(SALE_ORDER.code) && allRemove.contains(PRODUCT_ORDER.code)) {
                dto.setIsShowRelateOrder(false);
            }
        }
    },

    MATERIAL("material", "物料") {
        @Override
        public void removeMsg(WorkOrderEntity entity) {
            entity.setMaterialEntityList(null);
            entity.setMaterialFields(null);
        }

        @Override
        public void removeCondition(WorkOrderDetailDTO dto, List<String> allRemove) {
            dto.setIsShowMaterial(false);
        }
    },

    WAREHOUSE_INFO("warehouseInfo", "库存相关数量") {
        @Override
        public void removeMsg(WorkOrderEntity entity) {
            entity.setInputList(null);
            entity.setReturnList(null);
            entity.setTakeOutList(null);
        }

        @Override
        public void removeCondition(WorkOrderDetailDTO dto, List<String> allRemove) {
            dto.setIsShowWarehouseInfo(false);
        }
    },

    FILE_INFO("fileInfo", "附件") {
        @Override
        public void removeMsg(WorkOrderEntity entity) {
            entity.setFile(null);
            entity.setAppendixEntities(null);
        }

        @Override
        public void removeCondition(WorkOrderDetailDTO dto, List<String> allRemove) {
            dto.setIsShowFileInfo(false);
        }
    },

    CRAFT_PROCEDURE_INFO("craftProcedureInfo", "工艺工序") {
        @Override
        public void removeMsg(WorkOrderEntity entity) {
            entity.setCraftProcedureEntities(null);
        }

        @Override
        public void removeCondition(WorkOrderDetailDTO dto, List<String> allRemove) {
            dto.setIsShowCraftProcedureInfo(false);
        }
    },

    PACKAGE_SCHEME_INFO("packageSchemeInfo", "包装方案") {
        @Override
        public void removeMsg(WorkOrderEntity entity) {
            entity.setPackageSchemeEntities(null);
        }

        @Override
        public void removeCondition(WorkOrderDetailDTO dto, List<String> allRemove) {
            dto.setIsShowPackageSchemeInfo(false);
        }
    },

    WORK_ORDER_PLAN_INFO("workOrderPlanInfo", "工单计划") {
        @Override
        public void removeMsg(WorkOrderEntity entity) {
            entity.setWorkOrderPlanList(null);
        }

        @Override
        public void removeCondition(WorkOrderDetailDTO dto, List<String> allRemove) {
            dto.setIsShowWorkOrderPlanInfo(false);
        }
    },

    WORK_ORDER_TEAM_INFO("workOrderTeamInfo", "班组信息") {
        @Override
        public void removeMsg(WorkOrderEntity entity) {
            entity.setWorkOrderTeamEntities(null);
        }

        @Override
        public void removeCondition(WorkOrderDetailDTO dto, List<String> allRemove) {
        }
    },
    ;

    /**
     * 字段名
     */
    private String code;

    /**
     * 字段名称
     */
    private String name;

    public abstract void removeMsg(WorkOrderEntity entity);

    public abstract void removeCondition(WorkOrderDetailDTO dto, List<String> allRemove);


    WorkOrderMsgConfEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static WorkOrderMsgConfEnum getByCode(String code) {
        if (StringUtils.isBlank(code)) {
            return null;
        }
        for (WorkOrderMsgConfEnum msgConfEnum : WorkOrderMsgConfEnum.values()) {
            if (code.equals(msgConfEnum.getCode())) {
                return msgConfEnum;
            }
        }
        return null;
    }


}
