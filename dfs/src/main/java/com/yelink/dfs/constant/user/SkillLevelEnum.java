package com.yelink.dfs.constant.user;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @Date 2021/3/10 10:55
 */
public enum SkillLevelEnum {

    /**
     * 技能等级
     */
    PRIMARY(1, "初级"),
    INTERMEDIATE(2, "中级"),
    SENIOR(3, "高级");

    private Integer code;
    private String name;


    SkillLevelEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public Integer getCode() {

        return code;
    }

    public String getName() {
        return name;
    }

    public static String getNameByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (SkillLevelEnum skillLevelEnum : SkillLevelEnum.values()) {
            if (code.intValue() == skillLevelEnum.code.intValue()) {
                return skillLevelEnum.getName();
            }
        }
        return null;
    }

    public static SkillLevelEnum getByName(String name) {
        return Arrays.stream(SkillLevelEnum.values()).filter(v -> name.equals(v.getName())).findFirst().orElse(null);
    }
}
