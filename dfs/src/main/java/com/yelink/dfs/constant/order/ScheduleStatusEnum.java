package com.yelink.dfs.constant.order;

import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @Date 2021/11/8 15:35
 */
public enum ScheduleStatusEnum {

    /**
     * 不排程
     */
    UNSCHEDULED("unScheduled", "不排程"),
    TO_BE_SCHEDULED("toBeScheduled", "待排程"),
    SCHEDULED("scheduled", "已排程");

    private String type;
    private String typeName;


    ScheduleStatusEnum(String type, String typeName) {
        this.type = type;
        this.typeName = typeName;
    }

    public String getType() {
        return type;
    }

    public String getTypeName() {
        return typeName;
    }


    public static ScheduleStatusEnum getByType(String type) {
        if (StringUtils.isBlank(type)) {
            return null;
        }
        for (ScheduleStatusEnum orderTypeEnum : ScheduleStatusEnum.values()) {
            if (type.equals(orderTypeEnum.getType())) {
                return orderTypeEnum;
            }
        }
        return null;
    }

    public static String getNameByType(String type) {
        for (ScheduleStatusEnum orderTypeEnum : ScheduleStatusEnum.values()) {
            if (orderTypeEnum.getType().equals(type)) {
                return orderTypeEnum.getTypeName();
            }
        }
        return null;
    }
}
