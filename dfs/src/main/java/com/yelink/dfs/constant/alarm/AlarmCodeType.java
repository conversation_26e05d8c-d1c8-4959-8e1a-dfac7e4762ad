package com.yelink.dfs.constant.alarm;

/**
 * @description: 告警码和告警类型
 * @author: shuang
 * @time: 2020/6/22
 */
public enum AlarmCodeType {


    /**
     * 应用对应的告警类型(dfs1.2版保留心跳和正常数据上报)
     */
    INFRA_RED_ALARM("10000", "非法滞留"),
    GENERAL_ALARM("8888", "异常告警"),
    FAULT_ALARM("7777", "设备故障"),
    TEST_ALARM("2222", "设备测试"),
    ALARM_CANCEL("3333", "告警解除"),
    OFFLINE_ALARM("0000", "离线告警"),

    /**
     * 以下为dfs告警类型/告警分类
     */
    NORMAL_HEART("1111", "正常待机/心跳"),
    NORMAL_REPORT("6666", "正常数据上报"),

    DEVICE_ALARM("16001", "设备异常"),
    CRAFT_ALARM("16002", "工艺异常"),
    QUALITY_ALARM("16003", "质量异常"),
    MATERIAL_ALARM("16004", "物料异常"),
    PERSONNEL_ALARM("16005", "人员异常"),
    MOULD_ALARM("16006", "模具异常"),
    PROGRESS_ALARM("16007", "进度异常"),
    PRODUCE_ALARM("16008", "生产异常"),
    SENSOR_ALARM("16009", "采集器异常"),

    ;


    private String alarmCode;
    private String alarmType;


    AlarmCodeType(String alarmCode, String alarmType) {
        this.alarmCode = alarmCode;
        this.alarmType = alarmType;
    }

    public String getAlarmCode() {
        return alarmCode;
    }

    public String getAlarmType() {
        return alarmType;
    }


    public static AlarmCodeType getAlarmTypeByAlarmCode(String alarmCode) {
        for (AlarmCodeType alarmCodeType : AlarmCodeType.values()) {
            if (alarmCodeType.getAlarmCode().equals(alarmCode)) {
                return alarmCodeType;
            }
        }
        return null;
    }

    /**
     * 是否是告警
     *
     * @param alarmCode
     * @return
     */
    public static boolean hasAlarm(String alarmCode) {
        if (NORMAL_HEART.getAlarmCode().equals(alarmCode)) {
            return false;
        }
        if (NORMAL_REPORT.getAlarmCode().equals(alarmCode)) {
            return false;
        }
        return true;
    }
}
