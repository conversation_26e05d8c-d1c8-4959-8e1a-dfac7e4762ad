package com.yelink.dfs.constant.task;


/**
 * @Description: 任务中心表
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2024/12/16
 */
public enum TaskStateEnum {

    IN_PROGRESS("inProgress", "进行中"),
    COMPLETE("complete", "已完成"),
    ;

    private final String code;
    private final String name;

    TaskStateEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static String getNameByCode(String code) {
        if (code == null) {
            return null;
        }
        for (TaskStateEnum categoryEnum : TaskStateEnum.values()) {
            if (code.equals(categoryEnum.code)) {
                return categoryEnum.name;
            }
        }
        return null;
    }

    public static String getCodeByName(String name) {
        if(name == null){
            return null;
        }
        for (TaskStateEnum categoryEnum : TaskStateEnum.values()) {
            if (name.equals(categoryEnum.name)) {
                return categoryEnum.code;
            }
        }
        return null;
    }
}



