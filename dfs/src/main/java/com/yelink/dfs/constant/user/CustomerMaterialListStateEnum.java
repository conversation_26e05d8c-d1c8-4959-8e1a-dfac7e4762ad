package com.yelink.dfs.constant.user;

/**
 * <AUTHOR>
 * 客户物料清单状态枚举
 * @Date 2021/4/27 10:33
 */
public enum CustomerMaterialListStateEnum {
    /**
     * 状态编码及描述
     * 状态（创建1 生效2 停用3 废弃4)
     */
    CREATED(1, "创建"),
    RELEASED(2, "生效"),
    DEACTIVATE(3, "停用"),
    ABANDON(4, "废弃"),
    ;

    private int code;
    private String name;

    CustomerMaterialListStateEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }

    public int getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static String getNameByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (CustomerMaterialListStateEnum stateEnum : CustomerMaterialListStateEnum.values()) {
            if (stateEnum.code == code) {
                return stateEnum.name;
            }
        }
        return null;
    }

    public static Integer getCodeByName(String name) {
        if (name == null) {
            return null;
        }
        for (CustomerMaterialListStateEnum stateEnum : CustomerMaterialListStateEnum.values()) {
            if (stateEnum.name.equals(name)) {
                return stateEnum.code;
            }
        }
        return null;
    }
}
