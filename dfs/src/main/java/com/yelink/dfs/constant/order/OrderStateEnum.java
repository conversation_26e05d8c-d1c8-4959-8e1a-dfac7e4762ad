package com.yelink.dfs.constant.order;


import com.yelink.dfscommon.constant.dfs.order.WorkOrderStateEnum;

import java.util.Arrays;

/**
 * @Description: 订单状态枚举
 * @Author: zhengfu
 * @Date: 2020/12/5
 */
public enum OrderStateEnum {

    /**
     * 状态编码及描述
     * 1-创建 2-生效 3-完成 4-关闭 5-取消
     */
    CREATED(1, "创建"),
    RELEASED(2, "生效"),
    FINISHED(3, "完成"),
    CLOSED(4, "关闭"),
    CANCELED(5, "取消");

    private int code;
    private String name;

    OrderStateEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }

    public int getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
    public static Object getByName(String stateName) {
        if(stateName == null){
            return null;
        }
        return Arrays.stream(OrderStateEnum.values()).filter(v -> stateName.equals(v.getName())).findFirst().orElse(null);
    }

    public static String getNameByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (OrderStateEnum stateEnum : OrderStateEnum.values()) {
            if (stateEnum.code == code) {
                return stateEnum.name;
            }
        }
        return null;
    }

    public static Integer getCodeByName(String name) {
        if(name == null){
            return null;
        }
        for (OrderStateEnum stateEnum : OrderStateEnum.values()) {
            if (name.equals(stateEnum.name)) {
                return stateEnum.code;
            }
        }
        return null;
    }

    public static OrderStateEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (OrderStateEnum orderStateEnum : OrderStateEnum.values()) {
            if (orderStateEnum.code == code) {
                return orderStateEnum;
            }
        }
        return null;
    }
}
