package com.yelink.dfs.constant.product;

import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @Date 2022/5/11 16:43
 */
public enum ControlItemEnum {
    /**
     *
     */
    JUMP_STATION_CHECK("jumpStationCheck", "关键工序"),
    REFORM_CHECK("reformCheck", "重做检查"),
    MATERIAL_CHECK("materialCheck", "物料检查"),
    PRODUCTION_CHECK("productionCheck", "投产检查"),
    LAST_VALUE_CHECK("lastValueCheck", "工序间互检"),
    SIGNATURE_CHECK("signatureCheck", "签字确认"),
    INPUT_CHECK("inputCheck", "投入数检查"),
    ;

    private String code;
    private String name;

    ControlItemEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static ControlItemEnum getByName(String name) {
        if (StringUtils.isEmpty(name)) {
            return null;
        }
        for (ControlItemEnum e : ControlItemEnum.values()) {
            if (e.name.equals(name)) {
                return e;
            }
        }
        return null;
    }

}
