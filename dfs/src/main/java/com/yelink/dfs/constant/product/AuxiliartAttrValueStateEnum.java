package com.yelink.dfs.constant.product;


import com.baomidou.mybatisplus.annotation.EnumValue;
import org.apache.commons.lang3.StringUtils;

/**
 * @Description: 特征值状态枚举
 * @Author: zhuangwq
 * @Date: 2021/04/08
 */
public enum AuxiliartAttrValueStateEnum {

    /**
     * 1-显示 2-隐藏
     */
    DISPLAY(1, "显示"),
    HIDE(2, "隐藏"),
    ;

    @EnumValue
    private int code;
    private String name;

    AuxiliartAttrValueStateEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }

    public int getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static String getNameByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (AuxiliartAttrValueStateEnum stateEnum : AuxiliartAttrValueStateEnum.values()) {
            if (stateEnum.code == code) {
                return stateEnum.name;
            }
        }
        return null;
    }

    public static Integer getCodeByName(String name) {
        if (StringUtils.isBlank(name)) {
            return null;
        }
        for (AuxiliartAttrValueStateEnum stateEnum : AuxiliartAttrValueStateEnum.values()) {
            if (stateEnum.name.equals(name)) {
                return stateEnum.code;
            }
        }
        return null;
    }

}
