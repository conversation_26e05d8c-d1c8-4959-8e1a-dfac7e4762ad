package com.yelink.dfs.constant.user;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @Date 2021/3/10 10:44
 */
public enum EmployeeEnum {
    /**
     * 性别
     */

    MAN(1, "女"),
    WOMAN(2, "男");

    private Integer code;
    private String name;


    EmployeeEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static String getNameByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (EmployeeEnum employeeEnum : EmployeeEnum.values()) {
            if (code.intValue() == employeeEnum.code.intValue()) {
                return employeeEnum.getName();
            }
        }
        return null;
    }

    public static EmployeeEnum getByName(String sexName) {
       return Arrays.stream(EmployeeEnum.values()).filter(v -> sexName.equals(v.getName())).findFirst().orElse(null);
    }


}
