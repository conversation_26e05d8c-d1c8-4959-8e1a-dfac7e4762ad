package com.yelink.dfs.constant.device;


/**
 * @Description: 设备使用状态枚举
 * @Author: Chensn
 * @Date: 2020/12/5
 */
public enum DevicesUseStateEnum {

    /**
     * 设备使用状态 在用、闲置、维修、移交、报废、借出
     */
    IN_USE(0, "在用"),
    IN_IDLE(1, "闲置"),
    MAINTAINING(2, "维修"),
    TURN_OVER(3, "移交"),
    SCRAP(4, "报废"),
    LEND(5, "借出"),

    ;

    private Integer code;
    private String name;

    DevicesUseStateEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public int getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static String getNameByCode(Integer code) {
        for (DevicesUseStateEnum stateEnum : DevicesUseStateEnum.values()) {
            if (stateEnum.code.equals(code)) {
                return stateEnum.name;
            }
        }
        return null;
    }

    public static Integer getCodeByName(String name) {
        for (DevicesUseStateEnum stateEnum : DevicesUseStateEnum.values()) {
            if (name.equals(stateEnum.name)) {
                return stateEnum.code;
            }
        }
        return null;
    }
}
