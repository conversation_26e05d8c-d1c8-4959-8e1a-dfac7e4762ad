package com.yelink.dfs.constant;

import com.yelink.dfscommon.constant.EnumDTO;

import java.util.ArrayList;
import java.util.List;

/**
 * 附件类型
 */
public enum AppendixTypeEnum {
    /**
     * 物料
     */
    MATERIAL_APPENDIX("materialCode", "物料附件"),
    DEVICE_APPENDIX("deviceCode", "设备附件"),
    PRODUCT_ORDER_APPENDIX("productOrderCode", "生产订单附件"),
    WORKORDER_APPENDIX("workOrderCode", "工单附件"),
    PROCEDURE_APPENDIX("procedureCode", "工序附件"),
    BOM_APPENDIX("bomCode", "BOM附件"),
    ALARM_APPENDIX("alarmCode", "告警附件"),
    ALARM_DEFINITION_APPENDIX("alarmDefinitionCode", "告警定义附件"),
    EVENT_APPENDIX("eventCode", "事件附件"),
    EVENT_DEFINITION_APPENDIX("eventDefinitionCode", "事件定义附件"),
    USER_SIGNATURE_APPENDIX("userSignature", "用户签名附件"),
    DELIVERY("delivery", "发货管理单据附件"),
    PROCESS_ASSEMBLY_APPENDIX("processAssembly", "工装附件"),
    SUPPLIER_APPENDIX("supplierCode", "供应商档案附件"),
    ;

    private String code;
    private String name;

    AppendixTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static String getNameByCode(String code) {
        if (code == null) {
            return null;
        }
        for (AppendixTypeEnum appendixTypeEnum : AppendixTypeEnum.values()) {
            if (appendixTypeEnum.code.equals(code)) {
                return appendixTypeEnum.name;
            }
        }
        return null;
    }

    /**
     * 将枚举值转化成list集合
     *
     * @return
     */
    public static List<EnumDTO> toList() {
        List<EnumDTO> list = new ArrayList<>();
        for (AppendixTypeEnum appendixTypeEnum : AppendixTypeEnum.values()) {
            list.add(EnumDTO.builder().code(appendixTypeEnum.code).name(appendixTypeEnum.name).build());
        }
        return list;
    }
}
