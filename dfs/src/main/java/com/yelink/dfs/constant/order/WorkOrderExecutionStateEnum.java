package com.yelink.dfs.constant.order;

import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * 工单执行状态枚举
 * @Date 2022/2/12 16:55
 */
public enum WorkOrderExecutionStateEnum {
    /**
     * onTime - 按时
     * overtime - 超时
     */
    ON_TIME("onTime", "按时"),
    OVERTIME("overtime", "超时");


    private String code;
    private String name;

    WorkOrderExecutionStateEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static String getNameByCode(String code) {
        if (StringUtils.isBlank(code)) {
            return null;
        }
        for (WorkOrderExecutionStateEnum stateEnum : WorkOrderExecutionStateEnum.values()) {
            if (stateEnum.code.equals(code)) {
                return stateEnum.name;
            }
        }
        return null;
    }

    public static String getCodeByName(String name) {
        for (WorkOrderExecutionStateEnum stateEnum : WorkOrderExecutionStateEnum.values()) {
            if (name.equals(stateEnum.name)) {
                return stateEnum.code;
            }
        }
        return null;
    }
}
