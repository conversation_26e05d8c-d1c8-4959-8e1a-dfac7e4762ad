package com.yelink.dfs.constant.maintain;

/**
 * @description: 设备维修类型
 * @author: zhuangwq
 * @time: 2021/7/17
 */
public enum MaintainTypeEnum {

    /**
     * 设备维修类型
     */
    SLIGHT(0, "轻微"),
    COMMONLY(1, "一般"),
    SERIOUS(2, "严重");

    private int typeCode;
    private String typeName;

    MaintainTypeEnum(int typeCode, String typeName) {
        this.typeCode = typeCode;
        this.typeName = typeName;
    }

    public int getTypeCode() {
        return typeCode;
    }

    public String getTypeName() {
        return typeName;
    }

    public static String getTypeNameByTypeCode(Integer typeCode) {
        if (typeCode == null) {
            return null;
        }
        for (MaintainTypeEnum alarmDealStateEnum : MaintainTypeEnum.values()) {
            if (alarmDealStateEnum.typeCode == typeCode) {
                return alarmDealStateEnum.typeName;
            }
        }
        return null;
    }


}
