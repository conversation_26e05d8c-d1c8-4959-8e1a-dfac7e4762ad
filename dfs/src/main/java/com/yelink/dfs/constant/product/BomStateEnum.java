package com.yelink.dfs.constant.product;

import com.baomidou.mybatisplus.annotation.EnumValue;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;

/**
 * @Description: BOM状态枚举
 * @Author: zhuangwq
 * @Date: 2021/04/08
 */
public enum BomStateEnum {

    /**
     * 物料/bom状态
     * 1-创建 2-生效 3-停用 4-废弃
     */
    CREATE(1, "创建"),
    RELEASED(2, "生效"),
    STOP_USING(3, "停用"),
    ABANDONED(4, "废弃")
    ;

    @EnumValue
    private int code;
    private String name;

    BomStateEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }

    public static BomStateEnum getByName(String stateName) {
        return Arrays.stream(BomStateEnum.values()).filter(v -> stateName.equals(v.getName())).findFirst().orElse(null);
    }

    public int getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static String getNameByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (BomStateEnum stateEnum : BomStateEnum.values()) {
            if (stateEnum.code == code) {
                return stateEnum.name;
            }
        }
        return null;
    }

    public static Integer getCodeByName(String name) {
        if (StringUtils.isBlank(name)) {
            return null;
        }
        for (BomStateEnum stateEnum : BomStateEnum.values()) {
            if (stateEnum.name .equals(name) ) {
                return stateEnum.code;
            }
        }
        return null;
    }
}
