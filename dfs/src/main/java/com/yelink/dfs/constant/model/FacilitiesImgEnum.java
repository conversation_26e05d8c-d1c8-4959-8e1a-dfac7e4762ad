package com.yelink.dfs.constant.model;


/**
 * @Description: 工位图片父id枚举
 * @Author: zheng<PERSON>
 * @Date: 2020/12/5
 */
public enum FacilitiesImgEnum {

    /**
     * 工位、连接
     */
    FACILITIES(1003, "工位图片"),
    CONNECT(1004, "连接图片");

    private int code;
    private String name;

    FacilitiesImgEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }

    public int getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static String getNameByCode(int code) {
        for (FacilitiesImgEnum stateEnum : FacilitiesImgEnum.values()) {
            if (code == stateEnum.code) {
                return stateEnum.name;
            }
        }
        return null;
    }

    public static int getCodeByName(String name) {
        for (FacilitiesImgEnum stateEnum : FacilitiesImgEnum.values()) {
            if (name == stateEnum.name) {
                return stateEnum.code;
            }
        }
        return -999;
    }
}
