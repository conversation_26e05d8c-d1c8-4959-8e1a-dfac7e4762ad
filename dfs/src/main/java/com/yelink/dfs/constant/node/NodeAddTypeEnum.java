package com.yelink.dfs.constant.node;

import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * 节点新增类型
 * @Date 2022/4/29 11:38
 */
public enum NodeAddTypeEnum {
    /**
     * 上报节点类型类型
     */
    FACTORY_CONFIGURATION("factoryConfiguration", "工厂配置"),
    CUSTOM_CONFIGURATION("customConfiguration", "自定义配置");

    private String type;
    private String name;


    NodeAddTypeEnum(String type, String name) {
        this.type = type;
        this.name = name;
    }

    public String getName() {
        return name;
    }

    public String getType() {
        return type;
    }

    public static String getNameByCode(String type) {
        if (StringUtils.isBlank(type)) {
            return null;
        }
        for (NodeAddTypeEnum nodeAddTypeEnum : NodeAddTypeEnum.values()) {
            if (type.equals(nodeAddTypeEnum.type)) {
                return nodeAddTypeEnum.getName();
            }
        }
        return null;
    }
}
