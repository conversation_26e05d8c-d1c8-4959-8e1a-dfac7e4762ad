package com.yelink.dfs.constant.maintain;


import com.baomidou.mybatisplus.annotation.EnumValue;

/**
 * @Description: 维修工单状态枚举
 * @Author: zhuangwq
 * @Date: 2021/7/19
 */
public enum MaintainOrderStateEnum {

    /**
     * 维修工单状态枚举
     * 1-创建 2-生效 3-完成 4-取消
     */
    CREATE(1, "创建"),
    TO_BE_RESOLVED(2, "生效"),
    RESOLVED(3, "完成"),
    CANCELED(4, "取消"),

    ;

    @EnumValue
    private int code;
    private String name;

    MaintainOrderStateEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }

    public int getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static String getNameByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (MaintainOrderStateEnum orderStateEnum : MaintainOrderStateEnum.values()) {
            if (orderStateEnum.code == code) {
                return orderStateEnum.name;
            }
        }
        return null;
    }
}
