package com.yelink.dfs.constant.order;

/**
 * 投产检查结果枚举
 *
 * <AUTHOR>
 * @Date 2021/4/25 11:31
 */
public enum InvestCheckResultEnum {
    /**
     * 技能等级
     */
    OK(true, "通过"),
    NOK(false, "不通过");

    private Boolean code;
    private String name;


    InvestCheckResultEnum(Boolean code, String name) {
        this.code = code;
        this.name = name;
    }

    public Boolean getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static String getNameByCode(Boolean code) {
        if (code == null) {
            return null;
        }
        return code ? OK.name : NOK.name;
    }



}
