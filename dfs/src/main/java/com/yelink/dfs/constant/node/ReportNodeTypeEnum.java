package com.yelink.dfs.constant.node;

import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * 上报节点类型类型
 * @Date 2022/4/24 10:02
 */
public enum  ReportNodeTypeEnum {

    /**
     * 上报节点类型类型
     */
    BILL("bill", "单据"),
    ARTIFICIAL("artificial", "人工"),
    INTEGRATE("integrate", "集成");

    private String type;
    private String name;


    ReportNodeTypeEnum(String type, String name) {
        this.type = type;
        this.name = name;
    }

    public String getName() {
        return name;
    }

    public String getType() {
        return type;
    }

    public static String getNameByCode(String type) {
        if (StringUtils.isBlank(type)) {
            return null;
        }
        for (ReportNodeTypeEnum reportNodeTypeEnum : ReportNodeTypeEnum.values()) {
            if (type.equals(reportNodeTypeEnum.type)) {
                return reportNodeTypeEnum.getName();
            }
        }
        return null;
    }
}
