package com.yelink.dfs.constant.product;

import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @Description bom子项物料校验类型
 * @Date 2023/2/1 14:40
 */
public enum BomRawMaterialEnum {

    /**
     * 不允许重复、重复合并、重复不合并
     */
    NOT_REPEAT("notRepeat", "不允许重复"),
    REPEAT_MERGE("repeatMerge", "重复合并"),
    REPEAT_NOT_MERGE("repeatNotMerge", "重复不合并"),

    ;

    /**
     * 字段名
     */
    private String code;

    /**
     * 字段名称
     */
    private String name;

    BomRawMaterialEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static String getNameByCode(String code) {
        if (StringUtils.isBlank(code)) {
            return null;
        }
        for (BomRawMaterialEnum stateEnum : BomRawMaterialEnum.values()) {
            if (stateEnum.code.equals(code)) {
                return stateEnum.name;
            }
        }
        return null;
    }

    public static String getCodeByName(String name) {
        if (StringUtils.isBlank(name)) {
            return null;
        }
        for (BomRawMaterialEnum stateEnum : BomRawMaterialEnum.values()) {
            if (stateEnum.name .equals(name) ) {
                return stateEnum.code;
            }
        }
        return null;
    }
    
}
