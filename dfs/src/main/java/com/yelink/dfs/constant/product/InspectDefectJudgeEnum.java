package com.yelink.dfs.constant.product;

import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @Description 不良判断类型 枚举
 * @Date 2022/4/19 14:40
 */
public enum InspectDefectJudgeEnum {

    BOOLEAN_JUDGE("booleanJudge","布尔判断"),
    RANGE_JUDGE("rangeJudge","范围判断");

    /**
     * code
     */
    private String code;
    /**
     * name
     */
    private String name;

    InspectDefectJudgeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }


    public static String getNameByCode(String code) {
        if (code == null) {
            return null;
        }
        for (InspectDefectJudgeEnum stateEnum : InspectDefectJudgeEnum.values()) {
            if (stateEnum.code.equals(code)) {
                return stateEnum.name;
            }
        }
        return null;
    }

    public static String getCodeByName(String name) {
        if (StringUtils.isBlank(name)) {
            return null;
        }
        for (InspectDefectJudgeEnum stateEnum : InspectDefectJudgeEnum.values()) {
            if (name.equals(stateEnum.name)) {
                return stateEnum.code;
            }
        }
        return null;
    }

}
