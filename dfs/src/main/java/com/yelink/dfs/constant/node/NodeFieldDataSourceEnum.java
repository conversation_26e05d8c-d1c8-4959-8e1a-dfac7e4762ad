package com.yelink.dfs.constant.node;

import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * 节点字段数据源
 * @Date 2022/4/24 10:00
 */
public enum NodeFieldDataSourceEnum {

    /**
     * 节点字段数据源
     */
    EXCEPTION("exception", "异常描述"),
    
    ;

    private String code;
    private String name;

    NodeFieldDataSourceEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static String getNameByCode(String code) {
        if (code == null) {
            return null;
        }
        for (NodeFieldDataSourceEnum stateEnum : NodeFieldDataSourceEnum.values()) {
            if (stateEnum.code.equals(code)) {
                return stateEnum.name;
            }
        }
        return null;
    }

    public static String getCodeByName(String name) {
        if (StringUtils.isBlank(name)) {
            return null;
        }
        for (NodeFieldDataSourceEnum stateEnum : NodeFieldDataSourceEnum.values()) {
            if (stateEnum.name.equals(name)) {
                return stateEnum.code;
            }
        }
        return null;
    }
}
