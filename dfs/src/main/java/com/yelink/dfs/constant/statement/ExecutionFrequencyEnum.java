package com.yelink.dfs.constant.statement;

import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @Date 2023/7/4 12:00
 */
public enum ExecutionFrequencyEnum {
    /**
     * 每天Days、每周Weeks、每月Months、每年Years
     */
    DAYS("Days", "每天"),
    WEEKS("Weeks", "每周"),
    MONTHS("Months", "每月"),
    YEARS("Years", "每年"),
    ;

    private final String code;
    private final String name;

    ExecutionFrequencyEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static String getNameByCode(String code) {
        if (StringUtils.isNotBlank(code)) {
            for(ExecutionFrequencyEnum typeEnum: ExecutionFrequencyEnum.values()) {
                if(typeEnum.getCode().equals(code)) {
                    return typeEnum.getName();
                }
            }
        }
        return null;
    }

}
