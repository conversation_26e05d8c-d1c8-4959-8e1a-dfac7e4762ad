package com.yelink.dfs.constant.reportline;


import com.yelink.dfscommon.entity.CommonEnumInterface;
import lombok.Getter;

/**
 * 报工 工单类型 枚举
 * <AUTHOR>
 */
public enum OrderReportTypeEnum implements CommonEnumInterface {

    /**
     *
     */
    COMMON("common", "正常报工"),
    SUBCONTRACT("subcontract", "委外报工");

    @Getter
    private final String code;
    @Getter
    private final String name;

    OrderReportTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static OrderReportTypeEnum getByCode(String code) {
        if (code == null) {
            return null;
        }
        for (OrderReportTypeEnum e : OrderReportTypeEnum.values()) {
            if (e.code.equals(code)) {
                return e;
            }
        }
        return null;
    }
    public static String getNameByCode(String code) {
        OrderReportTypeEnum e = getByCode(code);
        if (e == null) {
            return null;
        }
        return e.name;
    }

}

