package com.yelink.dfs.constant;

/**
 * 条款类型枚举
 *
 * <AUTHOR>
 * @Date 2021/4/7 10:34
 */
public enum TermsType {
    /**
     * 类型
     */
    TRADE(500, "tradeAgreement", "贸易条款"),
    RECEIPT(600, "paymentAgreement", "收付款条件"),
    PAYMENT(700, "transportAgreement", "运输条款");

    private Integer code;
    private String type;
    private String name;


    TermsType(Integer code, String type, String name) {
        this.code = code;
        this.type = type;
        this.name = name;
    }

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public String getType() {
        return type;
    }

    public static String getNameByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (TermsType termsType : TermsType.values()) {
            if (code.intValue() == termsType.code.intValue()) {
                return termsType.getName();
            }
        }
        return null;
    }
}
