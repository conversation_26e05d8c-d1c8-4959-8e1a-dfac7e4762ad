package com.yelink.dfs.constant.product;

import com.baomidou.mybatisplus.annotation.EnumValue;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @Date 2022/5/11 16:43
 */
public enum TimeoutThresholdTypeEnum {
    /**
     * (流转，生产)超时阈值类型
     */
    PERCENTAGE("percentage", "百分比"),
    FIXED_VALUE("fixedValue", "固定值"),
    ;

    @EnumValue
    private String code;
    private String name;

    TimeoutThresholdTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static String getNameByCode(String code) {
        if (code == null) {
            return null;
        }
        for (TimeoutThresholdTypeEnum itemEnum : TimeoutThresholdTypeEnum.values()) {
            if (itemEnum.code.equals(code)) {
                return itemEnum.name;
            }
        }
        return null;
    }

    public static String getCodeByName(String name) {
        if (StringUtils.isBlank(name)) {
            return null;
        }
        for (TimeoutThresholdTypeEnum itemEnum : TimeoutThresholdTypeEnum.values()) {
            if (itemEnum.name.equals(name)) {
                return itemEnum.code;
            }
        }
        return null;
    }
}
