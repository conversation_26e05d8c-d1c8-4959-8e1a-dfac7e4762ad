package com.yelink.dfs.constant.model;


import com.yelink.dfscommon.entity.CommonEnumInterface;
import lombok.Getter;

/**
 * 版本模型
 * <AUTHOR>
 */
public enum VersionModelIdEnum implements CommonEnumInterface {

    /**
     * 类型
     */
    BOM("bom"),
    CRAFT("工艺"),
    ;

    VersionModelIdEnum(String name) {
        this.name = name;
    }

    @Getter
    private final String name;


    @Override
    public String getCode() {
        return name();
    }
}
