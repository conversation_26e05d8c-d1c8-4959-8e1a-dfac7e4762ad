package com.yelink.dfs.constant.order;


/**
 * @Description: 工单排产状态枚举
 * @Author: zhen<PERSON><PERSON>
 * @Date: 2020/12/5
 */
public enum WorkOrderCirculationStateEnum {

    /**
     * 状态编码及描述
     * 1-创建、8-审核、9-批准、2-生效、3-投产、4-挂起、5-完成、6-关闭、7-取消
     */
    UNABLE(0, "不可流转"),
    ENABE(1, "可流转"),
    OVER(2,"流转超时"),
    PLANED(3, "已流转");


    private Integer code;
    private String name;

    WorkOrderCirculationStateEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static String getNameByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (WorkOrderCirculationStateEnum workOrderStateEnum : WorkOrderCirculationStateEnum.values()) {
            if (workOrderStateEnum.code.equals(code)) {
                return workOrderStateEnum.name;
            }
        }
        return null;
    }

    public static Integer getCodeByName(String name) {
        if(name == null){
            return null;
        }
        for (WorkOrderCirculationStateEnum workOrderStateEnum : WorkOrderCirculationStateEnum.values()) {
            if (name.equals(workOrderStateEnum.name)) {
                return workOrderStateEnum.code;
            }
        }
        return null;
    }
}
