package com.yelink.dfs.constant.code;


import com.yelink.dfscommon.entity.CommonEnumInterface;

/**
 * @Description: 解码规则状态
 * @Author:
 * @Date: 2020/12/5
 */
public enum ScanCodeRuleStateEnum implements CommonEnumInterface {

    /**
     * 解码规则状态
     */
    ENABLE(1, "启用"),
    DISABLE(2,"停用"),
    ;

    private Integer code;
    private String name;

    ScanCodeRuleStateEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static String getNameByCode(Integer code) {
        for (ScanCodeRuleStateEnum stateEnum : ScanCodeRuleStateEnum.values()) {
            if (stateEnum.code.equals(code)) {
                return stateEnum.name;
            }
        }
        return null;
    }

    public static Integer getCodeByName(String name) {
        for (ScanCodeRuleStateEnum stateEnum : ScanCodeRuleStateEnum.values()) {
            if (name.equals(stateEnum.name)) {
                return stateEnum.code;
            }
        }
        return null;
    }
}
