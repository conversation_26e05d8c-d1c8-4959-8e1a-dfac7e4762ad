package com.yelink.dfs.constant.notice;


/**
 * @Description: 消息通知占位符
 * @Author:
 * @Date: 2020/12/5
 */
public enum NoticeMessagePlaceholder {

    /**
     * 消息通知占位符
     */
    PROCEDURE_NAME("procedureName", "{工序名称}"),
    PRE_PROCEDURE_NAME("preProcedureName", "{前工序名称}"),
    BACK_PROCEDURE_NAME("backProcedureName", "{后工序名称}"),
    WORK_CENTER_NAME("workCenterName", "{工作中心名称}"),
    PRODUCTION_BASIC_UNIT_NAME("productionBasicUnitName", "{基本生产单元名称}"),
    LINE_NAME("lineName", "{产线名称}"),
    PRE_LINE_NAME("preLineName", "{前产线名称}"),
    BACK_LINE_NAME("backLineName", "{后产线名称}"),
    CUSTOMER_CODE("customerCode", "{客户编码}"),
    MATERIAL_CODE("materialCode", "{物料编号}"),
    MATERIAL_NAME("materialName", "{物料名称}"),
    ORDER_QUANTITY("orderQuantity", "{下单数量}"),
    ORDER_DATE("orderDate", "{下单时间}"),
    DELIVERY_DATE("deliveryDate", "{交货日期}"),
    RETURN_NUMBER("returnNumber","{退货数量}"),
    RETURN_REASON("returnReason","{退货原因}"),
    DEFECT_REASON("defectReason","{不良原因}"),
    DEFECT_NUM("defectNum","{不良数量}"),
    DEFECT_RATE("defectRate","{不良率}"),
    DEFECT_NUM_THRESHOLD("defectNumThreshold","{不良数量阈值}"),
    DEFECT_RATE_THRESHOLD("defectRateThreshold","{不良率阈值}"),
    DEFECT_NAME("defectName","{不良名称}"),
    ORDER_NUMBER("orderNumber","{单号}"),
    EXCEPTION_DEAL_NAME("exceptionDealName","{异常处理人}"),
    EXCEPTION_COST("exceptionCost","{异常耗时}"),
    EXCEPTION_INFO("exceptionInfo","{异常信息}"),
    EXCEPTION_EXPLAIN("exceptionExplain","{异常描述}"),
    FNAME("fname", "{工位名称}"),
    NICKNAME("nickname", "{操作人名称}"),
    IQC_TYPE("iqcType", "{生产呼叫类型}"),
    REMARK("remark", "{备注}"),
    PRODUCT_FLOE_CODE("productFlowCode","{条码编号}"),
    CRAFT_CODE("craftCode", "{工艺编号}"),
    CRAFT_NAME("craftName", "{工艺名称}"),
    BOM_NUM("bomNum", "{bom编号}"),
    DEVICE_CODE("deviceCode", "{设备编号}"),
    DEVICE_NAME("deviceName", "{设备名称}"),
    DEVICE_MAG_NICK_NAME("deviceMagNickName", "{设备负责人}"),
    ALARM_DEFINITION_NAME("alarmDefinitionName", "{告警名称}"),
    ALARM_DEFINITION_CODE("alarmDefinitionCode", "{告警ID}"),
    ALARM_LEVEL_NAME("alarmLevelName", "{告警级别名称}"),
    ALARM_REPORT_TIME("alarmReportTime", "{告警上报时间}"),
    ALARM_RECOVERY_TIME("alarmRecoveryTime", "{告警恢复时间}"),
    ALARM_DEAL_USER_NICKNAME("alarmDealUserNickname", "{告警处理人}"),
    ALARM_EXPLAIN("alarmExplain", "{告警说明}"),
    /**
     * 单据相关占位符
     */
    SALE_ORDER_NUMBER("saleOrderNumber", "{销售订单号}"),
    PRODUCT_ORDER_NUMBER("productOrderNumber", "{生产订单号}"),
    WORK_ORDER_NUMBER("workOrderNumber", "{工单号}"),
    OPERATION_ORDER("operationOrder","{作业工单号}"),
    DELIVERY_APPLICATION_ORDER("deliveryApplicationOrder","{出货申请单号}"),
    STOCK_DELIVERY_ORDER("stockDeliveryOrder","{发货单号}"),
    PURCHASE_REQUEST_ORDER("purchaseRequestOrder","{采购需求单号}"),
    PURCHASE_CODE("purchaseCode","{采购订单号}"),
    PURCHASE_ORDER("purchaseOrder","{采购收料单号}"),
    PURCHASE_RECEIPT_ORDER("purchaseReceiptOrder","{委外订单号}"),
    SUBCONTRACT_ORDER("subcontractOrder","{委外订单收料单号}"),
    SUBCONTRACT_RECEIPT_ORDER("subcontractReceiptOrder","{生产入库单号}"),
    PRODUCT_IN_ORDER("productInOrder","{采购入库单号}"),
    PURCHASE_WAREHOUSING("purchaseWarehousing","{采购退料出库单号}"),
    PURCHASE_RETURN_OUT_ORDER("purchaseReturnOutOrder","{销售出库单号}"),
    SALES_OUT("salesOut","{销售退货入库单号}"),

    PRODUCTION_QUANTITY("productionQuantity", "{生产数量}"),
    SALES_QUANTITY("salesQuantity", "{销售数量}"),
    CREAT_TIME("createTime","{创建时间}"),
    SUPPLIER_CODE("supplierCode","{供应商编号}"),
    SUPPLIER_NAME("supplierName","{供应商名称}"),
    APPROVAL_STATE_NAME("approvalStateName","{审批状态}"),
    APPROVAL_TIME("approvalTime","{审批时间}"),
    STATE_NAME("stateName","{状态}"),
    STATE_CHANGE_TIME("stateChangeTime","{状态变更时间}"),
    PRE_STATE_NAME("preState","{变更前状态}"),

    /**
     * 质检模块相关占位符
     */
    INSPECTION_SHEET_TYPE_NAME("inspectionSheetTypeName","{送检单类型}"),
    INSPECTION_SHEET_CODE("inspectionSheetCode","{送检单编码}"),
    INSPECTION_CONCLUSION("inspectionConclusion","{检验结论}"),
    REVIEW_CONCLUSION("reviewConclusion","{复核结论}"),
    APPROVAL_CONCLUSION("approvalConclusion","{核准结论}"),
    CONCLUSION_CHANGE_TIME("conclusionChangeTime","{结论变更时间}"),
    INSPECTION_PERSON("inspectionPerson","{送检人}"),


    /**
     * 项目管理模块相关占位符
     */
    PROJECT_DEFINE_CODE("projectDefineCode","{项目编号}"),
    PROJECT_DEFINE_NAME("projectDefineName","{项目名称}"),
    TASK_CODE("taskCode","{任务编号}"),
    TASK_NAME("taskName","{任务名称}"),

    ;

    private String code;
    private String name;

    NoticeMessagePlaceholder(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static String getNameByCode(String code) {
        for (NoticeMessagePlaceholder stateEnum : NoticeMessagePlaceholder.values()) {
            if (stateEnum.code.equals(code)) {
                return stateEnum.name;
            }
        }
        return null;
    }

    public static String getCodeByName(String name) {
        for (NoticeMessagePlaceholder stateEnum : NoticeMessagePlaceholder.values()) {
            if (name.equals(stateEnum.name)) {
                return stateEnum.code;
            }
        }
        return null;
    }
}
