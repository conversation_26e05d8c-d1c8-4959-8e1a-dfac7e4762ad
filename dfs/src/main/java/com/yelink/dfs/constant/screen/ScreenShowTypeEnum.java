package com.yelink.dfs.constant.screen;

/**
 * <AUTHOR>
 * @description 大屏显示类型
 * @Date 2021/6/1
 */
public enum ScreenShowTypeEnum {

    /**
     * 显示类型（1-详情 2-图表）
     */
    DETAIL(1, "详情"),
    CHART(2, "图表"),
    ALARM(3, "告警"),
    USER(4, "人员"),
    LAMP_ONE(5, "灯检1"),
    LAMP_TWO(6, "灯检2"),
    OEE(7, "OEE"),
    LINKAGE_BOTTLE(8, "联动_洗瓶机"),
    LINKAGE_FILLING(9, "联动_灌装机"),
    DAY_ALARM_TOP_EIGHT(10, "日告警TOP8"),
    MONTH_ALARM_TOP_EIGHT(11, "月告警TOP8"),
    WEEK_ALARM_TOP_EIGHT(12, "周告警TOP8"),
    CHATS(13, "多图表");
    private int code;
    private String type;

    ScreenShowTypeEnum(int code, String type) {
        this.code = code;
        this.type = type;
    }

    public Integer getCode() {
        return code;
    }

    public String getType() {
        return type;
    }

    public static String getTypeByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (ScreenShowTypeEnum targetShowTypeEnum : ScreenShowTypeEnum.values()) {
            if (targetShowTypeEnum.code == code) {
                return targetShowTypeEnum.type;
            }
        }
        return null;
    }

    public static int getCodeByName(String name) {
        for (ScreenShowTypeEnum showTypeEnum : ScreenShowTypeEnum.values()) {
            if (name.equals(showTypeEnum.getType())) {
                return showTypeEnum.getCode();
            }
        }
        return -999;
    }

    public static ScreenShowTypeEnum getEnumByCode(Integer code) {
        for (ScreenShowTypeEnum showTypeEnum : values()) {
            if (code.equals(showTypeEnum.getCode())) {
                //获取指定的枚举
                return showTypeEnum;
            }
        }
        return null;
    }

}
