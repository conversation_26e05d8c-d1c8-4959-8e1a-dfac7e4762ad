package com.yelink.dfs.constant.statement;

import com.asyncexcel.core.exporter.ExportHandler;
import com.yelink.dfs.controller.order.WorkOrderListExportHandler;
import com.yelink.dfs.entity.attendance.vo.AttendanceExcelVO;
import com.yelink.dfs.entity.code.dto.ProductFlowCodeRecordExportDTO;
import com.yelink.dfs.entity.defect.dto.StatementRecordExcelDTO;
import com.yelink.dfs.entity.device.vo.DeviceExcelVO;
import com.yelink.dfs.entity.maintain.dto.MaintainRecordExcelDTO;
import com.yelink.dfs.entity.order.CustomerEntity;
import com.yelink.dfs.entity.order.dto.WorkOrderExportDTO;
import com.yelink.dfs.entity.statement.dto.FacListExportDTO;
import com.yelink.dfs.entity.statement.dto.GridListExportDTO;
import com.yelink.dfs.entity.statement.dto.ProductionLineExportDTO;
import com.yelink.dfs.entity.statement.dto.ProductionOrderExportDTO;
import com.yelink.dfs.entity.statement.dto.ReporterRecordExcelDTO;
import com.yelink.dfs.entity.statement.dto.RolePermissionExportDTO;
import com.yelink.dfs.entity.statement.dto.SaleOrderExportDTO;
import com.yelink.dfs.entity.statement.dto.StatementProductionDTO;
import com.yelink.dfs.entity.statement.dto.TeamListExportDTO;
import com.yelink.dfs.entity.statement.dto.UserManagerExportDTO;
import com.yelink.dfs.entity.statement.dto.WorkCenterExportDTO;
import com.yelink.dfs.entity.target.metrics.dto.MetricsMaterialDailyExportDTO;
import com.yelink.dfs.entity.target.metrics.dto.MetricsProcedureHourlyExportDTO;
import com.yelink.dfs.entity.target.metrics.dto.MetricsProductOrderDailyExportDTO;
import com.yelink.dfs.entity.target.metrics.dto.MetricsProductOrderExportDTO;
import com.yelink.dfs.entity.target.metrics.dto.MetricsProductOrderMonthlyExportDTO;
import com.yelink.dfs.entity.target.metrics.dto.MetricsProductOrderProcedureExportDTO;
import com.yelink.dfs.entity.target.metrics.dto.MetricsProductOrderSummaryMonthlyExportDTO;
import com.yelink.dfs.entity.target.metrics.dto.MetricsQualityLineMaterialDefectDailyExportDTO;
import com.yelink.dfs.entity.target.metrics.dto.MetricsSaleOrderMaterialDailyExportDTO;
import com.yelink.dfs.entity.target.metrics.dto.MetricsSaleOrderMaterialExportDTO;
import com.yelink.dfs.entity.target.metrics.dto.MetricsSaleOrderSummaryMonthlyExportDTO;
import com.yelink.dfs.entity.target.metrics.dto.MetricsStaffDailyExportDTO;
import com.yelink.dfs.entity.target.metrics.dto.MetricsWorkOrder10minExportDTO;
import com.yelink.dfs.entity.target.metrics.dto.MetricsWorkOrderDailyExportDTO;
import com.yelink.dfs.entity.target.metrics.dto.MetricsWorkOrderExportDTO;
import com.yelink.dfs.entity.target.metrics.dto.MetricsWorkOrderHourlyExportDTO;
import com.yelink.dfs.service.impl.code.FlowCodeRecordExportHandler;
import com.yelink.dfs.service.statement.impl.AttendanceExportHandler;
import com.yelink.dfs.service.statement.impl.CustomerExportHandler;
import com.yelink.dfs.service.statement.impl.DeviceLedgerExportHandler;
import com.yelink.dfs.service.statement.impl.FacListExportHandler;
import com.yelink.dfs.service.statement.impl.GridListExportHandler;
import com.yelink.dfs.service.statement.impl.MaintainExportHandler;
import com.yelink.dfs.service.statement.impl.MetricsMaterialDailyExportHandler;
import com.yelink.dfs.service.statement.impl.MetricsProcedureHourlyExportHandler;
import com.yelink.dfs.service.statement.impl.MetricsProductOrderDailyExportHandler;
import com.yelink.dfs.service.statement.impl.MetricsProductOrderExportHandler;
import com.yelink.dfs.service.statement.impl.MetricsProductOrderMonthlyExportHandler;
import com.yelink.dfs.service.statement.impl.MetricsProductOrderProcedureExportHandler;
import com.yelink.dfs.service.statement.impl.MetricsProductOrderSummaryMonthlyExportHandler;
import com.yelink.dfs.service.statement.impl.MetricsQualityLineMaterialDefectDailyExportHandler;
import com.yelink.dfs.service.statement.impl.MetricsSaleOrderMaterialDailyExportHandler;
import com.yelink.dfs.service.statement.impl.MetricsSaleOrderMaterialExportHandler;
import com.yelink.dfs.service.statement.impl.MetricsSaleOrderSummaryMonthlyExportHandler;
import com.yelink.dfs.service.statement.impl.MetricsStaffDailyExportHandler;
import com.yelink.dfs.service.statement.impl.MetricsWorkOrder10minExportHandler;
import com.yelink.dfs.service.statement.impl.MetricsWorkOrderDailyExportHandler;
import com.yelink.dfs.service.statement.impl.MetricsWorkOrderExportHandler;
import com.yelink.dfs.service.statement.impl.MetricsWorkOrderHourlyExportHandler;
import com.yelink.dfs.service.statement.impl.ProductOrderExportHandler;
import com.yelink.dfs.service.statement.impl.ProductionLineExportHandler;
import com.yelink.dfs.service.statement.impl.RecordWorkOrderUnqualifiedExportHandler;
import com.yelink.dfs.service.statement.impl.ReporterRecordExportHandler;
import com.yelink.dfs.service.statement.impl.RolePermissionExportHandler;
import com.yelink.dfs.service.statement.impl.SaleOrderExportHandler;
import com.yelink.dfs.service.statement.impl.TeamListExportHandler;
import com.yelink.dfs.service.statement.impl.UserManagerExportHandler;
import com.yelink.dfs.service.statement.impl.WorkCenterExportHandler;
import com.yelink.dfs.service.statement.impl.WorkOrderEfficiencyExportHandler;
import com.yelink.dfscommon.constant.ExcelExportFormEnum;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @Date 2023/7/4 12:00
 */
public enum ReportTypeEnum {
    /**
     * 报表管理的数据源:
     *  销售订单、生产订单、生产工单、质检明细表、生产工单绩效
     */
    SALE_ORDER("saleOrder", "销售订单", SaleOrderExportDTO.class, "nativeData", SaleOrderExportHandler.class, null),
    PRODUCT_ORDER("productOrder", "生产订单", ProductionOrderExportDTO.class, "nativeData", ProductOrderExportHandler.class, null),
    WORK_ORDER("workOrder", "生产工单", WorkOrderExportDTO.class, "nativeData", WorkOrderListExportHandler.class, WorkOrderListExportHandler.EXPORT_FORM),
    DEFECT_STATEMENT("defectStatement", "质检明细表", StatementRecordExcelDTO.class, "nativeData", RecordWorkOrderUnqualifiedExportHandler.class, null),
    WORK_ORDER_EFFICIENCY("workOrderEfficiency", "生产工单绩效", StatementProductionDTO.WorkOrderEfficiencyVO.class, "indexData", WorkOrderEfficiencyExportHandler.class, null),
    CUSTOMER("customer", "客户信息", CustomerEntity.class, "mainData", CustomerExportHandler.class, null),
    MAINTAIN("maintain", "质检返工明细", MaintainRecordExcelDTO.class, "nativeData", MaintainExportHandler.class, null),
    REPORTER_RECORD("reporterRecord", "生产报工明细", ReporterRecordExcelDTO.class, "nativeData", ReporterRecordExportHandler.class, null),
    ATTENDANCE("attendance", "生产上工记录", AttendanceExcelVO.class, "nativeData", AttendanceExportHandler.class, null),
    USER_MANAGER("userManager", "用户管理", UserManagerExportDTO.class, "mainData", UserManagerExportHandler.class, null),
    ROLE_PERMISSION("rolePermission", "角色权限", RolePermissionExportDTO.class, "mainData", RolePermissionExportHandler.class, null),
    GRID_LIST("gridList", "车间列表", GridListExportDTO.class, "mainData", GridListExportHandler.class, null),
    WORK_CENTER("workCenter", "工作中心", WorkCenterExportDTO.class, "mainData", WorkCenterExportHandler.class, null),
    PRODUCTION_LINE("productionLine", "制造单元", ProductionLineExportDTO.class, "mainData", ProductionLineExportHandler.class, null),
    FAC_LIST("facList", "工位列表", FacListExportDTO.class, "mainData", FacListExportHandler.class, null),
    TEAM_LIST("teamList", "班组列表", TeamListExportDTO.class, "mainData", TeamListExportHandler.class, null),
    DEVICE_LEDGER("deviceLedger", "设备台账", DeviceExcelVO.class, "mainData", DeviceLedgerExportHandler.class, null),

    PRODUCT_ORDER_PROCEDURE("productOrderProcedure", "生产订单工序进度", MetricsProductOrderProcedureExportDTO.class, "indexData", MetricsProductOrderProcedureExportHandler.class, null),
    LINE_DEFECT("lineDefect", "产线不良分布", MetricsQualityLineMaterialDefectDailyExportDTO.class, "indexData", MetricsQualityLineMaterialDefectDailyExportHandler.class, null),
    WORK_ORDER_DAILY_PROGRESS("workOrderDailyProgress", "工单每日进度", MetricsWorkOrderDailyExportDTO.class, "indexData", MetricsWorkOrderDailyExportHandler.class, null),
    WORK_ORDER_HOURLY_MONITOR("workOrderHourlyMonitor", "工单每小时监控", MetricsWorkOrderHourlyExportDTO.class, "indexData", MetricsWorkOrderHourlyExportHandler.class, null),
    WORK_ORDER_STATISTICS("workOrderStatistics", "生产整体进度", MetricsWorkOrderExportDTO.class, "indexData", MetricsWorkOrderExportHandler.class, null),

    MATERIAL_PRODUCT_DAILY("materialProductDaily", "产成品每日生产", MetricsMaterialDailyExportDTO.class, "indexData", MetricsMaterialDailyExportHandler.class, null),
    SALE_ORDER_PROGRESS("saleOrderProgress", "销售订单进度", MetricsSaleOrderMaterialExportDTO.class, "indexData", MetricsSaleOrderMaterialExportHandler.class, null),
    SALE_ORDER_PROGRESS_DAILY("saleOrderProgressDaily", "销售订单日进度", MetricsSaleOrderMaterialDailyExportDTO.class, "indexData", MetricsSaleOrderMaterialDailyExportHandler.class, null),
    SALE_ORDER_MONTH("saleOrderMonth", "销售订单月总结", MetricsSaleOrderSummaryMonthlyExportDTO.class, "indexData", MetricsSaleOrderSummaryMonthlyExportHandler.class, null),
    PRODUCT_ORDER_PROGRESS("productOrderProgress", "生产订单进度", MetricsProductOrderExportDTO.class, "indexData", MetricsProductOrderExportHandler.class, null),
    PRODUCT_ORDER_PROGRESS_DAILY("productOrderProgressDaily", "生产订单日进度", MetricsProductOrderDailyExportDTO.class, "indexData", MetricsProductOrderDailyExportHandler.class, null),
    PRODUCT_ORDER_MONTH_PROGRESS("productOrderMonthProgress", "生产订单-成品月进度", MetricsProductOrderMonthlyExportDTO.class, "indexData", MetricsProductOrderMonthlyExportHandler.class, null),
    PRODUCT_ORDER_MONTH("productOrderMonth", "生产订单-月总结", MetricsProductOrderSummaryMonthlyExportDTO.class, "indexData", MetricsProductOrderSummaryMonthlyExportHandler.class, null),

    PERSONNEL_PRODUCTION("personnelProduction", "人员生产", MetricsStaffDailyExportDTO.class, "indexData", MetricsStaffDailyExportHandler.class, null),
    PROCESS_PROGRESS_HOURLY_MONITOR("processProgressHourlyMonitor", "工序进展-每小时", MetricsProcedureHourlyExportDTO.class, "indexData", MetricsProcedureHourlyExportHandler.class, null),
    WORK_ORDER_EVERY_TEN_MIN("workOrderEveryTenMin", "生产工单-每十分钟监控", MetricsWorkOrder10minExportDTO.class, "indexData", MetricsWorkOrder10minExportHandler.class, null),
    FLOW_CODE_RECORD("flowCodeRecord", "过站记录", ProductFlowCodeRecordExportDTO.class, "nativeData", FlowCodeRecordExportHandler.class, null),
    ;

    @Getter
    private final String typeCode;
    @Getter
    private final String typeName;
    @Getter
    private final Class<?> exportClass;
    @Getter
    private final String dataSourceType;
    @Getter
    private final Class<? extends ExportHandler> exportHandler;
    /**
     * 导出实体类使用的是@DynamicExcelProperty注解, 这个字段需要加上
     */
    @Getter
    private final ExcelExportFormEnum exportForm;

    ReportTypeEnum(String typeCode, String typeName, Class<?> exportClass, String dataSourceType,Class<? extends ExportHandler> exportHandler, ExcelExportFormEnum exportForm) {
        this.typeCode = typeCode;
        this.typeName = typeName;
        this.exportClass = exportClass;
        this.dataSourceType = dataSourceType;
        this.exportHandler = exportHandler;
        this.exportForm = exportForm;
    }

    public static ReportTypeEnum getByCode(String code) {
        if (StringUtils.isNotBlank(code)) {
            for(ReportTypeEnum typeEnum: ReportTypeEnum.values()) {
                if(typeEnum.typeCode.equals(code)) {
                    return typeEnum;
                }
            }
        }
        return null;
    }

    public static ReportTypeEnum getByName(String typeName) {
        if (StringUtils.isNotBlank(typeName)) {
            for(ReportTypeEnum typeEnum: ReportTypeEnum.values()) {
                if(typeEnum.typeName.equals(typeName)) {
                    return typeEnum;
                }
            }
        }
        return null;
    }

    public static String getDataSourceTypeName(String dataSourceType) {
        String nativeData = "nativeData", mainData = "mainData", indexData = "indexData";
        if (nativeData.equals(dataSourceType)) {
            return "原生数据";
        } else if (mainData.equals(dataSourceType)) {
            return "主数据";
        } else if (indexData.equals(dataSourceType)) {
            return "指标数据";
        }
        return "";
    }

}
