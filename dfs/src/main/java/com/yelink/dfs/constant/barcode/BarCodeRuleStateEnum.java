package com.yelink.dfs.constant.barcode;


/**
 * @Description: 条码规则状态枚举
 * @Author: z<PERSON><PERSON><PERSON>
 * @Date: 2020/12/5
 */
public enum BarCodeRuleStateEnum {

    /**
     * 状态编码及描述
     * 0-创建 1-发放 2-完成 3-关闭 4-取消
     */
    CREATED(0, "创建"),
    RELEASED(1, "发放"),
    FINISHED(2, "完成"),
    CLOSED(3, "关闭"),
    CANCELED(4, "取消");

    private int code;
    private String name;

    BarCodeRuleStateEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }

    public int getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static String getNameByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (BarCodeRuleStateEnum stateEnum : BarCodeRuleStateEnum.values()) {
            if (stateEnum.code == code) {
                return stateEnum.name;
            }
        }
        return null;
    }

    public static Integer getCodeByName(String name) {
        for (BarCodeRuleStateEnum stateEnum : BarCodeRuleStateEnum.values()) {
            if (name.equals(stateEnum.name)) {
                return stateEnum.code;
            }
        }
        return null;
    }
}
