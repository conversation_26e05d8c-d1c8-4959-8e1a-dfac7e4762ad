package com.yelink.dfs.constant.order;


/**
 * @Description: 生产领料单状态枚举
 * @Author: zhen<PERSON><PERSON>
 * @Date: 2020/12/5
 */
public enum TakeOutApplicationStateEnum {

    /**
     * 状态编码及描述
     * 1-创建 2-生效 3-完成 4-关闭 5-取消
     */
    CREATED(1, "创建"),
//    TO_EXAMINE(6, "已审核"),
//    APPROVAL(7, "已批准"),
    RELEASED(2, "生效"),
    FINISHED(3, "完成"),
    CLOSED(4, "关闭"),
    CANCELED(5, "取消");

    private int code;
    private String name;

    TakeOutApplicationStateEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }

    public int getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static String getNameByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (TakeOutApplicationStateEnum workOrderStateEnum : TakeOutApplicationStateEnum.values()) {
            if (workOrderStateEnum.code == code) {
                return workOrderStateEnum.name;
            }
        }
        return null;
    }
    public static TakeOutApplicationStateEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (TakeOutApplicationStateEnum workOrderStateEnum : TakeOutApplicationStateEnum.values()) {
            if (workOrderStateEnum.code == code) {
                return workOrderStateEnum;
            }
        }
        return null;
    }

    public static Integer getCodeByName(String name) {
        for (TakeOutApplicationStateEnum workOrderStateEnum : TakeOutApplicationStateEnum.values()) {
            if (name.equals(workOrderStateEnum.name)) {
                return workOrderStateEnum.code;
            }
        }
        return null;
    }
}
