package com.yelink.dfs.constant.rule;

import com.yelink.dfscommon.constant.dfs.rule.NumberRulesConfigEntity;
import com.yelink.dfscommon.dto.dfs.RuleDetailDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.Map;

/**
 * @description: 前端传递的编码规则实体类，后台根据传递的对象生成对应的编码
 * @author: zhuangwq
 * @create: 2022-09-22 16:34
 **/
@Data
@ToString
@AllArgsConstructor
@NoArgsConstructor
public class RuleSeqExtendDTO extends RuleDetailDTO {

    /**
     * 编码规则id
     * {@link RuleTypeEnum}
     */
    private Integer id;

    /**
     * 关联单据对象
     * key为关联单号类型
     * value为关联单号（可以是生产工单号、销售订单号、采购订单。。。多种单号）
     * 举例说明：
     * 生成采购单品码
     * relatedMap 需传 relatedMap: {"purchase": "值为具体的采购订单号"}
     */
    private Map<String, String> relatedMap;

    /**
     * 前端传递的编码（可以是生产工单号、销售订单号、制造单元类型编号、制造单元编号。。。多种单号），用于组成编码信息
     * key为编码规则类型
     * value为编码号
     * 举例说明：
     * 生成采购单品码
     * 需传 numberMap: {3: "值为具体的采购订单号"}
     */
    private Map<String, String> numberMap;

    /**
     * 编码规则实体
     */
    private NumberRulesConfigEntity numberRule;

}
