package com.yelink.dfs.constant.node;

import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * 指定类型
 * @Date 2022/4/24 10:02
 */
public enum SaleOrderStateEnum {

    /**
     * 上报节点类型类型
     */
    NORMAL("normal", "正常"),
    DELAY("delay", "延期"),
    ;

    private String type;
    private String name;


    SaleOrderStateEnum(String type, String name) {
        this.type = type;
        this.name = name;
    }

    public String getName() {
        return name;
    }

    public String getType() {
        return type;
    }

    public static String getNameByCode(String type) {
        if (StringUtils.isBlank(type)) {
            return null;
        }
        for (SaleOrderStateEnum appointTypeEnum : SaleOrderStateEnum.values()) {
            if (type.equals(appointTypeEnum.type)) {
                return appointTypeEnum.getName();
            }
        }
        return null;
    }
}
