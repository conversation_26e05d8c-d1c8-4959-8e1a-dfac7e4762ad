package com.yelink.dfs.constant.order;


/**
 * @Description: 工单排产状态枚举
 * @Author: zhen<PERSON><PERSON>
 * @Date: 2020/12/5
 */
public enum WorkOrderPlanStateEnum {

    /**
     * 状态编码及描述
     * 1-创建、8-审核、9-批准、2-生效、3-投产、4-挂起、5-完成、6-关闭、7-取消
     */
    UNABLE(0, "不可排"),
    ENABE(1, "待排产"),
    PLANED(2, "已排产");


    private Integer code;
    private String name;

    WorkOrderPlanStateEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static String getNameByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (WorkOrderPlanStateEnum workOrderStateEnum : WorkOrderPlanStateEnum.values()) {
            if (workOrderStateEnum.code.equals(code)) {
                return workOrderStateEnum.name;
            }
        }
        return null;
    }

    public static Integer getCodeByName(String name) {
        if(name == null){
            return null;
        }
        for (WorkOrderPlanStateEnum workOrderStateEnum : WorkOrderPlanStateEnum.values()) {
            if (name.equals(workOrderStateEnum.name)) {
                return workOrderStateEnum.code;
            }
        }
        return null;
    }
}
