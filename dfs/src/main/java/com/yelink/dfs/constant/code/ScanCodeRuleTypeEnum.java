package com.yelink.dfs.constant.code;

import com.yelink.dfscommon.entity.CommonEnumInterface;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @Date 2022/4/1 9:50
 */
public enum ScanCodeRuleTypeEnum implements CommonEnumInterface {

    RULE("rule", "自定义规则"),
    SCRIPT("script", "自定义脚本"),
    ;

    private String code;
    private String name;

    ScanCodeRuleTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static String getNameByCode(String code) {
        if (StringUtils.isBlank(code)) {
            return null;
        }
        for (ScanCodeRuleTypeEnum stateEnum : ScanCodeRuleTypeEnum.values()) {
            if (stateEnum.code.equals(code)) {
                return stateEnum.name;
            }
        }
        return null;
    }

    public static String getCodeByName(String name) {
        for (ScanCodeRuleTypeEnum stateEnum : ScanCodeRuleTypeEnum.values()) {
            if (name.equals(stateEnum.name)) {
                return stateEnum.code;
            }
        }
        return null;
    }
}
