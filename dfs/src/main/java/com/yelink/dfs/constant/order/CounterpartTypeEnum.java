package com.yelink.dfs.constant.order;

/**
 * @description: 对接人列表管理
 * @author: shenzm
 * @time: 2022/11/1
 */
public enum CounterpartTypeEnum {


    /**
     * 对应的对接人类型
     */

    CUSTOMER_TYPE("customerType"), //客户
    SUPPLIER_TYPE("supplierType"),//供应商
    ;

    private String type;


    CounterpartTypeEnum(String type) {
        this.type = type;
    }

    public String getType() {
        return type;
    }

}
