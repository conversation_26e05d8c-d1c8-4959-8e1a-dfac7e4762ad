package com.yelink.dfs.constant.aql;

/**
 * @Description: AQL批量范围枚举
 * @Author: Chensn
 * @Date: 2023/5/30
 */
public enum AqlScopeEnum {

    /**
     * AQL批量范围枚举
     */
    SCOPE_A(1, 8, "A"),
    SCOPE_B(9, 15, "B"),
    SCOPE_C(16, 25, "C"),
    SCOPE_D(26, 50, "D"),
    SCOPE_E(51, 90, "E"),
    SCOPE_F(91, 150, "F"),
    SCOPE_G(151, 280, "G"),
    SCOPE_H(281, 500, "H"),
    SCOPE_J(501, 1200, "J"),
    SCOPE_K(1201, 3200, "K"),
    SCOPE_L(3201, 10000, "L"),
    SCOPE_M(10001, 35000, "M"),
    SCOPE_N(35001, 150000, "N"),
    SCOPE_P(150001, 500000, "P"),
    SCOPE_Q(5000001, 0x7fffffff, "Q"),
    ;

    private final Integer min;
    private final Integer max;
    private final String code;


    AqlScopeEnum(Integer min, Integer max, String code) {
        this.code = code;
        this.min = min;
        this.max = max;
    }

    public String getCode() {
        return code;
    }

    public Integer getMin() {
        return min;
    }

    public Integer getMax() {
        return max;
    }

    public static String getCodeByValue(Integer value) {
        if (value == null) {
            return null;
        }
        for (AqlScopeEnum aqlScopeEnum : AqlScopeEnum.values()) {
            if (aqlScopeEnum.min <= value && value <= aqlScopeEnum.max) {
                return aqlScopeEnum.code;
            }
        }
        return null;
    }

}
