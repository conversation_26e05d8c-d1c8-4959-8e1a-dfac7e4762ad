package com.yelink.dfs.constant.code;

/**
 * 条码特殊指标集
 *
 * <AUTHOR>
 * @Date 2022/4/1 9:50
 */
public enum SpecialTargetEnum {
    /**
     * 工装特殊指标
     */
    ACTUAL_LIFESPAN("actualLifespan", "实际使用寿命");

    private String code;
    private String name;

    SpecialTargetEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static String getNameByCode(String code) {
        if (code == null) {
            return null;
        }
        for (SpecialTargetEnum stateEnum : SpecialTargetEnum.values()) {
            if (stateEnum.code.equals(code)) {
                return stateEnum.name;
            }
        }
        return null;
    }

    public static String getCodeByName(String name) {
        for (SpecialTargetEnum stateEnum : SpecialTargetEnum.values()) {
            if (name.equals(stateEnum.name)) {
                return stateEnum.code;
            }
        }
        return null;
    }
}
