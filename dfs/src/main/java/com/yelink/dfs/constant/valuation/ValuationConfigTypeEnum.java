package com.yelink.dfs.constant.valuation;

import com.yelink.dfscommon.entity.CommonEnumInterface;
import lombok.Getter;

/**
 * 计价类型
 * <AUTHOR>
 */
public enum ValuationConfigTypeEnum implements CommonEnumInterface {


    /**
     * 物料
     */
    MATERIAL("material", "物料"),
    PROCEDURE("procedure", "工序"),
    MANUAL("manual", "手动"),
    ;
    @Getter
    private final String code;
    @Getter
    private final String name;

    ValuationConfigTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }
    public static ValuationConfigTypeEnum fromCode(String code) {
        for(ValuationConfigTypeEnum e : ValuationConfigTypeEnum.values()) {
            if(e.code.equals(code)) {
                return e;
            }
        }
        return null;
    }

}
