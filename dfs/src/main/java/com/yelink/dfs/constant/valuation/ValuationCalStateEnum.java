package com.yelink.dfs.constant.valuation;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.yelink.dfscommon.entity.CommonEnumInterface;
import lombok.Getter;

/**
 * 工资计算 状态
 * <AUTHOR>
 */
public enum ValuationCalStateEnum implements CommonEnumInterface {


    /**
     *
     */
    CREATED(0, "未审批"),
    AUDITED(1, "已审批"),
    ;
    @Getter
    @EnumValue
    private final Integer code;
    @Getter
    private final String name;

    ValuationCalStateEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }
    public static ValuationCalStateEnum fromCode(Integer code) {
        for(ValuationCalStateEnum e : ValuationCalStateEnum.values()) {
            if(e.code.equals(code)) {
                return e;
            }
        }
        return null;
    }
}
