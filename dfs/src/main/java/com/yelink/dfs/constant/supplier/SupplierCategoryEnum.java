package com.yelink.dfs.constant.supplier;

import com.yelink.dfscommon.entity.CommonEnumInterface;
import lombok.Getter;

/**
 * <AUTHOR>
 * 供应类别
 */
public enum SupplierCategoryEnum implements CommonEnumInterface {

    /**
     * 供应类别
     */
    PURCHASE("采购"),
    SUBCONTRACT( "委外"),
    COMPREHENSIVE("综合"),
    ;

    @Getter
    private final String name;

    SupplierCategoryEnum(String name) {
        this.name = name;
    }

    @Override
    public String getCode() {
        return name();
    }

    public static SupplierCategoryEnum getByName(String name) {
        if (name == null) {
            return null;
        }
        for (SupplierCategoryEnum e : SupplierCategoryEnum.values()) {
            if (e.name.equals(name)) {
                return e;
            }
        }
        return null;
    }
}
