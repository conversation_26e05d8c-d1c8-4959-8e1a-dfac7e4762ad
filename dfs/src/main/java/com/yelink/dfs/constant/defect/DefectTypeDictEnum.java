package com.yelink.dfs.constant.defect;


/**
 * @Description: 维修类型枚举
 * @Author: zheng<PERSON>
 * @Date: 2020/12/5
 */
public enum DefectTypeDictEnum {

    /**
     * 维修类型字典枚举
     */
    DEFECT_TYPE("defectType", "不良类型"),
    DEFECT_SCHEME_TYPE("defectSchemeType", "质检方案的质检类型"),
    DEFECT_RELATED_TYPE("defectRelatedType", "质检方案关联类型"),
    
    ;

    private String code;
    private String name;

    DefectTypeDictEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static String getNameByCode(String code) {
        if (code == null) {
            return null;
        }
        for (DefectTypeDictEnum stateEnum : DefectTypeDictEnum.values()) {
            if (stateEnum.code.equals(code)) {
                return stateEnum.name;
            }
        }
        return null;
    }

    public static String getCodeByName(String name) {
        for (DefectTypeDictEnum stateEnum : DefectTypeDictEnum.values()) {
            if (name.equals(stateEnum.name)) {
                return stateEnum.code;
            }
        }
        return null;
    }
}
