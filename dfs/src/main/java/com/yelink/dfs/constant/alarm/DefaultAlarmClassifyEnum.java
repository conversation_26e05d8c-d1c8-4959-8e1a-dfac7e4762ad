package com.yelink.dfs.constant.alarm;

import lombok.Getter;

/**
 * 默认的告警分类
 * <AUTHOR>
 */
public enum DefaultAlarmClassifyEnum {
    /**
     * 未知的类型
     */
    UNKNOWN("UNKNOWN", "未知类型", AlarmDataEnum.SYSTEM_GENERATION.getCode()),
    DEVICE("901", "设备告警", AlarmDataEnum.SYSTEM_GENERATION.getCode()),
    ;
    DefaultAlarmClassifyEnum(String classifyCode, String classifyName, String dataSources) {
        this.classifyCode = classifyCode;
        this.classifyName = classifyName;
        this.dataSources = dataSources;
    }
    @Getter
    private final String classifyCode;
    @Getter
    private final String classifyName;
    @Getter
    private final String dataSources;
}
