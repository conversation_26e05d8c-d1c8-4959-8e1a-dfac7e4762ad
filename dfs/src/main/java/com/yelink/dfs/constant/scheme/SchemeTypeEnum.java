package com.yelink.dfs.constant.scheme;

import com.yelink.dfs.constant.defect.DefectTypeDictEnum;
import com.yelink.dfs.constant.maintain.MaintainTypeDictEnum;
import com.yelink.dfscommon.constant.RespCodeEnum;
import com.yelink.dfscommon.constant.ResponseException;

/**
 * @description: 方案类型
 * @author: zhuangwq
 * @time: 2021/7/17
 */
public enum SchemeTypeEnum {

    /**
     * 设备维修类型
     */
    DEFECT("defect", "质检"),
    MAINTAIN("maintain", "维修"),

    UNDEFINE("undefine", "未定义");

    private final String typeCode;
    private final String typeName;

    SchemeTypeEnum(String typeCode, String typeName) {
        this.typeCode = typeCode;
        this.typeName = typeName;
    }

    public String getTypeCode() {
        return typeCode;
    }

    public String getTypeName() {
        return typeName;
    }

    public static SchemeTypeEnum getByCode(String code) {
        for(SchemeTypeEnum typeEnum: SchemeTypeEnum.values()) {
            if(typeEnum.typeCode.equals(code)) {
                return typeEnum;
            }
        }
        return UNDEFINE;
    }

    public static String getDictCode(String schemeType) {
        String dictCode;
        switch (getByCode(schemeType)) {
            case DEFECT:
                dictCode = DefectTypeDictEnum.DEFECT_TYPE.getCode();
                break;
            case MAINTAIN:
                dictCode = MaintainTypeDictEnum.MAINTAIN_TYPE.getCode();
                break;
            default:
                throw new ResponseException(RespCodeEnum.REQUIRED_PARAMETERS_FAIL);
        }
        return dictCode;
    }


}
