package com.yelink.dfs.constant.product;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Description 工序工艺参数类型
 * @Date 2023/2/1 14:40
 */
public enum ProcessParameterTypeEnum {

    /**
     * 工序检测项类型
     * character--字符、numerical--数值、typeBoolean--布尔、listType--列表、 table--表格
     */
    CHARACTER("character", "字符"),
    NUMERICAL("numerical", "数值"),
    TYPE_BOOLEAN("typeBoolean", "布尔"),
    LIST_TYPE("listType", "列表"),
    // 预留支持
//    TABLE("table", "表格"),
    ;

    /**
     * 字段code
     */
    private final String code;

    /**
     * 字段名称
     */
    private final String name;

    ProcessParameterTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }


    public static String getNameByCode(String code) {
        if (code == null) {
            return null;
        }
        for (ProcessParameterTypeEnum stateEnum : ProcessParameterTypeEnum.values()) {
            if (stateEnum.code.equals(code)) {
                return stateEnum.name;
            }
        }
        return null;
    }

    public static String getCodeByName(String name) {
        if(name == null){
            return null;
        }
        for (ProcessParameterTypeEnum stateEnum : ProcessParameterTypeEnum.values()) {
            if (name.equals(stateEnum.name)) {
                return stateEnum.code;
            }
        }
        return null;
    }

    public static List<String> getNames() {
        ArrayList<String> names = new ArrayList<>();
        for (ProcessParameterTypeEnum stateEnum : ProcessParameterTypeEnum.values()) {
            names.add(stateEnum.name);
        }
        return names;
    }


}
