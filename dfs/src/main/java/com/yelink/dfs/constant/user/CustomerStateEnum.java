package com.yelink.dfs.constant.user;

/**
 * <AUTHOR>
 * 客户状态枚举
 * @Date 2021/4/27 10:33
 */
public enum CustomerStateEnum {
    /**
     * 状态编码及描述
     * 状态（1-创建 2-生效 3-停用)
     */
    CREATED(1, "创建"),
    RELEASED(2, "生效"),
    DEACTIVATE(3, "停用"),
    ;

    private int code;
    private String name;

    CustomerStateEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }

    public int getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static String getNameByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (CustomerStateEnum stateEnum : CustomerStateEnum.values()) {
            if (stateEnum.code == code) {
                return stateEnum.name;
            }
        }
        return null;
    }
}
