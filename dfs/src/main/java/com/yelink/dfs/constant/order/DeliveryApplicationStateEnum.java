package com.yelink.dfs.constant.order;


/**
 * @Description: 订单状态枚举
 * @Author: z<PERSON><PERSON><PERSON>
 * @Date: 2020/12/5
 */
public enum DeliveryApplicationStateEnum {

    /**
     * 状态编码及描述
     * 1-创建，2-审核，3- 批准 4-生效 5-完成 6-关闭 7-取消
     */
    CREATED(1, "创建"),
    //TO_EXAMINE(2, "已审核"),
    //APPROVAL(3, "已批准"),
    RELEASED(4, "生效"),
    FINISHED(5, "完成"),
    CLOSED(6, "关闭"),
    CANCELED(7, "取消");

    private int code;
    private String name;

    DeliveryApplicationStateEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }

    public int getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static String getNameByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (DeliveryApplicationStateEnum stateEnum : DeliveryApplicationStateEnum.values()) {
            if (stateEnum.code == code) {
                return stateEnum.name;
            }
        }
        return null;
    }

    public static Integer getCodeByName(String name) {
        for (DeliveryApplicationStateEnum stateEnum : DeliveryApplicationStateEnum.values()) {
            if (name.equals(stateEnum.name)) {
                return stateEnum.code;
            }
        }
        return null;
    }
}
