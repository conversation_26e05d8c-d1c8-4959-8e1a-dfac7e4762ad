package com.yelink.dfs.constant.sys;

import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @Description 单据名称code 枚举
 * @Date 2022/4/19 14:40
 */
public enum OrderCodeNameEnum {

    /**
     * code/单据名称
     */
    SALES_ORDER("salesOrder","销售订单"),
    PRODUCT_ORDER("productOrder","生产订单"),
    WORK_ORDER("workOrder","生产工单"),
    QUALITY_INSPECTION("qualityInspection","产品检验单"),
    MATERIAL("material","物料"),
    BOM("bom","bom"),
    ;


    /**
     * code
     */
    private String code;
    /**
     * name
     */
    private String name;

    OrderCodeNameEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }


    public static String getNameByCode(String code) {
        if (code == null) {
            return null;
        }
        for (OrderCodeNameEnum stateEnum : OrderCodeNameEnum.values()) {
            if (stateEnum.code.equals(code)) {
                return stateEnum.name;
            }
        }
        return null;
    }

    public static String getCodeByName(String name) {
        if (StringUtils.isBlank(name)) {
            return null;
        }
        for (OrderCodeNameEnum stateEnum : OrderCodeNameEnum.values()) {
            if (name.equals(stateEnum.name)) {
                return stateEnum.code;
            }
        }
        return null;
    }

}
