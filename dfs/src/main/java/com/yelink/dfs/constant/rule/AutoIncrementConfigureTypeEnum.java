package com.yelink.dfs.constant.rule;

import com.yelink.dfs.service.rule.RuleAutoIncrementConfigService;
import com.yelink.dfs.utils.SpringUtil;
import com.yelink.dfscommon.constant.dfs.CompatorStateEnumDTO;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

/**
 * @Description: 自增序号配置类型
 * @Author: sq
 * @Date: 2021/10/10
 */
public enum AutoIncrementConfigureTypeEnum {
    /**
     * 不归一
     */
    NO_CROSS("noCross", "不归一"),
    /**
     * 跨天归1
     */
    DAY("day", "跨天归1"),
    /**
     * 跨月归1
     */
    MONTH("month", "跨月归1"),
    /**
     * 跨年归1
     */
    YEAR("year", "跨年归1"),
    /**
     * 按生产工单归1
     */
    WORK_ORDER("workOrder", "按生产工单归1"),
    /**
     * 按生产订单归1
     */
    PRODUCT_ORDER("productOrder", "按生产订单归1"),
    /**
     * 按生产订单归1(仅下推有效)
     */
    PRODUCT_ORDER_ONLY_PUSH_DOWN("productOrderOnlyPushDown", "按生产订单归1(仅下推有效)"),
    /**
     * 按销售订单归1(仅下推有效)
     */
    SALE_ORDER_ONLY_PUSH_DOWN("saleOrderOnlyPushDown", "按销售订单归1(仅下推有效)"),
    /**
     * 按制造单元类型归1
     */
    LINE_MODEL("lineModel", "按制造单元类型归1"),
    /**
     * 按制造单元归1
     */
    LINE("line", "按制造单元归1"),
    /**
     * 按供应商编码归1
     */
    SUPPLIER("supplier", "按供应商编码归1"),
    /**
     * 按采购订单归1
     */
    PURCHASE("purchase", "按采购订单归1"),
    /**
     * 按采购收货归1
     */
    PURCHASE_RECEIPT("purchaseReceipt", "按采购收货归1"),
    /**
     * 按采购批次归1
     */
    PURCHASE_BATCH("purchaseBatch", "按采购批次归1"),
    /**
     * 按生产工单+包装层级归1
     */
    WORK_ORDER_PACKAGE("workOrderPackage", "按生产工单+包装层级归1"),
    /**
     * 按部门编码归1
     */
    DEPARTMENT_CODE("departmentCode", "按部门编码归1"),
    /**
     * 按客户编码归1
     */
    CUSTOMER_CODE("customerCode", "按客户编码归1"),
    /**
     * 按订单类型编码归1
     */
    SALE_ORDER_TYPE_CODE("saleOrderTypeCode", "按订单类型编码归1"),
    /**
     * 按生产工单归1(仅排产拆分有效)
     */
    WORK_ORDER_ONLY_SPLIT("workOrderOnlySplit", "按生产工单归1(仅排产拆分有效)"),
    /**
     * 按包装工单归1
     */
    PACKAGE_ORDER("packageOrder", "按包装工单归1"),
    /**
     * 按包装工单_包装层级归1
     */
    PACKAGE_ORDER_LEVEL("packageOrderLevel", "按包装工单_包装层级归1"),
    /**
     * 按物料类型编码归1
     */
    MATERIAL_TYPE_CODE("materialTypeCode", "按物料类型编码归1"),
    /**
     * 按物料编码归1
     */
    MATERIAL_CODE("materialCode", "按物料编码归1"),
    /**
     * 按生产工单号+制造单元编码归1
     */
    WORK_ORDER_NUMBER_LINE_CODE("workOrderNumberLineCode", "按生产工单号+制造单元编码归1"),
    BOM_CODE("bomCode", "按bom编码归一"),
    CRAFT_CODE("craftCode", "按工艺编码归一"),
    ;

    private String code;
    private String name;

    AutoIncrementConfigureTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static String getNameByCode(String code) {
        if (StringUtils.isBlank(code)) {
            return null;
        }
        for (AutoIncrementConfigureTypeEnum stateEnum : AutoIncrementConfigureTypeEnum.values()) {
            if (stateEnum.code.equals(code)) {
                return stateEnum.name;
            }
        }
        return null;
    }

    /**
     * 通过编号获取按xx归一所需其他组成信息
     */
    public static List<String> getNeedOtherRuleCodeByCode(String code) {
        RuleAutoIncrementConfigService service = SpringUtil.getBean(RuleAutoIncrementConfigService.class);
        return service.getNeedOtherRuleCodeByCode(code);
    }

    /**
     * 根据编码规则类型查询所有对应的归一信息
     */
    public static List<CompatorStateEnumDTO> getEnumByRuleType(String ruleType) {
        RuleAutoIncrementConfigService service = SpringUtil.getBean(RuleAutoIncrementConfigService.class);
        return service.getByRuleType(ruleType);
    }

    /**
     * 获取所有的归一信息
     */
    public static List<CompatorStateEnumDTO> getAllEnum() {
        RuleAutoIncrementConfigService service = SpringUtil.getBean(RuleAutoIncrementConfigService.class);
        return service.listAll();
    }

    public static String getCodeByName(String name) {
        for (AutoIncrementConfigureTypeEnum stateEnum : AutoIncrementConfigureTypeEnum.values()) {
            if (name.equals(stateEnum.name)) {
                return stateEnum.code;
            }
        }
        return null;
    }

    public static AutoIncrementConfigureTypeEnum getByCode(String code) {
        for (AutoIncrementConfigureTypeEnum typeEnum : AutoIncrementConfigureTypeEnum.values()) {
            if (code.equals(typeEnum.getCode())) {
                return typeEnum;
            }
        }
        return null;
    }
}
