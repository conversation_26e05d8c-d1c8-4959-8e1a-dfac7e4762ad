package com.yelink.dfs.constant.order;


/**
 * @Description: 康居人流程卡特殊检验项
 * @Author: shenzm
 * @Date: 2022/10/20
 */
public enum WorkOrderExportStateEnum {

    /**
     * 状态编码及描述
     *
     */
    DEBUG_CHECK("DEBUG_CHECK", "调试"),
    PRESSURE("pressure", "压力"),
    OXYGEN("oxygen","氧气浓度"),
    TRAFFIC("traffic","氧气流量"),
    INSPECTION("inspection","成品检验"),
    FILE_URL("url","classpath:template/kjrFlowCard.xlsx"),
    FILE_URLS("urls","classpath:template/kjrFlowCards.xlsx"),
    EXPORT_FLAG("2","2"),
    CHECK_RESULT("ok","合格"),
    PACKING("pack","包装"),
    ;

    private String code;
    private String name;

    WorkOrderExportStateEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static String getNameByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (WorkOrderExportStateEnum stateEnum : WorkOrderExportStateEnum.values()) {
            if (stateEnum.code.equals(code) ) {
                return stateEnum.name;
            }
        }
        return null;
    }

    public static String getCodeByName(String name) {
        for (WorkOrderExportStateEnum stateEnum : WorkOrderExportStateEnum.values()) {
            if (name.equals(stateEnum.name)) {
                return stateEnum.code;
            }
        }
        return null;
    }
}
