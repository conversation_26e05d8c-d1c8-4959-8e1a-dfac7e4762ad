package com.yelink.dfs.constant.statement;


/**
 * <AUTHOR>
 * @Date 2023/7/4 12:00
 */
public enum SourceTypeEnum {
    /**
     * 状态编码及描述
     */
    DFS("dfs", "DFS自定义数据"),
    API("api", "API自定义数据"),

    // 用于给别的地方关联
    API_BASE("apiBase", "API基础数据"),
    ;

    private final String code;
    private final String name;

    SourceTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

}
