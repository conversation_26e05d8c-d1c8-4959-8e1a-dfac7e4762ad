package com.yelink.dfs.constant.product;

/**
 * @description: 工艺类型枚举
 * @author: zhuang
 * @time: 2021/5/28
 */
public enum CraftTypeEnum {

    /**
     * 工艺类型枚举
     */
    NORMAL("normal", "正常"),
    REWORK("rework", "返工");

    private String code;
    private String name;

    CraftTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static String getNameByCode(String code) {
        for (CraftTypeEnum craftTypeEnum : CraftTypeEnum.values()) {
            if (craftTypeEnum.code.equals(code)) {
                return craftTypeEnum.name;
            }
        }
        return null;
    }

    public static String getCodeByName(String name) {
        for (CraftTypeEnum craftTypeEnum : CraftTypeEnum.values()) {
            if (craftTypeEnum.name.equals(name)) {
                return craftTypeEnum.code;
            }
        }
        return null;
    }

}
