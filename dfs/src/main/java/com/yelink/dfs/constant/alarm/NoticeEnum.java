package com.yelink.dfs.constant.alarm;

/**
 * @Description: 通知方式
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021/3/6
 */
public enum NoticeEnum {

    /**
     * 类型
     */
//    SMS(0, "短信"),
//    TEL(1, "电话"),
    JINGZHI(2, "精制消息"),
//    WEBSOCKET(3, "websocket页面弹窗"),
//    WX(4, "微信"),
    ;

    private Integer code;
    private String name;


    NoticeEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static String getNameByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (NoticeEnum noticeEnum : NoticeEnum.values()) {
            if (code.intValue() == noticeEnum.code.intValue()) {
                return noticeEnum.getName();
            }
        }
        return null;
    }
}
