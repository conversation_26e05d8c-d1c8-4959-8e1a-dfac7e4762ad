package com.yelink.dfs.constant.node;

import com.baomidou.mybatisplus.annotation.EnumValue;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * 节点状态
 * @Date 2022/4/24 10:00
 */
public enum NodeStateEnum {

    /**
     * 节点状态
     * 1-创建 2-生效 3-作废
     */
    CREATE(1, "创建"),
    RELEASE(2, "生效"),
    STOP_USING(3, "作废");

    @EnumValue
    private int code;
    private String name;

    NodeStateEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }

    public int getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static String getNameByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (NodeStateEnum stateEnum : NodeStateEnum.values()) {
            if (stateEnum.code == code) {
                return stateEnum.name;
            }
        }
        return null;
    }

    public static Integer getCodeByName(String name) {
        if (StringUtils.isBlank(name)) {
            return null;
        }
        for (NodeStateEnum stateEnum : NodeStateEnum.values()) {
            if (stateEnum.name.equals(name)) {
                return stateEnum.code;
            }
        }
        return null;
    }
}
