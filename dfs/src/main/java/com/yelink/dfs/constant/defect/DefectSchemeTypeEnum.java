package com.yelink.dfs.constant.defect;


/**
 * @Description: 质检方案的质检类型枚举
 * @Author: z<PERSON><PERSON><PERSON>
 * @Date: 2020/12/5
 */
public enum DefectSchemeTypeEnum {

    /**
     * 状态编码及描述
     * 0-创建 1-生效
     */
    STATION_SEMIFINISHED("stationSemifinished", "工位质检-半成品"),
    STATION_FINISHED("stationFinished", "工位质检-成品");

    private String code;
    private String name;

    DefectSchemeTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static String getNameByCode(String code) {
        if (code == null) {
            return null;
        }
        for (DefectSchemeTypeEnum stateEnum : DefectSchemeTypeEnum.values()) {
            if (stateEnum.code.equals(code)) {
                return stateEnum.name;
            }
        }
        return null;
    }

    public static String getCodeByName(String name) {
        for (DefectSchemeTypeEnum stateEnum : DefectSchemeTypeEnum.values()) {
            if (name.equals(stateEnum.name)) {
                return stateEnum.code;
            }
        }
        return null;
    }
}
