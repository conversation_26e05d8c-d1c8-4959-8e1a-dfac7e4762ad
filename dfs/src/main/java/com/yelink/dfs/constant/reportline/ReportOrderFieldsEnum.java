package com.yelink.dfs.constant.reportline;


/**
 * 报工生产订单字段显示枚举
 *
 * <AUTHOR>
 * @date 2022-08-23
 */
public enum ReportOrderFieldsEnum {

    RELATED_ORDER_NUM("relatedOrderNum", "上级生产订单号"),
    RELATED_ROOT_ORDER_NUM("relatedRootOrderNum", "根生产订单号"),
    CUSTOMER_NAME("customerName", "客户名称"),
    CUSTOMER_CODE("customerCode", "客户代码"),
    SALE_ORDER_NUMBER("saleOrderCode", "销售订单号"),
    REQUIRE_GOODS_DATE("requireGoodsDate", "要货日期"),
    ORDER_DATE("orderDate", "下单日期"),
    SALES_QUANTITY("salesQuantity", "销售数量"),
    PLAN_QUANTITY("planQuantity", "计划数量"),
    FINISH_COUNT("finishCount", "完成数量"),
    UNQUALIFIED_COUNT("unqualifiedCount", "不良数量"),
    ;
    private final String code;
    private final String name;

    ReportOrderFieldsEnum(String type, String name) {
        this.code = type;
        this.name = name;
    }

    public String getType() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static String getNameByType(String type) {
        if (type == null) {
            return null;
        }
        for (ReportOrderFieldsEnum en : ReportOrderFieldsEnum.values()) {
            if (en.code.equals(type)) {
                return en.name;
            }
        }
        return null;
    }

    public static String getTypeByName(String name) {
        if (name == null) {
            return null;
        }
        for (ReportOrderFieldsEnum en : ReportOrderFieldsEnum.values()) {
            if (name.equals(en.name)) {
                return en.code;
            }
        }
        return null;
    }
}

