package com.yelink.dfs.constant.maintain;


/**
 * @Description: 维修定义状态枚举
 * @Author: z<PERSON><PERSON><PERSON>
 * @Date: 2020/12/5
 */
public enum MaintainDefineStateEnum {

    /**
     * 状态编码及描述
     * 0-创建 1-生效
     */
    CREATE(0, "创建"),
    RELEASED(1, "生效");

    private int code;
    private String name;

    MaintainDefineStateEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }

    public int getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static String getNameByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (MaintainDefineStateEnum stateEnum : MaintainDefineStateEnum.values()) {
            if (stateEnum.code == code) {
                return stateEnum.name;
            }
        }
        return null;
    }

    public static Integer getCodeByName(String name) {
        for (MaintainDefineStateEnum stateEnum : MaintainDefineStateEnum.values()) {
            if (name.equals(stateEnum.name)) {
                return stateEnum.code;
            }
        }
        return null;
    }
}
