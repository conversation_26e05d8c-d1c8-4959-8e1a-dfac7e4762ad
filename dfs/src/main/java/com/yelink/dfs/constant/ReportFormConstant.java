package com.yelink.dfs.constant;

/**
 * @description: 报表相关常量定义
 * @author: shuang
 * @time: 2021/12/17
 */
public class ReportFormConstant {

    /**
     * 模板存储文件夹
     */
    public static final String TEMPLATE_DIRECTORY_NAME = "/template";
    /**
     * 导入日志存储文件夹
     */
    public static final String IMPORT_LOG = "/importLog";

    /**
     * 生产日报表模板名称
     */
    public static final String PRODUCT_DAY_REPORT_TEMPLATE_NAME = "productDayReport";

    /**
     * 生产日报表名称
     */
    public static final String PRODUCT_DAY_REPORT_NAME = "生产日报表";

    /**
     * 默认生产日报模板名称
     */
    public static final String DEFAULT_PRODUCTION_DAILY_TEMPLATE = "DefaultProductionDailyTemplate";
    /**
     * 至美默认生产日报模板名称
     */
    public static final String ZHIMEI_DEFAULT_PRODUCTION_DAILY_TEMPLATE = "ZhiMeiDefaultDailyTemplate";
    /**
     * 用户自己上传的生产日报模板名称
     */
    public static final String PRODUCTION_DAILY_TEMPLATE = "productionDailyTemplate";
    /**
     * 默认的生产统计指标模板
     */
    public static final String DEFAULT_PRODUCTION_TEMPLATE = "DefaultStatisticsIndicatorTemplate";
    /**
     * 用户自己上传的生产统计指标模板
     */
    public static final String PRODUCTION_STATISTICS_INDICATOR_TEMPLATE = "productionStatisticsIndicator";

    /**
     * 报表指标名称定义
     */
    public static final String PRODUCT_MODEL_GOOD_RATE = "产线良品率";
    public static final String PRODUCT_MODEL_OEE = "产线OEE";
    public static final String PRODUCT_MODEL_ACHIEVEMENT_RATE = "工单达成率";
    public static final String PRODUCT_MODEL_COMPLETE_RATE = "产量完成率";
    public static final String PRODUCT_MODEL_YIELD_UNIT_RATE = "产线产量(%s)";
    public static final String PRODUCT_MODEL_START_TIME = "产线开工时长（小时）";
    public static final String PRODUCT_MODEL_YIELD_UNIT2_DAY_RATE = "每天计划产量（%s）";



    /**
     * 工序导入模板名称
     */
    public static final String PROCEDURE_TEMPLATE_NAME = "procedureTemplate";



    /**
     * 单据类型
     */
    public static final String BILL_MODE = "单据";
    /**
     * 列表类型
     */
    public static final String LIST_MODE = "列表";


    /**
     * 默认生产工单追溯模板名称
     */
    public static final String DEFAULT_WORK_ORDER_TRACE_TEMPLATE = "defaultWorkOrderTraceTemplate";

    /**
     * 默认质检返工记录模板
     */
    public static final String DEFAULT_MAINTAIN_RECORD_TEMPLATE = "defaultMaintainRecordTemplate";

    /**
     * 默认质检返工报表模板
     */
    public static final String DEFAULT_MAINTAIN_STATEMENT_TEMPLATE = "defaultMaintainStatementTemplate";
    /**
     * 默认质检返工记录模板
     */
    public static final String MAINTAIN_RECORD_TEMPLATE = "maintainRecordTemplate";

    /**
     * 默认质检返工报表模板
     */
    public static final String MAINTAIN_STATEMENT_TEMPLATE = "maintainStatementTemplate";

    /**
     * 用户上传的生产工单追溯模板名称
     */
    public static final String WORK_ORDER_TRACE_TEMPLATE = "workOrderTraceTemplate";

    /**
     * 用户上传的工位质检记录模板名称
     */
    public static final String DEFECT_RECORD_TEMPLATE = "defectRecordTemplate";

    /**
     * 用户上传的工位质检报表模板名称
     */
    public static final String DEFECT_STATEMENT_TEMPLATE = "defectStatementTemplate";

    /**
     * 默认客户档案上传模板
     */
    public static final String DEFAULT_CUSTOMER_TEMPLATE = "defaultCustomerTemplate";
    /**
     * 默认客户物料清单上传模板
     */
    public static final String DEFAULT_CUSTOMER_MATERIAL_LIST_TEMPLATE = "defaultCustomerMaterialListTemplate";

    /**
     * 默认供应商档案上传模板
     */
    public static final String DEFAULT_SUPPLIER_TEMPLATE = "defaultSupplierTemplate";


    /**
     * 默认的工单信息和bom信息单据模板
     */
    public static final String DEFAULT_WORK_ORDER_BOM_TEMPLATE = "workOrderBillTemplate";
    ;
}
