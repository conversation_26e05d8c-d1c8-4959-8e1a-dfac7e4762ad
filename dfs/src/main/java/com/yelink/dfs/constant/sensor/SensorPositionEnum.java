package com.yelink.dfs.constant.sensor;

/**
 * @description: 设备位置
 * @author: shuang
 * @time: 2020/6/18
 */
public enum SensorPositionEnum {

    /**
     * 设备
     * 0-入 1-出 2-中间
     */
    INPUT(0, "输入"),
    OUTPUT(1, "输出"),
    MIDDLE(2, "中间");


    private int code;
    private String name;


    SensorPositionEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }


    public int getCode() {
        return code;
    }

    public String getName() {
        return name;
    }


    public static String getNameByCode(int code) {
        for (SensorPositionEnum sensorTypeCodeEnum : SensorPositionEnum.values()) {
            if (sensorTypeCodeEnum.getCode() == (code)) {
                return sensorTypeCodeEnum.getName();
            }
        }
        return null;
    }


}
