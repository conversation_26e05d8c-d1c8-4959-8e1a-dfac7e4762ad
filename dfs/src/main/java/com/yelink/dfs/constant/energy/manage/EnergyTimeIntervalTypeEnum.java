package com.yelink.dfs.constant.energy.manage;

/**
 * <AUTHOR>
 * @Date 2022/7/7 9:32
 */
public enum EnergyTimeIntervalTypeEnum {
    /**
     * 生效时间、时段
     */
    EFFECTIVE_TIME("EFFECTIVE_TIME", "生效时间"),
    TIME_PERIOD("TIME_PERIOD", "时段");

    private final String code;
    private final String name;

    EnergyTimeIntervalTypeEnum(String code, String type) {
        this.code = code;
        this.name = type;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static EnergyTimeIntervalTypeEnum getEnumByCode(String code) {
        for (EnergyTimeIntervalTypeEnum typeEnum : EnergyTimeIntervalTypeEnum.values()) {
            if (typeEnum.code.equals(code)) {
                return typeEnum;
            }
        }
        return null;
    }
}
