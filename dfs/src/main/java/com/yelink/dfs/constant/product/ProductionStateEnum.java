package com.yelink.dfs.constant.product;

import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;

/**
 * @Description: 试产状态枚举
 * @Author: zhuangwq
 * @Date: 2021/04/08
 */
public enum ProductionStateEnum {

    /**
     * 物料/bom状态
     * 1-创建 2-生效 3-停用 4-废弃
     */
    TEST_PRODUCTION("testProduction", "试产"),
    MASS_PRODUCTION("massProduction", "量产"),
    ;

    private String code;
    private String name;

    ProductionStateEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static ProductionStateEnum getByName(String stateName) {
        return Arrays.stream(ProductionStateEnum.values()).filter(v -> stateName.equals(v.getName())).findFirst().orElse(null);
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static String getNameByCode(String code) {
        if (code == null) {
            return null;
        }
        for (ProductionStateEnum stateEnum : ProductionStateEnum.values()) {
            if (stateEnum.code.equals(code)) {
                return stateEnum.name;
            }
        }
        return null;
    }

    public static String getCodeByName(String name) {
        if (StringUtils.isBlank(name)) {
            return null;
        }
        for (ProductionStateEnum stateEnum : ProductionStateEnum.values()) {
            if (stateEnum.name.equals(name)) {
                return stateEnum.code;
            }
        }
        return null;
    }
}
