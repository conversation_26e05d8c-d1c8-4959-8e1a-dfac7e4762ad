package com.yelink.dfs.constant.code;

import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @Date 2022/4/1 9:50
 */
public enum ScannerQualityStateEnum {
    /**
     * 质量状态编码及描述
     */
    QUALIFIED("qualified", "合格"),
    UNQUALIFIED("unqualified", "不合格"),
    ;

    private String code;
    private String name;

    ScannerQualityStateEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static String getNameByCode(String code) {
        if (StringUtils.isBlank(code)) {
            return null;
        }
        for (ScannerQualityStateEnum stateEnum : ScannerQualityStateEnum.values()) {
            if (stateEnum.code.equals(code)) {
                return stateEnum.name;
            }
        }
        return null;
    }

    public static String getCodeByName(String name) {
        for (ScannerQualityStateEnum stateEnum : ScannerQualityStateEnum.values()) {
            if (name.equals(stateEnum.name)) {
                return stateEnum.code;
            }
        }
        return null;
    }
}
