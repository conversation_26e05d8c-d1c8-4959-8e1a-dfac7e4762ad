package com.yelink.dfs.constant.notice;


import lombok.Getter;

/**
 * 消息通知限制范围类型
 * <AUTHOR>
 */
public enum NoticeLimitScopeEnum {

    /**
     * 消息通知限制范围类型
     */
    LINE("line", "产线范围"),
    WORK_CENTER("workCenter","工作中心范围"),
    NO("no","无限制"),
    ;

    @Getter
    private final String code;
    @Getter
    private final String name;

    NoticeLimitScopeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static String getNameByCode(String code) {
        for (NoticeLimitScopeEnum e : NoticeLimitScopeEnum.values()) {
            if (e.code.equals(code)) {
                return e.name;
            }
        }
        return null;
    }
    public static NoticeLimitScopeEnum getByCode(String code) {
        for (NoticeLimitScopeEnum e : NoticeLimitScopeEnum.values()) {
            if (e.code.equals(code)) {
                return e;
            }
        }
        return null;
    }

}
