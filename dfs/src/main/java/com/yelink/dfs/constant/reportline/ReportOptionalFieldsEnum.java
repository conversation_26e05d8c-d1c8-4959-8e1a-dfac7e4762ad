package com.yelink.dfs.constant.reportline;


/**
 * 报工非必填字段显示枚举
 *
 * <AUTHOR>
 * @date 2022-08-23
 */
public enum ReportOptionalFieldsEnum {
    //关联资源类型、生产工时、载具号、班次、批次、操作员、人员数量；（默认全选）

    RESOURCE_TYPE("resourceType", "关联资源类型"),
    EFFECTIVE_HOURS("effectiveHours", "生产工时"),
    VEHICLE_CODE("vehicleCode", "载具号"),
    SHIFT_TYPE("shiftType", "班次"),
    BATCH("batch", "批次"),
    OPERATOR("operator", "操作员"),
    UNQUALIFIED("unqualified", "不良数量"),
    CREW_SIZE("crewSize", "人员数量"),
    LOSS_QUANTITY("lossQuantity", "损耗数量"),
    ;
    private String code;
    private String name;

    ReportOptionalFieldsEnum(String type, String name) {
        this.code = type;
        this.name = name;
    }

    public String getType() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static String getNameByType(String type) {
        if (type == null) {
            return null;
        }
        for (ReportOptionalFieldsEnum en : ReportOptionalFieldsEnum.values()) {
            if (en.code.equals(type)) {
                return en.name;
            }
        }
        return null;
    }

    public static String getTypeByName(String name) {
        if (name == null) {
            return null;
        }
        for (ReportOptionalFieldsEnum en : ReportOptionalFieldsEnum.values()) {
            if (name.equals(en.name)) {
                return en.code;
            }
        }
        return null;
    }
}

