package com.yelink.dfs.constant.reset;

import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/4/1 9:50
 */
public enum ConfigMigrateTableEnum {

    /**
     * 软件参数配置
     */
    DFS_BUSINESS_CONFIG_VALUE("businessConfig", "dfs_business_config_value", "id", "value_full_path_code", "update"),
    /**
     * 表单配置
     */
    DFS_FORM_FIELD_CONFIG("formConfig", "dfs_form_field_config", "id", "full_path_code,field_code,type_code,module_code", "update"),
    DFS_FORM_FIELD_RULE_CONFIG("formConfig", "dfs_form_field_rule_config", "id", "full_path_code,field_code,module_code", "update"),
    /**
     * 全局扩展字段配置
     */
    DFS_FORM_OVERALL_FIELD_CONFIG("formOverallFieldConfig", "dfs_form_overall_field_config", "id", "id", "insert"),
    DFS_FORM_OVERALL_FIELD_MAPPING_CONFIG("formOverallFieldConfig", "dfs_form_overall_field_mapping_config", "id", "id", "insert"),
    DFS_FORM_OVERALL_FIELD_RULE_CONFIG("formOverallFieldConfig", "dfs_form_overall_field_rule_config", "id", "id", "insert"),
    DFS_FIELD_MAPPING("formOverallFieldConfig", "dfs_field_mapping", "id", "id", "insert"),
    /**
     * 单据下推配置
     */
    DFS_ORDER_PUSH_DOWN_ITEM("pushDownConfig", "dfs_order_push_down_item", "id", "id", "insert"),
    DFS_ORDER_PUSH_DOWN_CONFIG_VALUE("pushDownConfig", "dfs_order_push_down_config_value", "id", "id", "insert"),
    DFS_ORDER_PUSH_DOWN_ITEM_VERIFY("pushDownConfig", "dfs_order_push_down_item_verify", "id", "id", "insert"),
    DFS_ORDER_PUSH_DOWN_ITEM_WRITE_BACK("pushDownConfig", "dfs_order_push_down_item_write_back", "id", "id", "insert"),
    /**
     * 编码规则
     */
    DFS_CONFIG_NUMBER_RULES("ruleConfig", "dfs_config_number_rules", "id", "id", "insert"),
    /**
     * 演示数据
     */
    COMPONENT_DATA("componentData", "dfs_component_data", "id", "id", "insert"),

    ;

    private String code;
    private String tableName;
    /**
     * 主键
     */
    private String primaryKey;
    /**
     * 唯一键
     */
    private String uniqueKey;
    /**
     * 操作，新增insert，更新update
     */
    private String operate;

    ConfigMigrateTableEnum(String code, String tableName, String primaryKey, String uniqueKey, String operate) {
        this.code = code;
        this.tableName = tableName;
        this.primaryKey = primaryKey;
        this.uniqueKey = uniqueKey;
        this.operate = operate;
    }

    public String getCode() {
        return code;
    }

    public String getTableName() {
        return tableName;
    }

    public String getPrimaryKey() {
        return primaryKey;
    }

    public String getUniqueKey() {
        return uniqueKey;
    }

    public String getOperate() {
        return operate;
    }

    public static String getTableNameByCode(String code) {
        if (StringUtils.isBlank(code)) {
            return null;
        }
        for (ConfigMigrateTableEnum stateEnum : ConfigMigrateTableEnum.values()) {
            if (stateEnum.code.equals(code)) {
                return stateEnum.tableName;
            }
        }
        return null;
    }

    public static String getCodeByName(String tableName) {
        for (ConfigMigrateTableEnum stateEnum : ConfigMigrateTableEnum.values()) {
            if (tableName.equals(stateEnum.tableName)) {
                return stateEnum.code;
            }
        }
        return null;
    }

    public static List<ConfigMigrateTableEnum> getTables(List<String> codes) {
        List<ConfigMigrateTableEnum> list = new ArrayList<>();
        for (ConfigMigrateTableEnum stateEnum : ConfigMigrateTableEnum.values()) {
            if (codes.contains(stateEnum.code)) {
                list.add(stateEnum);
            }
        }
        return list;
    }

}
