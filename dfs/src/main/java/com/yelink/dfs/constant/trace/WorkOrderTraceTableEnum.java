package com.yelink.dfs.constant.trace;

/**
 * <AUTHOR> @description 工单追溯信息枚举
 * @Date 2022/5/9
 */
public enum WorkOrderTraceTableEnum {

    /**
     * 枚举
     */
    PRODUCT_ORDER("productOrder", "工单追溯-生产订单"),
    WORK_ORDER_DETAIL("workOrderDetail", "工单追溯-工单详情"),
    REPORT_RECORD("reportRecord", "工单追溯-报工记录"),
    PURCHASE_REQUEST("purchaseRequest", "工单追溯-采购需求单"),
    PURCHASE("purchase", "工单追溯-采购订单"),
    BOM("BOM", "工单追溯-BOM"),
    CRAFT("craft", "工单追溯-工艺"),

    DEVICE_RUNNING_RECORD("deviceRunningRecord", "工单追溯-设备运行记录"),
    DEVICE_RUNNING_PARAM("deviceRunningParam", "工单追溯-设备运行参数"),
    DEVICE_ALARM("deviceAlarm", "工单追溯-设备告警"),
    CAPACITY("capacity", "工单追溯-产能节拍"),
    BAR_CODE("barCode", "工单追溯-工单成品码"),

    PRODUCT_OUTPUT("productOutput", "工单追溯-成品出库单"),
    TAKE_OUT_APPLICATION("takeOutApplication", "工单追溯-仓库领料单"),

    DEFECT_RECORD("defectRecord", "工单追溯-设备质检"),
    PASS_RATE("passRate", "工单追溯-合格率"),

    FEEDING_INFO("feedingInfo", "工单追溯-上料信息"),
    ;

    private String code;
    private String name;

    WorkOrderTraceTableEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }


    public static String getNameByCode(String code) {
        if (code == null) {
            return null;
        }
        for (WorkOrderTraceTableEnum targetShowTypeEnum : WorkOrderTraceTableEnum.values()) {
            if (targetShowTypeEnum.code.equals(code)) {
                return targetShowTypeEnum.name;
            }
        }
        return null;
    }

}
