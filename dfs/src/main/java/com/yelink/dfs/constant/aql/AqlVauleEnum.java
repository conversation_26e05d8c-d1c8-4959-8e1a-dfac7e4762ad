package com.yelink.dfs.constant.aql;

import java.util.ArrayList;
import java.util.List;

/**
 * @Description: AQL批量范围枚举
 * @Author: Chensn
 * @Date: 2023/5/30
 */
public enum AqlVauleEnum {

    /**
     * AQL值枚举
     */
    AC_0010(0.01, "ac0010"),
    RE_0010(0.01, "re0010"),
    AC_0015(0.015, "ac0015"),
    RE_0015(0.015, "re0015"),
    AC_0025(0.025, "ac0025"),
    RE_0025(0.025, "re0025"),
    AC_004(0.04, "ac0040"),
    RE_004(0.04, "re0040"),
    AC_0065(0.065, "ac0065"),
    RE_0065(0.065, "re0065"),
    AC_010(0.10, "ac010"),
    RE_010(0.10, "re010"),
    AC_015(0.15, "ac015"),
    RE_015(0.15, "re015"),
    AC_025(0.25, "ac025"),
    RE_025(0.25, "re025"),
    AC_040(0.40, "ac040"),
    RE_040(0.40, "re040"),
    AC_065(0.65, "ac065"),
    RE_065(0.65, "re065"),
    AC_1(1.0, "ac1"),
    RE_1(1.0, "re1"),
    AC_105(1.5, "ac105"),
    RE_105(1.5, "re105"),
    AC_205(2.5, "ac205"),
    RE_205(2.5, "re205"),
    AC_4(4.0, "ac4"),
    RE_4(4.0, "re4"),
    AC_605(6.5, "ac605"),
    RE_605(6.5, "re605"),
    AC_10(10.0, "ac10"),
    RE_10(10.0, "re10"),
    AC_15(15.0, "ac15"),
    RE_15(15.0, "re15"),
    AC_25(25.0, "ac25"),
    RE_25(25.0, "re25"),
    AC_40(40.0, "ac40"),
    RE_40(40.0, "re40"),
    AC_65(65.0, "ac65"),
    RE_65(65.0, "re65"),
    AC_100(100.0, "ac100"),
    RE_100(100.0, "re100"),
    AC_150(150.0, "ac150"),
    RE_150(150.0, "re150"),
    AC_250(250.0, "ac250"),
    RE_250(250.0, "re250"),
    AC_400(400.0, "ac400"),
    RE_400(400.0, "re400"),
    AC_650(650.0, "ac650"),
    RE_650(650.0, "re650"),
    AC_1000(1000.0, "ac1000"),
    RE_1000(1000.0, "re1000"),

    ;

    private final Double value;
    private final String code;


    AqlVauleEnum(Double value, String code) {
        this.value = value;
        this.code = code;
    }

    public String getCode() {
        return code;
    }

    public Double getvalue() {
        return value;
    }


    /**
     * 根据aql值获取对应字段code
     *
     * @param value
     * @return
     */
    public static List<String> getCodesByValue(Double value) {
        ArrayList<String> list = new ArrayList<>();
        if (value == null) {
            return list;
        }
        for (AqlVauleEnum aqlVauleEnum : AqlVauleEnum.values()) {
            if (value.equals(aqlVauleEnum.getvalue())) {
                list.add(aqlVauleEnum.getCode());
            }
        }
        return list;
    }

}
