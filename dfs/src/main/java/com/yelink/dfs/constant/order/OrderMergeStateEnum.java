package com.yelink.dfs.constant.order;


import java.util.Arrays;

/**
 * @Description: 订单合单状态枚举
 * @Author: zhuangwq
 * @Date: 2022/5/30
 */
public enum OrderMergeStateEnum {

    /**
     * 状态编码及描述
     * 0-否 1-被合 2-合
     */
    NOT_MERGE(0, "未合单"),
    IS_MERGED(1, "被合单"),
    MERGE(2, "合单"),
    ;

    private int code;
    private String name;

    OrderMergeStateEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }

    public int getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
    public static Object getByName(String stateName) {
        if(stateName == null){
            return null;
        }
        return Arrays.stream(OrderMergeStateEnum.values()).filter(v -> stateName.equals(v.getName())).findFirst().orElse(null);
    }

    public static String getNameByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (OrderMergeStateEnum stateEnum : OrderMergeStateEnum.values()) {
            if (stateEnum.code == code) {
                return stateEnum.name;
            }
        }
        return null;
    }

    public static Integer getCodeByName(String name) {
        if(name == null){
            return null;
        }
        for (OrderMergeStateEnum stateEnum : OrderMergeStateEnum.values()) {
            if (name.equals(stateEnum.name)) {
                return stateEnum.code;
            }
        }
        return null;
    }
}
