package com.yelink.dfs.constant.open;



/**
 * @Description:
 * @Author: z<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022/10/19
 */
public enum SortFieldsEnum {

    /**
     * 排序字段枚举
     */
    CREATE_TIME("createTime"),
    UPDATE_TIME("updateTime"),

    ;

    private String name;


    SortFieldsEnum(String name) {
        this.name = name;
    }

    public String getName() {
        return name;
    }



}
