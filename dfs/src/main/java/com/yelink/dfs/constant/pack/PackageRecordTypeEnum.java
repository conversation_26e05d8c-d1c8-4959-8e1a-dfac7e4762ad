package com.yelink.dfs.constant.pack;

import com.baomidou.mybatisplus.annotation.EnumValue;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;

/**
 * @Description: 包装记录类型枚举
 * @Author: Chensn
 * @Date: 2021/04/08
 */
public enum PackageRecordTypeEnum {

    /**
     * 包装记录类型枚举
     */
    PACKAGING("packaging", "包装"),
    DEVANNING("devanning", "拆箱"),
    ;

    @EnumValue
    private String code;
    private String name;

    PackageRecordTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static PackageRecordTypeEnum getByName(String stateName) {
        return Arrays.stream(PackageRecordTypeEnum.values()).filter(v -> stateName.equals(v.getName())).findFirst().orElse(null);
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static String getNameByCode(String code) {
        if (code == null) {
            return null;
        }
        for (PackageRecordTypeEnum stateEnum : PackageRecordTypeEnum.values()) {
            if (stateEnum.code.equals(code)) {
                return stateEnum.name;
            }
        }
        return null;
    }

    public static String getCodeByName(String name) {
        if (StringUtils.isBlank(name)) {
            return null;
        }
        for (PackageRecordTypeEnum stateEnum : PackageRecordTypeEnum.values()) {
            if (stateEnum.name.equals(name)) {
                return stateEnum.code;
            }
        }
        return null;
    }
}
