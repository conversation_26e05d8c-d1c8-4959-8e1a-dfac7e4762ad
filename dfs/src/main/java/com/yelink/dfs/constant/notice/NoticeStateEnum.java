package com.yelink.dfs.constant.notice;



/**
 * @Description: 消息通知类型
 * @Author:
 * @Date: 2020/12/5
 */
public enum NoticeStateEnum {

    /**
     * 消息通知类型
     */
    ENABLE(1, "启用"),
    DISABLE(2,"停用"),
    ;

    private Integer code;
    private String name;

    NoticeStateEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static String getNameByCode(Integer code) {
        for (NoticeStateEnum stateEnum : NoticeStateEnum.values()) {
            if (stateEnum.code.equals(code)) {
                return stateEnum.name;
            }
        }
        return null;
    }

    public static Integer getCodeByName(String name) {
        for (NoticeStateEnum stateEnum : NoticeStateEnum.values()) {
            if (name.equals(stateEnum.name)) {
                return stateEnum.code;
            }
        }
        return null;
    }
}
