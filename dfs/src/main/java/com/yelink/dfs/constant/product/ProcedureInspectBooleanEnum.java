package com.yelink.dfs.constant.product;

/**
 * <AUTHOR>
 * @Description 工序检验项布尔类型的字段值
 * @Date 2022/5/9 10:08
 */
public enum ProcedureInspectBooleanEnum {
    /**
     * 数据类型
     */
    Y("Y","y"),
    N("N","n"),
    Y_OR_NO("Y,N","y,n");


    /**
     * 字段名
     */
    private String fieldEname;
    /**
     * 字段名称
     */
    private String fieldName;

    ProcedureInspectBooleanEnum(String fieldEname, String fieldName) {
        this.fieldEname = fieldEname;
        this.fieldName = fieldName;
    }

    public String getFieldEname() {
        return fieldEname;
    }

    public String getFieldName() {
        return fieldName;
    }


    public static String getNameByCode(String fieldEname) {
        if (fieldEname == null) {
            return null;
        }
        for (ProcedureInspectBooleanEnum stateEnum : ProcedureInspectBooleanEnum.values()) {
            if (stateEnum.fieldEname.equals(fieldEname)) {
                return stateEnum.fieldName;
            }
        }
        return null;
    }

    public static String getCodeByName(String fieldName) {
        for (ProcedureInspectBooleanEnum stateEnum : ProcedureInspectBooleanEnum.values()) {
            if (fieldName.equals(stateEnum.fieldName)) {
                return stateEnum.fieldEname;
            }
        }
        return null;
    }
}
