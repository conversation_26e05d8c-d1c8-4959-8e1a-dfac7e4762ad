package com.yelink.dfs.constant.order;

import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @Date 2021/3/2 15:35
 */
public enum OrderTypeEnum {

    /**
     * 类型
     */
    WORK_ORDER("workOrder", "生产工单"),
    OPERATION_ORDER("operationOrder", "作业工单");

    private String type;
    private String typeName;


    OrderTypeEnum(String type, String typeName) {
        this.type = type;
        this.typeName = typeName;
    }

    public String getType() {
        return type;
    }

    public String getTypeName() {
        return typeName;
    }


    public static OrderTypeEnum getByType(String type) {
        if (StringUtils.isBlank(type)) {
            return null;
        }
        for (OrderTypeEnum orderTypeEnum : OrderTypeEnum.values()) {
            if (type.equals(orderTypeEnum.getType())) {
                return orderTypeEnum;
            }
        }
        return null;
    }

    public static OrderTypeEnum getNameByType(String typeName) {
        for (OrderTypeEnum orderTypeEnum : OrderTypeEnum.values()) {
            if (orderTypeEnum.getTypeName().equals(typeName)) {
                return orderTypeEnum;
            }
        }
        return null;
    }
}
