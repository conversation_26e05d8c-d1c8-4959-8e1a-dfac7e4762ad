package com.yelink.dfs.constant.code;

/**
 * <AUTHOR>
 * @Date 2022/4/1 9:50
 */
public enum ProductFlowCodeQualityStateEnum {
    /**
     * 质检状态编码及描述
     */
    TRUE(0,"合格"),
    FALSE(1,"不合格")
    ;

    private Integer code;
    private String name;

    ProductFlowCodeQualityStateEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }

    public int getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static String getNameByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (ProductFlowCodeQualityStateEnum stateEnum : ProductFlowCodeQualityStateEnum.values()) {
            if (stateEnum.code.equals(code)) {
                return stateEnum.name;
            }
        }
        return null;
    }

    public static Integer getCodeByName(String name) {
        for (ProductFlowCodeQualityStateEnum stateEnum : ProductFlowCodeQualityStateEnum.values()) {
            if (name.equals(stateEnum.name)) {
                return stateEnum.code;
            }
        }
        return null;
    }
}
