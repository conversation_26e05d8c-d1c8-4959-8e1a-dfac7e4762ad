package com.yelink.dfs.constant.statement;

import com.yelink.dfs.constant.common.ModelUploadFileEnum;

/**
 * 报表管理的默认模板
 *
 * <AUTHOR>
 * @Date 2023/7/4 12:00
 */
public enum ReportDefaultTemplateEnum {
    /**
     * 报表管理的数据源:
     *  销售订单、生产订单、生产工单、质检明细表、生产工单绩效
     */
    MANAGE_REPORT_PRODUCT_REPORT("manageReportProductReport", "生产报表", "适用数据源，原生数据-生产工单", ModelUploadFileEnum.MANAGE_REPORT_PRODUCT_REPORT.getCode()),
    MANAGE_REPORT_QUALITY_STATEMENT("manageReportQualityStatement", "质量报表", "适用数据源，原生数据-质检明细表", ModelUploadFileEnum.MANAGE_REPORT_QUALITY_STATEMENT.getCode()),

    MANAGE_REPORT_PROCESSING_PRODUCTION_SCHEDULE("manageReportProcessingProductionSchedule", "离散加工-生产日表", "适用数据源，离散加工-生产日表", ModelUploadFileEnum.MANAGE_REPORT_PROCESSING_PRODUCTION_SCHEDULE.getCode()),
    MANAGE_REPORT_PROCESSING_QUALITY_STATISTICS("manageReportProcessingQualityStatistics", "离散加工-质量统计", "适用数据源，离散加工-质量统计", ModelUploadFileEnum.MANAGE_REPORT_PROCESSING_QUALITY_STATISTICS.getCode()),
    ;

    private final String defaultTemplateCode;
    private final String defaultTemplateName;
    private final String desc;
    private final Integer modelType;

    ReportDefaultTemplateEnum(String defaultTemplateCode, String defaultTemplateName, String desc, Integer modelType) {
        this.defaultTemplateCode = defaultTemplateCode;
        this.defaultTemplateName = defaultTemplateName;
        this.desc = desc;
        this.modelType = modelType;
    }

    public String getDefaultTemplateCode() {
        return defaultTemplateCode;
    }

    public String getDefaultTemplateName() {
        return defaultTemplateName;
    }

    public String getDesc() {
        return desc;
    }

    public Integer getModelType() {
        return modelType;
    }

    public static ReportDefaultTemplateEnum getEnumByCode(String defaultTemplateCode) {
        for (ReportDefaultTemplateEnum value : ReportDefaultTemplateEnum.values()) {
            if (value.getDefaultTemplateCode().equals(defaultTemplateCode)) {
                return value;
            }
        }
        return null;
    }

}
