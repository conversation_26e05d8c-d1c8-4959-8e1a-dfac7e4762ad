package com.yelink.dfs.constant.device;

/**
 * <AUTHOR>
 * @description 设备型号
 * @Date 2021/6/10
 */
public enum DeviceModelEnum {

    /**
     * 设备型号
     */
    LAMP_EXAMINERS("S7-300", "灯检机", "2ml-zuoke szc"),
//    WATER_STERILIZED_CABINET("CPUS7-315", "水浴式灭菌柜", "xx"),
//    VENTILATION_STERILIZED_CABINET("CPU:S7-1215", "通风式干燥灭菌柜", "xx"),
//    LABELING_MACHINE("S7-1200", "贴签", "xx"),
//    BLISTER_PACKAGING_MACHINE("WAGO", "泡罩包装", "xx"),
//    CARTON_MACHINE("WAGO", "装盒", "xx"),
//    FILM_STRAPPING_MACHINE("S7-200", "捆包", "xx"),
//    PACKING("S7-200", "装箱", "xx"),
    ;

    private String model;
    private String eui;
    private String type;

    DeviceModelEnum(String model, String type, String eui) {
        this.model = model;
        this.type = type;
        this.eui = eui;
    }

    public String getModel() {
        return model;
    }

    public String getType() {
        return type;
    }

    public String getEui() {
        return eui;
    }

}
