package com.yelink.dfs.constant.defect;


/**
 * @Description: 处理类型枚举
 * @Author: zheng<PERSON>
 * @Date: 2020/12/5
 */
public enum DealEnum {

    /**
     * 处理类型枚举
     */
    DEAL(0, "已处理"),
    UN_DEAL(1, "未处理");

    private Integer code;
    private String name;

    DealEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static String getNameByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (DealEnum stateEnum : DealEnum.values()) {
            if (stateEnum.code.equals(code)) {
                return stateEnum.name;
            }
        }
        return null;
    }

    public static Integer getCodeByName(String name) {
        for (DealEnum stateEnum : DealEnum.values()) {
            if (name.equals(stateEnum.name)) {
                return stateEnum.code;
            }
        }
        return null;
    }
}
