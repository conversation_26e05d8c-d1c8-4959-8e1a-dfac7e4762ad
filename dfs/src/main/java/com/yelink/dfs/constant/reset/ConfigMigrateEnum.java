package com.yelink.dfs.constant.reset;

import com.yelink.dfscommon.entity.CommonEnumInterface;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @Date 2022/4/1 9:50
 */
public enum ConfigMigrateEnum implements CommonEnumInterface {

    BUSINESS_CONFIG("businessConfig", "软件参数配置"),
    FORM_CONFIG("formConfig", "表单配置"),
    FORM_OVERALL_FIELD_CONFIG("formOverallFieldConfig", "全局扩展字段配置"),
    PUSH_DOWN_CONFIG("pushDownConfig", "单据下推配置"),
    RULE_CONFIG("ruleConfig", "编码规则"),
    COMPONENT_DATA("componentData", "演示数据"),
    ;

    private String code;
    private String name;

    ConfigMigrateEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static String getNameByCode(String code) {
        if (StringUtils.isBlank(code)) {
            return null;
        }
        for (ConfigMigrateEnum stateEnum : ConfigMigrateEnum.values()) {
            if (stateEnum.code.equals(code)) {
                return stateEnum.name;
            }
        }
        return null;
    }

    public static String getCodeByName(String name) {
        for (ConfigMigrateEnum stateEnum : ConfigMigrateEnum.values()) {
            if (name.equals(stateEnum.name)) {
                return stateEnum.code;
            }
        }
        return null;
    }
}
