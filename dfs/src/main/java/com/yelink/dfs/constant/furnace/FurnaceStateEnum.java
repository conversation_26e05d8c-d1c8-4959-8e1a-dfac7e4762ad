package com.yelink.dfs.constant.furnace;

/**
 * <AUTHOR>
 * @Date 2021/8/3 19:31
 */
public enum FurnaceStateEnum {
    /**
     * 1-未冶炼 2-冶炼中  3-已完成
     */
    STOP(1, "未冶炼"),
    RUNNING(2, "冶炼中"),
    FINISH(3, "已完成");

    private int code;
    private String name;

    FurnaceStateEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }

    public int getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static String getNameByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (FurnaceStateEnum stateEnum : FurnaceStateEnum.values()) {
            if (stateEnum.code == code) {
                return stateEnum.name;
            }
        }
        return null;
    }
}
