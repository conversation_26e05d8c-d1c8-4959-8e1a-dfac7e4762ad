package com.yelink.dfs.constant.valuation;

import com.yelink.dfscommon.entity.CommonEnumInterface;
import lombok.Getter;

/**
 * 计价方式
 * <AUTHOR>
 */
public enum ValuationConfigMethodEnum implements CommonEnumInterface {


    /**
     *
     */
    DURATION("duration", "计时"),
    COUNT("count", "计件"),
    ;
    @Getter
    private final String code;
    @Getter
    private final String name;

    ValuationConfigMethodEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }
    public static ValuationConfigMethodEnum fromCode(String code) {
        for(ValuationConfigMethodEnum e : ValuationConfigMethodEnum.values()) {
            if(e.code.equals(code)) {
                return e;
            }
        }
        return null;
    }

}
