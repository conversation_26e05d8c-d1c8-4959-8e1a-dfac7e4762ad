package com.yelink.dfs.constant.product;

import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @Description 数据类型 枚举
 * @Date 2022/4/19 14:40
 */
public enum DataTypeEnum {

    /**
     * 数据类型
     */
    NUMERICAL_TYPE("number","数值型"),
    CHARACTER_TYPE("char","字符型"),
    BOOLEAN_TYPE("boolean","布尔型");


    /**
     * 字段名
     */
    private String fieldEname;
    /**
     * 字段名称
     */
    private String fieldName;

    DataTypeEnum(String fieldEname, String fieldName) {
        this.fieldEname = fieldEname;
        this.fieldName = fieldName;
    }

    public String getFieldEname() {
        return fieldEname;
    }

    public String getFieldName() {
        return fieldName;
    }


    public static String getNameByCode(String fieldEname) {
        if (fieldEname == null) {
            return null;
        }
        for (DataTypeEnum stateEnum : DataTypeEnum.values()) {
            if (stateEnum.fieldEname.equals(fieldEname)) {
                return stateEnum.fieldName;
            }
        }
        return null;
    }

    public static String getCodeByName(String fieldName) {
        if (StringUtils.isBlank(fieldName)) {
            return null;
        }
        for (DataTypeEnum stateEnum : DataTypeEnum.values()) {
            if (fieldName.equals(stateEnum.fieldName)) {
                return stateEnum.fieldEname;
            }
        }
        return null;
    }

}
