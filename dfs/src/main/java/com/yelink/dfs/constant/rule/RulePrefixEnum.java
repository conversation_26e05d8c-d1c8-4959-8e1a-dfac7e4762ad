package com.yelink.dfs.constant.rule;

import cn.hutool.extra.spring.SpringUtil;
import com.yelink.dfs.service.rule.RulePrefixConfigService;
import lombok.Getter;

import java.util.List;
import java.util.Map;

/**
 * @Description: 编码规则的组成信息配置字段
 * @Author: sq
 * @Date: 2021/10/10
 */
@Getter
public enum RulePrefixEnum {
    /**
     * 编码规则的组成信息配置字段
     */
    ARTIFICIAL_TYPE("人工输入", "1"),
    CURRENTDATE_TYPE("当前日期", "2"),
    AUTO_INCREMENT_TYPE("自动生成序号", "4"),
    FIXED_TYPE("固定信息", "5"),
    WORK_ORDER_TYPE("生产工单号", "3"),
    MATERIAL_CODE_TYPE("物料编码", "6"),
    CUSTOMER_CODE("客户编码", "7"),
    LINE_MODEL_CODE("制造单元类型编号", "9"),
    LINE_CODE("制造单元编号", "10"),
    SUPPLIER_CODE("供应商编码", "12"),
    PURCHASE_NUMBER("采购订单号", "13"),
    PURCHASE_BATCH("采购批次号", "14"),
    PURCHASE_RECEIPT("采购收货号", "15"),
    ORDER_DATE("下单日期", "16"),
    REQUIRE_GOODS_DATE("交货日期", "17"),
    CHECK_TIMES("按校验次数自增序号", "18"),
    SUBCONTRACT_ORDER("委外订单号", "19"),
    PRODUCT_ORDER("生产订单号", "20"),
    PRODUCT_ORDER_ONLY_PUSH_DOWN("生产订单号(仅下推有效)", "21"),
    SALE_ORDER_ONLY_PUSH_DOWN("销售订单号(仅下推有效)", "22"),
    DEPARTMENT_CODE("部门编码", "23"),
    SALE_ORDER_TYPE_CODE("订单类型编码", "24"),
    WORK_ORDER_ONLY_SPLIT("原生产工单号(仅排产拆分有效)", "25"),
    PACKAGE_ORDER_TYPE("包装工单编号", "26"),
    PACKAGE_ORDER_LEVEL("包装工单层级", "27"),
    MATERIAL_TYPE_CODE("物料类型编码", "28"),
    GEAR_POSITION("档位", "29"),
    DEFECT_TYPE_CODE("不良类型编码", "30"),
    MAINTAIN_TYPE_CODE("维修类型编码", "31"),
    BOM_CODE("bom编码(不展示,仅做归一处理)", "32"),
    CRAFT_CODE("工艺编码(不展示,仅做归一处理)", "33"),

    ;

    /**
     * 名称
     */
    private String type;
    /**
     * 编码
     */
    private String code;

    RulePrefixEnum(String type, String code) {
        this.type = type;
        this.code = code;
    }

    public static RulePrefixEnum getEnumByCode(String code) {
        for (RulePrefixEnum ruleTypeEnum : RulePrefixEnum.values()) {
            if (code.equals(ruleTypeEnum.getCode())) {
                return ruleTypeEnum;
            }
        }
        return null;
    }

    /**
     * 根据编码规则类型查询所有对应的组成信息
     */
    public static List<com.yelink.dfs.constant.rule.RulePrefixDTO> getEnumByRuleType(String ruleType) {
        RulePrefixConfigService service = SpringUtil.getBean(RulePrefixConfigService.class);
        return service.getByRuleType(ruleType);
    }

    /**
     * 将枚举值转化成list集合
     */
    public static List<com.yelink.dfs.constant.rule.RulePrefixDTO> toList() {
        RulePrefixConfigService service = SpringUtil.getBean(RulePrefixConfigService.class);
        return service.listAll();
    }

    /**
     * 获取规则扩展信息
     */
    public List<Map<String, String>> getRuleExtendList() {
        RulePrefixConfigService service = SpringUtil.getBean(RulePrefixConfigService.class);
        return service.getRuleExtendList(this.code);
    }
}
