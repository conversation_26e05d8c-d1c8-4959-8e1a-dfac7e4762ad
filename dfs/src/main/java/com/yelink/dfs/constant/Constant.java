package com.yelink.dfs.constant;

import java.util.Arrays;
import java.util.List;

/**
 * @description: 常量类
 * @author: zengfu
 * @create: 2020-12-04 15:22
 **/
public class Constant {

    public static final String SEP = ",";
    public static final String BLANK = " ";
    public static final String BACKSLASH = "/";
    public static final String VERTICAL = "|";
    public static final String CN_FULL_POINT = "。";
    public static final String FULL_POINT = ".";
    public static final String EQUAL = "=";
    public static final String CROSSBAR = "-";
    public static final String PLUS = "+";
    public static final String UNDERLINE = "_";
    public static final String SPECIAL_SEP = "_::_";
    public static final String CN_DUN_HAO = "、";
    public static final String EMPTY = "";
    public static final String CN_COMMA = "，";
    public static final String SINGLE_QUOTE = "'";
    public static final String COLON = ":";
    public static final String CN_COLON = "：";
    public static final String STAR = "*";
    public static final String UNIT = "unit";
    public static final String WAREHOUSE_TYPE = "warehouseType";
    public static final String ORDER_TYPE = "order";
    public static final String WORK_ORDER_TYPE = "work_order";
    public static final String MATERIAL_TYPE = "materialType";
    public static final String DEVICE_SORT = "deviceSort";
    public static final String STOP_TYPE = "stopType";
    public static final Integer START = 0;
    public static final Integer END = 1;
    public static final Integer PAUSE = 2;
    public static final String DATE_TYPE_DAY = "day";
    public static final String DATE_TYPE_WEEK = "week";
    public static final String DATE_TYPE_MONTH = "month";
    public static final String DFS_RECORD_DEVICE_ = "dfs_record_device_";
    public static final String DFS_RECORD_GRID_ = "dfs_record_grid_";
    public static final String FIVE_MIN = "_5min";
    public static final String TWENTY_MIN = "_20min";
    public static final String ONE_HOURS = "_1h";
    public static final String TWENTY_FOUR_HOURS = "_24h";
    public static final String SEVEN_DAYS = "_7day";
    public static final String THIRTY_DAYS = "_30day";
    public static final String YES = "1";
    public static final String NO = "0";
    public static final String ALARM_CYCLE_SETTING = "alarmCycleSetting";
    public static final String XLSX = ".xlsx";
    public static final String XLSM = ".xlsm";
    public static final String XLS = ".xls";
    public static final String PDF = ".pdf";
    public static final String TXT = ".txt";
    public static final String SLASH = "/";
    public static final String ZIP = ".zip";
    public static final String RAR = ".rar";
    public static final String DETAIL_MESSAGE = "详细信息关键字段";
    public static final String LIMIT_ONE = "limit 1";
    public static final String TRUE = "是";
    public static final String FALSE = "否";
    public static final String ARTIFICIAL_JUDGE = "人工判断";
    public static final String AUTO_JUDGE = "自动判断";
    //判断erp是否第一次的数量
    public static final Integer ERP_FIRST_QUANTITY = 1000;


    /**
     * 产线报工完成类型 1-换线导致完成
     */
    public static final Integer REPORT_CHANGE_LINE_FINISH = 1;

    /**
     * 工段大屏导入的导入类型
     */
    public static final String OPEN = "开";
    public static final String CLOSE = "关";
    public static final String DETAIL = "详情";
    public static final String CHART = "图表";
    public static final String USER = "人员";
    public static final String CHATS = "多图表";

    /**
     * 设备对接
     */
    public static final Integer IOT_DEVICE_START = 1;
    public static final String MANUAL_COLLECTION_TARGET = "manual";
    public static final String AUTO_COLLECTION_TARGET = "auto";
    public static final String SECOND = "s";
    public static final String MINUTE = "m";
    public static final String MINUTES = "min";
    public static final String HOUR = "h";
    public static final String ZERO_VALUE = "0";
    public static final Integer KAFKA_COMMON_DEVICE_ALARM_OCCUR = 1;
    public static final String CEQ = "Ceq";


    /**
     * 炉次号常量
     */
    public static final String TAPPING_CAPACITY_EN = "tappingCapacity";
    public static final String TAPPING_CAPACITY_CN = "出钢量";
    public static final String FURNACE = "H";


    //扬子江返回数据规范
    public static final String CONFIG_YZJ_SCREEN_ENTITY = "configYzjScreenEntity";
    public static final String CONTENT = "content";
    public static final String OEE = "OEE";
    public static final String SCREEN_NAME = "screenName";
    public static final String DEVICE_STATE = "deviceState";
    public static final String DEVICE_RUNNING_DURATION = "deviceRunningDuration";
    public static final String DEVICE_PAUSE_DURATION = "devicePauseDuration";
    public static final String DEVICE_STOP_DURATION = "deviceStopDuration";
    public static final String DEVICE_MAINTAIN_DURATION = "deviceMaintainDuration";
    public static final String LCM_DEVICE_STATE = "state";

    public static final String STATE = "deviceState";

    // 设备对接的key值
    // lit列表
    public static final String LIST = "list";

    /**
     * 奥德485采集器设备初始状态（Kafka传过来的数据）
     * 运行状态（0自动状态1自动运行2停止中3手动状态
     */

    public static final String AUTOMATIC_STATUS = "0";
    public static final String AUTOMATIC_OPERATION = "1";
    public static final String STOPPING = "2";
    public static final String MANUAL_STATUS = "3";


    /**
     * 模块编码
     */
    public static final String SALE_ORDER = "saleOrder";
    public static final String PRODUCT_ORDER = "productOrder";
    public static final String DELIVERY_APPLICATION = "deliveryApplication";
    public static final String TAKE_OUT_APPLICATION = "takeOutApplication";
    public static final String WORK_ORDER = "workOrder";

    /**
     * 数据库表字段
     */
    public static final String WORK_ORDER_NUMBER_FIELD = "work_order_number";
    public static final String WORK_ORDER_FIELD = "work_order";
    public static final String WORK_ORDER_NUM_FIELD = "work_order_num";
    public static final String WORK_ORDER_ID_FIELD = "work_order_id";

    public static final String DEVICE = "device";

    //产线父id
    public static final Integer PID_LINE = 2;

    //不良品条形码type编号
    public static final String BAR_CODE_NUMBER = "100";

    //授权认证方式
    public static final String KEY_CLOAK = "keycloak";
    public static final String SPRING_SECURITY = "springSecurity";

    // 用户启用禁用状态
    public static final String ENABLE = "enable";
    public static final String DISABLE = "disable";

    /**
     * 用户名账号密码错误
     */
    public static final String ERROR_USERNAME_PASSWORD = "Error requesting access token.";
    /**
     * 用户被禁用
     */
    public static final String USER_DISABLE = "Access token denied.";

    //内置系统管理员角色用户
    public static final String YELINK_ONCALL = "yelinkoncall";
    public static final String ONCALL_CODE = "oncall";

    public static final String TEMP_USER = "tempUser";

    public static final String ONE_CLICK_RECOVERY = "一键还原";

    public static final Integer THREE = 3;
    public static final Integer TWO = 2;
    public static final Integer TEN = 10;
    public static final Integer ZERO = 0;
    public static final Integer ONE = 1;

    public static final String TARGET_ALARM = "targetAlarm";
    public static final String DEVICE_ALARM = "deviceAlarm";
    public static final String MANUAL_ALARM = "manualAlarm";
    public static final String DEVICE_ORDER_ALARM = "deviceOrderAlarm";

    public static final String TEMP = "TEMP:";
    public static final String REAL = "REAL:";
    public static final int FIFTY_THOUSAND = 50000;
    /**
     * 消息通知聚合码前缀
     */
    public static final String INFO_NOTICE = "infoNotice";
    //cron表达式的替换格式
    public static final String CRON = "0 min hour ? * *";
    //默认一天中结束时间
    public static final String DEFAULT_CRON = "0 55 23 ? * *";

    //添加指标
    public static final String ADD_MANULLY_TARGET = "1";
    public static final String ADD_AUTOMATICALLY_TARGET = "2";
    public static final String ADD_COMBINE_TARGET = "4";

    //指标类型
    public static final String TARGET_NAME = "targetName";

    //产能
    public static final Integer ALL_PRODUCT_ID = -1;
    public static final String ALL_PRODUCT_CODE = "--";

    public static final String LINE_CAPACITY = "lineCapacity";
    public static final String FACTORY_CAPACITY = "factoryCapacity";
    public static final String DEVICE_CAPACITY = "deviceCapacity";
    public static final String TEAM_CAPACITY = "teamCapacity";
    public static final String DEFAULT_CAPACITY_VALUE_LOGIC = "defaultCapacityValueLogic";
    public static final String DEFAULT_CAPACITY_VALUE_LOGIC_ONE = "logicOne";
    public static final String DEFAULT_CAPACITY_VALUE_LOGIC_TWO = "logicTwo";
    /**
     * dict: 系统运行状态
     */
    public static final String SYSTEM_STATUS = "systemStatus";


    //单据状态信息
    public static final String STOCK_ENOUGH = "库存充足,待发货";
    public static final String CREATE = "创建";
    public static final String GRANT = "发放";
    public static final String COMPLETE = "完成";
    public static final String CLOSED = "关闭";
    public static final String CANCEL = "取消";
    public static final String CHECK = "审核";
    public static final String APPROVE = "批准";
    public static final String PUT_IN = "投入";
    public static final String HANG_UP = "挂起";

    public static final String MONTHLY_STANDARD_CAPACITY = "monthlyStandardCapacity";

    //报工类型
    public static final String INVESTMENT = "investment";
    public static final String REPORT = "report";
    public static final String FINISHED = "finished";

    public static final String PRODUCTION_LINE_REPORT = "产线报工";
    public static final String WORKORDER_STATUS_CHANGE = "工单状态变更";
    public static final String WORKORDER_LINE_CHANGE = "工单换线";

    public static final String DELIMITER = "--";

    // ECM调用返回数据
    public static final String APPS = "apps";
    public static final String ITEMS = "items";
    public static final String ADMIN = "admin";
    public static final String NULL = "null";
    public static final String SERVICE_ID = "serviceId";

    //产线默认设施名字后缀
    public static final String LINE = "产线";

    /**
     * 业务类型，用于内存计算
     */
    public static final String INPUT_RECORD = "inputRecord";
    public static final String REPORT_LINE = "reportLine";
    public static final String FAC_INPUT_RECORD = "facInputRecord";
    public static final String UNQUALIFIED_LINE = "unqualifiedLine";
    public static final String FIRST_UNQUALIFIED = "firstUnqualified";
    public static final String MCA_SERVICE = "McaService";
    public static final String MEMORY_CAL_INTERVAL = "memoryCalInterval";


    /**
     * 业务类型，用于组合指标绑定
     */
    public static final String COVER_DATA = "cover";
    public static final String ADD_DATA = "add";
    public static final String TIME_KEY = "timeKey";
    public static final String DATA_KEY = "dataKey";


    /**
     * 不良定义
     */
    // 内置合格项
    public static final String QUALIFIED_CODE = "qualified";
    public static final String QUALIFIED_TYPE_NAME = "合格";
    public static final Integer QUALIFIED_ID = 0;
    public static final Integer UN_QUALIFIED_ID = -1;

    public static final String Y = "Y";
    public static final String N = "N";
    public static final String TRUES = "true";
    public static final String FALSES = "false";
    public static final String MIDDLE_BRACKET = "[]";


    public static final String CRAFT_PROCEDURE_FILE_EXCEL_NAME = "工序附件列表";
    public static final String USER_SIGNATURE_FILE_EXCEL_NAME = "用户签名列表";

    //工厂标识
    /**
     * 奥德
     */
    public static final String ZHIMEI = "zhimei";
    public static final String XIDIAN = "xidian";

    /**
     * 计数器处理类bean名
     */
    public static final String NEW_COUNTER = "newCounter";

    public static final String ADD = "新增";
    public static final String DELETE = "删除";

    /**
     * 电子料架处理类Bean属性名
     */
    public static final String ELECTRONIC_RACK = "commandElectronicRack";
    /**
     * 电子料架指示灯信号灭：0红：1橙：2黄：3绿：4青：5蓝：6紫：7
     */
    public static List<Integer> ELECTRONIC_RACK_COLOR = Arrays.asList(0, 1, 2, 3, 4, 5, 6, 7);

    // 精致
    public static final String DFS_SERVICE_NICK_NAME = "消息通知";

    /**
     * 指标参数
     */
    public static final String PARAM_STOP_TIME = "stopTime";
    public static final String PARAM_RUN_TIME = "runTime";
    public static final String PARAM_PAUSE_TIME = "pauseTime";
    public static final String PARAM_MAINTAIN_TIME = "maintainTime";
    public static final String PARAM_STATE = "state";
    public static final String PARAM_MAINTENANCE_STATE = "maintenanceState";
    public static final String PARAM_COUNT = "count";
    public static final String PARAM_RUN_COUNT = "runCount";
    public static final String PARAM_PAUSE_COUNT = "pauseCount";
    public static final String PARAM_STOP_COUNT = "stopCount";
    public static final String PARAM_FAULT_COUNT = "faultCount";
    public static final String PARAM_MAINTAIN_COUNT = "maintainCount";


    /**
     * 名称字段校验正则
     * 只能包含汉字、英文字母、空格、英文点号、中划线、下划线、左右括号、井号、星号和斜杠、长度最大为120，不分顺序；
     */
    public static final String NAME_REX = "^[\u4E00-\u9FA5A-Za-z0-9.\\s\\-_#*/()（）\\\\]{1,120}$";

    /**
     * 生产工单操作行为
     */
    public static final String WORK_ORDER_INVESTMENT = "投产";
    public static final String WORK_ORDER_REPORT = "报工";
    public static final String WORK_ORDER_FINISH = "完工";


    /**
     * mongodb表名
     */
    public static final String MATERIAL_TYPE_TARGET = "dfs_mongodb_material_type_target_";
    public static final String TARGET_TIME = "_time";
    public static final String TARGET_BY = "_by";
    public static final String TARGET_RESULT = "_result";
    public static final String TARGET_URL = "_url";


    /**
     * 上次排程导入时间
     */
    public static final String SCHEDULE_TIME = "scheduleTime";

    /**
     * 指标数据库
     */
    public static final String DFS_METRICS = "dfs_metrics";

    /**
     * 指标失效配置
     */
    public static final String TARGET_EXPIRE_TIME = "targetExpireTime";

    public static final String HEADER_USER_AGENT = "User-Agent";
    public static final String TRIGGER_TIME = "@TRIGGER_TIME";

    public static final String COPY_SPLIT = "_copy_";
    public static final String BASE_TARGET_GROUP = "baseTargetGroupObject";
    public static final String CUSTOM_TARGET = "customTarget";

}
