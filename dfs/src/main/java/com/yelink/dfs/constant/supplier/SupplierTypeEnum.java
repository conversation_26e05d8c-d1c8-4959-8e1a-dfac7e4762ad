package com.yelink.dfs.constant.supplier;

/**
 * @Description: 采购订单状态枚举
 * @Author: zhuangwq
 * @Date: 2021/04/10
 */
public enum SupplierTypeEnum {

    /**
     * 状态编码及描述
     * 0-创建 1-发放 2-完成 3-关闭 4-取消
     */
    QUALIFIED(1001, "合格供应商"),
    IMPORT(1002, "导入供应商"),
    TEMPORARY(1003, "临时供应商"),
    UNQUALIFIED(1004, "非合格供应商"),
    OUTSOURCING_SUPPLIER(1005, "委外供应商");

    private Integer code;
    private String name;

    SupplierTypeEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static String getNameByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (SupplierTypeEnum supplierTypeEnum : SupplierTypeEnum.values()) {
            if (supplierTypeEnum.code.equals(code)) {
                return supplierTypeEnum.name;
            }
        }
        return null;
    }

    public static Integer getCodeByName(String name) {
        if (name == null) {
            return null;
        }
        for (SupplierTypeEnum supplierTypeEnum : SupplierTypeEnum.values()) {
            if (supplierTypeEnum.name.equals(name)) {
                return supplierTypeEnum.code;
            }
        }
        return null;
    }
}
