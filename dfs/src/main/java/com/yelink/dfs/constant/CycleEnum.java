package com.yelink.dfs.constant;


/**
 * <AUTHOR>
 * @Date 2021/5/27 17:10
 */
public enum CycleEnum {
    /**
     *
     */
    ONE_MIN(1, "原始值"),
    FIVE_MIN(2, "5分钟"),
    TWENTY_MIN(3, "20分钟"),
    ONE_HOURS(4, "1小时"),
    ONE_DAY(5, "1天"),
    ;

    private int code;
    private String name;

    CycleEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }

    public int getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static String getNameByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (CycleEnum cycleEnum : CycleEnum.values()) {
            if (cycleEnum.code == code) {
                return cycleEnum.name;
            }
        }
        return null;
    }
}
