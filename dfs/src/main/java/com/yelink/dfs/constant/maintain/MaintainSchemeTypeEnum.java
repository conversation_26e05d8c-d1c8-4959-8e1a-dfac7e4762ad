package com.yelink.dfs.constant.maintain;


/**
 * @Description: 维修方案的维修类型枚举
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2020/12/5
 */
public enum MaintainSchemeTypeEnum {

    /**
     * 状态编码及描述
     * 0-创建 1-生效
     */
    STATION_SEMIFINISHED("stationSemifinished", "工位维修-半成品"),
    STATION_FINISHED("stationFinished", "工位维修-成品");

    private String code;
    private String name;

    MaintainSchemeTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static String getNameByCode(String code) {
        if (code == null) {
            return null;
        }
        for (MaintainSchemeTypeEnum stateEnum : MaintainSchemeTypeEnum.values()) {
            if (stateEnum.code.equals(code)) {
                return stateEnum.name;
            }
        }
        return null;
    }

    public static String getCodeByName(String name) {
        for (MaintainSchemeTypeEnum stateEnum : MaintainSchemeTypeEnum.values()) {
            if (name.equals(stateEnum.name)) {
                return stateEnum.code;
            }
        }
        return null;
    }
}
