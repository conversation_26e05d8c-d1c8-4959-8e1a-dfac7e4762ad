package com.yelink.dfs.constant.sys;

import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 表字段类型定义
 */
public enum TableFieldTypeEnum {
    /**
     * 表字段类型定义
     */
    INT("int", 11, 0),
    DOUBLE("double", 11, 4),
    VARCHAR("varchar", 255, 0),
    DATETIME("datetime", 0, 0),
    DATE("date", 0, 0),
    ;

    /**
     * 字段类型
     */
    private String type;
    /**
     * 默认长度
     */
    private Integer defaultLength;
    /**
     * 默认小数点
     */
    private Integer defaultPoint;

    TableFieldTypeEnum(String type, Integer defaultLength, Integer defaultPoint) {
        this.type = type;
        this.defaultLength = defaultLength;
        this.defaultPoint = defaultPoint;
    }

    public static TableFieldTypeEnum getEnumByType(String type) {
        if (StringUtils.isEmpty(type)) {
            return null;
        }
        for (TableFieldTypeEnum e : TableFieldTypeEnum.values()) {
            if (e.getType().equals(type)) {
                return e;
            }
        }
        return null;
    }

    public String getType() {
        return type;
    }

    public Integer getDefaultLength() {
        return defaultLength;
    }

    public Integer getDefaultPoint() {
        return defaultPoint;
    }

    static final Set<String> NUMBER_SET = Stream.of(INT, DOUBLE).map(TableFieldTypeEnum::getType).collect(Collectors.toSet());
    static final Set<String> DATE_SET = Stream.of(DATETIME, DATE).map(TableFieldTypeEnum::getType).collect(Collectors.toSet());
    public static boolean isNumberType(String type) {
        return NUMBER_SET.contains(type);
    }
    public static boolean isDateType(String type) {
        return DATE_SET.contains(type);
    }

}
