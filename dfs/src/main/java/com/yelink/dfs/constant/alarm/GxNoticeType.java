package com.yelink.dfs.constant.alarm;

/**
 * @description: 个推消息类型
 * @author: z<PERSON><PERSON>
 * @create: 2020-07-01 11:44
 **/
public enum GxNoticeType {
    /**
     * 消息类型
     */
    SENSOR_ALARM_NOTICE("200", "告警消息"),

    SENSOR_SYSTEM_NOTICE("100", "系统消息");


    private String code;
    private String title;


    GxNoticeType(String code, String title) {
        this.code = code;
        this.title = title;
    }

    public String getCode() {
        return code;
    }

    public String getTitle() {
        return title;
    }
}
