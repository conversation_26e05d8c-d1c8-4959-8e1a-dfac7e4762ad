package com.yelink.dfs.constant.node;

import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * 上报子节点类型
 * @Date 2022/4/24 10:02
 */
public enum ReportNodeSubTypeEnum {

    /**
     * 上报节点类型类型
     */
    BILL("1", "1");

    private String type;
    private String name;


    ReportNodeSubTypeEnum(String type, String name) {
        this.type = type;
        this.name = name;
    }

    public String getName() {
        return name;
    }

    public String getType() {
        return type;
    }

    public static String getNameByCode(String type) {
        if (StringUtils.isBlank(type)) {
            return null;
        }
        for (ReportNodeSubTypeEnum reportNodeTypeEnum : ReportNodeSubTypeEnum.values()) {
            if (type.equals(reportNodeTypeEnum.type)) {
                return reportNodeTypeEnum.getName();
            }
        }
        return null;
    }
}
