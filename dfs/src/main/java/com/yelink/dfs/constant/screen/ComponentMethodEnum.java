package com.yelink.dfs.constant.screen;

import org.apache.commons.lang.StringUtils;

/**
 * <AUTHOR>
 * @description 大屏组件对应接口方法
 * @Date 2021/6/1
 */
public enum ComponentMethodEnum {

    /**
     * 状态总览
     */
    OVERVIEW_STATE("OverviewState", "productionServiceImpl", "workOrderInformation"),
    /**
     * 生产计划与进度
     */
    MAIN_CONTENT_ALL("MainContentAll","productionServiceImpl","listWorkOrder"),
    /**
     * 告警详情
     */
    RIGHT_ALARM_LIST("RightAlarmList","productionServiceImpl","getAlarm"),
    /**
     * 计划达成率
     */
    RIGHT_ALARM_CHART("RightAlarmChart","productionServiceImpl","getPlanRate"),
    /**
     * 产量统计
     */
    RIGHT_CHART("RightChart","workOrderDayCountServiceImpl","getRecordOutput"),

    /**
     * OEE&良率
     */
    PRODUCTION_OEE("ProductionOee","workOrderDayCountServiceImpl","getYieldAndOee"),

    /**
     * 产量统计
     */
    LINE_CHARTS("LineCharts","workOrderDayCountServiceImpl","getUnqualifiedAndFinishCountOnDay"),

    /**
     * 实时节拍信息
     */
    BEAT("Beat","workOrderDayCountServiceImpl","getGridBeat");
    /**
     * 组件名
     */
    private String componentName;

    /**
     * 类名
     */
    private String className;

    /**
     * 方法
     */
    private String methodName;

    ComponentMethodEnum(String componentName, String className, String methodName) {
        this.componentName = componentName;
        this.className = className;
        this.methodName = methodName;
    }

    public String getComponentName() {
        return componentName;
    }

    public String getClassName() {
        return className;
    }

    public String getMethodName() {
        return methodName;
    }

    public static ComponentMethodEnum getEnumByComponentName(String componentName) {
        if (StringUtils.isBlank(componentName)) {
            return null;
        }
        for (ComponentMethodEnum componentMethodEnum : ComponentMethodEnum.values()) {
            if (componentMethodEnum.componentName.equals(componentName)) {
                return componentMethodEnum;
            }
        }
        return null;
    }

}
