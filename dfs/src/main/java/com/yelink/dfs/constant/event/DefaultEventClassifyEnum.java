package com.yelink.dfs.constant.event;

import com.yelink.dfs.constant.alarm.AlarmDataEnum;
import lombok.Getter;

/**
 * 默认的告警分类
 * <AUTHOR>
 */
public enum DefaultEventClassifyEnum {
    /**
     * 未知的类型
     */
    UNKNOWN("UNKNOWN", "未知类型", AlarmDataEnum.SYSTEM_GENERATION.getCode())
    ;
    DefaultEventClassifyEnum(String classifyCode, String classifyName, String dataSources) {
        this.classifyCode = classifyCode;
        this.classifyName = classifyName;
        this.dataSources = dataSources;
    }
    @Getter
    private final String classifyCode;
    @Getter
    private final String classifyName;
    @Getter
    private final String dataSources;
}
