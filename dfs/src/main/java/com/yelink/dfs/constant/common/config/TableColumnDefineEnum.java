package com.yelink.dfs.constant.common.config;

import com.yelink.dfscommon.entity.CommonEnumInterface;
import lombok.Getter;

/**
 * 表字段定义枚举
 * <AUTHOR>
 */
public enum TableColumnDefineEnum implements CommonEnumInterface {
    /**
     *
     */
    DIMENSION("维度"),
    TIME_DIMENSION("时间维度"),
    TARGET("指标"),
    ATTACHMENT("附属信息"),
    ;

    @Getter
    private final String name;

    TableColumnDefineEnum(String name) {
        this.name = name;
    }

    @Override
    public String getCode() {
        return name();
    }
}
