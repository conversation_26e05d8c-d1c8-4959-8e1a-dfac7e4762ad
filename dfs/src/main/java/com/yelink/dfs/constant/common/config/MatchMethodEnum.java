package com.yelink.dfs.constant.common.config;

import lombok.Getter;

/**
 * 匹配方式枚举
 * <AUTHOR>
 */
public enum MatchMethodEnum {
    /**
     *
     */
    LIKE("包含"),
    LIKE_RIGHT("开始以"),
    ;
    @Getter
    private final String name;

    MatchMethodEnum(String name) {
        this.name = name;
    }

    public static MatchMethodEnum getByCode(String code) {
        for (MatchMethodEnum e : MatchMethodEnum.values()) {
            if(e.name().equals(code)) {
                return e;
            }
        }
        return null;
    }
}
