package com.yelink.dfs.constant.node;

import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * 指定类型
 * @Date 2022/4/24 10:02
 */
public enum AppointTypeEnum {

    /**
     * 上报节点类型类型
     */
    SALE_ORDER_MATERIAL("sale_order_material", "销售订单物料"),
    MATERIAL("material", "物料编码"),
    MATERIAL_TYPE("materialType", "物料类型"),
    ;

    private String type;
    private String name;


    AppointTypeEnum(String type, String name) {
        this.type = type;
        this.name = name;
    }

    public String getName() {
        return name;
    }

    public String getType() {
        return type;
    }

    public static String getNameByCode(String type) {
        if (StringUtils.isBlank(type)) {
            return null;
        }
        for (AppointTypeEnum appointTypeEnum : AppointTypeEnum.values()) {
            if (type.equals(appointTypeEnum.type)) {
                return appointTypeEnum.getName();
            }
        }
        return null;
    }
}
