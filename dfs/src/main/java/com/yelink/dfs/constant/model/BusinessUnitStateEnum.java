package com.yelink.dfs.constant.model;


import com.yelink.dfscommon.entity.CommonEnumInterface;
import lombok.Getter;

/**
 * 业务主体 状态
 * <AUTHOR>
 */
public enum BusinessUnitStateEnum implements CommonEnumInterface {

    /**
     * 状态 描述
     */
    CREATED("创建", 0),
    RELEASED("生效", 1),
    ABANDON("废弃", 2);

    @Getter
    private final String name;
    public final int sort;

    BusinessUnitStateEnum(String name, int sort) {
        this.name = name;
        this.sort = sort;
    }

    @Override
    public String getCode() {
        return this.name();
    }

}
