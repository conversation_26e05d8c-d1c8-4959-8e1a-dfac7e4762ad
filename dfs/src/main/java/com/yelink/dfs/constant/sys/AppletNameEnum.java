package com.yelink.dfs.constant.sys;

import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @Description 小程序名称code 枚举
 * @Date 2022/4/19 14:40
 */
public enum AppletNameEnum {

    /**
     * code/单据名称
     */
    ONE("yelink.station.qualitity.inspection","工位质检(工单)"),
    TWO("yelink.qualitity.inspection","工位质检(单品)"),
    THREE("yelink.processQualityInspection.workOrder.com","工序质检（工单）"),
    FOUR("yelink.processQualityInspection.singleItem.com","工序质检（单品）"),
    FIVE("yelink.kangjuren.qualityInspection.machine","质检工位机"),

    ;


    /**
     * code
     */
    private String code;
    /**
     * name
     */
    private String name;

    AppletNameEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }


    public static String getNameByCode(String code) {
        if (code == null) {
            return null;
        }
        for (AppletNameEnum stateEnum : AppletNameEnum.values()) {
            if (stateEnum.code.equals(code)) {
                return stateEnum.name;
            }
        }
        return null;
    }

    public static String getCodeByName(String name) {
        if (StringUtils.isBlank(name)) {
            return null;
        }
        for (AppletNameEnum stateEnum : AppletNameEnum.values()) {
            if (name.equals(stateEnum.name)) {
                return stateEnum.code;
            }
        }
        return null;
    }

}
