package com.yelink.dfs.constant.product;

import com.baomidou.mybatisplus.annotation.EnumValue;

/**
 * @Description: BOM子项类型枚举
 * @Author: shenzm
 * @Date: 2022/9/29
 */
public enum BomItemTypeEnum {

    /**
     * 1001-普通件 1002-替代件  1003-返还件  1004-联产品  1005-副产品
     */
    ORDINARY_PARTS(1001, "普通件"),
    REPLACE_ITEM(1002, "替代件"),
    RETURN_ITEM(1003, "返还件"),
    JOINT_PRODUCT(1004, "联产品"),
    BY_PRODUCT(1005, "副产品"),

    ;

    @EnumValue
    private int code;
    private String name;

    BomItemTypeEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }

    public int getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static String getNameByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (BomItemTypeEnum orderTypeEnum : BomItemTypeEnum.values()) {
            if (orderTypeEnum.getCode() == code) {
                return orderTypeEnum.getName();
            }
        }
        return null;
    }

    public static Integer getCodeByName(String name) {
        if (name == null) {
            return null;
        }
        for (BomItemTypeEnum orderTypeEnum : BomItemTypeEnum.values()) {
            if (orderTypeEnum.getName().equals(name)) {
                return orderTypeEnum.getCode();
            }
        }
        return null;
    }

}
