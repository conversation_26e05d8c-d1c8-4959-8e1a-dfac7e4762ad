package com.yelink.dfs.constant.pack;

/**
 * <AUTHOR>
 * @Date 2023/3/22 16:31
 */
public enum PackageBoxStateEnum {
    /**
     * 0:未完成包装,1:已完成包装, 2:已拆箱,3:合箱中,4:合箱完成
     */
    NOT_PACKAGE(0, "未完成包装"),
    PACKAGE_COMPLETED(1, "包装完成"),
    UNPACKING(2, "已拆箱"),
    CLOSING(3, "合箱中"),
    CLOSING_COMPLETED(4, "合箱完成"),
    ;

    private final Integer code;
    private final String name;


    PackageBoxStateEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static String getNameByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (PackageBoxStateEnum stateEnum : PackageBoxStateEnum.values()) {
            if (stateEnum.code.equals(code)) {
                return stateEnum.name;
            }
        }
        return null;
    }

    public static Integer getCodeByName(String name) {
        if(name == null){
            return null;
        }
        for (PackageBoxStateEnum stateEnum : PackageBoxStateEnum.values()) {
            if (name.equals(stateEnum.name)) {
                return stateEnum.code;
            }
        }
        return null;
    }
}
