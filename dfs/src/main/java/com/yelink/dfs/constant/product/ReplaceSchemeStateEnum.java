package com.yelink.dfs.constant.product;

import org.apache.commons.lang3.StringUtils;

/**
 * @Description: 物料替代方案状态枚举
 * @Author: zhuangwq
 * @Date: 2022/12/12
 */
public enum ReplaceSchemeStateEnum {

    /**
     * 状态编码及描述
     * 1-创建 2-生效 3-完成 4-关闭 5-取消
     */
    CREATED(1, "创建"),
    RELEASED(2, "生效"),
    FINISHED(3, "完成"),
    CLOSED(4, "关闭"),
    CANCELED(5, "取消");

    private int code;
    private String name;

    ReplaceSchemeStateEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }

    public int getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static String getNameByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (ReplaceSchemeStateEnum stateEnum : ReplaceSchemeStateEnum.values()) {
            if (stateEnum.code == code) {
                return stateEnum.name;
            }
        }
        return null;
    }

    public static Integer getCodeByName(String name) {
        if (StringUtils.isBlank(name)) {
            return null;
        }
        for (ReplaceSchemeStateEnum stateEnum : ReplaceSchemeStateEnum.values()) {
            if (stateEnum.name .equals(name) ) {
                return stateEnum.code;
            }
        }
        return null;
    }

}
