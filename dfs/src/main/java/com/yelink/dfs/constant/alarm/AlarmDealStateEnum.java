package com.yelink.dfs.constant.alarm;

import com.yelink.dfscommon.entity.CommonEnumInterface;

/**
 * @description: 告警处理状态
 * @author: shuang
 * @time: 2020/6/22
 */
public enum AlarmDealStateEnum implements CommonEnumInterface {


    /**
     * 告警处理状态
     */
    NOT_DEAL_ALARM(0, "未处理"),
    DEALING_ALARM(1, "处理中"),
    DEALED_ALARM(2, "已处理");

    private Integer stateCode;
    private String stateName;

    AlarmDealStateEnum(int stateCode, String stateName) {
        this.stateCode = stateCode;
        this.stateName = stateName;
    }

    public Integer getStateCode() {
        return stateCode;
    }

    public String getStateName() {
        return stateName;
    }

    public static String getStateNameByStateCode(Integer stateCode) {
        for (AlarmDealStateEnum alarmDealStateEnum : AlarmDealStateEnum.values()) {
            if (alarmDealStateEnum.stateCode.equals(stateCode)) {
                return alarmDealStateEnum.getStateName();
            }
        }
        return null;
    }


    @Override
    public Object getCode() {
        return stateCode;
    }

    @Override
    public Object getName() {
        return stateName;
    }
}
