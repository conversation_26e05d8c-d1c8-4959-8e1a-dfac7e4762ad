package com.yelink.dfs.constant.product;

import com.yelink.dfs.constant.Constant;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description 工艺工序配置枚举
 * @Date 2023/2/1 14:40
 */
public enum CraftProcedureConfEnum {

    /**
     * 工艺工序配置枚举
     */
    CRAFT_PROCEDURE_MATERIAL_USED("craftProcedureMaterialUsed", "工序用料"),
    CRAFT_PROCEDURE_MATERIAL("craftProcedureMaterial", "工序物料"),
    CRAFT_PROCEDURE_DEVICE("craftProcedureDevice", "工序设备"),
    CRAFT_PROCEDURE_PARAMETER("craftProcedureParameter", "工艺参数"),
    CRAFT_PROCEDURE_WORK_HOUR("craftProcedureWorkHour", "工序工时"),
    CRAFT_PROCEDURE_FILE("craftProcedureFile", "工序附件"),
    CRAFT_PROCEDURE_INSPECT("craftProcedureInspect", "工序检验项"),
    CRAFT_PROCEDURE_INSPECT_SCHEME("craftProcedureInspectScheme", "工序检验方案"),
    CRAFT_PROCEDURE_CONTROLLER("craftProcedureController", "工序控制"),
    CRAFT_PROCEDURE_ASSEMBLE("craftProcedureAssemble", "工序工装"),
    CRAFT_PROCEDURE_POST("craftProcedurePost", "工序人力"),
    CRAFT_PROCEDURE_DEFECT("craftProcedureDefect", "工序质检"),
    CRAFT_PROCEDURE_MAINTAIN("craftProcedureMaintain", "工序维修"),

    ;

    /**
     * 字段名
     */
    private String code;

    /**
     * 字段名称
     */
    private String name;

    CraftProcedureConfEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }


    public static String getNameByCode(String code) {
        if (StringUtils.isBlank(code)) {
            return null;
        }
        Map<String, String> map = Arrays.stream(CraftProcedureConfEnum.values())
                .collect(Collectors.toMap(CraftProcedureConfEnum::getCode, CraftProcedureConfEnum::getName));
        String[] inspectTypeList = code.split(Constant.SEP);
        StringBuilder sb = new StringBuilder();
        for (String inspectTypeCode : inspectTypeList) {
            String inspectTypeName = map.get(inspectTypeCode);
            sb.append(inspectTypeName).append(Constant.SEP);
        }
        sb.deleteCharAt(sb.length() - 1);
        return sb.toString();
    }


}
