package com.yelink.dfs.constant.product;

import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @Date 2022/5/11 16:43
 */
public enum ProcedureFlowTypeEnum {
    /**
     * 工序流转条件类型
     */
    NUM("num", "数量", ""),
    TIME("time", "时长", "h"),
    SHIFT("shift", "班次", "个"),
    ;

    private String code;
    private String name;
    private String unit;

    ProcedureFlowTypeEnum(String code, String name, String unit) {
        this.code = code;
        this.name = name;
        this.unit = unit;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public String getUnit() {
        return unit;
    }

    public static String getNameByCode(String code) {
        if (code == null) {
            return null;
        }
        for (ProcedureFlowTypeEnum itemEnum : ProcedureFlowTypeEnum.values()) {
            if (itemEnum.code.equals(code)) {
                return itemEnum.name;
            }
        }
        return null;
    }

    public static String getCodeByName(String name) {
        if (StringUtils.isBlank(name)) {
            return null;
        }
        for (ProcedureFlowTypeEnum itemEnum : ProcedureFlowTypeEnum.values()) {
            if (itemEnum.name.equals(name)) {
                return itemEnum.code;
            }
        }
        return null;
    }
}
