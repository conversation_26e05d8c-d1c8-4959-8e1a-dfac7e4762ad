package com.yelink.dfs.constant.product;


import com.baomidou.mybatisplus.annotation.EnumValue;
import org.apache.commons.lang3.StringUtils;

/**
 * @Description: 物料分类枚举
 * @Author: zhuangwq
 * @Date: 2021/04/08
 */
public enum MaterialSortEnum {

    /**
     * 采购品-purchase  生产品-product 委外品-outsourcing
     */
    PURCHASE("purchase", "采购品"),
    PRODUCT("product", "生产品"),
    OUTSOURCING("outsourcing", "委外品"),
    ;

    @EnumValue
    private String code;
    private String name;

    MaterialSortEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static String getNameByCode(String code) {
        if (code == null) {
            return null;
        }
        for (MaterialSortEnum stateEnum : MaterialSortEnum.values()) {
            if (stateEnum.code.equals(code)) {
                return stateEnum.name;
            }
        }
        return null;
    }

    public static String getCodeByName(String name) {
        if (StringUtils.isBlank(name)) {
            return null;
        }
        for (MaterialSortEnum stateEnum : MaterialSortEnum.values()) {
            if (stateEnum.name.equals(name)) {
                return stateEnum.code;
            }
        }
        return null;
    }

}
