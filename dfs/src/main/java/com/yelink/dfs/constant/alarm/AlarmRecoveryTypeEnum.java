package com.yelink.dfs.constant.alarm;

import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @description 告警恢复类型枚举
 * @Date 2021/11/2 10:50
 */
public enum AlarmRecoveryTypeEnum {

    /**
     * 告警恢复类型
     */
    MANUAL_RECOVERY("manualRecovery", "手动恢复"),
    AUTOMATIC_RECOVERY("automaticRecovery", "自动恢复"),

    // 当告警持续一段时间没有上报(iot)，可认为告警已经恢复
    SMART_RECOVERY("smartRecovery", "智能恢复");

    private String code;
    private String name;

    AlarmRecoveryTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static String getNameByCode(String code) {
        if (StringUtils.isBlank(code)) {
            return null;
        }
        for (AlarmRecoveryTypeEnum recoveryTypeEnum : AlarmRecoveryTypeEnum.values()) {
            if (recoveryTypeEnum.getCode().equals(code)) {
                return recoveryTypeEnum.getName();
            }
        }
        return null;
    }

    public static String getCodeByName(String name) {
        for (AlarmRecoveryTypeEnum recoveryTypeEnum : AlarmRecoveryTypeEnum.values()) {
            if (name.equals(recoveryTypeEnum.name)) {
                return recoveryTypeEnum.code;
            }
        }
        return null;
    }
}
