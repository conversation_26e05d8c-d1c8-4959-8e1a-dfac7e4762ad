package com.yelink.dfs.constant.print;


/**
 * <AUTHOR>
 * @Date 2022/4/21 18:35
 */
public enum PrintTemplateTypeEnum {

    /**
     * 模板枚举
     */
    SALE_ORDER("saleOrder", "销售订单"),
    PRODUCT_ORDER("productOrder", "生产订单"),
    WORK_ORDER("workOrder", "生产工单"),
    DELIVERY_ORDER("deliveryOrder", "发货单"),
    
    ;

    private String type;
    private String typeName;


    PrintTemplateTypeEnum(String type, String typeName) {
        this.type = type;
        this.typeName = typeName;
    }

    public String getType() {
        return type;
    }

    public String getTypeName() {
        return typeName;
    }


    public static PrintTemplateTypeEnum getByType(String type) {
        for (PrintTemplateTypeEnum rateEnum : PrintTemplateTypeEnum.values()) {
            if (type.equals(rateEnum.getType())) {
                return rateEnum;
            }
        }
        return null;
    }
    
    public static String getNameByType(String type) {
        for (PrintTemplateTypeEnum typeEnum : PrintTemplateTypeEnum.values()) {
            if (typeEnum.getType().equals(type)) {
                return typeEnum.getTypeName();
            }
        }
        return null;
    }
}
