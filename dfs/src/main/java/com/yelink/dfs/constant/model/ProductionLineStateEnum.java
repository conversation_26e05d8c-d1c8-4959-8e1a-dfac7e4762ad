package com.yelink.dfs.constant.model;


import com.baomidou.mybatisplus.annotation.EnumValue;


/**
 * @Description: 产线状态枚举
 * @Author: zheng<PERSON>
 * @Date: 2020/12/5
 */
public enum ProductionLineStateEnum {

    /**
     * 状态编码及描述
     * 1-生产中 2-暂停生产 3-闲置
     */
    PRODUCING(1, "生产中"),
    SUSPEND(2, "暂停"),
    UNUSED(3, "停止");

    @EnumValue
    private int code;
    private String name;

    ProductionLineStateEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }

    public int getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static String getNameByCode(Integer code) {
        if (code == null) {
            return UNUSED.name;
        }
        for (ProductionLineStateEnum stateEnum : ProductionLineStateEnum.values()) {
            if (stateEnum.code == code) {
                return stateEnum.name;
            }
        }
        return null;
    }
}
