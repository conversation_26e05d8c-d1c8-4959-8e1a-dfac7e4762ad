package com.yelink.dfs.constant.product;

import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Description 工序检测项的比较符
 * @Date 2022/4/19 14:40
 */
public enum InspectComparatorEnum {

//    >、=>、<、<=、<>、><，不填默认为<>；b) 字符型, 有=、!=、in、not in，默认为in; c) 布尔型，只有=，默认为=；
//    保存时，进行检验项(数据类型)与所填值进行校验

    //数值型
    /**
     * >、>=、<、<=、<>、><
     */
    BIG("大于", ">", DataTypeEnum.NUMERICAL_TYPE.getFieldEname()),
    SMALL("小于", "<", DataTypeEnum.NUMERICAL_TYPE.getFieldEname()),
    BIG_EQUAL("大于等于", ">=", DataTypeEnum.NUMERICAL_TYPE.getFieldEname()),
    SMALL_EQUAL("小于等于", "<=", DataTypeEnum.NUMERICAL_TYPE.getFieldEname()),
    DOUBLE_SYMBOL("范围内", "<>", DataTypeEnum.NUMERICAL_TYPE.getFieldEname()),
    BETWEEN("范围外", "><", DataTypeEnum.NUMERICAL_TYPE.getFieldEname()),
    DATA_CHAR_EQUAL("相等", "=", DataTypeEnum.NUMERICAL_TYPE.getFieldEname()),
    //字符型
    CHAR_EQUAL("相等", "=", DataTypeEnum.CHARACTER_TYPE.getFieldEname()),
    NOT_EQUAL("不等", "!=", DataTypeEnum.CHARACTER_TYPE.getFieldEname()),
    IN("包含在内", "in", DataTypeEnum.CHARACTER_TYPE.getFieldEname()),
    NOT_IN("不包含在内", "not in", DataTypeEnum.CHARACTER_TYPE.getFieldEname()),
    //布尔型
    EQUAL("等于", "==", DataTypeEnum.BOOLEAN_TYPE.getFieldEname());

    private String code;
    private String name;
    private String typeName;

    InspectComparatorEnum(String name, String code, String typeName) {
        this.name = name;
        this.code = code;
        this.typeName = typeName;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public String getTypeName() {
        return typeName;
    }

    public static String getNameByCode(String code) {
        if (StringUtils.isBlank(code)) {
            return null;
        }
        for (InspectComparatorEnum stateEnum : InspectComparatorEnum.values()) {
            if (stateEnum.code.equals(code)) {
                return stateEnum.name;
            }
        }
        return null;
    }

    public static String getCodeByName(String name) {
        if (StringUtils.isBlank(name)) {
            return null;
        }
        for (InspectComparatorEnum stateEnum : InspectComparatorEnum.values()) {
            if (name.equals(stateEnum.name)) {
                return stateEnum.code;
            }
        }
        return null;
    }

    public static List<String> getNames() {
        ArrayList<String> names = new ArrayList<>();
        for (InspectComparatorEnum stateEnum : InspectComparatorEnum.values()) {
            names.add(stateEnum.name);
        }
        return names;
    }
}
