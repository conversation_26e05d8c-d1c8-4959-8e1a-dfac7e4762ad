package com.yelink.dfs.constant.notice;

/**
 * @description: 消息通知人员类型
 * @author: zhuang
 * @time: 2021/5/28
 */
public enum NoticeContactTypeEnum {

    /**
     * 消息通知人员类型
     */
    RECEIVE("receive", "消息接收方"),
    SEND("send", "消息发送方");

    private String code;
    private String name;

    NoticeContactTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static String getNameByCode(String code) {
        for (NoticeContactTypeEnum alarmDealStateEnum : NoticeContactTypeEnum.values()) {
            if (alarmDealStateEnum.code.equals(code)) {
                return alarmDealStateEnum.name;
            }
        }
        return null;
    }


}
