package com.yelink.dfs.constant.alarm;


/**
 * @Description: IOT设备对接类型、码值、描述
 * @Author: zwq
 * @Date: 2021/6/23
 */
public enum IotAlarmEnum {

    /**
     * 系统告警类型
     */
    DOCKING_ALARM("5001", "设备对接失败", "设备获数据失败，请检查物料号和批次号");


    private String code;
    private String name;
    private String des;


    IotAlarmEnum(String code, String name, String des) {
        this.code = code;
        this.name = name;
        this.des = des;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public String getDes() {
        return des;
    }

    public static IotAlarmEnum getByCode(String code) {
        for (IotAlarmEnum targetAlarmTypeEnum : IotAlarmEnum.values()) {
            if (targetAlarmTypeEnum.getCode().equals(code)) {
                return targetAlarmTypeEnum;
            }
        }
        return null;
    }

}
