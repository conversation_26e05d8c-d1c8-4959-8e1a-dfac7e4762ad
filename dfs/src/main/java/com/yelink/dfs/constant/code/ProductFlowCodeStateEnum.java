package com.yelink.dfs.constant.code;

/**
 * <AUTHOR>
 * @Date 2022/4/1 9:50
 */
public enum ProductFlowCodeStateEnum {
    /**
     * 类型编码及描述
     * 0-未打印 1-已打印
     */
    NO_PRINT(0,"未打印"),
    PRINT(1,"已打印")
    ;

    private Integer code;
    private String name;

    ProductFlowCodeStateEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }

    public int getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static String getNameByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (ProductFlowCodeStateEnum stateEnum : ProductFlowCodeStateEnum.values()) {
            if (stateEnum.code.equals(code)) {
                return stateEnum.name;
            }
        }
        return null;
    }

    public static Integer getCodeByName(String name) {
        for (ProductFlowCodeStateEnum stateEnum : ProductFlowCodeStateEnum.values()) {
            if (name.equals(stateEnum.name)) {
                return stateEnum.code;
            }
        }
        return null;
    }
}
