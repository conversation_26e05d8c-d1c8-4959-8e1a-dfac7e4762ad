package com.yelink.dfs.constant.device;

import org.apache.commons.lang3.StringUtils;

/**
 * @description: 设备运行记录新增类型
 * @author: shuang
 * @time: 2020/6/22
 */
public enum DeviceAddTypeEnum {


    /**
     *
     */
    DEVICE_FINISH("finish", "完成时间", null),
    DEVICE_STOP("stop", "设备停止", 0),
    DEVICE_RUN("run", "设备运行", 1),
    DEVICE_PAUSE("pause", "设备暂停", 2),
    ;


    private String type;
    private String typeName;
    private Integer state;

    DeviceAddTypeEnum(String type, String typeName, Integer state) {
        this.type = type;
        this.typeName = typeName;
        this.state = state;
    }

    public String getType() {
        return type;
    }

    public String getTypeName() {
        return typeName;
    }

    public Integer getState() {
        return state;
    }

    public static String getNameByType(String type) {
        if (StringUtils.isBlank(type)) {
            return null;
        }
        for (DeviceAddTypeEnum typeEnum : DeviceAddTypeEnum.values()) {
            if (typeEnum.type.equals(type)) {
                return typeEnum.typeName;
            }
        }
        return null;
    }

    public static String getTypeByState(Integer state) {
        if (state == null) {
            return null;
        }
        for (DeviceAddTypeEnum typeEnum : DeviceAddTypeEnum.values()) {
            if (state.equals(typeEnum.state)) {
                return typeEnum.type;
            }
        }
        return null;
    }

    public static Integer getStateByType(String type) {
        if (StringUtils.isBlank(type)) {
            return null;
        }
        for (DeviceAddTypeEnum typeEnum : DeviceAddTypeEnum.values()) {
            if (typeEnum.type.equals(type)) {
                return typeEnum.state;
            }
        }
        return null;
    }

}
