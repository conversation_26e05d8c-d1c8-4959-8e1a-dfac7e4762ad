package com.yelink.dfs.constant.sensor;

/**
 * @description: 设备类型编号以及名称
 * @author: shuang
 * @time: 2020/6/18
 */
public enum SensorTypeCodeEnum {

    /**
     * 设备类型
     */
    ELECTRONIC_RACK(1004, "电子料架"),
    COMMON_COLLECTOR(1000, "通用设备类型"),

    TEMPERATURE_SENSOR(1001, "温度探测器"),
    TEMPERATURE_AND_HUMIDITY_SENSOR(1002, "温湿度探测器"),
    SWITCH_SENSOR(1005, "开关量探测器"),
    //CAMERA(1016, "摄像头"),
    COUNTER_SENSOR(1018, "计数器"),
    WRISTBAND(1019, "机器手环"),
    CUBICLE_BOARD(1020, "工位机"),
    LINE_BOARD(1021, "产线看板"),

    //######################yangzj#########################
    BOTTLE_WASHING_MACHINE(1023, "洗瓶机"),
    TUNNEL_OVEN_MACHINE(1024, "隧道烘箱"),
    FILLING_MACHINE(1025, "灌装机"),
    DISPENSER_MACHINE(1026, "配液系统"),
    WATER_STERILIZED_CABINET(1027, "水浴式灭菌柜"),
    VENTILATION_STERILIZED_CABINET(1028, "通风式干燥灭菌柜"),
    LAMP_EXAMINERS(1029, "灯检机"),
    LABELING_MACHINE(1030, "贴签机"),
    BLISTER_PACKAGING_MACHINE(1031, "泡罩包装机"),
    CARTON_MACHINE(1032, "装盒机"),
    FILM_STRAPPING_MACHINE(1033, "薄膜捆包机"),
    PACKING(1034, "装箱机"),
    LEAK_PICKER(1035, "捡漏机"),
    ELECTRONIC_LABEL_CODE(1036, "电子监管码"),
    DISPENSER_1500_MACHINE(1037, "配液系统1500"),
    TUNNEL_OVEN_BELT(1038, "隧道烘箱传送带"),
    AIR_CONDITIONING_SYSTEM(1039, "空调系统"),
    FILLING_MACHINE_DONG_FU_LONG(1041, "东富龙灌装机"),
    BOTTLE_WASHING_MACHINE_ACX160(1047, "洗瓶机ACX160"),
    TUNNEL_OVEN_MACHINE_STATE(1048, "烘箱状态采集器"),
    LABELING_MACHINE_STATE(1049, "贴标机状态采集器"),
    DONGFULONG_TUNNEL_OVEN_MACHINE(1050, "东富龙隧道烘箱"),

    //######################LCM#########################
    AUTOMATIC_COG_EQUIPMENT(1042, "全自动COG设备"),
    AUTOMATIC_FOG_EQUIPMENT(1043, "全自动FOG设备"),
    AUTOMATIC_PULL_UP_MACHINE(1044, "LCD全自动上料机"),
    TERMINAL_CLEANING_MACHINE(1045, "LCD端子清洗机"),
    AUTOMATIC_DISPENSING_MACHINE(1046, "自动点胶机"),

    //####################粤深钢##########################
    ELECTRIC_FURNACE_OLD(9000, "旧电炉"),
    ELECTRIC_FURNACE_NEW(9001, "新电炉"),
    ELECTRONIC_SCALE(9002, "电子秤"),
    PARALLEL_FEEDING(9003, "平行送料"),
    OXYGEN_BLOWING_POWDER_SPRAYING(9004, "吹氧喷粉"),
    MOLTEN_STEEL_THERMOMETER(9005, "钢水测温仪"),
    SPECTROGRAPH(9006, "光谱仪"),

    //###################奥德#################################
    WIFI_AIR_MONITORING(9008, "wifi空气监测仪"),
    COLLECTOR(9007, "485采集器"),
    //###################君兰#################################
    MARKING_MACHINE(2000, "打标机"),
    AOI_MACHINE(2001, "AOI采集设备"),
    MARKING_MACHINE_STATE(2002, "打标机状态"),
    AOI_MACHINE_STATE(2003, "AOI状态"),
    SPI_MACHINE_STATE(2004, "SPI状态"),
    SPI_MACHINE(2005, "SPI采集设备"),

    //###################奥维斯#################################
    WEIGHING_MACHINE(2050, "称重机"),
    //这个不是奥维斯专属的,其他工厂也可以用,只是序号连着放在这里
    CODE_SCANNER(2051, "扫码器"),

    //###################扫码机#################################
    BAR_CODE_SCANNER(3001, "流水码扫码器"),
    CCD_DETECTION(3002, "ccd检测机"),
    ;


    private int code;
    private String name;


    SensorTypeCodeEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }


    public int getCode() {
        return code;
    }

    public String getName() {
        return name;
    }


    public static String getNameByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (SensorTypeCodeEnum sensorTypeCodeEnum : SensorTypeCodeEnum.values()) {
            if (sensorTypeCodeEnum.getCode() == (code)) {
                return sensorTypeCodeEnum.getName();
            }
        }
        return null;
    }

    /**
     * 是否存在于通用枚举类型中
     */
    public static Integer getCodeByNameInCommonEnum(String name) {
        if (name == null) {
            return null;
        }
        SensorTypeCodeEnum[] commonEnum = getCommonEnum();
        for (SensorTypeCodeEnum sensorTypeCodeEnum : commonEnum) {
            if (sensorTypeCodeEnum.getName().equals(name)) {
                return sensorTypeCodeEnum.getCode();
            }
        }
        return null;
    }

    public static SensorTypeCodeEnum[] getCommonEnum() {
        return new SensorTypeCodeEnum[]{
                //(1000, "通用设备类型")
                COMMON_COLLECTOR,
                //(1001, "温度探测器")
                TEMPERATURE_SENSOR,
                //(1002, "温湿度探测器")
                TEMPERATURE_AND_HUMIDITY_SENSOR,
                //(1004, "电子料架")
                ELECTRONIC_RACK,
                //(1005, "开关量探测器")
                SWITCH_SENSOR,
                //(1018, "计数器")
                COUNTER_SENSOR,
                //(1019, "机器手环")
                WRISTBAND,
                //(3001, "流水码扫码器")
                BAR_CODE_SCANNER,
        };
    }


}
