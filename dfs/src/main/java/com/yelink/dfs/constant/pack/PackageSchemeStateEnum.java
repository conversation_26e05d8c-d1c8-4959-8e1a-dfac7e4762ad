package com.yelink.dfs.constant.pack;

import com.baomidou.mybatisplus.annotation.EnumValue;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;

/**
 * @Description: 包装方案状态枚举
 * @Author: Chensn
 * @Date: 2021/04/08
 */
public enum PackageSchemeStateEnum {

    /**
     * 包装方案状态枚举
     * 1-创建 2-生效 3-作废
     */
    CREATE(1, "创建"),
    RELEASED(2, "生效"),
    //STOP_USING(3, "停用"),
    ABANDONED(3, "废弃"),
    ;

    @EnumValue
    private int code;
    private String name;

    PackageSchemeStateEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }

    public static PackageSchemeStateEnum getByName(String stateName) {
        return Arrays.stream(PackageSchemeStateEnum.values()).filter(v -> stateName.equals(v.getName())).findFirst().orElse(null);
    }

    public int getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static String getNameByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (PackageSchemeStateEnum stateEnum : PackageSchemeStateEnum.values()) {
            if (stateEnum.code == code) {
                return stateEnum.name;
            }
        }
        return null;
    }

    public static Integer getCodeByName(String name) {
        if (StringUtils.isBlank(name)) {
            return null;
        }
        for (PackageSchemeStateEnum stateEnum : PackageSchemeStateEnum.values()) {
            if (stateEnum.name .equals(name) ) {
                return stateEnum.code;
            }
        }
        return null;
    }
}
