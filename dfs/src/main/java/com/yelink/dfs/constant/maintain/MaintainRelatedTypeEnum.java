package com.yelink.dfs.constant.maintain;


/**
 * @Description: 质检方案关联类型枚举
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2020/12/5
 */
public enum MaintainRelatedTypeEnum {

    /**
     * 不良类型枚举
     */
    STATION("station", "工位");

    private String code;
    private String name;

    MaintainRelatedTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static String getNameByCode(String code) {
        if (code == null) {
            return null;
        }
        for (MaintainRelatedTypeEnum stateEnum : MaintainRelatedTypeEnum.values()) {
            if (stateEnum.code.equals(code)) {
                return stateEnum.name;
            }
        }
        return null;
    }

    public static String getCodeByName(String name) {
        for (MaintainRelatedTypeEnum stateEnum : MaintainRelatedTypeEnum.values()) {
            if (name.equals(stateEnum.name)) {
                return stateEnum.code;
            }
        }
        return null;
    }
}
