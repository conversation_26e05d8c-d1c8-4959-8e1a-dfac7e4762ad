package com.yelink.dfs.constant.common.config;

import org.apache.commons.lang3.StringUtils;

/**
 * 下推类型
 *
 * <AUTHOR>
 * @Date 2022/9/20 17:20
 */
public enum ProductOrderMaterialListSmartConfigEnum {
    /**
     * 跳转页面、按bom下推、合单下推、按bom合单下推、按工艺路线下推、按工艺路线合单下推
     */
    SUBSTITUTE_ENABLE("substituteEnable", "使用替代"),
    INVENTORY_ASSIGN("inventoryAssign", "库存分配"),
    CLEAR_USELESS_SUBSTITUTE("clearUselessSubstitute", "清除无用替代"),
    ;

    private final String typeCode;
    private final String typeName;

    ProductOrderMaterialListSmartConfigEnum(String typeCode, String typeName) {
        this.typeCode = typeCode;
        this.typeName = typeName;
    }

    public String getTypeCode() {
        return typeCode;
    }

    public String getTypeName() {
        return typeName;
    }


    public static String getNameByCode(String code) {
        if (StringUtils.isNotBlank(code)) {
            for (ProductOrderMaterialListSmartConfigEnum typeEnum : ProductOrderMaterialListSmartConfigEnum.values()) {
                if (typeEnum.getTypeCode().equals(code)) {
                    return typeEnum.getTypeName();
                }
            }
        }
        return null;
    }

    public static String getCodeByName(String name) {
        if (StringUtils.isNotBlank(name)) {
            for (ProductOrderMaterialListSmartConfigEnum typeEnum : ProductOrderMaterialListSmartConfigEnum.values()) {
                if (typeEnum.getTypeName().equals(name)) {
                    return typeEnum.getTypeCode();
                }
            }
        }
        return null;
    }
}
