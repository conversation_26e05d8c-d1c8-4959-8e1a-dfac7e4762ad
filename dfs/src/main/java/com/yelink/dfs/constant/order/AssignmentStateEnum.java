package com.yelink.dfs.constant.order;

import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @Date 2021/11/8 15:35
 */
public enum AssignmentStateEnum {

    /**
     * 派工状态
     */
    TO_BE_ASSIGNED("toBeAssigned", "待派工"),
    ASSIGNED("assigned", "已派工"),
    ;

    private String type;
    private String typeName;


    AssignmentStateEnum(String type, String typeName) {
        this.type = type;
        this.typeName = typeName;
    }

    public String getType() {
        return type;
    }

    public String getTypeName() {
        return typeName;
    }


    public static AssignmentStateEnum getByType(String type) {
        if (StringUtils.isBlank(type)) {
            return null;
        }
        for (AssignmentStateEnum orderTypeEnum : AssignmentStateEnum.values()) {
            if (type.equals(orderTypeEnum.getType())) {
                return orderTypeEnum;
            }
        }
        return null;
    }

    public static String getNameByType(String type) {
        for (AssignmentStateEnum orderTypeEnum : AssignmentStateEnum.values()) {
            if (orderTypeEnum.getType().equals(type)) {
                return orderTypeEnum.getTypeName();
            }
        }
        return null;
    }

    public static String getTypeByName(String name) {
        for (AssignmentStateEnum orderTypeEnum : AssignmentStateEnum.values()) {
            if (orderTypeEnum.getTypeName().equals(name)) {
                return orderTypeEnum.getType();
            }
        }
        return null;
    }
}
