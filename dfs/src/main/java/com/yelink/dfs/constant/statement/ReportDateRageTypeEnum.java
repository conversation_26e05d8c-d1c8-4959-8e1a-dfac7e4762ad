package com.yelink.dfs.constant.statement;

import com.yelink.dfs.entity.statement.dto.CodeNameDTO;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/8/13 10:41
 */
public enum ReportDateRageTypeEnum {
    /**
     * 今天、昨天
     */
    TODAY("Today", "今天"),
    YESTERDAY("Yesterday", "昨天"),
    THIS_WEEK("This week", "本周"),
    THIS_MONTH("This month", "本月"),
    THIS_QUARTER("This quarter", "本季度"),
    THIS_YEAR("This year", "今年"),
    LAST_7_DAYS("Last 7 days", "过去7天"),
    LAST_30_DAYS("Last 30 days", "过去30天"),
    LAST_WEEK("Last week", "上周"),
    LAST_MONTH("Last month", "上个月"),
    LAST_QUARTER("Last quarter", "上个季度"),
    LAST_YEAR("Last year", "去年"),
    ;

    private final String code;
    private final String name;

    ReportDateRageTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static List<CodeNameDTO> codeNameList() {
        List<CodeNameDTO> list = new ArrayList<>();
        for (ReportDateRageTypeEnum value : ReportDateRageTypeEnum.values()) {
            list.add(CodeNameDTO.builder()
                    .code(value.getCode())
                    .name(value.getName())
                    .build());
        }
        return list;
    }


}
