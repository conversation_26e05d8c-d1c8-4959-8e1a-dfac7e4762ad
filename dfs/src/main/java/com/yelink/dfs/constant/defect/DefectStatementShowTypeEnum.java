package com.yelink.dfs.constant.defect;

import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import com.yelink.dfs.entity.target.metrics.MetricsQualityLineMaterialDefectDailyEntity;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * @Description:
 * @Author:
 * @Date: 2021/10/10
 */
public enum DefectStatementShowTypeEnum {


    /**
     * 按物料
     */
    MATERIAL("material",
            Arrays.asList(MetricsQualityLineMaterialDefectDailyEntity::getMaterialCode,
                    MetricsQualityLineMaterialDefectDailyEntity::getMaterialName,
                    MetricsQualityLineMaterialDefectDailyEntity::getRecordDate)),
    /**
     * 按物料+产线
     */
    MATERIAL_LINE("material_line",
            Arrays.asList(MetricsQualityLineMaterialDefectDailyEntity::getMaterialCode,
                    MetricsQualityLineMaterialDefectDailyEntity::getMaterialName,
                    MetricsQualityLineMaterialDefectDailyEntity::getLineName,
                    MetricsQualityLineMaterialDefectDailyEntity::getRecordDate)),
    /**
     * 按产线+不良类型+不良项
     */
    LINE_DEFECT("line_defect",
            Arrays.asList(MetricsQualityLineMaterialDefectDailyEntity::getLineName,
                    MetricsQualityLineMaterialDefectDailyEntity::getDefectTypeCode,
                    MetricsQualityLineMaterialDefectDailyEntity::getDefectName,
                    MetricsQualityLineMaterialDefectDailyEntity::getRecordDate)),
    /**
     * 按物料+产线+不良类型+不良项
     */
    MATERIAL_LINE_DEFECT("material_line_defect",
            Arrays.asList(MetricsQualityLineMaterialDefectDailyEntity::getMaterialCode,
                    MetricsQualityLineMaterialDefectDailyEntity::getMaterialName,
                    MetricsQualityLineMaterialDefectDailyEntity::getLineName,
                    MetricsQualityLineMaterialDefectDailyEntity::getDefectTypeCode,
                    MetricsQualityLineMaterialDefectDailyEntity::getDefectName,
                    MetricsQualityLineMaterialDefectDailyEntity::getRecordDate)),
    ;

    private String showType;
    private List<SFunction<MetricsQualityLineMaterialDefectDailyEntity, ?>> sFunctions;

    DefectStatementShowTypeEnum(String showType, List<SFunction<MetricsQualityLineMaterialDefectDailyEntity, ?>> sFunctions) {
        this.showType = showType;
        this.sFunctions = sFunctions;
    }

    public String getShowType() {
        return showType;
    }

    public List<SFunction<MetricsQualityLineMaterialDefectDailyEntity, ?>> getsFunctions() {
        return sFunctions;
    }


    public static List<SFunction<MetricsQualityLineMaterialDefectDailyEntity, ?>> getFunctionsByShowType(String showType) {
        for (DefectStatementShowTypeEnum typeEnum : DefectStatementShowTypeEnum.values()) {
            if (typeEnum.getShowType().equals(showType)) {
                return typeEnum.getsFunctions();
            }
        }
        return new ArrayList<>();
    }
    
}
