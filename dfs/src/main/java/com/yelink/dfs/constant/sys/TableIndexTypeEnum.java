package com.yelink.dfs.constant.sys;

/**
 * 表索引类型定义
 */
public enum TableIndexTypeEnum {
    /**
     * 表索引类型定义
     */
    UNIQUE("UNIQUE", "唯一索引"),
    NORMAL("NORMAL", "普通索引"),
    ;

    /**
     * 字段类型
     */
    private String code;
    /**
     * 默认长度
     */
    private String name;

    TableIndexTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }


    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

}
