package com.yelink.dfs.constant.product;

/**
 * @description: 工艺更新类型枚举
 * @author: zhuang
 * @time: 2021/5/28
 */
public enum CraftUpdateTypeEnum {

    /**
     * 更新方式
     *  更新时，根据文档导入的工序进行已有工序的更新
     *  替代时，会按照文档导入的工序项更新整个工艺，文档没有的工序会被删除，如果已有生效工艺则报错提醒
     */
    UPDATE("update", "更新"),
    REPLACE("replace", "替代");

    private String code;
    private String name;

    CraftUpdateTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static String getNameByCode(String code) {
        for (CraftUpdateTypeEnum craftTypeEnum : CraftUpdateTypeEnum.values()) {
            if (craftTypeEnum.code.equals(code)) {
                return craftTypeEnum.name;
            }
        }
        return null;
    }

    public static String getCodeByName(String name) {
        for (CraftUpdateTypeEnum craftTypeEnum : CraftUpdateTypeEnum.values()) {
            if (craftTypeEnum.name.equals(name)) {
                return craftTypeEnum.code;
            }
        }
        return null;
    }

}
