package com.yelink.dfs.constant.stock;

import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * 库房类型枚举
 * @Date 2022/2/14 11:33
 */
public enum WarehouseTypeEnum {
    /**
     * 库房类型枚举
     */
    RAW_MATERIAL_WAREHOUSE("rawMaterialWarehouse", "原材料库房"),
    SEMI_FINISHED_PRODUCT_WAREHOUSE("semiFinishedProductWarehouse", "半成品库房"),
    FINISHED_PRODUCT_WAREHOUSE("finishedProductWarehouse", "成品库房"),
    OTHER("other", "其他"),
    ;


    private String code;
    private String name;

    WarehouseTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static String getNameByCode(String code) {
        if (StringUtils.isBlank(code)) {
            return null;
        }
        for (WarehouseTypeEnum warehouseTypeEnum : WarehouseTypeEnum.values()) {
            if (warehouseTypeEnum.code.equals(code)) {
                return warehouseTypeEnum.name;
            }
        }
        return null;
    }

    public static String getCodeByName(String name) {
        for (WarehouseTypeEnum warehouseTypeEnum : WarehouseTypeEnum.values()) {
            if (name.equals(warehouseTypeEnum.name)) {
                return warehouseTypeEnum.code;
            }
        }
        return null;
    }
}
