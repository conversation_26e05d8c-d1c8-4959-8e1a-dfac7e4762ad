package com.yelink.dfs.constant;

/**
 * <AUTHOR>
 * @description: 停机类型
 * @time 2021/5/24 10:46
 */
public enum StopsType {
    /**
     * 类型
     */
    STOP(900, "stopType", "停机类型");

    private Integer code;
    private String type;
    private String name;


    StopsType(Integer code, String type, String name) {
        this.code = code;
        this.type = type;
        this.name = name;
    }

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public String getType() {
        return type;
    }

    public static String getNameByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (StopsType stopsType : StopsType.values()) {
            if (code.intValue() == stopsType.code.intValue()) {
                return stopsType.getName();
            }
        }
        return null;
    }
}
