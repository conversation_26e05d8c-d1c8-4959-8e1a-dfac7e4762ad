package com.yelink.dfs.constant.product;

/**
 * @description: 输入框类型
 * @author: zhuang
 * @time: 2021/5/28
 */
public enum MaterialAttributeInputTypeEnum {

    /**
     * 输入框类型
     */
    INPUT("input", "输入框"),
    SELECT("select", "下拉单选"),
    SELECT_MULTIPLE("selectMultiple", "下拉多选"),
    DATE("date", "日期");

    private String code;
    private String name;

    MaterialAttributeInputTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static String getNameByCode(String code) {
        for (MaterialAttributeInputTypeEnum craftTypeEnum : MaterialAttributeInputTypeEnum.values()) {
            if (craftTypeEnum.code.equals(code)) {
                return craftTypeEnum.name;
            }
        }
        return null;
    }

    public static String getCodeByName(String name) {
        for (MaterialAttributeInputTypeEnum craftTypeEnum : MaterialAttributeInputTypeEnum.values()) {
            if (craftTypeEnum.name.equals(name)) {
                return craftTypeEnum.code;
            }
        }
        return null;
    }

}
