package com.yelink.dfs.constant.product;


import com.baomidou.mybatisplus.annotation.EnumValue;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * @Description: 物料检验触发方式的枚举
 * @Author: zhuangwq
 * @Date: 2023/09/04
 */
public enum InspectTriggerConditionEnum {

    /**
     * 物料检验触发方式的枚举
     */
    CHECK_BY_FIRST_REPORT("checkByFirstReport", "首次报工执行校验", Arrays.asList("productOrderByOneOfWorkOrdersReport", "workOrderReport")),
    CHECK_BY_EVERY_REPORT("checkByEveryReport", "每次报工执行校验", Arrays.asList("productOrderByOneOfWorkOrdersReport", "workOrderReport")),
    CROSS_NEW_DATE_CHECK_BY_REPORT("crossNewDateCheckByReport", "跨天时校验(报工)", Arrays.asList("productOrderByOneOfWorkOrdersReport", "workOrderReport")),
    SHIFT_CHANGE_CHECK_BY_REPORT("shiftChangeCheckByReport", "首班时间校验(报工)", Arrays.asList("productOrderByOneOfWorkOrdersReport", "workOrderReport")),
    CHECK_BY_INVEST("checkByInvest", "投产执行校验", Arrays.asList("productOrderByOneOfWorkWorkOrderInvestment", "workOrderInvestment")),
    CHECK_BY_FINISH("checkByFinish", "完工执行校验", Arrays.asList("productOrderByOneOfWorkOrdersFinish", "workOrderFinish")),
    CHECK_BY_INCOMING("checkByIncoming", "来料执行校验", Collections.singletonList("purchaseInputIncomingInspection")),
    ;

    @EnumValue
    private String code;
    private String name;
    /**
     * 限制检验方式
     * {@link MaterialInspectMethodEnum}
     */
    private List<String> limitInspectMethods;

    InspectTriggerConditionEnum(String code, String name, List<String> limitInspectMethods) {
        this.code = code;
        this.name = name;
        this.limitInspectMethods = limitInspectMethods;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public List<String> getLimitInspectMethods() {
        return limitInspectMethods;
    }

    public static String getNameByCode(String code) {
        if (code == null) {
            return null;
        }
        for (InspectTriggerConditionEnum stateEnum : InspectTriggerConditionEnum.values()) {
            if (stateEnum.code.equals(code)) {
                return stateEnum.name;
            }
        }
        return null;
    }

    public static String getCodeByName(String name) {
        if (StringUtils.isBlank(name)) {
            return null;
        }
        for (InspectTriggerConditionEnum stateEnum : InspectTriggerConditionEnum.values()) {
            if (stateEnum.name.equals(name)) {
                return stateEnum.code;
            }
        }
        return null;
    }

}
