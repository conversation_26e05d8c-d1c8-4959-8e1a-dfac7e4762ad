package com.yelink.dfs.constant;

import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * 导入类型枚举
 * @Date 2022/3/16 20:09
 */
public enum ImportTypeEnum {
    /**
     * 类型
     */
    BASE_DATA_BATCH_IMPORT("baseDataBatchImport","基础数据一键式导入结果"),
    BOM_IMPORT("bom", "BOM导入结果"),
    SALE_ORDER_IMPORT("saleOrder", "销售订单导入结果"),
    PRODUCT_ORDER_IMPORT("productOrder", "生产订单导入结果"),
    WORK_ORDER_IMPORT("workOrder", "生产工单导入结果"),
    WORK_ORDER_PLAN_IMPORT("workOrderPlan", "生产工单每日计划导入结果"),
    MATERIAL_TYPE_IMPORT("materialType", "物料类型导入结果"),
    MATERIAL_UNIT_IMPORT("materialUnit", "物料单位导入结果"),
    MATERIAL_IMPORT("material", "物料导入结果"),
    CAPACITY_IMPORT("capacity", "产能导入结果"),
    MATERIAL_INSPECT_IMPORT("materialInspect", "物料检验导入结果"),
    PROCEDURE_IMPORT("procedure", "工序导入结果"),
    CRAFT_IMPORT("craft", "工艺导入结果"),
    CRAFT_PROCEDURE_IMPORT("craftProcedure", "工艺工序导入结果"),
    CRAFT_PROCEDURE_MATERIAL("craftProcedureMaterial", "工艺工序物料导入结果"),
    CRAFT_PROCEDURE_MATERIAL_USED("craftProcedureMaterialUsed", "工艺工序用料导入结果"),
    CRAFT_PROCEDURE_INSPECT("craftProcedureInspect", "工艺工序检验导入结果"),
    CRAFT_PROCEDURE_INSPECT_CONTROLLER("craftProcedureInspectController", "工艺工序检验方案控制导入结果"),
    CRAFT_PROCEDURE_CONTROL("craftProcedureControl", "工艺工序控制导入结果"),
    CRAFT_PROCEDURE_WORK_HOUR("craftProcedureWorkHour", "工艺工序工时导入结果"),
    CRAFT_PROCEDURE_PARAMETER("craftProcedureParameter", "工艺工序参数导入结果"),
    CRAFT_PROCEDURE_FILE("craftProcedureFile", "工艺工序附件导入结果"),
    CRAFT_TEMPLATE_PROCEDURE_IMPORT("craftTemplateProcedure", "工艺模板工序导入结果"),
    CRAFT_PROCEDURE_DEVICE_IMPORT("craftProcedureDevice", "工艺工序设备导入结果"),
    USER_IMPORT("userImport","用户数据导入结果"),
    FACILITIES_IMPORT("facilitiesImport","工位数据导入结果"),
    PROCEDURE_INSPECT_DEFINE("procedureInspectDefine", "工序检验项定义导入结果"),
    USER_SIGNATURE_IMPORT("userSignature", "用户签名导入结果"),

    CUSTOMER_IMPORT("customerImport","客户档案数据导入结果"),
    CUSTOMER_MATERIAL_LIST_IMPORT("customerMaterialListImport","客户物料清单数据导入结果"),
    SUPPLIER_IMPORT("supplierImport","供应商档案数据导入结果"),
    PURCHASE_RECEIPT_MATERIAL_IMPORT("purchaseReceiptMaterialImport","采购入库-物料导入结果"),
    BAR_CODE_LOCATION_IMPORT("barCodeLocationImport","批次库位导入结果"),

    REPLACE_SCHEME_IMPORT("replaceScheme","替代方案导入结果"),

    SUPPLIER_MATERIAL_IMPORT("supplierMaterialImport","供应商物料导入结果"),
    PRODUCT_INSPECTION_SCHEME_IMPORT("productInspectionScheme","产品检测方案导入结果"),
    DEVICE_IMPORT("deviceImport", "生产设备导入结果"),
    SENSOR_IMPORT("sensorImport", "采集数据导入"),

    CELL_DATA("cellData", "电芯数据导入"),
    MODULE_DATA("moduleData", "模组数据导入"),
    PACK_DATA("packData", "PACK数据导入"),
    CELL_GEAR_POSITION("cellGearPosition", "电芯档位数据导入"),

    PRODUCTION_LINE_IMPORT("productionLineImport", "制造单元数据导入"),
    TEAM_IMPORT("teamImport", "班组数据导入"),
    DEFECT_DEFINE_IMPORT("defectDefineImport", "不良定义数据导入"),

    CODE_IMPORT("codeImport", "标识数据导入"),
    PROCESS_ASSEMBLY_IMPORT("processAssemblyImport", "工艺装备数据导入"),
    PROCESS_ASSEMBLY_APPENDIX_IMPORT("processAssemblyAppendixImport", "工艺装备数据导入"),
    VALUATION_CONFIG_IMPORT("valuationConfig", "工资配置导入结果"),
    MATERIAL_ATTRIBUTE_TYPE_IMPORT("materialAttributeTypeImport", "属性分类数据导入"),
    MATERIAL_ATTRIBUTE_IMPORT("materialAttributeImport", "物料属性数据导入"),
    DEVICE_TARGET_IMPORT("deviceTargetImport", "设备指标导入"),
    WORK_ORDER_MATERIAL_LIST_IMPORT("workOrderMaterialList", "生产工单用料清单导入结果"),
    AUXILIARY_ATTR_IMPORT("auxiliaryAttr", "物料特征参数导入结果"),
    BASE_MODEL_IMPORT("baseModel", "基础模型导入结果"),
    ROLE_PERMISSION_IMPORT("rolePermission", "角色权限导入结果"),
    BOM_COPY_IMPORT("bomCopy", "BOM复制导入结果"),
    ;

    private String type;
    private String typeName;


    ImportTypeEnum(String type, String typeName) {
        this.type = type;
        this.typeName = typeName;
    }

    public String getType() {
        return type;
    }

    public String getTypeName() {
        return typeName;
    }


    public static String getTypeByName(String type) {
        if (StringUtils.isBlank(type)) {
            return null;
        }
        for (ImportTypeEnum typeEnum : ImportTypeEnum.values()) {
            if (type.equals(typeEnum.getType())) {
                return typeEnum.getType();
            }
        }
        return null;
    }

    public static String getNameByType(String type) {
        if (StringUtils.isBlank(type)) {
            return null;
        }
        for (ImportTypeEnum typeEnum : ImportTypeEnum.values()) {
            if (typeEnum.getType().equals(type)) {
                return typeEnum.getTypeName();
            }
        }
        return null;
    }
}
