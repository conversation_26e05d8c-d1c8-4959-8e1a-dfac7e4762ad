package com.yelink.dfs.constant.model;


/**
 * @Description: 工位状态枚举
 * @Author: zheng<PERSON>
 * @Date: 2020/12/5
 */
public enum FacilitiesStateEnum {

    /**
     * 状态编码及描述
     * 工位、生产设施状态 0-停机 1-运行中 2-暂停 3-故障
     */
    STOP(0, "停机"),
    RUNNING(1, "运行中"),
    SUSPEND(2, "暂停"),
    FAULT(3, "故障");

    private int code;
    private String name;

    FacilitiesStateEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }

    public int getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static String getNameByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (FacilitiesStateEnum stateEnum : FacilitiesStateEnum.values()) {
            if (stateEnum.code == code) {
                return stateEnum.name;
            }
        }
        return null;
    }

    public static int getCodeByName(String name) {
        for (FacilitiesStateEnum stateEnum : FacilitiesStateEnum.values()) {
            if (name.equals(stateEnum.name)) {
                return stateEnum.code;
            }
        }
        return -999;
    }
}
