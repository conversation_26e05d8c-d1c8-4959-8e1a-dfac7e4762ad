package com.yelink.dfs.constant.alarm;


/**
 * @Description: 系统告警类型、码值、描述
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021/4/26
 */
public enum SystemAlarmEnum {

    /**
     * 系统告警类型
     */
    CAPACITY_ALARM("4001", "系统容量告警", "系统容量不足");


    private String code;
    private String name;
    private String des;


    SystemAlarmEnum(String code, String name, String des) {
        this.code = code;
        this.name = name;
        this.des = des;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public String getDes() {
        return des;
    }

    public static SystemAlarmEnum getByCode(String code) {
        for (SystemAlarmEnum targetAlarmTypeEnum : SystemAlarmEnum.values()) {
            if (targetAlarmTypeEnum.getCode().equals(code)) {
                return targetAlarmTypeEnum;
            }
        }
        return null;
    }

}
