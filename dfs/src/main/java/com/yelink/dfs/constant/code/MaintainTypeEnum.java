package com.yelink.dfs.constant.code;

/**
 * 维修工位机维修类型
 *
 * <AUTHOR>
 * @Date 2022/4/1 9:50
 */
public enum MaintainTypeEnum {
    /**
     * 维修类型及描述
     *
     */
    FINISHED(1, "成品"),
    REPAIR(2, "返修"),
    SCRAP(3, "报废"),
    SCRAP_REUSE(4, "报废-条码复用"),
    FREEZE(5, "冻结");

    private Integer code;
    private String name;

    MaintainTypeEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static String getNameByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (MaintainTypeEnum stateEnum : MaintainTypeEnum.values()) {
            if (stateEnum.code.equals(code)) {
                return stateEnum.name;
            }
        }
        return null;
    }

    public static Integer getCodeByName(String name) {
        for (MaintainTypeEnum stateEnum : MaintainTypeEnum.values()) {
            if (name.equals(stateEnum.name)) {
                return stateEnum.code;
            }
        }
        return null;
    }
}
