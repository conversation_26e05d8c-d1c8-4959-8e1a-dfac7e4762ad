package com.yelink.dfs.constant.alarm;

/**
 * @description: 常量
 * @author: z<PERSON><PERSON>
 * @create: 2020-06-06 14:09
 **/
public class NoticeConstant {

    /**
     * 获取openId地址
     */
    public static final String WX_OPENID_HTTP = "https://api.weixin.qq.com/sns/jscode2session?appid=%s&secret=%s&js_code=%s&grant_type=authorization_code";

    /**
     * 发送服务订阅消息的接口
     */
    public static final String SEND_MESSAGE_HTTP = "https://api.weixin.qq.com/cgi-bin/message/subscribe/send?access_token=%s";

    /**
     * 获取accessToken接口地址
     */
    public static final String GET_ACCESS_TOKEN_HTTP = "https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=%s&secret=%s";
    /**
     * 短信
     */
    public static final int SMS_NOTICE_TYPE = 0;

    /**
     * 电话
     */
    public static final int TEL_NOTICE_TYPE = 1;

    /**
     * 个推
     */
    public static final int GEXIN_NOTICE_TYPE = 2;

    /**
     * websocket弹窗
     */
    public static final int PUP_NOTICE_TYPE = 3;

    /**
     * 微信
     */
    public static final int WX_NOTICE_TYPE = 4;

}
