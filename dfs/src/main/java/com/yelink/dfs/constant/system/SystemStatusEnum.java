package com.yelink.dfs.constant.system;

import com.yelink.dfscommon.constant.ResponseException;
import lombok.Getter;

/**
 * 系统状态
 * <AUTHOR>
 */
public enum SystemStatusEnum {

    /**
     * 试运行状态下 -> 正式运行 将有一系列的功能关闭
     */
    TRY_OPERATION(0, "试运行"),
    FORMAL_OPERATION(1, "正式运行"),
    ;
    @Getter
    private final Integer code;
    @Getter
    private final String name;

    SystemStatusEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }
    public static SystemStatusEnum getByCode(Integer code) {
        for(SystemStatusEnum e: SystemStatusEnum.values()) {
            if(e.code.equals(code)) {
                return e;
            }
        }
        throw new ResponseException("未匹配到系统状态");
    }

}
