package com.yelink.dfs.constant.alarm;

import com.yelink.dfs.constant.target.TargetAlarmLevelEnum;
import lombok.Getter;

/**
 * 默认的告警分类
 * <AUTHOR>
 */
public enum DefaultAlarmDefinitionEnum {
    /**
     * 未知的类型
     */
    UNKNOWN("UNKNOWN", "未知类型", DefaultAlarmClassifyEnum.UNKNOWN),
    DEVICE_RUN_NO_ORDER("DEVICE_RUN_NO_ORDER", "设备运行, 无投产工单", DefaultAlarmClassifyEnum.DEVICE),
    DEVICE_STOP_HAS_ORDER("DEVICE_STOP_HAS_ORDER", "设备停机, 有投产工单", DefaultAlarmClassifyEnum.DEVICE),
    DEVICE_SUSPEND_HAS_ORDER("DEVICE_SUSPEND_HAS_ORDER", "设备待机, 有投产工单", DefaultAlarmClassifyEnum.DEVICE),
    DEVICE_WORK_ORDER_OVERPRODUCTION("DEVICE_WORK_ORDER_OVERPRODUCTION", "工单的完成数，超出计划", DefaultAlarmClassifyEnum.DEVICE),
    ;
    DefaultAlarmDefinitionEnum(String definitionCode, String definitionName, DefaultAlarmClassifyEnum defaultClassifyEnum) {
        this.definitionCode = definitionCode;
        this.definitionName = definitionName;
        this.defaultClassifyEnum = defaultClassifyEnum;
    }
    @Getter
    private final String definitionCode;
    @Getter
    private final String definitionName;
    @Getter
    private final TargetAlarmLevelEnum defaultAlarmLevelEnum = TargetAlarmLevelEnum.SERIOUS_ALARM;
    @Getter
    private final DefaultAlarmClassifyEnum defaultClassifyEnum;
}
