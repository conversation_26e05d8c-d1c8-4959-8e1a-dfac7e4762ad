package com.yelink.dfs.constant.defect;


import org.apache.commons.lang3.StringUtils;

/**
 * @Description: 不良类型枚举
 * @Author: zheng<PERSON>
 * @Date: 2020/12/5
 */
public enum DefectTypeEnum {

    /**
     * 不良类型枚举
     */
    SERIOUS_FAULT("seriousFault", "重故障"),
    SD_TF("SD/TF", "SD/TF"),
    USB("USB", "USB"),
    RADIO_AND_TAPE("radioAndTape", "收放"),
    FRONT_SHELL("frontShell", "前咀"),
    BUTTON1("button1", "钮1"),
    BUTTON2("button2", "钮2"),
    SHELL_ANTENNA_SCREW("shellAntennaScrew", "壳/天线/螺丝"),
    LOUDSPEAKER("loudspeaker", "喇叭"),
    SEL1("SEL1", "SEL1"),
    SEL2("SEL2", "SEL2"),
    MP5("MP5", "MP5"),
    BLUETOOTH("bluetooth", "蓝牙"),
    DAB("DAB", "DAB"),
    MONITOR1("monitor1", "显示屏1"),
    MONITOR2("monitor2", "显示屏2"),
    AUDIO_OUTPUT("audioOutput", "音频输出"),
    AUX("AUX", "AUX"),
    BUTTON3("button3", "钮3"),
    MONITOR3("monitor3", "显示屏3"),

    ;


    private String code;
    private String name;

    DefectTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static String getNameByCode(String code) {
        if (StringUtils.isNotBlank(code)) {
            for (DefectTypeEnum typeEnum : DefectTypeEnum.values()) {
                if (typeEnum.code.equals(code)) {
                    return typeEnum.name;
                }
            }
        }
        return null;
    }

    public static String getCodeByName(String name) {
        for (DefectTypeEnum stateEnum : DefectTypeEnum.values()) {
            if (name.equals(stateEnum.name)) {
                return stateEnum.code;
            }
        }
        return null;
    }
}
