package com.yelink.dfs.constant;

import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @description: 建立socket链接的类型
 * @time 2021/5/24 10:46
 */
public enum SocketMessageType {
    /**
     * 请求类型
     */
    LABORATORY_REPORT("laboratoryReport", "化验结果"),


    /**
     * 返回类型
     */
    FURNACE_CODE("furnaceCode", "炉次号信息"),
    SPECIMEN("specimen", "化验信息"),
    FURNACE_MATERIAL("furnaceMaterial", "炉次辅料信息");
    private final String type;
    private final String name;


    SocketMessageType(String type, String name) {
        this.type = type;
        this.name = name;
    }

    public String getName() {
        return name;
    }

    public String getType() {
        return type;
    }

    public static String getNameByCode(String type) {
        if (StringUtils.isBlank(type)) {
            return null;
        }
        for (SocketMessageType reportType : SocketMessageType.values()) {
            if (type.equals(reportType.type)) {
                return reportType.getName();
            }
        }
        return null;
    }
}

