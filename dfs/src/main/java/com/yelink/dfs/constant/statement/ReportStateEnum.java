package com.yelink.dfs.constant.statement;


import java.util.Arrays;

/**
 * <AUTHOR>
 * @Date 2023/7/4 12:00
 */
public enum ReportStateEnum {
    /**
     * 状态编码及描述
     */
    CLOSED(0, "关闭"),
    RELEASED(1, "生效")
    ;

    private final int code;
    private final String name;

    ReportStateEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }

    public int getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
    public static Object getByName(String stateName) {
        if(stateName == null){
            return null;
        }
        return Arrays.stream(ReportStateEnum.values()).filter(v -> stateName.equals(v.getName())).findFirst().orElse(null);
    }

    public static String getNameByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (ReportStateEnum stateEnum : ReportStateEnum.values()) {
            if (stateEnum.code == code) {
                return stateEnum.name;
            }
        }
        return null;
    }

    public static Integer getCodeByName(String name) {
        if(name == null){
            return null;
        }
        for (ReportStateEnum stateEnum : ReportStateEnum.values()) {
            if (name.equals(stateEnum.name)) {
                return stateEnum.code;
            }
        }
        return null;
    }
}
