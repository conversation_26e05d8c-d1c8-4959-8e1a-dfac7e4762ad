package com.yelink.dfs.constant.product;

import com.baomidou.mybatisplus.annotation.EnumValue;

/**
 * @Description: 附件类型
 * @Author: zhuangwq
 * @Date: 2021/04/08
 */
public enum ProcedureFileTypeEnum {

    /**
     * 物料/bom类型
     * 1001-成品 1002-半成品 1003-原料
     */
    SOP(1, "SOP操作"),
    ROUTINE_MAINTENANCE(2, "附件类型 1-SOP操作，2-例行维护"),
    ;

    @EnumValue
    private Integer code;
    private String name;

    ProcedureFileTypeEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public static Integer getCodeByName(String name) {
        if (name == null) {
            return null;
        }
        for (ProcedureFileTypeEnum typeEnum : ProcedureFileTypeEnum.values()) {
            if (typeEnum.getName().equals(name)) {
                return typeEnum.getCode();
            }
        }
        return null;
    }

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

}
