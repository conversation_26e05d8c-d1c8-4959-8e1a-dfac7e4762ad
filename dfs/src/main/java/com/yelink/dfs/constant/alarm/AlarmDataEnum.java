package com.yelink.dfs.constant.alarm;

import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @description 告警定义数据来源枚举
 * @Date 2021/11/2 10:39
 */
public enum AlarmDataEnum {

    /**
     * 数据来源
     */
    SYSTEM_GENERATION("systemGeneration", "系统生成"),
    MANUAL_ENTRY("manualEntry", "手工录入");


    private String code;
    private String name;

    AlarmDataEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static String getNameByCode(String code) {
        if (StringUtils.isBlank(code)) {
            return null;
        }
        for (AlarmDataEnum dataEnum : AlarmDataEnum.values()) {
            if (dataEnum.getCode().equals(code)) {
                return dataEnum.getName();
            }
        }
        return null;
    }

    public static String getCodeByName(String name) {
        for (AlarmDataEnum dataEnum : AlarmDataEnum.values()) {
            if (name.equals(dataEnum.name)) {
                return dataEnum.code;
            }
        }
        return null;
    }


}
