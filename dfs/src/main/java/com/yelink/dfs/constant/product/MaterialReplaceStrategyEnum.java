package com.yelink.dfs.constant.product;


import com.baomidou.mybatisplus.annotation.EnumValue;
import org.apache.commons.lang3.StringUtils;

/**
 * @Description: 物料替代策略  枚举
 * @Author: zhuangwq
 * @Date: 2022/12/12
 */
public enum MaterialReplaceStrategyEnum {

    /**
     * 新版本只有不限制
     * 替代策略（不限制：noLimit）
     */
    NO_LIMIT("noLimit", "不限制", true),
    MIXED_AND_BATCH_REPLACE("mixedAndBatchReplace", "整批 + 混用替代", false),
    MIXED_REPLACE("mixedReplace", "混用替代", false),
    BATCH_REPLACE("batchReplace", "整批替代", false),
    MANUAL_REPLACE("manualReplace", "手工替代", false),
    ;

    @EnumValue
    private String code;
    private String name;
    private Boolean enable;

    MaterialReplaceStrategyEnum(String code, String name, Boolean enable) {
        this.code = code;
        this.name = name;
        this.enable = enable;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public Boolean getEnable() {
        return enable;
    }

    public static String getNameByCode(String code) {
        if (code == null) {
            return null;
        }
        for (MaterialReplaceStrategyEnum stateEnum : MaterialReplaceStrategyEnum.values()) {
            if (stateEnum.code.equals(code)) {
                return stateEnum.name;
            }
        }
        return null;
    }

    public static String getCodeByName(String name) {
        if (StringUtils.isBlank(name)) {
            return null;
        }
        for (MaterialReplaceStrategyEnum stateEnum : MaterialReplaceStrategyEnum.values()) {
            if (stateEnum.name.equals(name)) {
                return stateEnum.code;
            }
        }
        return null;
    }

}
