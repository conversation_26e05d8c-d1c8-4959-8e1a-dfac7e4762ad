package com.yelink.dfs.constant.node;


/**
 * @description: 单据状态
 * @author: shuang
 * @time: 2022/2/15
 */
public enum DeliverStatusEnum {

    /**
     * 任何单据的状态转换
     */
    CREATE_STATUS("创建", 1),
    EFFECT_STATUS("生效", 2),
    COMPLETE_STATUS("完成", 3),
    CLOSE_STATUS("关闭", 4),
    CANCEL_STATUS("取消", 5);

    private String name;
    private Integer value;

    DeliverStatusEnum(String name, Integer value) {
        this.name = name;
        this.value = value;
    }

    public String getName() {
        return name;
    }

    public Integer getValue() {
        return value;
    }

    public static String getNameByCode(int value) {
        for (DeliverStatusEnum stateEnum : DeliverStatusEnum.values()) {
            if (value == stateEnum.value) {
                return stateEnum.name;
            }
        }
        return null;
    }

}
