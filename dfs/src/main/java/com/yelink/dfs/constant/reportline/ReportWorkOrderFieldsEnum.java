package com.yelink.dfs.constant.reportline;


/**
 * 报工工单字段显示枚举
 *
 * <AUTHOR>
 * @date 2022-08-23
 */
public enum ReportWorkOrderFieldsEnum {
// 报工工单字段显示枚举
    WORK_ORDER_NAME("workOrderName", "生产工单名称"),
    START_DATE("startDate", "计划开始时间"),
    PROCEDURE_NAME("procedureName", "工序名称"),
    ACTUAL_START_DATE("actualStartDate", "实际开始时间"),
    ACTUAL_END_DATE("actualEndDate", "实际完成时间"),
    ASSIGNMENT_STATE_NAME("assignmentStateName", "派工状态"),
    WORK_CENTER_NAME("workCenterName", "工作中心"),
    //基本生产单元 = 产线 设备 班组
    BASIC_PRODUCTION_UNIT("basicProductionUnit", "基本生产单元"),
    PRIORITY("priority", "优先级"),
    IS_COUNT_NOW("isCountNow", "自动计数"),
    // 生产订单
    PRODUCTION_ORDER_NUMBER("productOrderNumber", "生产订单号"),
    RELATED_ORDER_NUM("relatedOrderNum", "上级生产订单号"),
    RELATED_ROOT_ORDER_NUM("relatedRootOrderNum", "根生产订单号"),

    // 销售订单号
    CUSTOMER_NAME("customerName", "客户名称"),
    CUSTOMER_CODE("customerCode", "客户代码"),
    SALE_ORDER_NUMBER("saleOrderNumber", "销售订单号"),
    REQUIRE_GOODS_DATE("requireGoodsDate", "要货日期"),
    ORDER_DATE("orderDate", "下单日期"),
    SALES_QUANTITY("salesQuantity", "销售数量"),
    FINISH_COUNT("finishCount", "完成数量"),
    UNQUALIFIED("unqualified", "不良数量"),

    ;
    private final String code;
    private final String name;

    ReportWorkOrderFieldsEnum(String type, String name) {
        this.code = type;
        this.name = name;
    }

    public String getType() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static String getNameByType(String type) {
        if (type == null) {
            return null;
        }
        for (ReportWorkOrderFieldsEnum en : ReportWorkOrderFieldsEnum.values()) {
            if (en.code.equals(type)) {
                return en.name;
            }
        }
        return null;
    }

    public static String getTypeByName(String name) {
        if (name == null) {
            return null;
        }
        for (ReportWorkOrderFieldsEnum en : ReportWorkOrderFieldsEnum.values()) {
            if (name.equals(en.name)) {
                return en.code;
            }
        }
        return null;
    }
}

