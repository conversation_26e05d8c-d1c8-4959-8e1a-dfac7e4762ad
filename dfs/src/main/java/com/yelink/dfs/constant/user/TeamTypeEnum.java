package com.yelink.dfs.constant.user;

/**
 * <AUTHOR>
 * @Date 2022/5/31 14:55
 */
public enum TeamTypeEnum {

    /**
     * 班组类型
     */
    WAREHOUSE(1, "仓管"),
//    PRODUCTION_MANAGEMENT(2, "生管"),
    PRODUCTION(3, "生产");
//    QUALITY_INSPECTION(4, "质检"),
//    REPAIR(5, "维修"),
//    EQUIPMENT_MAINTENANCE(6, "设备维护");

    private Integer code;
    private String name;


    TeamTypeEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static String getNameByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (TeamTypeEnum teamTypeEnum : TeamTypeEnum.values()) {
            if (code.intValue() == teamTypeEnum.code.intValue()) {
                return teamTypeEnum.getName();
            }
        }
        return null;
    }

    public static Integer getCodeByName(String name) {
        if(name == null){
            return null;
        }
        for (TeamTypeEnum teamTypeEnum : TeamTypeEnum.values()) {
            if (name.equals(teamTypeEnum.name)) {
                return teamTypeEnum.code;
            }
        }
        return null;
    }
}
