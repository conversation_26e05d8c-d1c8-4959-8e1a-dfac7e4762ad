package com.yelink.dfs.constant.furnace;

/**
 * <AUTHOR>
 * @Date 2021/8/3 19:31
 */
public enum LaboratoryStateEnum {
    /**
     * 0-未确认 1-已确认
     */
    NOT_AFFIRM(0, "未确认"),
    AFFIRM(1, "已确认");

    private int code;
    private String name;

    LaboratoryStateEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }

    public int getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static String getNameByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (LaboratoryStateEnum stateEnum : LaboratoryStateEnum.values()) {
            if (stateEnum.code == code) {
                return stateEnum.name;
            }
        }
        return null;
    }
}
