package com.yelink.dfs.constant.device;

/**
 * <AUTHOR>
 * @description 设备管理
 * @Date 2021/3/3
 */
public enum DeviceTypeEnum {

    /**
     * 设备类型
     */
    PRODUCTION(1001, "生产设备"),
    KEY(1002, "关键设备"),
    ENVIRONMENT(1003, "环境设备");

    private int code;
    private String type;

    DeviceTypeEnum(int code, String type) {
        this.code = code;
        this.type = type;
    }

    public int getCode() {
        return code;
    }

    public String getType() {
        return type;
    }

    public static String getNameByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (DeviceTypeEnum stateEnum : DeviceTypeEnum.values()) {
            if (stateEnum.code == code) {
                return stateEnum.type;
            }
        }
        return null;
    }

    public static Integer getCodeByName(String name) {
        for (DeviceTypeEnum stateEnum : DeviceTypeEnum.values()) {
            if (name.equals(stateEnum.type)) {
                return stateEnum.code;
            }
        }
        return null;
    }

}
