package com.yelink.dfs.constant.alarm;

import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @Date 2021/11/3 10:08
 */
public enum AlarmClassifyEnum {

    /**
     * 告警分类
     */
    EQUIPMENT_ALARM("901", "设备告警"),
    PRODUCTION_ALARM("902", "生产告警"),
    SECURITY_ALARM("903", "安全告警");

    private String code;
    private String title;


    AlarmClassifyEnum(String code, String title) {
        this.code = code;
        this.title = title;
    }

    public String getCode() {
        return code;
    }

    public String getTitle() {
        return title;
    }

    public static String getNameByCode(String code) {
        if (StringUtils.isNotBlank(code)) {
            for (AlarmClassifyEnum classifyEnum : AlarmClassifyEnum.values()) {
                if (classifyEnum.code.equals(code)) {
                    return classifyEnum.title;
                }
            }
        }
        return null;
    }

}
