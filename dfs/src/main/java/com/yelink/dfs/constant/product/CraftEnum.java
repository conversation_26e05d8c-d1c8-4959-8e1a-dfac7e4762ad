package com.yelink.dfs.constant.product;

/**
 * <AUTHOR>
 * @Date 2021/3/2 15:35
 */
public enum CraftEnum {

    /**
     * 类型
     */
    PUBLISH(1, "发布"),
    NOT_PUBLISH(0, "未发布");

    private Integer code;
    private String name;


    CraftEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static String getNameByType(Integer code) {
        if (code == null) {
            return null;
        }
        for (CraftEnum modelEnum : CraftEnum.values()) {
            if (code.equals(modelEnum.getCode())) {
                return modelEnum.getName();
            }
        }
        return null;
    }
}
