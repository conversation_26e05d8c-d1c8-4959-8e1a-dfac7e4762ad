package com.yelink.dfs.constant.product;


import com.baomidou.mybatisplus.annotation.EnumValue;
import org.apache.commons.lang3.StringUtils;

/**
 * @Description: 物料检验控制方式的枚举
 * @Author: zhuangwq
 * @Date: 2023/09/04
 */
public enum MaterialInspectMethodEnum {

    /**
     * 物料检验控制方式的枚举
     */
    PRODUCT_ORDER_BY_ONE_OF_WORK_WORK_ORDER_INVESTMENT("productOrderByOneOfWorkWorkOrderInvestment", "投产-生产订单下其中一个生产工单需要校验"),
    PRODUCT_ORDER_BY_ONE_OF_WORK_ORDERS_REPORT("productOrderByOneOfWorkOrdersReport", "报工-生产订单下其中一个生产工单需要校验"),
    PRODUCT_ORDER_BY_ONE_OF_WORK_ORDERS_FINISH("productOrderByOneOfWorkOrdersFinish", "完工-生产订单下其中一个生产工单需要校验"),
    WORK_ORDER_INVESTMENT("workOrderInvestment", "投产-生产工单需要校验"),
    WORK_ORDER_REPORT("workOrderReport", "报工-生产工单需要校验"),
    WORK_ORDER_FINISH("workOrderFinish", "完工-生产工单需要校验"),
    PURCHASE_INPUT_INCOMING_INSPECTION("purchaseInputIncomingInspection", "采购入库-来料检验必须合格"),
    ;

    @EnumValue
    private String code;
    private String name;

    MaterialInspectMethodEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static String getNameByCode(String code) {
        if (code == null) {
            return null;
        }
        for (MaterialInspectMethodEnum stateEnum : MaterialInspectMethodEnum.values()) {
            if (stateEnum.code.equals(code)) {
                return stateEnum.name;
            }
        }
        return null;
    }

    public static String getCodeByName(String name) {
        if (StringUtils.isBlank(name)) {
            return null;
        }
        for (MaterialInspectMethodEnum stateEnum : MaterialInspectMethodEnum.values()) {
            if (stateEnum.name.equals(name)) {
                return stateEnum.code;
            }
        }
        return null;
    }

}
