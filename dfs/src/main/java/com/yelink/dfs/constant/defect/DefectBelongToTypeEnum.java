package com.yelink.dfs.constant.defect;


/**
 * @Description: 质检方案所属类型枚举（）
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2020/12/5
 */
public enum DefectBelongToTypeEnum {

    /**
     * 所属工艺工序、工位、工序定义
     */
    STATION("station", "工位"),
    PROCEDURE("procedure", "工序"),
    CRAFT_PROCEDURE("craftProcedure", "工艺工序"),
        ;

    private String code;
    private String name;

    DefectBelongToTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static String getNameByCode(String code) {
        if (code == null) {
            return null;
        }
        for (DefectBelongToTypeEnum stateEnum : DefectBelongToTypeEnum.values()) {
            if (stateEnum.code.equals(code)) {
                return stateEnum.name;
            }
        }
        return null;
    }

    public static String getCodeByName(String name) {
        for (DefectBelongToTypeEnum stateEnum : DefectBelongToTypeEnum.values()) {
            if (name.equals(stateEnum.name)) {
                return stateEnum.code;
            }
        }
        return null;
    }
}
