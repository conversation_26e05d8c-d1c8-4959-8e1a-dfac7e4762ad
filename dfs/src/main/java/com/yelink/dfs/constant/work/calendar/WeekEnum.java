package com.yelink.dfs.constant.work.calendar;


/**
 * @Description: 日期类型枚举
 * @Author: yejiarun
 * @Date: 2020/12/5
 */
public enum WeekEnum {

    /**
     * 类型编码及描述
     */
    MONDAY(1, "星期一"),
    TUESDAY(2, "星期二"),
    WEDNESDAY(3, "星期三"),
    THURSDAY(4, "星期四"),
    FRIDAY(5, "星期五"),
    SATURDAY(6, "星期六"),
    SUNDAY(7, "星期天");


    private Integer code;
    private String name;

    WeekEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }


    public static String getNameByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (WeekEnum stateEnum : WeekEnum.values()) {
            if (stateEnum.code.equals(code)) {
                return stateEnum.name;
            }
        }
        return null;
    }

    public static Integer getCodeByName(String name) {
        for (WeekEnum stateEnum : WeekEnum.values()) {
            if (name.equals(stateEnum.name)) {
                return stateEnum.code;
            }
        }
        return null;
    }
}
