package com.yelink.dfs.constant.order;

/**
 * @description: 常量类
 * @author: zengfu
 * @create: 2020-12-04 15:22
 **/
public class Constant {


    /**
     * 生产订单层级数-第一层
     */
    public static final Integer BOM_LAYER_FIRST_NUM = 1;
    /**
     * BOM第15层，用于防止bom嵌套循环
     */
    public static final Integer BOM_LAYER_MAX_NUM = 15;

    //辅助计划相关字段
    public static final String PLANNED_SHIP_DATE = "plannedShipDate";
    public static final String DELIVERY_DEADLINE = "deliveryDeadline";
    public static final String ORDER_DATE = "orderDate";
    public static final String SALE_ORDER_CHANGE_TIME = "saleOrderChangeTime";
    public static final String DELIVERY_DATE = "deliveryDate";
    public static final String MATERIAL_CODE = "materialCode";
    //此变量还被工单排程用到
    public static final String START_DATE = "startDate";
    //此变量还被工单排程用到
    public static final String END_DATE = "endDate";
    public static final String CREATE_TIME = "createTime";
    public static final String UPDATE_TIME = "updateTime";
    public static final String SYNCHRONIZATION_TIME = "synchronizationTime";
    public static final String APPROVAL_TIME = "approvalTime";
    public static final String ARRANGEMENT_DATE = "arrangementDate";
    public static final String PLANNED_DELIVERY_DATE = "plannedDeliveryDate";
    public static final String RANGE = "range";
    public static final String LT_EQUAL = "ltEqual";
    public static final String GT_EQUAL = "gtEqual";
    public static final String SCHEDULED = "scheduled";
    public static final String TO_BE_SCHEDULED = "toBeScheduled";
    public static final String NULL = "null";
    public static final String CURRENT = "current";
    public static final String SIZE = "size";
    public static final String IS_CONDITION_QUERY = "isConditionQuery";
    public static final String IS_LAST_SORT = "isLastSort";
    public static final String EXTEND_MATERIAL_LIST = "extendMaterialList";

    // 工单排程相关字段
    public static final String CUSTOMER_NAME = "customerName";
    public static final String PACKING_METHOD = "packingMethod";
    public static final String ACTUAL_START_DATE = "actualStartDate";
    public static final String ACTUAL_END_DATE = "actualEndDate";

    // 模块类型（用于EXCEL控件保存列配置）
    /**
     * 工单排程-生产工单
     */
    public static final String SCHEDULE_WORK_ORDER = "scheduleWorkOrder";
    /**
     * 辅助计划
     */
    public static final String AUXILIARY_PLAN = "auxiliaryPlan";


}
