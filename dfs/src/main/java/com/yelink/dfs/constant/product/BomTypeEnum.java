package com.yelink.dfs.constant.product;

import com.baomidou.mybatisplus.annotation.EnumValue;

/**
 * @Description: BOM类型枚举
 * @Author: zhuangwq
 * @Date: 2021/04/08
 */
public enum BomTypeEnum {

    /**
     * 物料/bom类型
     * 1001-成品 1002-半成品 1003-原料
     */
    END_PRODUCT(1001, "成品"),
    SEMI_PRODUCT(1002, "半成品"),
    RAW_MATERIAL(1003, "原料");

    @EnumValue
    private int code;
    private String name;

    BomTypeEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }

    public int getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

}
