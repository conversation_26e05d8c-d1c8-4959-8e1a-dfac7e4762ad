package com.yelink.dfs.constant.product;

/**
 * @description: 工艺工序物料的数据来源枚举
 * @author: zhuang
 * @time: 2021/5/28
 */
public enum CraftProcedureMaterialDataSourceEnum {

    /**
     * 工艺类型枚举
     */
    BOM_MATERIAL("bomMaterial", "BOM物料"),
    MATERIAL("material", "物料");

    private String code;
    private String name;

    CraftProcedureMaterialDataSourceEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static String getNameByCode(String code) {
        for (CraftProcedureMaterialDataSourceEnum craftTypeEnum : CraftProcedureMaterialDataSourceEnum.values()) {
            if (craftTypeEnum.code.equals(code)) {
                return craftTypeEnum.name;
            }
        }
        return null;
    }

    public static String getCodeByName(String name) {
        for (CraftProcedureMaterialDataSourceEnum craftTypeEnum : CraftProcedureMaterialDataSourceEnum.values()) {
            if (craftTypeEnum.name.equals(name)) {
                return craftTypeEnum.code;
            }
        }
        return null;
    }

}
