package com.yelink.dfs.constant.product;

import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @Description 工序检验项工序互检 枚举
 * @Date 2022/4/19 14:40
 */
public enum InspectItemTypeEnum {

    /**
     * 自检、互检、专检
     */
    SELF_INSPECTION("selfInspection","自检"),
    MUTUAL_INSPECTION("mutualInspection","互检"),
    SPECIAL_INSPECTION("specialInspection","专检");


    /**
     * code
     */
    private String code;
    /**
     * name
     */
    private String name;

    InspectItemTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }


    public static String getNameByCode(String code) {
        if (code == null) {
            return null;
        }
        for (InspectItemTypeEnum stateEnum : InspectItemTypeEnum.values()) {
            if (stateEnum.code.equals(code)) {
                return stateEnum.name;
            }
        }
        return null;
    }

    public static String getCodeByName(String name) {
        if (StringUtils.isBlank(name)) {
            return null;
        }
        for (InspectItemTypeEnum stateEnum : InspectItemTypeEnum.values()) {
            if (name.equals(stateEnum.name)) {
                return stateEnum.code;
            }
        }
        return null;
    }

}
