package com.yelink.dfs.constant.code;

/**
 * 条码解析规则类型
 *
 * <AUTHOR>
 * @Date 2022/4/1 9:50
 */
public enum CodeResolverTypeEnum {
    /**
     * 类型编码及描述
     * 0-未打印 1-已打印
     */
    PRODUCT_FLOW_CODE("productFlowCode", "生产流水码"),
    MATERIAL_CODE("materialCode", "物料码"),
    FINISHED_PRODUCT_CODE("finishedProductCode", "成品码");

    private String code;
    private String name;

    CodeResolverTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static String getNameByCode(String code) {
        if (code == null) {
            return null;
        }
        for (CodeResolverTypeEnum stateEnum : CodeResolverTypeEnum.values()) {
            if (stateEnum.code.equals(code)) {
                return stateEnum.name;
            }
        }
        return null;
    }

    public static String getCodeByName(String name) {
        for (CodeResolverTypeEnum stateEnum : CodeResolverTypeEnum.values()) {
            if (name.equals(stateEnum.name)) {
                return stateEnum.code;
            }
        }
        return null;
    }
}
