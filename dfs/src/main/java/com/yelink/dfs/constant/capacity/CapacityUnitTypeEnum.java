package com.yelink.dfs.constant.capacity;


/**
 * @Description: 产能单位类型
 * @Author: zheng<PERSON>
 * @Date: 2020/12/5
 */
public enum CapacityUnitTypeEnum {

    /**
     * 状态编码及描述
     */
    FIRST(1, "第一单位"),
    SECOND(2,"第二单位"),
    ;

    private Integer code;
    private String name;

    CapacityUnitTypeEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static String getNameByCode(Integer code) {
        for (CapacityUnitTypeEnum stateEnum : CapacityUnitTypeEnum.values()) {
            if (stateEnum.code.equals(code)) {
                return stateEnum.name;
            }
        }
        return null;
    }

    public static Integer getCodeByName(String name) {
        for (CapacityUnitTypeEnum stateEnum : CapacityUnitTypeEnum.values()) {
            if (name.equals(stateEnum.name)) {
                return stateEnum.code;
            }
        }
        return null;
    }
}
