package com.yelink.dfs.constant.product;


import com.baomidou.mybatisplus.annotation.EnumValue;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * @Description: 工艺工序检验控制方式的枚举
 * @Author: zhuangwq
 * @Date: 2023/09/04
 */
public enum CraftProcedureInspectMethodEnum {

    /**
     * 工艺工序检验控制方式的枚举
     */
    WORK_ORDER_INVESTMENT("workOrderInvestment", "投产"),
    WORK_ORDER_REPORT("workOrderReport", "报工"),
    WORK_ORDER_FINISH("workOrderFinish", "完工"),
    ;

    @EnumValue
    private String code;
    private String name;

    CraftProcedureInspectMethodEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static String getNameByCode(String code) {
        if (code == null) {
            return null;
        }
        for (CraftProcedureInspectMethodEnum stateEnum : CraftProcedureInspectMethodEnum.values()) {
            if (stateEnum.code.equals(code)) {
                return stateEnum.name;
            }
        }
        return null;
    }

    public static String getCodeByName(String name) {
        if (StringUtils.isBlank(name)) {
            return null;
        }
        for (CraftProcedureInspectMethodEnum stateEnum : CraftProcedureInspectMethodEnum.values()) {
            if (stateEnum.name.equals(name)) {
                return stateEnum.code;
            }
        }
        return null;
    }

    public static List<String> getNames() {
        ArrayList<String> names = new ArrayList<>();
        for (CraftProcedureInspectMethodEnum methodEnum : CraftProcedureInspectMethodEnum.values()) {
            names.add(methodEnum.name);
        }
        return names;
    }

}
