package com.yelink.dfs.constant.product;

import com.baomidou.mybatisplus.annotation.EnumValue;

/**
 * @Description: 工艺状态枚举
 * @Author: zhuangwq
 * @Date: 2021/05/24
 */
public enum CraftStateEnum {

    /**
     * 1-创建 4-生效 5-停用 6-废弃
     */
    CREATE(1, "创建"),
    RELEASED(4, "生效"),
    STOP_USING(5, "停用"),
    ABANDONED(6, "废弃"),
    ;

    @EnumValue
    private int code;
    private String name;

    CraftStateEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }

    public int getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static String getNameByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (CraftStateEnum stateEnum : CraftStateEnum.values()) {
            if (stateEnum.code == code) {
                return stateEnum.name;
            }
        }
        return null;
    }

    public static Integer getCodeByName(String name) {
        for (CraftStateEnum stateEnum : CraftStateEnum.values()) {
            if (stateEnum.name.equals(name)) {
                return stateEnum.code;
            }
        }
        return null;
    }
    public static CraftStateEnum getByName(String name) {
        for (CraftStateEnum stateEnum : CraftStateEnum.values()) {
            if (stateEnum.name.equals(name)) {
                return stateEnum;
            }
        }
        return null;
    }

}
