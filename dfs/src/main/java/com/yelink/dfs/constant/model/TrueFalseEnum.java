package com.yelink.dfs.constant.model;


/**
 * @Description: 工位状态枚举
 * @Author: zhengfu
 * @Date: 2020/12/5
 */
public enum TrueFalseEnum {

    /**
     * 状态编码及描述
     */
    FALSE(0, "否"),
    TRUE(1, "是");

    private int code;
    private String name;

    TrueFalseEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }

    public int getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static String getNameByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (TrueFalseEnum trueFalseEnum : TrueFalseEnum.values()) {
            if (trueFalseEnum.code == code) {
                return trueFalseEnum.name;
            }
        }
        return null;
    }

    public static int getCodeByName(String name) {
        for (TrueFalseEnum trueFalseEnum : TrueFalseEnum.values()) {
            if (name.equals(trueFalseEnum.name)) {
                return trueFalseEnum.code;
            }
        }
        return -999;
    }
}
