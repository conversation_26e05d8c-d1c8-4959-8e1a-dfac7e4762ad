package com.yelink.dfs.constant.user;


import com.baomidou.mybatisplus.annotation.EnumValue;

/**
 * @Description: 权限类型枚举
 * @Author: zhuangwq
 * @Date: 2021/04/08
 */
public enum PermissionTypeEnum {

    /**
     * 0-目录   1-菜单   2-按钮   3-三级菜单
     */
    CATALOGUE(0, "目录"),
    MENU(1, "菜单"),
    BUTTON(2, "按钮"),
    THIRD_MENU(3, "三级菜单");

    @EnumValue
    private Integer code;
    private String name;

    PermissionTypeEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static String getNameByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (PermissionTypeEnum stateEnum : PermissionTypeEnum.values()) {
            if (stateEnum.code.equals(code)) {
                return stateEnum.name;
            }
        }
        return null;
    }

}
