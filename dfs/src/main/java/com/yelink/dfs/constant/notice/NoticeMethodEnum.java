package com.yelink.dfs.constant.notice;


/**
 * @Description: 消息通知方式
 * @Author:
 * @Date: 2020/12/5
 */
public enum NoticeMethodEnum {

    /**
     * 消息通知方式
     */
    JINGZHI(1, "精制消息"),
    LOCATION_MACHINE(2, "工位机消息"),
    ;

    private Integer code;
    private String name;

    NoticeMethodEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static String getNameByCode(Integer code) {
        for (NoticeMethodEnum stateEnum : NoticeMethodEnum.values()) {
            if (stateEnum.code.equals(code)) {
                return stateEnum.name;
            }
        }
        return null;
    }

    public static NoticeMethodEnum getByCode(Integer code) {
        for (NoticeMethodEnum stateEnum : NoticeMethodEnum.values()) {
            if (stateEnum.code.equals(code)) {
                return stateEnum;
            }
        }
        return null;
    }
}
