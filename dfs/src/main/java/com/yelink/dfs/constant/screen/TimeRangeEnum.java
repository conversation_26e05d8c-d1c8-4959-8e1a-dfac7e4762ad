package com.yelink.dfs.constant.screen;

import com.yelink.dfscommon.constant.ResponseException;
import com.yelink.dfscommon.utils.DateUtil;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2022/5/24 17:22
 */
public enum TimeRangeEnum {
    /**
     * 时间段枚举,日，周，月
     */
    DAY("day", "日"),
    WEEK("week", "周"),
    MONTH("month", "月");

    final private String code;
    final private String text;

    TimeRangeEnum(String code, String text) {
        this.code = code;
        this.text = text;
    }

    public String getCode() {
        return code;
    }

    public String getText() {
        return text;
    }

    public static TimeRangeEnum getByCode(String code) {
        for (TimeRangeEnum timeRangeEnum : TimeRangeEnum.values()) {
            if (code.equals(timeRangeEnum.code)) {
                return timeRangeEnum;
            }
        }
        return null;
    }
    public static String getTextByCode(String code) {
        for (TimeRangeEnum timeRangeEnum : TimeRangeEnum.values()) {
            if (code.equals(timeRangeEnum.code)) {
                return timeRangeEnum.getText();
            }
        }
        return null;
    }

    /**
     * 本日，本周，本月
     */
    public static Date getStartTime(String timeRange) {
        Date startDate;
        if (StringUtils.isEmpty(timeRange) || TimeRangeEnum.DAY.getCode().equals(timeRange)) {
            // 当日 yyyy-MM-dd 00:00:00 - yyyy-MM-dd 23:59:59
            startDate = DateUtil.formatToDate(new Date(), DateUtil.DATETIME_FORMAT_ZERO);
        } else if (TimeRangeEnum.WEEK.getCode().equals(timeRange)) {
            // 本周 yyyy-MM-dd 00:00:00
            startDate = DateUtil.formatToDate(DateUtil.getMondayOfWeek(new Date()), DateUtil.DATETIME_FORMAT_ZERO);
        } else if (TimeRangeEnum.MONTH.getCode().equals(timeRange)) {
            // 本月 yyyy-MM-01 00:00:00 - now()
            startDate = DateUtil.formatToDate(new Date(), DateUtil.DATETIME_FORMAT_MOUTH_ZERO);
        } else {
            throw new ResponseException("不支持的时间段");
        }
        return startDate;
    }

    public static Date getEndTime(String timeRange) {
        Date startDate;
        Date endDate;
        if (StringUtils.isEmpty(timeRange) || TimeRangeEnum.DAY.getCode().equals(timeRange)) {
            // 当日 yyyy-MM-dd 00:00:00 - yyyy-MM-dd 23:59:59
            startDate = DateUtil.formatToDate(new Date(), DateUtil.DATETIME_FORMAT_ZERO);
            endDate = DateUtil.addSec(DateUtil.addDay(startDate, 1), -1);
        } else if (TimeRangeEnum.WEEK.getCode().equals(timeRange)) {
            // 本周 yyyy-MM-dd 00:00:00
            endDate = new Date();
        } else if (TimeRangeEnum.MONTH.getCode().equals(timeRange)) {
            // 本月 yyyy-MM-01 00:00:00 - now()
            endDate = new Date();
        } else {
            throw new ResponseException("不支持的时间段");
        }
        return endDate;
    }
}
