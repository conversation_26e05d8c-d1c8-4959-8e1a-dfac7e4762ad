package com.yelink.dfs.constant.product;

/**
 * @description: 数据类型
 * @author: zhuang
 * @time: 2021/5/28
 */
public enum MaterialAttributeDataTypeEnum {

    /**
     * 数据类型
     */
    NUMBER("number", "数值型"),
    STRING("string", "字符型"),
    BOOL("bool", "布尔型"),
    DATE("date", "日期型"),
    FILE("file", "文件型"),
    ;

    private String code;
    private String name;

    MaterialAttributeDataTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static String getNameByCode(String code) {
        for (MaterialAttributeDataTypeEnum craftTypeEnum : MaterialAttributeDataTypeEnum.values()) {
            if (craftTypeEnum.code.equals(code)) {
                return craftTypeEnum.name;
            }
        }
        return null;
    }

    public static String getCodeByName(String name) {
        for (MaterialAttributeDataTypeEnum craftTypeEnum : MaterialAttributeDataTypeEnum.values()) {
            if (craftTypeEnum.name.equals(name)) {
                return craftTypeEnum.code;
            }
        }
        return null;
    }

}
