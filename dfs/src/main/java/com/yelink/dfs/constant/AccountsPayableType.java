package com.yelink.dfs.constant;

/**
 * 条款类型枚举
 *
 * <AUTHOR>
 * @Date 2021/4/7 10:34
 */
public enum AccountsPayableType {
    /**
     * 类型
     */
    RECEIVABLES( "receivables", "应付"),
    ACCOUNTS_PAYABLE( "accountsPayable", "应收");

    private String type;
    private String name;


    AccountsPayableType( String type, String name) {
        this.type = type;
        this.name = name;
    }


    public String getName() {
        return name;
    }

    public String getType() {
        return type;
    }

    public static String getNameByCode(String type) {
        if (type == null) {
            return null;
        }
        for (AccountsPayableType termsType : AccountsPayableType.values()) {
            if (type.equals(termsType.type)) {
                return termsType.getName();
            }
        }
        return null;
    }
}
