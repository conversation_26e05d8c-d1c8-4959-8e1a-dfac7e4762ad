package com.yelink.dfs.constant.station;


/**
 * @Description: 质检工位直通率枚举
 * @Author: yejiarun
 * @Date: 2021/07/21
 */
public enum PassStandardEnum {

    /**
     * 质检工位直通率枚举
     */
    PASS_STANDARD("passStandard", "直通率"),
    DATE_STANDARD("dateStandard", "每日直通率标准"),
    MATERIAL_STANDARD("materialStandard", "各型号直通率标准");

    private String code;
    private String name;

    PassStandardEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static String getNameByCode(String code) {
        if (code == null) {
            return null;
        }
        for (PassStandardEnum workOrderStateEnum : PassStandardEnum.values()) {
            if (workOrderStateEnum.code.equals(code)) {
                return workOrderStateEnum.name;
            }
        }
        return null;
    }

    public static String getCodeByName(String name) {
        for (PassStandardEnum workOrderStateEnum : PassStandardEnum.values()) {
            if (name.equals(workOrderStateEnum.name)) {
                return workOrderStateEnum.code;
            }
        }
        return null;
    }
}
