package com.yelink.dfs.constant.reportline;


/**
 * 产线数量报工表的上报方式枚举
 * <AUTHOR>
 * @date 2022-08-23
 */
public enum ReportLineTypeEnum {

    REPORT("report", "手动报工"),
    AUTO("auto", "设备采集");

    private String type;
    private String name;

    ReportLineTypeEnum(String type, String name) {
        this.type = type;
        this.name = name;
    }

    public String getType() {
        return type;
    }

    public String getName() {
        return name;
    }

    public static String getNameByType(String type) {
        if (type == null) {
            return null;
        }
        for (ReportLineTypeEnum en : ReportLineTypeEnum.values()) {
            if (en.type.equals(type)) {
                return en.name;
            }
        }
        return null;
    }

    public static String getTypeByName(String name) {
        if(name == null){
            return null;
        }
        for (ReportLineTypeEnum en : ReportLineTypeEnum.values()) {
            if (name.equals(en.name)) {
                return en.type;
            }
        }
        return null;
    }
}

