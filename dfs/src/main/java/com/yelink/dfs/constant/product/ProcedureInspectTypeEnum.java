package com.yelink.dfs.constant.product;

import com.yelink.dfs.constant.Constant;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description 工序检测项类型
 * @Date 2023/2/1 14:40
 */
public enum ProcedureInspectTypeEnum {

    /**
     * 工序检测项类型
     * selfInspection--自检、mutualInspection--互检、specialInspection--专检
     */
    SELF_INSPECTION("selfInspection", "自检"),
    MUTUAL_INSPECTION("mutualInspection", "互检"),
    SPECIAL_INSPECTION("specialInspection", "专检"),

    ;

    /**
     * 字段名
     */
    private String code;

    /**
     * 字段名称
     */
    private String name;

    ProcedureInspectTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }


    public static String getNameByCode(String code) {
        if (StringUtils.isBlank(code)) {
            return null;
        }
        Map<String, String> map = Arrays.stream(ProcedureInspectTypeEnum.values())
                .collect(Collectors.toMap(ProcedureInspectTypeEnum::getCode, ProcedureInspectTypeEnum::getName));
        String[] inspectTypeList = code.split(Constant.SEP);
        StringBuilder sb = new StringBuilder();
        for (String inspectTypeCode : inspectTypeList) {
            String inspectTypeName = map.get(inspectTypeCode);
            sb.append(inspectTypeName).append(Constant.SEP);
        }
        sb.deleteCharAt(sb.length() - 1);
        return sb.toString();
    }


}
