package com.yelink.dfs.constant.code;

import com.yelink.dfscommon.entity.CommonEnumInterface;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @Date 2022/4/1 9:50
 */
public enum ScanCodeRuleDataFormatEnum implements CommonEnumInterface {

    STRING("string", "字符串"),
    JSON("json", "json"),
    ;

    private String code;
    private String name;

    ScanCodeRuleDataFormatEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static String getNameByCode(String code) {
        if (StringUtils.isBlank(code)) {
            return null;
        }
        for (ScanCodeRuleDataFormatEnum stateEnum : ScanCodeRuleDataFormatEnum.values()) {
            if (stateEnum.code.equals(code)) {
                return stateEnum.name;
            }
        }
        return null;
    }

    public static String getCodeByName(String name) {
        for (ScanCodeRuleDataFormatEnum stateEnum : ScanCodeRuleDataFormatEnum.values()) {
            if (name.equals(stateEnum.name)) {
                return stateEnum.code;
            }
        }
        return null;
    }
}
