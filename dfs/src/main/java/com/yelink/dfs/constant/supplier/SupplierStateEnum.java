package com.yelink.dfs.constant.supplier;

import com.baomidou.mybatisplus.annotation.EnumValue;

/**
 * @Description: 供应商状态
 * @Author: zhuangwq
 * @Date: 2021/04/28
 */
public enum SupplierStateEnum {

    /**
     * 供应商状态
     * 1-创建 2-生效 3-停用
     */
    CREATE(1, "创建"),
    TAKE_EFFECT(2, "生效"),
    DEACTIVATE(3, "停用");

    @EnumValue
    private int code;
    private String name;

    SupplierStateEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }

    public int getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static String getNameByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (SupplierStateEnum stateEnum : SupplierStateEnum.values()) {
            if (stateEnum.code == code) {
                return stateEnum.name;
            }
        }
        return null;
    }

}
