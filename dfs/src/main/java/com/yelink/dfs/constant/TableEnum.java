package com.yelink.dfs.constant;

/**
 * <AUTHOR>
 * @Date 2021/4/26
 */
public enum TableEnum {

    /**
     * 表名
     */
    DFS_OPERATION_LOG(1, "dfs_operation_log"),
    DFS_REPORT_COUNT(2, "dfs_report_count"),
    DFS_REPORT_FAC_STATE(3, "dfs_report_fac_state"),
    DFS_SENSOR_RECORD(4, "dfs_sensor_record"),
    DFS_ALARM_RECORD(5, "dfs_alarm_record"),
    DFS_NOTICE_RECORD(6, "dfs_notice_record");
    private Integer id;
    private String tableName;

    TableEnum(Integer id, String tableName) {
        this.id = id;
        this.tableName = tableName;
    }

    public Integer getId() {
        return id;
    }

    public String getTableName() {
        return tableName;
    }

}
