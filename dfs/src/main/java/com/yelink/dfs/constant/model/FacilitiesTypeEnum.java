package com.yelink.dfs.constant.model;


import com.yelink.dfscommon.constant.RespCodeEnum;
import com.yelink.dfscommon.constant.ResponseException;

/**
 * @Description: 工位状态枚举
 * @Author: zhengfu
 * @Date: 2020/12/5
 */
public enum FacilitiesTypeEnum {

    /**
     * 设施编码及描述
     * 生产设施类型
     */
    CUBICLE("cubicle", 100201, "工位"),
    STATION("station", 100202, "工作站"),
    PRODUCE("produce", 100203, "生产设备"),
    SECURITY("security", 100204, "安全设施"),
    SENSOR("sensor", 100205, "传感器默认设施"),
    PRODUCTION_LINE("productionLine", 100206, "产线默认设施");

    /**
     * 接口名称定义
     */
    private String interfaceType;
    private int code;
    private String name;

    FacilitiesTypeEnum(String interfaceType, int code, String name) {
        this.interfaceType = interfaceType;
        this.code = code;
        this.name = name;
    }


    public int getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public String getInterfaceType() {
        return interfaceType;
    }

    public static FacilitiesTypeEnum getByInterfaceType(String interfaceType) {
        for (FacilitiesTypeEnum facilitiesTypeEnum : FacilitiesTypeEnum.values()) {
            if (facilitiesTypeEnum.getInterfaceType().equals(interfaceType)) {
                return facilitiesTypeEnum;
            }
        }
        throw new ResponseException(RespCodeEnum.INCORRECT_PARAMETER);
    }


    public static boolean isOnProducionLine(Integer code) {
        if (CUBICLE.code == code || STATION.code == code || PRODUCE.code == code) {
            return true;
        }
        return false;
    }

    public static boolean isOnGrid(Integer code) {

        if (SECURITY.code == code) {
            return true;
        }
        return false;
    }
}
