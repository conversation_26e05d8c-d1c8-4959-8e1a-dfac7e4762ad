package com.yelink.dfs.constant.user;

/**
 * @Description: 角色枚举
 * @Author: zhuangwq
 * @Date: 2021/06/7
 */
public enum RoleEnum {

    /**
     * 角色枚举
     */
    ADMIN(1, "管理员"),
    FACTORY(2, "厂长"),
    LINE(3, "线长"),
    TEAM(4, "组长"),
    BUILD(5, "维装"),
    SYSTEM_ADMIN(6, "系统运维"),

    /**
     * 3.12 新增的
     */
    SALE(null, "销售"),
    PURCHASE(null, "采购"),
    DESIGN(null, "设计"),
    PRODUCT(null, "生产"),
    WAREHOUSE(null, "仓库"),
    INSPECTION(null, "质检"),
    FINANCE(null, "财务"),
    OPERATE(null, "经营"),
    DEVICE(null, "设备"),
    SUPPLIER(null, "供应商"),
    ;

    private final Integer code;
    private final String name;

    RoleEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }


}
