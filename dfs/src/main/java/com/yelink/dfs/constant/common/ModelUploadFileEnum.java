package com.yelink.dfs.constant.common;

/**
 * 上传文件模块类型
 * <AUTHOR>
 * @date 2022-09-29
 */
public enum ModelUploadFileEnum {
    /**
     * 上传文件所属模块
     */
    WORK_ORDER(1, "生产工单"),
    PURCHASE_ORDER(2, "采购订单"),
    PRODUCT_ORDER(3, "生产订单"),
    REQUEST_ORDER(4, "采购需求单"),
    WORK_ORDER_TRACE(5, "工单追溯"),
    PURCHASE_RECEIPT_ORDER(6, "采购收货单"),
    FLOW_CODE_RECORD(7, "单品追溯"),

    WORK_ORDER_EXPORT(8, "生产工单_导出"),
    DAILY_PRODUCTION_REPORT(9, "生产日报_导出"),
    WEEK_PRODUCTION_REPORT(10, "生产周报_导出"),
    MONTH_PRODUCTION_REPORT(11, "生产月报_导出"),

    WORK_ORDER_REPORT_RECORD_REPORT(12, "生产工单报工记录_导出"),
    ATTENDANCE_RECORD_REPORT(13, "打卡记录_导出"),
    WORK_ORDER_DETAIL(14, "生产工单明细表_导出"),
    PRODUCT_REPORT_DETAIL(15, "生产报工明细表_导出"),
    CODE_RECORD_DETAIL(16, "单品过站明细表_导出"),
    WORK_ORDER_EFFICIENCY_DETAIL(17, "生产工单绩效明细表_导出"),

    DEFECT_STATEMENT_MATERIAL(19, "质量报表按物料汇总_导出"),
    DEFECT_STATEMENT_MATERIAL_LINE(20, "质量报表按产品产线汇总_导出"),
    DEFECT_STATEMENT_LINE_DEFECT(21, "质量报表按产线不良项汇总_导出"),
    DEFECT_STATEMENT_MATERIAL_LINE_DEFECT(22, "质量报表按产品产线不良项汇总_导出"),

    PRODUCT_ORDER_PROCEDURE_PROGRESS(23, "工序进度明细表_导出"),
    WORK_ORDER_INPUT_OUTPUT_DETAIL(24, "投入产出明细表_导出"),

    MANAGE_REPORT(25, "报表管理模板"),
    MATERIAL_REPORT(26, "物料_导出"),
    BOM_SIMPLE_REPORT(27, "BOM基本信息_导出"),
    BOM_DETAIL_REPORT(28, "BOM详细信息_导出"),
    CRAFT_SIMPLE_REPORT(29, "工艺基础信息_导出"),

    SALARY_DETAIL_REPORT(30, "工资看板_工资明细_导出"),
    SALARY_SUM_USER_REPORT(31, "工资汇总_人员维度_导出"),
    SALARY_SUM_WORK_CENTER_REPORT(32, "工资汇总_工作中心维度_导出"),

    MANAGE_REPORT_PRODUCT_REPORT(33, "报表管理_生产报表默认模板(应该只有一条记录)"),
    MANAGE_REPORT_QUALITY_STATEMENT(34, "报表管理_质量报表默认模板(应该只有一条记录)"),
    MANAGE_REPORT_SALE_ORDER_DETAIL(35,"销售订单_当前订单明细默认模板"),
    MANAGE_REPORT_SALE_ORDER_PROGRESS(36,"销售订单_订单进度默认模板"),
    MANAGE_REPORT_SALE_ORDER_QUALITY_TRACE(37,"销售订单_订单质量追踪默认模板"),
    MANAGE_REPORT_PRODUCT_STATISTICS_TOTAL_UNIT_MATERIAL_STATEMENT(38, "报表管理_生产看板_生产工单汇总_按生产单元和产品_报表默认模板"),
    MANAGE_REPORT_PRODUCT_STATISTICS_TOTAL_MATERIAL_STATEMENT(39, "报表管理_生产看板_生产工单汇总_按产品_报表默认模板"),
    MANAGE_REPORT_PRODUCT_STATISTICS_TOTAL_CENTER_MATERIAL_STATEMENT(40, "报表管理_生产看板_生产工单汇总_按工作中心和产品_报表默认模板"),
    MANAGE_REPORT_SALE_STATISTICS_TOTAL_MATERIAL_STATEMENT(41, "报表管理_销售看板_销售汇总_按产品_报表默认模板"),
    MANAGE_REPORT_SALE_STATISTICS_TOTAL_SALESMAN_CUSTOMER_STATEMENT(42, "报表管理_销售看板_销售汇总_按销售员和客户_报表默认模板"),
    MANAGE_REPORT_SALE_STATISTICS_TOTAL_CUSTOMER_STATEMENT(43, "报表管理_销售看板_销售汇总_按客户_报表默认模板"),
    QUALITY_INSPECTION_REWORK(44, "质检返工导出"),
    WORK_ORDER_RESUME(45, "生产工单状态履历_导出"),
    PROCEDURE_INSPECT_RECORD(46, "工序检验记录_导出"),
    WORK_ORDER_OVERVIEW(47, "生产工单状态总览_导出"),
    DEFECT_DISTRIBUTION(48, "不良项分布_导出"),
    DEVICE_RUN_RECORD(49, "设备运行记录_导出"),

    MANAGE_REPORT_PROCESSING_PRODUCTION_SCHEDULE(50, "报表管理_离散加工-生产日表默认模板(应该只有一条记录)"),
    MANAGE_REPORT_PROCESSING_QUALITY_STATISTICS(51, "报表管理_离散加工-质量统计默认模板(应该只有一条记录)"),
    DEVICE_EXPORT(52, "设备台账_导出"),
    WORK_ORDER_MATERIAL_LIST_EXPORT(53, "生产工单用料清单_导出"),
    WORK_SCHEDULE_PRODUCT_LIST(54, "工单排程列表_导出"),
    PRODUCT_FLOW_CODE_RECORD_EXPORT(55, "过站记录_导出"),
    FEED_RECORD_EXPORT(56, "上料记录_导出"),
    PRODUCT_FLOW_CODE_EXPORT(57, "物料条码_导出"),
    CRAFT_TEMP_SIMPLE_REPORT(58, "默认工艺基础信息_导出"),
    ;

    private Integer code;
    private String name;

    ModelUploadFileEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public Integer getCode() {
        return code;
    }

    public String getType() {
        return name;
    }

    public static String getNameByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (ModelUploadFileEnum en : ModelUploadFileEnum.values()) {
            if (en.code.equals(code)) {
                return en.name;
            }
        }
        return null;
    }

    public static Integer getCodeByName(String name) {
        if(name == null){
            return null;
        }
        for (ModelUploadFileEnum en : ModelUploadFileEnum.values()) {
            if (name.equals(en.name)) {
                return en.code;
            }
        }
        return null;
    }

}
