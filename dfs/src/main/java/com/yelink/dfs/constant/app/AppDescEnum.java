package com.yelink.dfs.constant.app;

/**
 * <AUTHOR>
 * @Date 2022/4/14 11:36
 */
public enum AppDescEnum {

    /**
     * 目前1.12.1版本先对工位机进行说明，以便区分小程序的类型
     */
    LOCATION_MACHINE("locationMachine", "工位机");

    private String code;
    private String name;

    AppDescEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static String getNameByCode(String code) {
        if (code == null) {
            return null;
        }
        for (AppDescEnum stateEnum : AppDescEnum.values()) {
            if (stateEnum.code.equals(code)) {
                return stateEnum.name;
            }
        }
        return null;
    }

    public static String getCodeByName(String name) {
        for (AppDescEnum stateEnum : AppDescEnum.values()) {
            if (name.equals(stateEnum.name)) {
                return stateEnum.code;
            }
        }
        return null;
    }
}
