package com.yelink.dfs.constant.device;


/**
 * @Description: 工位状态枚举
 * @Author: z<PERSON><PERSON><PERSON>
 * @Date: 2020/12/5
 */
public enum DevicesStateEnum {

    /**
     * 状态编码及描述
     * 工位、生产设施状态 0-停机 1-运行中 2-待机 3-故障 4-运行到位
     */
    STOP(0, "停机"),
    RUNNING(1, "运行中"),
    SUSPEND(2, "待机"),
    FAULT(3, "故障"),
    OPERATION_IN_PLACE(4, "运行到位"),

    ;

    private int code;
    private String name;

    DevicesStateEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }

    public int getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static String getNameByCode(Integer code) {
        for (DevicesStateEnum stateEnum : DevicesStateEnum.values()) {
            if (code == stateEnum.code) {
                return stateEnum.name;
            }
        }
        return null;
    }

    public static int getCodeByName(String name) {
        for (DevicesStateEnum stateEnum : DevicesStateEnum.values()) {
            if (name.equals(stateEnum.name)) {
                return stateEnum.code;
            }
        }
        return -999;
    }
}
