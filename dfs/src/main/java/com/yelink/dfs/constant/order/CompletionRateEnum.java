package com.yelink.dfs.constant.order;

/**
 * <AUTHOR>
 * @Date 2022/4/21 18:35
 */
public enum CompletionRateEnum {

    /**
     * 完成率枚举
     */
    LESS_THAN_OR_EQUAL_TO_ZERO_PERCENT("lessThanOrEqualToZeroPercent", "<=0%"),
    BETWEEN_ZERO_AND_FIFTY_PERCENT("betweenZeroAndFiftyPercent", "(0%,50%]"),
    BETWEEN_FIFTY_AND_ONE_HUNDRED_PERCENT("betweenFiftyAndOneHundredPercent", "(50%,100)"),
    GREATER_THAN_OR_EQUAL_TO_ONE_HUNDRED_PERCENT("greaterThanOrEqualToOneHundredPercent", ">=100%"),
    ;

    private String type;
    private String typeName;


    CompletionRateEnum(String type, String typeName) {
        this.type = type;
        this.typeName = typeName;
    }

    public String getType() {
        return type;
    }

    public String getTypeName() {
        return typeName;
    }


    public static CompletionRateEnum getByType(String type) {
        for (CompletionRateEnum rateEnum : CompletionRateEnum.values()) {
            if (type.equals(rateEnum.getType())) {
                return rateEnum;
            }
        }
        return null;
    }

    public static CompletionRateEnum getNameByType(String typeName) {
        for (CompletionRateEnum rateEnum : CompletionRateEnum.values()) {
            if (rateEnum.getTypeName().equals(typeName)) {
                return rateEnum;
            }
        }
        return null;
    }
}
