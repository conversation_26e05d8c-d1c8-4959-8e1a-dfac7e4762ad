package com.yelink.dfs.constant.product;

import com.baomidou.mybatisplus.annotation.EnumValue;

/**
 * @Description: 物料出库规则
 * @Author: zhuang
 * @Date: 2021/09/23
 */
public enum DeliveryRulesEnum {

    /**
     * 1-顺序出库-入库日期（默认）   0-无
     */
    CREATE(1, "顺序出库"),
    REVIEWED(0, "无"),
    ;

    @EnumValue
    private int code;
    private String name;

    DeliveryRulesEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }

    public int getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static String getNameByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (DeliveryRulesEnum stateEnum : DeliveryRulesEnum.values()) {
            if (stateEnum.code == code) {
                return stateEnum.name;
            }
        }
        return null;
    }

}
