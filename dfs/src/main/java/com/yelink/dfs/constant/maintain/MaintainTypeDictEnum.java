package com.yelink.dfs.constant.maintain;


/**
 * @Description: 维修类型枚举
 * @Author: zheng<PERSON>
 * @Date: 2020/12/5
 */
public enum MaintainTypeDictEnum {

    /**
     * 维修类型字典枚举
     */
    FAULT_TYPE("faultType", "故障类型"),
    MAINTAIN_TYPE("maintainType", "维修类型"),
    MAINTAIN_SCHEME_TYPE("maintainSchemeType", "维修方案的维修类型"),
    MAINTAIN_RELATED_TYPE("maintainRelatedType", "维修方案关联类型"),
    
    ;

    private String code;
    private String name;

    MaintainTypeDictEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static String getNameByCode(String code) {
        if (code == null) {
            return null;
        }
        for (MaintainTypeDictEnum stateEnum : MaintainTypeDictEnum.values()) {
            if (stateEnum.code.equals(code)) {
                return stateEnum.name;
            }
        }
        return null;
    }

    public static String getCodeByName(String name) {
        for (MaintainTypeDictEnum stateEnum : MaintainTypeDictEnum.values()) {
            if (name.equals(stateEnum.name)) {
                return stateEnum.code;
            }
        }
        return null;
    }
}
