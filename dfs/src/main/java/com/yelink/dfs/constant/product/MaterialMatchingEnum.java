package com.yelink.dfs.constant.product;

/**
 * 一般工位机,上料防错 -物料检查
 * <AUTHOR>
 * @Date 2023/2/17 18:12
 */
public enum MaterialMatchingEnum {
    /**
     * 物料匹配、bom物料匹配、用料清单匹配、工序物料匹配
     */
    NO("no", "无"),

    MATERIAL("material", "物料匹配"),
    BOM_MATERIAL("bomMaterial", "bom物料匹配"),
    MATERIAL_LIST("materialList", "用料清单匹配"),
    PROCEDURE_MATERIAL("procedureMaterial", "工序物料匹配"),
    ;


    private final String type;
    private final String name;

    MaterialMatchingEnum(String type, String name) {
        this.type = type;
        this.name = name;
    }

    public String getType() {
        return type;
    }

    public String getName() {
        return name;
    }

    public static MaterialMatchingEnum getByType(String type) {
        for(MaterialMatchingEnum e: MaterialMatchingEnum.values()) {
            if(e.type.equals(type)) {
                return e;
            }
        }
        return null;
    }

    public static String getNameByType(String type) {
        if (type == null) {
            return null;
        }
        for (MaterialMatchingEnum matchingEnum : MaterialMatchingEnum.values()) {
            if (matchingEnum.type.equals(type)) {
                return matchingEnum.getName();
            }
        }
        return null;
    }

    public static String getTypeByName(String name) {
        if(name == null){
            return null;
        }
        for (MaterialMatchingEnum matchingEnum : MaterialMatchingEnum.values()) {
            if (matchingEnum.name.equals(name)) {
                return matchingEnum.type;
            }
        }
        return null;
    }

    public static MaterialMatchingEnum[] getMaterialCheckEnum() {
        return new MaterialMatchingEnum[]{
                NO,
                MATERIAL,
                BOM_MATERIAL,
                PROCEDURE_MATERIAL,
        };
    }

}
