package com.yelink.dfs.constant.sensor;


import java.util.ArrayList;
import java.util.List;

/**
 * @description: 带计数功能的采集器类型及对应的指标
 * @author: zengfu
 * @create: 2021-02-21 09:54
 **/
public enum CountSensorEnum {
    /**
     * 计数器、COG、FOG设备
     */
    COUNTER_SENSOR(SensorTypeCodeEnum.COUNTER_SENSOR, SensorParamEnum.COUNTER_COUNT),
    AUTOMATIC_COG_EQUIPMENT(SensorTypeCodeEnum.AUTOMATIC_COG_EQUIPMENT, SensorParamEnum.NUMBER),
    AUTOMATIC_FOG_EQUIPMENT(SensorTypeCodeEnum.AUTOMATIC_FOG_EQUIPMENT, SensorParamEnum.NUMBER),
    AUTOMATIC_DISPENSING_MACHINE(SensorTypeCodeEnum.AUTOMATIC_DISPENSING_MACHINE, SensorParamEnum.NUMBER),
    ;


    private SensorTypeCodeEnum typeCodeEnum;
    private SensorParamEnum paramEnum;

    public SensorTypeCodeEnum getTypeCodeEnum() {
        return typeCodeEnum;
    }

    public SensorParamEnum getParamEnum() {
        return paramEnum;
    }

    CountSensorEnum(SensorTypeCodeEnum typeCodeEnum, SensorParamEnum paramEnum) {
        this.typeCodeEnum = typeCodeEnum;
        this.paramEnum = paramEnum;
    }

    /**
     * 根据采集器类型获取枚举
     *
     * @param typeCode
     * @return
     */
    public static CountSensorEnum getByTypeCode(int typeCode) {
        for (CountSensorEnum countSensorEnum : CountSensorEnum.values()) {
            if (countSensorEnum.getTypeCodeEnum().getCode() == typeCode) {
                return countSensorEnum;
            }
        }
        return null;
    }

    /**
     * 判断该指标是否为计数指标
     *
     * @param ename
     * @return
     */
    public static boolean isCountTargetName(String ename) {
        for (CountSensorEnum countSensorEnum : CountSensorEnum.values()) {
            if (countSensorEnum.getParamEnum().getEname().equals(ename)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 获取所有带有计数功能的采集器
     */
    public static Integer[] getTypeCode() {
        List<Integer> list = new ArrayList<>();
        for (CountSensorEnum countSensorEnum : CountSensorEnum.values()) {
            list.add(countSensorEnum.getTypeCodeEnum().getCode());
        }
        return list.toArray(new Integer[list.size()]);

    }

}






