package com.yelink.dfs.constant.code;

/**
 * 流水码记录上报类型
 *
 * <AUTHOR>
 * @Date 2022/4/1 9:50
 */
public enum CodeReportAttributeEnum {
    /**
     * 类型编码及描述
     * 0-未打印 1-已打印
     */
    AUTO_REPORT("autoReport", "自动上报"),
    MANUAL_REPORT("manualReport", "手动上报");

    private String code;
    private String name;

    CodeReportAttributeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static String getNameByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (CodeReportAttributeEnum stateEnum : CodeReportAttributeEnum.values()) {
            if (stateEnum.code.equals(code)) {
                return stateEnum.name;
            }
        }
        return null;
    }

    public static String getCodeByName(String name) {
        for (CodeReportAttributeEnum stateEnum : CodeReportAttributeEnum.values()) {
            if (name.equals(stateEnum.name)) {
                return stateEnum.code;
            }
        }
        return null;
    }
}
