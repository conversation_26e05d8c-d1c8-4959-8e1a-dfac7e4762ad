package com.yelink.dfs.constant;

/**
 * @description: 告警码和告警类型
 * @author: shuang
 * @time: 2020/6/22
 */
public enum DictTypeEnum {


    /**
     * 应用对应的告警类型
     */
    ALARM_TYPE("alarmType"),
    ALARM_LEVEL("alarmLevel"),
    ALARM_ADVICE("alarmAdvice"),

    /**
     * 缺陷种类名称
     */
    ABNORMAL_NAME("abnormalName"),
    /**
     * 工位状态
     */
    FACILITIES_STATE("facilitiesState"),
    /**
     * 同步数据表
     */
    SYN_TABLE("synTable"),
    /**
     * 单位
     */
    UNIT("unit"),
    /**
     * 物料类型
     */
    MATERIAL_TYPE("materialType"),
    /**
     * 产流水码从第几个字符开始截取工单
     */
    BAR_CODE_SCANNER_BEGIN("barCodeScannerBegin"),
    /**
     * 产流水码从第几个字符结束截取工单
     */
    BAR_CODE_SCANNER_END("barCodeScannerEnd"),
    /**
     * 生产基本单元
     */
    PRODUCTION_BASIC_UNIT("productionBasicUnit"),

    /**
     * 工艺工装类型
     */
    PROCESS_ASSEMBLY("processAssembly"),


    /**
     * 供应商类型配置
     */
    SUPPLIER_TYPE("supplierType"),

    /**
     * 算法配置(提前量配置)
     */
    AHEAD_CONFIG("aheadConfig"),

    BOM_RAW_MATERIAL_CONFIG("bomRawMaterialConfig"),

    /**
     * 键值对数据
     */
    KEY_VALUE("keyValue"),
    /**
     * 缓存开关
     */
    CACHE_SWITCH("cacheSwitch"),
    ;

    private String type;


    DictTypeEnum(String type) {
        this.type = type;
    }

    public String getType() {
        return type;
    }

}
