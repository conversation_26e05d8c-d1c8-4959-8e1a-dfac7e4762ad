package com.yelink.dfs.pushdown.writeback;

import com.yelink.dfs.entity.order.TakeOutApplicationEntity;
import com.yelink.dfs.service.order.TakeOutApplicationService;
import com.yelink.dfs.service.order.WorkOrderService;
import com.yelink.dfscommon.constant.dfs.pushdown.PushDownConfigPath;
import com.yelink.dfscommon.constant.dfs.pushdown.PushDownOrderStateEnum;
import com.yelink.dfscommon.dto.pushdown.writeback.StateChangeDTO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * 下推处理
 * <AUTHOR>
 */
@Component
public class WriteBackWorkOrder2TakeOutApplication extends AbstractPushDownWriteBack<TakeOutApplicationEntity> {


    @Resource
    @Lazy
    protected TakeOutApplicationService takeOutApplicationService;
    @Resource
    @Lazy
    private WorkOrderService workOrderService;

    @Override
    public String configPath() {
        return PushDownConfigPath.WORK_ORDER_2_PRODUCT_TAKEOUT_APPLICATION;
    }


    @Override
    public List<TakeOutApplicationEntity> getAllTargetOrder(TakeOutApplicationEntity order) {
        if(StringUtils.isEmpty(order.getWorkOrderNumber())) {
            return Collections.emptyList();
        }
        return takeOutApplicationService.lambdaQuery().eq(TakeOutApplicationEntity::getWorkOrderNumber, order.getWorkOrderNumber()).list();
    }

    @Override
    public void changeSourceState(TakeOutApplicationEntity order, PushDownOrderStateEnum sourceState) {
        if(StringUtils.isEmpty(order.getWorkOrderNumber())) {
            return ;
        }
        workOrderService.writeBackOrderState(StateChangeDTO.builder().sourceNumber(order.getWorkOrderNumber()).changeState(sourceState).build());
    }
}
