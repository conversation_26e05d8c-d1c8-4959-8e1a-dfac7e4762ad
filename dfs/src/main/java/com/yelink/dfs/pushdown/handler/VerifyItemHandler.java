package com.yelink.dfs.pushdown.handler;

import com.yelink.dfs.service.common.config.OrderPushDownItemVerifyService;
import com.yelink.dfscommon.constant.ResponseException;
import com.yelink.dfscommon.constant.dfs.pushdown.PushDownItemTypeEnum;
import com.yelink.dfscommon.dto.pushdown.item.AbstractPushDownItemAddDTO;
import com.yelink.dfscommon.dto.pushdown.item.AbstractPushDownItemEditDTO;
import com.yelink.dfscommon.dto.pushdown.item.PushDownVerifyItemAddDTO;
import com.yelink.dfscommon.dto.pushdown.item.PushDownVerifyItemEditDTO;
import com.yelink.dfscommon.dto.pushdown.item.PushDownVerifyItemInnerDTO;
import com.yelink.dfscommon.entity.dfs.OrderPushDownItemEntity;
import com.yelink.dfscommon.entity.dfs.OrderPushDownItemVerifyEntity;
import com.yelink.dfscommon.utils.JacksonUtil;
import com.yelink.dfscommon.vo.pushdown.AbstractOrderPushDownItemVO;
import com.yelink.dfscommon.vo.pushdown.OrderPushDownVerifyItemVO;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 校验项
 * <AUTHOR>
 */
@Component
public class VerifyItemHandler implements ItemHandler{

    @Resource
    private OrderPushDownItemVerifyService verifyService;

    @Override
    public PushDownItemTypeEnum itemEnum() {
        return PushDownItemTypeEnum.VERIFY;
    }
    @Override
    public OrderPushDownVerifyItemVO parseVo(OrderPushDownItemEntity item) {
        // 外层的
        OrderPushDownVerifyItemVO vo = JacksonUtil.convertObject(item, OrderPushDownVerifyItemVO.class);
        // 内层
        OrderPushDownItemVerifyEntity verify = verifyService.getByItemId(item.getId());
        vo.setVerify(JacksonUtil.convertObject(verify, PushDownVerifyItemInnerDTO.class));
        return vo;
    }

    @Override
    public List<AbstractOrderPushDownItemVO> parseVoList(List<OrderPushDownItemEntity> items) {
        List<OrderPushDownVerifyItemVO> vos = JacksonUtil.convertArray(items, OrderPushDownVerifyItemVO.class);
        Map<Integer, OrderPushDownVerifyItemVO> idMap = vos.stream().collect(Collectors.toMap(OrderPushDownVerifyItemVO::getId, Function.identity()));
        List<OrderPushDownItemVerifyEntity> verifies = verifyService.getByItemIds(idMap.keySet());
        verifies.forEach(verify -> {
            OrderPushDownVerifyItemVO vo = idMap.get(verify.getItemId());
            vo.setVerify(JacksonUtil.convertObject(verify, PushDownVerifyItemInnerDTO.class));
        });
        return new ArrayList<>(vos);
    }

    @Override
    public void add(AbstractPushDownItemAddDTO originDto) {
        PushDownVerifyItemAddDTO dto = (PushDownVerifyItemAddDTO) originDto;
        OrderPushDownItemVerifyEntity verify = OrderPushDownItemVerifyEntity.builder()
                .itemId(dto.getItem().getId())
                .url(dto.getVerify().getUrl())
                .isAccess(dto.getVerify().getIsAccess())
                .tips(dto.getVerify().getTips())
                .build();
        verifyService.save(verify);
    }


    @Override
    public void edit(AbstractPushDownItemEditDTO originDto) {
        PushDownVerifyItemEditDTO dto = (PushDownVerifyItemEditDTO) originDto;
        PushDownVerifyItemInnerDTO verifyDto = dto.getVerify();
        OrderPushDownItemVerifyEntity verify = verifyService.getByItemId(dto.getId());
        if(verify == null) {
            throw new ResponseException("出现脏数据, 请先删除");
        }
        verify.setUrl(verifyDto.getUrl());
        verify.setIsAccess(verifyDto.getIsAccess());
        verify.setTips(verifyDto.getTips());
        verifyService.updateById(verify);
    }



    @Override
    public void delete(OrderPushDownItemEntity item) {
        verifyService.lambdaUpdate().eq(OrderPushDownItemVerifyEntity::getItemId, item.getId()).remove();
    }
}
