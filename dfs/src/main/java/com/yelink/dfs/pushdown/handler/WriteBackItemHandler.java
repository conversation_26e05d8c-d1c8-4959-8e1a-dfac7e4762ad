package com.yelink.dfs.pushdown.handler;

import com.yelink.dfs.service.common.config.OrderPushDownItemWriteBackService;
import com.yelink.dfs.service.common.config.OrderPushDownWriteBackStrategyService;
import com.yelink.dfscommon.constant.ResponseException;
import com.yelink.dfscommon.constant.dfs.pushdown.PushDownItemTypeEnum;
import com.yelink.dfscommon.constant.dfs.pushdown.PushDownWriteBackTypeEnum;
import com.yelink.dfscommon.dto.pushdown.item.AbstractPushDownItemAddDTO;
import com.yelink.dfscommon.dto.pushdown.item.AbstractPushDownItemEditDTO;
import com.yelink.dfscommon.dto.pushdown.item.PushDownWriteBackItemAddDTO;
import com.yelink.dfscommon.dto.pushdown.item.PushDownWriteBackItemEditDTO;
import com.yelink.dfscommon.dto.pushdown.item.PushDownWriteBackItemInnerDTO;
import com.yelink.dfscommon.entity.dfs.OrderPushDownItemEntity;
import com.yelink.dfscommon.entity.dfs.OrderPushDownItemWriteBackEntity;
import com.yelink.dfscommon.entity.dfs.OrderPushDownWriteBackStrategyEntity;
import com.yelink.dfscommon.utils.JacksonUtil;
import com.yelink.dfscommon.vo.pushdown.OrderPushDownWriteBackItemVO;
import jodd.util.StringUtil;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Optional;

/**
 * 回写项
 * <AUTHOR>
 */
@Component
public class WriteBackItemHandler implements ItemHandler{

    @Resource
    private OrderPushDownItemWriteBackService itemWriteBackService;
    @Resource
    private OrderPushDownWriteBackStrategyService writeBackStrategyService;
    @Override
    public PushDownItemTypeEnum itemEnum() {
        return PushDownItemTypeEnum.WRITE_BACK;
    }
    @Override
    public OrderPushDownWriteBackItemVO parseVo(OrderPushDownItemEntity item) {
        // 外层的
        OrderPushDownWriteBackItemVO vo = JacksonUtil.convertObject(item, OrderPushDownWriteBackItemVO.class);
        // 内层的
        OrderPushDownItemWriteBackEntity writeBack = itemWriteBackService.getByItemId(item.getId());
        vo.setWriteBack(JacksonUtil.convertObject(Optional.ofNullable(writeBack).orElse(new OrderPushDownItemWriteBackEntity()), PushDownWriteBackItemInnerDTO.class));
        return vo;
    }

    @Override
    public void add(AbstractPushDownItemAddDTO originDto) {
        PushDownWriteBackItemAddDTO dto = (PushDownWriteBackItemAddDTO) originDto;
        // 上级保存的信息
        OrderPushDownItemEntity item = dto.getItem();
        // 前端传递的
        PushDownWriteBackItemInnerDTO writeBackDto = dto.getWriteBack();

        OrderPushDownItemWriteBackEntity writeBack = OrderPushDownItemWriteBackEntity.builder()
                .type(writeBackDto.getType())
                .itemId(item.getId())
                .build();
        initData(writeBackDto, writeBack);
        itemWriteBackService.save(writeBack);
    }


    @Override
    public void edit(AbstractPushDownItemEditDTO originDto) {
        PushDownWriteBackItemEditDTO dto = (PushDownWriteBackItemEditDTO) originDto;
        OrderPushDownItemEntity item = dto.getItem();
        // 前端传递的
        PushDownWriteBackItemInnerDTO writeBackDto = dto.getWriteBack();

        OrderPushDownItemWriteBackEntity oldWriteBack = itemWriteBackService.getByItemId(item.getId());
        if(oldWriteBack == null) {
            throw new ResponseException("出现脏数据, 请先删除");
        }
        OrderPushDownItemWriteBackEntity writeBack = OrderPushDownItemWriteBackEntity.builder()
                .id(oldWriteBack.getId())
                .type(writeBackDto.getType())
                .itemId(item.getId())
                .build();
        initData(writeBackDto, writeBack);
        itemWriteBackService.updateById(writeBack);
    }

    private void initData(PushDownWriteBackItemInnerDTO writeBackDto, OrderPushDownItemWriteBackEntity writeBack) {
        Integer strategyId = writeBackDto.getStrategyId();
        if(writeBackDto.getType() == PushDownWriteBackTypeEnum.STATE) {
            if(writeBackDto.getSourceState() == null || writeBackDto.getTargetState() == null) {
                throw new ResponseException("反写类型为'状态'时, 源状态与目标状态必填");
            }
            if(strategyId == null) {
                throw new ResponseException("反写类型为'状态'时, 策略id必填");
            }
            OrderPushDownWriteBackStrategyEntity strategy = writeBackStrategyService.getById(strategyId);
            if(strategy == null) {
                throw new ResponseException("该反写策略不存在");
            }
            writeBack.setSourceState(writeBackDto.getSourceState());
            writeBack.setTargetState(writeBackDto.getTargetState());
            writeBack.setStrategyId(strategyId);
        }else {
            if(StringUtil.isEmpty(writeBackDto.getUrl())) {
                throw new ResponseException("反写类型为'自定义'时, 接口必填");
            }
            writeBack.setUrl(writeBackDto.getUrl());
        }
    }

    @Override
    public void delete(OrderPushDownItemEntity item) {
        itemWriteBackService.lambdaUpdate().eq(OrderPushDownItemWriteBackEntity::getItemId, item.getId()).remove();
    }
}
