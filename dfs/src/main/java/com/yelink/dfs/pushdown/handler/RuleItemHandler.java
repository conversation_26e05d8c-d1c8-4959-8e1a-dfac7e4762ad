package com.yelink.dfs.pushdown.handler;

import com.yelink.dfs.service.common.config.OrderPushDownConfigService;
import com.yelink.dfs.service.common.config.OrderPushDownConfigValueService;
import com.yelink.dfscommon.constant.dfs.pushdown.PushDownItemTypeEnum;
import com.yelink.dfscommon.dto.pushdown.item.AbstractPushDownItemAddDTO;
import com.yelink.dfscommon.dto.pushdown.item.AbstractPushDownItemEditDTO;
import com.yelink.dfscommon.dto.pushdown.item.PushDownRuleItemAddDTO;
import com.yelink.dfscommon.dto.pushdown.item.PushDownRuleItemEditDTO;
import com.yelink.dfscommon.entity.dfs.OrderPushDownConfigValueEntity;
import com.yelink.dfscommon.entity.dfs.OrderPushDownItemEntity;
import com.yelink.dfscommon.utils.JacksonUtil;
import com.yelink.dfscommon.vo.pushdown.OrderPushDownRuleItemVO;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * 回写项
 *
 * <AUTHOR>
 */
@Component
public class RuleItemHandler implements ItemHandler {

    @Resource
    private OrderPushDownConfigService configService;
    @Resource
    private OrderPushDownConfigValueService configValueService;


    @Override
    public PushDownItemTypeEnum itemEnum() {
        return PushDownItemTypeEnum.RULE;
    }

    @Override
    public OrderPushDownRuleItemVO parseVo(OrderPushDownItemEntity item) {
        List<OrderPushDownConfigValueEntity> valueList = configValueService.getValueList(item.getId());
        OrderPushDownRuleItemVO vo = JacksonUtil.convertObject(item, OrderPushDownRuleItemVO.class);
        vo.setValueList(valueList);
        return vo;
    }

    @Override
    public void add(AbstractPushDownItemAddDTO originDto) {
        PushDownRuleItemAddDTO dto = (PushDownRuleItemAddDTO) originDto;
        OrderPushDownItemEntity item = dto.getItem();

        for (OrderPushDownConfigValueEntity valueEntity : dto.getValueList()) {
            valueEntity.setId(null);
            valueEntity.setItemId(item.getId());
        }
        configValueService.saveBatch(dto.getValueList());

    }

    @Override
    public void edit(AbstractPushDownItemEditDTO originDto) {
        PushDownRuleItemEditDTO dto = (PushDownRuleItemEditDTO) originDto;
        List<OrderPushDownConfigValueEntity> valueList = dto.getValueList();
        // 校验数据必须为json格式
        configService.verifyData(valueList);
        configValueService.updateBatchById(valueList);
    }

    @Override
    public void delete(OrderPushDownItemEntity item) {
        configValueService.lambdaUpdate().eq(OrderPushDownConfigValueEntity::getItemId, item.getId()).remove();
    }
}
