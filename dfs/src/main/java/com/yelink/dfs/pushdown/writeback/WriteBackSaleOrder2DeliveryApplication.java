package com.yelink.dfs.pushdown.writeback;

import com.yelink.dfs.entity.stock.DeliveryApplicationEntity;
import com.yelink.dfs.service.stock.DeliveryApplicationService;
import com.yelink.dfscommon.constant.Constants;
import com.yelink.dfscommon.constant.ams.AmsEventTypeEnum;
import com.yelink.dfscommon.constant.dfs.pushdown.PushDownConfigPath;
import com.yelink.dfscommon.constant.dfs.pushdown.PushDownOrderStateEnum;
import com.yelink.dfscommon.dto.pushdown.writeback.StateChangeDTO;
import com.yelink.dfscommon.provider.MessagePushToKafkaService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * 下推处理
 * <AUTHOR>
 */
@Component
public class WriteBackSaleOrder2DeliveryApplication extends AbstractPushDownWriteBack<DeliveryApplicationEntity> {

    @Resource
    protected MessagePushToKafkaService messagePushToKafkaService;
    @Resource
    @Lazy
    protected DeliveryApplicationService deliveryApplicationService;


    @Override
    public String configPath() {
        return PushDownConfigPath.SALE_ORDER_2_DELIVERY_APPLICATION;
    }

    @Override
    public List<DeliveryApplicationEntity> getAllTargetOrder(DeliveryApplicationEntity order) {
        if(StringUtils.isEmpty(order.getSaleOrderNumber())) {
            return Collections.emptyList();
        }
        return deliveryApplicationService.lambdaQuery().eq(DeliveryApplicationEntity::getSaleOrderNumber, order.getSaleOrderNumber()).list();
    }

    @Override
    public void changeSourceState(DeliveryApplicationEntity order, PushDownOrderStateEnum sourceState) {
        if(StringUtils.isEmpty(order.getSaleOrderNumber())) {
            return ;
        }
        messagePushToKafkaService.pushNewMessage(
                StateChangeDTO.builder().sourceNumber(order.getSaleOrderNumber()).changeState(sourceState).build(),
                Constants.KAFKA_VALUE_CHAIN_TOPIC, AmsEventTypeEnum.WRITE_BACK_CHANGE_SALE_ORDER_STATE
        );
    }
}
