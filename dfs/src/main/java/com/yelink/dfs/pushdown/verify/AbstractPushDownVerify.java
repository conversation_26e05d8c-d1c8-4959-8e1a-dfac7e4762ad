package com.yelink.dfs.pushdown.verify;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.yelink.dfs.service.common.config.OrderPushDownItemService;
import com.yelink.dfs.service.common.config.OrderPushDownItemVerifyService;
import com.yelink.dfscommon.constant.Constant;
import com.yelink.dfscommon.constant.ResponseException;
import com.yelink.dfscommon.constant.dfs.pushdown.PushDownItemTypeEnum;
import com.yelink.dfscommon.constant.dfs.pushdown.PushDownItemVerifyEnum;
import com.yelink.dfscommon.dto.pushdown.verify.PushDownVerifyDTO;
import com.yelink.dfscommon.entity.dfs.OrderPushDownItemEntity;
import com.yelink.dfscommon.entity.dfs.OrderPushDownItemVerifyEntity;
import com.yelink.dfscommon.pojo.ResponseData;
import com.yelink.dfscommon.utils.JacksonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import javax.validation.Validation;
import javax.validation.Validator;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 下推相关处理
 *
 * <AUTHOR>
 */
@Slf4j
public abstract class AbstractPushDownVerify {
    @Resource
    private OrderPushDownItemService orderPushDownItemService;
    @Resource
    private OrderPushDownItemVerifyService orderPushDownItemVerifyService;
    @Resource
    private RestTemplate restTemplate;
    private static final Validator VALIDATOR = Validation.buildDefaultValidatorFactory().getValidator();

    /**
     * 获取配置路径
     *
     * @return 配置路径
     */
    public abstract String configPath();


    /**
     * 处理校验
     */
    public void dealVerify(PushDownVerifyDTO o) {
        if(ObjectUtil.isEmpty(o)) {
            throw new ResponseException("校验参数为空");
        }
        List<String> errorMsg = VALIDATOR.validate(o).stream()
                .map(e -> e.getPropertyPath().toString() + e.getMessage())
                .collect(Collectors.toList());
        if(CollUtil.isNotEmpty(errorMsg)) {
            throw new ResponseException(JacksonUtil.toJSONString(errorMsg));
        }

        List<OrderPushDownItemEntity> allItems = orderPushDownItemService.lambdaQuery()
                .eq(OrderPushDownItemEntity::getEnable, true)
                .eq(OrderPushDownItemEntity::getConfigPath, configPath())
                .eq(OrderPushDownItemEntity::getType, PushDownItemTypeEnum.VERIFY)
                .list();
        if (CollUtil.isEmpty(allItems)) {
            return;
        }
        List<OrderPushDownItemEntity> innerItems = allItems.stream().filter(OrderPushDownItemEntity::getIsInner).collect(Collectors.toList());
        // 1. 内置的
        dealInnerVerify(o, innerItems);

        // 2. 通用的
        List<OrderPushDownItemEntity> outItems = allItems.stream().filter(e -> !e.getIsInner()).collect(Collectors.toList());
        List<Integer> outItemIds = outItems.stream().map(OrderPushDownItemEntity::getId).collect(Collectors.toList());
        // 明细
        List<OrderPushDownItemVerifyEntity> itemVerifies = orderPushDownItemVerifyService.getByItemIds(outItemIds);
        Map<Integer, OrderPushDownItemVerifyEntity> itemVerifyIdMap = itemVerifies.stream().collect(Collectors.toMap(OrderPushDownItemVerifyEntity::getItemId, Function.identity()));

        for (OrderPushDownItemEntity outItem : outItems) {
            OrderPushDownItemVerifyEntity itemVerify = itemVerifyIdMap.get(outItem.getId());
            callUrlVerify(o, outItem, itemVerify);
        }
    }

    public void dealInnerVerify(PushDownVerifyDTO o, List<OrderPushDownItemEntity> innerItems) {
    }


    protected Map<String, OrderPushDownItemEntity> buildItemMap(List<OrderPushDownItemEntity> items) {
        return items.stream().collect(Collectors.toMap(e -> e.getConfigPath()+ Constant.UNDERLINE + e.getCode(), Function.identity()));
    }

    protected OrderPushDownItemEntity getWriteBackItem(Map<String, OrderPushDownItemEntity> map, PushDownItemVerifyEnum e) {
        return map.get(e.getConfigPath() +Constant.UNDERLINE + e.getCode());
    }


    private void callUrlVerify(PushDownVerifyDTO o, OrderPushDownItemEntity outItem, OrderPushDownItemVerifyEntity itemVerify) {
        if (outItem == null || itemVerify == null || StringUtils.isEmpty(itemVerify.getUrl())) {
            return;
        }
        //创建请求对象
        HttpHeaders httpHeaders = new HttpHeaders();
        HttpEntity<Object> request = new HttpEntity<>(o, httpHeaders);
        ResponseEntity<ResponseData> response;
        String title = "下推校验[" + outItem.getName() + "]: ";
        try {
            response = restTemplate.postForEntity(itemVerify.getUrl(), request, ResponseData.class);
        } catch (Exception e) {
            throw new ResponseException(title + "调用接口失败, error:{}" + e.getMessage());
        }
        ResponseData body = response.getBody();
        if (body == null || !HttpStatus.OK.equals(response.getStatusCode())) {
            throw new ResponseException(title + "调用接口异常, msg：" + response);
        }
        if (!ResponseData.SUCCESS_CODE.equals(body.getCode())) {
            throw new ResponseException(title + body.getMessage());
        }
        Boolean flag;
        try {
            flag = JacksonUtil.convertObject(body.getData(), Boolean.class);
        } catch (Exception e) {
            throw new ResponseException(title + "接口回值不规范, error:{}" + e.getMessage());
        }
        if (Boolean.FALSE.equals(flag)) {
            throw new ResponseException(title + itemVerify.getTips());
        }
    }
}
