package com.yelink.dfs.pushdown.writeback;

import com.yelink.dfs.entity.order.WorkOrderEntity;
import com.yelink.dfs.service.common.config.OrderPushDownItemService;
import com.yelink.dfs.service.order.WorkOrderService;
import com.yelink.dfscommon.constant.Constants;
import com.yelink.dfscommon.constant.ams.AmsEventTypeEnum;
import com.yelink.dfscommon.constant.dfs.pushdown.PushDownConfigPath;
import com.yelink.dfscommon.constant.dfs.pushdown.PushDownItemWriteBackEnum;
import com.yelink.dfscommon.constant.dfs.pushdown.PushDownOrderStateEnum;
import com.yelink.dfscommon.dto.pushdown.writeback.StateChangeDTO;
import com.yelink.dfscommon.entity.dfs.OrderPushDownItemEntity;
import com.yelink.dfscommon.provider.MessagePushToKafkaService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * 下推处理
 * 生产订单 -> 工单
 * <AUTHOR>
 */
@Component
public class WriteBackProductOrder2WorkOrder extends AbstractPushDownWriteBack<WorkOrderEntity> {

    @Resource
    private OrderPushDownItemService orderPushDownItemService;
    @Resource
    protected MessagePushToKafkaService messagePushToKafkaService;
    @Resource
    @Lazy
    private WorkOrderService workOrderService;

    @Override
    public String configPath() {
        return PushDownConfigPath.PRODUCT_ORDER_2_WORK_ORDER;
    }

    @Override
    public void dealInnerWriteBack(WorkOrderEntity order, List<OrderPushDownItemEntity> innerItems) {
        Map<String, OrderPushDownItemEntity> itemMap = super.buildItemMap(innerItems);
        //
        OrderPushDownItemEntity autoCompleteItem = super.getWriteBackItem(itemMap, PushDownItemWriteBackEnum.SALE_ORDER_TO_WORK_ORDER_AUTO_COMPLETE);
        this.pushProductOrderStateAutoFinishMsg(order, autoCompleteItem);
    }

    /**
     * 生产订单自动完成消息推送（生效 -> 完成）
     */
    public void pushProductOrderStateAutoFinishMsg(WorkOrderEntity order, OrderPushDownItemEntity innerItem) {
        if (innerItem != null && innerItem.getEnable() && StringUtils.isNotEmpty(order.getProductOrderNumber())) {
            messagePushToKafkaService.pushNewMessage(order.getWorkOrderNumber(), Constants.KAFKA_VALUE_CHAIN_TOPIC, AmsEventTypeEnum.AUTO_CHANGE_PRODUCT_ORDER_STATE);
        }
    }
    public void pushProductOrderStateAutoFinishMsg(WorkOrderEntity order) {
        if(StringUtils.isEmpty(order.getProductOrderNumber())) {
            return ;
        }
        OrderPushDownItemEntity innerItem = orderPushDownItemService.getInnerItem(PushDownItemWriteBackEnum.SALE_ORDER_TO_WORK_ORDER_AUTO_COMPLETE);
        pushProductOrderStateAutoFinishMsg(order, innerItem);
    }

    @Override
    public List<WorkOrderEntity> getAllTargetOrder(WorkOrderEntity order) {
        if(StringUtils.isEmpty(order.getProductOrderNumber())) {
            return Collections.emptyList();
        }
        return workOrderService.lambdaQuery().eq(WorkOrderEntity::getProductOrderNumber, order.getProductOrderNumber()).list();
    }

    @Override
    public void changeSourceState(WorkOrderEntity order, PushDownOrderStateEnum sourceState) {
        if(StringUtils.isEmpty(order.getProductOrderNumber())) {
            return ;
        }
        messagePushToKafkaService.pushNewMessage(
                StateChangeDTO.builder().sourceNumber(order.getProductOrderNumber()).changeState(sourceState).build(),
                Constants.KAFKA_VALUE_CHAIN_TOPIC, AmsEventTypeEnum.WRITE_BACK_CHANGE_PRODUCT_ORDER_STATE
        );
    }

}
