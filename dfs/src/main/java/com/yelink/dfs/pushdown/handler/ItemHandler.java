package com.yelink.dfs.pushdown.handler;

import com.yelink.dfscommon.constant.dfs.pushdown.PushDownItemTypeEnum;
import com.yelink.dfscommon.dto.pushdown.item.AbstractPushDownItemAddDTO;
import com.yelink.dfscommon.dto.pushdown.item.AbstractPushDownItemEditDTO;
import com.yelink.dfscommon.utils.JacksonUtil;
import com.yelink.dfscommon.vo.pushdown.AbstractOrderPushDownItemVO;
import com.yelink.dfscommon.entity.dfs.OrderPushDownItemEntity;

import java.util.List;

/**
 * 回写项接口
 * <AUTHOR>
 */
public interface ItemHandler {

    /**
     * 获取对应的类型枚举
     * @return 对应的类型枚举
     */
    PushDownItemTypeEnum itemEnum();

    /**
     * 转换vo
     * @param item 入参
     * @return 出参
     */
    AbstractOrderPushDownItemVO parseVo(OrderPushDownItemEntity item);

    /**
     * 批量转换vo
     * @param items 入参
     * @return 出参
     */
    default List<AbstractOrderPushDownItemVO> parseVoList(List<OrderPushDownItemEntity> items) {
        return JacksonUtil.convertArray(items, AbstractOrderPushDownItemVO.class);
    }

    /**
     * 添加
     * @param originDto 入参
     */
    void add(AbstractPushDownItemAddDTO originDto);


    /**
     * 编辑
     * @param originDto 入参
     */
    void edit(AbstractPushDownItemEditDTO originDto);

    /**
     * 删除
     * @param item 配置项
     */
    void delete(OrderPushDownItemEntity item);
}
