package com.yelink.dfs.pushdown.writeback;

import com.yelink.dfs.service.order.WorkOrderMaterialListService;
import com.yelink.dfs.service.order.WorkOrderService;
import com.yelink.dfscommon.constant.dfs.MaterialListRelateOrderEnum;
import com.yelink.dfscommon.constant.dfs.pushdown.PushDownConfigPath;
import com.yelink.dfscommon.constant.dfs.pushdown.PushDownOrderStateEnum;
import com.yelink.dfscommon.dto.pushdown.writeback.StateChangeDTO;
import com.yelink.dfscommon.entity.dfs.WorkOrderMaterialListEntity;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * 下推处理
 * <AUTHOR>
 */
@Component
public class WriteBackWorkOrder2WorkMaterialList extends AbstractPushDownWriteBack<WorkOrderMaterialListEntity> {


    @Resource
    @Lazy
    protected WorkOrderMaterialListService workOrderMaterialListService;
    @Resource
    @Lazy
    private WorkOrderService workOrderService;

    @Override
    public String configPath() {
        return PushDownConfigPath.WORK_ORDER_2_WORK_MATERIAL_LIST;
    }


    @Override
    public List<WorkOrderMaterialListEntity> getAllTargetOrder(WorkOrderMaterialListEntity order) {
        if(!MaterialListRelateOrderEnum.WORK_ORDER.getCode().equals(order.getRelateType()) || StringUtils.isEmpty(order.getRelateNumber())) {
            return Collections.emptyList();
        }
        return workOrderMaterialListService.lambdaQuery()
                .eq(WorkOrderMaterialListEntity::getRelateType, MaterialListRelateOrderEnum.WORK_ORDER.getCode())
                .eq(WorkOrderMaterialListEntity::getRelateNumber, order.getRelateNumber())
                .list();
    }

    @Override
    public void changeSourceState(WorkOrderMaterialListEntity order, PushDownOrderStateEnum sourceState) {
        if(!MaterialListRelateOrderEnum.WORK_ORDER.getCode().equals(order.getRelateType()) || StringUtils.isEmpty(order.getRelateNumber())) {
            return ;
        }
        workOrderService.writeBackOrderState(StateChangeDTO.builder().sourceNumber(order.getRelateNumber()).changeState(sourceState).build());
    }
}
