package com.yelink.dfs.pushdown.writeback;

import cn.hutool.core.collection.CollUtil;
import com.yelink.dfs.service.common.config.OrderPushDownItemService;
import com.yelink.dfs.service.common.config.OrderPushDownItemWriteBackService;
import com.yelink.dfs.service.common.config.OrderPushDownWriteBackStrategyService;
import com.yelink.dfscommon.constant.Constant;
import com.yelink.dfscommon.constant.dfs.pushdown.PushDownItemTypeEnum;
import com.yelink.dfscommon.constant.dfs.pushdown.PushDownItemWriteBackEnum;
import com.yelink.dfscommon.constant.dfs.pushdown.PushDownOrderStateEnum;
import com.yelink.dfscommon.dto.pushdown.writeback.PushDownOrder;
import com.yelink.dfscommon.entity.dfs.OrderPushDownItemEntity;
import com.yelink.dfscommon.entity.dfs.OrderPushDownItemWriteBackEntity;
import com.yelink.dfscommon.entity.dfs.OrderPushDownWriteBackStrategyEntity;
import com.yelink.dfscommon.pojo.ResponseData;
import com.yelink.dfscommon.utils.JacksonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.scheduling.annotation.Async;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 下推相关处理
 *
 * @param <T> 下游单据类型
 * <AUTHOR>
 */
@Slf4j
public abstract class AbstractPushDownWriteBack<T extends PushDownOrder> {
    @Resource
    private OrderPushDownItemService orderPushDownItemService;
    @Resource
    private OrderPushDownItemWriteBackService orderPushDownItemWriteBackService;
    @Resource
    private OrderPushDownWriteBackStrategyService orderPushDownWriteBackStrategyService;
    @Resource
    private RestTemplate restTemplate;

    /**
     * 获取配置路径
     *
     * @return 配置路径
     */
    public abstract String configPath();

    /**
     * 处理回写
     */
    @Async
    public void dealWriteBack(T order) {
        log.info("触发回写-埋点-{}, detail: {}", configPath(), order);
        if(order == null) {
            return;
        }
        List<OrderPushDownItemEntity> allItems = orderPushDownItemService.lambdaQuery()
                .eq(OrderPushDownItemEntity::getEnable, true)
                .eq(OrderPushDownItemEntity::getConfigPath, configPath())
                .eq(OrderPushDownItemEntity::getType, PushDownItemTypeEnum.WRITE_BACK)
                .list();
        if (CollUtil.isEmpty(allItems)) {
            return;
        }
        // 1. 内置的
        List<OrderPushDownItemEntity> innerItems = allItems.stream().filter(OrderPushDownItemEntity::getIsInner).collect(Collectors.toList());
        dealInnerWriteBack(order, innerItems);

        // 2. 通用的
        List<OrderPushDownItemEntity> outItems = allItems.stream().filter(e -> !e.getIsInner()).collect(Collectors.toList());
        List<Integer> outItemIds = outItems.stream().map(OrderPushDownItemEntity::getId).collect(Collectors.toList());
        // 明细
        List<OrderPushDownItemWriteBackEntity> itemWriteBacks = orderPushDownItemWriteBackService.getByItemIds(outItemIds);
        // 策略
        List<Integer> strategyIds = itemWriteBacks.stream().map(OrderPushDownItemWriteBackEntity::getStrategyId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        List<OrderPushDownWriteBackStrategyEntity> strategies = CollUtil.isEmpty(strategyIds) ? Collections.emptyList() : orderPushDownWriteBackStrategyService.listByIds(strategyIds);
        Map<Integer, OrderPushDownWriteBackStrategyEntity> strategyIdMap = strategies.stream().collect(Collectors.toMap(OrderPushDownWriteBackStrategyEntity::getId, Function.identity()));

        for (OrderPushDownItemWriteBackEntity itemWriteBack : itemWriteBacks) {
            if (itemWriteBack == null) {
                continue;
            }
            // 状态反写
            switch (itemWriteBack.getType()) {
                case STATE:
                    // 找到策略
                    OrderPushDownWriteBackStrategyEntity strategy = strategyIdMap.get(itemWriteBack.getStrategyId());
                    if (strategy == null) {
                        continue;
                    }
                    // 内置的策略
                    if (strategy.getIsInner()) {
                        this.peek(order);
                        switch (strategy.getInnerCode()) {
                            case ALL_CHANGE:
                                List<T> allTargetOrder = getAllTargetOrder(order);
                                if (CollUtil.isNotEmpty(allTargetOrder) &&
                                        allTargetOrder.stream().allMatch(o -> Objects.equals(o.calPushDownOrderState(), itemWriteBack.getTargetState()))) {
                                    log.info("触发回写-改变状态-{}, state: {}", configPath(), itemWriteBack.getSourceState());
                                    changeSourceState(order, itemWriteBack.getSourceState());
                                }
                                break;
                            case ONE_CHANGE:
                                if (Objects.equals(order.calPushDownOrderState(), itemWriteBack.getTargetState())) {
                                    log.info("触发回写-改变状态-{}, state: {}", configPath(), itemWriteBack.getSourceState());
                                    changeSourceState(order, itemWriteBack.getSourceState());
                                }
                            default:
                        }
                    } else {
                        // 状态策略自定义
                        callUrlWriteBack(order, strategy.getUrl());
                    }


                    break;
                case DIY:
                    callUrlWriteBack(order, itemWriteBack.getUrl());
                default:
            }
        }
    }

    /**
     * 处理内置回写
     */
    public void dealInnerWriteBack(T order, List<OrderPushDownItemEntity> innerItems) {

    }
    public void peek(T order){}
    /**
     * 获取到所有下游单据
     *
     * @param order 下游单据
     * @return 所有下游单据
     */
    public abstract List<T> getAllTargetOrder(T order);
    /**
     * 改变上游单据状态
     *
     * @param order 下游单据
     * @param sourceState 上游单据要变更的状态
     */
    public abstract void changeSourceState(T order, PushDownOrderStateEnum sourceState);


    protected Map<String, OrderPushDownItemEntity> buildItemMap(List<OrderPushDownItemEntity> items) {
        return items.stream().collect(Collectors.toMap(e -> e.getConfigPath()+ Constant.UNDERLINE + e.getCode(), Function.identity()));
    }

    protected OrderPushDownItemEntity getWriteBackItem(Map<String, OrderPushDownItemEntity> map, PushDownItemWriteBackEnum e) {
        return map.get(e.getConfigPath() +Constant.UNDERLINE + e.getCode());
    }

    private void callUrlWriteBack(T o, String url) {
        if (StringUtils.isEmpty(url)) {
            return;
        }
        //创建请求对象
        HttpHeaders httpHeaders = new HttpHeaders();
        HttpEntity<Object> request = new HttpEntity<>(o, httpHeaders);
        try {
            ResponseEntity<ResponseData> response = restTemplate.postForEntity(url, request, ResponseData.class);
            if (!HttpStatus.OK.equals(response.getStatusCode())) {
                log.info("下推回写: 调用接正常");
            } else {
                log.warn("下推回写: 调用接口异常, msg: {}", JacksonUtil.toJSONStringIfNull(response.getBody()));
            }
        } catch (Exception e) {
            log.error("下推回写: 调用接口失败, error:{}", e.getMessage());
        }
    }

}
