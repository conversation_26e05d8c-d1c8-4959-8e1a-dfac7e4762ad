package com.yelink.dfs.migration;

import cn.hutool.extra.spring.SpringUtil;
import com.yelink.dfs.service.impl.metrics.MetricsTableConfigInitializer;
import org.flywaydb.core.api.migration.BaseJavaMigration;
import org.flywaydb.core.api.migration.Context;



/**
 * 内置角色权限
 *
 * <AUTHOR>
 */
public class V3_18_1_4__JavaMigration extends BaseJavaMigration {


    @Override
    public void migrate(Context context) {

    }

}

