package com.yelink.dfs.migration;

import com.yelink.dfs.entity.device.dto.MetricsDocument;
import com.yelink.dfs.mapper.device.IndicatorMapper;
import com.yelink.dfs.service.mongodb.MongodbService;
import com.yelink.dfs.utils.SpringUtil;
import com.yelink.dfscommon.utils.ColumnUtil;
import org.flywaydb.core.api.migration.BaseJavaMigration;
import org.flywaydb.core.api.migration.Context;

import java.util.List;


/**
 * @Description: 新装机器菜单整理
 * @Author: zengzhengfu
 * @Date: 2024/2/23
 */
public class V3_0_3__JavaMigration extends BaseJavaMigration {

    @Override
    public void migrate(Context context) {
        MongodbService mongodbService = SpringUtil.getBean(MongodbService.class);
        IndicatorMapper indicatorMapper = SpringUtil.getBean(IndicatorMapper.class);
        List<String> metricsTables = indicatorMapper.getMetricsTables();
        for (String metricsTable : metricsTables) {
            mongodbService.createTimeSeriesDataBase(ColumnUtil.getField(MetricsDocument::getTime),
                    ColumnUtil.getField(MetricsDocument::getDeviceId),
                    metricsTable);
            mongodbService.addIndex(ColumnUtil.getField(MetricsDocument::getDeviceId), metricsTable);
            mongodbService.addIndex(ColumnUtil.getField(MetricsDocument::getTime), metricsTable);
            mongodbService.addIndex(ColumnUtil.getField(MetricsDocument::getBatch), metricsTable);
        }
    }


}
