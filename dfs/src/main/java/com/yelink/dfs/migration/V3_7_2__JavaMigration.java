package com.yelink.dfs.migration;

import com.yelink.dfs.entity.user.SysFieldPermissionEntity;
import com.yelink.dfs.entity.user.SysRoleEntity;
import com.yelink.dfs.entity.user.SysRoleFieldPermissionEntity;
import com.yelink.dfs.service.user.SysFieldPermissionService;
import com.yelink.dfs.service.user.SysRoleFieldPermissionService;
import com.yelink.dfs.service.user.SysRoleService;
import com.yelink.dfs.utils.SpringUtil;
import org.flywaydb.core.api.migration.BaseJavaMigration;
import org.flywaydb.core.api.migration.Context;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;


/**
 * @Description: 历史角色需要默认绑定字段权限的敏感字段
 * @Author: zwq
 * @Date: 2024/2/23
 */
public class V3_7_2__JavaMigration extends BaseJavaMigration {

    @Override
    public void migrate(Context context) {
        List<SysRoleFieldPermissionEntity> list = new ArrayList<>();

        SysRoleService roleService = SpringUtil.getBean(SysRoleService.class);
        SysRoleFieldPermissionService roleFieldPermissionService = SpringUtil.getBean(SysRoleFieldPermissionService.class);
        SysFieldPermissionService fieldPermissionService = SpringUtil.getBean(SysFieldPermissionService.class);

        List<SysRoleEntity> roleEntities = roleService.lambdaQuery().select(SysRoleEntity::getId).list();
        // 因3.5.2绑定过数据，这次是对新增的数据进行绑定
        List<String> newList = Arrays.asList("purchase_customerCode", "purchase_customerName", "purchaseRequest", "purchase_request_customerCode", "purchase_request_customerName");
        List<SysFieldPermissionEntity> permissionEntities = fieldPermissionService.lambdaQuery()
                .select(SysFieldPermissionEntity::getUniqueCode)
                .in(SysFieldPermissionEntity::getUniqueCode, newList).list();
        for (SysRoleEntity roleEntity : roleEntities) {
            for (SysFieldPermissionEntity permissionEntity : permissionEntities) {
                list.add(SysRoleFieldPermissionEntity.builder()
                        .roleId(roleEntity.getId())
                        .permissionUniqueCode(permissionEntity.getUniqueCode())
                        .build());
            }
        }
        roleFieldPermissionService.saveBatch(list);
    }

}
