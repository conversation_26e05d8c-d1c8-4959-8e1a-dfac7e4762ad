package com.yelink.dfs.migration.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * <AUTHOR>
 * @Date 2020-12-03 11:16
 */
@Data
@Builder
@ToString
@AllArgsConstructor
@NoArgsConstructor
@Document("dfs_migration_record")
public class MigrationRecordDocument {

    /**
     * id
     */
    @Id
    private String id;

    /**
     * 表名
     */
    @Indexed
    private String table;

    /**
     * 已完成的最大id
     */
    private Long finishId;

    /**
     * 是否迁移完毕
     */
    @Indexed
    private Boolean isFinish;

}
