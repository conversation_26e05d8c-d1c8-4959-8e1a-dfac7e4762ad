package com.yelink.dfs.migration;

import com.yelink.dfs.constant.statement.SourceTypeEnum;
import com.yelink.dfs.entity.statement.ManageSourceEntity;
import com.yelink.dfs.open.v1.rule.dto.RuleTypeConfigInsertDTO;
import com.yelink.dfs.service.rule.NumberRuleService;
import com.yelink.dfs.service.rule.RuleTypeConfigService;
import com.yelink.dfs.service.statement.ManageSourceService;
import com.yelink.dfs.utils.SpringUtil;
import com.yelink.dfscommon.constant.dfs.rule.NumberRuleCodeDTO;
import com.yelink.dfscommon.constant.dfs.rule.NumberRulesConfigEntity;
import com.yelink.dfscommon.utils.JacksonUtil;
import org.flywaydb.core.api.migration.BaseJavaMigration;
import org.flywaydb.core.api.migration.Context;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;


/**
 * 内置角色权限
 *
 * <AUTHOR>
 */
public class V3_17_2_10__JavaMigration extends BaseJavaMigration {

    @Override
    public void migrate(Context context) {
        // 新增BOM版本、工艺版本的编码规则
        addBomVersionNumberRules();
        addCraftVersionNumberRules();
        // 报表管理-初始默认数据集：基础数据增量每周统计、单据增量每周统计
        initManageReportSource();
    }

    private void initManageReportSource() {
        // 添加报表-dfs数据源
        ManageSourceService manageSourceService = SpringUtil.getBean(ManageSourceService.class);
        List<ManageSourceEntity> sources = new ArrayList<>();
        sources.add(ManageSourceEntity.builder().sourceCode("increaseBaseWeekly").sourceName("基础数据增量每周统计").sourceType(SourceTypeEnum.DFS.getCode()).build());
        sources.add(ManageSourceEntity.builder().sourceCode("increaseOrderWeekly").sourceName("单据增量每周统计").sourceType(SourceTypeEnum.DFS.getCode()).build());
        manageSourceService.addDataSource(sources);
    }


    private void addBomVersionNumberRules() {
        RuleTypeConfigService ruleTypeConfigService = SpringUtil.getBean(RuleTypeConfigService.class);
        NumberRuleService numberRuleService = SpringUtil.getBean(NumberRuleService.class);
        List<RuleTypeConfigInsertDTO.RulePrefixConfigDTO> prefixConfigs = new ArrayList<>();
        List<RuleTypeConfigInsertDTO.AutoIncrementConfigDTO> autoIncrementConfigDTOS = new ArrayList<>();
//        prefixConfigs.add(RuleTypeConfigInsertDTO.RulePrefixConfigDTO.builder().code(6).type("物料编码").input(0).build());
//        prefixConfigs.add(RuleTypeConfigInsertDTO.RulePrefixConfigDTO.builder().code(28).type("物料类型编码").input(0).build());
        prefixConfigs.add(RuleTypeConfigInsertDTO.RulePrefixConfigDTO.builder().code("32").type("bom编码(不展示,仅做归一处理)").input(0).build());

        autoIncrementConfigDTOS.add(RuleTypeConfigInsertDTO.AutoIncrementConfigDTO.builder().code("bomCode").name("按bom编码归一").needOtherRuleCodes("32").build());

        RuleTypeConfigInsertDTO build = RuleTypeConfigInsertDTO.builder()
                .type("105")
                .moduleName("BOM版本")
                .parentTypeCode("BASIC")
                .prefixConfigs(prefixConfigs)
                .incrementConfigs(autoIncrementConfigDTOS)
                .remark("版本的编码规则只在保存为新版本的时候调用默认规则")
                .build();
        ruleTypeConfigService.addRuleTypeConfig(build);
        // 修改编码规则组成信息细节
        numberRuleService.lambdaUpdate().eq(NumberRulesConfigEntity::getType, 105)
                .set(NumberRulesConfigEntity::getPrefixDetail, getNumberRulePrefixDetail("V", "32", "bom编码(不展示,仅做归一处理)", "bomCode"))
                .update();
    }

    private void addCraftVersionNumberRules() {
        RuleTypeConfigService ruleTypeConfigService = SpringUtil.getBean(RuleTypeConfigService.class);
        NumberRuleService numberRuleService = SpringUtil.getBean(NumberRuleService.class);
        List<RuleTypeConfigInsertDTO.RulePrefixConfigDTO> prefixConfigs = new ArrayList<>();
        List<RuleTypeConfigInsertDTO.AutoIncrementConfigDTO> autoIncrementConfigDTOS = new ArrayList<>();
//        prefixConfigs.add(RuleTypeConfigInsertDTO.RulePrefixConfigDTO.builder().code(6).type("物料编码").input(0).build());
//        prefixConfigs.add(RuleTypeConfigInsertDTO.RulePrefixConfigDTO.builder().code(28).type("物料类型编码").input(0).build());
        prefixConfigs.add(RuleTypeConfigInsertDTO.RulePrefixConfigDTO.builder().code("33").type("工艺编码(不展示,仅做归一处理)").input(0).build());

        autoIncrementConfigDTOS.add(RuleTypeConfigInsertDTO.AutoIncrementConfigDTO.builder().code("craftCode").name("按工艺编码归一").needOtherRuleCodes("33").build());

        RuleTypeConfigInsertDTO build = RuleTypeConfigInsertDTO.builder()
                .type("106")
                .moduleName("工艺版本")
                .parentTypeCode("BASIC")
                .prefixConfigs(prefixConfigs)
                .incrementConfigs(autoIncrementConfigDTOS)
                .remark("版本的编码规则只在保存为新版本的时候调用默认规则")
                .build();
        ruleTypeConfigService.addRuleTypeConfig(build);
        // 修改编码规则组成信息细节
        numberRuleService.lambdaUpdate().eq(NumberRulesConfigEntity::getType, 106)
                .set(NumberRulesConfigEntity::getPrefixDetail, getNumberRulePrefixDetail("V", "33", "工艺编码(不展示,仅做归一处理)", "craftCode"))
                .update();
    }

    /**
     * 重新组转编码规则
     */
    private static String getNumberRulePrefixDetail(String fixedInfo, String code, String name, String autoIncrementConfigureType) {
        List<NumberRuleCodeDTO> prefixDetailList = new ArrayList<>();
        NumberRuleCodeDTO noShowInfoDto = NumberRuleCodeDTO.builder().code(code).name(name).example("--").build();
        NumberRuleCodeDTO fixedInfoDto = NumberRuleCodeDTO.builder().code("5").name("固定信息").rule(fixedInfo).initValue(1).example(fixedInfo).build();
        NumberRuleCodeDTO autoSeqDto = NumberRuleCodeDTO.builder().code("4").name("自动生成序号").autoIncrementConfigureType(autoIncrementConfigureType).uuid(UUID.randomUUID().toString()).rule("1").initValue(1).example("1").build();
        prefixDetailList.add(noShowInfoDto);
        prefixDetailList.add(fixedInfoDto);
        prefixDetailList.add(autoSeqDto);
        return JacksonUtil.toJSONString(prefixDetailList);
    }


}

