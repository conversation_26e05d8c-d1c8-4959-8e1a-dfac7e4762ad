package com.yelink.dfs.migration;

import org.flywaydb.core.api.migration.BaseJavaMigration;
import org.flywaydb.core.api.migration.Context;

import java.util.List;


/**
 * @Description: 新装机器菜单整理
 * @Author: z<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2024/2/23
 */
public class V3_3_1__JavaMigration extends BaseJavaMigration {

    @Override
    public void migrate(Context context) {
        List<String> paths = V3_0_2__JavaMigration.getPaths();
        //作业工单管理
        paths.add("/assignment-model");
        V3_0_2__JavaMigration.migrate(paths);
    }


}
