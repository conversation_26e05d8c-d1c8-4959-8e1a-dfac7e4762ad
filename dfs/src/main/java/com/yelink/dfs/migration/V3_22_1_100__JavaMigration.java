package com.yelink.dfs.migration;

import com.yelink.dfs.entity.rule.AutoIncrementConfigEntity;
import com.yelink.dfs.entity.rule.RulePrefixConfigEntity;
import com.yelink.dfs.service.rule.NumberRuleService;
import com.yelink.dfs.service.rule.RuleAutoIncrementConfigService;
import com.yelink.dfs.service.rule.RulePrefixConfigService;
import com.yelink.dfs.utils.SpringUtil;
import com.yelink.dfscommon.constant.Constants;
import com.yelink.dfscommon.constant.dfs.rule.NumberRuleCodeDTO;
import com.yelink.dfscommon.constant.dfs.rule.NumberRulesConfigEntity;
import com.yelink.dfscommon.utils.JacksonUtil;
import lombok.extern.slf4j.Slf4j;
import org.flywaydb.core.api.migration.BaseJavaMigration;
import org.flywaydb.core.api.migration.Context;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;
import java.util.stream.Stream;


/**
 * 维表视图
 *
 * <AUTHOR>
 */
@Slf4j
public class V3_22_1_100__JavaMigration extends BaseJavaMigration {

    /**
     * 初始化新字段
     *
     * @param context
     */
    @Override
    public void migrate(Context context) {
        // 不良定义需要配置编码规则
        RulePrefixConfigService prefixConfigService = SpringUtil.getBean(RulePrefixConfigService.class);
        List<RulePrefixConfigEntity> rulePrefixes = prefixConfigService.lambdaQuery()
                .in(RulePrefixConfigEntity::getCode, Stream.of("1", "2", "4", "5").collect(Collectors.toList()))
                .list();
        for (RulePrefixConfigEntity rulePrefix : rulePrefixes) {
            List<String> allowRuleTypes = new ArrayList<>(Arrays.asList(rulePrefix.getAllowRuleTypes().split(Constants.SEP)));
            // 包含则代表现网手动处理过这个问题，无需往下继续执行
            if (allowRuleTypes.contains("102")) {
                return;
            }
            allowRuleTypes.add("102");
            prefixConfigService.lambdaUpdate().eq(RulePrefixConfigEntity::getId, rulePrefix.getId())
                    .set(RulePrefixConfigEntity::getAllowRuleTypes, String.join(Constants.SEP, allowRuleTypes))
                    .update();
        }
        RuleAutoIncrementConfigService autoIncrementConfigService = SpringUtil.getBean(RuleAutoIncrementConfigService.class);
        List<AutoIncrementConfigEntity> incrementConfigEntities = autoIncrementConfigService.lambdaQuery()
                .in(AutoIncrementConfigEntity::getCode, Stream.of("noCross", "day", "month", "year").collect(Collectors.toList()))
                .list();
        for (AutoIncrementConfigEntity incrementConfigEntity : incrementConfigEntities) {
            List<String> allowRuleTypes = new ArrayList<>(Arrays.asList(incrementConfigEntity.getAllowRuleTypes().split(Constants.SEP)));
            allowRuleTypes.add("102");
            autoIncrementConfigService.lambdaUpdate().eq(AutoIncrementConfigEntity::getId, incrementConfigEntity.getId())
                    .set(AutoIncrementConfigEntity::getAllowRuleTypes, String.join(Constants.SEP, allowRuleTypes))
                    .update();
        }
    }


}

