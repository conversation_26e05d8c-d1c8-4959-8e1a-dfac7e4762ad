package com.yelink.dfs.migration;

import com.yelink.dfs.service.rule.NumberRuleService;
import com.yelink.dfs.utils.SpringUtil;
import com.yelink.dfscommon.constant.dfs.rule.NumberRuleCodeDTO;
import com.yelink.dfscommon.constant.dfs.rule.NumberRulesConfigEntity;
import com.yelink.dfscommon.utils.JacksonUtil;
import org.flywaydb.core.api.migration.BaseJavaMigration;
import org.flywaydb.core.api.migration.Context;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;


/**
 * @Description: 修改编码规则为人工输入的默认规则为  单据首字母大写（固定信息） + 年月日 + 自增序号
 * @Author: zwq
 * @Date: 2024/2/23
 */
public class V3_9_4__JavaMigration extends BaseJavaMigration {

    @Override
    public void migrate(Context context) {
        // 修改编码规则为人工输入的默认规则为  单据首字母大写（固定信息） + 年月日 + 自增序号
        initNumberRule();
    }

    /**
     * 修改编码规则为人工输入的默认规则为  单据首字母大写（固定信息） + 年月日 + 自增序号
     */
    private static void initNumberRule() {
        NumberRuleService ruleService = SpringUtil.getBean(NumberRuleService.class);
        String json = "[{\"code\":1,\"example\":\"--\",\"name\":\"人工输入\"}]";
        String json2 = "[{\"code\":1,\"name\":\"人工输入\",\"example\":\"--\"}]";
        // 销售订单-单据编号1
        ruleService.lambdaUpdate()
                .and(wrapper -> wrapper.eq(NumberRulesConfigEntity::getPrefixDetail, json).or().eq(NumberRulesConfigEntity::getPrefixDetail, json2))
                .eq(NumberRulesConfigEntity::getType, 1)
                .set(NumberRulesConfigEntity::getPrefixDetail, getNumberRulePrefixDetail("XSDD"))
                .set(NumberRulesConfigEntity::getIsDefault, true)
                .update();
        // 出货申请-单据编号16
        ruleService.lambdaUpdate()
                .and(wrapper -> wrapper.eq(NumberRulesConfigEntity::getPrefixDetail, json).or().eq(NumberRulesConfigEntity::getPrefixDetail, json2))
                .eq(NumberRulesConfigEntity::getType, 16)
                .set(NumberRulesConfigEntity::getPrefixDetail, getNumberRulePrefixDetail("CHSQ"))
                .set(NumberRulesConfigEntity::getIsDefault, true)
                .update();
        // 销售发货-单据编号17
        ruleService.lambdaUpdate()
                .and(wrapper -> wrapper.eq(NumberRulesConfigEntity::getPrefixDetail, json).or().eq(NumberRulesConfigEntity::getPrefixDetail, json2))
                .eq(NumberRulesConfigEntity::getType, 17)
                .set(NumberRulesConfigEntity::getPrefixDetail, getNumberRulePrefixDetail("XSFH"))
                .set(NumberRulesConfigEntity::getIsDefault, true)
                .update();
        // 生产订单-单据编号10
        ruleService.lambdaUpdate()
                .and(wrapper -> wrapper.eq(NumberRulesConfigEntity::getPrefixDetail, json).or().eq(NumberRulesConfigEntity::getPrefixDetail, json2))
                .eq(NumberRulesConfigEntity::getType, 10)
                .set(NumberRulesConfigEntity::getPrefixDetail, getNumberRulePrefixDetail("SCDD"))
                .set(NumberRulesConfigEntity::getIsDefault, true)
                .update();
        // 生产订单-生产订单用料清单34
        ruleService.lambdaUpdate()
                .and(wrapper -> wrapper.eq(NumberRulesConfigEntity::getPrefixDetail, json).or().eq(NumberRulesConfigEntity::getPrefixDetail, json2))
                .eq(NumberRulesConfigEntity::getType, 34)
                .set(NumberRulesConfigEntity::getPrefixDetail, getNumberRulePrefixDetail("SCDDYLQD"))
                .set(NumberRulesConfigEntity::getIsDefault, true)
                .update();
        // 生产工单-单据编号2
        ruleService.lambdaUpdate()
                .and(wrapper -> wrapper.eq(NumberRulesConfigEntity::getPrefixDetail, json).or().eq(NumberRulesConfigEntity::getPrefixDetail, json2))
                .eq(NumberRulesConfigEntity::getType, 2)
                .set(NumberRulesConfigEntity::getPrefixDetail, getNumberRulePrefixDetail("SCGD"))
                .set(NumberRulesConfigEntity::getIsDefault, true)
                .update();
        // 生产工单-生产批次号5
        ruleService.lambdaUpdate()
                .and(wrapper -> wrapper.eq(NumberRulesConfigEntity::getPrefixDetail, json).or().eq(NumberRulesConfigEntity::getPrefixDetail, json2))
                .eq(NumberRulesConfigEntity::getType, 5)
                .set(NumberRulesConfigEntity::getPrefixDetail, getNumberRulePrefixDetail("SCPCH"))
                .set(NumberRulesConfigEntity::getIsDefault, true)
                .update();
        // 生产工单-生产流水码12
        ruleService.lambdaUpdate()
                .and(wrapper -> wrapper.eq(NumberRulesConfigEntity::getPrefixDetail, json).or().eq(NumberRulesConfigEntity::getPrefixDetail, json2))
                .eq(NumberRulesConfigEntity::getType, 12)
                .set(NumberRulesConfigEntity::getPrefixDetail, getNumberRulePrefixDetail("SCLSM"))
                .set(NumberRulesConfigEntity::getIsDefault, true)
                .update();
        // 生产工单-生产成品码14
        ruleService.lambdaUpdate()
                .and(wrapper -> wrapper.eq(NumberRulesConfigEntity::getPrefixDetail, json).or().eq(NumberRulesConfigEntity::getPrefixDetail, json2))
                .eq(NumberRulesConfigEntity::getType, 14)
                .set(NumberRulesConfigEntity::getPrefixDetail, getNumberRulePrefixDetail("SCCPM"))
                .set(NumberRulesConfigEntity::getIsDefault, true)
                .update();
        // 领料申请-单据编号18
        ruleService.lambdaUpdate()
                .and(wrapper -> wrapper.eq(NumberRulesConfigEntity::getPrefixDetail, json).or().eq(NumberRulesConfigEntity::getPrefixDetail, json2))
                .eq(NumberRulesConfigEntity::getType, 18)
                .set(NumberRulesConfigEntity::getPrefixDetail, getNumberRulePrefixDetail("LLSQ"))
                .set(NumberRulesConfigEntity::getIsDefault, true)
                .update();
        // 作业工单-单据编号9
        ruleService.lambdaUpdate()
                .and(wrapper -> wrapper.eq(NumberRulesConfigEntity::getPrefixDetail, json).or().eq(NumberRulesConfigEntity::getPrefixDetail, json2))
                .eq(NumberRulesConfigEntity::getType, 9)
                .set(NumberRulesConfigEntity::getPrefixDetail, getNumberRulePrefixDetail("ZYGD"))
                .set(NumberRulesConfigEntity::getIsDefault, true)
                .update();
        // 采购需求-单据编号19
        ruleService.lambdaUpdate()
                .and(wrapper -> wrapper.eq(NumberRulesConfigEntity::getPrefixDetail, json).or().eq(NumberRulesConfigEntity::getPrefixDetail, json2))
                .eq(NumberRulesConfigEntity::getType, 19)
                .set(NumberRulesConfigEntity::getPrefixDetail, getNumberRulePrefixDetail("CGXQ"))
                .set(NumberRulesConfigEntity::getIsDefault, true)
                .update();
        // 采购订单-单据编号20
        ruleService.lambdaUpdate()
                .and(wrapper -> wrapper.eq(NumberRulesConfigEntity::getPrefixDetail, json).or().eq(NumberRulesConfigEntity::getPrefixDetail, json2))
                .eq(NumberRulesConfigEntity::getType, 20)
                .set(NumberRulesConfigEntity::getPrefixDetail, getNumberRulePrefixDetail("CGDD"))
                .set(NumberRulesConfigEntity::getIsDefault, true)
                .update();
        // 采购订单-采购批次号21
        ruleService.lambdaUpdate()
                .and(wrapper -> wrapper.eq(NumberRulesConfigEntity::getPrefixDetail, json).or().eq(NumberRulesConfigEntity::getPrefixDetail, json2))
                .eq(NumberRulesConfigEntity::getType, 21)
                .set(NumberRulesConfigEntity::getPrefixDetail, getNumberRulePrefixDetail("SCDDPCH"))
                .set(NumberRulesConfigEntity::getIsDefault, true)
                .update();
        // 采购订单-采购单品码22
        ruleService.lambdaUpdate()
                .and(wrapper -> wrapper.eq(NumberRulesConfigEntity::getPrefixDetail, json).or().eq(NumberRulesConfigEntity::getPrefixDetail, json2))
                .eq(NumberRulesConfigEntity::getType, 22)
                .set(NumberRulesConfigEntity::getPrefixDetail, getNumberRulePrefixDetail("SCDPM"))
                .set(NumberRulesConfigEntity::getIsDefault, true)
                .update();
        // 采购收货-单据编号23
        ruleService.lambdaUpdate()
                .and(wrapper -> wrapper.eq(NumberRulesConfigEntity::getPrefixDetail, json).or().eq(NumberRulesConfigEntity::getPrefixDetail, json2))
                .eq(NumberRulesConfigEntity::getType, 23)
                .set(NumberRulesConfigEntity::getPrefixDetail, getNumberRulePrefixDetail("CGSH"))
                .set(NumberRulesConfigEntity::getIsDefault, true)
                .update();
        // 采购收货-采购批次号11
        ruleService.lambdaUpdate()
                .and(wrapper -> wrapper.eq(NumberRulesConfigEntity::getPrefixDetail, json).or().eq(NumberRulesConfigEntity::getPrefixDetail, json2))
                .eq(NumberRulesConfigEntity::getType, 11)
                .set(NumberRulesConfigEntity::getPrefixDetail, getNumberRulePrefixDetail("CGSHPCH"))
                .set(NumberRulesConfigEntity::getIsDefault, true)
                .update();
        // 采购退料-申请单号33
        ruleService.lambdaUpdate()
                .and(wrapper -> wrapper.eq(NumberRulesConfigEntity::getPrefixDetail, json).or().eq(NumberRulesConfigEntity::getPrefixDetail, json2))
                .eq(NumberRulesConfigEntity::getType, 33)
                .set(NumberRulesConfigEntity::getPrefixDetail, getNumberRulePrefixDetail("CGTL"))
                .set(NumberRulesConfigEntity::getIsDefault, true)
                .update();
        // 来料检验-单据编号24
        ruleService.lambdaUpdate()
                .and(wrapper -> wrapper.eq(NumberRulesConfigEntity::getPrefixDetail, json).or().eq(NumberRulesConfigEntity::getPrefixDetail, json2))
                .eq(NumberRulesConfigEntity::getType, 24)
                .set(NumberRulesConfigEntity::getPrefixDetail, getNumberRulePrefixDetail("LLJY"))
                .set(NumberRulesConfigEntity::getIsDefault, true)
                .update();
        // 来料检验-采购批次号(拆批)25
        ruleService.lambdaUpdate()
                .and(wrapper -> wrapper.eq(NumberRulesConfigEntity::getPrefixDetail, json).or().eq(NumberRulesConfigEntity::getPrefixDetail, json2))
                .eq(NumberRulesConfigEntity::getType, 25)
                .set(NumberRulesConfigEntity::getPrefixDetail, getNumberRulePrefixDetail("LLJYCGPC"))
                .set(NumberRulesConfigEntity::getIsDefault, true)
                .update();
        // BOM-单据编号4
        ruleService.lambdaUpdate()
                .and(wrapper -> wrapper.eq(NumberRulesConfigEntity::getPrefixDetail, json).or().eq(NumberRulesConfigEntity::getPrefixDetail, json2))
                .eq(NumberRulesConfigEntity::getType, 4)
                .set(NumberRulesConfigEntity::getPrefixDetail, getNumberRulePrefixDetail("BOM"))
                .set(NumberRulesConfigEntity::getIsDefault, true)
                .update();
        // 工艺-单据编号26
        ruleService.lambdaUpdate()
                .and(wrapper -> wrapper.eq(NumberRulesConfigEntity::getPrefixDetail, json).or().eq(NumberRulesConfigEntity::getPrefixDetail, json2))
                .eq(NumberRulesConfigEntity::getType, 26)
                .set(NumberRulesConfigEntity::getPrefixDetail, getNumberRulePrefixDetail("GY"))
                .set(NumberRulesConfigEntity::getIsDefault, true)
                .update();
        // 入库记录-单据编号27
        ruleService.lambdaUpdate()
                .and(wrapper -> wrapper.eq(NumberRulesConfigEntity::getPrefixDetail, json).or().eq(NumberRulesConfigEntity::getPrefixDetail, json2))
                .eq(NumberRulesConfigEntity::getType, 27)
                .set(NumberRulesConfigEntity::getPrefixDetail, getNumberRulePrefixDetail("RKJL"))
                .set(NumberRulesConfigEntity::getIsDefault, true)
                .update();
        // 入库记录-采购入库35
        ruleService.lambdaUpdate()
                .and(wrapper -> wrapper.eq(NumberRulesConfigEntity::getPrefixDetail, json).or().eq(NumberRulesConfigEntity::getPrefixDetail, json2))
                .eq(NumberRulesConfigEntity::getType, 35)
                .set(NumberRulesConfigEntity::getPrefixDetail, getNumberRulePrefixDetail("CGRKJL"))
                .set(NumberRulesConfigEntity::getIsDefault, true)
                .update();
        // 入库记录-生产入库36
        ruleService.lambdaUpdate()
                .and(wrapper -> wrapper.eq(NumberRulesConfigEntity::getPrefixDetail, json).or().eq(NumberRulesConfigEntity::getPrefixDetail, json2))
                .eq(NumberRulesConfigEntity::getType, 36)
                .set(NumberRulesConfigEntity::getPrefixDetail, getNumberRulePrefixDetail("SCRKJL"))
                .set(NumberRulesConfigEntity::getIsDefault, true)
                .update();
        // 入库记录-生产退料37
        ruleService.lambdaUpdate()
                .and(wrapper -> wrapper.eq(NumberRulesConfigEntity::getPrefixDetail, json).or().eq(NumberRulesConfigEntity::getPrefixDetail, json2))
                .eq(NumberRulesConfigEntity::getType, 37)
                .set(NumberRulesConfigEntity::getPrefixDetail, getNumberRulePrefixDetail("SCTLJL"))
                .set(NumberRulesConfigEntity::getIsDefault, true)
                .update();
        // 出库记录-单据编号6
        ruleService.lambdaUpdate()
                .and(wrapper -> wrapper.eq(NumberRulesConfigEntity::getPrefixDetail, json).or().eq(NumberRulesConfigEntity::getPrefixDetail, json2))
                .eq(NumberRulesConfigEntity::getType, 6)
                .set(NumberRulesConfigEntity::getPrefixDetail, getNumberRulePrefixDetail("CKJL"))
                .set(NumberRulesConfigEntity::getIsDefault, true)
                .update();
        // 出库记录-采购退料出库38
        ruleService.lambdaUpdate()
                .and(wrapper -> wrapper.eq(NumberRulesConfigEntity::getPrefixDetail, json).or().eq(NumberRulesConfigEntity::getPrefixDetail, json2))
                .eq(NumberRulesConfigEntity::getType, 38)
                .set(NumberRulesConfigEntity::getPrefixDetail, getNumberRulePrefixDetail("CGTLCKJL"))
                .set(NumberRulesConfigEntity::getIsDefault, true)
                .update();
        // 出库记录-生产领料出库39
        ruleService.lambdaUpdate()
                .and(wrapper -> wrapper.eq(NumberRulesConfigEntity::getPrefixDetail, json).or().eq(NumberRulesConfigEntity::getPrefixDetail, json2))
                .eq(NumberRulesConfigEntity::getType, 39)
                .set(NumberRulesConfigEntity::getPrefixDetail, getNumberRulePrefixDetail("SCLLCKJL"))
                .set(NumberRulesConfigEntity::getIsDefault, true)
                .update();
        // 出库记录-生产补料出库40
        ruleService.lambdaUpdate()
                .and(wrapper -> wrapper.eq(NumberRulesConfigEntity::getPrefixDetail, json).or().eq(NumberRulesConfigEntity::getPrefixDetail, json2))
                .eq(NumberRulesConfigEntity::getType, 40)
                .set(NumberRulesConfigEntity::getPrefixDetail, getNumberRulePrefixDetail("SCBLCKJL"))
                .set(NumberRulesConfigEntity::getIsDefault, true)
                .update();
        // 仓库调拨-单据编号28
        ruleService.lambdaUpdate()
                .and(wrapper -> wrapper.eq(NumberRulesConfigEntity::getPrefixDetail, json).or().eq(NumberRulesConfigEntity::getPrefixDetail, json2))
                .eq(NumberRulesConfigEntity::getType, 28)
                .set(NumberRulesConfigEntity::getPrefixDetail, getNumberRulePrefixDetail("CKDB"))
                .set(NumberRulesConfigEntity::getIsDefault, true)
                .update();
        // 盘点任务单-单据编号29
        ruleService.lambdaUpdate()
                .and(wrapper -> wrapper.eq(NumberRulesConfigEntity::getPrefixDetail, json).or().eq(NumberRulesConfigEntity::getPrefixDetail, json2))
                .eq(NumberRulesConfigEntity::getType, 29)
                .set(NumberRulesConfigEntity::getPrefixDetail, getNumberRulePrefixDetail("PDRWD"))
                .set(NumberRulesConfigEntity::getIsDefault, true)
                .update();
        // 质检报工-质检号13
        ruleService.lambdaUpdate()
                .and(wrapper -> wrapper.eq(NumberRulesConfigEntity::getPrefixDetail, json).or().eq(NumberRulesConfigEntity::getPrefixDetail, json2))
                .eq(NumberRulesConfigEntity::getType, 13)
                .set(NumberRulesConfigEntity::getPrefixDetail, getNumberRulePrefixDetail("ZJH"))
                .set(NumberRulesConfigEntity::getIsDefault, true)
                .update();
        // 产品检测-送检单号15
        ruleService.lambdaUpdate()
                .and(wrapper -> wrapper.eq(NumberRulesConfigEntity::getPrefixDetail, json).or().eq(NumberRulesConfigEntity::getPrefixDetail, json2))
                .eq(NumberRulesConfigEntity::getType, 15)
                .set(NumberRulesConfigEntity::getPrefixDetail, getNumberRulePrefixDetail("SJD"))
                .set(NumberRulesConfigEntity::getIsDefault, true)
                .update();
        // 产品检测-质检单号30
        ruleService.lambdaUpdate()
                .and(wrapper -> wrapper.eq(NumberRulesConfigEntity::getPrefixDetail, json).or().eq(NumberRulesConfigEntity::getPrefixDetail, json2))
                .eq(NumberRulesConfigEntity::getType, 30)
                .set(NumberRulesConfigEntity::getPrefixDetail, getNumberRulePrefixDetail("ZJD"))
                .set(NumberRulesConfigEntity::getIsDefault, true)
                .update();
        // 产品检测-报告单号31
        ruleService.lambdaUpdate()
                .and(wrapper -> wrapper.eq(NumberRulesConfigEntity::getPrefixDetail, json).or().eq(NumberRulesConfigEntity::getPrefixDetail, json2))
                .eq(NumberRulesConfigEntity::getType, 31)
                .set(NumberRulesConfigEntity::getPrefixDetail, getNumberRulePrefixDetail("BGD"))
                .set(NumberRulesConfigEntity::getIsDefault, true)
                .update();
        // 生产任务-单据编号32
        ruleService.lambdaUpdate()
                .and(wrapper -> wrapper.eq(NumberRulesConfigEntity::getPrefixDetail, json).or().eq(NumberRulesConfigEntity::getPrefixDetail, json2))
                .eq(NumberRulesConfigEntity::getType, 32)
                .set(NumberRulesConfigEntity::getPrefixDetail, getNumberRulePrefixDetail("SCRW"))
                .set(NumberRulesConfigEntity::getIsDefault, true)
                .update();
        // 供应商档案-供应商编码3
        ruleService.lambdaUpdate()
                .and(wrapper -> wrapper.eq(NumberRulesConfigEntity::getPrefixDetail, json).or().eq(NumberRulesConfigEntity::getPrefixDetail, json2))
                .eq(NumberRulesConfigEntity::getType, 3)
                .set(NumberRulesConfigEntity::getPrefixDetail, getNumberRulePrefixDetail("GYS"))
                .set(NumberRulesConfigEntity::getIsDefault, true)
                .update();
        // 炉号信息-电炉炉次号7
        ruleService.lambdaUpdate()
                .and(wrapper -> wrapper.eq(NumberRulesConfigEntity::getPrefixDetail, json).or().eq(NumberRulesConfigEntity::getPrefixDetail, json2))
                .eq(NumberRulesConfigEntity::getType, 7)
                .set(NumberRulesConfigEntity::getPrefixDetail, getNumberRulePrefixDetail("DL"))
                .set(NumberRulesConfigEntity::getIsDefault, true)
                .update();
        // 炉号信息-精炼炉炉次号8
        ruleService.lambdaUpdate()
                .and(wrapper -> wrapper.eq(NumberRulesConfigEntity::getPrefixDetail, json).or().eq(NumberRulesConfigEntity::getPrefixDetail, json2))
                .eq(NumberRulesConfigEntity::getType, 8)
                .set(NumberRulesConfigEntity::getPrefixDetail, getNumberRulePrefixDetail("JLL"))
                .set(NumberRulesConfigEntity::getIsDefault, true)
                .update();
        // 委外订单-单据编号41
        ruleService.lambdaUpdate()
                .and(wrapper -> wrapper.eq(NumberRulesConfigEntity::getPrefixDetail, json).or().eq(NumberRulesConfigEntity::getPrefixDetail, json2))
                .eq(NumberRulesConfigEntity::getType, 41)
                .set(NumberRulesConfigEntity::getPrefixDetail, getNumberRulePrefixDetail("WWDD"))
                .set(NumberRulesConfigEntity::getIsDefault, true)
                .update();
        // 委外订单物料清单-单据编号45
        ruleService.lambdaUpdate()
                .and(wrapper -> wrapper.eq(NumberRulesConfigEntity::getPrefixDetail, json).or().eq(NumberRulesConfigEntity::getPrefixDetail, json2))
                .eq(NumberRulesConfigEntity::getType, 45)
                .set(NumberRulesConfigEntity::getPrefixDetail, getNumberRulePrefixDetail("WWDDWLQD"))
                .set(NumberRulesConfigEntity::getIsDefault, true)
                .update();
        // 委外订单收货单-单据编号44
        ruleService.lambdaUpdate()
                .and(wrapper -> wrapper.eq(NumberRulesConfigEntity::getPrefixDetail, json).or().eq(NumberRulesConfigEntity::getPrefixDetail, json2))
                .eq(NumberRulesConfigEntity::getType, 44)
                .set(NumberRulesConfigEntity::getPrefixDetail, getNumberRulePrefixDetail("WWDDSH"))
                .set(NumberRulesConfigEntity::getIsDefault, true)
                .update();
        // 委外订单收货单-收货批次号43
        ruleService.lambdaUpdate()
                .and(wrapper -> wrapper.eq(NumberRulesConfigEntity::getPrefixDetail, json).or().eq(NumberRulesConfigEntity::getPrefixDetail, json2))
                .eq(NumberRulesConfigEntity::getType, 43)
                .set(NumberRulesConfigEntity::getPrefixDetail, getNumberRulePrefixDetail("WWDDSHPC"))
                .set(NumberRulesConfigEntity::getIsDefault, true)
                .update();
        // 仓储配送-货架定义42
        ruleService.lambdaUpdate()
                .and(wrapper -> wrapper.eq(NumberRulesConfigEntity::getPrefixDetail, json).or().eq(NumberRulesConfigEntity::getPrefixDetail, json2))
                .eq(NumberRulesConfigEntity::getType, 42)
                .set(NumberRulesConfigEntity::getPrefixDetail, getNumberRulePrefixDetail("HJ"))
                .set(NumberRulesConfigEntity::getIsDefault, true)
                .update();
        // 其他出库申请单-单据编号46
        ruleService.lambdaUpdate()
                .and(wrapper -> wrapper.eq(NumberRulesConfigEntity::getPrefixDetail, json).or().eq(NumberRulesConfigEntity::getPrefixDetail, json2))
                .eq(NumberRulesConfigEntity::getType, 46)
                .set(NumberRulesConfigEntity::getPrefixDetail, getNumberRulePrefixDetail("QTCKSQ"))
                .set(NumberRulesConfigEntity::getIsDefault, true)
                .update();
        // 其他入库申请单-单据编号47
        ruleService.lambdaUpdate()
                .and(wrapper -> wrapper.eq(NumberRulesConfigEntity::getPrefixDetail, json).or().eq(NumberRulesConfigEntity::getPrefixDetail, json2))
                .eq(NumberRulesConfigEntity::getType, 47)
                .set(NumberRulesConfigEntity::getPrefixDetail, getNumberRulePrefixDetail("QTRKSQ"))
                .set(NumberRulesConfigEntity::getIsDefault, true)
                .update();
        // 其他入库-单据编号48
        ruleService.lambdaUpdate()
                .and(wrapper -> wrapper.eq(NumberRulesConfigEntity::getPrefixDetail, json).or().eq(NumberRulesConfigEntity::getPrefixDetail, json2))
                .eq(NumberRulesConfigEntity::getType, 48)
                .set(NumberRulesConfigEntity::getPrefixDetail, getNumberRulePrefixDetail("QTRK"))
                .set(NumberRulesConfigEntity::getIsDefault, true)
                .update();
        // 其他出库-单据编号49
        ruleService.lambdaUpdate()
                .and(wrapper -> wrapper.eq(NumberRulesConfigEntity::getPrefixDetail, json).or().eq(NumberRulesConfigEntity::getPrefixDetail, json2))
                .eq(NumberRulesConfigEntity::getType, 49)
                .set(NumberRulesConfigEntity::getPrefixDetail, getNumberRulePrefixDetail("QTCK"))
                .set(NumberRulesConfigEntity::getIsDefault, true)
                .update();
        // 销售出库单-单据编号50
        ruleService.lambdaUpdate()
                .and(wrapper -> wrapper.eq(NumberRulesConfigEntity::getPrefixDetail, json).or().eq(NumberRulesConfigEntity::getPrefixDetail, json2))
                .eq(NumberRulesConfigEntity::getType, 50)
                .set(NumberRulesConfigEntity::getPrefixDetail, getNumberRulePrefixDetail("XSCK"))
                .set(NumberRulesConfigEntity::getIsDefault, true)
                .update();
        // 销售退料入库单-单据编号51
        ruleService.lambdaUpdate()
                .and(wrapper -> wrapper.eq(NumberRulesConfigEntity::getPrefixDetail, json).or().eq(NumberRulesConfigEntity::getPrefixDetail, json2))
                .eq(NumberRulesConfigEntity::getType, 51)
                .set(NumberRulesConfigEntity::getPrefixDetail, getNumberRulePrefixDetail("XSTLRK"))
                .set(NumberRulesConfigEntity::getIsDefault, true)
                .update();
        // 委外订单入库单-单据编号52
        ruleService.lambdaUpdate()
                .and(wrapper -> wrapper.eq(NumberRulesConfigEntity::getPrefixDetail, json).or().eq(NumberRulesConfigEntity::getPrefixDetail, json2))
                .eq(NumberRulesConfigEntity::getType, 52)
                .set(NumberRulesConfigEntity::getPrefixDetail, getNumberRulePrefixDetail("WWDDRK"))
                .set(NumberRulesConfigEntity::getIsDefault, true)
                .update();
        // 委外领料出库单-单据编号53
        ruleService.lambdaUpdate()
                .and(wrapper -> wrapper.eq(NumberRulesConfigEntity::getPrefixDetail, json).or().eq(NumberRulesConfigEntity::getPrefixDetail, json2))
                .eq(NumberRulesConfigEntity::getType, 53)
                .set(NumberRulesConfigEntity::getPrefixDetail, getNumberRulePrefixDetail("WWLLCK"))
                .set(NumberRulesConfigEntity::getIsDefault, true)
                .update();
        // 委外补料出库单-单据编号54
        ruleService.lambdaUpdate()
                .and(wrapper -> wrapper.eq(NumberRulesConfigEntity::getPrefixDetail, json).or().eq(NumberRulesConfigEntity::getPrefixDetail, json2))
                .eq(NumberRulesConfigEntity::getType, 54)
                .set(NumberRulesConfigEntity::getPrefixDetail, getNumberRulePrefixDetail("WWBLCK"))
                .set(NumberRulesConfigEntity::getIsDefault, true)
                .update();
        // 委外退料入库单-单据编号55
        ruleService.lambdaUpdate()
                .and(wrapper -> wrapper.eq(NumberRulesConfigEntity::getPrefixDetail, json).or().eq(NumberRulesConfigEntity::getPrefixDetail, json2))
                .eq(NumberRulesConfigEntity::getType, 55)
                .set(NumberRulesConfigEntity::getPrefixDetail, getNumberRulePrefixDetail("WWTLRK"))
                .set(NumberRulesConfigEntity::getIsDefault, true)
                .update();
        // 替代方案-单据编号56
        ruleService.lambdaUpdate()
                .and(wrapper -> wrapper.eq(NumberRulesConfigEntity::getPrefixDetail, json).or().eq(NumberRulesConfigEntity::getPrefixDetail, json2))
                .eq(NumberRulesConfigEntity::getType, 56)
                .set(NumberRulesConfigEntity::getPrefixDetail, getNumberRulePrefixDetail("TDFA"))
                .set(NumberRulesConfigEntity::getIsDefault, true)
                .update();
        // 生产订单-订单流水码57
        ruleService.lambdaUpdate()
                .and(wrapper -> wrapper.eq(NumberRulesConfigEntity::getPrefixDetail, json).or().eq(NumberRulesConfigEntity::getPrefixDetail, json2))
                .eq(NumberRulesConfigEntity::getType, 57)
                .set(NumberRulesConfigEntity::getPrefixDetail, getNumberRulePrefixDetail("DDLSM"))
                .set(NumberRulesConfigEntity::getIsDefault, true)
                .update();
        // 特征参数-单据编号58
        ruleService.lambdaUpdate()
                .and(wrapper -> wrapper.eq(NumberRulesConfigEntity::getPrefixDetail, json).or().eq(NumberRulesConfigEntity::getPrefixDetail, json2))
                .eq(NumberRulesConfigEntity::getType, 58)
                .set(NumberRulesConfigEntity::getPrefixDetail, getNumberRulePrefixDetail("TZCS"))
                .set(NumberRulesConfigEntity::getIsDefault, true)
                .update();
        // 维修工单-单据编号59
        ruleService.lambdaUpdate()
                .and(wrapper -> wrapper.eq(NumberRulesConfigEntity::getPrefixDetail, json).or().eq(NumberRulesConfigEntity::getPrefixDetail, json2))
                .eq(NumberRulesConfigEntity::getType, 59)
                .set(NumberRulesConfigEntity::getPrefixDetail, getNumberRulePrefixDetail("WXGD"))
                .set(NumberRulesConfigEntity::getIsDefault, true)
                .update();
        // 包装方案-单据编号60
        ruleService.lambdaUpdate()
                .and(wrapper -> wrapper.eq(NumberRulesConfigEntity::getPrefixDetail, json).or().eq(NumberRulesConfigEntity::getPrefixDetail, json2))
                .eq(NumberRulesConfigEntity::getType, 60)
                .set(NumberRulesConfigEntity::getPrefixDetail, getNumberRulePrefixDetail("BZFA"))
                .set(NumberRulesConfigEntity::getIsDefault, true)
                .update();
        // 生产工单-包装成品码61
        ruleService.lambdaUpdate()
                .and(wrapper -> wrapper.eq(NumberRulesConfigEntity::getPrefixDetail, json).or().eq(NumberRulesConfigEntity::getPrefixDetail, json2))
                .eq(NumberRulesConfigEntity::getType, 61)
                .set(NumberRulesConfigEntity::getPrefixDetail, getNumberRulePrefixDetail("BZCPM"))
                .set(NumberRulesConfigEntity::getIsDefault, true)
                .update();
        // 仓库-批次号62
        ruleService.lambdaUpdate()
                .and(wrapper -> wrapper.eq(NumberRulesConfigEntity::getPrefixDetail, json).or().eq(NumberRulesConfigEntity::getPrefixDetail, json2))
                .eq(NumberRulesConfigEntity::getType, 62)
                .set(NumberRulesConfigEntity::getPrefixDetail, getNumberRulePrefixDetail("CKPC"))
                .set(NumberRulesConfigEntity::getIsDefault, true)
                .update();
        // 移位单-单据编号63
        ruleService.lambdaUpdate()
                .and(wrapper -> wrapper.eq(NumberRulesConfigEntity::getPrefixDetail, json).or().eq(NumberRulesConfigEntity::getPrefixDetail, json2))
                .eq(NumberRulesConfigEntity::getType, 63)
                .set(NumberRulesConfigEntity::getPrefixDetail, getNumberRulePrefixDetail("YWD"))
                .set(NumberRulesConfigEntity::getIsDefault, true)
                .update();
        // 调整单-单据编号64
        ruleService.lambdaUpdate()
                .and(wrapper -> wrapper.eq(NumberRulesConfigEntity::getPrefixDetail, json).or().eq(NumberRulesConfigEntity::getPrefixDetail, json2))
                .eq(NumberRulesConfigEntity::getType, 64)
                .set(NumberRulesConfigEntity::getPrefixDetail, getNumberRulePrefixDetail("TZD"))
                .set(NumberRulesConfigEntity::getIsDefault, true)
                .update();
        // 盘点计划单-单据编号65
        ruleService.lambdaUpdate()
                .and(wrapper -> wrapper.eq(NumberRulesConfigEntity::getPrefixDetail, json).or().eq(NumberRulesConfigEntity::getPrefixDetail, json2))
                .eq(NumberRulesConfigEntity::getType, 65)
                .set(NumberRulesConfigEntity::getPrefixDetail, getNumberRulePrefixDetail("PDJHD"))
                .set(NumberRulesConfigEntity::getIsDefault, true)
                .update();
        // 上架记录-单据编号66
        ruleService.lambdaUpdate()
                .and(wrapper -> wrapper.eq(NumberRulesConfigEntity::getPrefixDetail, json).or().eq(NumberRulesConfigEntity::getPrefixDetail, json2))
                .eq(NumberRulesConfigEntity::getType, 66)
                .set(NumberRulesConfigEntity::getPrefixDetail, getNumberRulePrefixDetail("SJJL"))
                .set(NumberRulesConfigEntity::getIsDefault, true)
                .update();
        // SMT拼版-组合码67
        ruleService.lambdaUpdate()
                .and(wrapper -> wrapper.eq(NumberRulesConfigEntity::getPrefixDetail, json).or().eq(NumberRulesConfigEntity::getPrefixDetail, json2))
                .eq(NumberRulesConfigEntity::getType, 67)
                .set(NumberRulesConfigEntity::getPrefixDetail, getNumberRulePrefixDetail("SMTZHM"))
                .set(NumberRulesConfigEntity::getIsDefault, true)
                .update();
        // 巡检计划编号70
        ruleService.lambdaUpdate()
                .and(wrapper -> wrapper.eq(NumberRulesConfigEntity::getPrefixDetail, json).or().eq(NumberRulesConfigEntity::getPrefixDetail, json2))
                .eq(NumberRulesConfigEntity::getType, 70)
                .set(NumberRulesConfigEntity::getPrefixDetail, getNumberRulePrefixDetail("XJJH"))
                .set(NumberRulesConfigEntity::getIsDefault, true)
                .update();
        // 巡检任务编号71
        ruleService.lambdaUpdate()
                .and(wrapper -> wrapper.eq(NumberRulesConfigEntity::getPrefixDetail, json).or().eq(NumberRulesConfigEntity::getPrefixDetail, json2))
                .eq(NumberRulesConfigEntity::getType, 71)
                .set(NumberRulesConfigEntity::getPrefixDetail, getNumberRulePrefixDetail("XJRW"))
                .set(NumberRulesConfigEntity::getIsDefault, true)
                .update();
        // 点检计划编号72
        ruleService.lambdaUpdate()
                .and(wrapper -> wrapper.eq(NumberRulesConfigEntity::getPrefixDetail, json).or().eq(NumberRulesConfigEntity::getPrefixDetail, json2))
                .eq(NumberRulesConfigEntity::getType, 72)
                .set(NumberRulesConfigEntity::getPrefixDetail, getNumberRulePrefixDetail("DJJH"))
                .set(NumberRulesConfigEntity::getIsDefault, true)
                .update();
        // 点检任务编号73
        ruleService.lambdaUpdate()
                .and(wrapper -> wrapper.eq(NumberRulesConfigEntity::getPrefixDetail, json).or().eq(NumberRulesConfigEntity::getPrefixDetail, json2))
                .eq(NumberRulesConfigEntity::getType, 73)
                .set(NumberRulesConfigEntity::getPrefixDetail, getNumberRulePrefixDetail("DJRW"))
                .set(NumberRulesConfigEntity::getIsDefault, true)
                .update();
        // 保养计划编号74
        ruleService.lambdaUpdate()
                .and(wrapper -> wrapper.eq(NumberRulesConfigEntity::getPrefixDetail, json).or().eq(NumberRulesConfigEntity::getPrefixDetail, json2))
                .eq(NumberRulesConfigEntity::getType, 74)
                .set(NumberRulesConfigEntity::getPrefixDetail, getNumberRulePrefixDetail("BYJH"))
                .set(NumberRulesConfigEntity::getIsDefault, true)
                .update();
        // 保养任务编号75
        ruleService.lambdaUpdate()
                .and(wrapper -> wrapper.eq(NumberRulesConfigEntity::getPrefixDetail, json).or().eq(NumberRulesConfigEntity::getPrefixDetail, json2))
                .eq(NumberRulesConfigEntity::getType, 75)
                .set(NumberRulesConfigEntity::getPrefixDetail, getNumberRulePrefixDetail("BYRW"))
                .set(NumberRulesConfigEntity::getIsDefault, true)
                .update();
        // 生产工单-生产工单用料清单77
        ruleService.lambdaUpdate()
                .and(wrapper -> wrapper.eq(NumberRulesConfigEntity::getPrefixDetail, json).or().eq(NumberRulesConfigEntity::getPrefixDetail, json2))
                .eq(NumberRulesConfigEntity::getType, 77)
                .set(NumberRulesConfigEntity::getPrefixDetail, getNumberRulePrefixDetail("YLQD"))
                .set(NumberRulesConfigEntity::getIsDefault, true)
                .update();
        // 客户档案-客户编码78
        ruleService.lambdaUpdate()
                .and(wrapper -> wrapper.eq(NumberRulesConfigEntity::getPrefixDetail, json).or().eq(NumberRulesConfigEntity::getPrefixDetail, json2))
                .eq(NumberRulesConfigEntity::getType, 78)
                .set(NumberRulesConfigEntity::getPrefixDetail, getNumberRulePrefixDetail("KH"))
                .set(NumberRulesConfigEntity::getIsDefault, true)
                .update();
        // 生产订单-订单批次号80
        ruleService.lambdaUpdate()
                .and(wrapper -> wrapper.eq(NumberRulesConfigEntity::getPrefixDetail, json).or().eq(NumberRulesConfigEntity::getPrefixDetail, json2))
                .eq(NumberRulesConfigEntity::getType, 80)
                .set(NumberRulesConfigEntity::getPrefixDetail, getNumberRulePrefixDetail("DDPC"))
                .set(NumberRulesConfigEntity::getIsDefault, true)
                .update();
        // 委外发料单-单据编号83
        ruleService.lambdaUpdate()
                .and(wrapper -> wrapper.eq(NumberRulesConfigEntity::getPrefixDetail, json).or().eq(NumberRulesConfigEntity::getPrefixDetail, json2))
                .eq(NumberRulesConfigEntity::getType, 83)
                .set(NumberRulesConfigEntity::getPrefixDetail, getNumberRulePrefixDetail("WWFL"))
                .set(NumberRulesConfigEntity::getIsDefault, true)
                .update();
        // 物料-单据编号82
        ruleService.lambdaUpdate()
                .and(wrapper -> wrapper.eq(NumberRulesConfigEntity::getPrefixDetail, json).or().eq(NumberRulesConfigEntity::getPrefixDetail, json2))
                .eq(NumberRulesConfigEntity::getType, 82)
                .set(NumberRulesConfigEntity::getPrefixDetail, getNumberRulePrefixDetail("WL"))
                .set(NumberRulesConfigEntity::getIsDefault, true)
                .update();
        // 委外退货单-单据编号84
        ruleService.lambdaUpdate()
                .and(wrapper -> wrapper.eq(NumberRulesConfigEntity::getPrefixDetail, json).or().eq(NumberRulesConfigEntity::getPrefixDetail, json2))
                .eq(NumberRulesConfigEntity::getType, 84)
                .set(NumberRulesConfigEntity::getPrefixDetail, getNumberRulePrefixDetail("WWTH"))
                .set(NumberRulesConfigEntity::getIsDefault, true)
                .update();
        // 生产工单-单据编号(排产拆分)85
        ruleService.lambdaUpdate()
                .and(wrapper -> wrapper.eq(NumberRulesConfigEntity::getPrefixDetail, json).or().eq(NumberRulesConfigEntity::getPrefixDetail, json2))
                .eq(NumberRulesConfigEntity::getType, 85)
                .set(NumberRulesConfigEntity::getPrefixDetail, getNumberRulePrefixDetail("SCGD"))
                .set(NumberRulesConfigEntity::getIsDefault, true)
                .update();
        // 包装工单-单据编号86
        ruleService.lambdaUpdate()
                .and(wrapper -> wrapper.eq(NumberRulesConfigEntity::getPrefixDetail, json).or().eq(NumberRulesConfigEntity::getPrefixDetail, json2))
                .eq(NumberRulesConfigEntity::getType, 86)
                .set(NumberRulesConfigEntity::getPrefixDetail, getNumberRulePrefixDetail("BZGD"))
                .set(NumberRulesConfigEntity::getIsDefault, true)
                .update();
        // 包装工单-包装码87
        ruleService.lambdaUpdate()
                .and(wrapper -> wrapper.eq(NumberRulesConfigEntity::getPrefixDetail, json).or().eq(NumberRulesConfigEntity::getPrefixDetail, json2))
                .eq(NumberRulesConfigEntity::getType, 87)
                .set(NumberRulesConfigEntity::getPrefixDetail, getNumberRulePrefixDetail("BZM"))
                .set(NumberRulesConfigEntity::getIsDefault, true)
                .update();
        // 不良定义-不良类型编号88
        ruleService.lambdaUpdate()
                .and(wrapper -> wrapper.eq(NumberRulesConfigEntity::getPrefixDetail, json).or().eq(NumberRulesConfigEntity::getPrefixDetail, json2))
                .eq(NumberRulesConfigEntity::getType, 88)
                .set(NumberRulesConfigEntity::getPrefixDetail, getNumberRulePrefixDetail("BLLX"))
                .set(NumberRulesConfigEntity::getIsDefault, true)
                .update();
        // 质量管理-检验项目组89
        ruleService.lambdaUpdate()
                .and(wrapper -> wrapper.eq(NumberRulesConfigEntity::getPrefixDetail, json).or().eq(NumberRulesConfigEntity::getPrefixDetail, json2))
                .eq(NumberRulesConfigEntity::getType, 89)
                .set(NumberRulesConfigEntity::getPrefixDetail, getNumberRulePrefixDetail("JYXMZ"))
                .set(NumberRulesConfigEntity::getIsDefault, true)
                .update();
        // 质量管理-检验项目90
        ruleService.lambdaUpdate()
                .and(wrapper -> wrapper.eq(NumberRulesConfigEntity::getPrefixDetail, json).or().eq(NumberRulesConfigEntity::getPrefixDetail, json2))
                .eq(NumberRulesConfigEntity::getType, 90)
                .set(NumberRulesConfigEntity::getPrefixDetail, getNumberRulePrefixDetail("JYXM"))
                .set(NumberRulesConfigEntity::getIsDefault, true)
                .update();
        // 检验单-单据编号91
        ruleService.lambdaUpdate()
                .and(wrapper -> wrapper.eq(NumberRulesConfigEntity::getPrefixDetail, json).or().eq(NumberRulesConfigEntity::getPrefixDetail, json2))
                .eq(NumberRulesConfigEntity::getType, 91)
                .set(NumberRulesConfigEntity::getPrefixDetail, getNumberRulePrefixDetail("JYD"))
                .set(NumberRulesConfigEntity::getIsDefault, true)
                .update();
        // 质量管理-缺陷定义92
        ruleService.lambdaUpdate()
                .and(wrapper -> wrapper.eq(NumberRulesConfigEntity::getPrefixDetail, json).or().eq(NumberRulesConfigEntity::getPrefixDetail, json2))
                .eq(NumberRulesConfigEntity::getType, 92)
                .set(NumberRulesConfigEntity::getPrefixDetail, getNumberRulePrefixDetail("QXDY"))
                .set(NumberRulesConfigEntity::getIsDefault, true)
                .update();
        // 质量管理-抽样方案93
        ruleService.lambdaUpdate()
                .and(wrapper -> wrapper.eq(NumberRulesConfigEntity::getPrefixDetail, json).or().eq(NumberRulesConfigEntity::getPrefixDetail, json2))
                .eq(NumberRulesConfigEntity::getType, 93)
                .set(NumberRulesConfigEntity::getPrefixDetail, getNumberRulePrefixDetail("CYFA"))
                .set(NumberRulesConfigEntity::getIsDefault, true)
                .update();
        // 质量管理-检验方案94
        ruleService.lambdaUpdate()
                .and(wrapper -> wrapper.eq(NumberRulesConfigEntity::getPrefixDetail, json).or().eq(NumberRulesConfigEntity::getPrefixDetail, json2))
                .eq(NumberRulesConfigEntity::getType, 94)
                .set(NumberRulesConfigEntity::getPrefixDetail, getNumberRulePrefixDetail("JYFA"))
                .set(NumberRulesConfigEntity::getIsDefault, true)
                .update();
        // 销售退货单95
        ruleService.lambdaUpdate()
                .and(wrapper -> wrapper.eq(NumberRulesConfigEntity::getPrefixDetail, json).or().eq(NumberRulesConfigEntity::getPrefixDetail, json2))
                .eq(NumberRulesConfigEntity::getType, 95)
                .set(NumberRulesConfigEntity::getPrefixDetail, getNumberRulePrefixDetail("XSTH"))
                .set(NumberRulesConfigEntity::getIsDefault, true)
                .update();
        // 销售退货单批次96
        ruleService.lambdaUpdate()
                .and(wrapper -> wrapper.eq(NumberRulesConfigEntity::getPrefixDetail, json).or().eq(NumberRulesConfigEntity::getPrefixDetail, json2))
                .eq(NumberRulesConfigEntity::getType, 96)
                .set(NumberRulesConfigEntity::getPrefixDetail, getNumberRulePrefixDetail("XSTHPC"))
                .set(NumberRulesConfigEntity::getIsDefault, true)
                .update();
        // 电池条码97
        ruleService.lambdaUpdate()
                .and(wrapper -> wrapper.eq(NumberRulesConfigEntity::getPrefixDetail, json).or().eq(NumberRulesConfigEntity::getPrefixDetail, json2))
                .eq(NumberRulesConfigEntity::getType, 97)
                .set(NumberRulesConfigEntity::getPrefixDetail, getNumberRulePrefixDetail("DCTM"))
                .set(NumberRulesConfigEntity::getIsDefault, true)
                .update();
        // 生产领料-申请单号81
        ruleService.lambdaUpdate()
                .and(wrapper -> wrapper.eq(NumberRulesConfigEntity::getPrefixDetail, json).or().eq(NumberRulesConfigEntity::getPrefixDetail, json2))
                .eq(NumberRulesConfigEntity::getType, 81)
                .set(NumberRulesConfigEntity::getPrefixDetail, getNumberRulePrefixDetail("SCLLSQ"))
                .set(NumberRulesConfigEntity::getIsDefault, true)
                .update();
        // 光猫发货单-单据编号76
        ruleService.lambdaUpdate()
                .and(wrapper -> wrapper.eq(NumberRulesConfigEntity::getPrefixDetail, json).or().eq(NumberRulesConfigEntity::getPrefixDetail, json2))
                .eq(NumberRulesConfigEntity::getType, 76)
                .set(NumberRulesConfigEntity::getPrefixDetail, getNumberRulePrefixDetail("GMFHD"))
                .set(NumberRulesConfigEntity::getIsDefault, true)
                .update();
        // 装箱发货单-单据编号79
        ruleService.lambdaUpdate()
                .and(wrapper -> wrapper.eq(NumberRulesConfigEntity::getPrefixDetail, json).or().eq(NumberRulesConfigEntity::getPrefixDetail, json2))
                .eq(NumberRulesConfigEntity::getType, 79)
                .set(NumberRulesConfigEntity::getPrefixDetail, getNumberRulePrefixDetail("ZXFHD"))
                .set(NumberRulesConfigEntity::getIsDefault, true)
                .update();
    }

    /**
     * 组装编码规则json
     * // [{"autoIncrementConfigureType":"","code":5,"example":"SCDD","initValue":1,"name":"固定信息","rule":"SCDD","uuid":""},
     * // {"autoIncrementConfigureType":"","code":2,"example":"20240813","initValue":1,"name":"当前日期","rule":"yyyyMMdd","uuid":""},
     * // {"autoIncrementConfigureType":"day","code":4,"example":"001","initValue":1,"name":"自动生成序号","rule":"3","uuid":"c5ada309-f3ce-4bbd-b41c-9ddbe91d28dd"}]
     */
    private static String getNumberRulePrefixDetail(String fixedInfo) {
        List<NumberRuleCodeDTO> prefixDetailList = new ArrayList<>();
        NumberRuleCodeDTO fixedInfoDto = NumberRuleCodeDTO.builder().code("5").name("固定信息").rule(fixedInfo).initValue(1).example(fixedInfo).build();
        NumberRuleCodeDTO dateDto = NumberRuleCodeDTO.builder().code("2").name("当前日期").rule("yyyyMMdd").initValue(1).example("20240813").build();
        NumberRuleCodeDTO autoSeqDto = NumberRuleCodeDTO.builder().code("4").name("自动生成序号").autoIncrementConfigureType("day").uuid(UUID.randomUUID().toString()).rule("3").initValue(1).example("001").build();
        prefixDetailList.add(fixedInfoDto);
        prefixDetailList.add(dateDto);
        prefixDetailList.add(autoSeqDto);
        return JacksonUtil.toJSONString(prefixDetailList);
    }

}
