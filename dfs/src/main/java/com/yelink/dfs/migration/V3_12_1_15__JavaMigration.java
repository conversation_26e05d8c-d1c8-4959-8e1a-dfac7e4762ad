package com.yelink.dfs.migration;

import com.yelink.dfs.constant.statement.ReportDefaultTemplateEnum;
import com.yelink.dfs.entity.common.ModelUploadFileEntity;
import com.yelink.dfs.entity.statement.ManageReportEntity;
import com.yelink.dfs.service.common.config.OrderPushDownConfigValueDictService;
import com.yelink.dfs.service.common.config.OrderPushDownConfigValueService;
import com.yelink.dfs.service.common.config.OrderPushDownItemService;
import com.yelink.dfs.service.statement.ManageReportService;
import com.yelink.dfs.utils.SpringUtil;
import com.yelink.dfscommon.constant.Constant;
import com.yelink.dfscommon.entity.dfs.OrderPushDownConfigValueDictEntity;
import com.yelink.dfscommon.entity.dfs.OrderPushDownConfigValueEntity;
import com.yelink.dfscommon.entity.dfs.OrderPushDownItemEntity;
import org.flywaydb.core.api.migration.BaseJavaMigration;
import org.flywaydb.core.api.migration.Context;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;


/**
 * 处理历史数据
 */
public class V3_12_1_15__JavaMigration extends BaseJavaMigration {


    @Override
    public void migrate(Context context) {
        // 处理销售订单下推采购需求单、采购订单下推配置项
        dealHistorySaleOrderPushDownPurchaseRequestData();
        dealHistorySaleOrderPushDownPurchaseOrderData();
        // 下推配置新增【是否支持无工艺】配置
        dealHistorySaleOrderPushDownWorkOrderData();
        // 报表管理中内置的默认报表支持内置模板
        dealManageReportInnerDefaultTemplate();
    }

    private void dealManageReportInnerDefaultTemplate() {
        ManageReportService manageReportService = SpringUtil.getBean(ManageReportService.class);
        //离散加工-生产日表
        ModelUploadFileEntity uploadFileEntity1 = manageReportService.applyInnerDefaultTemplate(ReportDefaultTemplateEnum.MANAGE_REPORT_PROCESSING_PRODUCTION_SCHEDULE);
        manageReportService.lambdaUpdate()
                .eq(ManageReportEntity::getReportName, ReportDefaultTemplateEnum.MANAGE_REPORT_PROCESSING_PRODUCTION_SCHEDULE.getDefaultTemplateName())
                .eq(ManageReportEntity::getCreateBy, Constant.ADMIN)
                .set(ManageReportEntity::getTemplateId, uploadFileEntity1.getId())
                .update();
        //离散加工-质量统计
        ModelUploadFileEntity uploadFileEntity2 = manageReportService.applyInnerDefaultTemplate(ReportDefaultTemplateEnum.MANAGE_REPORT_PROCESSING_QUALITY_STATISTICS);
        manageReportService.lambdaUpdate()
                .eq(ManageReportEntity::getReportName, ReportDefaultTemplateEnum.MANAGE_REPORT_PROCESSING_QUALITY_STATISTICS.getDefaultTemplateName())
                .eq(ManageReportEntity::getCreateBy, Constant.ADMIN)
                .set(ManageReportEntity::getTemplateId, uploadFileEntity2.getId())
                .update();
    }

    private void dealHistorySaleOrderPushDownWorkOrderData() {
        List<String> collect = Stream.of("saleOrder.pushDownConfig.workOrder.craftPush", "production.productOrderPushDownConfig.workOrder.craftPush").collect(Collectors.toList());
        List<OrderPushDownConfigValueEntity> list = new ArrayList<>();
        List<OrderPushDownConfigValueDictEntity> dictlist = new ArrayList<>();
        OrderPushDownConfigValueDictService valueDictService = SpringUtil.getBean(OrderPushDownConfigValueDictService.class);
        List<OrderPushDownConfigValueDictEntity> dictValueEntities = valueDictService.lambdaQuery()
                .in(OrderPushDownConfigValueDictEntity::getConfigFullPathCode, collect)
                .select(OrderPushDownConfigValueDictEntity::getConfigFullPathCode)
                .list();
        Map<String, List<OrderPushDownConfigValueDictEntity>> dictMap = dictValueEntities.stream().collect(Collectors.groupingBy(OrderPushDownConfigValueDictEntity::getConfigFullPathCode));
        for (Map.Entry<String, List<OrderPushDownConfigValueDictEntity>> listEntry : dictMap.entrySet()) {
            OrderPushDownConfigValueDictEntity valueEntity = listEntry.getValue().get(0);
            String configFullPathCode = valueEntity.getConfigFullPathCode();
            dictlist.add(OrderPushDownConfigValueDictEntity.builder()
                    .valueCode("isSupportNoCraft")
                    .valueName("无工艺下推")
                    .inputType("select")
                    .optionValuesType("table")
                    .optionValues("[{\"value\":true,\"label\":\"支持\"},{\"value\":false,\"label\":\"不支持\"}]")
                    .valueFullPathCode(configFullPathCode + ".isSupportNoCraft")
                    .configFullPathCode(configFullPathCode)
                    .value("false")
                    .build());
        }
        valueDictService.saveBatch(dictlist);

        OrderPushDownConfigValueService valueService = SpringUtil.getBean(OrderPushDownConfigValueService.class);
        // 根据itemId分组，对每个下推工艺路线的配置项新增两配置
        List<OrderPushDownConfigValueEntity> valueEntities = valueService.lambdaQuery()
                .in(OrderPushDownConfigValueEntity::getConfigFullPathCode, collect)
                .select(OrderPushDownConfigValueEntity::getConfigFullPathCode, OrderPushDownConfigValueEntity::getItemId)
                .list();
        Map<Integer, List<OrderPushDownConfigValueEntity>> map = valueEntities.stream().collect(Collectors.groupingBy(OrderPushDownConfigValueEntity::getItemId));
        for (Map.Entry<Integer, List<OrderPushDownConfigValueEntity>> listEntry : map.entrySet()) {
            Integer itemId = listEntry.getKey();
            OrderPushDownConfigValueEntity valueEntity = listEntry.getValue().get(0);
            String configFullPathCode = valueEntity.getConfigFullPathCode();
            list.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(itemId)
                    .valueCode("isSupportNoCraft")
                    .valueName("无工艺下推")
                    .inputType("select")
                    .optionValuesType("table")
                    .optionValues("[{\"value\":true,\"label\":\"支持\"},{\"value\":false,\"label\":\"不支持\"}]")
                    .valueFullPathCode(configFullPathCode + ".isSupportNoCraft")
                    .configFullPathCode(configFullPathCode)
                    .value("false")
                    .build());
        }
        valueService.saveBatch(list);
    }

    private void dealHistorySaleOrderPushDownPurchaseOrderData() {
        List<OrderPushDownConfigValueEntity> orderPushDownConfigValueEntities = new ArrayList<>();
        OrderPushDownItemService itemService = SpringUtil.getBean(OrderPushDownItemService.class);
        List<OrderPushDownItemEntity> itemEntities = itemService.lambdaQuery()
                .eq(OrderPushDownItemEntity::getInstanceType, "saleOrder.pushDownConfig.purchaseOrder.pushBatch")
                .select(OrderPushDownItemEntity::getId)
                .list();
        OrderPushDownConfigValueService valueService = SpringUtil.getBean(OrderPushDownConfigValueService.class);
        itemEntities.forEach(orderPushDownItemEntity -> {
            orderPushDownConfigValueEntities.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(orderPushDownItemEntity.getId())
                    .groupType("targetConf")
                    .valueCode("filterMaterialSorts")
                    .valueName("物料分类过滤")
                    .valueFullPathCode("saleOrder.pushDownConfig.purchaseOrder.pushBatch.filterMaterialSorts")
                    .configFullPathCode("saleOrder.pushDownConfig.purchaseOrder.pushBatch")
                    .inputType("select-multiple")
                    .optionValuesType("api")
                    .url("/api/materials/sort")
                    .method("get")
                    .params(null)
                    .respParamField("code,name")
                    .optionValues("[\"purchase\",\"product\",\"outsourcing\"]")
                    .value("[\"product\",\"outsourcing\"]")
                    .rules(null)
                    .build());
            orderPushDownConfigValueEntities.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(orderPushDownItemEntity.getId())
                    .groupType("targetConf")
                    .valueCode("bomSplitType")
                    .valueName("BOM拆分方式(单选)")
                    .valueFullPathCode("saleOrder.pushDownConfig.purchaseOrder.pushBatch.bomSplitType")
                    .configFullPathCode("saleOrder.pushDownConfig.purchaseOrder.pushBatch")
                    .inputType("select")
                    .optionValuesType("api")
                    .url("/ams/requests/bom/split")
                    .method("get")
                    .params(null)
                    .respParamField("code,name")
                    .optionValues("[\"direct\",\"byTierOneBom\",\"byMultiLevelBom\"]")
                    .value("\"byMultiLevelBom\"")
                    .rules(null)
                    .build());
        });
        valueService.saveBatch(orderPushDownConfigValueEntities);
    }

    private void dealHistorySaleOrderPushDownPurchaseRequestData() {
        List<OrderPushDownConfigValueEntity> orderPushDownConfigValueEntities = new ArrayList<>();
        OrderPushDownItemService itemService = SpringUtil.getBean(OrderPushDownItemService.class);
        List<OrderPushDownItemEntity> itemEntities = itemService.lambdaQuery()
                .eq(OrderPushDownItemEntity::getInstanceType, "saleOrder.pushDownConfig.purchaseRequest.pushBatch")
                .select(OrderPushDownItemEntity::getId)
                .list();
        OrderPushDownConfigValueService valueService = SpringUtil.getBean(OrderPushDownConfigValueService.class);
        itemEntities.forEach(orderPushDownItemEntity -> {
            orderPushDownConfigValueEntities.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(orderPushDownItemEntity.getId())
                    .groupType("targetConf")
                    .valueCode("filterMaterialSorts")
                    .valueName("物料分类过滤")
                    .valueFullPathCode("saleOrder.pushDownConfig.purchaseRequest.pushBatch.filterMaterialSorts")
                    .configFullPathCode("saleOrder.pushDownConfig.purchaseRequest.pushBatch")
                    .inputType("select-multiple")
                    .optionValuesType("api")
                    .url("/api/materials/sort")
                    .method("get")
                    .params(null)
                    .respParamField("code,name")
                    .optionValues("[\"purchase\",\"product\",\"outsourcing\"]")
                    .value("[\"product\",\"outsourcing\"]")
                    .rules(null)
                    .build());
        });
        valueService.saveBatch(orderPushDownConfigValueEntities);
    }
}

