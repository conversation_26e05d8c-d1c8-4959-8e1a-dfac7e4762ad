package com.yelink.dfs.migration;

import com.yelink.dfs.entity.target.TargetModelEntity;
import com.yelink.dfs.entity.target.dto.TargetImportDTO;
import com.yelink.dfs.entity.target.dto.TargetImportReqDTO;
import com.yelink.dfs.service.common.OperationLogService;
import com.yelink.dfs.service.impl.target.TargetModelServiceImpl;
import com.yelink.dfs.service.target.TargetModelService;
import com.yelink.dfs.utils.SpringUtil;
import com.yelink.dfscommon.constant.Constant;
import org.flywaydb.core.api.migration.BaseJavaMigration;
import org.flywaydb.core.api.migration.Context;

import java.util.ArrayList;
import java.util.List;


/**
 * @Description: 添加默认指标组
 * @Author: zengzhengfu
 * @Date: 2024/2/23
 */
public class V3_7_3__JavaMigration extends BaseJavaMigration {

    @Override
    public void migrate(Context context) {
        //判断是否使用过，有登录记录则不初始化
        OperationLogService operationLogService = SpringUtil.getBean(OperationLogService.class);
        long count = operationLogService.count();
        if (count > 0) {
            return;
        }

        TargetModelService targetModelService = SpringUtil.getBean(TargetModelService.class);
        List<TargetModelEntity> list = new ArrayList<>();
        list.add(TargetModelEntity.builder().targetName("saleOrderMaterialDaily").targetCnname("销售订单物料每日").modelId(3).methodName("saleOrderMaterialDaily").methodCname("系统统计").frequency(5).frequencyUnit("m").source("自动").build());
        list.add(TargetModelEntity.builder().targetName("saleOrderMaterial").targetCnname("销售订单物料").modelId(3).methodName("saleOrderMaterial").methodCname("系统统计").frequency(30).frequencyUnit("m").source("自动").build());
        list.add(TargetModelEntity.builder().targetName("workOrderDailyProgress").targetCnname("工单每日进度").modelId(4).methodName("systemCollectWorkOrderDailyProgress").methodCname("系统统计").frequency(5).frequencyUnit("m").source("自动").build());
        list.add(TargetModelEntity.builder().targetName("workOrderStatistics").targetCnname("累计工单统计").modelId(4).methodName("systemCollectWorkOrderStatistics").methodCname("系统统计").frequency(30).frequencyUnit("m").source("自动").build());
        list.add(TargetModelEntity.builder().targetName("qualityLineMaterialDaily").targetCnname("产线产品每日质量").modelId(5).methodName("qualityLineMaterialDaily").methodCname("系统统计").frequency(5).frequencyUnit("m").source("自动").build());
        list.add(TargetModelEntity.builder().targetName("qualityLineMaterialDefectDaily").targetCnname("产线产品不良每日质量").modelId(5).methodName("qualityLineMaterialDefectDaily").methodCname("系统统计").frequency(5).frequencyUnit("m").source("自动").build());
        list.add(TargetModelEntity.builder().targetName("productOrderDaily").targetCnname("生产订单每日").modelId(6).methodName("productOrderDaily").methodCname("系统统计").frequency(5).frequencyUnit("m").source("自动").build());
        list.add(TargetModelEntity.builder().targetName("productOrder").targetCnname("生产订单整体").modelId(6).methodName("productOrder").methodCname("系统统计").frequency(30).frequencyUnit("m").source("自动").build());
        list.add(TargetModelEntity.builder().targetName("endMaterialDaily").targetCnname("产成品每日").modelId(22).methodName("endMaterialDaily").methodCname("系统统计").frequency(5).frequencyUnit("m").source("自动").build());
        list.add(TargetModelEntity.builder().targetName("endMaterial").targetCnname("产成品整体").modelId(22).methodName("endMaterial").methodCname("系统统计").frequency(30).frequencyUnit("m").source("自动").build());
        list.add(TargetModelEntity.builder().targetName("topDaily").targetCnname("每日排行").modelId(112).methodName("topDaily").methodCname("系统统计").frequency(5).frequencyUnit("m").source("自动").build());
        targetModelService.saveBatch(list);

    }

}
