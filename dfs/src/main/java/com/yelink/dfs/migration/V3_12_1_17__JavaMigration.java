package com.yelink.dfs.migration;

import com.yelink.dfs.entity.user.SysRoleEntity;
import com.yelink.dfs.service.common.OperationLogService;
import com.yelink.dfs.service.user.SysRoleService;
import com.yelink.dfs.utils.SpringUtil;
import org.flywaydb.core.api.migration.BaseJavaMigration;
import org.flywaydb.core.api.migration.Context;


/**
 * 内置角色权限
 *
 * <AUTHOR>
 */
public class V3_12_1_17__JavaMigration extends BaseJavaMigration {

    /**
     * 新部署系统，管理员和系统管理员默认授权工作中心权限
     *
     * @param context
     */
    @Override
    public void migrate(Context context) {
        //判断是否使用过，有记录则不整理
        OperationLogService operationLogService = SpringUtil.getBean(OperationLogService.class);
        long count = operationLogService.count();
        if (count > 0) {
            return;
        }
        SysRoleService roleService = SpringUtil.getBean(SysRoleService.class);
        //管理员
        roleService.lambdaUpdate().eq(SysRoleEntity::getId, 1).set(SysRoleEntity::getAllWorkCenter, true).update();
        roleService.lambdaUpdate().eq(SysRoleEntity::getId, 3).set(SysRoleEntity::getAllWorkCenter, true).update();

    }

}

