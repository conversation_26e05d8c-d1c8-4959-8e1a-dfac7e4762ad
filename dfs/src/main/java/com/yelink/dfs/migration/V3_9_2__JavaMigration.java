package com.yelink.dfs.migration;

import com.yelink.dfs.service.common.config.OrderPushDownConfigValueDictService;
import com.yelink.dfs.service.common.config.OrderPushDownConfigValueService;
import com.yelink.dfs.utils.SpringUtil;
import com.yelink.dfscommon.entity.dfs.OrderPushDownConfigValueDictEntity;
import com.yelink.dfscommon.entity.dfs.OrderPushDownConfigValueEntity;
import org.flywaydb.core.api.migration.BaseJavaMigration;
import org.flywaydb.core.api.migration.Context;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;


/**
 * @Description: 历史下推配置需要绑定新增的下推配置项
 * @Author: zwq
 * @Date: 2024/2/23
 */
public class V3_9_2__JavaMigration extends BaseJavaMigration {

    @Override
    public void migrate(Context context) {
        List<String> collect = Stream.of("saleOrder.pushDownConfig.workOrder.craftRoute", "saleOrder.pushDownConfig.workOrder.craftRouteMergeOrder",
                "saleOrder.pushDownConfig.workOrderSubcontractOrder.craftRouteBatch", "production.productOrderPushDownConfig.workOrder.craftRoute",
                "production.productOrderPushDownConfig.workOrder.craftRouteAuto").collect(Collectors.toList());
        List<OrderPushDownConfigValueEntity> list = new ArrayList<>();
        List<OrderPushDownConfigValueDictEntity> dictlist = new ArrayList<>();
        OrderPushDownConfigValueDictService valueDictService = SpringUtil.getBean(OrderPushDownConfigValueDictService.class);
        List<OrderPushDownConfigValueDictEntity> dictValueEntities = valueDictService.lambdaQuery()
                .in(OrderPushDownConfigValueDictEntity::getConfigFullPathCode, collect)
                .select(OrderPushDownConfigValueDictEntity::getConfigFullPathCode)
                .list();
        Map<String, List<OrderPushDownConfigValueDictEntity>> dictMap = dictValueEntities.stream().collect(Collectors.groupingBy(OrderPushDownConfigValueDictEntity::getConfigFullPathCode));
        for (Map.Entry<String, List<OrderPushDownConfigValueDictEntity>> listEntry : dictMap.entrySet()) {
            OrderPushDownConfigValueDictEntity valueEntity = listEntry.getValue().get(0);
            String configFullPathCode = valueEntity.getConfigFullPathCode();
            dictlist.add(OrderPushDownConfigValueDictEntity.builder()
                    .valueCode("materialChoose")
                    .valueName("工序物料选择")
                    .inputType("select")
                    .optionValuesType("table")
                    .optionValues("[{\"value\":\"finishProduct\",\"label\":\"成品\"},{\"value\":\"procedureMaterial\",\"label\":\"工序物料\"}]")
                    .valueFullPathCode(configFullPathCode + ".materialChoose")
                    .configFullPathCode(configFullPathCode)
                    .value("\"finishProduct\"")
                    .build());
            dictlist.add(OrderPushDownConfigValueDictEntity.builder()
                    .valueCode("isShowFinishProductCraft")
                    .valueName("是否绑定成品工艺")
                    .inputType("select")
                    .optionValuesType("table")
                    .optionValues("[{\"value\":true,\"label\":\"是\"},{\"value\":false,\"label\":\"否\"}]")
                    .valueFullPathCode(configFullPathCode + ".isShowFinishProductCraft")
                    .configFullPathCode(configFullPathCode)
                    .value("false")
                    .build());
        }
        valueDictService.saveBatch(dictlist);

        OrderPushDownConfigValueService valueService = SpringUtil.getBean(OrderPushDownConfigValueService.class);
        // 根据itemId分组，对每个下推工艺路线的配置项新增两配置
        List<OrderPushDownConfigValueEntity> valueEntities = valueService.lambdaQuery()
                .in(OrderPushDownConfigValueEntity::getConfigFullPathCode, collect)
                .select(OrderPushDownConfigValueEntity::getConfigFullPathCode, OrderPushDownConfigValueEntity::getItemId)
                .list();
        Map<Integer, List<OrderPushDownConfigValueEntity>> map = valueEntities.stream().collect(Collectors.groupingBy(OrderPushDownConfigValueEntity::getItemId));
        for (Map.Entry<Integer, List<OrderPushDownConfigValueEntity>> listEntry : map.entrySet()) {
            Integer itemId = listEntry.getKey();
            OrderPushDownConfigValueEntity valueEntity = listEntry.getValue().get(0);
            String configFullPathCode = valueEntity.getConfigFullPathCode();
            list.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(itemId)
                    .valueCode("materialChoose")
                    .valueName("物料选择")
                    .inputType("select")
                    .optionValuesType("table")
                    .optionValues("[{\"value\":\"finishProduct\",\"label\":\"成品\"},{\"value\":\"procedureMaterial\",\"label\":\"工序物料\"}]")
                    .valueFullPathCode(configFullPathCode + ".materialChoose")
                    .configFullPathCode(configFullPathCode)
                    .value("\"finishProduct\"")
                    .build());
            list.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(itemId)
                    .valueCode("isShowFinishProductCraft")
                    .valueName("是否绑定成品工艺")
                    .inputType("select")
                    .optionValuesType("table")
                    .optionValues("[{\"value\":true,\"label\":\"是\"},{\"value\":false,\"label\":\"否\"}]")
                    .valueFullPathCode(configFullPathCode + ".isShowFinishProductCraft")
                    .configFullPathCode(configFullPathCode)
                    .value("false")
                    .build());
        }
        valueService.saveBatch(list);
    }


}
