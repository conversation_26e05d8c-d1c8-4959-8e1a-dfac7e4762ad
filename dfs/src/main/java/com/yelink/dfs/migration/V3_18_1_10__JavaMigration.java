package com.yelink.dfs.migration;

import com.yelink.dfs.entity.product.ProcedureEntity;
import com.yelink.dfs.service.common.config.BusinessConfigValueService;
import com.yelink.dfs.service.common.config.OrderPushDownConfigValueDictService;
import com.yelink.dfs.service.common.config.OrderPushDownConfigValueService;
import com.yelink.dfs.service.product.ProcedureService;
import com.yelink.dfs.utils.SpringUtil;
import com.yelink.dfscommon.constant.Constants;
import com.yelink.dfscommon.entity.dfs.BusinessConfigValueEntity;
import com.yelink.dfscommon.entity.dfs.OrderPushDownConfigValueDictEntity;
import com.yelink.dfscommon.entity.dfs.OrderPushDownConfigValueEntity;
import org.flywaydb.core.api.migration.BaseJavaMigration;
import org.flywaydb.core.api.migration.Context;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;


/**
 * 内置角色权限
 *
 * <AUTHOR>
 */
public class V3_18_1_10__JavaMigration extends BaseJavaMigration {

    @Override
    public void migrate(Context context) {
        // 下推配置目标单据配置中 新增 再次下推配置
        addHistoryPushDownDictConf();
        addHistoryPushDownConf();
        // 历史数据如果工序定义选择了多个工作中心，则软件参数配置改为单选
        ProcedureService procedureService = SpringUtil.getBean(ProcedureService.class);
        long count = procedureService.lambdaQuery().select(ProcedureEntity::getWorkCenterIds)
                .isNotNull(ProcedureEntity::getWorkCenterIds)
                .list().stream()
                .filter(procedureEntity -> procedureEntity.getWorkCenterIds().contains(Constants.SEP))
                .count();
        if (count > 0) {
            BusinessConfigValueService valueService = SpringUtil.getBean(BusinessConfigValueService.class);
            valueService.lambdaUpdate()
                    .eq(BusinessConfigValueEntity::getValueFullPathCode, "design.procedureDefConfig.relatedWorkCenterConfig.enable")
                    .set(BusinessConfigValueEntity::getValue, "false")
                    .update();
        }
    }

    private void addHistoryPushDownConf() {
        List<OrderPushDownConfigValueEntity> list = new ArrayList<>();
        List<String> collect = Stream.of(
                "production.productOrderPushDownConfig.subcontractOrder.craftRouteBatch",
                "saleOrder.pushDownConfig.subcontractOrder.craftRouteBatch"
        ).collect(Collectors.toList());
        OrderPushDownConfigValueService valueService = SpringUtil.getBean(OrderPushDownConfigValueService.class);
        // 根据itemId分组
        List<OrderPushDownConfigValueEntity> valueEntities = valueService.lambdaQuery()
                .in(OrderPushDownConfigValueEntity::getConfigFullPathCode, collect)
                .select(OrderPushDownConfigValueEntity::getConfigFullPathCode, OrderPushDownConfigValueEntity::getItemId)
                .list();
        Map<Integer, List<OrderPushDownConfigValueEntity>> map = valueEntities.stream().collect(Collectors.groupingBy(OrderPushDownConfigValueEntity::getItemId));
        for (Map.Entry<Integer, List<OrderPushDownConfigValueEntity>> listEntry : map.entrySet()) {
            Integer itemId = listEntry.getKey();
            OrderPushDownConfigValueEntity valueEntity = listEntry.getValue().get(0);
            String configFullPathCode = valueEntity.getConfigFullPathCode();
            list.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(itemId)
                    .groupType("targetConf")
                    .valueCode("processMerge")
                    .valueName("合并相同供应商")
                    .inputType("select")
                    .optionValuesType("api")
                    .url("/ams/subcontract/materials/get/merge/same/supplier")
                    .valueFullPathCode(configFullPathCode + ".processMerge")
                    .configFullPathCode(configFullPathCode)
                    .respParamField("type,name")
                    .optionValues("[\"continuousProcessMerge\",\"noContinuousProcessMerge\",\"noMerger\"]")
                    .value("\"noMerger\"")
                    .params(null)
                    .rules(null)
                    .build());
        }
        valueService.saveBatch(list);


    }


    private void addHistoryPushDownDictConf() {
        List<String> collect = Stream.of(
                "production.productOrderPushDownConfig.subcontractOrder.craftRouteBatch",
                "saleOrder.pushDownConfig.subcontractOrder.craftRouteBatch"
        ).collect(Collectors.toList());
        List<OrderPushDownConfigValueDictEntity> dictlist = new ArrayList<>();
        OrderPushDownConfigValueDictService valueDictService = SpringUtil.getBean(OrderPushDownConfigValueDictService.class);
        List<OrderPushDownConfigValueDictEntity> dictValueEntities = valueDictService.lambdaQuery()
                .in(OrderPushDownConfigValueDictEntity::getConfigFullPathCode, collect)
                .select(OrderPushDownConfigValueDictEntity::getConfigFullPathCode)
                .list();
        Map<String, List<OrderPushDownConfigValueDictEntity>> dictMap = dictValueEntities.stream().collect(Collectors.groupingBy(OrderPushDownConfigValueDictEntity::getConfigFullPathCode));
        for (Map.Entry<String, List<OrderPushDownConfigValueDictEntity>> listEntry : dictMap.entrySet()) {
            OrderPushDownConfigValueDictEntity valueEntity = listEntry.getValue().get(0);
            String configFullPathCode = valueEntity.getConfigFullPathCode();
            dictlist.add(OrderPushDownConfigValueDictEntity.builder()
                    .groupType("targetConf")
                    .valueCode("processMerge")
                    .valueName("合并相同供应商")
                    .inputType("select")
                    .optionValuesType("api")
                    .url("/ams/subcontract/materials/get/merge/same/supplier")
                    .method("get")
                    .valueFullPathCode(configFullPathCode + ".processMerge")
                    .configFullPathCode(configFullPathCode)
                    .respParamField("type,name")
                    .optionValues("[\"continuousProcessMerge\",\"noContinuousProcessMerge\",\"noMerger\"]")
                    .value("\"noMerger\"")
                    .params(null)
                    .rules(null)
                    .build());

        }
        valueDictService.saveBatch(dictlist);
    }


}

