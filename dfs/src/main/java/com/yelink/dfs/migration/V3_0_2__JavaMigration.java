package com.yelink.dfs.migration;

import com.yelink.dfs.constant.Constant;
import com.yelink.dfs.entity.user.SysPermissionEntity;
import com.yelink.dfs.entity.user.SysRoleBindPermissionEntity;
import com.yelink.dfs.mapper.user.SysPermissionMapper;
import com.yelink.dfs.service.common.OperationLogService;
import com.yelink.dfs.service.user.SysPermissionService;
import com.yelink.dfs.utils.SpringUtil;
import org.flywaydb.core.api.migration.BaseJavaMigration;
import org.flywaydb.core.api.migration.Context;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;


/**
 * @Description: 新装机器菜单整理
 * @Author: zengzhengfu
 * @Date: 2024/2/23
 */
public class V3_0_2__JavaMigration extends BaseJavaMigration {

    public static final int ROLE_ADMIN = 1;
    public static final int ROLE_YELINK_ONCALL = 3;

    @Override
    public void migrate(Context context) {
        List<String> paths = getPaths();

        migrate(paths);
    }

    public static List<String> getPaths() {
        List<String> paths = new ArrayList<>();
        //辅助计划
        paths.add("/order-model/auxiliary_plan");
        //工单调度
        paths.add("/order-model/workorder-scheduling");
        //自动排程
        paths.add("/order-model/automaticscheduling");
        //作业工单
        paths.add("/workorder-model/home-workorder");
        //生产检查项目
        paths.add("/workorder-model/product-checked/productionCheckProject");
        //生产检查方案
        paths.add("/workorder-model/product-checked/productionCheckPlan");
        //作业工单报工记录
        paths.add("/workorder-model/workorder-records");
        //上料防错
        paths.add("/workorder-model/feed-to-prevent-errors");
        //包装记录
        paths.add("/trace/package-record");
        //现场管理
        paths.add("/smt-management");
        //专家系统
        paths.add("/expert-system");
        //终端设备
        paths.add("/equipment/station-machine");
        //视频设备
        paths.add("/equipment/video-device");
        //配送配置
        paths.add("/avg/dispath");
        //白牌设置
        paths.add("/system_settings/whiteCardSetting");
        //初始化配置
        paths.add("/system_settings/init-config");
        //演示数据配置
        paths.add("/system_settings/demo-data-config");
        //项目管理
        paths.add("/projectManagement");

        return paths;
    }

    public static void migrate(List<String> paths) {
        //判断是否使用过，有登录记录则不整理
        OperationLogService operationLogService = SpringUtil.getBean(OperationLogService.class);
        long count = operationLogService.count();
        if (count > 0) {
            return;
        }

        SysPermissionService sysPermissionService = SpringUtil.getBean(SysPermissionService.class);
        //3.0只有以下这些字段
        List<SysPermissionEntity> allPermissions = sysPermissionService
                .lambdaQuery()
                .select(SysPermissionEntity::getId,
                        SysPermissionEntity::getName,
                        SysPermissionEntity::getPath,
                        SysPermissionEntity::getDescription,
                        SysPermissionEntity::getStatus,
                        SysPermissionEntity::getMethod,
                        SysPermissionEntity::getParentId,
                        SysPermissionEntity::getType,
                        SysPermissionEntity::getIsBackStage,
                        SysPermissionEntity::getIsWeb,
                        SysPermissionEntity::getIsEnable,
                        SysPermissionEntity::getParentPath,
                        SysPermissionEntity::getCreateTime,
                        SysPermissionEntity::getUpdateTime,
                        SysPermissionEntity::getServiceName,
                        SysPermissionEntity::getSort)
                .list();

        List<String> removeList = new ArrayList<>();
        for (String path : paths) {
            getRemoveListByPath(removeList, path, allPermissions);
        }

        List<String> permissionIdsList = allPermissions.stream().filter(o -> !removeList.contains(o.getPath())).map(SysPermissionEntity::getId).collect(Collectors.toList());
        bind(ROLE_ADMIN, permissionIdsList);
        bind(ROLE_YELINK_ONCALL, permissionIdsList);
    }

    private static void bind(Integer roleId, List<String> permissionIdsList) {
        // 删除绑定关系
        SysPermissionMapper sysPermissionMapper = SpringUtil.getBean(SysPermissionMapper.class);
        sysPermissionMapper.deletePermissionRoleByRoleId(roleId, null);

        SysPermissionService sysPermissionService = SpringUtil.getBean(SysPermissionService.class);
        if (roleId == ROLE_YELINK_ONCALL) {
            //系统管理员 设置菜单是否启用
            sysPermissionService.lambdaUpdate().set(SysPermissionEntity::getIsEnable, Constant.ZERO).update();
            sysPermissionService.lambdaUpdate().in(SysPermissionEntity::getId, permissionIdsList)
                    .set(SysPermissionEntity::getIsEnable, 1)
                    .update();
        }

        List<SysRoleBindPermissionEntity> list = new ArrayList<>();

        for (String permissionId : permissionIdsList) {
            list.add(
                    SysRoleBindPermissionEntity.builder().roleId(roleId).permissionId(permissionId).build()
            );
        }
        sysPermissionMapper.insertUserPermissionBatch(list);

    }

    private static void getRemoveListByPath(List<String> removeList, String path, List<SysPermissionEntity> allPermissions) {
        removeList.add(path);
        for (SysPermissionEntity permission : allPermissions) {
            if (path.equals(permission.getParentPath())) {
                getRemoveListByPath(removeList, permission.getPath(), allPermissions);
            }
        }
    }

}
