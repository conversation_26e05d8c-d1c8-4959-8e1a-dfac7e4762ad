package com.yelink.dfs.migration;

import com.yelink.dfs.service.common.config.FormFieldRuleConfigService;
import com.yelink.dfs.utils.SpringUtil;
import lombok.extern.slf4j.Slf4j;
import org.flywaydb.core.api.migration.BaseJavaMigration;
import org.flywaydb.core.api.migration.Context;


/**
 *
 * <AUTHOR>
 */
@Slf4j
public class V3_21_2_100__JavaMigration extends BaseJavaMigration {

    /**
     * 刷新物料全局扩展字段
     * @param context
     */
    @Override
    public void migrate(Context context) {
        // 维修工单、维修定义需要配置编码规则
        FormFieldRuleConfigService prefixConfigService = SpringUtil.getBean(FormFieldRuleConfigService.class);
        if(prefixConfigService == null) {
            return;
        }
        prefixConfigService.refreshAllMaterialExtendField();
    }


}

