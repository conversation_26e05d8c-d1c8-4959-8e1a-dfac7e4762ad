package com.yelink.dfs.migration;

import com.yelink.dfs.entity.device.progress.DeviceProgressParamDetailDocument;
import com.yelink.dfs.entity.device.progress.DeviceProgressParamDocument;
import com.yelink.dfs.service.mongodb.MongodbService;
import com.yelink.dfs.utils.SpringUtil;
import org.flywaydb.core.api.migration.BaseJavaMigration;
import org.flywaydb.core.api.migration.Context;
import org.springframework.data.mongodb.core.MongoTemplate;

/**
 * <AUTHOR>
 */
public class V2_29_1__JavaMigration extends BaseJavaMigration {

    private MongoTemplate mongoTemplate = SpringUtil.getBean(MongoTemplate.class);
    private MongodbService mongodbService = SpringUtil.getBean(MongodbService.class);

    @Override
    public void migrate(Context context) {
        mongoTemplate.dropCollection(DeviceProgressParamDocument.class);
        mongoTemplate.dropCollection(DeviceProgressParamDetailDocument.class);
        mongodbService.createTimeSeriesDataBase("createTime", null, "device_progress_param");
        mongodbService.createTimeSeriesDataBase("recordTime", null, "device_progress_param_detail");
        mongodbService.createTimeSeriesDataBase("createTime", "eui", "dfs_sensor_record");
        mongodbService.addIndex("eui", "dfs_sensor_record");
        mongodbService.addIndex("createTime", "dfs_sensor_record");
    }

}
