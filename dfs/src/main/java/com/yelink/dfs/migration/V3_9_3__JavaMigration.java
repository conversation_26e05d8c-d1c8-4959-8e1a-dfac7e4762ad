package com.yelink.dfs.migration;

import com.yelink.dfs.constant.Constant;
import com.yelink.dfs.constant.statement.ReportModelEnum;
import com.yelink.dfs.constant.statement.SourceTypeEnum;
import com.yelink.dfs.constant.sys.TableFieldTypeEnum;
import com.yelink.dfs.constant.target.TargetModelDataSourceTypeEnum;
import com.yelink.dfs.entity.model.ModelEntity;
import com.yelink.dfs.entity.statement.ManageReportEntity;
import com.yelink.dfs.entity.statement.ManageSourceEntity;
import com.yelink.dfs.entity.statement.dto.DataSourceTypeConfigDTO;
import com.yelink.dfs.entity.sys.dto.TableCreateOrUpdateDTO;
import com.yelink.dfs.entity.target.TargetModelEntity;
import com.yelink.dfs.entity.target.TargetModelTableRelatedEntity;
import com.yelink.dfs.entity.target.dto.TargetGroupInsertDTO;
import com.yelink.dfs.open.v1.metrics.dto.TableFieldCreateDTO;
import com.yelink.dfs.open.v1.metrics.dto.TableViewCreateDTO;
import com.yelink.dfs.open.v1.metrics.dto.TargetGroupViewInsertDTO;
import com.yelink.dfs.service.model.ModelService;
import com.yelink.dfs.service.statement.ManageReportService;
import com.yelink.dfs.service.statement.ManageSourceService;
import com.yelink.dfs.service.sys.TableService;
import com.yelink.dfs.service.target.TargetModelService;
import com.yelink.dfs.service.target.TargetModelTableRelatedService;
import com.yelink.dfs.utils.SpringUtil;
import com.yelink.dfscommon.entity.CommonType;
import com.yelink.dfscommon.utils.JacksonUtil;
import org.apache.commons.lang3.StringUtils;
import org.flywaydb.core.api.migration.BaseJavaMigration;
import org.flywaydb.core.api.migration.Context;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;
import java.util.stream.Stream;


/**
 * @Description: 历史下推配置需要绑定新增的下推配置项
 * @Author: zwq
 * @Date: 2024/2/23
 */
public class V3_9_3__JavaMigration extends BaseJavaMigration {

    @Override
    public void migrate(Context context) {
        // 初始化默认视图指标组
        initTargetGroupView();

        // 初始默认报表：离散加工-生产日表、离散加工-质量统计、离散加工-人员工资统计、组装线-销售进度表、组装线-生产日表
        initManageReport();
    }

    private void initManageReport() {
        // 添加报表-dfs数据源
        ManageSourceService manageSourceService = SpringUtil.getBean(ManageSourceService.class);
        List<ManageSourceEntity> sources = new ArrayList<>();
        sources.add(ManageSourceEntity.builder().sourceCode("discreteProcessingProductionSchedule").sourceName("离散加工-生产日表").sourceType(SourceTypeEnum.DFS.getCode()).build());
        sources.add(ManageSourceEntity.builder().sourceCode("discreteProcessingQualityStatistics").sourceName("离散加工-质量统计").sourceType(SourceTypeEnum.DFS.getCode()).build());
        sources.add(ManageSourceEntity.builder().sourceCode("discreteProcessingPersonnelSalaryStatistics").sourceName("离散加工-人员工资统计").sourceType(SourceTypeEnum.DFS.getCode()).build());
//        sources.add(ManageSourceEntity.builder().sourceCode("assemblyLineSalesSchedule").sourceName("组装线-销售进度表").sourceType(SourceTypeEnum.DFS.getCode()).build());
        sources.add(ManageSourceEntity.builder().sourceCode("assemblyLineProductionSchedule").sourceName("组装线-生产日表").sourceType(SourceTypeEnum.DFS.getCode()).build());
        manageSourceService.addDataSource(sources);
        // 添加报表
        ManageReportService manageReportService = SpringUtil.getBean(ManageReportService.class);

        List<DataSourceTypeConfigDTO> dataSourceConfig = manageReportService.getDataSourceConfig(ReportModelEnum.STATEMENT_CENTER.getCode());
        for (ManageSourceEntity source : sources) {
            ManageReportEntity build = ManageReportEntity.builder()
                    .reportName(source.getSourceName())
                    .model(ReportModelEnum.STATEMENT_CENTER.getCode())
                    .dataSources(source.getSourceCode())
                    .dataSourceConfig(manageReportService.getDataSourceConfig(dataSourceConfig, source.getSourceCode()))
                    .build();
            ManageReportEntity add = manageReportService.addReport(build);
        }
    }


    /**
     * 初始化默认视图指标组
     */
    private void initTargetGroupView() {
        // 获取自定义指标组对象模型，将指标视图添加在该模型下
        ModelService modelService = SpringUtil.getBean(ModelService.class);
        ModelEntity modelEntity = modelService.lambdaQuery().select(ModelEntity::getId)
                .eq(ModelEntity::getType, "baseTargetGroupObject")
                .eq(ModelEntity::getCode, "customTarget").one();
        int modelId = modelEntity.getId();

        List<TargetGroupViewInsertDTO> targetGroupViewInserts = new ArrayList<>();
        // 获取指标对应表的字段
        List<CommonType> targetGroups = Stream.of(CommonType.builder().code("discreteProcessingProductionSchedule").name("离散加工-生产日表")
                                .type("select vdrl.report_date,vdwo.line_name ,vdm.`code`, vdm.`name`, vdm.`standard`,vdm.`drawing_number`,\n" +
                                        "vdrl.`work_order`,vdwo.product_order_number ,vdwo.`sale_order_number`,vdrl.`finish_count`,vdrl.unqualified,\n" +
                                        "vdrl.user_nickname ,vdrl.operator_name,vdrl.shift_type ,vdrl.device_code,vdrl.device_id\n" +
                                        "from dfs_metrics.v_dfs_report_line vdrl,dfs_metrics.v_dfs_work_order vdwo,dfs_metrics.v_dfs_material vdm \n" +
                                        "where vdrl.work_order = vdwo.work_order_number \n" +
                                        "and vdwo.material_code = vdm.code \n" +
                                        "and vdrl.report_date = CURRENT_DATE()").build(),
                        CommonType.builder().code("discreteProcessingQualityStatistics").name("离散加工-质量统计").type("SELECT aaa.record_date,aaa.work_order_number,aaa.defect_count_sum,aaa.defect_1,aaa.defect_2,aaa.defect_3,aaa.defect_4,aaa.defect_5,aaa.defect_6,aaa.defect_7,aaa.defect_8,aaa.defect_9,aaa.defect_10,vdm.name,vdm.standard,vdm.code,vdm.drawing_number,vdwo.product_order_number  FROM \n" +
                                "(SELECT DATE(time) record_date ,work_order_number,sum(defect_count) as defect_count_sum,\n" +
                                "case when `rank`=1 then defect_name end as defect_1,\n" +
                                "case when `rank`=2 then defect_name end as defect_2,\n" +
                                "case when `rank`=3 then defect_name end as defect_3,\n" +
                                "case when `rank`=4 then defect_name end as defect_4,\n" +
                                "case when `rank`=5 then defect_name end as defect_5,\n" +
                                "case when `rank`=6 then defect_name end as defect_6,\n" +
                                "case when `rank`=7 then defect_name end as defect_7,\n" +
                                "case when `rank`=8 then defect_name end as defect_8,\n" +
                                "case when `rank`=9 then defect_name end as defect_9,\n" +
                                "case when `rank`=10 then defect_name end as defect_10\n" +
                                "from dfs_metrics.dfs_metrics_top_work_order dmtwo \n" +
                                "where DATE(time)  = CURRENT_DATE() \n" +
                                "group by record_date ,work_order_number \n" +
                                ")aaa, dfs_metrics.v_dfs_work_order vdwo,dfs_metrics.v_dfs_material vdm\n" +
                                "where aaa.work_order_number = vdwo.work_order_number and vdwo.material_code = vdm.code").build(),
                        CommonType.builder().code("discreteProcessingPersonnelSalaryStatistics").name("离散加工-人员工资统计").type("SELECT device_name,fname,line_name,material_name,nick_name,procedure_name,product_order_number,COUNT(id) as quantity,record_date,record_hour,relation_number from\n" +
                                "(SELECT ee.*,su.nick_name from \n" +
                                "(SELECT cc.*,dd.device_name from\n" +
                                "(SELECT bb.*,df.fname from\n" +
                                "(SELECT aa.*,dcp.procedure_name from\n" +
                                "(SELECT date(dpfcr.report_time) record_date,HOUR(dpfcr.report_time) record_hour,dpfcr.report_by,dpfcr.material_name,dpfcr.relation_number,dpfcr.produre_id ,dwo.line_name ,dpfcr.fac_id ,dpfcr.device_id ,dpfcr.id,dpfcr.product_order_number\n" +
                                "from dfs.dfs_product_flow_code_record dpfcr \n" +
                                "left join dfs_work_order dwo on dpfcr.relation_number = dwo.work_order_number having record_date=CURRENT_DATE()) aa\n" +
                                "left join dfs_craft_procedure dcp on aa.produre_id = dcp.id)bb\n" +
                                "left join dfs_facilities df on bb.fac_id = df.fid)cc\n" +
                                "left join dfs_device dd on cc.device_id = dd.device_id )ee\n" +
                                "left join sys_users su on ee.report_by = su.user_name) ff\n" +
                                "group by record_date,record_hour,nick_name,relation_number,procedure_name").build(),
                        CommonType.builder().code("assemblyLineProductionSchedule").name("组装线-生产日表").type("SELECT vdwo.sale_order_number ,vdwo.product_order_number ,dmwod.work_order_number,dmwod.plan_quantity,dmwod.produce_quantity,dmwod.inspection_quantity,dmwod.unqualified_quantity,dmwod.inbound_quantity,dmwod.direct_access_quantity,dmwod.start_work_time,dmwod.working_people_quantity,dmwod.actual_working_hour,dmwod.theory_working_hour,dmwod.qualified_rate,dmwod.product_efficiency_rate,dmwod.achievements,dmwod.repair_quantity,dmwod.repair_qualified_quantity,dmwod.repair_quantity_rate,dmwod.record_date,dmwod.time,dmwod.unqualified_record_quantity,dmwod.unqualified_record_item_quantity from dfs_metrics.dfs_metrics_work_order_daily dmwod , dfs_metrics.v_dfs_work_order vdwo \n" +
                                "where dmwod.record_date = CURRENT_DATE() and dmwod.work_order_number = vdwo.work_order_number").build()
                )
                .collect(Collectors.toList());

        for (CommonType commonType : targetGroups) {
            Object[][] tableFields = getTableFields(String.valueOf(commonType.getCode()));
            TargetGroupViewInsertDTO targetGroupView = getTargetGroupView(modelId, commonType, tableFields);
            targetGroupViewInserts.add(targetGroupView);
        }
        // 添加指标组视图
        addTargetGroupView(targetGroupViewInserts);
    }

    private static void addTargetGroupView(List<TargetGroupViewInsertDTO> targetGroupViewInserts) {
        TargetModelService targetModelService = SpringUtil.getBean(TargetModelService.class);
        TableService tableService = SpringUtil.getBean(TableService.class);
        TargetModelTableRelatedService targetModelTableRelatedService = SpringUtil.getBean(TargetModelTableRelatedService.class);
        List<TargetGroupInsertDTO> dtos = JacksonUtil.convertArray(targetGroupViewInserts, TargetGroupInsertDTO.class);
        for (TargetGroupInsertDTO insertDTO : dtos) {
            insertDTO.setDataSourceType(TargetModelDataSourceTypeEnum.VIEW.getCode());
            Date date = new Date();
            insertDTO.setCreateTime(date);
            insertDTO.setUpdateTime(date);
            // 指标组编码未填则由系统自动生成
            insertDTO.setTargetName(StringUtils.isNotBlank(insertDTO.getTargetName()) ? insertDTO.getTargetName() : UUID.randomUUID().toString());
            TableCreateOrUpdateDTO tableDTO = insertDTO.getTableDTO();
            tableDTO.setTableSchema(Constant.DFS_METRICS);
            // 创建视图
            tableService.createTableView(tableDTO.getTableSchema(), tableDTO.getTableName(), insertDTO.getScript());
            // 添加表结构定义
            tableService.saveTableConf(tableDTO);
            // 创建视图不需要计算频率
            insertDTO.setFrequency(null);
            insertDTO.setFrequencyUnit(null);
            // 添加不存在的自动指标
            TargetModelEntity targetModelEntity = JacksonUtil.convertObject(insertDTO, TargetModelEntity.class);
            targetModelService.addAutoTarget(targetModelEntity);
            // 创建指标模型和表的关联表
            targetModelTableRelatedService.save(
                    TargetModelTableRelatedEntity.builder()
                            .targetName(targetModelEntity.getTargetName())
                            .modelId(targetModelEntity.getModelId())
                            .tableSchema(tableDTO.getTableSchema())
                            .tableName(tableDTO.getTableName())
                            .build()
            );
        }
    }

    private Object[][] getTableFields(String targetCode) {
        if ("discreteProcessingProductionSchedule".equals(targetCode)) {
            // 指标视图：离散加工-生产日表
            return new Object[][]{
                    {"report_date", "日期", TableFieldTypeEnum.DATE.getType(), TableFieldTypeEnum.DATE.getDefaultLength(), TableFieldTypeEnum.DATE.getDefaultPoint(), "日期"},
                    {"line_name", "产线名称", TableFieldTypeEnum.VARCHAR.getType(), TableFieldTypeEnum.VARCHAR.getDefaultLength(), TableFieldTypeEnum.VARCHAR.getDefaultPoint(), "产线名称"},
                    {"code", "物料编码", TableFieldTypeEnum.VARCHAR.getType(), TableFieldTypeEnum.VARCHAR.getDefaultLength(), TableFieldTypeEnum.VARCHAR.getDefaultPoint(), "物料编码"},
                    {"name", "物料名称", TableFieldTypeEnum.VARCHAR.getType(), TableFieldTypeEnum.VARCHAR.getDefaultLength(), TableFieldTypeEnum.VARCHAR.getDefaultPoint(), "物料名称"},
                    {"standard", "规格", TableFieldTypeEnum.VARCHAR.getType(), TableFieldTypeEnum.VARCHAR.getDefaultLength(), TableFieldTypeEnum.VARCHAR.getDefaultPoint(), "规格"},
                    {"drawing_number", "图号", TableFieldTypeEnum.VARCHAR.getType(), TableFieldTypeEnum.VARCHAR.getDefaultLength(), TableFieldTypeEnum.VARCHAR.getDefaultPoint(), "图号"},
                    {"work_order", "工单号", TableFieldTypeEnum.VARCHAR.getType(), TableFieldTypeEnum.VARCHAR.getDefaultLength(), TableFieldTypeEnum.VARCHAR.getDefaultPoint(), "工单号"},
                    {"product_order_number", "生产订单号", TableFieldTypeEnum.VARCHAR.getType(), TableFieldTypeEnum.VARCHAR.getDefaultLength(), TableFieldTypeEnum.VARCHAR.getDefaultPoint(), "生产订单号"},
                    {"sale_order_number", "销售订单号", TableFieldTypeEnum.VARCHAR.getType(), TableFieldTypeEnum.VARCHAR.getDefaultLength(), TableFieldTypeEnum.VARCHAR.getDefaultPoint(), "销售订单号"},
                    {"finish_count", "完成数量", TableFieldTypeEnum.DOUBLE.getType(), TableFieldTypeEnum.DOUBLE.getDefaultLength(), TableFieldTypeEnum.DOUBLE.getDefaultPoint(), "完成数量"},
                    {"unqualified", "不良数量", TableFieldTypeEnum.DOUBLE.getType(), TableFieldTypeEnum.DOUBLE.getDefaultLength(), TableFieldTypeEnum.DOUBLE.getDefaultPoint(), "不良数量"},
                    {"user_nickname", "上报人姓名", TableFieldTypeEnum.VARCHAR.getType(), TableFieldTypeEnum.VARCHAR.getDefaultLength(), TableFieldTypeEnum.VARCHAR.getDefaultPoint(), "上报人姓名"},
                    {"operator_name", "操作员姓名", TableFieldTypeEnum.VARCHAR.getType(), TableFieldTypeEnum.VARCHAR.getDefaultLength(), TableFieldTypeEnum.VARCHAR.getDefaultPoint(), "操作员姓名"},
                    {"shift_type", "班次名称", TableFieldTypeEnum.VARCHAR.getType(), TableFieldTypeEnum.VARCHAR.getDefaultLength(), TableFieldTypeEnum.VARCHAR.getDefaultPoint(), "班次名称"},
                    {"device_code", "设备编码", TableFieldTypeEnum.VARCHAR.getType(), TableFieldTypeEnum.VARCHAR.getDefaultLength(), TableFieldTypeEnum.VARCHAR.getDefaultPoint(), "设备编码"},
                    {"device_id", "设备id", TableFieldTypeEnum.INT.getType(), TableFieldTypeEnum.INT.getDefaultLength(), TableFieldTypeEnum.INT.getDefaultPoint(), "设备id"}
            };
        } else if ("discreteProcessingQualityStatistics".equals(targetCode)) {
            // 指标视图：离散加工-质量统计
            return new Object[][]{
                    {"record_date", "日期", TableFieldTypeEnum.DATE.getType(), TableFieldTypeEnum.DATE.getDefaultLength(), TableFieldTypeEnum.DATE.getDefaultPoint(), "日期"},
                    {"work_order_number", "工单", TableFieldTypeEnum.VARCHAR.getType(), TableFieldTypeEnum.VARCHAR.getDefaultLength(), TableFieldTypeEnum.VARCHAR.getDefaultPoint(), "工单"},
                    {"defect_count_sum", "不良总数", TableFieldTypeEnum.DOUBLE.getType(), TableFieldTypeEnum.DOUBLE.getDefaultLength(), TableFieldTypeEnum.DOUBLE.getDefaultPoint(), "不良总数"},
                    {"defect_1", "不良rank1", TableFieldTypeEnum.VARCHAR.getType(), TableFieldTypeEnum.VARCHAR.getDefaultLength(), TableFieldTypeEnum.VARCHAR.getDefaultPoint(), "不良rank1"},
                    {"defect_2", "不良rank2", TableFieldTypeEnum.VARCHAR.getType(), TableFieldTypeEnum.VARCHAR.getDefaultLength(), TableFieldTypeEnum.VARCHAR.getDefaultPoint(), "不良rank2"},
                    {"defect_3", "不良rank3", TableFieldTypeEnum.VARCHAR.getType(), TableFieldTypeEnum.VARCHAR.getDefaultLength(), TableFieldTypeEnum.VARCHAR.getDefaultPoint(), "不良rank3"},
                    {"defect_4", "不良rank4", TableFieldTypeEnum.VARCHAR.getType(), TableFieldTypeEnum.VARCHAR.getDefaultLength(), TableFieldTypeEnum.VARCHAR.getDefaultPoint(), "不良rank4"},
                    {"defect_5", "不良rank5", TableFieldTypeEnum.VARCHAR.getType(), TableFieldTypeEnum.VARCHAR.getDefaultLength(), TableFieldTypeEnum.VARCHAR.getDefaultPoint(), "不良rank5"},
                    {"defect_6", "不良rank6", TableFieldTypeEnum.VARCHAR.getType(), TableFieldTypeEnum.VARCHAR.getDefaultLength(), TableFieldTypeEnum.VARCHAR.getDefaultPoint(), "不良rank6"},
                    {"defect_7", "不良rank7", TableFieldTypeEnum.VARCHAR.getType(), TableFieldTypeEnum.VARCHAR.getDefaultLength(), TableFieldTypeEnum.VARCHAR.getDefaultPoint(), "不良rank7"},
                    {"defect_8", "不良rank8", TableFieldTypeEnum.VARCHAR.getType(), TableFieldTypeEnum.VARCHAR.getDefaultLength(), TableFieldTypeEnum.VARCHAR.getDefaultPoint(), "不良rank8"},
                    {"defect_9", "不良rank9", TableFieldTypeEnum.VARCHAR.getType(), TableFieldTypeEnum.VARCHAR.getDefaultLength(), TableFieldTypeEnum.VARCHAR.getDefaultPoint(), "不良rank9"},
                    {"defect_10", "不良rank10", TableFieldTypeEnum.VARCHAR.getType(), TableFieldTypeEnum.VARCHAR.getDefaultLength(), TableFieldTypeEnum.VARCHAR.getDefaultPoint(), "不良rank10"},
                    {"name", "物料名称", TableFieldTypeEnum.VARCHAR.getType(), TableFieldTypeEnum.VARCHAR.getDefaultLength(), TableFieldTypeEnum.VARCHAR.getDefaultPoint(), "物料名称"},
                    {"standard", "物料规格", TableFieldTypeEnum.VARCHAR.getType(), TableFieldTypeEnum.VARCHAR.getDefaultLength(), TableFieldTypeEnum.VARCHAR.getDefaultPoint(), "物料规格"},
                    {"code", "物料编码", TableFieldTypeEnum.VARCHAR.getType(), TableFieldTypeEnum.VARCHAR.getDefaultLength(), TableFieldTypeEnum.VARCHAR.getDefaultPoint(), "物料编码"},
                    {"drawing_number", "图号", TableFieldTypeEnum.VARCHAR.getType(), TableFieldTypeEnum.VARCHAR.getDefaultLength(), TableFieldTypeEnum.VARCHAR.getDefaultPoint(), "图号"},
                    {"product_order_number", "生产订单号", TableFieldTypeEnum.VARCHAR.getType(), TableFieldTypeEnum.VARCHAR.getDefaultLength(), TableFieldTypeEnum.VARCHAR.getDefaultPoint(), "生产订单号"},
            };
        } else if ("discreteProcessingPersonnelSalaryStatistics".equals(targetCode)) {
            // 指标视图：离散加工-人员工资统计
            return new Object[][]{
                    {"device_name", "设备名称", TableFieldTypeEnum.VARCHAR.getType(), TableFieldTypeEnum.VARCHAR.getDefaultLength(), TableFieldTypeEnum.VARCHAR.getDefaultPoint(), "主键"},
                    {"fname", "工位名称", TableFieldTypeEnum.VARCHAR.getType(), TableFieldTypeEnum.VARCHAR.getDefaultLength(), TableFieldTypeEnum.VARCHAR.getDefaultPoint(), "物料编码"},
                    {"line_name", "产线名称", TableFieldTypeEnum.VARCHAR.getType(), TableFieldTypeEnum.VARCHAR.getDefaultLength(), TableFieldTypeEnum.VARCHAR.getDefaultPoint(), "缺陷编号"},
                    {"material_name", "物料名称", TableFieldTypeEnum.VARCHAR.getType(), TableFieldTypeEnum.VARCHAR.getDefaultLength(), TableFieldTypeEnum.VARCHAR.getDefaultPoint(), "缺陷名称"},
                    {"nick_name", "用户名", TableFieldTypeEnum.VARCHAR.getType(), TableFieldTypeEnum.VARCHAR.getDefaultLength(), TableFieldTypeEnum.VARCHAR.getDefaultPoint(), "记录日期"},
                    {"procedure_name", "工序名", TableFieldTypeEnum.VARCHAR.getType(), TableFieldTypeEnum.VARCHAR.getDefaultLength(), TableFieldTypeEnum.VARCHAR.getDefaultPoint(), "检验单数量,检验单据的数量"},
                    {"product_order_number", "生产订单号", TableFieldTypeEnum.VARCHAR.getType(), TableFieldTypeEnum.VARCHAR.getDefaultLength(), TableFieldTypeEnum.VARCHAR.getDefaultPoint(), "物料送检数量，检验单当前物料的送检数量"},
                    {"COUNT(id)", "产出数", TableFieldTypeEnum.DOUBLE.getType(), TableFieldTypeEnum.DOUBLE.getDefaultLength(), TableFieldTypeEnum.DOUBLE.getDefaultPoint(), "物料抽样数量，检验单当前物料的抽样数量"},
                    {"record_date", "日期", TableFieldTypeEnum.DATE.getType(), TableFieldTypeEnum.DATE.getDefaultLength(), TableFieldTypeEnum.DATE.getDefaultPoint(), "当前缺陷对应的不良数量"},
                    {"record_hour", "小时", TableFieldTypeEnum.INT.getType(), TableFieldTypeEnum.INT.getDefaultLength(), TableFieldTypeEnum.INT.getDefaultPoint(), "缺陷发生率，检验不良数量/抽样物料数量"},
                    {"relation_number", "关联单号", TableFieldTypeEnum.VARCHAR.getType(), TableFieldTypeEnum.VARCHAR.getDefaultLength(), TableFieldTypeEnum.VARCHAR.getDefaultPoint(), "缺陷发生率，检验不良数量/抽样物料数量"},
            };
        } else if ("assemblyLineProductionSchedule".equals(targetCode)) {
            // 指标视图：组装线-生产日表
            return new Object[][]{
                    {"sale_order_number", "销售订单号", TableFieldTypeEnum.VARCHAR.getType(), TableFieldTypeEnum.VARCHAR.getDefaultLength(), TableFieldTypeEnum.VARCHAR.getDefaultPoint(), "销售订单号"},
                    {"product_order_number", "生产订单号", TableFieldTypeEnum.VARCHAR.getType(), TableFieldTypeEnum.VARCHAR.getDefaultLength(), TableFieldTypeEnum.VARCHAR.getDefaultPoint(), "生产订单号"},
                    {"work_order_number", "工单号", TableFieldTypeEnum.VARCHAR.getType(), TableFieldTypeEnum.VARCHAR.getDefaultLength(), TableFieldTypeEnum.VARCHAR.getDefaultPoint(), "工单号"},
                    {"plan_quantity", "当日计划数量", TableFieldTypeEnum.DOUBLE.getType(), TableFieldTypeEnum.DOUBLE.getDefaultLength(), TableFieldTypeEnum.DOUBLE.getDefaultPoint(), "当日计划数量"},
                    {"produce_quantity", "当日产出数量", TableFieldTypeEnum.DOUBLE.getType(), TableFieldTypeEnum.DOUBLE.getDefaultLength(), TableFieldTypeEnum.DOUBLE.getDefaultPoint(), "当日产出数量"},
                    {"inspection_quantity", "当日检测数量", TableFieldTypeEnum.DOUBLE.getType(), TableFieldTypeEnum.DOUBLE.getDefaultLength(), TableFieldTypeEnum.DOUBLE.getDefaultPoint(), "当日检测数量"},
                    {"unqualified_quantity", "当日不良数量", TableFieldTypeEnum.DOUBLE.getType(), TableFieldTypeEnum.DOUBLE.getDefaultLength(), TableFieldTypeEnum.DOUBLE.getDefaultPoint(), "当日不良数量"},
                    {"inbound_quantity", "当日入库数量", TableFieldTypeEnum.DOUBLE.getType(), TableFieldTypeEnum.DOUBLE.getDefaultLength(), TableFieldTypeEnum.DOUBLE.getDefaultPoint(), "当日入库数量"},
                    {"direct_access_quantity", "当日直通数量", TableFieldTypeEnum.DOUBLE.getType(), TableFieldTypeEnum.DOUBLE.getDefaultLength(), TableFieldTypeEnum.DOUBLE.getDefaultPoint(), "当日直通数量"},
                    {"start_work_time", "当日开工时间(小程序录入)", TableFieldTypeEnum.DATETIME.getType(), TableFieldTypeEnum.DATETIME.getDefaultLength(), TableFieldTypeEnum.DATETIME.getDefaultPoint(), "当日开工时间(小程序录入)"},
                    {"working_people_quantity", "当日在岗人数", TableFieldTypeEnum.INT.getType(), TableFieldTypeEnum.INT.getDefaultLength(), TableFieldTypeEnum.INT.getDefaultPoint(), "当日在岗人数"},
                    {"actual_working_hour", "实际投入工时", TableFieldTypeEnum.DOUBLE.getType(), TableFieldTypeEnum.DOUBLE.getDefaultLength(), TableFieldTypeEnum.DOUBLE.getDefaultPoint(), "实际投入工时"},
                    {"theory_working_hour", "理论产出工时", TableFieldTypeEnum.DOUBLE.getType(), TableFieldTypeEnum.DOUBLE.getDefaultLength(), TableFieldTypeEnum.DOUBLE.getDefaultPoint(), "理论产出工时"},
                    {"qualified_rate", "当日合格率", TableFieldTypeEnum.DOUBLE.getType(), TableFieldTypeEnum.DOUBLE.getDefaultLength(), TableFieldTypeEnum.DOUBLE.getDefaultPoint(), "当日合格率"},
                    {"product_efficiency_rate", "生产效率", TableFieldTypeEnum.DOUBLE.getType(), TableFieldTypeEnum.DOUBLE.getDefaultLength(), TableFieldTypeEnum.DOUBLE.getDefaultPoint(), "生产效率"},
                    {"achievements", "绩效", TableFieldTypeEnum.DOUBLE.getType(), TableFieldTypeEnum.DOUBLE.getDefaultLength(), TableFieldTypeEnum.DOUBLE.getDefaultPoint(), "绩效"},
                    {"repair_quantity", "当日返修数", TableFieldTypeEnum.DOUBLE.getType(), TableFieldTypeEnum.DOUBLE.getDefaultLength(), TableFieldTypeEnum.DOUBLE.getDefaultPoint(), "当日返修数"},
                    {"repair_qualified_quantity", "当日返修良品数（维修判定: 不报废）", TableFieldTypeEnum.DOUBLE.getType(), TableFieldTypeEnum.DOUBLE.getDefaultLength(), TableFieldTypeEnum.DOUBLE.getDefaultPoint(), "当日返修良品数（维修判定: 不报废）"},
                    {"repair_quantity_rate", "当日返修率", TableFieldTypeEnum.DOUBLE.getType(), TableFieldTypeEnum.DOUBLE.getDefaultLength(), TableFieldTypeEnum.DOUBLE.getDefaultPoint(), "当日返修率"},
                    {"record_date", "记录日期", TableFieldTypeEnum.DATETIME.getType(), TableFieldTypeEnum.DATETIME.getDefaultLength(), TableFieldTypeEnum.DATETIME.getDefaultPoint(), "记录日期"},
                    {"time", "最新统计时间", TableFieldTypeEnum.DATETIME.getType(), TableFieldTypeEnum.DATETIME.getDefaultLength(), TableFieldTypeEnum.DATETIME.getDefaultPoint(), "最新统计时间"},
                    {"unqualified_record_quantity", "不良品记录数量: 流水码去重", TableFieldTypeEnum.DOUBLE.getType(), TableFieldTypeEnum.DOUBLE.getDefaultLength(), TableFieldTypeEnum.DOUBLE.getDefaultPoint(), "不良品记录数量: 流水码去重"},
                    {"unqualified_record_item_quantity", "不良项记录数量: 流水码不去重", TableFieldTypeEnum.DOUBLE.getType(), TableFieldTypeEnum.DOUBLE.getDefaultLength(), TableFieldTypeEnum.DOUBLE.getDefaultPoint(), "不良项记录数量: 流水码去重"},
            };
        }
        return null;
    }

    private TargetGroupViewInsertDTO getTargetGroupView(Integer modelId, CommonType commonType, Object[][] tableFields) {
        if (tableFields == null) {
            return null;
        }
        String viewName = String.valueOf(commonType.getCode());

        List<TableFieldCreateDTO> addTableFields = new ArrayList<>();
        List<String> fieldCodes = new ArrayList<>();
        for (Object[] fields : tableFields) {
            fieldCodes.add((String) fields[0]);
            addTableFields.add(TableFieldCreateDTO.builder()
                    .fieldCode((String) fields[0])
                    .fieldName((String) fields[1])
                    .fieldType((String) fields[2])
                    .fieldLength((Integer) fields[3])
                    .fieldPoint((Integer) fields[4])
                    .fieldRemark((String) fields[5])
                    .build()
            );
        }
        return TargetGroupViewInsertDTO.builder()
                .modelId(modelId)
                .targetName(String.valueOf(commonType.getCode()))
                .targetCnname(String.valueOf(commonType.getName()))
                .script(String.valueOf(commonType.getType()))
                .tableDTO(TableViewCreateDTO.builder()
                        .tableName("v_" + viewName)
                        .tableRemark(String.valueOf(commonType.getName()))
                        .fields(addTableFields)
                        .build())
                .build();
    }
}
