package com.yelink.dfs.migration;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.yelink.dfs.constant.Constant;
import com.yelink.dfs.constant.sys.TableFieldTypeEnum;
import com.yelink.dfs.entity.model.ModelEntity;
import com.yelink.dfs.entity.target.TargetModelEntity;
import com.yelink.dfs.metrics.config.DfsModelType;
import com.yelink.dfs.open.v1.metrics.dto.TableViewCreateDTO;
import com.yelink.dfs.open.v1.metrics.dto.TargetGroupViewInsertDTO;
import com.yelink.dfs.open.v1.metrics.vo.TableFieldCreateVO;
import com.yelink.dfs.service.model.ModelService;
import com.yelink.dfs.service.target.TargetModelService;
import lombok.extern.slf4j.Slf4j;
import org.flywaydb.core.api.migration.BaseJavaMigration;
import org.flywaydb.core.api.migration.Context;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;


/**
 * 维表视图
 *
 * <AUTHOR>
 */
@Slf4j
public class V3_22_1_101__JavaMigration extends BaseJavaMigration {

    @Override
    public void migrate(Context context) {
        ModelService modelService = SpringUtil.getBean(ModelService.class);
        ModelEntity alarmModel = modelService.lambdaQuery().eq(ModelEntity::getType, Constant.BASE_TARGET_GROUP).eq(ModelEntity::getCode, DfsModelType.ALARM.code).one();
        TargetModelService targetModelService = SpringUtil.getBean(TargetModelService.class);
        List<TargetGroupViewInsertDTO> addViews = new ArrayList<>();
        // 1
        if(alarmModel == null) {
            log.warn("model:告警指标模型不存在， 新插入");
            alarmModel = ModelEntity.builder().pid(-1).type(Constant.BASE_TARGET_GROUP).code(DfsModelType.ALARM.code).name(DfsModelType.ALARM.name).seq(17).build();
            modelService.save(alarmModel);

        }
        String alarmTableName = "v_alarm";
        boolean existsDimensionDevice = targetModelService.lambdaQuery().eq(TargetModelEntity::getTargetName, alarmTableName).exists();
        if (existsDimensionDevice) {
            log.warn("告警记录视图已存在");
        } else {
            addViews.add(TargetGroupViewInsertDTO.builder()
                    .modelId(alarmModel.getId())
                    .targetName(alarmTableName)
                    .targetCnname("告警记录视图")
                    .script("SELECT \n" +
                            "\talarm_id,\n" +
                            "\talarm_code_id,\n" +
                            "\taggregate_code,\n" +
                            "\talarm_classify_id,\n" +
                            "\talarm_classify_code,\n" +
                            "\talarm_classify_name,\n" +
                            "\talarm_definition_code,\n" +
                            "\talarm_des,\n" +
                            "\talarm_add_type,\n" +
                            "\talarm_type,\n" +
                            "\talarm_time,\n" +
                            "\talarm_report_personnel,\n" +
                            "\talarm_detail,\n" +
                            "\talarm_value,\n" +
                            "\talarm_explain,\n" +
                            "\talarm_recovery_type,\n" +
                            "\talarm_recovery_time,\n" +
                            "\talarm_recovery_username,\n" +
                            "\talarm_level,\n" +
                            "\talarm_level_name,\n" +
                            "\thigher_notification,\n" +
                            "\tlast_upgrade_time\n" +
                            "\tstart_deal_time,\n" +
                            "\tdeal_time,\n" +
                            "\tdeal_name,\n" +
                            "\tdeal_des,\n" +
                            "\tdeal_state,\n" +
                            "\ttarget_model_id,\n" +
                            "\ttarget_name,\n" +
                            "\ttarget_cnname,\n" +
                            "\tdevice_id,\n" +
                            "\tdevice_code,\n" +
                            "\tdevice_name,\n" +
                            "\twork_order_number,\n" +
                            "\tinfluence_work_hour,\n" +
                            "\tis_stop_line,\n" +
                            "\timprovement_measure,\n" +
                            "\talarm_extend_field_one,\n" +
                            "\talarm_extend_field_two,\n" +
                            "\talarm_extend_field_three,\n" +
                            "\talarm_extend_field_four\n" +
                            "\t\n" +
                            "FROM `dfs_alarm`")
                    .tableDTO(TableViewCreateDTO.builder()
                            .tableName(alarmTableName)
                            .fields(Stream.of(
                                    TableFieldCreateVO.builder().fieldCode("alarm_id").fieldName("告警id").fieldType(TableFieldTypeEnum.INT).build(),
                                    TableFieldCreateVO.builder().fieldCode("alarm_code_id").fieldName("告警编号ID").fieldType(TableFieldTypeEnum.VARCHAR).build(),
                                    TableFieldCreateVO.builder().fieldCode("aggregate_code").fieldName("告警聚合码：（设备的故障代码）").fieldType(TableFieldTypeEnum.VARCHAR).build(),
                                    TableFieldCreateVO.builder().fieldCode("alarm_classify_id").fieldName("告警分类（deviceAlarm-设备告警， sysAlarm-系统告警）").fieldType(TableFieldTypeEnum.VARCHAR).build(),
                                    TableFieldCreateVO.builder().fieldCode("alarm_classify_code").fieldName("告警分类编号").fieldType(TableFieldTypeEnum.VARCHAR).build(),
                                    TableFieldCreateVO.builder().fieldCode("alarm_classify_name").fieldName("告警分类名称").fieldType(TableFieldTypeEnum.VARCHAR).build(),
                                    TableFieldCreateVO.builder().fieldCode("alarm_definition_code").fieldName("告警定义id").fieldType(TableFieldTypeEnum.VARCHAR).build(),
                                    TableFieldCreateVO.builder().fieldCode("alarm_des").fieldName("告警描述:设备具体告警信息描述").fieldType(TableFieldTypeEnum.VARCHAR).build(),
                                    TableFieldCreateVO.builder().fieldCode("alarm_add_type").fieldName("告警添加类型（auto-自动 manual-手动）").fieldType(TableFieldTypeEnum.VARCHAR).build(),
                                    TableFieldCreateVO.builder().fieldCode("alarm_type").fieldName("告警类型 faultAlarm-故障告警 recoveryAlarm-恢复告警").fieldType(TableFieldTypeEnum.VARCHAR).build(),
                                    TableFieldCreateVO.builder().fieldCode("alarm_time").fieldName("告警时间").fieldType(TableFieldTypeEnum.DATETIME).build(),
                                    TableFieldCreateVO.builder().fieldCode("alarm_report_personnel").fieldName("告警上报人员（默认为系统上报）").fieldType(TableFieldTypeEnum.VARCHAR).build(),
                                    TableFieldCreateVO.builder().fieldCode("alarm_detail").fieldName("告警显示详情（json）").fieldType(TableFieldTypeEnum.VARCHAR).build(),
                                    TableFieldCreateVO.builder().fieldCode("alarm_value").fieldName("告警数据_json格式").fieldType(TableFieldTypeEnum.VARCHAR).build(),
                                    TableFieldCreateVO.builder().fieldCode("alarm_explain").fieldName("告警说明").fieldType(TableFieldTypeEnum.VARCHAR).build(),
                                    TableFieldCreateVO.builder().fieldCode("alarm_recovery_type").fieldName("告警恢复类型manualRecovery-手动恢复 autoRecovery-自动恢复").fieldType(TableFieldTypeEnum.VARCHAR).build(),
                                    TableFieldCreateVO.builder().fieldCode("alarm_recovery_time").fieldName("告警恢复时间").fieldType(TableFieldTypeEnum.DATETIME).build(),
                                    TableFieldCreateVO.builder().fieldCode("alarm_recovery_username").fieldName("告警恢复人").fieldType(TableFieldTypeEnum.VARCHAR).build(),
                                    TableFieldCreateVO.builder().fieldCode("alarm_level").fieldName("告警级别").fieldType(TableFieldTypeEnum.VARCHAR).build(),
                                    TableFieldCreateVO.builder().fieldCode("alarm_level_name").fieldName("告警级别名称").fieldType(TableFieldTypeEnum.VARCHAR).build(),
                                    TableFieldCreateVO.builder().fieldCode("higher_notification").fieldName("上级通知人").fieldType(TableFieldTypeEnum.VARCHAR).build(),
                                    TableFieldCreateVO.builder().fieldCode("start_deal_time").fieldName("上次告警升级时间").fieldType(TableFieldTypeEnum.DATETIME).build(),
                                    TableFieldCreateVO.builder().fieldCode("deal_time").fieldName("处理时间").fieldType(TableFieldTypeEnum.DATETIME).build(),
                                    TableFieldCreateVO.builder().fieldCode("deal_name").fieldName("处理人姓名").fieldType(TableFieldTypeEnum.VARCHAR).build(),
                                    TableFieldCreateVO.builder().fieldCode("deal_des").fieldName("处理描述").fieldType(TableFieldTypeEnum.VARCHAR).build(),
                                    TableFieldCreateVO.builder().fieldCode("deal_state").fieldName("处理状态：0-未处理 1-处理中 2-已处理").fieldType(TableFieldTypeEnum.INT).build(),
                                    TableFieldCreateVO.builder().fieldCode("target_model_id").fieldName("指标模型id（关联具体发生告警的指标）").fieldType(TableFieldTypeEnum.INT).build(),
                                    TableFieldCreateVO.builder().fieldCode("target_name").fieldName("指标名").fieldType(TableFieldTypeEnum.VARCHAR).build(),
                                    TableFieldCreateVO.builder().fieldCode("target_cnname").fieldName("指标中文名称").fieldType(TableFieldTypeEnum.VARCHAR).build(),
                                    TableFieldCreateVO.builder().fieldCode("device_id").fieldName("设备id").fieldType(TableFieldTypeEnum.INT).build(),
                                    TableFieldCreateVO.builder().fieldCode("device_code").fieldName("设备编号").fieldType(TableFieldTypeEnum.VARCHAR).build(),
                                    TableFieldCreateVO.builder().fieldCode("device_name").fieldName("设备名称").fieldType(TableFieldTypeEnum.VARCHAR).build(),
                                    TableFieldCreateVO.builder().fieldCode("work_order_number").fieldName("工单编号").fieldType(TableFieldTypeEnum.VARCHAR).build(),
                                    TableFieldCreateVO.builder().fieldCode("influence_work_hour").fieldName("影响工时(unit: h)").fieldType(TableFieldTypeEnum.DOUBLE).build(),
                                    TableFieldCreateVO.builder().fieldCode("is_stop_line").fieldName("是否停线").fieldType(TableFieldTypeEnum.INT).build(),
                                    TableFieldCreateVO.builder().fieldCode("improvement_measure").fieldName("改善措施").fieldType(TableFieldTypeEnum.VARCHAR).build(),
                                    TableFieldCreateVO.builder().fieldCode("alarm_extend_field_one").fieldName("告警扩展字段1").fieldType(TableFieldTypeEnum.VARCHAR).build(),
                                    TableFieldCreateVO.builder().fieldCode("alarm_extend_field_two").fieldName("告警扩展字段2").fieldType(TableFieldTypeEnum.VARCHAR).build(),
                                    TableFieldCreateVO.builder().fieldCode("alarm_extend_field_three").fieldName("告警扩展字段3").fieldType(TableFieldTypeEnum.VARCHAR).build(),
                                    TableFieldCreateVO.builder().fieldCode("alarm_extend_field_four").fieldName("告警扩展字段4").fieldType(TableFieldTypeEnum.VARCHAR).build()
                            ).map(TableFieldCreateVO::covert).collect(Collectors.toList()))
                            .build()
                    )
                    .build()
            );
        }

        if(CollUtil.isNotEmpty(addViews)) {
            targetModelService.batchAddTargetGroupView(addViews);
        }

    }

}

