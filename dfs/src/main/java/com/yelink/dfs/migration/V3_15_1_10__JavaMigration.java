package com.yelink.dfs.migration;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yelink.dfs.entity.product.BomRawMaterialEntity;
import com.yelink.dfs.entity.product.dto.BomSelectDTO;
import com.yelink.dfs.mapper.sys.TableMapper;
import com.yelink.dfs.service.product.BomRawMaterialService;
import com.yelink.dfs.service.rule.NumberRuleService;
import com.yelink.dfs.service.order.WorkOrderMaterialListMaterialService;
import com.yelink.dfs.service.order.WorkOrderMaterialListService;
import com.yelink.dfs.service.product.BomService;
import com.yelink.dfs.utils.SpringUtil;
import com.yelink.dfscommon.constant.dfs.rule.NumberRuleCodeDTO;
import com.yelink.dfscommon.constant.dfs.rule.NumberRulesConfigEntity;
import com.yelink.dfscommon.entity.dfs.WorkOrderMaterialListEntity;
import com.yelink.dfscommon.entity.dfs.WorkOrderMaterialListMaterialEntity;
import com.yelink.dfscommon.utils.JacksonUtil;
import org.flywaydb.core.api.migration.BaseJavaMigration;
import org.flywaydb.core.api.migration.Context;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;
import java.util.stream.Collectors;


/**
 * 内置角色权限
 *
 * <AUTHOR>
 */
public class V3_15_1_10__JavaMigration extends BaseJavaMigration {

    @Override
    public void migrate(Context context) {
        // qms新增单据编码规则
        addQmsRules();
        // 历史生产工单用料清单数据关联的bom需要关联到新加的字段上去
        refreshHistoryProductOrderMaterialListData();
    }

    private void addQmsRules() {
        // 1. 编码规则增加“质量管理-来料检验单”。
        // 默认编码规则：固定信息（LLJ）-当前日期（yyyyMMdd）-自动生成序号（位数3；跨天归1；起始计数1）
        //
        // 2. 编码规则增加“质量管理-生产检验单”。
        // 默认编码规则：固定信息（SCJ）-当前日期（yyyyMMdd）-自动生成序号（位数3；跨天归1；起始计数1）
        //
        // 3. 编码规则增加“质量管理-生产巡检单”。
        // 默认编码规则：固定信息（SCXJ）-当前日期（yyyyMMdd）-自动生成序号（位数3；跨天归1；起始计数1）
        //
        // 4. 编码规则增加“质量管理-销售检验单”。
        // 默认编码规则：固定信息（XSJ）-当前日期（yyyyMMdd）-自动生成序号（位数3；跨天归1；起始计数1）
        //
        // 5. 编码规则增加“质量管理-其他检验单”。
        // 默认编码规则：固定信息（QTJ）-当前日期（yyyyMMdd）-自动生成序号（位数3；跨天归1；起始计数1）
        NumberRuleService ruleService = SpringUtil.getBean(NumberRuleService.class);
        TableMapper tableMapper = SpringUtil.getBean(TableMapper.class);
        String sb = "INSERT INTO `dfs_config_rule`(`rule_type`, `rule_type_name`) " +
                "VALUES (500, '质量管理-来料检验单')," +
                "(501, '质量管理-生产检验单')," +
                "(502, '质量管理-生产巡检单')," +
                "(503, '质量管理-销售检验单')," +
                "(504, '质量管理-其他检验单');";
        tableMapper.executeSql(sb.toString());

        Date date = new Date();
        String createBy = "admin";
        String name = "编码规则";
        List<NumberRulesConfigEntity> defaultRules = new ArrayList<>();
        defaultRules.add(NumberRulesConfigEntity.builder()
                .type("500")
                .prefixDetail(getNumberRulePrefixDetail("LLJ"))
                .name(name).createBy(createBy).createTime(date).isDefault(true).build());
        defaultRules.add(NumberRulesConfigEntity.builder()
                .type("501")
                .prefixDetail(getNumberRulePrefixDetail("SCJ"))
                .name(name).createBy(createBy).createTime(date).isDefault(true).build());
        defaultRules.add(NumberRulesConfigEntity.builder()
                .type("502")
                .prefixDetail(getNumberRulePrefixDetail("SCXJ"))
                .name(name).createBy(createBy).createTime(date).isDefault(true).build());
        defaultRules.add(NumberRulesConfigEntity.builder()
                .type("503")
                .prefixDetail(getNumberRulePrefixDetail("XSJ"))
                .name(name).createBy(createBy).createTime(date).isDefault(true).build());
        defaultRules.add(NumberRulesConfigEntity.builder()
                .type("504")
                .prefixDetail(getNumberRulePrefixDetail("QTJ"))
                .name(name).createBy(createBy).createTime(date).isDefault(true).build());
        ruleService.saveBatch(defaultRules);
    }

    /**
     * 组装编码规则json
     * // [{"autoIncrementConfigureType":"","code":5,"example":"SCDD","initValue":1,"name":"固定信息","rule":"SCDD","uuid":""},
     * // {"autoIncrementConfigureType":"","code":2,"example":"20240813","initValue":1,"name":"当前日期","rule":"yyyyMMdd","uuid":""},
     * // {"autoIncrementConfigureType":"day","code":4,"example":"001","initValue":1,"name":"自动生成序号","rule":"3","uuid":"c5ada309-f3ce-4bbd-b41c-9ddbe91d28dd"}]
     */
    private static String getNumberRulePrefixDetail(String fixedInfo) {
        List<NumberRuleCodeDTO> prefixDetailList = new ArrayList<>();
        NumberRuleCodeDTO fixedInfoDto = NumberRuleCodeDTO.builder().code("5").name("固定信息").rule(fixedInfo).initValue(1).example(fixedInfo).build();
        NumberRuleCodeDTO dateDto = NumberRuleCodeDTO.builder().code("2").name("当前日期").rule("yyyyMMdd").initValue(1).example("20240813").build();
        NumberRuleCodeDTO autoSeqDto = NumberRuleCodeDTO.builder().code("4").name("自动生成序号").autoIncrementConfigureType("day").uuid(UUID.randomUUID().toString()).rule("3").initValue(1).example("001").build();
        prefixDetailList.add(fixedInfoDto);
        prefixDetailList.add(dateDto);
        prefixDetailList.add(autoSeqDto);
        return JacksonUtil.toJSONString(prefixDetailList);
    }


    private void refreshHistoryProductOrderMaterialListData() {
        WorkOrderMaterialListMaterialService listMaterialService = SpringUtil.getBean(WorkOrderMaterialListMaterialService.class);
        List<WorkOrderMaterialListMaterialEntity> materialListMaterialEntities = listMaterialService.lambdaQuery()
                .select(WorkOrderMaterialListMaterialEntity::getBomRawMaterialId, WorkOrderMaterialListMaterialEntity::getId, WorkOrderMaterialListMaterialEntity::getMaterialListId)
                .isNotNull(WorkOrderMaterialListMaterialEntity::getBomRawMaterialId)
                .list();
        List<Integer> bomRawMaterialIds = materialListMaterialEntities.stream().map(WorkOrderMaterialListMaterialEntity::getBomRawMaterialId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(bomRawMaterialIds)) {
            return;
        }
//        BomService bomService = SpringUtil.getBean(BomService.class);
//        BomSelectDTO build = BomSelectDTO.builder()
//                .bomRawMaterialIds(bomRawMaterialIds)
//                .build();
//        Page<com.yelink.dfs.entity.product.BomEntity> bomPage = bomService.list(build);

        BomRawMaterialService bomRawMaterialService = SpringUtil.getBean(BomRawMaterialService.class);
        List<BomRawMaterialEntity> bomRawMaterialEntities = bomRawMaterialService.lambdaQuery()
                .select(BomRawMaterialEntity::getBomId, BomRawMaterialEntity::getId)
                .in(BomRawMaterialEntity::getId, bomRawMaterialIds).list();
//        List<com.yelink.dfs.entity.product.BomEntity> bomList = bomPage.getRecords();
        if (CollectionUtils.isEmpty(bomRawMaterialEntities)) {
            return;
        }
        Map<Integer, Integer> bomMap = bomRawMaterialEntities.stream()
                .collect(Collectors.toMap(BomRawMaterialEntity::getId, BomRawMaterialEntity::getBomId));
        WorkOrderMaterialListService WorkOrderMaterialListService = cn.hutool.extra.spring.SpringUtil.getBean(WorkOrderMaterialListService.class);
        Map<Integer, List<WorkOrderMaterialListMaterialEntity>> map = materialListMaterialEntities.stream().collect(Collectors.groupingBy(WorkOrderMaterialListMaterialEntity::getMaterialListId));
        for (Map.Entry<Integer, List<WorkOrderMaterialListMaterialEntity>> entry : map.entrySet()) {
            WorkOrderMaterialListMaterialEntity WorkOrderMaterialListMaterialEntity = entry.getValue().get(0);
            Integer bomId = bomMap.get(WorkOrderMaterialListMaterialEntity.getBomRawMaterialId());
            if (Objects.isNull(bomId)) {
                continue;
            }
            WorkOrderMaterialListService.lambdaUpdate().eq(WorkOrderMaterialListEntity::getMaterialListId, entry.getKey())
                    .set(WorkOrderMaterialListEntity::getBomId, bomId)
                    .set(WorkOrderMaterialListEntity::getBomVersionRevision, 0)
                    .update();
        }
    }



}

