package com.yelink.dfs.migration;

import com.yelink.dfs.service.common.config.OrderPushDownConfigValueDictService;
import com.yelink.dfs.service.common.config.OrderPushDownConfigValueService;
import com.yelink.dfs.utils.SpringUtil;
import com.yelink.dfscommon.entity.dfs.OrderPushDownConfigValueDictEntity;
import com.yelink.dfscommon.entity.dfs.OrderPushDownConfigValueEntity;
import org.flywaydb.core.api.migration.BaseJavaMigration;
import org.flywaydb.core.api.migration.Context;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;


/**
 * 内置角色权限
 *
 * <AUTHOR>
 */
public class V3_17_1_10__JavaMigration extends BaseJavaMigration {

    @Override
    public void migrate(Context context) {
        // 下推配置目标单据配置中 新增 携带源单据附件 配置
        addHistoryPushDownDictConf();
        addHistoryPushDownConf();
    }

    private void addHistoryPushDownConf() {
        List<OrderPushDownConfigValueEntity> list = new ArrayList<>();
        List<String> collect = Stream.of(
                "saleOrder.pushDownConfig.workOrder.craftPush",
                "production.productOrderPushDownConfig.workOrder.craftPush",
                "production.productOrderPushDownConfig.workOrder.productOrderDaily",
                "saleOrder.pushDownConfig.productOrder.bomPush",
                "saleOrder.pushDownConfig.purchaseRequest.pushBatch",
                "saleOrder.pushDownConfig.purchaseOrder.pushBatch",
                "purchase.purchaseRequestPushDownConfig.purchaseOrder.push"
        ).collect(Collectors.toList());
        OrderPushDownConfigValueService valueService = SpringUtil.getBean(OrderPushDownConfigValueService.class);
        // 根据itemId分组
        List<OrderPushDownConfigValueEntity> valueEntities = valueService.lambdaQuery()
                .in(OrderPushDownConfigValueEntity::getConfigFullPathCode, collect)
                .select(OrderPushDownConfigValueEntity::getConfigFullPathCode, OrderPushDownConfigValueEntity::getItemId)
                .list();
        Map<Integer, List<OrderPushDownConfigValueEntity>> map = valueEntities.stream().collect(Collectors.groupingBy(OrderPushDownConfigValueEntity::getItemId));
        for (Map.Entry<Integer, List<OrderPushDownConfigValueEntity>> listEntry : map.entrySet()) {
            Integer itemId = listEntry.getKey();
            OrderPushDownConfigValueEntity valueEntity = listEntry.getValue().get(0);
            String configFullPathCode = valueEntity.getConfigFullPathCode();
            list.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(itemId)
                    .valueCode("carryFile")
                    .valueName("携带源单据附件")
                    .inputType("select")
                    .optionValuesType("table")
                    .valueFullPathCode(configFullPathCode + ".carryFile")
                    .configFullPathCode(configFullPathCode)
                    .optionValues("[{\"value\":true,\"label\":\"携带\"},{\"value\":false,\"label\":\"不携带\"}]")
                    .value("true")
                    .build());
        }
        valueService.saveBatch(list);
    }


    private void addHistoryPushDownDictConf() {
        List<String> collect = Stream.of(
                "saleOrder.pushDownConfig.workOrder.craftPush",
                "production.productOrderPushDownConfig.workOrder.craftPush",
                "production.productOrderPushDownConfig.workOrder.productOrderDaily",
                "saleOrder.pushDownConfig.productOrder.bomPush",
                "saleOrder.pushDownConfig.purchaseRequest.pushBatch",
                "saleOrder.pushDownConfig.purchaseOrder.pushBatch",
                "purchase.purchaseRequestPushDownConfig.purchaseOrder.push"
        ).collect(Collectors.toList());
        List<OrderPushDownConfigValueDictEntity> dictlist = new ArrayList<>();
        OrderPushDownConfigValueDictService valueDictService = SpringUtil.getBean(OrderPushDownConfigValueDictService.class);
        List<OrderPushDownConfigValueDictEntity> dictValueEntities = valueDictService.lambdaQuery()
                .in(OrderPushDownConfigValueDictEntity::getConfigFullPathCode, collect)
                .select(OrderPushDownConfigValueDictEntity::getConfigFullPathCode)
                .list();
        Map<String, List<OrderPushDownConfigValueDictEntity>> dictMap = dictValueEntities.stream().collect(Collectors.groupingBy(OrderPushDownConfigValueDictEntity::getConfigFullPathCode));
        for (Map.Entry<String, List<OrderPushDownConfigValueDictEntity>> listEntry : dictMap.entrySet()) {
            OrderPushDownConfigValueDictEntity valueEntity = listEntry.getValue().get(0);
            String configFullPathCode = valueEntity.getConfigFullPathCode();
            dictlist.add(OrderPushDownConfigValueDictEntity.builder()
                    .valueCode("carryFile")
                    .valueName("携带源单据附件")
                    .inputType("select")
                    .optionValuesType("table")
                    .valueFullPathCode(configFullPathCode + ".carryFile")
                    .configFullPathCode(configFullPathCode)
                    .value("true")
                    .optionValues("[{\"value\":true,\"label\":\"携带\"},{\"value\":false,\"label\":\"不携带\"}]")
                    .build());
        }
        valueDictService.saveBatch(dictlist);
    }


}

