package com.yelink.dfs.migration;

import com.yelink.dfs.constant.user.RoleEnum;
import com.yelink.dfs.entity.user.SysRoleEntity;
import com.yelink.dfs.service.common.OperationLogService;
import com.yelink.dfs.service.sys.SysService;
import com.yelink.dfs.service.user.SysRoleService;
import com.yelink.dfs.utils.SpringUtil;
import org.flywaydb.core.api.migration.BaseJavaMigration;
import org.flywaydb.core.api.migration.Context;

import java.util.Arrays;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;


/**
 * 内置角色权限
 * <AUTHOR>
 */
public class V3_12_1_16__JavaMigration extends BaseJavaMigration {

    @Override
    public void migrate(Context context) {
        SysService service = SpringUtil.getBean(SysService.class);
        SysRoleService roleService = SpringUtil.getBean(SysRoleService.class);
        OperationLogService operationLogService = SpringUtil.getBean(OperationLogService.class);

        assert service != null;
        assert roleService != null;
        assert operationLogService != null;
        // 需要初始化， 精制同步过来了角色, 但此时系统还没使用, 默认要添加权限
        boolean needInit = !operationLogService.lambdaQuery().exists();
        // 查角色
        Set<String> existRoleName = roleService.lambdaQuery().list().stream().map(SysRoleEntity::getName).collect(Collectors.toSet());
        // 新角色
        List<RoleEnum> roleEnums = Arrays.asList(
                RoleEnum.SALE,
                RoleEnum.PURCHASE,
                RoleEnum.PRODUCT,
                RoleEnum.DESIGN,
                RoleEnum.WAREHOUSE,
                RoleEnum.INSPECTION,
                RoleEnum.FINANCE,
                RoleEnum.OPERATE,
                RoleEnum.DEVICE,
                RoleEnum.SUPPLIER
        );
        for (RoleEnum roleEnum : roleEnums) {
            // 是否已存在角色
            boolean existRole = existRoleName.contains(roleEnum.getName());
            if(!existRole) {
                SysRoleEntity role = SysRoleEntity.builder().name(roleEnum.getName()).description("系统内置-" + roleEnum.getName()).build();
                roleService.insert(role);
            }
            if(needInit || !existRole) {
                service.runSqlBySpringUtils("db/migration/script/role_permission/" + roleEnum.name() +".sql");
            }
        }


    }

}

