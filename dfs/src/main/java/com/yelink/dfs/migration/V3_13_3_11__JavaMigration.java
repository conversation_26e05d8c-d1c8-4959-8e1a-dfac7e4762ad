package com.yelink.dfs.migration;

import com.yelink.dfs.entity.order.WorkOrderEntity;
import com.yelink.dfs.service.common.config.OrderPushDownRecordService;
import com.yelink.dfs.service.order.WorkOrderService;
import com.yelink.dfs.utils.SpringUtil;
import com.yelink.dfscommon.entity.dfs.OrderPushDownRecordEntity;
import org.flywaydb.core.api.migration.BaseJavaMigration;
import org.flywaydb.core.api.migration.Context;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * 内置角色权限
 *
 * <AUTHOR>
 */
public class V3_13_3_11__JavaMigration extends BaseJavaMigration {

    @Override
    public void migrate(Context context) {
        // 历史生产订单下推生产工单的下推记录需要更新到对应的生产工单的下推批数上
        refreshWorkOrderPushTimesForHistoryPushDownRecord();
    }

    private void refreshWorkOrderPushTimesForHistoryPushDownRecord() {
        OrderPushDownRecordService pushDownRecordService = SpringUtil.getBean(OrderPushDownRecordService.class);
        WorkOrderService workOrderService = SpringUtil.getBean(WorkOrderService.class);
        // 根据生产订单分组
        Map<String, List<OrderPushDownRecordEntity>> map = pushDownRecordService
                .lambdaQuery()
                .eq(OrderPushDownRecordEntity::getSourceOrderType, "productOrder")
                .eq(OrderPushDownRecordEntity::getTargetOrderType, "workOrder")
                .select(OrderPushDownRecordEntity::getTargetOrderNumber, OrderPushDownRecordEntity::getRecordDate, OrderPushDownRecordEntity::getSourceOrderNumber)
                .list().stream()
                .collect(Collectors.groupingBy(OrderPushDownRecordEntity::getSourceOrderNumber));
        for (Map.Entry<String, List<OrderPushDownRecordEntity>> entry : map.entrySet()) {
            List<OrderPushDownRecordEntity> recordEntities = entry.getValue();
            HashMap<Date, Integer> recordDateMap = new HashMap<>();
            List<Date> recordDates = recordEntities.stream().map(OrderPushDownRecordEntity::getRecordDate).distinct().sorted().collect(Collectors.toList());

            int pushTimes = 1;
            for (Date recordDate : recordDates) {
                recordDateMap.put(recordDate, pushTimes);
                pushTimes = recordDateMap.get(recordDate) + 1;
            }
            for (OrderPushDownRecordEntity recordEntity : recordEntities) {
                Integer i = recordDateMap.get(recordEntity.getRecordDate());
                workOrderService.lambdaUpdate().eq(WorkOrderEntity::getWorkOrderNumber, recordEntity.getTargetOrderNumber())
                        .set(WorkOrderEntity::getPushTimes, i)
                        .update();
            }
        }
    }


}

