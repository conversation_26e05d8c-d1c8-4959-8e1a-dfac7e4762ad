package com.yelink.dfs.migration;

import com.yelink.dfs.constant.Constant;
import com.yelink.dfs.service.common.DictService;
import com.yelink.dfs.service.mongodb.MongodbService;
import com.yelink.dfs.utils.SpringUtil;
import com.yelink.dfscommon.entity.dfs.DictEntity;
import org.flywaydb.core.api.migration.BaseJavaMigration;
import org.flywaydb.core.api.migration.Context;
import org.springframework.util.CollectionUtils;

import java.util.List;


/**
 * @Description: 历史物料类型加索引
 * @Author: hxc
 * @Date: 2024/2/23
 */
public class V3_2_2__JavaMigration extends BaseJavaMigration {

    @Override
    public void migrate(Context context) {
        MongodbService mongodbService = SpringUtil.getBean(MongodbService.class);
        DictService dictService = SpringUtil.getBean(DictService.class);
//        List<DictEntity> list = dictService.commonTypeList(DictDTO.builder().type("materialType").build()).getRecords();
        List<DictEntity> list = dictService.lambdaQuery().select(DictEntity::getId).eq(DictEntity::getType, "materialType").list();
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        for (DictEntity dictEntity : list) {
            //增加标识关联指标表
            mongodbService.addIndices("code", Constant.MATERIAL_TYPE_TARGET + dictEntity.getId());
        }
    }
}
