package com.yelink.dfs.migration;

import cn.hutool.core.collection.CollUtil;
import com.yelink.dfs.entity.energy.manage.EnergyTimePeriodConfigEntity;
import com.yelink.dfs.entity.energy.manage.EnergyTimePeriodEntity;
import com.yelink.dfs.service.energy.manage.EnergyTimePeriodConfigService;
import com.yelink.dfs.service.energy.manage.EnergyTimePeriodService;
import com.yelink.dfs.utils.SpringUtil;
import com.yelink.dfscommon.constant.EnergyTimePeriodEnum;
import com.yelink.dfscommon.constant.ResponseException;
import org.flywaydb.core.api.migration.BaseJavaMigration;
import org.flywaydb.core.api.migration.Context;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;


/**
 * 用电时段表 添加 深谷项
 * <AUTHOR>
 */
public class V3_9_6__JavaMigration extends BaseJavaMigration {


    @Override
    public void migrate(Context context) {
        EnergyTimePeriodConfigService timePeriodConfigService = SpringUtil.getBean(EnergyTimePeriodConfigService.class);
        EnergyTimePeriodService timePeriodService = SpringUtil.getBean(EnergyTimePeriodService.class);
        if(timePeriodService == null || timePeriodConfigService == null) {
            throw new ResponseException("意想不到的错误");
        }
        // 找到所有配置
        List<EnergyTimePeriodConfigEntity> configs = timePeriodConfigService.list();
        List<Integer> configIds = configs.stream().map(EnergyTimePeriodConfigEntity::getId).collect(Collectors.toList());
        if(CollUtil.isEmpty(configIds)) {
            return;
        }
        // 分组： 配置ID -> 用电时段类型
        List<EnergyTimePeriodEntity> timePeriods = timePeriodService.lambdaQuery().in(EnergyTimePeriodEntity::getConfigId, configIds).list();
        Map<Integer, Set<Integer>> configTimeGroup = timePeriods.stream().collect(Collectors.groupingBy(
                EnergyTimePeriodEntity::getConfigId,
                Collectors.mapping(EnergyTimePeriodEntity::getNameType, Collectors.toSet())
        ));
        // 将不包含深谷的抽取出来
        List<EnergyTimePeriodEntity> needAdds = configTimeGroup.entrySet().stream()
                .filter(entry -> !entry.getValue().contains(EnergyTimePeriodEnum.DEEP_OFF_PEAK.getCode()))
                .map(entry -> EnergyTimePeriodEntity.builder().configId(entry.getKey()).nameType(EnergyTimePeriodEnum.DEEP_OFF_PEAK.getCode()).build())
                .collect(Collectors.toList());
        if(CollUtil.isNotEmpty(needAdds)) {
            timePeriodService.saveBatch(needAdds);
        }
    }

}

