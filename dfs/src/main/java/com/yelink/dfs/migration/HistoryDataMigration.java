package com.yelink.dfs.migration;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.yelink.dfs.constant.RedisKeyPrefix;
import com.yelink.dfs.entity.sensor.SensorRecordDocument;
import com.yelink.dfs.entity.sensor.SensorRecordEntity;
import com.yelink.dfs.mapper.device.IndicatorMapper;
import com.yelink.dfs.mapper.sensor.SensorRecordMapper;
import com.yelink.dfs.migration.entity.MigrationRecordDocument;
import com.yelink.dfs.service.sensor.SensorRecordService;
import com.yelink.dfscommon.utils.ColumnUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;


/**
 * @Description:
 * @Author: zengzhengfu
 * @Date: 2024/1/25
 */
@Slf4j
@Service
public class HistoryDataMigration {

    private static final String DFS_RECORD_DEVICE_ = "dfs_record_device_";
    private static final String ID = "id";
    private static final String DATA_VAL = "dataVal";
    @Resource
    private MongoTemplate mongoTemplate;
    @Resource
    private SensorRecordMapper sensorRecordMapper;
    @Resource
    private SensorRecordService sensorRecordService;
    @Resource
    private RedisTemplate redisTemplate;
    @Resource
    private IndicatorMapper indicatorMapper;

    @Async
    public void historyDataMigration() {
        migrateRawWithLock();
        migrateMetricsWithLock();
    }

    private void migrateRawWithLock() {
        String key = RedisKeyPrefix.SENSOR_RECORD_MIGRATION_LOCK;
        Boolean lockStatus = redisTemplate.opsForValue().setIfAbsent(key, new Date(), 3, TimeUnit.HOURS);
        if (lockStatus == null || !lockStatus) {
            return;
        }
        migrateRaw();
        redisTemplate.delete(key);
    }

    private void migrateMetricsWithLock() {
        String key = RedisKeyPrefix.METRICS_TABLE_MIGRATION_LOCK;
        Boolean lockStatus = redisTemplate.opsForValue().setIfAbsent(key, new Date(), 3, TimeUnit.HOURS);
        if (lockStatus == null || !lockStatus) {
            return;
        }
        migrateMetrics();
        redisTemplate.delete(key);
    }

    private void migrateRaw() {
        //先判断raw表有没有迁移记录
        String tableName = ColumnUtil.getTableName(new SensorRecordEntity());
        Query query = new Query(Criteria.where(ColumnUtil.getField(MigrationRecordDocument::getTable)).is(tableName));
        MigrationRecordDocument one = existMigrationRecord(tableName, query);
        if (one.getIsFinish()) {
            return;
        }
        //起始id
        long startId = one.getFinishId() == null ? 1L : one.getFinishId() + 1;
        Long maxId = sensorRecordMapper.selectMaxId();

        if (maxId == null) {
            //没有任何内容
            updateFinish(tableName);
            return;
        }
        List<SensorRecordEntity> list;
        List<SensorRecordDocument> sensorRecordDocuments;
        Update update;
        while (startId <= maxId) {
            long endId = startId + 9999;
            list = sensorRecordService.lambdaQuery().between(SensorRecordEntity::getSensorRecordId, startId, endId).list();
            if (CollectionUtils.isEmpty(list)) {
                continue;
            }
            sensorRecordDocuments = JSON.parseArray(JSON.toJSONString(list), SensorRecordDocument.class);
            mongoTemplate.insertAll(sensorRecordDocuments);
            //更新id
            update = new Update();
            update.set(ColumnUtil.getField(MigrationRecordDocument::getFinishId), list.get(list.size() - 1).getSensorRecordId());
            mongoTemplate.updateFirst(query, update, MigrationRecordDocument.class);
            startId = endId + 1;
        }
        updateFinish(tableName);
    }

    private MigrationRecordDocument existMigrationRecord(String tableName, Query query) {
        MigrationRecordDocument one = mongoTemplate.findOne(query, MigrationRecordDocument.class);
        //无记录则插入
        if (one == null) {
            one = MigrationRecordDocument.builder().table(tableName).isFinish(false).build();
            mongoTemplate.insert(one);
        }
        return one;
    }

    private void updateFinish(String tableName) {
        Query query = new Query(Criteria.where(ColumnUtil.getField(MigrationRecordDocument::getTable)).is(tableName));
        //更新为已完成
        Update updateCondition = new Update();
        updateCondition.set(ColumnUtil.getField(MigrationRecordDocument::getIsFinish), true);
        mongoTemplate.updateFirst(query, updateCondition, MigrationRecordDocument.class);
    }


    private void migrateMetrics() {
        List<String> metricsTables = indicatorMapper.getMetricsTables();
        for (String metricsTable : metricsTables) {
            try {
                migrateMetric(metricsTable);
            } catch (Exception e) {
                log.error("", e);
            }
        }

    }

    private void migrateMetric(String tableName) {
        //先判断有没有迁移记录
        Query query = new Query(Criteria.where(ColumnUtil.getField(MigrationRecordDocument::getTable)).is(tableName));
        MigrationRecordDocument one = existMigrationRecord(tableName, query);
        Boolean isFinish = one.getIsFinish();
        if (isFinish) {
            return;
        }
        //起始id
        long startId = one.getFinishId() == null ? 1L : one.getFinishId() + 1;
        Long maxId = indicatorMapper.selectMaxId(tableName, ID);

        if (maxId == null) {
            //没有任何内容
            updateFinish(tableName);
            return;
        }

        String metricsNameUnderLine = tableName.replace(DFS_RECORD_DEVICE_, "");

        List<Map> list;
        Update update;
        while (startId <= maxId) {
            long endId = startId + 9999;
            list = indicatorMapper.selectByIdRange(tableName, ID, startId, endId);
            if (CollectionUtils.isEmpty(list)) {
                startId = endId + 1;
                continue;
            }

            list = list.stream().map(o -> {
                // 替换键
                HashMap<String, Object> map = new HashMap<>(8);
                if (o.containsKey(metricsNameUnderLine)) {
                    map.put(DATA_VAL, o.remove(metricsNameUnderLine));
                }
                for (Object key : o.keySet()) {
                    map.put(StrUtil.toCamelCase((String) key), o.get(key));
                }
                return map;
            }).collect(Collectors.toList());

            mongoTemplate.insert(list, tableName);
            //更新id
            update = new Update();
            update.set(ColumnUtil.getField(MigrationRecordDocument::getFinishId), list.get(list.size() - 1).get(ID));
            mongoTemplate.updateFirst(query, update, MigrationRecordDocument.class);
            startId = endId + 1;
        }
        updateFinish(tableName);
    }

}
