package com.yelink.dfs.migration;

import com.yelink.dfs.service.common.config.FormFieldRuleConfigService;
import com.yelink.dfs.service.target.ApiTransformService;
import com.yelink.dfs.utils.SpringUtil;
import com.yelink.dfscommon.entity.dfs.ApiTransformEntity;
import com.yelink.dfscommon.entity.dfs.FormFieldRuleConfigEntity;
import org.flywaydb.core.api.migration.BaseJavaMigration;
import org.flywaydb.core.api.migration.Context;
import org.springframework.http.HttpMethod;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
public class V3_11_1_10__JavaMigration extends BaseJavaMigration {

    @Override
    public void migrate(Context context) {
        // 兼容之前版本通过表单配置设置的api，将数据移植到api_transform表中
        migrateApiTransform();
    }

    private void migrateApiTransform() {
        // https://127.0.0.1:8080/factory/v1/task/inner/dfs/getBundleList  (GET) code,name
        FormFieldRuleConfigService formFieldRuleConfigService = SpringUtil.getBean(FormFieldRuleConfigService.class);
        ApiTransformService apiTransformService = SpringUtil.getBean(ApiTransformService.class);
        String apiPrefix = "https://127.0.0.1:8080/factory/v1";
        List<FormFieldRuleConfigEntity> list = formFieldRuleConfigService.lambdaQuery()
                .eq(FormFieldRuleConfigEntity::getOptionValuesType, "api")
                .isNotNull(FormFieldRuleConfigEntity::getUrl)
                .select(FormFieldRuleConfigEntity::getUrl, FormFieldRuleConfigEntity::getId)
                .list();
        List<String> apis = list.stream().map(FormFieldRuleConfigEntity::getUrl).distinct().collect(Collectors.toList());
        List<ApiTransformEntity> apiTransformEntities = new ArrayList<>();
        for (int i = 0; i < apis.size(); i++) {
            String api = apis.get(i);
            apiTransformEntities.add(ApiTransformEntity.builder()
                    .code("thirdPartyDevelopApi" + i)
                    .name("第三方开发接口" + i)
                    .url(apiPrefix + api)
                    .requestMethod(HttpMethod.GET)
                    .requestHeader("{\"Content-Type\":\"application/json;charset=UTF-8\"}")
                    .remark("第三方开发接口")
                    .reqParamScriptEnable(false)
                    .reqBodyScriptEnable(false)
                    .resBodyScriptEnable(true)
                    .reqParamScript("function transform(){\n" +
                            "}")
                    .reqBodyScript("function transform(){\n" +
                            "}")
                    .resBodyScript("function transform(data){\n" +
                            "   var result = [];\n" +
                            "  for (var i = 0; i < data.data.length; i++) {\n" +
                            "    result.push({\n" +
                            "      value:''+ data.data[i].code,\n" +
                            "      label: data.data[i].name\n" +
                            "    });\n" +
                            "  }\n" +
                            "  return result;\n" +
                            "}")
                    .build());
        }
        apiTransformService.saveBatch(apiTransformEntities);
        // 表单配置字段关联新的api
        Map<String, String> map = apiTransformEntities.stream().collect(Collectors.toMap(ApiTransformEntity::getUrl, ApiTransformEntity::getCode));
        for (FormFieldRuleConfigEntity formFieldRuleConfigEntity : list) {
            formFieldRuleConfigEntity.setRelatedApiCode(map.get(apiPrefix + formFieldRuleConfigEntity.getUrl()));
        }
        formFieldRuleConfigService.updateBatchById(list);
    }

}

