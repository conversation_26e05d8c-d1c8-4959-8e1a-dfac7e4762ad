package com.yelink.dfs.migration;

import cn.hutool.core.collection.CollUtil;
import com.yelink.dfs.constant.DictTypeEnum;
import com.yelink.dfs.service.common.DictService;
import com.yelink.dfs.service.user.SysRoleService;
import com.yelink.dfs.utils.SpringUtil;
import com.yelink.dfscommon.common.unit.constant.RoundingType;
import com.yelink.dfscommon.constant.ResponseException;
import com.yelink.dfscommon.entity.dfs.DictEntity;
import org.flywaydb.core.api.migration.BaseJavaMigration;
import org.flywaydb.core.api.migration.Context;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;


/**
 * 默认物料类型 及 单位
 * <AUTHOR>
 */
public class V3_9_5__JavaMigration extends BaseJavaMigration {


    @Override
    public void migrate(Context context) {
        // 1. 单位
        List<DictEntity> addUnits = Arrays.asList(
                DictEntity.builder().type(DictTypeEnum.UNIT.getType()).name("个").unit("1").value(RoundingType.CEILING.getCode()).build(),
                DictEntity.builder().type(DictTypeEnum.UNIT.getType()).name("Pcs").unit("1").value(RoundingType.CEILING.getCode()).build(),
                DictEntity.builder().type(DictTypeEnum.UNIT.getType()).name("千克").unit("3").value(RoundingType.CEILING.getCode()).build(),
                DictEntity.builder().type(DictTypeEnum.UNIT.getType()).name("吨").unit("3").value(RoundingType.CEILING.getCode()).build(),
                DictEntity.builder().type(DictTypeEnum.UNIT.getType()).name("升").unit("3").value(RoundingType.CEILING.getCode()).build(),
                DictEntity.builder().type(DictTypeEnum.UNIT.getType()).name("立方米").unit("3").value(RoundingType.CEILING.getCode()).build()
        );
        save(DictTypeEnum.UNIT, addUnits);

        // 2. 物料类型
        List<DictEntity> addMaterialTypes = Arrays.asList(
                DictEntity.builder().type(DictTypeEnum.MATERIAL_TYPE.getType()).name("原材料").code("rawMaterial").unit(Boolean.FALSE.toString()).isProcessAssembly(false).build(),
                DictEntity.builder().type(DictTypeEnum.MATERIAL_TYPE.getType()).name("半成品").code("semiFinishedProduct").unit(Boolean.FALSE.toString()).isProcessAssembly(false).build(),
                DictEntity.builder().type(DictTypeEnum.MATERIAL_TYPE.getType()).name("产成品").code("finishedProduct").unit(Boolean.FALSE.toString()).isProcessAssembly(false).build()
        );
        save(DictTypeEnum.MATERIAL_TYPE, addMaterialTypes);
    }
    private void save(DictTypeEnum typeEnum, List<DictEntity> adds) {
        DictService dictService = SpringUtil.getBean(DictService.class);
        if(dictService == null) {
            throw new ResponseException("意想不到的错误");
        }
        List<DictEntity> exists = dictService.lambdaQuery().eq(DictEntity::getType, typeEnum.getType()).list();
        if(CollUtil.isNotEmpty(exists)) {
            return;
        }
        dictService.saveBatch(
                adds.stream().peek(e -> e.setCreateTime(new Date())).collect(Collectors.toList())
        );
    }

}

