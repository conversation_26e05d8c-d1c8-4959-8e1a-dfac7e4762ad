package com.yelink.dfs.migration;

import com.yelink.dfs.mapper.screen.ComponentDataMapper;
import com.yelink.dfs.service.common.OperationLogService;
import com.yelink.dfs.utils.SpringUtil;
import org.flywaydb.core.api.migration.BaseJavaMigration;
import org.flywaydb.core.api.migration.Context;


/**
 * 演示数据初始化
 *
 * <AUTHOR>
 */
public class V3_15_1_11__JavaMigration extends BaseJavaMigration {

    @Override
    public void migrate(Context context) {
        //判断是否使用过，有登录记录则不整理
        OperationLogService operationLogService = SpringUtil.getBean(OperationLogService.class);
        long count = operationLogService.count();
        if (count > 0) {
            return;
        }
        // 演示数据初始化
        ComponentDataMapper componentDataMapper = SpringUtil.getBean(ComponentDataMapper.class);
        componentDataMapper.initComponentData();
    }


}

