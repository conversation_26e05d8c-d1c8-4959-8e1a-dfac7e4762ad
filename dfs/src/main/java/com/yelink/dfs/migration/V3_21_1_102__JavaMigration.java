package com.yelink.dfs.migration;

import com.yelink.dfs.entity.rule.AutoIncrementConfigEntity;
import com.yelink.dfs.entity.rule.RulePrefixConfigEntity;
import com.yelink.dfs.service.rule.NumberRuleService;
import com.yelink.dfs.service.rule.RuleAutoIncrementConfigService;
import com.yelink.dfs.service.rule.RulePrefixConfigService;
import com.yelink.dfs.utils.SpringUtil;
import com.yelink.dfscommon.constant.Constants;
import com.yelink.dfscommon.constant.dfs.rule.NumberRuleCodeDTO;
import com.yelink.dfscommon.constant.dfs.rule.NumberRulesConfigEntity;
import com.yelink.dfscommon.pojo.ResponseData;
import com.yelink.dfscommon.utils.JacksonUtil;
import lombok.extern.slf4j.Slf4j;
import org.flywaydb.core.api.migration.BaseJavaMigration;
import org.flywaydb.core.api.migration.Context;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;
import java.util.stream.Stream;


/**
 * 维表视图
 *
 * <AUTHOR>
 */
@Slf4j
public class V3_21_1_102__JavaMigration extends BaseJavaMigration {

    /**
     * 初始化新字段
     *
     * @param context
     */
    @Override
    public void migrate(Context context) {
        // 维修工单、维修定义需要配置编码规则
        RulePrefixConfigService prefixConfigService = SpringUtil.getBean(RulePrefixConfigService.class);
        List<RulePrefixConfigEntity> rulePrefixes = prefixConfigService.lambdaQuery()
                .in(RulePrefixConfigEntity::getCode, Stream.of("1", "2", "4", "5").collect(Collectors.toList()))
                .list();
        for (RulePrefixConfigEntity rulePrefix : rulePrefixes) {
            List<String> allowRuleTypes = new ArrayList<>(Arrays.asList(rulePrefix.getAllowRuleTypes().split(Constants.SEP)));
            // 包含则代表现网手动处理过这个问题，无需往下继续执行
            if (allowRuleTypes.contains("103") || allowRuleTypes.contains("104")) {
                return;
            }
            allowRuleTypes.add("103");
            allowRuleTypes.add("104");
            prefixConfigService.lambdaUpdate().eq(RulePrefixConfigEntity::getId, rulePrefix.getId())
                    .set(RulePrefixConfigEntity::getAllowRuleTypes, String.join(Constants.SEP, allowRuleTypes))
                    .update();
        }
        RuleAutoIncrementConfigService autoIncrementConfigService = SpringUtil.getBean(RuleAutoIncrementConfigService.class);
        List<AutoIncrementConfigEntity> incrementConfigEntities = autoIncrementConfigService.lambdaQuery()
                .in(AutoIncrementConfigEntity::getCode, Stream.of("noCross", "day", "month", "year").collect(Collectors.toList()))
                .list();
        for (AutoIncrementConfigEntity incrementConfigEntity : incrementConfigEntities) {
            List<String> allowRuleTypes = new ArrayList<>(Arrays.asList(incrementConfigEntity.getAllowRuleTypes().split(Constants.SEP)));
            allowRuleTypes.add("103");
            allowRuleTypes.add("104");
            autoIncrementConfigService.lambdaUpdate().eq(AutoIncrementConfigEntity::getId, incrementConfigEntity.getId())
                    .set(AutoIncrementConfigEntity::getAllowRuleTypes, String.join(Constants.SEP, allowRuleTypes))
                    .update();
        }
        // 新增一条默认的编码规则（维修工单）
        NumberRuleService numberRuleService = SpringUtil.getBean(NumberRuleService.class);
        NumberRulesConfigEntity build = NumberRulesConfigEntity.builder()
                .type("104")
                .name("编码规则")
                .prefixDetail(getNumberRulePrefixDetail("WXGD"))
                .createBy("admin")
                .createTime(new Date())
                .updateTime(new Date())
                .isDefault(true)
                .build();
        numberRuleService.save(build);
    }

    private static String getNumberRulePrefixDetail(String fixedInfo) {
        List<NumberRuleCodeDTO> prefixDetailList = new ArrayList<>();
        NumberRuleCodeDTO fixedInfoDto = NumberRuleCodeDTO.builder().code("5").name("固定信息").rule(fixedInfo).initValue(1).example(fixedInfo).build();
        NumberRuleCodeDTO dateDto = NumberRuleCodeDTO.builder().code("2").name("当前日期").rule("yyyyMMdd").initValue(1).example("20240813").build();
        NumberRuleCodeDTO autoSeqDto = NumberRuleCodeDTO.builder().code("4").name("自动生成序号").autoIncrementConfigureType("day").uuid(UUID.randomUUID().toString()).rule("3").initValue(1).example("001").build();
        prefixDetailList.add(fixedInfoDto);
        prefixDetailList.add(dateDto);
        prefixDetailList.add(autoSeqDto);
        return JacksonUtil.toJSONString(prefixDetailList);
    }

}

