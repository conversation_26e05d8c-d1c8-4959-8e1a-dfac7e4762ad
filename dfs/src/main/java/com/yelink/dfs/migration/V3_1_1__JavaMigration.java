package com.yelink.dfs.migration;

import com.yelink.dfs.service.mongodb.MongodbService;
import com.yelink.dfs.utils.SpringUtil;
import org.flywaydb.core.api.migration.BaseJavaMigration;
import org.flywaydb.core.api.migration.Context;


/**
 * @Description: 条码关联表加索引
 * @Author: z<PERSON>zhengfu
 * @Date: 2024/2/23
 */
public class V3_1_1__JavaMigration extends BaseJavaMigration {


    @Override
    public void migrate(Context context) {
        MongodbService mongodbService = SpringUtil.getBean(MongodbService.class);
        mongodbService.addIndex("target.code", "dfs_code_relevance");
    }
}
