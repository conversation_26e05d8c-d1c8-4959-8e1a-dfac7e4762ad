package com.yelink.dfs.migration;

import com.yelink.dfs.entity.order.OrderExecuteSeqEntity;
import com.yelink.dfs.entity.order.WorkOrderDeviceRelevanceEntity;
import com.yelink.dfs.entity.order.WorkOrderEntity;
import com.yelink.dfs.service.impl.order.OrderExecuteSeqServiceImpl;
import com.yelink.dfs.service.order.WorkOrderDeviceRelevanceService;
import com.yelink.dfs.service.order.WorkOrderService;
import com.yelink.dfs.utils.SpringUtil;
import lombok.extern.slf4j.Slf4j;
import org.flywaydb.core.api.migration.BaseJavaMigration;
import org.flywaydb.core.api.migration.Context;
import org.springframework.util.CollectionUtils;

import java.util.List;


/**
 * 维表视图
 *
 * <AUTHOR>
 */
@Slf4j
public class V3_21_1_101__JavaMigration extends BaseJavaMigration {

    /**
     * 初始化新字段
     *
     * @param context
     */
    @Override
    public void migrate(Context context) {
        OrderExecuteSeqServiceImpl orderExecuteSeqService = SpringUtil.getBean(OrderExecuteSeqServiceImpl.class);
        List<OrderExecuteSeqEntity> list = orderExecuteSeqService.lambdaQuery()
                .select(OrderExecuteSeqEntity::getId, OrderExecuteSeqEntity::getOrderNumber,
                        OrderExecuteSeqEntity::getDeviceId, OrderExecuteSeqEntity::getLineId,
                        OrderExecuteSeqEntity::getCreateTime, OrderExecuteSeqEntity::getIsOperation,
                        OrderExecuteSeqEntity::getDeviceAsResource)
                .isNotNull(OrderExecuteSeqEntity::getDeviceId).list();
        if (CollectionUtils.isEmpty(list)) {
            return;
        }

        WorkOrderDeviceRelevanceService WorkOrderDeviceRelevanceService = SpringUtil.getBean(WorkOrderDeviceRelevanceService.class);
        WorkOrderService workOrderService = SpringUtil.getBean(WorkOrderService.class);

        for (OrderExecuteSeqEntity entity : list) {
            WorkOrderEntity one = workOrderService.lambdaQuery()
                    .select(WorkOrderEntity::getWorkOrderId)
                    .eq(WorkOrderEntity::getWorkOrderNumber, entity.getOrderNumber()).one();
            if (one == null) {
                continue;
            }

            boolean exists = WorkOrderDeviceRelevanceService.lambdaQuery()
                    .eq(WorkOrderDeviceRelevanceEntity::getDeviceId, entity.getDeviceId())
                    .eq(WorkOrderDeviceRelevanceEntity::getWorkOrderId, one.getWorkOrderId()).exists();
            if (exists) {
                orderExecuteSeqService.lambdaUpdate()
                        .eq(OrderExecuteSeqEntity::getId, entity.getId())
                        .set(OrderExecuteSeqEntity::getDeviceAsResource, true)
                        .update();
            }
        }
    }

}

