package com.yelink.dfs.migration;

import com.yelink.dfs.entity.device.DeviceEntity;
import com.yelink.dfs.entity.manufacture.ProductionLineEntity;
import com.yelink.dfs.entity.model.ModelEntity;
import com.yelink.dfs.entity.model.WorkCenterDeviceEntity;
import com.yelink.dfs.service.device.DeviceService;
import com.yelink.dfs.service.manufacture.ProductionLineService;
import com.yelink.dfs.service.model.ModelService;
import com.yelink.dfs.service.model.WorkCenterDeviceService;
import com.yelink.dfs.service.model.WorkCenterService;
import com.yelink.dfs.service.model.WorkCenterTeamService;
import com.yelink.dfs.utils.SpringUtil;
import com.yelink.dfscommon.constant.Constants;
import com.yelink.dfscommon.entity.dfs.WorkCenterEntity;
import org.flywaydb.core.api.migration.BaseJavaMigration;
import org.flywaydb.core.api.migration.Context;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * 内置角色权限
 *
 * <AUTHOR>
 */
public class V3_19_1_100__JavaMigration extends BaseJavaMigration {

    @Override
    public void migrate(Context context) {
        // 为前人填坑
        // 工作中心的lineModelId和deviceModelId和实际的关联数据不匹配，导致产线页面无法回显工作中心，工艺路线无法创建工艺工序
        WorkCenterService workCenterService = SpringUtil.getBean(WorkCenterService.class);
        ProductionLineService productionLineService = SpringUtil.getBean(ProductionLineService.class);
        DeviceService deviceService = SpringUtil.getBean(DeviceService.class);
        WorkCenterDeviceService workCenterDeviceService = SpringUtil.getBean(WorkCenterDeviceService.class);
        ModelService modelService = SpringUtil.getBean(ModelService.class);

        // 重新置空数据，将数据刷对
        workCenterService.lambdaUpdate()
                .set(WorkCenterEntity::getLineModelId, null)
                .set(WorkCenterEntity::getLineModelName, null)
                .set(WorkCenterEntity::getDeviceModelId, null)
                .update();
        // 找到工作中心关联的产线实例，将模型id刷新到工作中心上
        Map<Integer, List<Integer>> lineModelMap = productionLineService.lambdaQuery()
                .select(ProductionLineEntity::getModelId, ProductionLineEntity::getWorkCenterId)
                .isNotNull(ProductionLineEntity::getWorkCenterId)
                .list().stream()
                .collect(Collectors.groupingBy(ProductionLineEntity::getWorkCenterId,
                        Collectors.mapping(ProductionLineEntity::getModelId, Collectors.toList())));
        for (Map.Entry<Integer, List<Integer>> entry : lineModelMap.entrySet()) {
            Integer workCenterId = entry.getKey();
            List<Integer> modelIds = entry.getValue();
            String modelNames = modelService.lambdaQuery()
                    .select(ModelEntity::getName)
                    .in(ModelEntity::getId, modelIds).list().stream()
                    .map(ModelEntity::getName)
                    .distinct()
                    .collect(Collectors.joining(Constants.SEP));
            workCenterService.lambdaUpdate().eq(WorkCenterEntity::getId, workCenterId)
                    .set(WorkCenterEntity::getLineModelId, modelIds.stream().distinct().map(String::valueOf).collect(Collectors.joining(Constants.SEP)))
                    .set(WorkCenterEntity::getLineModelName, modelNames)
                    .update();
        }
        // 找到工作中心关联的设备实例，将模型id刷新到工作中心上
        Map<Integer, List<Integer>> deviceIdMap = workCenterDeviceService.list().stream()
                .collect(Collectors.groupingBy(WorkCenterDeviceEntity::getWorkCenterId,
                        Collectors.mapping(WorkCenterDeviceEntity::getDeviceId, Collectors.toList())));
        for (Map.Entry<Integer, List<Integer>> entry : deviceIdMap.entrySet()) {
            Integer workCenterId = entry.getKey();
            List<Integer> deviceIds = entry.getValue();
            String modelIds = deviceService.lambdaQuery().select(DeviceEntity::getModelId)
                    .in(DeviceEntity::getDeviceId, deviceIds)
                    .list().stream()
                    .map(DeviceEntity::getModelId)
                    .distinct()
                    .map(String::valueOf)
                    .collect(Collectors.joining(Constants.SEP));
            workCenterService.lambdaUpdate().eq(WorkCenterEntity::getId, workCenterId)
                    .set(WorkCenterEntity::getDeviceModelId, modelIds)
                    .update();

        }

    }



}

