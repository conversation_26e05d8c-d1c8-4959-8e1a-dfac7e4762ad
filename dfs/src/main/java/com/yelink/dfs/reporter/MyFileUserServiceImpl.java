package com.yelink.dfs.reporter;

import com.yelink.office.model.documenteditor.config.document.Permissions;
import com.yelink.office.service.impl.FileUserServiceImpl;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * @Description:
 * @Author: shuang
 * @Date: 2025/1/6
 * @Version:
 */

@Component
@RequiredArgsConstructor
public class MyFileUserServiceImpl extends FileUserServiceImpl {


    @Override
    public Permissions getPermission(String fileId) {
        return Permissions.builder()
                .edit(true)
                .download(true)
                .modifyFilter(true)
                .modifyContentControl(true)
                .review(true)
                .build();
    }
}
