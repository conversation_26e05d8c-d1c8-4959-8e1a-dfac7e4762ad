package com.yelink.dfs.reporter;

import com.asyncexcel.core.model.ExcelTask;
import com.asyncexcel.springboot.context.service.IExcelTaskService;
import com.github.tobato.fastdfs.domain.StorePath;
import com.yelink.dfs.entity.common.UploadEntity;
import com.yelink.dfs.service.common.UploadService;
import com.yelink.dfscommon.constant.ResponseException;
import com.yelink.office.config.DocumentServerProperties;
import com.yelink.office.model.FileStorageVo;
import com.yelink.office.service.FileServiceService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * @Description:  onlyoffice预览文件信息获取实现
 * @Author: shuang
 * @Date: 2024/6/19
 * @Version:
 */
@Slf4j
@Service
//@RequiredArgsConstructor
public class FileServiceServiceImpl implements FileServiceService {

    private final UploadService uploadService;
    private final DocumentServerProperties documentServerProperties;


    public FileServiceServiceImpl(final UploadService uploadService,final DocumentServerProperties documentServerProperties){
        this.uploadService = uploadService;
        this.documentServerProperties = documentServerProperties;
    }


    @Override
    public FileStorageVo getFileStorageVo(String id) {

        UploadEntity uploadEntity = uploadService.getById(id);
        if(uploadEntity == null){
            throw new ResponseException("获取数据错误");
        }
        StorePath storePath = StorePath.praseFromUrl(uploadEntity.getUrl());
        return new FileStorageVo()
                .setBaseUrl(documentServerProperties.getFileServerUrl())
                .setGroup(storePath.getGroup())
                .setName(uploadEntity.getFileName())
                .setPath(storePath.getPath())
                .setTimedDeletion(false);
    }


}
