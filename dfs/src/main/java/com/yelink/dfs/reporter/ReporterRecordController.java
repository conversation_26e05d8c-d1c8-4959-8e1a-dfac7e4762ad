package com.yelink.dfs.reporter;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yelink.dfs.common.BaseController;
import com.yelink.dfs.constant.Constant;
import com.yelink.dfs.constant.OperationType;
import com.yelink.dfs.entity.common.ModelUploadFileEntity;
import com.yelink.dfs.entity.reporter.dto.ReporterRecordDTO;
import com.yelink.dfs.entity.reporter.dto.ReporterRecordPrintSelectDTO;
import com.yelink.dfs.entity.reporter.excel.ReporterRecordExcelVO;
import com.yelink.dfscommon.dto.dfs.ReporterRecordVO;
import com.yelink.dfs.provider.FastDfsClientService;
import com.yelink.dfs.service.common.ModelUploadFileService;
import com.yelink.dfs.service.target.record.ReportLineService;
import com.yelink.dfs.utils.ExcelTemplateExportUtils;
import com.yelink.dfscommon.annotation.OperLog;
import com.yelink.dfscommon.entity.dfs.barcode.PrintDTO;
import com.yelink.dfscommon.pojo.ResponseData;
import com.yelink.dfscommon.utils.JacksonUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.util.List;
import java.util.Objects;

/**
 * 报工记录
 * <AUTHOR>
 * @date 2022-08-22
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/reporter")
public class ReporterRecordController extends BaseController {

    private ReportLineService reportLineService;
    private ModelUploadFileService modelUploadFileService;
    private FastDfsClientService fastDfsClientService;

    /**
     * 生产工单的报工记录查询
     * @param dto 报工记录-请求DTO
     * @return
     */
    @PostMapping("/get/list")
    public ResponseData getList(@RequestBody ReporterRecordDTO dto) {
        Page<ReporterRecordVO> result = reportLineService.getReporterRecord(dto);
        return success(result);
    }

    /**
     * 获取生产工单的产线数量报工表的所有上报方式
     *
     * @return
     */
    @GetMapping("/all/type")
    public ResponseData getAllType() {
        return success(reportLineService.getAllReportType());
    }

    /**
     * 生产工单的报工记录导出
     * @param dto 导出条件
     * @param response
     */
    @PostMapping("/export")
    public void getDailyExport(@RequestBody ReporterRecordDTO dto,HttpServletResponse response) {
        if(dto!=null){
            dto.setCurrent(null);
            dto.setSize(null);
        }
        Page<ReporterRecordVO> result = reportLineService.getReporterRecord(dto);
        List<ReporterRecordVO> data = result.getRecords();
        reportLineService.exportData(data,response);
    }

    /**
     * 生产工单--报工记录默认导出模板
     */
    @GetMapping("/default/export/template")
    public void downloadSaleOrderDefaultExportTemplate(HttpServletResponse response) {
        String fileName = "生产工单报工记录默认导出模板" + Constant.XLSX;
        ExcelTemplateExportUtils.downloadDefaultExportTemplate(response, fileName, ReporterRecordExcelVO.class);
    }

    /**
     * 生产工单--报工记录导出
     */
    @PostMapping("/excel/exports")
    public void export(@RequestParam(required = false) Integer templateId,
                       @RequestBody ReporterRecordDTO dto, HttpServletResponse response) {
        Page<ReporterRecordVO> result = reportLineService.getReporterRecord(dto);
        // 封装成DTO
        List<ReporterRecordExcelVO> data = JacksonUtil.convertArray(result.getRecords(), ReporterRecordExcelVO.class);

        if (Objects.isNull(templateId)) {
            // 按默认模板下载
            ExcelTemplateExportUtils.downloadDefaultExportData(response, "生产工单报工记录" + Constant.XLSX, ReporterRecordExcelVO.class, data);
        } else {
            // 按模板下载
            ModelUploadFileEntity uploadFile = modelUploadFileService.getById(templateId);
            byte[] bytes = fastDfsClientService.getFileStream(uploadFile.getFileAddress());
            ExcelTemplateExportUtils.downloadExportData(response, new ByteArrayInputStream(bytes), uploadFile.getFileName(), ReporterRecordExcelVO.class, data);
        }
    }

    /**
     * 报工标签打印
     *
     * @param selectDTO 查询条件
     * @return
     */
    @OperLog(module = "报工标签", type = OperationType.PRINT, desc = "打印了生产工单号为#{printCodes}的报工标签")
    @PostMapping("/print")
    public ResponseData print(@RequestBody ReporterRecordPrintSelectDTO selectDTO) {
        log.info("{}调用了打印接口：{}", getUsername(), JacksonUtil.toJSONString(selectDTO));
        PrintDTO print = reportLineService.print(selectDTO);
        return success(print);
    }

}

