package com.yelink.dfs.listener;

import com.yelink.dfs.entity.approve.config.ApproveDockingConfigEntity;
import com.yelink.dfs.entity.approve.config.ApproveRecordEntity;
import com.yelink.dfs.service.approve.config.ApproveDockingConfigService;
import com.yelink.dfs.service.approve.config.ApproveRecordService;
import com.yelink.dfs.service.user.SysUserService;
import com.yelink.dfscommon.constant.Constants;
import com.yelink.dfscommon.constant.approve.config.ApprovalStatusEnum;
import com.yelink.dfscommon.constant.approve.config.ApprovalWeChatApplyStatusEnum;
import com.yelink.dfscommon.constant.approve.config.IsApproveEnum;
import com.yelink.dfscommon.constant.dfs.DfsEventTypeEnum;
import com.yelink.dfscommon.dto.dfs.WeChatApprovalChangeDTO;
import com.yelink.dfscommon.provider.MessagePushToKafkaService;
import com.yelink.dfscommon.utils.JacksonUtil;
import com.yelink.wechat.client.manager.WeChatApproveManager;
import com.yelink.wechat.server.service.WeChatFlowService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;
import org.xml.sax.InputSource;

import javax.annotation.Resource;
import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import java.io.StringReader;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * 企业微信回调处理类
 *
 * <AUTHOR>
 * @Date 2023/10/30 17:06
 */
@Slf4j
@Service
public class WeChatFlowServiceImpl implements WeChatFlowService {
    /**
     * 审批申请状态变化回调通知
     */
    private static final String EVENT_SYS_APPROVAL_CHANGE = "sys_approval_change";
    @Resource
    protected MessagePushToKafkaService messagePushToKafkaService;
    @Resource
    private ApproveDockingConfigService approveDockingConfigService;
    @Resource
    private ApproveRecordService approveRecordService;
    @Resource
    private SysUserService userService;
    @Resource
    private WeChatApproveManager weChatApproveManager;

    @Override
    public void verifyFeedback(Integer approveCallbackState) {
        // 更新审批回调状态
        approveDockingConfigService.lambdaUpdate()
                .eq(ApproveDockingConfigEntity::getDockingCode, IsApproveEnum.WECHAT.getCodeStr())
                .set(ApproveDockingConfigEntity::getApproveCallbackState, approveCallbackState)
                .update();
    }

    @Override
    public void feedbackData(String msg) {
        log.info("feedbackData, msg:{}", msg);
        try {
            DocumentBuilderFactory dbf = DocumentBuilderFactory.newInstance();
            DocumentBuilder db = dbf.newDocumentBuilder();
            StringReader sr = new StringReader(msg);
            InputSource is = new InputSource(sr);
            Document document = db.parse(is);

            Element root = document.getDocumentElement();
            String event = root.getElementsByTagName("Event").item(0).getTextContent();

            if (EVENT_SYS_APPROVAL_CHANGE.equals(event)) {
                // 审批申请状态变化回调通知
                WeChatApprovalChangeDTO approvalChange = getWeChatApprovalChangeDTO(root);
                // 保存审批记录
                saveApproveRecord(approvalChange);
                // 回调更改状态
                messagePushToKafkaService.pushNewMessage(approvalChange, Constants.KAFKA_VALUE_CHAIN_STATE_TOPIC, DfsEventTypeEnum.WECHAT_APPROVAL_CHANGE);
                log.info("企业微信审批申请状态变化回调通知, WeChatApprovalChangeDTO:{}", JacksonUtil.toJSONString(approvalChange));
            }
        } catch (Exception e) {
            log.error("企业微信回调消息处理错误", e);
        }
    }

    /**
     * 获取企业微信审批回调的数据DTO
     */
    public static WeChatApprovalChangeDTO getWeChatApprovalChangeDTO(Element root) {
        // 审批申请状态变化回调通知
        NodeList rootChildNodes = root.getChildNodes();
        String spNo = getNodeValueByNodeName(rootChildNodes, "SpNo");
        String spName = getNodeValueByNodeName(rootChildNodes, "SpName");
        String spStatus = getNodeValueByNodeName(rootChildNodes, "SpStatus");
        String templateId = getNodeValueByNodeName(rootChildNodes, "TemplateId");
        String applyTime = getNodeValueByNodeName(rootChildNodes, "ApplyTime");
        String statuChangeEvent = getNodeValueByNodeName(rootChildNodes, "StatuChangeEvent");
        // 审批流程信息，可能有多个审批节点。
        NodeList spRecordNodeList = root.getElementsByTagName("SpRecord");
        List<WeChatApprovalChangeDTO.SpRecord> spRecords = new ArrayList<>();
        for (int i = 0; i < spRecordNodeList.getLength(); i++) {
            NodeList childNodes = spRecordNodeList.item(i).getChildNodes();
            // <Details> 节点
            List<WeChatApprovalChangeDTO.SpRecordDetail> details = new ArrayList<>();
            for (int j = 0; j < childNodes.getLength(); j++) {
                Node item = childNodes.item(j);
                if ("Details".equals(item.getNodeName())) {
                    NodeList detailsChildNodes = item.getChildNodes();
                    details.add(WeChatApprovalChangeDTO.SpRecordDetail.builder()
                            .userId(getNodeValueByNodeName(detailsChildNodes, "UserId"))
                            .speech(getNodeValueByNodeName(detailsChildNodes, "Speech"))
                            .spStatus(Integer.valueOf(getNodeValueByNodeName(childNodes, "SpStatus")))
                            .spTime(getNodeValueByNodeName(detailsChildNodes, "SpTime"))
                            .attach(getNodeValueByNodeName(detailsChildNodes, "Attach"))
                            .build());
                }
            }
            WeChatApprovalChangeDTO.SpRecord spRecord = WeChatApprovalChangeDTO.SpRecord.builder()
                    .spStatus(Integer.valueOf(getNodeValueByNodeName(childNodes, "SpStatus")))
                    .approverAttr(Integer.valueOf(getNodeValueByNodeName(childNodes, "ApproverAttr")))
                    .details(details)
                    .build();
            spRecords.add(spRecord);
        }
        // 抄送信息，可能有多个抄送节点
        NodeList notifyerNodeList = root.getElementsByTagName("Notifyer");
        List<String> notifyerUserIds = new ArrayList<>();
        for (int i = 0; i < notifyerNodeList.getLength(); i++) {
            NodeList childNodes = notifyerNodeList.item(i).getChildNodes();
            String userId = getNodeValueByNodeName(childNodes, "UserId");
            notifyerUserIds.add(userId);
        }
        // 审批申请备注信息，可能有多个备注节点
        NodeList commentNodeList = root.getElementsByTagName("Comments");
        List<WeChatApprovalChangeDTO.Comment> comments = new ArrayList<>();
        for (int i = 0; i < commentNodeList.getLength(); i++) {
            NodeList childNodes = commentNodeList.item(i).getChildNodes();
            WeChatApprovalChangeDTO.Comment commentBuild = WeChatApprovalChangeDTO.Comment.builder()
                    .userId(getNodeValueByNodeName(childNodes, "UserId"))
                    .commentId(getNodeValueByNodeName(childNodes, "CommentId"))
                    .commentContent(getNodeValueByNodeName(childNodes, "CommentContent"))
                    .commentTime(getNodeValueByNodeName(childNodes, "CommentTime"))
                    .build();
            comments.add(commentBuild);
        }

        return WeChatApprovalChangeDTO.builder()
                .spNo(spNo)
                .spName(spName)
                .spStatus(Integer.valueOf(spStatus))
                .templateId(templateId)
                .applyTime(applyTime)
                .spRecords(spRecords)
                .notifyerUserIds(notifyerUserIds)
                .comments(comments)
                .statuChangeEvent(Integer.valueOf(statuChangeEvent))
                .build();
    }

    /**
     * 获取标签中的值
     *
     * @param commentNodeList
     * @param nodeName        一层一层获取,名称必须在NodeList中唯一
     */
    public static String getNodeValueByNodeName(NodeList commentNodeList, String nodeName) {
        for (int i = 0; i < commentNodeList.getLength(); i++) {
            Node item = commentNodeList.item(i);
            // node名称和想要的一样,直接返回
            if (nodeName.equals(item.getNodeName())) {
                return item.getTextContent();
            } else {
                String value = getNodeValueByNodeName(item.getChildNodes(), nodeName);
                if (StringUtils.isNotBlank(value)) {
                    return value;
                }
            }
        }
        return "";
    }

    /**
     * 保存审批记录
     *
     * @param weChatApprovalChangeDTO
     */
    private void saveApproveRecord(WeChatApprovalChangeDTO weChatApprovalChangeDTO) {
        // 提单
        if (weChatApprovalChangeDTO.getStatuChangeEvent() == ApprovalWeChatApplyStatusEnum.SUBMIT_ORDER.getCode()) {
            // 提交申请时已创建本条数据
            return;
        }
        if (CollectionUtils.isEmpty(weChatApprovalChangeDTO.getSpRecords())) {
            return;
        }
        List<ApproveRecordEntity> approvalRecordEntities = approveRecordService.lambdaQuery()
                .eq(ApproveRecordEntity::getOuterApproveNumber, weChatApprovalChangeDTO.getSpNo())
                .list();
        if (CollectionUtils.isEmpty(approvalRecordEntities)) {
            return;
        }
        ApproveRecordEntity oldRecord = approvalRecordEntities.get(0);
        Set<String> spNoTimeSet = new HashSet<>();
        approvalRecordEntities.forEach(item -> {
            if (StringUtils.isNotBlank(item.getApprovalTimestamp())) {
                spNoTimeSet.add(item.getApprovalTimestamp());
            }
        });
        Date date = new Date();
        List<ApproveRecordEntity> saveList = new ArrayList<>();
        // 每次审批回调 只会有一条审批记录的更新的，其他的都是不变的
        for (WeChatApprovalChangeDTO.SpRecord spRecordItem : weChatApprovalChangeDTO.getSpRecords()) {
            if (CollectionUtils.isEmpty(spRecordItem.getDetails())) {
                continue;
            }

            for (WeChatApprovalChangeDTO.SpRecordDetail spRecordDetail : spRecordItem.getDetails()) {
                // 审批时间戳为空，不去读取
                if (StringUtils.isEmpty(spRecordDetail.getSpTime()) || "0".equals(spRecordDetail.getSpTime())) {
                    continue;
                }
                // 存在时间戳，不去读取
                if (spNoTimeSet.contains(spRecordDetail.getSpTime())) {
                    continue;
                }
                ApproveRecordEntity recordEntity = new ApproveRecordEntity();
                recordEntity.setFullPathCode(oldRecord.getFullPathCode());
                recordEntity.setOrderNumber(oldRecord.getOrderNumber());
                recordEntity.setOuterApproveNumber(oldRecord.getOuterApproveNumber());
                recordEntity.setApproveType(oldRecord.getApproveType());

                String nickName = null;
                try {
                    nickName = weChatApproveManager.getNickName(spRecordDetail.getUserId());
                } catch (Exception e) {
                    log.error("获取企微用户信息错误：", e);
                }
                if (StringUtils.isBlank(nickName)) {
                    nickName = spRecordDetail.getUserId();
                }
                recordEntity.setApproverId(spRecordDetail.getUserId());
                recordEntity.setApprover(nickName);
                recordEntity.setApprovalTime(new Date(Long.parseLong(spRecordDetail.getSpTime()) * 1000));
                recordEntity.setApprovalSuggestion(spRecordDetail.getSpeech());
                recordEntity.setApprovalTimestamp(spRecordDetail.getSpTime());
                recordEntity.setApprovalStatus(spRecordDetail.getSpStatus());
                recordEntity.setCreateBy(nickName);
                recordEntity.setCreateTime(date);
                recordEntity.setUpdateBy(nickName);
                recordEntity.setUpdateTime(date);
                saveList.add(recordEntity);
            }
        }
        if (CollectionUtils.isEmpty(saveList)) {
            return;
        }
        // 保存记录
        approveRecordService.saveBatch(saveList);
        // 若非审批中（已是 审批通过、审批驳回、审批撤销），发送审批申请结束通知
        if (!ApprovalStatusEnum.UNDER_APPROVAL.getWechatCode().equals(weChatApprovalChangeDTO.getSpStatus())) {
            messagePushToKafkaService.pushNewMessage(saveList.get(0), Constants.KAFKA_VALUE_CHAIN_STATE_TOPIC, DfsEventTypeEnum.WECHAT_APPROVAL_RESULT_MESSAGE);
        }
    }

}
