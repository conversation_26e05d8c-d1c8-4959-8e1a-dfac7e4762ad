package com.yelink.dfs.listener;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.read.listener.ReadListener;
import com.alibaba.excel.read.metadata.holder.ReadRowHolder;
import com.alibaba.excel.read.metadata.holder.ReadSheetHolder;
import com.yelink.dfs.utils.EasyExcelDTO;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;

/**
 * 从Excel文件流中分批导入数据到库中
 * EasyExcel参考文档：https://easyexcel.opensource.alibaba.com/docs/current/quickstart/read
 *
 * <AUTHOR>
 * @date 2022-08-02
 */
@Data
@Slf4j
public class EasyExcelImportListener<T extends EasyExcelDTO> implements ReadListener<T> {


    /**
     * 缓存数据
     */
    private List<T> dataList = new ArrayList<>();

    /**
     * 缓存大小,实际使用中可以100条,然后清理list,方便内存回收
     */
    private static final int BATCH_SIZE = 100;

    /**
     * 这个每一条数据解析都会来调用
     *
     * @param data
     * @param analysisContext
     */
    @Override
    public void invoke(T data, AnalysisContext analysisContext) {
        //表名
        ReadSheetHolder sheetHolder = analysisContext.readSheetHolder();
        String sheetName = sheetHolder.getSheetName();
        //行号
        ReadRowHolder rowHolder = analysisContext.readRowHolder();
        Integer rowIndex = rowHolder.getRowIndex();
        data.setSheetName(sheetName);
        data.setRowIndex(rowIndex + 1);
        dataList.add(data);
        //达到BATCH_COUNT后,可存储一次数据库,防止数据几万条数据在内存中,容易OOM
        //if (cacheList.size() >= BATCH_SIZE) {
        //    log.info("完成一批Excel记录的导入,条数为：{}", cacheList.size());
        //    cacheList = new ArrayList<>(BATCH_SIZE);
        //}
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {
        ReadSheetHolder sheetHolder = analysisContext.readSheetHolder();
        String sheetName = sheetHolder.getSheetName();
        log.info("{}Excel表完成记录的导入，条数为：{}", sheetName, dataList.size());
    }

}
