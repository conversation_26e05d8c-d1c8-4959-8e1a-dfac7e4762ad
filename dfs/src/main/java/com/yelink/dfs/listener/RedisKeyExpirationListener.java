package com.yelink.dfs.listener;

import com.yelink.dfs.constant.Constant;
import com.yelink.dfs.constant.RedisKeyPrefix;
import com.yelink.dfs.entity.common.RedisCallBackEntity;
import com.yelink.dfs.service.alarm.AlarmService;
import com.yelink.dfs.service.common.RedisCallBackService;
import com.yelink.dfs.service.impl.product.MaterialCache;
import com.yelink.dfs.service.impl.target.workorder.ReportLineMcaServiceImpl;
import com.yelink.dfs.service.model.ModelService;
import com.yelink.dfs.service.order.RecordWorkOrderDayCountService;
import com.yelink.dfs.service.sensor.MemoryCalService;
import com.yelink.dfs.service.target.CombineTargetService;
import com.yelink.dfs.service.target.TargetModelService;
import com.yelink.dfs.utils.SpringUtil;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.connection.Message;
import org.springframework.data.redis.listener.KeyExpirationEventMessageListener;
import org.springframework.data.redis.listener.RedisMessageListenerContainer;
import org.springframework.stereotype.Component;

import java.text.ParseException;

import static com.yelink.dfs.constant.RedisKeyPrefix.COMBINE_TARGET_ITEM;
import static com.yelink.dfs.constant.RedisKeyPrefix.MEMORY_CAL_TASK_ITEM;


/**
 * 监听所有db的过期事件__keyevent@*__:expired"
 *
 * <AUTHOR>
@Component
@Slf4j
public class RedisKeyExpirationListener extends KeyExpirationEventMessageListener {
    @Autowired
    private AlarmService alarmService;
    @Autowired
    private MemoryCalService memoryCalService;
    @Autowired
    private CombineTargetService combineTargetService;
    @Autowired
    private RecordWorkOrderDayCountService recordWorkOrderDayCountService;
    @Autowired
    private RedisCallBackService redisCallBackService;
    @Autowired
    private TargetModelService targetModelService;
    @Autowired
    private ModelService modelService;
    @Autowired
    private MaterialCache materialCache;

    public RedisKeyExpirationListener(RedisMessageListenerContainer listenerContainer) {
        super(listenerContainer);
    }

    /**
     * 针对redis数据失效事件，进行数据处理
     *
     * @param message
     * @param pattern
     */
    @SneakyThrows
    @Override
    public void onMessage(Message message, byte[] pattern) {
        //获取失效的key
        String expiredKey = message.toString();
        //对告警的key进行处理
        dealAlarmUpgrade(expiredKey);
        //对内存计算的key进行处理
        dealMemoryCalExpiredKey(expiredKey);
        //对组合指标过期的key进行处理
        dealCombineTargetExpiredKey(expiredKey);
        //计数设备计算当日完成量回调
        dealWorkOrderDayCountByCounter(expiredKey);
        //计数设备计算当订单进度回调
        updateProductProgressByCounter(expiredKey);
        //指标总列表
        dealTargetModel(expiredKey);
        //模型总列表
        dealModel(expiredKey);
        //物料缓存
        dealMaterialCache(expiredKey);
    }

    /**
     * 对告警的key进行处理
     *
     * @param expiredKey
     */
    private void dealAlarmUpgrade(String expiredKey) {
        //对告警为前缀开头的key进行处理
        if (!expiredKey.startsWith(RedisKeyPrefix.ALARM + Constant.REAL)) {
            return;
        }
        redisCallBackService.lambdaUpdate().eq(RedisCallBackEntity::getRedisKey, expiredKey).remove();
        alarmService.dealAlarmExpiredKey(expiredKey);
    }


    private void dealMemoryCalExpiredKey(String expiredKey) {
        if (expiredKey.startsWith(MEMORY_CAL_TASK_ITEM)) {
            String euiAndTargetName = expiredKey.replace(MEMORY_CAL_TASK_ITEM, "");
            String[] split = euiAndTargetName.split(Constant.SPECIAL_SEP);
            memoryCalService.calByTask(split[0], split[1]);
        }
    }

    /**
     * 处理组合指标过期的key（联动绑定其他指标）
     *
     * @param expiredKey
     */
    private void dealCombineTargetExpiredKey(String expiredKey) throws ParseException {
        if (expiredKey.startsWith(COMBINE_TARGET_ITEM)) {
            combineTargetService.dealCombineTarget(expiredKey.replace(COMBINE_TARGET_ITEM, ""));
        }
    }

    /**
     * 计数设备计算当日完成量回调
     *
     * @param expiredKey
     */
    private void dealWorkOrderDayCountByCounter(String expiredKey) {
        if (expiredKey.startsWith(RedisKeyPrefix.UPDATE_WORK_ORDER_DAY_COUNT_BY_COUNTER)) {
            redisCallBackService.lambdaUpdate().eq(RedisCallBackEntity::getRedisKey, expiredKey).remove();
            recordWorkOrderDayCountService.updateByWorkOrderNumberCallBack(expiredKey.replace(RedisKeyPrefix.UPDATE_WORK_ORDER_DAY_COUNT_BY_COUNTER, ""));
        }
    }

    private void updateProductProgressByCounter(String expiredKey) {
        if (expiredKey.startsWith(RedisKeyPrefix.UPDATE_PRODUCT_ORDER_BY_COUNTER)) {
            redisCallBackService.lambdaUpdate().eq(RedisCallBackEntity::getRedisKey, expiredKey).remove();
            ReportLineMcaServiceImpl bean = SpringUtil.getBean(ReportLineMcaServiceImpl.class);
            bean.updateProductProgressCallBack(expiredKey.replace(RedisKeyPrefix.UPDATE_PRODUCT_ORDER_BY_COUNTER, ""));
        }
    }

    private void dealTargetModel(String expiredKey) {
        if (expiredKey.startsWith(RedisKeyPrefix.TARGET_METHOD_LIST)) {
            targetModelService.refresh();
        }
    }

    private void dealModel(String expiredKey) {
        if (expiredKey.startsWith(RedisKeyPrefix.MODEL_LIST)) {
            modelService.refresh();
        }
    }

    private void dealMaterialCache(String expiredKey) {
        if (expiredKey.equals(RedisKeyPrefix.MATERIAL_CACHE)) {
            materialCache.refreshCache();
        }
    }

}
