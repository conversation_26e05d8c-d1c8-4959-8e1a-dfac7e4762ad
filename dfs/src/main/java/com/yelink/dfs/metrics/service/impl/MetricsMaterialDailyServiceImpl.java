package com.yelink.dfs.metrics.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yelink.dfs.constant.code.MaintainTypeEnum;
import com.yelink.dfs.entity.alarm.AlarmEntity;
import com.yelink.dfs.entity.attendance.dto.AttendanceRecordDTO;
import com.yelink.dfs.entity.attendance.vo.AttendanceRecordVO;
import com.yelink.dfs.entity.capacity.vo.CapacityVO;
import com.yelink.dfs.entity.maintain.MaintainRecordEntity;
import com.yelink.dfs.entity.order.RecordWorkOrderDayCountEntity;
import com.yelink.dfs.entity.order.RecordWorkOrderUnqualifiedEntity;
import com.yelink.dfs.entity.order.WorkOrderBasicUnitInputRecordEntity;
import com.yelink.dfs.entity.order.WorkOrderEntity;
import com.yelink.dfs.entity.order.WorkOrderPlanEntity;
import com.yelink.dfs.entity.reporter.dto.CodeReportAccessDTO;
import com.yelink.dfs.metrics.entity.MetricsMaterialDailyEntity;
import com.yelink.dfs.metrics.mapper.MetricsMaterialDailyMapper;
import com.yelink.dfs.metrics.service.CommonMetricsService;
import com.yelink.dfs.service.alarm.AlarmService;
import com.yelink.dfs.service.attendance.AttendanceRecordService;
import com.yelink.dfs.service.capacity.CapacityService;
import com.yelink.dfs.service.code.CodeReportService;
import com.yelink.dfs.service.maintain.MaintainRecordService;
import com.yelink.dfs.service.order.RecordWorkOrderDayCountService;
import com.yelink.dfs.service.order.RecordWorkOrderUnqualifiedService;
import com.yelink.dfs.service.order.WorkOrderBasicUnitInputRecordService;
import com.yelink.dfs.service.order.WorkOrderPlanService;
import com.yelink.dfs.service.order.WorkOrderService;
import com.yelink.dfscommon.constant.dfs.order.WorkOrderStateEnum;
import com.yelink.dfscommon.utils.NullableDouble;
import com.yelink.metrics.api.MetricsApi;
import com.yelink.metrics.api.time.DailyVO;
import com.yelink.metrics.core.domain.TargetModel;
import com.yelink.metrics.core.service.BaseMetricsService;
import com.yelink.metrics.core.service.BaseMetricsServiceImpl;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 */
@Service
public class MetricsMaterialDailyServiceImpl extends BaseMetricsServiceImpl<MetricsMaterialDailyMapper, MetricsMaterialDailyEntity> implements BaseMetricsService<MetricsMaterialDailyEntity> {

    @Resource
    private MetricsApi metricsApi;
    @Resource
    private WorkOrderService workOrderService;
    @Resource
    private RecordWorkOrderDayCountService recordWorkOrderDayCountService;
    @Resource
    private MaintainRecordService maintainRecordService;
    @Resource
    private AttendanceRecordService attendanceRecordService;
    @Resource
    private CodeReportService codeReportService;
    @Resource
    private WorkOrderBasicUnitInputRecordService workOrderBasicUnitInputRecordService;
    @Resource
    private CapacityService capacityService;
    @Resource
    private WorkOrderPlanService workOrderPlanService;
    @Resource
    private AlarmService alarmService;
    @Resource
    private CommonMetricsService commonMetricsService;
    @Resource
    private RecordWorkOrderUnqualifiedService recordWorkOrderUnqualifiedService;


    @Override
    public List<MetricsMaterialDailyEntity> deal(TargetModel targetModel) {
        DailyVO daily = metricsApi.getDaily();

        List<WorkOrderEntity> allWorkOrders = workOrderService.lambdaQuery().in(WorkOrderEntity::getState,
                WorkOrderStateEnum.INVESTMENT.getCode(),
                WorkOrderStateEnum.HANG_UP.getCode()
        ).or(wrapper -> wrapper
                .eq(WorkOrderEntity::getState, WorkOrderStateEnum.FINISHED.getCode())
                .ge(WorkOrderEntity::getStateChangeTime, daily.getStartTime())
                .lt(WorkOrderEntity::getStateChangeTime, daily.getEndTime())
        ).list();
        List<String> workOrderNumbers = allWorkOrders.stream().map(WorkOrderEntity::getWorkOrderNumber).collect(Collectors.toList());
        if(CollUtil.isEmpty(workOrderNumbers)) {
            return Collections.emptyList();
        }
        // 查工单的每日统计
        List<RecordWorkOrderDayCountEntity> dayCounts = recordWorkOrderDayCountService.list(Wrappers.lambdaQuery(RecordWorkOrderDayCountEntity.class)
                .in(RecordWorkOrderDayCountEntity::getWorkOrderNumber, workOrderNumbers)
                .eq(RecordWorkOrderDayCountEntity::getTime, daily.getRecordTime())
        );
        Map<String, RecordWorkOrderDayCountEntity> dayCountMap = dayCounts.stream().collect(Collectors.toMap(RecordWorkOrderDayCountEntity::getWorkOrderNumber, Function.identity(), (v1, v2) -> v2));
        // 维修相关
        List<MaintainRecordEntity> maintainRecords = maintainRecordService.lambdaQuery()
                .in(MaintainRecordEntity::getWorkOrder, workOrderNumbers)
                .between(MaintainRecordEntity::getCreateTime,  daily.getStartTime(),  daily.getEndTime())
                .list();
        Map<String, List<MaintainRecordEntity>> maintainRecordsMap = maintainRecords.stream().collect(Collectors.groupingBy(MaintainRecordEntity::getWorkOrder));
        // 人员工时统计
        Page<AttendanceRecordVO> page = attendanceRecordService.recordList(
                AttendanceRecordDTO.builder()
                        .workOrderNumbers(workOrderNumbers)
                        .recordDate(daily.getRecordTime())
                        .simple(true)
                        .smartCal(true)
                        .build()
        );
        Map<String, List<AttendanceRecordVO>> attendanceRecordsMap = page.getRecords().stream().collect(Collectors.groupingBy(AttendanceRecordVO::getWorkOrderNumber));

        // 扫码相关
        Map<String, Integer> directAccessMap = codeReportService.accessQuantityMap(CodeReportAccessDTO.builder()
                .isFirstUnqualified(false)
                .relationNumbers(workOrderNumbers)
                .reportTimeDown(daily.getStartTime())
                .reportTimeUp(daily.getEndTime())
                .build()
        );
        Map<String, Integer> accessMap = codeReportService.accessQuantityMap(CodeReportAccessDTO.builder()
                .relationNumbers(workOrderNumbers)
                .reportTimeDown(daily.getStartTime())
                .reportTimeUp(daily.getEndTime())
                .build()
        );
        // 资源投入工时
        List<WorkOrderBasicUnitInputRecordEntity> basicUnitInputRecords = workOrderBasicUnitInputRecordService.lambdaQuery()
                .in(WorkOrderBasicUnitInputRecordEntity::getWorkOrderNumber, workOrderNumbers)
                // 投产开始时间 < 今天最晚时刻
                .lt(WorkOrderBasicUnitInputRecordEntity::getStartTime, daily.getEndTime())
                .and(o -> o
                        // 投产结束时间 > 今天最早时刻 || 投产结束时间为空
                        .gt(WorkOrderBasicUnitInputRecordEntity::getEndTime, daily.getStartTime())
                        .or()
                        .isNull(WorkOrderBasicUnitInputRecordEntity::getEndTime)
                )
                .list();
        Map<String, Double> resourceInputMap = basicUnitInputRecords.stream().collect(
                Collectors.groupingBy(WorkOrderBasicUnitInputRecordEntity::getWorkOrderNumber, Collectors.summingDouble(e -> e.getDurationH(daily.getStartTime(), daily.getEndTime())))
        );
        // 产能
        Map<String, CapacityVO> workOrderCapacityMap = capacityService.getWorkOrderListCapacity(allWorkOrders);
        // 今日计划
        List<WorkOrderPlanEntity> workOrderPlans = workOrderPlanService.lambdaQuery()
                .eq(WorkOrderPlanEntity::getTime, daily.getRecordTime())
                .in(WorkOrderPlanEntity::getWorkOrderNumber, workOrderNumbers)
                .list();
        Map<String, WorkOrderPlanEntity> workOrderPlanMap = workOrderPlans.stream().collect(Collectors.toMap(WorkOrderPlanEntity::getWorkOrderNumber, Function.identity(), (v1, v2) -> v2));
        // 告警
        List<AlarmEntity> allAlarms = alarmService.lambdaQuery()
                .in(AlarmEntity::getWorkOrderNumber, workOrderNumbers)
                .between(AlarmEntity::getAlarmTime, daily.getStartTime(), daily.getEndTime())
                .list();
        Map<String, List<AlarmEntity>> workOrderAlarmGroup = allAlarms.stream().collect(Collectors.groupingBy(AlarmEntity::getWorkOrderNumber));
        // 不良记录
        Map<String, List<RecordWorkOrderUnqualifiedEntity>> unqualifiedRecordsGroup = recordWorkOrderUnqualifiedService.workOrderUnqualifiedRecordsMap(workOrderNumbers, daily.getStartTime(), daily.getEndTime());


        //  物料 : 工单集合
        Map<String, List<WorkOrderEntity>> materialCodeOrderGroup = allWorkOrders.stream().collect(Collectors.groupingBy(WorkOrderEntity::getMaterialCode));

        List<MetricsMaterialDailyEntity> results = materialCodeOrderGroup.entrySet().stream().map(entry -> {
            String materialCode = entry.getKey();
            List<WorkOrderEntity> workOrders = entry.getValue();
            double planQuantity = workOrders.stream().map(e -> workOrderPlanMap.get(e.getWorkOrderNumber()))
                    .filter(Objects::nonNull).map(WorkOrderPlanEntity::getPlanQuantity).filter(Objects::nonNull).mapToDouble(e -> e).sum();
            double produceQuantity = workOrders.stream().map(e -> dayCountMap.get(e.getWorkOrderNumber()))
                    .filter(Objects::nonNull).map(RecordWorkOrderDayCountEntity::getCount).filter(Objects::nonNull).mapToDouble(e -> e).sum();
            double unqualifiedQuantity = workOrders.stream().map(e -> dayCountMap.get(e.getWorkOrderNumber()))
                    .filter(Objects::nonNull).map(RecordWorkOrderDayCountEntity::getUnqualified).filter(Objects::nonNull).mapToDouble(e -> e).sum();
            double repairScrapQuantity = (double) workOrders.stream().map(e -> maintainRecordsMap.getOrDefault(e.getWorkOrderNumber(), Collections.emptyList()))
                    .flatMap(Collection::stream).filter(e -> !MaintainTypeEnum.SCRAP.getCode().equals(e.getMaintainResultType())).count();
            double repairQualifiedQuantity = (double) workOrders.stream().map(e -> maintainRecordsMap.getOrDefault(e.getWorkOrderNumber(), Collections.emptyList()))
                    .flatMap(Collection::stream).filter(e -> !MaintainTypeEnum.FINISHED.getCode().equals(e.getMaintainResultType())).count();
            double directAccessQuantity = workOrders.stream().map(e -> directAccessMap.get(e.getWorkOrderNumber()))
                    .filter(Objects::nonNull).mapToDouble(e -> e).sum();
            double accessQuantity = workOrders.stream().map(e -> accessMap.get(e.getWorkOrderNumber()))
                    .filter(Objects::nonNull).mapToDouble(e -> e).sum();
            // 不良记录
            List<RecordWorkOrderUnqualifiedEntity> unqualifiedRecordItems = workOrderNumbers.stream().map(e -> unqualifiedRecordsGroup.getOrDefault(e, Collections.emptyList())).flatMap(Collection::stream).collect(Collectors.toList());
            int unqualifiedRecordQuantity = (int) unqualifiedRecordItems.stream().map(RecordWorkOrderUnqualifiedEntity::getSequenceId).distinct().count();

            // 工时
            double humanWorkingHour = workOrders.stream().map(e -> attendanceRecordsMap.getOrDefault(e.getWorkOrderNumber(), Collections.emptyList()))
                    .flatMap(Collection::stream).map(AttendanceRecordVO::getDurationH).filter(Objects::nonNull)
                    .reduce(BigDecimal::add).map(e -> Double.valueOf(e.toString())).orElse(0d);
            double resourceWorkingHour = workOrders.stream().map(e -> resourceInputMap.getOrDefault(e.getWorkOrderNumber(), 0d)).mapToDouble(e -> e).sum();
            // 产能 -> 理论工时
            Double productHour = workOrders.stream().map(e -> {
                CapacityVO capacityVO = workOrderCapacityMap.get(e.getWorkOrderNumber());
                if (capacityVO == null) {
                    return null;
                }
                RecordWorkOrderDayCountEntity dayCount = dayCountMap.get(e.getWorkOrderNumber());
                double tempCount = dayCount == null? 0d : dayCount.getCount();
                return NullableDouble.of(tempCount).div(capacityVO.getCapacity()).cal();
            }).filter(Objects::nonNull).reduce(Double::sum).orElse(0d);
            // 告警相关
            List<AlarmEntity> alarms = workOrders.stream().map(e -> workOrderAlarmGroup.getOrDefault(e.getWorkOrderNumber(), Collections.emptyList())).flatMap(Collection::stream).collect(Collectors.toList());
            double alarmDealDurationH = alarms.stream().map(AlarmEntity::getDealDurationH).filter(Objects::nonNull).reduce(Double::sum).orElse(0d);
            double alarmRecoverDurationH =alarms.stream().map(AlarmEntity::getResponseDurationH).filter(Objects::nonNull).reduce(Double::sum).orElse(0d);
            return MetricsMaterialDailyEntity.builder()
                    .recordTime(daily.getRecordTime())
                    .materialCode(materialCode)
                    .planQuantity(planQuantity)
                    .produceQuantity(produceQuantity)
                    .unqualifiedQuantity(unqualifiedQuantity)
                    .unqualifiedRecordItemQuantity(unqualifiedRecordItems.size())
                    .unqualifiedRecordQuantity(unqualifiedRecordQuantity)
                    .repairQuantity(repairScrapQuantity + repairQualifiedQuantity)
                    .repairScrapQuantity(repairScrapQuantity)
                    .repairQualifiedQuantity(repairQualifiedQuantity)
                    .directAccessQuantity(directAccessQuantity)
                    .qualifiedRate(NullableDouble.of(produceQuantity).div(produceQuantity + unqualifiedQuantity).cal())
                    .directAccessRate(NullableDouble.of(directAccessQuantity).div(accessQuantity).cal())
                    .humanWorkingHour(humanWorkingHour)
                    .resourceWorkingHour(resourceWorkingHour)
                    .productHour(productHour)
                    .humanEfficiencyRate(NullableDouble.of(productHour).div(humanWorkingHour).cal())
                    .resourceEfficiencyRate(NullableDouble.of(productHour).div(resourceWorkingHour).cal())
                    .alarmCount(alarms.size())
                    .alarmDealDurationH(alarmDealDurationH)
                    .alarmRecoverDurationH(alarmRecoverDurationH)
                    .build();
        }).collect(Collectors.toList());
        commonMetricsService.setMaterialAttr(results);
        return results;
    }
}
