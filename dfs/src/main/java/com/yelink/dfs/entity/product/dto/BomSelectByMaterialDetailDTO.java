package com.yelink.dfs.entity.product.dto;

import com.yelink.dfscommon.dto.CommSelectPageDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * <AUTHOR>
 * @Date 2022/8/4 19:31
 */
@Data
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class BomSelectByMaterialDetailDTO extends CommSelectPageDTO {

    /**
     * 物料code
     */
    @ApiModelProperty("物料code")
    private String code;

    /**
     * BOM编号
     */
    @ApiModelProperty("BOM编号")
    private String bomNum;


    /**
     * 特征值名称
     */
    @ApiModelProperty("特征值名称")
    private String valueName;

    /**
     * 子物料名称
     */
    @ApiModelProperty("子物料名称")
    private String materialCode;

    /**
     * 子物料skuId
     */
    @ApiModelProperty("子物料skuId")
    private Integer skuId;
}
