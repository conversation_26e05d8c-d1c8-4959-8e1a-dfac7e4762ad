package com.yelink.dfs.entity.screen.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.io.Serializable;

/**
 * @description: 工单状态总览
 * @author: shuang
 * @time: 2022/6/24
 */
@Data
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class WorkOrderDataStatisticsVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 待生产
     */
    private Integer produced;

    /**
     * 生产中
     */
    private Integer producing;

    /**
     * 已完成
     */
    private Integer completed;



}
