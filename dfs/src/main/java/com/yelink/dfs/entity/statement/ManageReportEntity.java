package com.yelink.dfs.entity.statement;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.yelink.dfs.entity.common.ModelUploadFileEntity;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/7/4 10:43
 */
@Data
@Builder
@ToString
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@TableName("dfs_manage_report")
public class ManageReportEntity extends Model<ManageReportEntity> {
    private static final long serialVersionUID = 763737416609243069L;

    @TableId(value = "report_id", type = IdType.AUTO)
    private Integer reportId;
    /**
     * 报表名称
     */
    @TableField("report_name")
    private String reportName;

    /**
     * 副标题
     */
    @TableField("subhead")
    private String subhead;

    /**
     * 报表类型
     */
    @TableField(exist = false)
    private List<Integer> relateReportTypeIds;
    @TableField(exist = false)
    private String relateReportTypeNames;

    /**
     * {@link com.yelink.dfs.constant.statement.ReportStateEnum}
     * 状态,生效:1、关闭:0
     */
    @TableField("state")
    private Integer state;
    /**
     * 状态名称
     */
    @TableField(exist = false)
    private String stateName;

    /**
     * 是否内置，1：内置，0：否
     */
    @TableField("default_inner")
    private Boolean defaultInner;

    /**
     * 在那个模块下创建的,计划调度:order-model、生产作业:workorder-model、质量管理:qualities-manage、报表中心:statementCenter
     * {@link com.yelink.dfs.constant.statement.ReportModelEnum}
     */
    @TableField("model")
    private String model;
    /**
     * {@link com.yelink.dfs.constant.statement.ReportTypeEnum}
     * 配置的数据源,多个逗号分割
     */
    @TableField("data_sources")
    private String dataSources;
    /**
     * 数据源名称
     */
    @TableField(exist = false)
    private String dataSourcesName;
    /**
     * 数据源的配置
     */
    @TableField("data_source_config")
    private String dataSourceConfig;
    /**
     * 应用模板id
     */
    @TableField(value = "template_id", updateStrategy = FieldStrategy.IGNORED)
    private Integer templateId;
    /**
     * 模板名称
     */
    @TableField(exist = false)
    private String templateName;
    /**
     * 模板信息
     */
    @TableField(exist = false)
    private ModelUploadFileEntity templateFile;
    /**
     * 创建时间
     */
    @TableField("create_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
    /**
     * 更新时间
     */
    @TableField("update_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;
    /**
     * 创建人
     */
    @TableField("create_by")
    private String createBy;
    @TableField(exist = false)
    private String createByName;
    /**
     * 更新人
     */
    @TableField("update_by")
    private String updateBy;

    /**
     * 是否同步创建小程序快捷方式
     */
    @TableField("program_shortcut_flag")
    private Boolean programShortcutFlag;
    /**
     * 创建的小程序快捷方式的appId
     */
    @TableField("program_shortcut_app_id")
    private String programShortcutAppId;
    /**
     * 小程序快捷方式名称
     */
    @TableField("program_shortcut_name")
    private String programShortcutName;
    /**
     * 权重
     */
    @TableField("program_shortcut_weight")
    private Integer programShortcutWeight;
    /**
     * 类型
     */
    @TableField("program_shortcut_category_id")
    private Integer programShortcutCategoryId;

    @TableField("program_shortcut_category")
    private String programShortcutCategory;
    /**
     * 标签
     */
    @TableField("program_shortcut_label")
    private String programShortcutLabel;
    /**
     * 参数
     */
    @TableField("program_shortcut_param")
    private String programShortcutParam;
    /**
     * 图标地址
     */
    @TableField("program_shortcut_icon")
    private String programShortcutIcon;
    /**
     * 备注
     */
    @TableField("program_shortcut_remark")
    private String programShortcutRemark;

}
