package com.yelink.dfs.entity.maintain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.yelink.dfs.entity.maintain.MaintainRecordEntity;
import com.yelink.dfscommon.constant.Constants;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.springframework.util.CollectionUtils;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2021-10-19 10:34
 */
@Data
@Builder
@ToString
@AllArgsConstructor
@NoArgsConstructor
public class MaintainRecordExcelDTO {

	/**
	 * 流水号
	 */
	@ExcelProperty(value = "记录号", order = 1)
	private String serialNumber;

	/**
	 * 条码号
	 */
	@ExcelProperty(value = "流水码", order = 2)
	private String barCode;


	/**
	 * 维修定义名称
	 */
	@ExcelProperty(value = "维修项目", order = 3)
	private String maintainName;

	/**
	 * 维修定义类型
	 */
	@ExcelProperty(value = "维修类型", order = 4)
	private String maintainTypeName;

	/**
	 * 故障类型
	 */
	@ExcelProperty(value = "问题类别", order = 5)
	private String faultTypeName;

	/**
	 * 故障工位
	 */
	@ExcelProperty(value = "关联故障工位", order = 6)
	private String faultFname;

	/**
	 * 工单号
	 */
	@ExcelProperty(value = "生产工单号", order = 7)
	private String workOrder;

	/**
	 * 物料名称
	 */
	@ExcelProperty(value = "物料名称", order = 11)
	private String materialName;
	/**
	 * 物料编码
	 */
	@ExcelProperty(value = "物料编码", order = 10)
	private String materialCode;
	/**
	 * 工单名称
	 */
	@ExcelProperty(value = "工单名称", order = 9)
	private String workOrderName;
	/**
	 * 工厂型号
	 */
	@ExcelProperty(value = "工厂型号", order = 13)
	private String factoryModel;
	/**
	 * 关联生产订单
	 */
	@ExcelProperty(value = "关联生产订单", order = 7)
	private String productOrderNumber;
	/**
	 * 关联销售订单
	 */
	@ExcelProperty(value = "关联销售订单", order = 8)
	private String saleOrderNumber;

	/**
	 * 物料规格
	 */
	@ExcelProperty(value = "物料规格", order = 12)
	private String standard;

	/**
	 * 关联上报位置
	 */
	@ExcelProperty(value = "上报位置", order = 14)
	private String fname;

	/**
	 * 备注说明
	 */
	@ExcelProperty(value = "备注说明", order = 15)
	private String remark;


	/**
	 * 创建人
	 */
	@ExcelProperty(value = "操作员", order = 16)
	private String createByName;

	/**
	 * 创建时间
	 */
	@ExcelProperty(value = "维修时间", order = 17)
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Date createTime;

	/**
	 * 不良代号
	 */
	@ExcelProperty(value = "不良代号", order = 18)
	private String defectCode;

	/**
	 * 不良名称
	 */
	@ExcelProperty(value = "不良名称", order = 19)
	private String defectName;

	@ExcelProperty("问题物料原始码(多个','分隔)")
	private String replaceMaterials;

	@ExcelProperty("问题物料名称(多个','分隔)")
	private String replaceMaterialNames;
	public static MaintainRecordExcelDTO convertToDTO(MaintainRecordEntity record) {
		String faultFname = null;
		if (!CollectionUtils.isEmpty(record.getFaultFnames())) {
			faultFname = String.join(Constants.SEP, record.getFaultFnames());
		}
		return MaintainRecordExcelDTO.builder()
				.serialNumber(record.getSerialNumber())
				.barCode(record.getBarCode())
				.maintainName(record.getMaintainName())
				.maintainTypeName(record.getMaintainTypeName())
				.faultTypeName(record.getFaultTypeName())
				.faultFname(faultFname)
				.workOrder(record.getWorkOrder())
				.materialName(record.getMaterialName())
				.materialCode(record.getMaterialCode())
				.workOrderName(record.getWorkOrderName())
				.factoryModel(record.getFactoryModel())
				.productOrderNumber(record.getProductOrderNumber())
				.saleOrderNumber(record.getSaleOrderNumber())
				.standard(record.getStandard())
				.fname(record.getFname())
				.remark(record.getRemark())
				.createByName(record.getCreateByName())
				.createTime(record.getCreateTime())
				.defectCode(record.getDefectCode())
				.defectName(record.getDefectName())
				.replaceMaterials(record.getReplaceMaterials())
				.replaceMaterialNames(record.getReplaceMaterialNames())
				.build();
	}
}
