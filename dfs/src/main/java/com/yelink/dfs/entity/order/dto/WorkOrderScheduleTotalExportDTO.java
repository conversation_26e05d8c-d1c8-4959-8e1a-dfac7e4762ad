package com.yelink.dfs.entity.order.dto;

import com.asyncexcel.core.annotation.DynamicExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.yelink.dfscommon.constant.Constants;
import com.yelink.dfscommon.utils.JacksonUtil;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 用于工单查询条件的对象
 *
 * <AUTHOR>
 * @Date 2021/12/10 11:18
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class WorkOrderScheduleTotalExportDTO {

    @DynamicExcelProperty
    private  String sep;

    /**
     * 工单编号
     */
    @DynamicExcelProperty
    private String workOrderName;
    /**
     * 工单名称
     */
    @DynamicExcelProperty
    private String workOrderNumber;

    /**
     * 订单号
     */
    @DynamicExcelProperty
    private String productOrderNumber;

    /**
     * 销售订单号
     */
    @DynamicExcelProperty
    private String saleOrderNumber;

    /**
     * 状态名称
     */
    @DynamicExcelProperty
    private String stateName;

    /**
     * 客户名称
     */
    @DynamicExcelProperty
    private String customerName;

    /**
     * 客户编号
     */
    @DynamicExcelProperty
    private String customerCode;
    /**
     * 物料编码
     */
    @DynamicExcelProperty
    protected String materialCode;

    /**
     * 物料名称
     */
    @DynamicExcelProperty(moduleCode = Constants.MATERIAL_BASE_FIELD, fieldCode = "name")
    protected String materialName;
    @DynamicExcelProperty(moduleCode = Constants.MATERIAL_BASE_FIELD, fieldCode = "standard")
    protected String materialStandard;

    /**
     * 计划数量
     */
    @DynamicExcelProperty
    private Double planQuantity;


    /**
     * 工序名称
     */
    @DynamicExcelProperty
    private String procedureName;

    /**
     * 工作中心名称
     */
    @DynamicExcelProperty
    private String workCenterName;

    /**
     * 排程状态
     */
    @DynamicExcelProperty
    private String schedulingStateName;

    /**
     * 计划理论工时
     */
    @DynamicExcelProperty
    private Double planTheoryHour;

    /**
     * 订单计划完成时间
     */
    @DynamicExcelProperty
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date planProductEndTime;


    /**
     * 开始时间
     */
    @DynamicExcelProperty
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startDate;

    /**
     * 截止时间
     */
    @DynamicExcelProperty
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endDate;
    /**
     * 销售订单的下单日期
     */
    @DynamicExcelProperty
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date orderDate;
    /**
     * 销售订单的要货日期
     */
    @DynamicExcelProperty
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date requireGoodsDate;

    /**
     * 备注
     */
    @DynamicExcelProperty
    private String remark;


    /**
     * 工单拓展字段1
     */
    @DynamicExcelProperty
    private String workOrderExtendFieldOne;
    /**
     * 工单拓展字段2
     */
    @DynamicExcelProperty
    private String workOrderExtendFieldTwo;
    /**
     * 工单拓展字段3
     */
    @DynamicExcelProperty
    private String workOrderExtendFieldThree;
    /**
     * 工单拓展字段4
     */
    @DynamicExcelProperty
    private String workOrderExtendFieldFour;
    /**
     * 工单拓展字段5
     */
    @DynamicExcelProperty
    private String workOrderExtendFieldFive;

    /**
     * 工单拓展字段6
     */
    @DynamicExcelProperty
    private String workOrderExtendFieldSix;
    /**
     * 工单拓展字段7
     */
    @DynamicExcelProperty
    private String workOrderExtendFieldSeven;
    /**
     * 工单拓展字段8
     */
    @DynamicExcelProperty
    private String workOrderExtendFieldEight;
    /**
     * 工单拓展字段9
     */
    @DynamicExcelProperty
    private String workOrderExtendFieldNine;
    /**
     * 工单拓展字段10
     */
    @DynamicExcelProperty
    private String workOrderExtendFieldTen;
    /**
     * 工单物料拓展字段1
     */
    @DynamicExcelProperty
    private String workOrderMaterialExtendFieldOne;
    /**
     * 工单物料拓展字段2
     */
    @DynamicExcelProperty
    private String workOrderMaterialExtendFieldTwo;
    /**
     * 工单物料拓展字段3
     */
    @DynamicExcelProperty
    private String workOrderMaterialExtendFieldThree;
    /**
     * 工单物料拓展字段4
     */
    @DynamicExcelProperty
    private String workOrderMaterialExtendFieldFour;
    /**
     * 工单物料拓展字段5
     */
    @DynamicExcelProperty
    private String workOrderMaterialExtendFieldFive;
    /**
     * 工单物料拓展字段6
     */
    @DynamicExcelProperty
    private String workOrderMaterialExtendFieldSix;
    /**
     * 工单物料拓展字段7
     */
    @DynamicExcelProperty
    private String workOrderMaterialExtendFieldSeven;
    /**
     * 工单物料拓展字段8
     */
    @DynamicExcelProperty
    private String workOrderMaterialExtendFieldEight;
    /**
     * 工单物料拓展字段9
     */
    @DynamicExcelProperty
    private String workOrderMaterialExtendFieldNine;
    /**
     * 工单物料拓展字段10
     */
    @DynamicExcelProperty
    private String workOrderMaterialExtendFieldTen;

    public static List<WorkOrderScheduleTotalExportDTO> convertToDTO(List<WorkOrderScheduleTotalDTO> workOrderScheduleDTOList) {
        if (CollectionUtils.isEmpty(workOrderScheduleDTOList)) {
            return new ArrayList<>();
        }
        List<WorkOrderScheduleTotalExportDTO> workOrderScheduleTotalExportDTOS = new ArrayList<>();
        workOrderScheduleDTOList.forEach(workOrderScheduleTotalDTO -> {
            workOrderScheduleTotalExportDTOS.add(WorkOrderScheduleTotalExportDTO.builder()
                    .sep(workOrderScheduleTotalDTO.getName())
                    .build());
            List<WorkOrderScheduleDTO> workOrderScheduleDTOList1 = workOrderScheduleTotalDTO.getWorkOrderScheduleDTOList();
            AtomicInteger seq = new AtomicInteger(1);
            if (!CollectionUtils.isEmpty(workOrderScheduleDTOList1)) {
                workOrderScheduleDTOList1.forEach(workOrderScheduleDTO -> {
                    WorkOrderScheduleTotalExportDTO workOrderScheduleTotalExportDTO = JacksonUtil.convertObject(workOrderScheduleDTO, WorkOrderScheduleTotalExportDTO.class);
                    workOrderScheduleTotalExportDTO.setSep(String.valueOf(seq.get()));
                    seq.getAndIncrement();
                    workOrderScheduleTotalExportDTOS.add(workOrderScheduleTotalExportDTO);
                });
            }
        });
        return workOrderScheduleTotalExportDTOS;
    }

}
