package com.yelink.dfs.entity.work.calendar;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.yelink.dfs.entity.model.dto.InstanceDTO;
import com.yelink.dfs.entity.work.calendar.dto.TimeRangeDTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021-09-24 10:49
 */
@Data
@Builder
@ToString
@AllArgsConstructor
@NoArgsConstructor
@TableName("dfs_work_calendar")
public class WorkCalendarEntity extends Model<WorkCalendarEntity> {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "calendar_id", type = IdType.AUTO)
    private Integer calendarId;

    /**
     * 实例id
     */
    @NotNull(message = "实例id不可为空")
    @TableField(value = "instance_id")
    private Integer instanceId;

    /**
     * 实例编号
     */
    @NotBlank(message = "实例编号不可为空")
    @TableField(value = "instance_code")
    private String instanceCode;

    /**
     * 实例名称
     */
    @NotBlank(message = "实例名称不可为空")
    @TableField(value = "instance_name")
    private String instanceName;

    /**
     * 模型id
     */
    @NotNull(message = "模型id不可为空")
    @TableField(value = "model_id")
    private Integer modelId;

    /**
     * 模型类型
     */
    @NotBlank(message = "模型类型不可为空")
    @TableField(value = "model_type")
    private String modelType;

    /**
     * 班次名称
     */
    @TableField(value = "name")
    private String name;

    /**
     * 开始时间
     */
    @TableField(value = "start")
    private String start;

    /**
     * 结束时间
     */
    @TableField(value = "end")
    private String end;

    /**
     * 总工时
     */
    @TableField(value = "duration")
    private Double duration;

    /**
     * 班次时间
     */
    @TableField(value = "work_calendar_time")
    private String workCalendarTime;

    /**
     * 非工作时间
     */
    @TableField(value = "down_time")
    private String downTime;

    /**
     * 有效期开始日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @TableField(value = "validity_start")
    private Date validityStart;

    /**
     * 有效期结束日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @TableField(value = "validity_end")
    private Date validityEnd;

    /**
     * 描述
     */
    @TableField(value = "description")
    private String description;

    /**
     * 创建人
     */
    @TableField(value = "create_by")
    private String createBy;

    /**
     * 修改人
     */
    @TableField(value = "update_by")
    private String updateBy;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField(value = "update_time")
    private Date updateTime;

    /**
     * 非生产时间
     */
    @TableField(exist = false)
    private List<TimeRangeDTO> downTimes;

    /**
     * 班次时间
     */
    @TableField(exist = false)
    private List<TimeRangeDTO> workCalendarTimes;

    /**
     * 下级的实例
     */
    @TableField(exist = false)
    private List<InstanceDTO> children;

    @Override
    public Serializable pkVal() {
        return this.calendarId;
    }

}
