package com.yelink.dfs.entity.device.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * <AUTHOR>
 * @date 2021/5/26 14:42
 */
@Data
@Builder
@ToString
@AllArgsConstructor
@NoArgsConstructor
public class DeviceStateVo {


    /**
     * 状态码
     */
    private int code;
    /**
     * 状态名称
     */
    private String name;
}
