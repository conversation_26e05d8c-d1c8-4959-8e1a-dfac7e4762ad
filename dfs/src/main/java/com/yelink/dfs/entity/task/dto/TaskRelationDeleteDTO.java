package com.yelink.dfs.entity.task.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import javax.validation.constraints.NotBlank;


/**
 * 任务关联项 删除
 * <AUTHOR>
 */
@Data
@Builder
@ToString
@AllArgsConstructor
@NoArgsConstructor
public class TaskRelationDeleteDTO {


    /**
     * 单据类型
     */
    @ApiModelProperty("主单据类型")
    @NotBlank(message = "主单据类型不能为空")
    private String orderCategory;

    /**
     * 单据编码
     */
    @ApiModelProperty("主单据编码")
    @NotBlank(message = "主单据编码不能为空")
    private String orderNumber;

    @ApiModelProperty("物料行Id")
    private Long materialLineId;

    /**
     * 关联类型
     */
    @ApiModelProperty("关联类型")
    @NotBlank(message = "关联类型不能为空")
    private String relateType;


    /**
     * 关联id
     */
    @ApiModelProperty("关联id")
    @NotBlank(message = "关联id不能为空")
    private String relateId;



}
