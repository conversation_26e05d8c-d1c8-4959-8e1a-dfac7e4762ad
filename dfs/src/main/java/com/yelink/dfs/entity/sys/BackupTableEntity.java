package com.yelink.dfs.entity.sys;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @description 备份表
 * @Date 2023/1/5
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BackupTableEntity {
    /**
     * 仅备份结构的表名
     */
    private List<String> onlyStructure;

    /**
     * 备份结构和数据的表名
     */
    private List<String> dataStructure;
}
