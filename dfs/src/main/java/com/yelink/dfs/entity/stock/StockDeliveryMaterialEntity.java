package com.yelink.dfs.entity.stock;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.yelink.dfs.entity.product.MaterialEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2021-05-07 17:43
 */
@Data
@Builder
@ToString
@AllArgsConstructor
@NoArgsConstructor
@TableName("dfs_stock_delivery_material")
public class StockDeliveryMaterialEntity extends Model<StockDeliveryMaterialEntity> {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ApiModelProperty("主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 发货单id
     */
    @ApiModelProperty("发货单id")
    @TableField(value = "delivery_id")
    private Integer deliveryId;

    /**
     * 物料编号
     */
    @ApiModelProperty(value = "物料编号", required = true)
    @TableField(value = "materiel_code")
    private String materielCode;


    /**
     * ERP关联物料行id
     */
    @ApiModelProperty("ERP关联物料行id")
    @TableField(value = "external_material_id")
    private String externalMaterialId;



    /**
     * 数量
     */
    @ApiModelProperty(value = "数量", required = true)
    @TableField(value = "amount")
    private Double amount;




    /**
     * 业务主体编码
     */
    @ApiModelProperty("业务主体编码")
    @TableField(value = "business_unit_code")
    private String businessUnitCode;
    /**
     * 业务主体名称
     */
    @ApiModelProperty("业务主体名称")
    @TableField(value = "business_unit_name")
    private String businessUnitName;

    /**
     * 单价
     */
    @ApiModelProperty("单价")
    @TableField(exist = false)
    private Double price;

    /**
     * 总价
     */
    @ApiModelProperty("总价")
    @TableField(exist = false)
    private Double totalPrice;

    /**
     * 物料字段
     */
    @ApiModelProperty("物料字段")
    @TableField(exist = false)
    private MaterialEntity materialFields;


    @Override
    public Serializable pkVal() {
        return this.id;
    }

}
