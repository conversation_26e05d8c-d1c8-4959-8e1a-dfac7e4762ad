package com.yelink.dfs.service.impl.pack;

import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yelink.dfs.common.UserAuthenService;
import com.yelink.dfs.constant.Constant;
import com.yelink.dfs.constant.OperationType;
import com.yelink.dfs.constant.RedisKeyPrefix;
import com.yelink.dfs.constant.pack.PackageBoxStateEnum;
import com.yelink.dfs.constant.pack.PackageRecordTypeEnum;
import com.yelink.dfs.constant.rule.AutoIncrementConfigureTypeEnum;
import com.yelink.dfs.controller.pack.ProductFlowCodeRecordManager;
import com.yelink.dfs.entity.barcode.BarCodeEntity;
import com.yelink.dfs.entity.code.ProductFlowCodeEntity;
import com.yelink.dfs.entity.management.PackageRecordEntity;
import com.yelink.dfs.entity.model.FacilitiesEntity;
import com.yelink.dfs.entity.order.WorkOrderEntity;
import com.yelink.dfs.entity.order.WorkOrderProcedureRelationEntity;
import com.yelink.dfs.entity.order.dto.WorkOrderInspectionPackageSelectDTO;
import com.yelink.dfs.entity.order.vo.InspectionPackageCountVO;
import com.yelink.dfs.entity.pack.PackageBoxEntity;
import com.yelink.dfs.entity.pack.PackageBoxMacSnRelationEntity;
import com.yelink.dfs.entity.pack.PackageLevelEntity;
import com.yelink.dfs.entity.pack.PackageOrderEntity;
import com.yelink.dfs.entity.pack.PackageTempRelateCodeEntity;
import com.yelink.dfs.entity.pack.dto.PackageBoxAddDTO;
import com.yelink.dfs.entity.pack.dto.PackageBoxBatchAddDTO;
import com.yelink.dfs.entity.pack.dto.PackageBoxPrintBagDTO;
import com.yelink.dfs.entity.pack.dto.PackageBoxSelectDTO;
import com.yelink.dfs.entity.pack.dto.PackageUnpackDTO;
import com.yelink.dfs.entity.pack.vo.PackageBoxViewVO;
import com.yelink.dfs.entity.product.CraftProcedureEntity;
import com.yelink.dfs.entity.product.MaterialEntity;
import com.yelink.dfs.entity.target.record.ReportLineEntity;
import com.yelink.dfs.entity.user.SysUserEntity;
import com.yelink.dfs.mapper.pack.PackageBoxMapper;
import com.yelink.dfs.open.v1.barcode.dto.InsertBarCodeDTO;
import com.yelink.dfs.open.v1.barcode.dto.UpdateBarCodeDTO;
import com.yelink.dfs.open.v1.locationMachine.dto.PackageBoxOpenSelectDTO;
import com.yelink.dfs.open.v1.locationMachine.dto.PackageCompleteDTO;
import com.yelink.dfs.open.v1.locationMachine.dto.PackageCreateBoxDTO;
import com.yelink.dfs.open.v1.locationMachine.dto.PackageNumUpdateDTO;
import com.yelink.dfs.open.v1.locationMachine.dto.PackageScanDTO;
import com.yelink.dfs.service.barcode.BarCodeService;
import com.yelink.dfs.service.barcode.LabelService;
import com.yelink.dfs.service.code.CodeLogService;
import com.yelink.dfs.service.code.ProductFlowCodeRecordService;
import com.yelink.dfs.service.code.ProductFlowCodeService;
import com.yelink.dfs.service.code.ScannerService;
import com.yelink.dfs.service.common.CommonService;
import com.yelink.dfs.service.management.PackageRecordService;
import com.yelink.dfs.service.model.FacilitiesService;
import com.yelink.dfs.service.order.RuleSeqService;
import com.yelink.dfs.service.order.WorkOrderProcedureRelationService;
import com.yelink.dfs.service.order.WorkOrderService;
import com.yelink.dfs.service.pack.PackageBoxMacSnRelationService;
import com.yelink.dfs.service.pack.PackageBoxService;
import com.yelink.dfs.service.pack.PackageLevelService;
import com.yelink.dfs.service.pack.PackageOrderService;
import com.yelink.dfs.service.pack.PackageTempRelateCodeService;
import com.yelink.dfs.service.product.CraftProcedureService;
import com.yelink.dfs.service.product.MaterialService;
import com.yelink.dfs.service.rule.NumberRuleService;
import com.yelink.dfs.service.target.record.ReportLineService;
import com.yelink.dfs.service.user.SysUserService;
import com.yelink.dfscommon.api.qms.InspectionSheetInterface;
import com.yelink.dfscommon.constant.Constants;
import com.yelink.dfscommon.constant.RespCodeEnum;
import com.yelink.dfscommon.constant.ResponseException;
import com.yelink.dfscommon.constant.dfs.barcode.BarCodeTypeEnum;
import com.yelink.dfscommon.constant.dfs.barcode.LabelInfoEnum;
import com.yelink.dfscommon.constant.dfs.code.CodeRelationTypeEnum;
import com.yelink.dfscommon.constant.dfs.code.ProductFlowCodeTypeEnum;
import com.yelink.dfscommon.constant.dfs.pack.PackageBoxRelateTypeEnum;
import com.yelink.dfscommon.constant.dfs.pack.PackageOrderRelateTypeEnum;
import com.yelink.dfscommon.constant.dfs.rule.NumberCodeDTO;
import com.yelink.dfscommon.constant.dfs.rule.NumberRulesConfigEntity;
import com.yelink.dfscommon.constant.model.WorkCenterTypeEnum;
import com.yelink.dfscommon.constant.qms.InspectionsSchemeTypeEnum;
import com.yelink.dfscommon.dto.ImportProgressDTO;
import com.yelink.dfscommon.dto.dfs.CodeInfoSelectDTO;
import com.yelink.dfscommon.dto.dfs.PackageRecordAddDTO;
import com.yelink.dfscommon.dto.dfs.RuleDetailDTO;
import com.yelink.dfscommon.entity.dfs.barcode.LabelEntity;
import com.yelink.dfscommon.entity.dfs.barcode.PrintDTO;
import com.yelink.dfscommon.entity.dfs.barcode.PrintOptionDTO;
import com.yelink.dfscommon.entity.dfs.barcode.RecordDTO;
import com.yelink.dfscommon.entity.dfs.productFlowCode.CodeRelevanceEntity;
import com.yelink.dfscommon.entity.qms.InspectionSheetEntity;
import com.yelink.dfscommon.pojo.Result;
import com.yelink.dfscommon.utils.DateUtil;
import com.yelink.dfscommon.utils.JacksonUtil;
import com.yelink.dfscommon.utils.MathUtil;
import com.yelink.dfscommon.utils.PageData;
import com.yelink.dfscommon.utils.PrintDataUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @Date 2023/3/5 17:07
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PackageBoxServiceImpl extends ServiceImpl<PackageBoxMapper, PackageBoxEntity> implements PackageBoxService {
    private final UserAuthenService userAuthenService;
    private final ProductFlowCodeRecordManager productFlowCodeRecordManager;
    private final ProductFlowCodeService productFlowCodeService;
    private final ProductFlowCodeRecordService productFlowCodeRecordService;
    private final ScannerService scannerService;
    public final PackageBoxMacSnRelationService packageBoxMacSnRelationService;
    private final PackageLevelService packageLevelService;
    private final PackageRecordService packageRecordService;
    private final RuleSeqService ruleSeqService;
    private final LabelService labelService;
    private final WorkOrderService workOrderService;
    private final MaterialService materialService;
    private final InspectionSheetInterface inspectionSheetInterface;
    private final SysUserService sysUserService;
    private final ReportLineService reportLineService;
    private final CraftProcedureService craftProcedureService;
    private final WorkOrderProcedureRelationService workOrderProcedureRelationService;
    private final PackageTempRelateCodeService packageTempRelateCodeService;
    private final BarCodeService barCodeService;
    private final RedisTemplate redisTemplate;
    private final NumberRuleService numberRuleService;
    private final PackageOrderService packageOrderService;
    private final FacilitiesService facilitiesService;
    private final CodeLogService codeLogService;

    /**
     * 打印时需要替换的占位符 ${ }
     */
    private static final Pattern PATTERN = Pattern.compile("\\$\\{(.*?)\\}");

    @Override
    public PageData<PackageBoxViewVO> listWorkPackageBox(PackageBoxSelectDTO selectDTO) {
        LambdaQueryWrapper<PackageBoxEntity> queryWrapper = new LambdaQueryWrapper<>();
        List<String> packageMaterialCodes = null;
        if (StringUtils.isNotBlank(selectDTO.getPackageMaterialCode())
                || StringUtils.isNotBlank(selectDTO.getPackageMaterialName())) {
            packageMaterialCodes = materialService.lambdaQuery()
                    .select(MaterialEntity::getCode)
                    .like(StringUtils.isNotBlank(selectDTO.getPackageMaterialCode()), MaterialEntity::getCode, selectDTO.getPackageMaterialCode())
                    .like(StringUtils.isNotBlank(selectDTO.getPackageMaterialName()), MaterialEntity::getName, selectDTO.getPackageMaterialName())
                    .list()
                    .stream().map(MaterialEntity::getCode).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(packageMaterialCodes)) {
                return new PageData<>(selectDTO.getCurrent(), selectDTO.getSize(), 0, new ArrayList<>());
            }
            queryWrapper.in(PackageBoxEntity::getPackageMaterialCode, packageMaterialCodes);
        }
        List<String> workOrderNumbers = null;
        if (StringUtils.isNotBlank(selectDTO.getProductOrderNumber())) {
            workOrderNumbers = workOrderService.lambdaQuery()
                    .like(StringUtils.isNotBlank(selectDTO.getProductOrderNumber()), WorkOrderEntity::getProductOrderNumber, selectDTO.getProductOrderNumber())
                    .list()
                    .stream().map(WorkOrderEntity::getWorkOrderNumber).collect(Collectors.toList());
            List<String> packageOrderNumbers = packageOrderService.lambdaQuery()
                    .like(PackageOrderEntity::getRelateNumber, selectDTO.getProductOrderNumber())
                    .list()
                    .stream().map(PackageOrderEntity::getPackageOrderNumber).collect(Collectors.toList());
            workOrderNumbers.addAll(packageOrderNumbers);
            if (CollectionUtils.isEmpty(workOrderNumbers)) {
                return new PageData<>(selectDTO.getCurrent(), selectDTO.getSize(), 0, new ArrayList<>());
            }
        }
        // 关联类型
        if (StringUtils.isNotBlank(selectDTO.getRelateTypes())) {
            List<String> relateTypes = Arrays.stream(selectDTO.getRelateTypes().split(Constants.SEP)).collect(Collectors.toList());
            queryWrapper.in(PackageBoxEntity::getRelateType, relateTypes);
        }

        queryWrapper.like(StringUtils.isNotBlank(selectDTO.getBoxNumber()), PackageBoxEntity::getBoxNumber, selectDTO.getBoxNumber())
                .like(StringUtils.isNotBlank(selectDTO.getWorkOrderNumber()), PackageBoxEntity::getWorkOrderNumber, selectDTO.getWorkOrderNumber())
                .in(!CollectionUtils.isEmpty(workOrderNumbers), PackageBoxEntity::getWorkOrderNumber, workOrderNumbers)
                .between(StringUtils.isNoneBlank(selectDTO.getCreateStartTime(), selectDTO.getCreateEndTime()), PackageBoxEntity::getCreateTime, selectDTO.getCreateStartTime(), selectDTO.getCreateEndTime())
                .orderByDesc(PackageBoxEntity::getCreateTime)
                .orderByDesc(PackageBoxEntity::getId);
        //是否打印筛选
        if(selectDTO.getIsPrint()!=null){
            if(selectDTO.getIsPrint()){
                queryWrapper.isNotNull(PackageBoxEntity::getPrintBoxTime);
            }else{
                queryWrapper.isNull(PackageBoxEntity::getPrintBoxTime);
            }
        }
        Page<PackageBoxEntity> page = this.page(new Page<>(selectDTO.getCurrent(), selectDTO.getSize()), queryWrapper);
        List<PackageBoxViewVO> vos = showName(page.getRecords());
        return new PageData<>(selectDTO.getCurrent(), selectDTO.getSize(), Long.valueOf(page.getTotal()).intValue(), vos);
    }

    private List<PackageBoxViewVO> showName(List<PackageBoxEntity> records) {
        List<PackageBoxViewVO> vos = new ArrayList<>();
        if (CollectionUtils.isEmpty(records)) {
            return vos;
        }
        // 包装层级
        Set<Integer> levelIds = records.stream().map(PackageBoxEntity::getPackageContainerId).collect(Collectors.toSet());
        Map<Integer, PackageLevelEntity> levelIdEntityMap = packageLevelService.lambdaQuery()
                .in(PackageLevelEntity::getId, levelIds)
                .list().stream().collect(Collectors.toMap(PackageLevelEntity::getId, v -> v));
        List<Integer> boxIds = records.stream().map(PackageBoxEntity::getId).collect(Collectors.toList());
        Map<Integer, List<PackageBoxMacSnRelationEntity>> boxIdRelationsMap = packageBoxMacSnRelationService.lambdaQuery()
                .in(PackageBoxMacSnRelationEntity::getBoxId, boxIds)
                .list().stream().collect(Collectors.groupingBy(PackageBoxMacSnRelationEntity::getBoxId));
        List<String> schemeCodes = records.stream().map(PackageBoxEntity::getSchemeCode).distinct().collect(Collectors.toList());
        Map<Integer, Double> levelTotalPackageFactorMap = packageLevelService.getLevelTotalPackageFactor(schemeCodes);
        // 用户名称
        Set<String> usernameSet = records.stream().map(PackageBoxEntity::getCreateBy).filter(Objects::nonNull).collect(Collectors.toSet());
        Map<String, String> userNameNickMap = sysUserService.getUserNameNickMap(new ArrayList<>(usernameSet));
        int totalInternalProductionQuality;
        for (PackageBoxEntity record : records) {
            Integer packageContainerId = record.getPackageContainerId();
            PackageLevelEntity packageLevelEntity = levelIdEntityMap.getOrDefault(packageContainerId, new PackageLevelEntity());
            List<PackageBoxMacSnRelationEntity> boxRelates = boxIdRelationsMap.getOrDefault(record.getId(), new ArrayList<>());
            List<String> nameplateSns = boxRelates.stream().map(PackageBoxMacSnRelationEntity::getNameplateSn).collect(Collectors.toList());
            // 查询内部产品关联批次
            String internalRelationBatch = null;
            if (CollectionUtils.isNotEmpty(nameplateSns)) {
                internalRelationBatch = productFlowCodeService.lambdaQuery().in(ProductFlowCodeEntity::getProductFlowCode, nameplateSns).list()
                        .stream().map(ProductFlowCodeEntity::getBatch).filter(StringUtils::isNotBlank).distinct()
                        .collect(Collectors.joining(Constants.SEP));
            }
            // 查询出最里层的包装容器层级id
            List<PackageLevelEntity> packageLevels = packageLevelService.listPackageLevelByPackageLevelId(packageContainerId);
            PackageLevelEntity oneLevel = packageLevels.stream().filter(res -> res.getLevel().equals(Constants.ONE)).collect(Collectors.toList()).get(0);

            totalInternalProductionQuality = getTotalInternalProductionQuality(oneLevel.getId(), record, boxRelates);

            PackageBoxViewVO build = PackageBoxViewVO.builder()
                    .boxId(record.getId())
                    .boxNumber(record.getBoxNumber())
                    .workOrderNumber(record.getWorkOrderNumber())
                    .packageMaterialCode(packageLevelEntity.getPackageMaterialCode())
                    .materialCode(packageLevelEntity.getMaterialCode())
                    .level(packageLevelEntity.getLevel())
                    .packageFactor(packageLevelEntity.getPackageFactor())
                    .levelTotalPackageFactor(levelTotalPackageFactorMap.get(packageContainerId))
                    .actualPackageQuality(boxRelates.size())
                    .totalInternalProductionQuality(totalInternalProductionQuality)
                    .createTime(record.getCreateTime())
                    .relateType(record.getRelateType())
                    .relateTypeName(PackageBoxRelateTypeEnum.getNameByCode(record.getRelateType()))
                    .stateName(PackageBoxStateEnum.getNameByCode(record.getState()))
                    .internalRelationBatch(internalRelationBatch)
                    .createBy(record.getCreateBy())
                    .createByName(userNameNickMap.get(record.getCreateBy()))
                    .printBoxTime(record.getPrintBoxTime())
                    .isPrint(record.getPrintBoxTime() != null)
                    .build();
            vos.add(build);
        }
        Set<String> packageMaterialCodes = vos.stream().map(PackageBoxViewVO::getPackageMaterialCode).collect(Collectors.toSet());
        packageMaterialCodes.addAll(vos.stream().map(PackageBoxViewVO::getMaterialCode).collect(Collectors.toSet()));
        Map<String, MaterialEntity> codeMaterialMap  = materialService.lambdaQuery()
                .in(MaterialEntity::getCode, packageMaterialCodes)
                .list().stream().collect(Collectors.toMap(MaterialEntity::getCode, v -> v, (o, n) -> n));
        // 查询物料信息
        for (PackageBoxViewVO vo : vos) {
            MaterialEntity packageMaterialEntity = codeMaterialMap.get(vo.getPackageMaterialCode());
            if (packageMaterialEntity != null) {
                vo.setPackageMaterialName(packageMaterialEntity.getName());
            }
            MaterialEntity materialEntity = codeMaterialMap.get(vo.getMaterialCode());
            if (materialEntity != null) {
                vo.setMaterialName(materialEntity.getName());
            }
        }
        return vos;
    }

    /**
     * 获取 盒子内部总生产品数量
     */
    private int getTotalInternalProductionQuality(Integer oneLevelId, PackageBoxEntity record, List<PackageBoxMacSnRelationEntity> boxRelates) {
        if (record.getPackageContainerId().equals(oneLevelId) || CollectionUtils.isEmpty(boxRelates)) {
            // 本来就是最里层级
            return CollectionUtils.isEmpty(boxRelates) ? 0 : boxRelates.size();
        }
        int totalInternalProductionQuality = 0;
        List<String> nameplateSns = boxRelates.stream().map(PackageBoxMacSnRelationEntity::getNameplateSn).collect(Collectors.toList());
        List<PackageBoxEntity> innerBoxs = this.lambdaQuery()
                .eq(PackageBoxEntity::getWorkOrderNumber, record.getWorkOrderNumber())
                .in(PackageBoxEntity::getBoxNumber, nameplateSns)
                .list();
        if (!CollectionUtils.isEmpty(innerBoxs)) {
            Map<Integer, List<PackageBoxMacSnRelationEntity>> innerBoxIdRelationsMap = packageBoxMacSnRelationService.lambdaQuery()
                    .in(PackageBoxMacSnRelationEntity::getBoxId, innerBoxs.stream().map(PackageBoxEntity::getId).collect(Collectors.toList()))
                    .list().stream()
                    .collect(Collectors.groupingBy(PackageBoxMacSnRelationEntity::getBoxId));
            if (innerBoxs.get(0).getPackageContainerId().equals(oneLevelId)) {
                // 说明是最里层级了
                for (PackageBoxEntity innerBox : innerBoxs) {
                    List<PackageBoxMacSnRelationEntity> relations = innerBoxIdRelationsMap.getOrDefault(innerBox.getId(), new ArrayList<>());
                    totalInternalProductionQuality += relations.size();
                }
            } else {
                for (PackageBoxEntity innerBox : innerBoxs) {
                    totalInternalProductionQuality += getTotalInternalProductionQuality(oneLevelId, innerBox, innerBoxIdRelationsMap.get(innerBox.getId()));
                }
            }
        }
        return totalInternalProductionQuality;
    }

    @Override
    public PackageBoxViewVO getPackageBoxById(Integer boxId) {
        PackageBoxEntity packageBox = this.getById(boxId);
        if (Objects.isNull(packageBox)) {
            log.error("不存在该包装容器,{}", boxId);
            throw new ResponseException("不存在该包装容器");
        }
        MaterialEntity material =  materialService.getSimpleMaterialByCode(packageBox.getPackageMaterialCode());
        packageBox.setPackageMaterialFields(material);
        PackageLevelEntity packageLevel = packageLevelService.getById(packageBox.getPackageContainerId());

        // 查询该盒被放进哪一个盒子, 查出来应该只有一个元素
        List<PackageBoxMacSnRelationEntity> putIntoRelation = packageBoxMacSnRelationService.listRelation(null, packageBox.getBoxNumber());

        PackageBoxViewVO vo = PackageBoxViewVO.builder()
                .boxId(packageBox.getId())
                .boxNumber(packageBox.getBoxNumber())
                .workOrderNumber(packageBox.getWorkOrderNumber())
                .materialCode(packageBox.getPackageMaterialCode())
                .materialName(Objects.isNull(material) ? null : material.getName())
                //.productOrderNumber(Objects.isNull(material) ? null : workOrder.getProductOrderNumber())
                .level(packageLevel.getLevel())
                //.containerName(packageLevel.getContainerName())
                .level(packageLevel.getLevel())
                .putIntoBoxNumber(CollectionUtils.isEmpty(putIntoRelation) ? null : putIntoRelation.get(0).getBoxNumber())
                .createTime(packageBox.getCreateTime())
                .build();
        // 设置包装层级关系
        vo.setPackageBoxLevel(getPackageBoxRelate(packageBox));
        return vo;
    }

    /**
     * 获取包装层级关系
     */
    private PackageBoxViewVO.PackageBoxLevelVO getPackageBoxRelate(PackageBoxEntity packageBox) {
        MaterialEntity packageMaterialFields = packageBox.getPackageMaterialFields();
        PackageBoxViewVO.PackageBoxLevelVO build = PackageBoxViewVO.PackageBoxLevelVO.builder()
                .boxId(packageBox.getId())
                .boxNumber(packageBox.getBoxNumber())
                .level(packageBox.getLevel())
                .containerName(packageMaterialFields == null ? null : packageMaterialFields.getName())
                .build();
        List<PackageBoxMacSnRelationEntity> boxRelations = packageBoxMacSnRelationService.lambdaQuery()
                .eq(PackageBoxMacSnRelationEntity::getBoxId, packageBox.getId())
                .orderByDesc(PackageBoxMacSnRelationEntity::getCreateTime)
                .list();
        if (CollectionUtils.isEmpty(boxRelations)) {
            build.setRelations(new ArrayList<>());
            return build;
        }
        List<PackageBoxEntity> innerBoxs = this.lambdaQuery()
                .eq(PackageBoxEntity::getWorkOrderNumber, packageBox.getWorkOrderNumber())
                .in(PackageBoxEntity::getBoxNumber, boxRelations.stream().map(PackageBoxMacSnRelationEntity::getNameplateSn).collect(Collectors.toList()))
                .list();
        if (CollectionUtils.isEmpty(innerBoxs)) {
            build.setRelations(boxRelations);
            return build;
        }
        Set<String> packageMaterialCodes = innerBoxs.stream().map(PackageBoxEntity::getPackageMaterialCode).collect(Collectors.toSet());
        Map<String, MaterialEntity> materialMap = materialService.lambdaQuery().in(MaterialEntity::getCode, packageMaterialCodes).list()
                .stream().collect(Collectors.toMap(MaterialEntity::getCode, v -> v));
        List<PackageBoxViewVO.PackageBoxLevelVO> subPackageBoxLevels = new ArrayList<>();
        for (PackageBoxEntity innerBox : innerBoxs) {
            innerBox.setPackageMaterialFields(materialMap.get(innerBox.getPackageMaterialCode()));
            subPackageBoxLevels.add(getPackageBoxRelate(innerBox));
        }
        build.setSubPackageBoxLevels(subPackageBoxLevels);
        return build;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public PackageBoxEntity createBox(PackageCreateBoxDTO dto) {
        String boxNumber = dto.getBoxNumber();
        String relateNumber = dto.getRelateNumber();
        Integer packageLevelId = dto.getPackageLevelId();
        String username = dto.getUsername();
        PackageBoxEntity packageBox = this.lambdaQuery().eq(PackageBoxEntity::getBoxNumber, boxNumber).one();
        if (Objects.nonNull(packageBox)) {
            log.error("箱标编码重复,{}", boxNumber);
            throw new ResponseException("箱标编码重复");
        }
        Date nowDate = new Date();
        // 找到包装层级
        PackageLevelEntity packageLevelEntity = packageLevelService.getById(packageLevelId);
        PackageBoxEntity build = PackageBoxEntity.builder()
                .boxNumber(boxNumber)
                .workOrderNumber(relateNumber)
                .relateType(dto.getRelateType())
                .packageContainerId(packageLevelId)
                .schemeCode(packageLevelEntity.getSchemeCode())
                .level(packageLevelEntity.getLevel())
                .packageMaterialCode(packageLevelEntity.getPackageMaterialCode())
                .state(PackageBoxStateEnum.NOT_PACKAGE.getCode())
                .operateTime(nowDate)
                .createTime(nowDate)
                .createBy(username)
                .build();
        this.save(build);
        if (PackageBoxRelateTypeEnum.PACKAGE_ORDER.getCode().equals(dto.getRelateType())) {
            ProductFlowCodeEntity productFlowCodeEntity = ProductFlowCodeEntity.builder().productFlowCode(boxNumber)
                    .type(ProductFlowCodeTypeEnum.PACKAGE_CODE.getCode())
                    .relationType(ProductFlowCodeTypeEnum.PACKAGE_CODE.getRelationType())
                    .relationNumber(relateNumber)
                    .createBy(username)
                    .createTime(nowDate)
                    .materialCode(packageLevelEntity.getPackageMaterialCode())
                    .skuId(Constants.SKU_ID_DEFAULT_VAL)
                    .build();
            productFlowCodeService.save(productFlowCodeEntity);
            // 添加日志
            codeLogService.addCodeLog(Stream.of(productFlowCodeEntity).collect(Collectors.toList()), OperationType.ADD);
        }
        // 更新编码规则自增序列号
        updateRuleSeq(relateNumber, packageLevelEntity, dto.getRuleId());
        return build;
    }

    /**
     * 更新编码规则自增序列号
     */
    private void updateRuleSeq(String workOrderNumber, PackageLevelEntity packageLevelEntity, Integer ruleId) {
        if (Objects.isNull(ruleId)) {
            return;
        }
        Map<String, String> relatedMap = new HashMap<>(8);
        relatedMap.put(AutoIncrementConfigureTypeEnum.WORK_ORDER_PACKAGE.getCode(), workOrderNumber + "__" + packageLevelEntity.getLevel());
        relatedMap.put(AutoIncrementConfigureTypeEnum.WORK_ORDER.getCode(), workOrderNumber);
        relatedMap.put(AutoIncrementConfigureTypeEnum.PACKAGE_ORDER_LEVEL.getCode(), workOrderNumber + "_" + packageLevelEntity.getLevel());
        relatedMap.put(AutoIncrementConfigureTypeEnum.PACKAGE_ORDER.getCode(), workOrderNumber);
        // 编码规则有自动生成序号的，seq加1
        ruleSeqService.updateSeqEntity(relatedMap, ruleId, false);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void packageScanSn(String scanCode, String boxNumber, String workOrderNumber, Integer facId, Integer packageLevelId, Boolean enableScanSnAdd) {
        PackageBoxEntity box = this.getPackageBoxByNumber(boxNumber, workOrderNumber);
        if (Objects.isNull(box)) {
            throw new ResponseException("容器不存在");
        }
        if (!box.getPackageContainerId().equals(packageLevelId)) {
            log.error("容器的包装层级与所选层级不一致, {}", scanCode);
            throw new ResponseException("容器的包装层级与所选层级不一致");
        }
        if (boxNumber.equals(scanCode)) {
            log.error("扫码信息有误,不能扫码本箱, {}, {}", scanCode, boxNumber);
            throw new ResponseException("扫码信息有误,不能扫码本箱");
        }
        boolean exists = packageBoxMacSnRelationService.lambdaQuery()
                .eq(PackageBoxMacSnRelationEntity::getBoxId, box.getId())
                .eq(PackageBoxMacSnRelationEntity::getNameplateSn, scanCode)
                .exists();
        if (exists) {
            log.info("当前产品已存在于当前包装,请勿重复包装, {},{}", scanCode, boxNumber);
            throw new ResponseException("当前产品已存在于当前包装,请勿重复包装");
        }
        // 如果是如果当前的包装层级为包装的第一个层级，则扫到的SN是生产流水码或生产成品码（此时都要追溯到生产流水码）
        ProductFlowCodeEntity productFlowCode = productFlowCodeService.lambdaQuery()
                .eq(ProductFlowCodeEntity::getProductFlowCode, scanCode)
                .one();
        //开启配置-“产品支持扫码添加”后，判断扫码是否存在，如果不存在，会反向添加到条码中心
        if (Objects.nonNull(enableScanSnAdd) && enableScanSnAdd && Objects.isNull(productFlowCode)) {
            String username = userAuthenService.getUsername();
            PackageLevelEntity packageLevel = packageLevelService.getById(packageLevelId);
            //不存在，会反向添加到条码中心
            List<InsertBarCodeDTO> barCodeInserts = new ArrayList<>();
            barCodeInserts.add(InsertBarCodeDTO.builder()
                    .barCode(scanCode)
                    .relateNumber(workOrderNumber)
                    .materialCode(packageLevel.getMaterialCode())
                    .skuId(Constants.SKU_ID_DEFAULT_VAL)
                    .count(1.0)
                    .moduleType(BarCodeTypeEnum.FINISHED.getCode())
                    .build());
            barCodeService.batchAddBarCode(barCodeInserts, username);
            productFlowCode = productFlowCodeService.lambdaQuery()
                    .eq(ProductFlowCodeEntity::getProductFlowCode, scanCode)
                    .one();
        }
        if (Objects.isNull(productFlowCode)) {
            log.error("未找到关联的生产流水码, scanCode:{}", scanCode);
            throw new ResponseException("未找到关联的生产流水码");
        } else {
            WorkOrderEntity workOrder = workOrderService.getSimpleWorkOrderByNumber(workOrderNumber);
            if (!(workOrderNumber.equals(productFlowCode.getRelationNumber()) || productFlowCode.getRelationNumber().equals(workOrder.getProductOrderNumber()))) {
                log.error("未找到工单或生产订单关联的生产流水码, workOrderNumber:{}, scanCode:{}", workOrderNumber, scanCode);
                throw new ResponseException("未找到工单或生产订单关联的生产流水码");
            }
        }
        // 非包装层级是第一个层级
        List<PackageLevelEntity> packageLevels = packageLevelService.listPackageLevelByPackageLevelId(packageLevelId);
        List<PackageLevelEntity> levels = packageLevels.stream().filter(res -> res.getId().equals(packageLevelId)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(levels)) {
            log.error("未找到对应包装层级, {}", packageLevelId);
            throw new ResponseException("未找到对应包装层级");
        }
        if (!levels.get(0).getLevel().equals(Constants.ONE)) {
            // scanCode 是 包装成品码 或 包装成品码对应盒子中的包装SN
            // 第二次包装扫到的则为第一层包装产生的包装成品码（同时若扫到前一层的包装SN，需要查找到前一层的容器SN）
            List<PackageBoxMacSnRelationEntity> list = packageBoxMacSnRelationService.lambdaQuery()
                    .eq(PackageBoxMacSnRelationEntity::getNameplateSn, scanCode)
                    .list();
            if (!CollectionUtils.isEmpty(list)) {
                scanCode = list.get(0).getBoxNumber();
            }
        }
        // 保存关联关系
        packageBoxMacSnRelationService.saveRelation(box.getId(), boxNumber, scanCode);
        //添加过站记录
        productFlowCodeRecordManager.addProductFlowCode(scanCode, boxNumber, workOrderNumber, facId);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void generalPackageRemoveRelation(String nameplateSn, String boxNumber, String workOrderNumber, Integer facId, String relateType) {
        PackageBoxEntity packageBox = this.getPackageBoxByNumber(boxNumber, workOrderNumber, relateType);
        packageBoxMacSnRelationService.lambdaUpdate()
                .eq(PackageBoxMacSnRelationEntity::getBoxId, packageBox.getId())
                .eq(PackageBoxMacSnRelationEntity::getNameplateSn, nameplateSn)
                .remove();
        if (PackageBoxRelateTypeEnum.WORK_ORDER.getCode().equals(relateType)) {
            // 更新过站记录状态为失效
            productFlowCodeRecordService.disableRecordByFid(RecordDTO.builder().productFlowCode(nameplateSn).fid(facId).workOrderNumber(workOrderNumber).build());
            // 移除条码关系
            CodeRelevanceEntity build = CodeRelevanceEntity.builder().code(boxNumber).relevanceCode(nameplateSn).build();
            productFlowCodeService.removeRelevance(build);
        } else {
            // 移除条码关系
            CodeRelevanceEntity build = CodeRelevanceEntity.builder().code(boxNumber).relevanceCode(nameplateSn).build();
            productFlowCodeService.removeRelevance(build);
        }
    }

    @Override
    public PackageBoxEntity getPackageBoxByNumber(String boxNumber, String workOrderNumber) {
        return this.getPackageBoxByNumber(boxNumber, workOrderNumber, PackageBoxRelateTypeEnum.WORK_ORDER.getCode());
    }

    @Override
    public PackageBoxEntity getPackageBoxByNumber(String boxNumber, String relateNumber, String relateType) {
        return this.lambdaQuery()
                .eq(PackageBoxEntity::getBoxNumber, boxNumber)
                .eq(PackageBoxEntity::getWorkOrderNumber, relateNumber)
                .eq(PackageBoxEntity::getRelateType, relateType)
                .one();
    }

    @Override
    public List<PackageBoxEntity> listPackageBox(String workOrderNumber, String states, Integer packageLevelId) {
        List<Integer> stateList = null;
        if (StringUtils.isNotBlank(states)) {
            stateList = Arrays.stream(states.split(Constants.SEP)).map(Integer::valueOf).collect(Collectors.toList());
        }
        return this.lambdaQuery()
                .eq(Objects.nonNull(packageLevelId), PackageBoxEntity::getPackageContainerId, packageLevelId)
                .in(StringUtils.isNotBlank(states), PackageBoxEntity::getState, stateList)
                .eq(PackageBoxEntity::getWorkOrderNumber, workOrderNumber)
                .orderByDesc(PackageBoxEntity::getCreateTime)
                .list();
    }

    @Override
    public List<PackageBoxEntity> generalListPackageBox(String workOrderNumber, String states, Integer packageLevelId) {
        List<PackageBoxEntity> packageBoxList = listPackageBox(workOrderNumber, states, packageLevelId);
        Map<String, Integer> packageFactorMap = new HashMap<>();
        // 查询容器内装的sn数量和包装系数
        for (PackageBoxEntity packageBox : packageBoxList) {
            Long count = packageBoxMacSnRelationService.lambdaQuery()
                    .eq(PackageBoxMacSnRelationEntity::getBoxId, packageBox.getId())
                    .count();
            packageBox.setPackageCount(count.intValue());
            // 包装系数
            Integer packageFactor = packageFactorMap.get(packageBox.getSchemeCode() + packageBox.getLevel());
            if (Objects.isNull(packageFactor)) {
                PackageLevelEntity packageLevel = packageLevelService.lambdaQuery()
                        .eq(PackageLevelEntity::getSchemeCode, packageBox.getSchemeCode())
                        .eq(PackageLevelEntity::getLevel, packageBox.getLevel())
                        .last("limit 1").one();
                packageFactor = Objects.isNull(packageLevel) ? 0 : packageLevel.getPackageFactor().intValue();
                packageFactorMap.put(packageBox.getSchemeCode() + packageBox.getLevel(), packageFactor);
            }
            packageBox.setPackageFactor(packageFactor);
        }
        return packageBoxList;
    }

    @Override
    public void updateBoxState(Integer boxId, int state) {
        this.lambdaUpdate()
                .eq(PackageBoxEntity::getId, boxId)
                .set(PackageBoxEntity::getState, state)
                .set(PackageBoxEntity::getOperateTime, new Date())
                .update();
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean generalPackageComplete(PackageCompleteDTO dto) {
        String boxNumber = dto.getBoxNumber();
        String relateNumber = dto.getRelateNumber();
        String username = dto.getUsername();
        Integer fid = dto.getFid();
        PackageBoxEntity box = this.getPackageBoxByNumber(boxNumber, relateNumber, dto.getRelateType());
        //查询外箱关联的彩盒信息列表
        List<PackageBoxMacSnRelationEntity> relations = packageBoxMacSnRelationService.lambdaQuery()
                .eq(PackageBoxMacSnRelationEntity::getBoxId, box.getId())
                .list();
        if (relations.size() <= 0) {
            log.error("该外箱没有绑定SN,无法完成包装,{}", boxNumber);
            throw new ResponseException("该外箱没有绑定SN,无法完成包装");
        }
        if (PackageBoxStateEnum.PACKAGE_COMPLETED.getCode().equals(box.getState())) {
            log.error("该外箱已完成包装,请勿重复提交,{}", boxNumber);
            throw new ResponseException("该外箱已完成包装,请勿重复提交");
        }
        this.updateBoxState(box.getId(), PackageBoxStateEnum.PACKAGE_COMPLETED.getCode());
        String materialCode, moduleType;
        Integer skuId, lineId = null;
        if (fid != null) {
            FacilitiesEntity facilitiesEntity = facilitiesService.getById(fid);
            if (facilitiesEntity == null) {
                throw new ResponseException("未找到工位信息");
            }
            lineId = facilitiesEntity.getProductionLineId();
        }
        if (PackageBoxRelateTypeEnum.PACKAGE_ORDER.getCode().equals(box.getRelateType())) {
            materialCode = box.getPackageMaterialCode();
            skuId = Constants.SKU_ID_DEFAULT_VAL;
            moduleType = BarCodeTypeEnum.PACKAGE_BAR_CODE.getCode();
            if (lineId == null) {
                PackageOrderEntity packageOrderEntity = packageOrderService.getSimpleEntity(relateNumber);
                if (packageOrderEntity == null) {
                    throw new ResponseException("未找到包装工单信息");
                }
                if (WorkCenterTypeEnum.LINE.getCode().equals(packageOrderEntity.getWorkCenterType())) {
                    lineId = packageOrderEntity.getProductionBasicUnitId();
                }
            }
        } else {
            WorkOrderEntity workOrder = workOrderService.getSimpleWorkOrderByNumber(box.getWorkOrderNumber());
            materialCode = workOrder.getMaterialCode();
            skuId = workOrder.getSkuId();
            moduleType = BarCodeTypeEnum.FINISHED.getCode();

            // 箱号加到条码中心，对应的物料应该是包装层级对应的包装物料编码,而不仅仅是工单的物料编码
            PackageLevelEntity packageLevel = packageLevelService.getById(box.getPackageContainerId());
            if (Objects.nonNull(packageLevel)) {
                materialCode = packageLevel.getPackageMaterialCode();
                skuId = Constants.SKU_ID_DEFAULT_VAL;
            }
        }

        // 生产包装记录
        List<PackageRecordAddDTO> list = new ArrayList<>();
        for (PackageBoxMacSnRelationEntity relation : relations) {
            list.add(PackageRecordAddDTO.builder()
                    .workOrderNumber(relateNumber)
                    .boxNumber(boxNumber)
                    .packageLevelId(box.getPackageContainerId())
                    .sn(relation.getNameplateSn())
                    .materialCode(materialCode)
                    .packType(PackageRecordTypeEnum.PACKAGING.getCode())
                    .username(username)
                    .lineId(lineId)
                    .fid(fid)
                    .build());
        }
        packageRecordService.batchAdd(list);

        BarCodeEntity barCodeEntity = barCodeService.getBarCodeByCode(boxNumber);
        List<InsertBarCodeDTO> barCodeInserts = new ArrayList<>();
        barCodeInserts.add(InsertBarCodeDTO.builder()
                .barCode(boxNumber)
                .relateNumber(relateNumber)
                .materialCode(materialCode)
                .skuId(skuId)
                .count((double) relations.size())
                .finishCount((double) relations.size())
                .moduleType(moduleType)
                .build());
        if (barCodeEntity != null) {
            List<UpdateBarCodeDTO> updateBarCodes = JacksonUtil.convertArray(barCodeInserts, UpdateBarCodeDTO.class);
            barCodeService.batchUpdateBarCode(updateBarCodes, username);
        } else {
            barCodeService.batchAddBarCode(barCodeInserts, username);
        }
        // 更新已包装数量
        return updatePackageCount(box);
    }

    @Override
    public PackageBoxEntity generalsUnpackingScanCodeGetPackageBox(String scanCode, String workOrderNumber, Integer packageLevelId) {
        // 扫码可能扫到Carton No或者是箱子里面的某个产品SN
        // 扫码可能扫到的是箱子里面的某个产品SN
        List<PackageBoxMacSnRelationEntity> list = packageBoxMacSnRelationService.listRelation(null, scanCode);
        PackageBoxEntity packageBox = CollectionUtils.isEmpty(list) ? null : this.getById(list.get(0).getBoxId());
        if (Objects.isNull(packageBox)) {
            // 扫码可能扫到Carton No
            packageBox = this.getPackageBoxByNumber(scanCode, workOrderNumber);
        }
        if (Objects.isNull(packageBox)) {
            log.error("未找到编码对应的容器SN, {}", scanCode);
            throw new ResponseException("未找到编码对应的容器SN");
        }
        if (!packageBox.getWorkOrderNumber().equals(workOrderNumber)) {
            log.error("扫描到的容器与工单不匹配, {}", JSON.toJSONString(packageBox));
            throw new ResponseException("扫描到的容器与工单不匹配");
        }
        if (!packageBox.getPackageContainerId().equals(packageLevelId)) {
            log.error("编码对应容器的包装层级与所选层级不一致, {}", scanCode);
            throw new ResponseException("编码对应容器的包装层级与所选层级不一致");
        }
        if (!(PackageBoxStateEnum.PACKAGE_COMPLETED.getCode().equals(packageBox.getState())
                || PackageBoxStateEnum.UNPACKING.getCode().equals(packageBox.getState())
                || PackageBoxStateEnum.CLOSING_COMPLETED.getCode().equals(packageBox.getState()))) {
            log.error("拆箱只能拆包装完成的箱子, {}", scanCode);
            throw new ResponseException("拆箱只能拆包装完成的箱子");
        }
        // 上层层级是否拆分,需要严格按照包装层级进行拆分：若想要拆分一个二级包装，但是其已经被包装到了一个三级包装中，需要先在三级包装中进行拆分；
        List<PackageBoxMacSnRelationEntity> parRelates = packageBoxMacSnRelationService.listRelation(null, packageBox.getBoxNumber());
        while (!CollectionUtils.isEmpty(parRelates)) {
            PackageBoxEntity tempBox = this.getById(parRelates.get(0).getBoxId());
            if (Objects.isNull(tempBox)) {
                break;
            }
            if (PackageBoxStateEnum.PACKAGE_COMPLETED.getCode().equals(tempBox.getState())) {
                log.error("请严格按照包装层级进行拆分, {}", scanCode);
                throw new ResponseException("请严格按照包装层级进行拆分");
            }
            parRelates = packageBoxMacSnRelationService.listRelation(null, tempBox.getBoxNumber());
        }
        return packageBox;
    }

    @Override
    public String generalUnpackingScanCodeGetSn(String scanCode, String boxNumber, String workOrderNumber, Integer packageLevelId) {
        PackageBoxEntity packageBox = this.getPackageBoxByNumber(boxNumber, workOrderNumber);
        if (!packageBox.getPackageContainerId().equals(packageLevelId)) {
            log.error("容器的包装层级与所选层级不一致, {}", scanCode);
            throw new ResponseException("容器的包装层级与所选层级不一致");
        }
        List<PackageBoxMacSnRelationEntity> list = packageBoxMacSnRelationService.lambdaQuery()
                .eq(PackageBoxMacSnRelationEntity::getBoxId, packageBox.getId())
                .eq(PackageBoxMacSnRelationEntity::getNameplateSn, scanCode)
                .list();
        if (CollectionUtils.isEmpty(list)) {
            log.error("箱子内未找到对应sn, boxNumber:{}, scanCode:{}", boxNumber, scanCode);
            throw new ResponseException("箱子内未找到对应sn");
        }
        return scanCode;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void generalUnpackingConfirm(PackageUnpackDTO packageUnpacking, String username) {
        PackageBoxEntity box = this.getPackageBoxByNumber(packageUnpacking.getBoxNumber(), packageUnpacking.getWorkOrderNumber());
        // 删除关联
        packageBoxMacSnRelationService.lambdaUpdate()
                .eq(PackageBoxMacSnRelationEntity::getBoxId, box.getId())
                .in(PackageBoxMacSnRelationEntity::getNameplateSn, packageUnpacking.getNameplateSns())
                .remove();
        // 更新过站记录状态为失效
        productFlowCodeRecordService.disableRecordByFid(RecordDTO.builder().productFlowCodes(packageUnpacking.getNameplateSns()).fid(packageUnpacking.getFacId()).workOrderNumber(box.getWorkOrderNumber()).build());
        // 更新状态
        this.updateBoxState(box.getId(), PackageBoxStateEnum.UNPACKING.getCode());
        // 生产拆箱记录
        List<PackageRecordAddDTO> list = packageUnpacking.getNameplateSns().stream().map(nameplateSn ->
                PackageRecordAddDTO.builder()
                        .workOrderNumber(box.getWorkOrderNumber())
                        .boxNumber(box.getBoxNumber())
                        .packageLevelId(box.getPackageContainerId())
                        .sn(nameplateSn)
                        .packType(PackageRecordTypeEnum.DEVANNING.getCode())
                        .username(username)
                        .build()).collect(Collectors.toList());
        packageRecordService.batchAdd(list);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void generalUnpackingWholeBoxSplit(PackageUnpackDTO packageUnpacking, String username) {
        PackageBoxEntity box = this.getPackageBoxByNumber(packageUnpacking.getBoxNumber(), packageUnpacking.getWorkOrderNumber());
        List<PackageBoxMacSnRelationEntity> list = packageBoxMacSnRelationService.lambdaQuery()
                .eq(PackageBoxMacSnRelationEntity::getBoxId, box.getId())
                .list();
        if (CollectionUtils.isEmpty(list)) {
            log.error("箱子内无内容,无法整箱拆分, {}", packageUnpacking.getBoxNumber());
            throw new ResponseException("箱子内无内容,无法整箱拆分");
        }
        // 删除关联
        packageBoxMacSnRelationService.lambdaUpdate()
                .eq(PackageBoxMacSnRelationEntity::getBoxId, box.getId())
                .remove();
        List<String> nameplateSns = list.stream().map(PackageBoxMacSnRelationEntity::getNameplateSn).collect(Collectors.toList());
        // 更新过站记录状态为失效
        productFlowCodeRecordService.disableRecordByFid(RecordDTO.builder().productFlowCodes(nameplateSns).fid(packageUnpacking.getFacId()).workOrderNumber(packageUnpacking.getWorkOrderNumber()).build());
        // 更新状态
        this.updateBoxState(box.getId(), PackageBoxStateEnum.UNPACKING.getCode());
        // 生产拆箱记录
        List<PackageRecordAddDTO> packageRecords = nameplateSns.stream().map(nameplateSn ->
                PackageRecordAddDTO.builder()
                        .workOrderNumber(box.getWorkOrderNumber())
                        .boxNumber(box.getBoxNumber())
                        .packageLevelId(box.getPackageContainerId())
                        .sn(nameplateSn)
                        .packType(PackageRecordTypeEnum.DEVANNING.getCode())
                        .username(username)
                        .build()).collect(Collectors.toList());
        packageRecordService.batchAdd(packageRecords);
    }

    @Override
    public PrintDTO printPackage(String boxNumber, String workOrderNumber, Integer ruleId) {
        PackageBoxEntity packageBox = this.getPackageBoxByNumber(boxNumber, workOrderNumber);
        //查询外盒关联的内盒SN的信息列表
        List<PackageBoxMacSnRelationEntity> packageBoxMacSnRelations = packageBoxMacSnRelationService.lambdaQuery()
                .eq(PackageBoxMacSnRelationEntity::getBoxId, packageBox.getId())
                .orderByAsc(PackageBoxMacSnRelationEntity::getCreateTime)
                .list();

        if (CollectionUtils.isEmpty(packageBoxMacSnRelations)) {
            log.error("没有打印数据,{}", boxNumber);
            throw new ResponseException("没有打印数据");
        }
        WorkOrderEntity workOrder = workOrderService.getSimpleWorkOrderByNumber(workOrderNumber);
        if (Objects.isNull(workOrder)) {
            throw new ResponseException("工单不存在");
        }
        MaterialEntity materialEntity = materialService.getSimpleMaterialByCode(workOrder.getMaterialCode());
        workOrder.setMaterialFields(materialEntity);

        // 获取打印模板, 替换打印模板里{ }的数据
        LabelEntity labelEntity = labelService.getById(ruleId);
        if (Objects.isNull(labelEntity)) {
            throw new ResponseException(RespCodeEnum.NOT_FIND_BAR_CODE_RULE);
        }
        PrintDTO printDTO = JSON.parseObject(labelEntity.getContent(), PrintDTO.class);
        List<PrintDTO.ParamDto.Content> printElements = printDTO.getPanels().get(0).getPrintElements();

        for (PrintDTO.ParamDto.Content content : printElements) {
            PrintOptionDTO options = content.getOptions();
            String data = options.getTitle();
            if (StringUtils.isBlank(data)) {
                continue;
            }
            // 替换占位符, 替换值为查询对象的某个属性值
            Matcher matcher = PATTERN.matcher(data);
            while (matcher.find()) {
                String placeholder = matcher.group();
                // 铭牌SN 特殊处理
                String boxNameplateSn = "";
                if (placeholder.contains(LabelInfoEnum.THIS_LEVEL_OF_PACKAGE_PRODUCT_SN.getCode())) {
                    String indexString = placeholder.replace(LabelInfoEnum.THIS_LEVEL_OF_PACKAGE_PRODUCT_SN.getCode(), "")
                            .replace("${", "")
                            .replace("}", "");
                    if (StringUtils.isNumeric(indexString)) {
                        int index = Integer.parseInt(indexString);
                        boxNameplateSn = index > packageBoxMacSnRelations.size() ? "" : packageBoxMacSnRelations.get(index - 1).getNameplateSn();
                    }
                }
                String placeholderValue = replacePrintData(placeholder, content.getOptions().getSliceDigits(), workOrder, packageBox, boxNameplateSn, packageBoxMacSnRelations.size());
                data = data.replace(placeholder, placeholderValue);
            }
            content.getOptions().setTitle(data);
            // 字体大小如果为空,则设置默认值
            if (content.getOptions().getFontSize() == null) {
                content.getOptions().setFontSize(9.0);
            }
        }
        return labelService.getPrintDtoByOpenApi(printDTO);
    }

    /**
     * 针对东风电工专门开发，特殊场景
     * @param selectDTO
     * @param username
     * @return
     */
    @Override
    public PrintDTO printBag(PackageBoxPrintBagDTO selectDTO, String username) {
        //获取打印数据
        WorkOrderEntity workOrderByNumber = workOrderService.lambdaQuery()
                .eq(WorkOrderEntity::getWorkOrderNumber, selectDTO.getWorkOrderNumber())
                .one();
        //获取操作人，批次对应的生产订单下推的首工序对应的工单的最后一个报工记录报工人
        if (StringUtils.isBlank(workOrderByNumber.getProductOrderNumber()) || Objects.isNull(workOrderByNumber.getCraftId())) {
            throw new ResponseException("工单没有对应的生产订单或工艺信息");
        }
        List<CraftProcedureEntity> list = craftProcedureService.lambdaQuery().eq(CraftProcedureEntity::getCraftId, workOrderByNumber.getCraftId())
                .and(o->o.eq(CraftProcedureEntity::getSupProcedureId, Constant.EMPTY).or().isNull(CraftProcedureEntity::getSupProcedureId))
                .list();
        if (CollectionUtils.isEmpty(list)) {
            throw new ResponseException("没有找到批次对应的工单的首工序信息");
        }
        List<Integer> workOrderIds = workOrderService.lambdaQuery()
                .eq(WorkOrderEntity::getProductOrderNumber, workOrderByNumber.getProductOrderNumber())
                .list()
                .stream().map(WorkOrderEntity::getWorkOrderId)
                .collect(Collectors.toList());
        List<WorkOrderProcedureRelationEntity> list1 = workOrderProcedureRelationService.lambdaQuery().in(WorkOrderProcedureRelationEntity::getWorkOrderId, workOrderIds)
                .eq(WorkOrderProcedureRelationEntity::getProcedureId, list.get(0).getProcedureId()).list();
        if (CollectionUtils.isEmpty(list1)) {
            throw new ResponseException("没有找到批次对应的首工序工单");
        }
        ReportLineEntity byWorkOrder = reportLineService.getFirstByWorkOrder(list1.stream().map(WorkOrderProcedureRelationEntity::getWorkOrderNumber).collect(Collectors.toList())
                , selectDTO.getProductBatchCode());
        String userByUsername = null;
        if (Objects.nonNull(byWorkOrder) && StringUtils.isNotBlank(byWorkOrder.getOperatorName())) {
            List<SysUserEntity> listByNickNames = sysUserService.getListByNickNames(Arrays.asList(byWorkOrder.getOperatorName().split(Constants.SEP)));
            List<String> collect = listByNickNames.stream().map(SysUserEntity::getJobNumber).filter(Objects::nonNull).collect(Collectors.toList());
            userByUsername = StringUtils.join(collect, Constants.SEP);
        }
        // 新版本通过前端选择的标签规则进行打印
        LabelEntity labelEntity = labelService.lambdaQuery().eq(LabelEntity::getRuleId, selectDTO.getRuleId()).one();
        if (labelEntity == null) {
            throw new ResponseException(RespCodeEnum.NOT_FIND_BAR_CODE_RULE);
        }
        // 获取打印模板, 替换打印模板里${ }的数据
        PrintDTO printDTO = JSON.parseObject(labelEntity.getContent(), PrintDTO.class);
        List<PrintDTO.ParamDto.Content> printElements = printDTO.getPanels().get(0).getPrintElements();
        CodeInfoSelectDTO build = CodeInfoSelectDTO.builder()
                .ruleType(labelEntity.getCodeType())
                .materialCode(workOrderByNumber.getMaterialCode())
                .materialName(selectDTO.getProductName())
                .customerCode(workOrderByNumber.getCustomerCode())
                .customerMaterialCode(workOrderByNumber.getCustomerMaterialCode())
                .number(selectDTO.getNumber())
                .weight(selectDTO.getWeight())
                .productBatch(selectDTO.getProductBatchCode())
                .productTime(Objects.isNull(byWorkOrder) ? null : byWorkOrder.getReportTime())
                .operatorName(userByUsername)
                .checkName(selectDTO.getCheckName())
                .build();
        for (PrintDTO.ParamDto.Content content : printElements) {
            String data = content.getOptions().getTitle();
            if (StringUtils.isBlank(data)) {
                continue;
            }
            // 替换占位符, 替换值为查询对象的某个属性值
            Matcher matcher = PATTERN.matcher(data);
            while (matcher.find()) {
                String placeholder = matcher.group();
                build.setPlaceholder(placeholder);
                build.setSliceDigits(content.getOptions().getSliceDigits());
                // 如果公式为空，则直接进行真实值替换占位符的逻辑
                String placeholderValue = replaceData(placeholder, content.getOptions().getSliceDigits(), build);
                data = data.replace(placeholder, placeholderValue);
            }
            content.getOptions().setTitle(data);
            // 字体大小如果为空,则设置默认值
            if (content.getOptions().getFontSize() == null) {
                content.getOptions().setFontSize(9.0);
            }
        }
        PrintDTO build1 = PrintDTO.builder().ruleType(labelEntity.getCodeType()).ruleCode(labelEntity.getRuleCode()).panels(printDTO.getPanels()).build();
        return labelService.getPrintDtoByOpenApi(build1);
    }

    @Override
    public PrintDTO printBox(PackageBoxPrintBagDTO packageBoxPrintBagDTO, String username) {
        //获取打印数据
        List<PackageBoxMacSnRelationEntity> list = packageBoxMacSnRelationService.lambdaQuery()
                .eq(PackageBoxMacSnRelationEntity::getBoxNumber, packageBoxPrintBagDTO.getBoxNumber())
                .orderByAsc(PackageBoxMacSnRelationEntity::getCreateTime)
                .list();
        WorkOrderEntity workOrderByNumber = workOrderService.lambdaQuery()
                .eq(WorkOrderEntity::getWorkOrderNumber, packageBoxPrintBagDTO.getWorkOrderNumber())
                .one();
        if (CollectionUtils.isEmpty(list)) {
            throw new ResponseException("无关联数据，不可打印");
        }
        List<String> collect = list.stream().map(PackageBoxMacSnRelationEntity::getNameplateSn).collect(Collectors.toList());
        //获取操作人，批次对应的生产订单下推的首工序对应的工单的最后一个报工记录报工人
        if (StringUtils.isBlank(workOrderByNumber.getProductOrderNumber()) || Objects.isNull(workOrderByNumber.getCraftId())) {
            throw new ResponseException("工单没有对应的生产订单或工艺信息");
        }
        List<CraftProcedureEntity> craftProcedureEntities = craftProcedureService.lambdaQuery().eq(CraftProcedureEntity::getCraftId, workOrderByNumber.getCraftId())
                .and(o->o.eq(CraftProcedureEntity::getSupProcedureId, Constant.EMPTY).or().isNull(CraftProcedureEntity::getSupProcedureId))
                .list();
        if (CollectionUtils.isEmpty(craftProcedureEntities)) {
            throw new ResponseException("没有找到批次对应的工单的首工序信息");
        }
        List<Integer> workOrderIds = workOrderService.lambdaQuery()
                .eq(WorkOrderEntity::getProductOrderNumber, workOrderByNumber.getProductOrderNumber())
                .list()
                .stream().map(WorkOrderEntity::getWorkOrderId)
                .collect(Collectors.toList());
        List<WorkOrderProcedureRelationEntity> relationEntities = workOrderProcedureRelationService.lambdaQuery().in(WorkOrderProcedureRelationEntity::getWorkOrderId, workOrderIds)
                .eq(WorkOrderProcedureRelationEntity::getProcedureId, craftProcedureEntities.get(0).getProcedureId()).list();
        if (CollectionUtils.isEmpty(relationEntities)) {
            throw new ResponseException("没有找到批次对应的首工序工单");
        }
        //取操作员，第一个工序报工上报人
        Set<String> userByUsername = new HashSet<>();
        Set<String> checkUser = new HashSet<>();
        collect.forEach(s -> {
            //获取第一个工序上报人
            ReportLineEntity firstByWorkOrder = reportLineService.getFirstByWorkOrder(relationEntities.stream().map(WorkOrderProcedureRelationEntity::getWorkOrderNumber).collect(Collectors.toList()), s);
            if (Objects.nonNull(firstByWorkOrder) && StringUtils.isNotBlank(firstByWorkOrder.getOperatorName())) {
                List<SysUserEntity> listByNickNames = sysUserService.getListByNickNames(Arrays.asList(firstByWorkOrder.getOperatorName().split(Constants.SEP)));
                userByUsername.addAll(listByNickNames.stream().map(SysUserEntity::getJobNumber).filter(Objects::nonNull).collect(Collectors.toList()));
            }
            //获取末检上报人
            InspectionSheetEntity responseObject = JacksonUtil.getResponseObject(
                    inspectionSheetInterface.getInspectSheetByWorkOrder(packageBoxPrintBagDTO.getWorkOrderNumber(), InspectionsSchemeTypeEnum.LAST_PRODUCT_INSPECTION.getCode(), s), InspectionSheetEntity.class);
            if (Objects.nonNull(responseObject)) {
                SysUserEntity sysUserEntity = sysUserService.getUserByUsername(responseObject.getInspector());
                if (!Objects.isNull(sysUserEntity) && !StringUtils.isEmpty(sysUserEntity.getJobNumber())) {
                    checkUser.add(sysUserEntity.getJobNumber());
                }
            }
        });
        String nicknamesByUsernames = StringUtils.join(userByUsername, Constants.SEP);
        String checkUserNames = StringUtils.join(checkUser, Constants.SEP);
        //获取数量
        List<PackageRecordEntity> list1 = packageRecordService.lambdaQuery()
                .eq(PackageRecordEntity::getWorkOrderNumber, packageBoxPrintBagDTO.getWorkOrderNumber())
                .eq(PackageRecordEntity::getContainerSn, packageBoxPrintBagDTO.getBoxNumber())
                .eq(PackageRecordEntity::getIsDelete, Constant.ZERO)
                .list();
        int sum = list1.stream().mapToInt(PackageRecordEntity::getNumber).sum();
        //获取二维码
        StringBuilder baggingBatch = new StringBuilder();
        for (PackageRecordEntity packageRecordEntity : list1) {
            baggingBatch.append(packageRecordEntity.getProductSn()).append(Constants.DIAGONAL_LINE)
                    .append(packageRecordEntity.getNumber()).append(Constants.DIAGONAL_LINE).append(packageRecordEntity.getWeight().intValue())
                    .append(System.lineSeparator());
        }

        // 新版本通过前端选择的标签规则进行打印
        LabelEntity labelEntity = labelService.lambdaQuery().eq(LabelEntity::getRuleId, packageBoxPrintBagDTO.getRuleId()).one();
        if (labelEntity == null) {
            throw new ResponseException(RespCodeEnum.NOT_FIND_BAR_CODE_RULE);
        }
        // 获取打印模板, 替换打印模板里${ }的数据
        PrintDTO printDTO = JSON.parseObject(labelEntity.getContent(), PrintDTO.class);
        List<PrintDTO.ParamDto.Content> printElements = printDTO.getPanels().get(0).getPrintElements();
        CodeInfoSelectDTO build = CodeInfoSelectDTO.builder()
                .ruleType(labelEntity.getCodeType())
                .materialCode(workOrderByNumber.getMaterialCode())
                .materialName(packageBoxPrintBagDTO.getProductName())
                .customerCode(workOrderByNumber.getCustomerCode())
                .customerMaterialCode(workOrderByNumber.getCustomerMaterialCode())
                .operatorName(nicknamesByUsernames)
                .checkName(checkUserNames)
                .number(String.valueOf(sum))
                .baggingBatch(baggingBatch.toString())
                .boxNumber(packageBoxPrintBagDTO.getBoxNumber())
                .build();
        int flag = 0;
        for (PrintDTO.ParamDto.Content content : printElements) {
            String data = content.getOptions().getTitle();
            String title = content.getOptions().getTextType();
            if (StringUtils.isBlank(data)) {
                continue;
            }
            // 替换占位符, 替换值为查询对象的某个属性值
            Matcher matcher = PATTERN.matcher(data);
            while (matcher.find()) {
                String placeholder = matcher.group();
                build.setPlaceholder(placeholder);
                build.setSliceDigits(content.getOptions().getSliceDigits());
                if (placeholder.contains(LabelInfoEnum.PRODUCT_BATCH_A.getCode()) && !Constants.QRCODE.equals(title)) {
                    if (collect.size() - 1 >= flag) {
                        build.setProductBatch(collect.get(flag));
                    }else {
                        build.setProductBatch(null);
                    }
                    flag++;
                }
                if (placeholder.contains(LabelInfoEnum.PRODUCT_BATCH_A.getCode()) && Constants.QRCODE.equals(title)) {
                    build.setProductBatch(StringUtils.join(collect, Constants.DIAGONAL_LINE));
                }
                // 如果公式为空，则直接进行真实值替换占位符的逻辑
                String placeholderValue = replaceData(placeholder, content.getOptions().getSliceDigits(), build);
                data = data.replace(placeholder, placeholderValue);
            }
            content.getOptions().setTitle(data);
            // 字体大小如果为空,则设置默认值
            if (content.getOptions().getFontSize() == null) {
                content.getOptions().setFontSize(9.0);
            }

        }
        PrintDTO build1 = PrintDTO.builder().ruleType(labelEntity.getCodeType()).ruleCode(labelEntity.getRuleCode()).panels(printDTO.getPanels()).build();
        return labelService.getPrintDtoByOpenApi(build1);
    }

    private String replaceData(String placeholder, String sliceDigits, CodeInfoSelectDTO build) {
        String placeholderValue = "";
        if (placeholder.contains(LabelInfoEnum.PRODUCT_NAME.getCode())) {
            placeholderValue = PrintDataUtils.sliceDigits(build.getMaterialName(), sliceDigits);
        } else if (placeholder.contains(LabelInfoEnum.PRODUCT_CODE.getCode())) {
            placeholderValue = PrintDataUtils.sliceDigits(build.getMaterialCode(), sliceDigits);
        }else if (placeholder.contains(LabelInfoEnum.CUSTOMER_MATERIAL_CODE.getCode())) {
            placeholderValue = PrintDataUtils.sliceDigits(build.getCustomerMaterialCode(), sliceDigits);
        }else if (placeholder.contains(LabelInfoEnum.NUMBER.getCode())) {
            placeholderValue = PrintDataUtils.sliceDigits(build.getNumber(), sliceDigits);
        }else if (placeholder.contains(LabelInfoEnum.WEIGHT.getCode())) {
            placeholderValue = PrintDataUtils.sliceDigits(build.getWeight(), sliceDigits);
        }else if (placeholder.contains(LabelInfoEnum.PRODUCT_BATCH_A.getCode())) {
            placeholderValue = PrintDataUtils.sliceDigits(build.getProductBatch(), sliceDigits);
        }else if (placeholder.contains(LabelInfoEnum.PRODUCT_TIME.getCode())) {
            placeholderValue = PrintDataUtils.sliceDigits(DateUtil.format(build.getProductTime(), DateUtil.DATE_FORMAT), sliceDigits);
        }else if (placeholder.contains(LabelInfoEnum.OPERATOR_NAME.getCode())) {
            placeholderValue = PrintDataUtils.sliceDigits(build.getOperatorName(), sliceDigits);
        }else if (placeholder.contains(LabelInfoEnum.CHECK_NAME.getCode())) {
            placeholderValue = PrintDataUtils.sliceDigits(build.getCheckName(), sliceDigits);
        }else if (placeholder.contains(LabelInfoEnum.THIS_LEVEL_OF_PACKAGE_PRODUCT_CODE.getCode())) {
            placeholderValue = PrintDataUtils.sliceDigits(build.getBoxNumber(), sliceDigits);
        } else if (placeholder.contains(LabelInfoEnum.BAGGING_BATCH.getCode())) {
            placeholderValue = PrintDataUtils.sliceDigits(build.getBaggingBatch(), sliceDigits);
        } else if (placeholder.contains(LabelInfoEnum.WORK_ORDER_CUSTOMER_CODE.getCode())) {
            // 工单客户编码
            placeholderValue = PrintDataUtils.sliceDigits(build.getCustomerCode(), sliceDigits);
        }
        return placeholderValue;
    }

    @Override
    public InspectionPackageCountVO inspectionPackageSelectCount(WorkOrderInspectionPackageSelectDTO selectDTO) {
        String workOrderNumber = selectDTO.getWorkOrderNumber();
        WorkOrderEntity workOrder = workOrderService.getSimpleWorkOrderByNumber(workOrderNumber);
        //本工序过站数
        Integer passCount = workOrderService.getPassCount(workOrderNumber, selectDTO.getCraftProcedureId());

        List<PackageBoxEntity> packageBoxes = this.lambdaQuery()
                .eq(PackageBoxEntity::getWorkOrderNumber, workOrderNumber)
                //.eq(PackageBoxEntity::getState, PackageBoxStateEnum.PACKAGE_COMPLETED.getCode())
                .list();
        int packageCount = 0;
        if (!CollectionUtils.isEmpty(packageBoxes)) {
            packageCount = packageBoxMacSnRelationService.lambdaQuery()
                    .eq(PackageBoxMacSnRelationEntity::getBoxId, packageBoxes.stream().map(PackageBoxEntity::getId).collect(Collectors.toList()))
                    .count().intValue();
        }
        InspectionPackageCountVO build = InspectionPackageCountVO.builder()
                .planQuantity(workOrder.getPlanQuantity())
                .finishCount(workOrder.getFinishCount())
                .unqualified(workOrder.getUnqualified())
                .passCount(passCount)
                .packageBoxCount(packageBoxes.size())
                .packageCount(packageCount)
                .build();
        return build;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void inspectionPackageBox(PackageBoxAddDTO packageBox) {
        PackageBoxEntity box = this.getPackageBoxByNumber(packageBox.getBoxNumber(), packageBox.getWorkOrderNumber());
        if (Objects.isNull(box)) {
            log.error("箱标不存在,{}, {}", packageBox.getWorkOrderNumber(), packageBox.getBoxNumber());
            throw new ResponseException("箱标不存在");
        }
        List<String> relateSns = packageBox.getRelateCodes();
        if (!CollectionUtils.isEmpty(relateSns)) {
            // 已包装，请勿重复包装
            List<PackageBoxMacSnRelationEntity> existRelates = packageBoxMacSnRelationService.lambdaQuery()
                    .eq(PackageBoxMacSnRelationEntity::getBoxId, box.getId())
                    .in(PackageBoxMacSnRelationEntity::getNameplateSn, relateSns)
                    .list();
            if (!CollectionUtils.isEmpty(existRelates)) {
                throw new ResponseException("已包装,请勿重复包装");
            }
        }
        String username = userAuthenService.getUsername();
        Date nowDate = new Date();
        List<PackageBoxMacSnRelationEntity> saves = new ArrayList<>();
        for (String relateCode : relateSns) {
            saves.add(PackageBoxMacSnRelationEntity.builder()
                    .boxId(box.getId())
                    .boxNumber(box.getBoxNumber())
                    .nameplateSn(relateCode)
                    .createTime(nowDate)
                    .build());
        }
        packageBoxMacSnRelationService.saveBatch(saves);
        // 生产包装记录
        List<PackageRecordAddDTO> list = new ArrayList<>();
        if (!CollectionUtils.isEmpty(saves)) {
            for (PackageBoxMacSnRelationEntity relation : saves) {
                list.add(PackageRecordAddDTO.builder()
                        .workOrderNumber(box.getWorkOrderNumber())
                        .boxNumber(box.getBoxNumber())
                        .packageLevelId(box.getPackageContainerId())
                        .sn(relation.getNameplateSn())
                        .packType(PackageRecordTypeEnum.PACKAGING.getCode())
                        .username(username)
                        .build());
            }
        } else {
            // 添加一条空的包装记录
            list.add(PackageRecordAddDTO.builder()
                    .workOrderNumber(box.getWorkOrderNumber())
                    .boxNumber(box.getBoxNumber())
                    .packageLevelId(box.getPackageContainerId())
                    .packType(PackageRecordTypeEnum.PACKAGING.getCode())
                    .username(username)
                    .build());
        }
        packageRecordService.batchAdd(list);

        // 包装完成
        this.updateBoxState(box.getId(), PackageBoxStateEnum.PACKAGE_COMPLETED.getCode());
        // 移除 可以被包装的流水码
        if (!CollectionUtils.isEmpty(packageBox.getRelateCodes())) {
            packageTempRelateCodeService.lambdaUpdate()
                    .eq(PackageTempRelateCodeEntity::getWorkOrderNumber, box.getWorkOrderNumber())
                    .eq(Objects.nonNull(packageBox.getCraftProcedureId()), PackageTempRelateCodeEntity::getCraftProcedureId, packageBox.getCraftProcedureId())
                    .in(PackageTempRelateCodeEntity::getFlowCode, packageBox.getRelateCodes())
                    .remove();
        }
        // 包装码支持WMS扫码和查询其中包装的产品数量进行入库
        WorkOrderEntity workOrder = workOrderService.getSimpleWorkOrderByNumber(box.getWorkOrderNumber());
        List<InsertBarCodeDTO> barCodeInserts = new ArrayList<>();
        barCodeInserts.add(InsertBarCodeDTO.builder()
                .barCode(box.getBoxNumber())
                .relateNumber(box.getWorkOrderNumber())
                .materialCode(workOrder.getMaterialCode())
                .skuId(workOrder.getSkuId())
                .count((double) saves.size())
                .finishCount((double) saves.size())
                .moduleType(BarCodeTypeEnum.FINISHED.getCode())
                .build());
        barCodeService.batchAddBarCode(barCodeInserts, username);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addPackageBox(PackageBoxAddDTO packageBox) {
        PackageBoxEntity box = this.getPackageBoxByNumber(packageBox.getBoxNumber(), packageBox.getWorkOrderNumber());
        if (Objects.isNull(box)) {
            log.error("箱标不存在,{}, {}", packageBox.getWorkOrderNumber(), packageBox.getBoxNumber());
            throw new ResponseException("箱标不存在");
        }
        if (StringUtils.isBlank(packageBox.getRelateCode())) {
            throw new ResponseException("扫码的批次不存在");
        }
        // 已包装，请勿重复包装
        List<PackageBoxMacSnRelationEntity> existRelates = packageBoxMacSnRelationService.lambdaQuery()
                .eq(PackageBoxMacSnRelationEntity::getBoxId, box.getId())
                .eq(PackageBoxMacSnRelationEntity::getNameplateSn, packageBox.getRelateCode())
                .list();
        String username = userAuthenService.getUsername();
        Date nowDate = new Date();
        List<PackageBoxMacSnRelationEntity> saves = new ArrayList<>();
        saves.add(PackageBoxMacSnRelationEntity.builder()
                .boxId(box.getId())
                .boxNumber(box.getBoxNumber())
                .nameplateSn(packageBox.getRelateCode())
                .createTime(nowDate)
                .build());
        if (CollectionUtils.isEmpty(existRelates)) {
            packageBoxMacSnRelationService.saveBatch(saves);
        }
        // 生产包装记录
        List<PackageRecordAddDTO> list = new ArrayList<>();
        for (PackageBoxMacSnRelationEntity relation : saves) {
            list.add(PackageRecordAddDTO.builder()
                    .workOrderNumber(box.getWorkOrderNumber())
                    .boxNumber(box.getBoxNumber())
                    .packageLevelId(box.getPackageContainerId())
                    .sn(relation.getNameplateSn())
                    .packType(PackageRecordTypeEnum.PACKAGING.getCode())
                    .username(username)
                    .number(packageBox.getNumber())
                    .weight(packageBox.getWeight())
                    .build());
        }
        packageRecordService.batchAdd(list);
    }

    @Override
    public void generalMachePackageComplete(String boxNumber, String workOrderNumber, String username) {
        PackageBoxEntity box = this.getPackageBoxByNumber(boxNumber, workOrderNumber);
        //查询外箱关联的彩盒信息列表
        List<PackageBoxMacSnRelationEntity> relations = packageBoxMacSnRelationService.lambdaQuery()
                .eq(PackageBoxMacSnRelationEntity::getBoxId, box.getId())
                .list();
        if (relations.isEmpty()) {
            log.error("该外箱没有绑定SN,无法完成包装,{}", boxNumber);
            throw new ResponseException("该外箱没有绑定SN,无法完成包装");
        }
        if (PackageBoxStateEnum.PACKAGE_COMPLETED.getCode().equals(box.getState())) {
            log.error("该外箱已完成包装,请勿重复提交,{}", boxNumber);
            throw new ResponseException("该外箱已完成包装,请勿重复提交");
        }
        this.updateBoxState(box.getId(), PackageBoxStateEnum.PACKAGE_COMPLETED.getCode());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void generalMachePackageRemoveRelation(Integer id) {
        PackageRecordEntity packageRecordEntity = packageRecordService.getById(id);
        packageRecordEntity.setIsDelete(Constants.ONE);
        packageRecordService.updateById(packageRecordEntity);
        List<PackageRecordEntity> list = packageRecordService.lambdaQuery()
                .eq(PackageRecordEntity::getWorkOrderNumber, packageRecordEntity.getWorkOrderNumber())
                .eq(PackageRecordEntity::getContainerSn, packageRecordEntity.getContainerSn())
                .eq(PackageRecordEntity::getProductSn, packageRecordEntity.getProductSn())
                .eq(PackageRecordEntity::getIsDelete, Constants.SKU_ID_DEFAULT_VAL)
                .list();
        if (CollectionUtils.isEmpty(list)) {
            PackageBoxEntity packageBox = this.getPackageBoxByNumber(packageRecordEntity.getContainerSn(), packageRecordEntity.getWorkOrderNumber());
            packageBoxMacSnRelationService.lambdaUpdate()
                    .eq(PackageBoxMacSnRelationEntity::getBoxId, packageBox.getId())
                    .eq(PackageBoxMacSnRelationEntity::getNameplateSn, packageRecordEntity.getProductSn())
                    .remove();
        }
    }

    private String replacePrintData(String placeholder, String sliceDigits, WorkOrderEntity workOrder, PackageBoxEntity packageBox, String boxNameplateSn, int packageNum) {
        MaterialEntity materialEntity = workOrder.getMaterialFields() != null ? workOrder.getMaterialFields() : new MaterialEntity();
        String placeholderValue = "";
        if (placeholder.contains(LabelInfoEnum.THIS_LEVEL_OF_PACKAGE_PRODUCT_CODE.getCode())) {
            placeholderValue = PrintDataUtils.sliceDigits(packageBox.getBoxNumber(), sliceDigits);
        } else if (placeholder.contains(LabelInfoEnum.THIS_LEVEL_OF_PACKAGE_PRODUCT_SN.getCode())) {
            placeholderValue = PrintDataUtils.sliceDigits(boxNameplateSn, sliceDigits);
        } else if (placeholder.contains(LabelInfoEnum.MATERIAL_CODE.getCode())) {
            //物料编码
            placeholderValue = PrintDataUtils.sliceDigits(workOrder.getMaterialCode(), sliceDigits);
        } else if (placeholder.contains(LabelInfoEnum.PRODUCT_CODE.getCode())) {
            //产品编号
            placeholderValue = PrintDataUtils.sliceDigits(workOrder.getMaterialCode(), sliceDigits);
        } else if (placeholder.contains(LabelInfoEnum.MATERIAL_NAME.getCode())) {
            //物料名称
            placeholderValue = PrintDataUtils.sliceDigits(materialEntity.getName(), sliceDigits);
        } else if (placeholder.contains(LabelInfoEnum.PRODUCT_NAME.getCode())) {
            //产品名称
            placeholderValue = PrintDataUtils.sliceDigits(materialEntity.getName(), sliceDigits);
        } else if (placeholder.contains(LabelInfoEnum.THIS_LEVEL_PACKAGE_NUM.getCode())) {
            //本层级实际包装产品数量
            placeholderValue = PrintDataUtils.sliceDigits(String.valueOf(packageNum), sliceDigits);
        } else if (placeholder.contains(LabelInfoEnum.PACKING_DATE.getCode())) {
            //包装日期
            if (PackageBoxStateEnum.PACKAGE_COMPLETED.getCode().equals(packageBox.getState())
                    || PackageBoxStateEnum.CLOSING_COMPLETED.getCode().equals(packageBox.getState())) {
                placeholderValue = PrintDataUtils.sliceDigits(DateUtil.format(packageBox.getOperateTime(), DateUtil.DATE_FORMAT), sliceDigits);
            }
        } else if (placeholder.contains(LabelInfoEnum.WORK_ORDER_CUSTOMER_CODE.getCode())) {
            // 工单客户编码
            placeholderValue = PrintDataUtils.sliceDigits(workOrder.getCustomerCode(), sliceDigits);
        } else if (placeholder.contains(LabelInfoEnum.WORK_ORDER_CUSTOMER_MATERIAL_CODE.getCode())) {
            placeholderValue = PrintDataUtils.sliceDigits(workOrder.getCustomerMaterialCode(), sliceDigits);
        } else if (placeholder.contains(LabelInfoEnum.WORK_ORDER_CUSTOMER_MATERIAL_NAME.getCode())) {
            placeholderValue = PrintDataUtils.sliceDigits(workOrder.getCustomerMaterialName(), sliceDigits);
        } else if (placeholder.contains(LabelInfoEnum.WORK_ORDER_CUSTOMER_SPECIFICATION.getCode())) {
            placeholderValue = PrintDataUtils.sliceDigits(workOrder.getCustomerSpecification(), sliceDigits);
        }
        return placeholderValue;
    }

    @Override
    public void packageScanCode(PackageScanDTO dto) {
        String relateNumber = dto.getRelateNumber();
        String scanCode = dto.getCode();
        PackageLevelEntity levelEntity = packageLevelService.getById(dto.getPackageLevelId());
        if (levelEntity == null) {
            throw new ResponseException("未找到包装层级信息");
        }
        // 包装物料码判断
        PackageBoxEntity boxEntity = this.getPackageBoxByNumber(scanCode, relateNumber, PackageBoxRelateTypeEnum.PACKAGE_ORDER.getCode());
        if (boxEntity != null) {
            if (!boxEntity.getWorkOrderNumber().equals(relateNumber)) {
                throw new ResponseException("该条码所属工单和选择的工单不一致");
            }
            // 是本层包装码直接返回
            if (levelEntity.getPackageMaterialCode().equals(boxEntity.getPackageMaterialCode())
                    && boxEntity.getLevel().equals(levelEntity.getLevel())) {
                return;
            }
            throw new ResponseException("该条码不是本层包装码");
        } else {
            throw new ResponseException("条码" + scanCode + "无法解析");
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void packageScanRelevanceCode(PackageScanDTO dto) {
        String relateNumber = dto.getRelateNumber();
        String scanCode = dto.getCode();
        String boxNumber = dto.getBoxNumber();
        List<PackageLevelEntity> levelEntities = packageLevelService.getSameLevelMaterials(dto.getPackageLevelId());
        if (CollectionUtils.isEmpty(levelEntities)) {
            throw new ResponseException("未找到包装层级信息");
        }
        PackageLevelEntity levelEntity = levelEntities.get(0);
        PackageBoxEntity boxEntity = this.getPackageBoxByNumber(boxNumber, relateNumber, PackageBoxRelateTypeEnum.PACKAGE_ORDER.getCode());
        // 1、上一层包装码判断
        PackageBoxEntity prefixBoxEntity = this.getPackageBoxByNumber(scanCode, relateNumber, PackageBoxRelateTypeEnum.PACKAGE_ORDER.getCode());
        if (prefixBoxEntity != null) {
            if (!prefixBoxEntity.getWorkOrderNumber().equals(relateNumber)) {
                throw new ResponseException("该条码所属工单和选择的工单不一致");
            }
            // 是上一层包装码
            if (prefixBoxEntity.getPackageMaterialCode().equals(levelEntity.getMaterialCode())
                    && prefixBoxEntity.getLevel().equals(levelEntity.getLevel() - 1)) {
                // 保存关联关系
                packageBoxMacSnRelationService.savePackageOrderRelation(boxEntity.getId(), boxNumber, scanCode);
                return;
            }
            throw new ResponseException("该条码不是上一层包装码");
        }
        // 2、流水码和批次判断
        ProductFlowCodeEntity codeEntity = productFlowCodeService.getByProductFlowCode(scanCode);
        if (codeEntity == null) {
            throw new ResponseException("条码" + scanCode + "无法解析");
        }
        // 2.1、物料校验
        List<String> materialCodes = levelEntities.stream().map(PackageLevelEntity::getMaterialCode).collect(Collectors.toList());
        if (!materialCodes.contains(codeEntity.getMaterialCode())) {
            throw new ResponseException("该条码所属物料不是本层的被包装物料");
        }
        // 包装工单
        PackageOrderEntity packageOrderEntity = packageOrderService.getSimpleEntity(relateNumber);
        if (packageOrderEntity == null || !PackageOrderRelateTypeEnum.PRODUCT_ORDER.getCode().equals(packageOrderEntity.getRelateType())) {
            throw new ResponseException("未找到包装工单信息");
        }
        // 2.2、单据检验
        if (CodeRelationTypeEnum.PRODUCT_ORDER.getCode().equals(codeEntity.getRelationType())) {
            if (!packageOrderEntity.getRelateNumber().equals(codeEntity.getRelationNumber())) {
                throw new ResponseException("该条码所属订单和包装工单的关联订单不一致");
            }
        } else {
            WorkOrderEntity workOrderEntity = workOrderService.getSimpleWorkOrderByNumber(codeEntity.getRelationNumber());
            if (workOrderEntity == null) {
                throw new ResponseException("条码对应生产工单不存在");
            }
            if (!packageOrderEntity.getRelateNumber().equals(workOrderEntity.getProductOrderNumber())) {
                throw new ResponseException("该条码所属订单和包装工单的关联订单不一致");
            }
        }
        // 保存关联关系
        packageBoxMacSnRelationService.savePackageOrderRelation(boxEntity.getId(), boxNumber, scanCode);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void packageRemoveAllRelation(PackageCompleteDTO dto) {
        String boxNumber = dto.getBoxNumber();
        if (StringUtils.isBlank(boxNumber)) {
            throw new ResponseException("包装码不能为空");
        }
        String relateNumber = dto.getRelateNumber();
        if (StringUtils.isBlank(relateNumber)) {
            throw new ResponseException("关联单据不能为空");
        }
        PackageBoxEntity packageBox = this.getPackageBoxByNumber(boxNumber, dto.getRelateNumber(), PackageBoxRelateTypeEnum.PACKAGE_ORDER.getCode());
        packageBoxMacSnRelationService.lambdaUpdate().eq(PackageBoxMacSnRelationEntity::getBoxId, packageBox.getId()).remove();
        productFlowCodeService.removeAllRelevance(CodeRelevanceEntity.builder().code(boxNumber).build());
    }

    @Override
    public List<PackageBoxEntity> listPackageBox(PackageBoxOpenSelectDTO dto) {
        List<Integer> stateList = null;
        if (StringUtils.isNotBlank(dto.getStates())) {
            stateList = Arrays.stream(dto.getStates().split(Constants.SEP)).map(Integer::valueOf).collect(Collectors.toList());
        }
        List<PackageBoxEntity> list = this.lambdaQuery()
                .eq(Objects.nonNull(dto.getPackageLevelId()), PackageBoxEntity::getPackageContainerId, dto.getPackageLevelId())
                .in(CollectionUtils.isNotEmpty(stateList), PackageBoxEntity::getState, stateList)
                .eq(PackageBoxEntity::getWorkOrderNumber, dto.getRelateNumber())
                .eq(PackageBoxEntity::getRelateType, dto.getRelateType())
                .like(StringUtils.isNotBlank(dto.getBoxNumber()), PackageBoxEntity::getBoxNumber, dto.getBoxNumber())
                .orderByDesc(PackageBoxEntity::getCreateTime)
                .list();
        for (PackageBoxEntity packageBox : list) {
            Long count = packageBoxMacSnRelationService.lambdaQuery()
                    .eq(PackageBoxMacSnRelationEntity::getBoxId, packageBox.getId())
                    .count();
            packageBox.setPackageCount(count.intValue());
        }
        return list;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean packageUnboxing(PackageCompleteDTO dto) {
        Long count = packageBoxMacSnRelationService.lambdaQuery()
                .eq(PackageBoxMacSnRelationEntity::getNameplateSn, dto.getBoxNumber())
                .count();
        if (count > 0) {
            throw new ResponseException("该包装码已被下一层绑定，无法拆箱");
        }
        PackageBoxEntity box = this.getPackageBoxByNumber(dto.getBoxNumber(), dto.getRelateNumber(), dto.getRelateType());
        PackageRecordAddDTO build = PackageRecordAddDTO.builder()
                .workOrderNumber(box.getWorkOrderNumber())
                .boxNumber(box.getBoxNumber())
                .packageLevelId(box.getPackageContainerId())
                .sn(null)
                .materialCode(box.getPackageMaterialCode())
                .packType(PackageRecordTypeEnum.DEVANNING.getCode())
                .username(dto.getUsername())
                .build();
        // 更新状态
        this.updateBoxState(box.getId(), PackageBoxStateEnum.UNPACKING.getCode());
        // 生产拆箱记录
        packageRecordService.batchAdd(Stream.of(build).collect(Collectors.toList()));
        // 更新已包装数量
        return updatePackageCount(box);
    }

    /**
     * 更新包装工单已包装数量
     * @param box
     */
    private Boolean updatePackageCount(PackageBoxEntity box) {
        if (!PackageBoxRelateTypeEnum.PACKAGE_ORDER.getCode().equals(box.getRelateType())) {
            return false;
        }
        int maxLevel = packageLevelService.getMaxLevel(box.getSchemeCode());
        if (maxLevel != box.getLevel()) {
            return false;
        }
        // 查询最后一层且是完成态的包装码
        List<PackageBoxEntity> list = this.lambdaQuery().select(PackageBoxEntity::getId)
                .eq(PackageBoxEntity::getWorkOrderNumber, box.getWorkOrderNumber())
                .eq(PackageBoxEntity::getRelateType, box.getRelateType())
                .eq(PackageBoxEntity::getState, PackageBoxStateEnum.PACKAGE_COMPLETED.getCode())
                .eq(PackageBoxEntity::getLevel, maxLevel)
                .list();
        if (CollectionUtils.isEmpty(list)) {
            // 更新包装工单完成数量
            packageOrderService.updatePackageCount(box.getWorkOrderNumber(), 0.0);
            return true;
        }
        List<Integer> ids = list.stream().map(PackageBoxEntity::getId).collect(Collectors.toList());
        List<Integer> firstLevelBoxIds = getFirstLevelBoxIds(ids, maxLevel, box.getWorkOrderNumber());
        // 查询包装数量
        Long count = packageBoxMacSnRelationService.lambdaQuery().in(PackageBoxMacSnRelationEntity::getBoxId, firstLevelBoxIds).count();
        // 更新包装工单完成数量
        packageOrderService.updatePackageCount(box.getWorkOrderNumber(), count.doubleValue());
        return true;
    }

    /**
     * 获取第一层包装码id
     * @param ids
     * @param maxLevel
     * @param workOrderNumber
     */
    private List<Integer> getFirstLevelBoxIds(List<Integer> ids, int maxLevel, String workOrderNumber) {
        if (maxLevel == Constants.ONE) {
            return ids;
        }
        List<String> nameplateSns = packageBoxMacSnRelationService.lambdaQuery()
                .in(PackageBoxMacSnRelationEntity::getBoxId, ids).list()
                .stream().map(PackageBoxMacSnRelationEntity::getNameplateSn).collect(Collectors.toList());
        List<Integer> supIds = this.lambdaQuery().select(PackageBoxEntity::getId)
                .in(PackageBoxEntity::getBoxNumber, nameplateSns)
                .eq(PackageBoxEntity::getWorkOrderNumber, workOrderNumber)
                .eq(PackageBoxEntity::getRelateType, PackageBoxRelateTypeEnum.PACKAGE_ORDER.getCode()).list()
                .stream().map(PackageBoxEntity::getId).collect(Collectors.toList());
        return getFirstLevelBoxIds(supIds, maxLevel - 1, workOrderNumber);
    }

    @Override
    public void updateBatchNum(PackageNumUpdateDTO dto) {
        PackageBoxEntity box = this.getPackageBoxByNumber(dto.getBoxNumber(), dto.getRelateNumber(), dto.getRelateType());
        packageBoxMacSnRelationService.lambdaUpdate().eq(PackageBoxMacSnRelationEntity::getBoxId, box.getId())
                .eq(PackageBoxMacSnRelationEntity::getNameplateSn, dto.getCode())
                .set(PackageBoxMacSnRelationEntity::getNum, dto.getNum())
                .update();
    }

    @Override
    public PackageBoxEntity deleteById(Integer id) {
        PackageBoxEntity entity = this.getById(id);
        if (Objects.isNull(entity)) {
            return null;
        }
        Long count = packageBoxMacSnRelationService.lambdaQuery().eq(PackageBoxMacSnRelationEntity::getBoxId, entity.getId()).count();
        if (count > 0) {
            throw new ResponseException("该条码已有被包装物料码关联信息，无法删除");
        }
        this.removeById(id);
        return entity;
    }


    /**
     * 处理Redis操作
     *
     * @param redisKey
     * @param redisLock
     * @param build
     */
    private void processRedis(String redisKey, String redisLock, ImportProgressDTO build) {
        // 重置进度
        redisTemplate.opsForValue().set(redisKey, JSONObject.toJSONString(build), 1, TimeUnit.HOURS);
        // 加锁
        Boolean lockStatus = redisTemplate.opsForValue().setIfAbsent(redisLock, new Date(), 1, TimeUnit.HOURS);
        if (lockStatus == null || !lockStatus) {
            build = ImportProgressDTO.builder().executionDescription("当前方法已被占用，稍后再试").executionStatus(false).progress(1.0).code(Result.FAIL_CODE).build();
            redisTemplate.opsForValue().set(redisKey, JSONObject.toJSONString(build), 1, TimeUnit.HOURS);
            throw new ResponseException("当前方法已被占用，稍后再试");
        }
    }
    /**
     * Redis设置值
     *
     * @param redisKey
     * @param batchRedisKey
     * @param build
     */
    private void setRedis(String redisKey, String batchRedisKey, ImportProgressDTO build, boolean isBatch) {
        redisTemplate.opsForValue().set(redisKey, JSONObject.toJSONString(build), 1, TimeUnit.HOURS);
        if (isBatch) {
            redisTemplate.opsForValue().set(batchRedisKey, JSONObject.toJSONString(build), 1, TimeUnit.HOURS);
        }
    }
    /**
     * 在Async 方法上标注@Transactional会失效
     */
    @Override
    @Async
    @Transactional(rollbackFor = Exception.class)
    public void batchAddPackageCode(PackageBoxBatchAddDTO packageBoxBatchAddDTO, String userName) {
        //初始化进度
        ImportProgressDTO build = ImportProgressDTO.builder()
                .progress(Double.valueOf(com.yelink.dfscommon.constant.Constant.ZERO_VALUE))
                .executionDescription("正在处理中，请耐心等候...")
                .executionStatus(false)
                .code(Result.SUCCESS_CODE)
                .build();
        int rowTotal =packageBoxBatchAddDTO.getNum() ;
        int currentNum = 0;
        String batchRedisKey = RedisKeyPrefix.PACKAGE_CODE_PROGRESS+packageBoxBatchAddDTO.getPackageOrderNumber();
        String batchRedisLock = RedisKeyPrefix.PACKAGE_CODE_PROGRESS_LOCK+packageBoxBatchAddDTO.getPackageOrderNumber();
        // 处理Redis操作，重置进度和加锁
        this.processRedis(batchRedisKey, batchRedisLock, build);
        try {
            if (rowTotal == 0) {
                Double processPercent = MathUtil.divideDouble(1, 1, 2);
                build = ImportProgressDTO.builder().progress(processPercent).executionDescription(String.format("新增完成：成功%s条；失败%s条", 0, 0)).executionStatus(processPercent.compareTo(1.0) == 0).code(Result.SUCCESS_CODE).build();
                // 设置生成进度
                // 重置进度
                redisTemplate.opsForValue().set(batchRedisKey, JSONObject.toJSONString(build), 1, TimeUnit.HOURS);
                return;
            }
            try {
                String description;
                MaterialEntity materialEntity = materialService.getSimpleMaterialByCode(packageBoxBatchAddDTO.getPackageMaterialCode());
                if (materialEntity == null) {
                    description = "查询不到物料[" + packageBoxBatchAddDTO.getPackageMaterialCode() + "]的信息";
                    build.setExecutionDescription(description);
                    build.setExecutionStatus(false);
                    build.setProgress(1.0);
                    // 设置生成进度
                    redisTemplate.opsForValue().set(batchRedisKey, JSONObject.toJSONString(build), 1, TimeUnit.HOURS);
                    throw new ResponseException(description);
                }
                //
                PackageBoxEntity packageBoxEntity = PackageBoxEntity.builder()
                        .workOrderNumber(packageBoxBatchAddDTO.getPackageOrderNumber())
                        .createBy(userName)
                        .createTime(new Date())
                        .state(PackageBoxStateEnum.NOT_PACKAGE.getCode())
                        .relateType(PackageBoxRelateTypeEnum.PACKAGE_ORDER.getCode())
                        .build();

                NumberRulesConfigEntity numberRulesConfigEntity = numberRuleService.getById(packageBoxBatchAddDTO.getNumberRuleId());

                // 批量单据生成码或一个单据批量生成码
                Double processPercent;
                List<RuleDetailDTO> ruleDetailDTOs = JSONArray.parseArray(packageBoxBatchAddDTO.getRuleDetail(), RuleDetailDTO.class);
                for (int i = 1; i <= packageBoxBatchAddDTO.getNum(); i++) {
                    packageBoxBatchAddDTO.getRelatedMap().put(AutoIncrementConfigureTypeEnum.PACKAGE_ORDER_LEVEL.getCode(), packageBoxBatchAddDTO.getPackageOrderNumber() + "__" + packageBoxBatchAddDTO.getLevel());
                    NumberCodeDTO seqById = numberRuleService.generateRules(numberRulesConfigEntity.getId(), ruleDetailDTOs, packageBoxBatchAddDTO.getRelatedMap());
                    if (seqById.getSeq() == null) {
                        build = ImportProgressDTO.builder().executionDescription("批量新增时，编码规则需要存在自动生成序号").executionStatus(false).progress(1.0).code(Result.FAIL_CODE).build();
                        // 设置生成进度
                        redisTemplate.opsForValue().set(batchRedisKey, JSONObject.toJSONString(build), 1, TimeUnit.HOURS);
                        throw new ResponseException("批量新增时，编码规则需要存在自动生成序号");
                    }
                    packageBoxEntity.setId(null);
                    // 判断包装码是否存在
                  Long count = this.lambdaQuery().eq(PackageBoxEntity::getBoxNumber,seqById.getCode()).count();
                  if(count>0){
                      build = ImportProgressDTO.builder().executionDescription("包装码已存在").executionStatus(false).progress(1.0).code(Result.FAIL_CODE).build();
                      // 设置生成进度
                      redisTemplate.opsForValue().set(batchRedisKey, JSONObject.toJSONString(build), 1, TimeUnit.HOURS);
                      throw new ResponseException("包装码已存在");
                  }
                    packageBoxEntity.setBoxNumber(seqById.getCode());
                    this.save(packageBoxEntity);
                    currentNum++;
                    processPercent = MathUtil.divideDouble(currentNum, rowTotal, 2);
                    build = ImportProgressDTO.builder().progress(processPercent).executionDescription(processPercent.compareTo(1.0) == 0 ? String.format("新增完成：成功%s条；失败%s条", currentNum, rowTotal - currentNum) : String.format("正在处理中，已完成%s条；", currentNum))
                            .executionStatus(processPercent.compareTo(1.0) == 0).code(Result.SUCCESS_CODE).build();
                    // 设置生成进度
                    redisTemplate.opsForValue().set(batchRedisKey, JSONObject.toJSONString(build), 1, TimeUnit.HOURS);
                    // 编码规则有自动生成序号的，seq才加1
                    ruleSeqService.updateSeqEntity(packageBoxBatchAddDTO.getRelatedMap(), numberRulesConfigEntity.getId(), false);
                }
            } catch (Exception e) {
                log.error("包装码新增数据错误", e);
                CommonService commonService = SpringUtil.getBean(CommonService.class);
                build = commonService.getImportProgressDTO(batchRedisKey, e);
                // 设置生成进度
                redisTemplate.opsForValue().set(batchRedisKey, JSONObject.toJSONString(build), 1, TimeUnit.HOURS);
                // 手动触发事务回滚
                TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            } finally {
                // 释放锁
                redisTemplate.delete(batchRedisLock);
            }
        } finally {
            // 释放批量锁
            redisTemplate.delete(batchRedisLock);
        }
    }
}
