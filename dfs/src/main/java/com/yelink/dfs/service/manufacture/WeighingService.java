package com.yelink.dfs.service.manufacture;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yelink.dfs.entity.manufacture.WeighingEntity;


/**
 * Service
 *
 * <AUTHOR>
 * @Date 2020-12-22 16:01
 */
public interface WeighingService extends IService<WeighingEntity> {

    /**
     * @param batchNumber
     * @param carNum
     * @param driverName
     * @param start
     * @param end
     * @param current
     * @param size
     * @return
     */
    Page<WeighingEntity> list(String batchNumber, String carNum, String driverName, Long start, Long end, Integer current, Integer size);

}
