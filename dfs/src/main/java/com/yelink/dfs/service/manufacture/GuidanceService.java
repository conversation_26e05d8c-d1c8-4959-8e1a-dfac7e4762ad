package com.yelink.dfs.service.manufacture;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yelink.dfs.entity.manufacture.GuidanceEntity;


/**
 * Service
 *
 * <AUTHOR>
 * @Date 2020-12-03 11:14
 */
public interface GuidanceService extends IService<GuidanceEntity> {

    /**
     * 分页、模糊查询
     *
     * @param code    产品编码
     * @param title   标题
     * @param current
     * @param size
     * @return
     */
    Page<GuidanceEntity> getList(String code, String title, Integer current, Integer size);
}
