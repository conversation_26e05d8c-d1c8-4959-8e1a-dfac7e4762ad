package com.yelink.dfs.service.attendance;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yelink.dfs.entity.attendance.AttendanceEntity;
import com.yelink.dfs.entity.attendance.dto.AttendanceCheckDTO;
import com.yelink.dfs.entity.attendance.dto.AttendanceCheckExitTodayDTO;
import com.yelink.dfs.entity.attendance.dto.AttendanceDownDTO;
import com.yelink.dfs.entity.attendance.dto.AttendanceQrCodeInfoDTO;
import com.yelink.dfs.entity.attendance.dto.AttendanceScanUserDTO;
import com.yelink.dfs.entity.attendance.dto.AttendanceSupplementaryAdminDTO;
import com.yelink.dfs.entity.attendance.vo.AttendanceCheckVO;
import com.yelink.dfs.entity.attendance.vo.AttendanceRecordVO;
import com.yelink.dfs.entity.attendance.vo.AttendanceScanWorkOrderVO;

import java.util.Date;
import java.util.List;

/**
 * 打卡考勤服务
 * <AUTHOR>
 */
public interface AttendanceService extends IService<AttendanceEntity> {
    /**
     * 扫描的二维码， 获取上/下机的对应的参数
     * @param dto 二维码入参
     * @return 上/下机的对应的参数
     */
    List<AttendanceScanWorkOrderVO> scanQrCode(AttendanceQrCodeInfoDTO dto);

    /**
     * 当日上机打卡
     * @param dto 入参
     * @param scanUser 是否扫用户信息
     * @return 上机记录
     */
    AttendanceRecordVO submitUp(AttendanceCheckDTO dto, boolean scanUser);

    /**
     * 当日下机打卡
     * @param dto 入参
     * @return 下机记录
     */
    AttendanceRecordVO submitDown(AttendanceDownDTO dto);

    /**
     * 主管工时补录
     * @param dto 入参
     */
    void submitSupplementary(AttendanceSupplementaryAdminDTO dto);

    /**
     * 下机补卡：员工
     * @param dto 入参
     * @return 下机记录
     */
    AttendanceRecordVO supplementaryDown(AttendanceDownDTO dto);

    /**
     * 进入打卡页面，需要校验是否有未下班/漏打卡
     * @return 校验结果
     */
    AttendanceCheckVO check();

    /**
     * 当日下机打卡 (扫码)
     * @param dto 入参
     * @return 下机记录
     */
    AttendanceRecordVO scanDown(AttendanceScanUserDTO dto);

    /**
     * 获取某日的出勤人数
     * @param list 出勤列表
     * @param recordDate 日期： 没有时分秒
     * @return 人数
     */
    long getAttendanceNum(List<AttendanceEntity> list, Date recordDate);


    /**
     * 对用户, 检查某个工单当日是否有未下机的记录
     * @param dto 入参
     * @return 打卡记录id
     */
    Integer checkExitToday(AttendanceCheckExitTodayDTO dto);
}
