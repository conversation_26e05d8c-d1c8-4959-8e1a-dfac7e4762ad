package com.yelink.dfs.service.valuation;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yelink.dfs.entity.valuation.ValuationConfigEntity;
import com.yelink.dfs.entity.valuation.dto.ValuationConfigInsertDTO;
import com.yelink.dfs.entity.valuation.dto.ValuationConfigSelectDTO;
import com.yelink.dfs.entity.valuation.dto.ValuationConfigUpdateDTO;
import com.yelink.dfs.entity.valuation.vo.ValuationConfigVO;
import com.yelink.dfscommon.entity.common.BatchChangeStateDTO;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface ValuationConfigService extends IService<ValuationConfigEntity> {


    /**
     * 添加配置
     * @param dto 入参
     */
    void add(ValuationConfigInsertDTO dto);

    /**
     * 更新配置
     * @param dto 入参
     */
    void update(ValuationConfigUpdateDTO dto);

    /**
     * 分页查询
     * @param dto 入参
     * @return 分页列表
     */
    Page<ValuationConfigVO> getPage(ValuationConfigSelectDTO dto);

    /**
     * 查明细
     * @param id id
     * @return 明细
     */
    ValuationConfigVO detail(Integer id);

    /**
     * 删除
     * @param id id
     */
    void delete(Integer id);

    /**
     * 是否存在为审核
     * @param dto 入参
     * @return 是否存在为审核
     */
    boolean checkExistNoAudit(ValuationConfigUpdateDTO dto);

    /**
     * 批量编辑
     * @param batchApprovalDTO
     * @param username
     * @return
     */
    Boolean batchUpdate(BatchChangeStateDTO batchApprovalDTO, String username);

    /**
     * 批量删除
     * @param ids
     * @return
     */
    void batchDelete(List<Integer> ids);
}
