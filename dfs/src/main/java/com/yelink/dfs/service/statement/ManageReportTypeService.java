package com.yelink.dfs.service.statement;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yelink.dfs.entity.statement.ManageReportEntity;
import com.yelink.dfs.entity.statement.ManageReportTypeEntity;

import java.util.List;

/**
 * 报表类型(DfsManageReportType)表服务接口
 *
 * <AUTHOR>
 * @since 2024-08-16 11:26:38
 */
public interface ManageReportTypeService extends IService<ManageReportTypeEntity> {

    List<ManageReportTypeEntity> listReportType(Integer typeId);

    /**
     * 新增报表类型
     */
    ManageReportTypeEntity addReportType(ManageReportTypeEntity reportType);

    /**
     * 更新报表类型
     */
    ManageReportTypeEntity updateReportType(ManageReportTypeEntity reportType);

    void removeReportType(Integer typeId);

    List<ManageReportEntity> listCanAddReport(Integer typeId);

    /**
     * 类型,并查出类型下关联的报表
     */
    List<ManageReportTypeEntity> listRelateReport(Integer typeId);


}

