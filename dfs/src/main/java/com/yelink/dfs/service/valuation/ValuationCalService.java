package com.yelink.dfs.service.valuation;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yelink.dfs.entity.valuation.dto.ValuationCalAuditDTO;
import com.yelink.dfs.entity.valuation.dto.ValuationCalReportSelectDTO;
import com.yelink.dfs.entity.valuation.dto.ValuationCalSelectDTO;
import com.yelink.dfs.entity.valuation.dto.ValuationManualReportAddDTO;
import com.yelink.dfs.entity.valuation.dto.ValuationManualReportEditDTO;
import com.yelink.dfs.entity.valuation.vo.ValuationCalVO;
import com.yelink.dfs.entity.valuation.vo.ValuationReportUserVO;
import com.yelink.dfs.entity.valuation.vo.ValuationReportWorkCenterVO;

import java.util.Collection;
import java.util.Date;
import java.util.List;


/**
 * <AUTHOR>
 */
public interface ValuationCalService {

    /**
     * 计算任务
     */
    void calTask();

    /**
     * 总的统计
     * @param recordDates 记录时间列表
     */
    void calTask(Collection<Date> recordDates);


    /**
     * 计时的统计
     * @param recordDates 记录时间列表
     */
    void durationTask(Collection<Date> recordDates);

    /**
     * 计件的统计
     * @param recordDates 记录时间列表
     */
    void countTask(Collection<Date> recordDates);

    /**
     * 审核工资
     * @param dtoList 入参
     */
    void audit(List<ValuationCalAuditDTO> dtoList);

    /**
     * 工资分页
     * @param dto 入参
     * @return 分页
     */
    Page<ValuationCalVO> getPage(ValuationCalSelectDTO dto);

    /**
     * 统计统计报表：用户维度
     * @param dto 入参
     * @return 分页
     */
    Page<ValuationReportUserVO> reportUserPage(ValuationCalReportSelectDTO dto);

    /**
     * 统计统计报表：工作中心维度
     * @param dto 入参
     * @return 分页
     */
    Page<ValuationReportWorkCenterVO> reportWorkCenterPage(ValuationCalReportSelectDTO dto);

    /**
     * 新增手动上报
     * @param dto 入参
     */
    void manualReportAdd(ValuationManualReportAddDTO dto);

    /**
     * 修改手动上报
     * @param dto 入参
     */
    void manualReportEdit(ValuationManualReportEditDTO dto);
}
