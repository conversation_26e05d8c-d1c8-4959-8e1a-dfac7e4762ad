package com.yelink.dfs.service.statistics.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.yelink.dfs.constant.model.FacilitiesTypeEnum;
import com.yelink.dfs.constant.user.FacUserStateEnum;
import com.yelink.dfs.entity.manufacture.FacUserReportEntity;
import com.yelink.dfs.entity.model.FacUserEntity;
import com.yelink.dfs.entity.model.FacilitiesEntity;
import com.yelink.dfs.mapper.manufacture.FacUserReportMapper;
import com.yelink.dfs.mapper.model.FacUserMapper;
import com.yelink.dfs.service.common.DictService;
import com.yelink.dfs.service.model.FacilitiesService;
import com.yelink.dfs.service.order.WorkOrderService;
import com.yelink.dfs.service.statistics.StatisticsService;
import com.yelink.dfs.service.user.SysUserService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @description: 统计业务层
 * @author: shuang
 * @time: 2020/6/30
 */
@Service
@AllArgsConstructor
public class StatisticsServiceImpl implements StatisticsService {

    private DictService dictService;
    private WorkOrderService workOrderService;
    private FacilitiesService facilitiesService;
    private SysUserService sysUserService;
    private FacUserMapper facUserMapper;
    private FacUserReportMapper facUserReportMapper;



    @Override
    public List<FacilitiesEntity> getFacUser(Integer lineId, String workOrder) {
        LambdaQueryWrapper<FacilitiesEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(FacilitiesEntity::getProductionLineId, lineId);
        wrapper.eq(FacilitiesEntity::getTypeOneCode, FacilitiesTypeEnum.CUBICLE.getCode());
        List<FacilitiesEntity> facilitiesEntities = facilitiesService.getByLineId(lineId);


        List<FacilitiesEntity> list = new ArrayList<>();

        for (FacilitiesEntity facility : facilitiesEntities) {
            Integer fid = facility.getFid();

            QueryWrapper<FacUserEntity> facUserEntityQueryWrapper = new QueryWrapper<>();
            facUserEntityQueryWrapper.lambda().eq(FacUserEntity::getFid, fid);
            List<FacUserEntity> facUserEntities = facUserMapper.selectList(facUserEntityQueryWrapper);
            List<String> usernames = facUserEntities.stream().map(FacUserEntity::getUserName).collect(Collectors.toList());
            Map<String, Integer> map = new HashMap<>();
            for (String username : usernames) {
                QueryWrapper<FacUserReportEntity> qw = new QueryWrapper<>();
                qw.lambda().eq(FacUserReportEntity::getUsername, username)
                        .eq(FacUserReportEntity::getFid, fid)
                        .eq(FacUserReportEntity::getWorkOrder, workOrder)
                        .orderByDesc(FacUserReportEntity::getCreateTime)
                        .orderByDesc(FacUserReportEntity::getId).last("limit 1");
                FacUserReportEntity reportEntity = facUserReportMapper.selectOne(qw);
                map.put(username, reportEntity == null ? FacUserStateEnum.FINISH.getCode() : reportEntity.getState());
            }
            for (FacUserEntity user : facUserEntities) {
                user.setState(map.get(user.getUserName()));
                user.setStateName(FacUserStateEnum.getNameByCode(user.getState()));
                user.setNickName(sysUserService.getNicknameByUsername(user.getUserName()));
            }
            facility.setFacUser(facUserEntities);
            list.add(facility);
        }
        return list;
    }

    @Override
    public void reportFacUser(String username, Integer fid, String workOrder, Integer state) {
        Date now = new Date();
        FacUserReportEntity build = FacUserReportEntity.builder()
                .fid(fid)
                .username(username)
                .nickname(sysUserService.getNicknameByUsername(username))
                .state(state)
                .workOrder(workOrder)
                .createTime(now)
                .build();
        facUserReportMapper.insert(build);
        //更改此工位员工最新的状态
        QueryWrapper<FacUserEntity> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(FacUserEntity::getFid, fid)
                .eq(FacUserEntity::getUserName, username);
        FacUserEntity facUserEntity = facUserMapper.selectOne(wrapper);
        facUserEntity.setState(state);
        facUserEntity.setUpdateDate(now);
        facUserMapper.updateById(facUserEntity);
    }


}

