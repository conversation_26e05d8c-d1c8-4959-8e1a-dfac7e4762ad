package com.yelink.dfs.service.statement.impl;

import com.alibaba.fastjson.JSON;
import com.asyncexcel.core.DataParam;
import com.asyncexcel.core.ExcelContext;
import com.asyncexcel.core.ExportPage;
import com.asyncexcel.core.annotation.ExcelHandle;
import com.asyncexcel.core.exporter.DataExportParam;
import com.asyncexcel.core.exporter.ExportContext;
import com.asyncexcel.core.exporter.ExportHandler;
import com.yelink.dfs.entity.model.GridEntity;
import com.yelink.dfs.entity.statement.dto.ProductionOrderExportDTO;
import com.yelink.dfs.open.v1.aksk.ams.ExtProductOrderInterface;
import com.yelink.dfs.service.common.CommonService;
import com.yelink.dfs.service.model.GridService;
import com.yelink.dfscommon.dto.ams.open.order.ProductOrderSelectOpenDTO;
import com.yelink.dfscommon.entity.ams.ProductOrderEntity;
import com.yelink.dfscommon.entity.ams.ProductOrderMaterialEntity;
import com.yelink.dfscommon.entity.dfs.MaterialEntity;
import com.yelink.dfscommon.pojo.PageResult;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 生产订单导出
 *
 * <AUTHOR>
 */
@Slf4j
@ExcelHandle
@RequiredArgsConstructor
public class ProductOrderExportHandler implements ExportHandler<ProductionOrderExportDTO> {

    private final ExtProductOrderInterface extProductOrderInterface;
    private final CommonService commonService;
    private final GridService gridService;
    /**
     * 导出数据最大不能超过100万行
     */
    public static final Long EXPORT_MAX_ROWS = 1000000L;


    @Override
    public void init(ExcelContext ctx, DataParam param) {
        ExportContext context = (ExportContext) ctx;
        Integer templateId = (Integer) param.getParameters().get("templateId");
        String templateSheetName = (String) param.getParameters().get("productOrder");
        String cleanSheetNames = (String) param.getParameters().get("cleanSheetNames");
        String templateFileUrl = (String) param.getParameters().get("templateFileUrl");
        if (StringUtils.isNotBlank(templateFileUrl)) {
            // 模板文件是一个url地址
            commonService.initExcelContext(templateFileUrl, templateSheetName, cleanSheetNames, context, ProductionOrderExportDTO.class, null);
        } else {
            commonService.initExcelContext(templateId,templateSheetName, cleanSheetNames, context, ProductionOrderExportDTO.class, null);
        }
    }

    @Override
    public ExportPage<ProductionOrderExportDTO> exportData(int startPage, int limit, DataExportParam param) {
        String filterFieldValue = (String)param.getParameters().get(this.getClass().getName());
        ProductOrderSelectOpenDTO selectDTO = StringUtils.isEmpty(filterFieldValue) ? new ProductOrderSelectOpenDTO() : JSON.parseObject(filterFieldValue, ProductOrderSelectOpenDTO.class);

        selectDTO.setCurrent(startPage);
        selectDTO.setSize(limit);
        PageResult<ProductOrderEntity> page = extProductOrderInterface.getPage(selectDTO);

        Map<String, String> codeNameMap = gridService.list().stream().collect(Collectors.toMap(GridEntity::getGcode, GridEntity::getGname));
        List<ProductionOrderExportDTO> exportList = new ArrayList<>();
        for (ProductOrderEntity orderEntity : page.getRecords()) {
            ProductOrderMaterialEntity productOrderMaterial = orderEntity.getProductOrderMaterial() != null ? orderEntity.getProductOrderMaterial() : new ProductOrderMaterialEntity();
            productOrderMaterial.setGridName(codeNameMap.get(productOrderMaterial.getGridCode()));
            MaterialEntity tempMaterial = productOrderMaterial.getMaterialFields() != null ? productOrderMaterial.getMaterialFields() : new MaterialEntity();

            exportList.add(ProductionOrderExportDTO.buildProductionOrderExportDTO(orderEntity, productOrderMaterial, tempMaterial));
            // 子订单
            if (CollectionUtils.isNotEmpty(orderEntity.getSubEntities())) {
                for (ProductOrderEntity subEntity : orderEntity.getSubEntities()) {
                    ProductOrderMaterialEntity subProductOrderMaterial = subEntity.getProductOrderMaterial();
                    subProductOrderMaterial.setGridName(codeNameMap.get(subProductOrderMaterial.getGridCode()));
                    MaterialEntity subMaterial = subProductOrderMaterial.getMaterialFields() != null ? subProductOrderMaterial.getMaterialFields() : new MaterialEntity();
                    exportList.add(ProductionOrderExportDTO.buildProductionOrderExportDTO(subEntity, subProductOrderMaterial, subMaterial));
                }
            }
        }
        ExportPage<ProductionOrderExportDTO> result = new ExportPage<>();
        result.setTotal(Math.min(page.getTotal(), EXPORT_MAX_ROWS));
        result.setCurrent((long) startPage);
        result.setSize((long) limit);
        result.setRecords(exportList);
        return result;
    }


    @Override
    public void beforePerPage(ExportContext ctx, DataExportParam param) {
        log.info("开始查询第{}页", Math.ceil(ctx.getSuccessCount() + ctx.getFailCount()) / ctx.getLimit());
    }

    @Override
    public void afterPerPage(List<ProductionOrderExportDTO> list, ExportContext ctx, DataExportParam param) {
        log.info("已完成数量：{}", ctx.getSuccessCount());
    }

    @Override
    public void callBack(ExcelContext ctx, DataParam param) {
        ExportContext context = (ExportContext) ctx;
        log.info("导出完成：{}", context.getFailMessage());
    }
}
