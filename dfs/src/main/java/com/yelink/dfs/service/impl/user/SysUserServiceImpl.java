package com.yelink.dfs.service.impl.user;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yelink.dfs.common.UserAuthenService;
import com.yelink.dfs.constant.Constant;
import com.yelink.dfs.constant.user.RoleEnum;
import com.yelink.dfs.constant.user.SkillLevelEnum;
import com.yelink.dfs.entity.alarm.dto.UserVo;
import com.yelink.dfs.entity.model.AreaEntity;
import com.yelink.dfs.entity.model.CompanyEntity;
import com.yelink.dfs.entity.model.FacilitiesEntity;
import com.yelink.dfs.entity.model.GridEntity;
import com.yelink.dfs.entity.terminal.SysPadBindRoleEntity;
import com.yelink.dfs.entity.terminal.SysPadEntity;
import com.yelink.dfs.entity.user.DepartmentEntity;
import com.yelink.dfs.entity.user.DepartmentUserEntity;
import com.yelink.dfs.entity.user.SysRoleEntity;
import com.yelink.dfs.entity.user.SysRoleProductionBasicUnitEntity;
import com.yelink.dfs.entity.user.SysUserEntity;
import com.yelink.dfs.entity.user.SysUserRoleEntity;
import com.yelink.dfs.entity.user.SysUserSignatureEntity;
import com.yelink.dfs.entity.user.dto.ResetPasswordDTO;
import com.yelink.dfs.entity.user.dto.SysUserPrintSelectDTO;
import com.yelink.dfs.entity.user.dto.UserSelectDTO;
import com.yelink.dfs.mapper.model.AreaMapper;
import com.yelink.dfs.mapper.model.CompanyMapper;
import com.yelink.dfs.mapper.model.FacilitiesMapper;
import com.yelink.dfs.mapper.model.GridMapper;
import com.yelink.dfs.mapper.terminal.SysPadMapper;
import com.yelink.dfs.mapper.user.SysRoleMapper;
import com.yelink.dfs.mapper.user.SysUserMapper;
import com.yelink.dfs.mapper.user.enums.EnabledEnum;
import com.yelink.dfs.open.v1.user.dto.SysUserDetailDTO;
import com.yelink.dfs.open.v1.user.dto.UserSelectOpenDTO;
import com.yelink.dfs.open.v1.user.vo.UserBindProductBasicUnitVO;
import com.yelink.dfs.open.v2.user.dto.UserQueryDTO;
import com.yelink.dfs.service.barcode.BarCodeAnalysisService;
import com.yelink.dfs.service.barcode.LabelService;
import com.yelink.dfs.service.barcode.LabelTypeConfigService;
import com.yelink.dfs.service.common.ColumnConfigurationService;
import com.yelink.dfs.service.common.UploadService;
import com.yelink.dfs.service.device.DeviceService;
import com.yelink.dfs.service.impl.common.WorkPropertise;
import com.yelink.dfs.service.keycloak.IAuthService;
import com.yelink.dfs.service.keycloak.KeyCloakService;
import com.yelink.dfs.service.manufacture.ProductionLineService;
import com.yelink.dfs.service.model.WorkCenterService;
import com.yelink.dfs.service.user.DepartmentService;
import com.yelink.dfs.service.user.DepartmentUserService;
import com.yelink.dfs.service.user.SysPostService;
import com.yelink.dfs.service.user.SysPostUserService;
import com.yelink.dfs.service.user.SysRoleProductionBasicUnitService;
import com.yelink.dfs.service.user.SysRoleService;
import com.yelink.dfs.service.user.SysTeamService;
import com.yelink.dfs.service.user.SysUserRoleService;
import com.yelink.dfs.service.user.SysUserService;
import com.yelink.dfs.service.user.SysUserSignatureService;
import com.yelink.dfs.utils.ExtApiUtil;
import com.yelink.dfs.utils.SpringUtil;
import com.yelink.dfs.utils.SqlUtil;
import com.yelink.dfscommon.constant.RespCodeEnum;
import com.yelink.dfscommon.constant.ResponseException;
import com.yelink.dfscommon.constant.dfs.barcode.LabelInfoEnum;
import com.yelink.dfscommon.constant.model.WorkCenterTypeEnum;
import com.yelink.dfscommon.dto.dfs.CodeInfoSelectDTO;
import com.yelink.dfscommon.dto.dfs.push.ProductBasicUnitVO;
import com.yelink.dfscommon.dto.dfs.push.WorkCenterVO;
import com.yelink.dfscommon.entity.dfs.SysPostEntity;
import com.yelink.dfscommon.entity.dfs.SysPostUserEntity;
import com.yelink.dfscommon.entity.dfs.barcode.LabelEntity;
import com.yelink.dfscommon.entity.dfs.barcode.LabelTypeConfigEntity;
import com.yelink.dfscommon.entity.dfs.barcode.PrintDTO;
import com.yelink.dfscommon.utils.FieldUtil;
import com.yelink.inner.common.AppsService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.keycloak.representations.AccessTokenResponse;
import org.springframework.context.annotation.Lazy;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.yelink.dfs.constant.Constant.KEY_CLOAK;

/**
 * @description:
 * @author: shuang
 * @time: 2020/4/15
 */
@Slf4j
@Service
public class SysUserServiceImpl extends ServiceImpl<SysUserMapper, SysUserEntity> implements SysUserService {
    @Resource
    private SysUserService userService;
    @Resource
    private SysRoleMapper sysRoleMapper;
    @Resource
    private CompanyMapper companyMapper;
    @Resource
    private AreaMapper areaMapper;
    @Resource
    private GridMapper gridMapper;
    @Resource
    private FacilitiesMapper facilitiesMapper;
    @Resource
    private DepartmentService departmentService;
    @Resource
    private DepartmentUserService departmentUserService;
    @Resource
    private WorkPropertise workPropertise;
    @Resource
    private KeyCloakService keyCloakService;
    @Resource
    private UploadService uploadService;
    @Resource
    private SysPadMapper sysPadMapper;
    @Lazy
    @Resource
    private AppsService appsService;
    @Resource
    private SysPostUserService sysPostUserService;
    @Resource
    private LabelService labelService;
    @Resource
    private SysUserRoleService userRoleService;
    @Resource
    private SysPostService postService;
    @Resource
    private SysRoleService roleService;
    @Resource
    private SysUserSignatureService userSignatureService;
    @Resource
    private IAuthService iAuthService;
    @Resource
    @Lazy
    private WorkCenterService workCenterService;
    @Resource
    @Lazy
    private SysRoleProductionBasicUnitService roleProductionBasicUnitService;
    @Resource
    @Lazy
    private ProductionLineService productionLineService;
    @Resource
    @Lazy
    private SysTeamService teamService;
    @Resource
    @Lazy
    private DeviceService deviceService;
    @Resource
    private LabelTypeConfigService labelTypeConfigService;
    @Resource
    protected UserAuthenService userAuthenService;
    @Resource
    private ColumnConfigurationService columnConfigurationService;
    /**
     * 打印时需要替换的占位符 ${ }
     */
    private static final Pattern PATTERN = Pattern.compile("\\$\\{(.*?)\\}");

    @Override
    @Transactional(rollbackFor = Exception.class)
    public synchronized void add(SysUserEntity sysUserEntity, String roleIds, String handelName, String token) {
        // 如果系统管理员用户，则不能新增用户
        List<String> names = roleService.selectByUsername(handelName).stream().map(SysRoleEntity::getName).collect(Collectors.toList());
        if (names.contains(RoleEnum.SYSTEM_ADMIN.getName()) && handelName.equals(Constant.YELINK_ONCALL)) {
            throw new ResponseException(RespCodeEnum.YELINK_ONCALL_NOT_ALLOWED.getMsgDes());
        }
        // 判断系统中存不存在该用户,如果存在则将用户改为启用
        SysUserEntity oldUserEntity = this.lambdaQuery().eq(SysUserEntity::getUsername, sysUserEntity.getUsername()).one();
        if (Objects.nonNull(oldUserEntity)) {
            this.lambdaUpdate().eq(SysUserEntity::getUsername, oldUserEntity.getUsername()).set(SysUserEntity::getIsDelete, false).update();
            updateDisableById(Constant.ENABLE, String.valueOf(oldUserEntity.getId()), userAuthenService.getUsername());
            return;
        }
        sysUserEntity.setEnabled(EnabledEnum.ENABLE.getCode());
        // 用户名转小写 和keycloak用户名保持一致，因为keyclock存在用户名大写转小写的情况
        sysUserEntity.setUsername(StringUtils.lowerCase(sysUserEntity.getUsername()));
        // 如果用户创建了相同的username，此时应该将原来用户记录变为enable并更新
        SysUserEntity existUser = selectByUsername(sysUserEntity.getUsername());
        if (existUser != null) {
//            throw new ResponseException(RespCodeEnum.USERNAME_EXIST.getMsgDes());
            sysUserEntity.setId(existUser.getId());
            update(sysUserEntity, token);
            return;
        }
        //判断用户姓名是否重复
        Long nameCount = this.lambdaQuery().eq(SysUserEntity::getNickname, sysUserEntity.getNickname())
                .eq(SysUserEntity::getEnabled, Constant.ENABLE).count();
        if (nameCount > 0) {
            throw new ResponseException(RespCodeEnum.NICKNAME_EXIST.getMsgDes());
        }

        // 获取部门名称(用于用户列表展示)
        if (StringUtils.isNotBlank(sysUserEntity.getDepartmentId())) {
            getDepartmentName(sysUserEntity);
        } else {
            sysUserEntity.setDepartmentName(Constant.BLANK);
        }
        sysUserEntity.setCreatByJz(false);
        sysUserEntity.setCreateTime(Objects.nonNull(sysUserEntity.getCreateTime()) ? sysUserEntity.getCreateTime() : new Date());
        sysUserEntity.setUpdateTime(new Date());
        this.save(sysUserEntity);
        // 用户绑定部门
        userBindDepartment(sysUserEntity);
        // 用户绑定角色
        insertUserBindRole(sysUserEntity, roleIds);
        // 插入用户签名
        insertUserSignature(sysUserEntity);
        uploadService.markUploadFile(sysUserEntity.getHeaderUrl(), sysUserEntity);
        uploadService.markUploadFile(sysUserEntity.getSignatureUrl(), sysUserEntity);

        // 精制和dfs需要强耦合，如果精制报错，dfs也不能新增成功
//        if (TransactionSynchronizationManager.isActualTransactionActive()) {
//            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
//                @Override
//                public void afterCommit() {
        // 添加精制用户
//        boolean success = appsService.addUserByJz(sysUserEntity, token);
        appsService.addUserByJz(sysUserEntity, token);
//        if (!success) {
//            throw new ResponseException(RespCodeEnum.JINZHI_ADD_USER_FAIL);
            // 调用精制添加keycloak用户失败 直接调用keycloak新增用户，兼容精制旧版本
//                        SysUserEntity sysUser = keyCloakService.addUser(sysUserEntity);
//                        userService.updateEntity(sysUser);
//        }
//                }
//            });
//        }
        //添加默认计划、生产列配置权限
        columnConfigurationService.addDefaultColumn(sysUserEntity.getUsername());
    }

    /**
     * 用户绑定部门
     */
    private void userBindDepartment(SysUserEntity sysUserEntity) {
        departmentUserService.lambdaUpdate().eq(DepartmentUserEntity::getUserId, sysUserEntity.getId()).remove();
        if (StringUtils.isBlank(sysUserEntity.getDepartmentId())) {
            return;
        }
        List<String> departmentIds = Arrays.asList(sysUserEntity.getDepartmentId().split(Constant.SEP));
        List<DepartmentUserEntity> departmentUserEntities = departmentIds.stream()
                .map(o -> DepartmentUserEntity.builder().departmentId(o).userId(sysUserEntity.getId()).build())
                .collect(Collectors.toList());
        departmentUserService.saveBatch(departmentUserEntities);
    }

    /**
     * 插入用户角色关联表
     */
    private void insertUserBindRole(SysUserEntity sysUserEntity, String roleId) {
        //传null不更新
        if (roleId == null) {
            return;
        }
        // 删除原绑定关系
        userRoleService.lambdaUpdate().eq(SysUserRoleEntity::getUserId, sysUserEntity.getId()).remove();
        // 插入用户角色关联表
        if (StringUtils.isBlank(roleId)) {
            return;
        }
        List<String> roleIds = Arrays.asList(roleId.split(Constant.SEP));
        List<SysRoleEntity> roleEntities = roleService.listByIds(roleIds);
        if (CollectionUtils.isEmpty(roleEntities)) {
            return;
        }
        List<SysUserRoleEntity> userRoleEntities = roleEntities.stream().map(o ->
                SysUserRoleEntity.builder().roleId(o.getId()).userId(sysUserEntity.getId()).build()
        ).collect(Collectors.toList());
        userRoleService.saveBatch(userRoleEntities);
    }

    /**
     * 插入用户签名
     */
    private void insertUserSignature(SysUserEntity sysUserEntity) {
        String signatureUrl = sysUserEntity.getSignatureUrl();
        //传null不更新
        if (signatureUrl == null) {
            return;
        }

        Integer userId = sysUserEntity.getId();
        // 删除原绑定关系
        userSignatureService.lambdaUpdate().eq(SysUserSignatureEntity::getUserId, userId).remove();
        // 插入用户角色关联表
        if (StringUtils.isBlank(signatureUrl)) {
            return;
        }
        SysUserSignatureEntity signatureEntity = SysUserSignatureEntity.builder().userId(userId).userName(sysUserEntity.getUsername())
                .signatureUrl(signatureUrl).createBy(sysUserEntity.getUpdateBy()).updateBy(sysUserEntity.getUpdateBy())
                .createTime(sysUserEntity.getUpdateTime()).updateTime(sysUserEntity.getUpdateTime()).build();
        // 插入用户签名
        userSignatureService.save(signatureEntity);
    }

    /**
     * 获取部门名称
     */
    @Override
    public void getDepartmentName(SysUserEntity sysUserEntity) {
        List<String> departmentIdsArr = new ArrayList<>();
        if (StringUtils.isNotBlank(sysUserEntity.getDepartmentId())) {
            departmentIdsArr = Arrays.asList(sysUserEntity.getDepartmentId().split(Constant.SEP));
        }
        StringBuilder sb = new StringBuilder();
        for (String s : departmentIdsArr) {
            DepartmentEntity departmentEntity = departmentService.lambdaQuery().eq(DepartmentEntity::getDepartmentId, s).one();
            if (departmentEntity != null) {
                sb.append(departmentEntity.getDepartmentName()).append("/");
            }
        }
        if (StringUtils.isNotBlank(sb)) {
            sb.deleteCharAt(sb.length() - 1);
        }
        sysUserEntity.setDepartmentName(sb.toString());
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(SysUserEntity sysUserEntity, String token) {
        SysUserEntity old = selectByUsername(sysUserEntity.getUsername());
        // 获取部门名称
        getDepartmentName(sysUserEntity);
        // 用户重新绑定部门
        userBindDepartment(sysUserEntity);
        // 更新角色信息
        insertUserBindRole(sysUserEntity, sysUserEntity.getRoleId());
        //删除已绑定岗位
//        sysPostUserService.lambdaUpdate().eq(SysPostUserEntity::getUserId, sysUserEntity.getId()).remove();
        // 插入用户签名
        insertUserSignature(sysUserEntity);
        //更新基础模型
        updateComanyMagInfo(sysUserEntity);
        updateAreaMagInfo(sysUserEntity);
        updateGridMagInfo(sysUserEntity);
        updateFacilitiesMagInfo(sysUserEntity);
        // 如果使用keycloak权限
        if (KEY_CLOAK.equals(workPropertise.getOauthType())) {
            // 判断是否有keycloakid，有keycloakid才可已进行更新
            if (StringUtils.isEmpty(old.getKeycloakId())) {
                throw new ResponseException(RespCodeEnum.KC_USER_IS_NOT_EXIST);
            }
            sysUserEntity.setKeycloakId(old.getKeycloakId());
        }
        updateEntity(sysUserEntity);
        //标记上传文件
        uploadService.markUploadFile(old.getHeaderUrl(), sysUserEntity.getHeaderUrl(), sysUserEntity);
        uploadService.markUploadFile(old.getSignatureUrl(), sysUserEntity.getSignatureUrl(), sysUserEntity);
//        if (TransactionSynchronizationManager.isActualTransactionActive()) {
//            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
//                @Override
//                public void afterCommit() {
        // 同步精制,失败则回滚
        appsService.syncRoleBindUserInfo(Collections.singletonList(sysUserEntity.getUsername()), token);
//                }
//            });
//        }
    }

    private void updateComanyMagInfo(SysUserEntity sysUserEntity) {
        QueryWrapper<CompanyEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(CompanyEntity::getMagName, sysUserEntity.getUsername());
        companyMapper.update(CompanyEntity.builder().magNickname(sysUserEntity.getNickname()).magPhone(sysUserEntity.getMobile()).build(), queryWrapper);
    }

    private void updateAreaMagInfo(SysUserEntity sysUserEntity) {
        QueryWrapper<AreaEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(AreaEntity::getMagName, sysUserEntity.getUsername());
        areaMapper.update(AreaEntity.builder().magNickname(sysUserEntity.getNickname()).magPhone(sysUserEntity.getMobile()).build(), queryWrapper);
    }

    private void updateGridMagInfo(SysUserEntity sysUserEntity) {
        QueryWrapper<GridEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(GridEntity::getMagName, sysUserEntity.getUsername());
        gridMapper.update(GridEntity.builder().magNickname(sysUserEntity.getNickname()).magPhone(sysUserEntity.getMobile()).build(), queryWrapper);
    }

    private void updateFacilitiesMagInfo(SysUserEntity sysUserEntity) {
        QueryWrapper<FacilitiesEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(FacilitiesEntity::getMagName, sysUserEntity.getUsername());
        facilitiesMapper.update(FacilitiesEntity.builder().magNickname(sysUserEntity.getNickname()).magPhone(sysUserEntity.getMobile()).build(), queryWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public SysUserEntity updateDisableById(String disOrEnable, String id, String updateBy) {
        SysUserEntity sysUserEntity = getUserById(Integer.valueOf(id));
        // 判断禁用的用户是否属于部门负责人， 部门负责人不能禁用
        Long count = departmentService.lambdaQuery().eq(DepartmentEntity::getResponsibleName, sysUserEntity.getUsername()).count();
        if (count > 0) {
            throw new ResponseException(RespCodeEnum.USER_EXIST_RELATE_DEPARTMENT);
        }
        // 内置用户不能禁用（admin，yelinkoncall账号）
        List<String> builtInUserNames = Stream.of(Constant.ADMIN, Constant.YELINK_ONCALL).collect(Collectors.toList());
        if (disOrEnable.equals(EnabledEnum.DISABLE.getCode()) && builtInUserNames.contains(sysUserEntity.getUsername())) {
            throw new ResponseException(RespCodeEnum.CANNOT_DISABLE_BUILT_IN_USER);
        }
        this.lambdaUpdate()
                .eq(SysUserEntity::getId, id)
                .set(SysUserEntity::getEnabled, disOrEnable)
                .set(SysUserEntity::getUpdateBy, updateBy)
                .update();
        // 更新用户角色表是否启用
        userRoleService.lambdaUpdate().eq(SysUserRoleEntity::getUserId, sysUserEntity.getId())
                .set(SysUserRoleEntity::getEnabled, disOrEnable.equals(EnabledEnum.ENABLE.getCode()) ? EnabledEnum.ENABLE.getCode() : EnabledEnum.DISABLE.getCode())
                .update();
        // 用户的启用禁用切换
        if (KEY_CLOAK.equals(workPropertise.getOauthType()) && StringUtils.isNotBlank(updateBy)) {
            // 先调用精制修改用户状态，不成功再调用keycloak修改
            // 注释原因：dfs的禁用不影响精制的登录，所以不调用精制的更新用户接口
//            boolean success = appsService.updateUser(disOrEnable, sysUserEntity);
//            if (!success) {
//                keyCloakService.changeStatus(disOrEnable, sysUserEntity);
//            }
            // 重新绑定精制用户-角色映射关系
            String token = ExtApiUtil.getTokenBasedOnContext();
            appsService.syncRoleBindUserInfo(Collections.singletonList(sysUserEntity.getUsername()), token);
        }
        sysUserEntity.setDisEnableLog(EnabledEnum.getNameByCode(disOrEnable));
        return sysUserEntity;
    }

    @Override
    public SysUserEntity selectById(Integer id) {
        SysUserEntity sysUserEntity = getUserById(id);
        if (sysUserEntity != null) {
            sysUserEntity.setSkillLevelName(SkillLevelEnum.getNameByCode(sysUserEntity.getSkillLevel()));
            // 获取用户关联的部门ID
            List<DepartmentUserEntity> departmentUserEntityList = departmentUserService.lambdaQuery().eq(DepartmentUserEntity::getUserId, id).list();
            List<String> collect = departmentUserEntityList.stream().map(DepartmentUserEntity::getDepartmentId).collect(Collectors.toList());
            StringBuffer sb = new StringBuffer();
            if (!collect.isEmpty()) {
                for (String departmentId : collect) {
                    sb.append(departmentId).append(Constant.SEP);
                }
                sb.deleteCharAt(sb.length() - 1);
            }
            sysUserEntity.setDepartmentId(sb.toString());
            sysUserEntity.setPassword(null);
        }
        return sysUserEntity;
    }

    @Override
    public SysUserEntity selectRoleByUsername(String username) {
        // 判断是否在用户表
        SysUserEntity sysUserEntity = this.lambdaQuery().eq(SysUserEntity::getUsername, username).one();
        showRoleAndPostInfo(Stream.of(sysUserEntity).collect(Collectors.toList()));
        if (sysUserEntity != null) {
            sysUserEntity.setPassword(null);
            return sysUserEntity;
        } else {
            // 判断是否在Pad表（免密登录特性）
            SysPadEntity sysPadEntity = sysPadMapper.selectByPadMac(username);
            if (sysPadEntity != null) {
                String roleIds = sysRoleMapper.selectRoleBindPad(sysPadEntity.getId()).stream()
                        .map(SysPadBindRoleEntity::getRoleId).collect(Collectors.toList()).toString();
                // 构建返回的用户信息
                return SysUserEntity.builder().roleId(roleIds).nickname(sysPadEntity.getName()).username(sysPadEntity.getMac()).build();
            }
        }
        return null;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public SysUserEntity updatePassword(String username, String oldPassword, String newPassword) {
        SysUserEntity sysUserEntity = selectByUsername(username);
        BCryptPasswordEncoder bcryptPasswordEncoder = new BCryptPasswordEncoder();

        // 调用keyCloak获取token接口 判断旧密码是否正确
        checkOldPassword(username, oldPassword, sysUserEntity, bcryptPasswordEncoder);
        if (oldPassword.equals(newPassword)) {
            throw new ResponseException(RespCodeEnum.PASSWORD_IS_SAME_AS_OLD);
        }
        // 旧密码正确 设置新密码
        if (KEY_CLOAK.equals(workPropertise.getOauthType())) {
            boolean success = appsService.resetPassword(newPassword, sysUserEntity.getKeycloakId());
            // 调用精制修改密码失败 直接调用keycloak
            if (!success) {
                keyCloakService.resetPassword(sysUserEntity, newPassword);
            }
        }
        sysUserEntity.setPassword(null);
        return sysUserEntity;
    }


    private void checkOldPassword(String username, String password, SysUserEntity sysUserEntity, BCryptPasswordEncoder bcryptPasswordEncoder) {
        boolean match = false;
        // keycloak
        if (KEY_CLOAK.equals(workPropertise.getOauthType())) {
            AccessTokenResponse accessTokenResponse = iAuthService.auth(username, password);
            if (accessTokenResponse != null) {
                match = true;
            }
        } else {
            // 兼容开发环境
            match = bcryptPasswordEncoder.matches(password, sysUserEntity.getPassword());
        }
        if (!match) {
            throw new ResponseException(RespCodeEnum.OLD_PASSWORD_FAIL);
        }
    }

    @Override
    public List<SysUserEntity> selectList() {
        return this.lambdaQuery().orderByDesc(SysUserEntity::getCreateTime).list();
    }

    @Override
    public List<SysUserEntity> selectByUsernames(String[] usernames) {
        if (ArrayUtils.isEmpty(usernames)) {
            return new ArrayList<>();
        }
        List<String> userNameList = Arrays.asList(usernames);
        return this.lambdaQuery().select(SysUserEntity::getId, SysUserEntity::getUsername, SysUserEntity::getNickname,
                        SysUserEntity::getMobile, SysUserEntity::getKeycloakId, SysUserEntity::getDepartmentId,
                        SysUserEntity::getEmail, SysUserEntity::getJobNumber, SysUserEntity::getPost, SysUserEntity::getEntryDate,
                        SysUserEntity::getWechat, SysUserEntity::getSkillLevel, SysUserEntity::getSex, SysUserEntity::getAge)
                .in(SysUserEntity::getUsername, userNameList).list();
    }

    @Override
    public Page<SysUserEntity> selectPageNotIncludeYelinkOncall(String userName, String nickName, String roleName, String key, String sort, Page<SysUserEntity> page, String mobile,
                                                                String email, String jobNumber, String post, String departmentName, List<String> usernames, String userState, List<String> roleNames, List<Integer> userIds) {
        LambdaQueryWrapper<SysUserEntity> wrapper = new LambdaQueryWrapper<>();
        // 查询角色
        if (StringUtils.isNotBlank(roleName) || !CollectionUtils.isEmpty(roleNames)) {
            List<SysRoleEntity> roleEntities = roleService.lambdaQuery()
                    .like(StringUtils.isNotBlank(roleName), SysRoleEntity::getName, roleName)
                    .in(!CollectionUtils.isEmpty(roleNames), SysRoleEntity::getName, roleNames)
                    .list();
            if (CollectionUtils.isEmpty(roleEntities)) {
                return new Page<>();
            }
            List<Integer> roleIds = roleEntities.stream().map(SysRoleEntity::getId).collect(Collectors.toList());
            List<SysUserRoleEntity> userRoleEntities = userRoleService.lambdaQuery().eq(SysUserRoleEntity::getEnabled, EnabledEnum.ENABLE.getCode()).in(SysUserRoleEntity::getRoleId, roleIds).list();
            if (CollectionUtils.isEmpty(userRoleEntities)) {
                return new Page<>();
            }
            List<Integer> roleUserIds = userRoleEntities.stream().map(SysUserRoleEntity::getUserId).collect(Collectors.toList());
            wrapper.in(SysUserEntity::getId, roleUserIds);
        }

        // 查询除yelinkoncall外的所有用户信息，PS:yelinkoncall用户只能维护用户权限，在系统中不做其他途径使用
        wrapper.ne(SysUserEntity::getUsername, Constant.YELINK_ONCALL)
                .eq(SysUserEntity::getIsDelete, false)
                .like(StringUtils.isNotBlank(userName), SysUserEntity::getUsername, userName)
                .like(StringUtils.isNotBlank(nickName), SysUserEntity::getNickname, nickName)
                .like(StringUtils.isNotBlank(mobile), SysUserEntity::getMobile, mobile)
                .like(StringUtils.isNotBlank(email), SysUserEntity::getEmail, email)
                .like(StringUtils.isNotBlank(jobNumber), SysUserEntity::getJobNumber, jobNumber)
                .in(StringUtils.isNotBlank(userState), SysUserEntity::getEnabled, StringUtils.isNotBlank(userState) ? userState.split(Constant.SEP) : null)
                .like(StringUtils.isNotBlank(post), SysUserEntity::getPost, post)
                .like(StringUtils.isNotBlank(departmentName), SysUserEntity::getDepartmentName, departmentName)
                .in(!CollectionUtils.isEmpty(usernames), SysUserEntity::getUsername, usernames)
                .in(!CollectionUtils.isEmpty(userIds), SysUserEntity::getId, userIds)
                .orderBy("createTime".equals(key), "asc".equals(sort), SysUserEntity::getCreateTime)
                .orderBy("updateTime".equals(key), "asc".equals(sort), SysUserEntity::getUpdateTime)
                .orderByDesc(SysUserEntity::getId);
        Page<SysUserEntity> userPage = this.page(page, wrapper);
        if (CollectionUtils.isEmpty(userPage.getRecords())) {
            return page;
        }
        // 查询角色、岗位相关信息
        showRoleAndPostInfo(userPage.getRecords());
        return page;
    }

    /**
     * 查询角色、岗位相关信息
     */
    private void showRoleAndPostInfo(List<SysUserEntity> userEntities) {
        for (SysUserEntity userEntity : userEntities) {
            List<SysUserRoleEntity> userRoleEntities = userRoleService.lambdaQuery().eq(SysUserRoleEntity::getEnabled, EnabledEnum.ENABLE.getCode()).eq(SysUserRoleEntity::getUserId, userEntity.getId()).list();
            if (!CollectionUtils.isEmpty(userRoleEntities)) {
                List<Integer> roleIds = userRoleEntities.stream().map(SysUserRoleEntity::getRoleId).collect(Collectors.toList());
                List<SysRoleEntity> roles = roleService.listByIds(roleIds);
                String roleNames = roles.stream().map(SysRoleEntity::getName).collect(Collectors.joining(Constant.SEP));
                String roleCodes = roles.stream().map(SysRoleEntity::getRoleCode).collect(Collectors.joining(Constant.SEP));
                String roleId = String.join(Constant.SEP, roleIds.stream().map(String::valueOf).collect(Collectors.joining(Constant.SEP)));
                userEntity.setRoleName(roleNames);
                userEntity.setRoleId(roleId);
                userEntity.setRoleIds(roleId);
                userEntity.setRoleCode(roleCodes);
            }
            List<SysPostUserEntity> postUserEntities = sysPostUserService.lambdaQuery().eq(SysPostUserEntity::getUserId, userEntity.getId()).list();
            if (!CollectionUtils.isEmpty(postUserEntities)) {
                List<Integer> postIds = postUserEntities.stream().map(SysPostUserEntity::getPostId).collect(Collectors.toList());
                List<SysPostEntity> postEntities = postService.listByIds(postIds);
                String postNames = postEntities.stream().map(SysPostEntity::getPostName).collect(Collectors.joining(Constant.SEP));
                userEntity.setPostNames(postNames);
                userEntity.setPostIds(String.join(Constant.SEP, postIds.stream().map(String::valueOf).collect(Collectors.joining(Constant.SEP))));
            }
            SysUserSignatureEntity signatureEntity = userSignatureService.lambdaQuery().eq(SysUserSignatureEntity::getUserId, userEntity.getId()).one();
            if (signatureEntity != null) {
                userEntity.setSignatureUrl(signatureEntity.getSignatureUrl());
            }
            if (StringUtils.isNotBlank(userEntity.getDepartmentId())) {
                DepartmentEntity departmentEntity = departmentService.lambdaQuery().eq(DepartmentEntity::getDepartmentId, userEntity.getDepartmentId()).one();
                userEntity.setDepartmentCode(Objects.isNull(departmentEntity) ? null : departmentEntity.getDepartmentCode());
            }
        }
    }

    @Override
    public Page<SysUserEntity> getList(UserSelectOpenDTO dto) {
        int size = dto.getSize() == null ? Integer.MAX_VALUE : dto.getSize();
        int current = dto.getCurrent() == null ? 1 : dto.getCurrent();
        LambdaQueryWrapper<SysUserEntity> wrapper = new LambdaQueryWrapper<>();
        // 查询除yelinkoncall外的所有用户信息，PS:yelinkoncall用户只能维护用户权限，在系统中不做其他途径使用
        wrapper.ne(SysUserEntity::getUsername, Constant.YELINK_ONCALL)
                .eq(Objects.nonNull(dto.getId()), SysUserEntity::getId, dto.getId())
                .eq(StringUtils.isNotBlank(dto.getMobile()), SysUserEntity::getMobile, dto.getMobile())
                .like(StringUtils.isNotBlank(dto.getNickname()), SysUserEntity::getNickname, dto.getNickname())
                .eq(StringUtils.isNotBlank(dto.getEnable()), SysUserEntity::getEnabled, dto.getEnable())
                .like(StringUtils.isNotBlank(dto.getUsername()), SysUserEntity::getUsername, dto.getUsername())
                .between(StringUtils.isNoneBlank(dto.getUpdateStartTime(), dto.getUpdateEndTime()), SysUserEntity::getUpdateTime, dto.getUpdateStartTime(), dto.getUpdateEndTime())
                .in(!CollectionUtils.isEmpty(dto.getUserNames()), SysUserEntity::getUsername, dto.getUserNames());
        // 部门名称查询
        if (StringUtils.isNotBlank(dto.getDepartmentName()) || !CollectionUtils.isEmpty(dto.getDepartmentNames())) {
            List<Integer> departmentIds = departmentService.lambdaQuery()
                    .like(StringUtils.isNotBlank(dto.getDepartmentName()), DepartmentEntity::getDepartmentName, dto.getDepartmentName())
                    .in(!CollectionUtils.isEmpty(dto.getDepartmentNames()), DepartmentEntity::getDepartmentName, dto.getDepartmentNames())
                    .list().stream()
                    .map(DepartmentEntity::getDepartmentId)
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(departmentIds)) {
                return new Page<>();
            }
            List<Integer> userIds = departmentUserService.lambdaQuery().in(DepartmentUserEntity::getDepartmentId, departmentIds)
                    .list().stream()
                    .map(DepartmentUserEntity::getUserId)
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(userIds)) {
                return new Page<>();
            }
            wrapper.in(SysUserEntity::getId, userIds);
        }
        // 部门编码查询
        if (!CollectionUtils.isEmpty(dto.getDepartmentCodes())) {
            List<Integer> departmentIds = departmentService.lambdaQuery()
                    .in(DepartmentEntity::getDepartmentCode, dto.getDepartmentCodes())
                    .list().stream()
                    .map(DepartmentEntity::getDepartmentId)
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(departmentIds)) {
                return new Page<>();
            }
            List<Integer> userIds = departmentUserService.lambdaQuery().in(DepartmentUserEntity::getDepartmentId, departmentIds)
                    .list().stream()
                    .map(DepartmentUserEntity::getUserId)
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(userIds)) {
                return new Page<>();
            }
            wrapper.in(SysUserEntity::getId, userIds);
        }
        // 部门id查询
        if (!CollectionUtils.isEmpty(dto.getDepartmentIds())) {
            List<Integer> userIds = departmentUserService.lambdaQuery().in(DepartmentUserEntity::getDepartmentId, dto.getDepartmentIds())
                    .list().stream()
                    .map(DepartmentUserEntity::getUserId)
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(userIds)) {
                return new Page<>();
            }
            wrapper.in(SysUserEntity::getId, userIds);
        }
        // 角色名称查询
        if (StringUtils.isNotBlank(dto.getRoleName()) || StringUtils.isNotBlank(dto.getRoleCode()) || !CollectionUtils.isEmpty(dto.getRoleNames())) {
            List<Integer> roleIds = roleService.lambdaQuery()
                    .like(StringUtils.isNotBlank(dto.getRoleName()), SysRoleEntity::getName, dto.getRoleName())
                    .like(StringUtils.isNotBlank(dto.getRoleCode()), SysRoleEntity::getRoleCode, dto.getRoleCode())
                    .in(!CollectionUtils.isEmpty(dto.getRoleNames()), SysRoleEntity::getName, dto.getRoleNames())
                    .list().stream()
                    .map(SysRoleEntity::getId)
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(roleIds)) {
                return new Page<>();
            }
            List<Integer> userIds = userRoleService.lambdaQuery().in(SysUserRoleEntity::getRoleId, roleIds)
                    .list().stream()
                    .map(SysUserRoleEntity::getUserId)
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(userIds)) {
                return new Page<>();
            }
            wrapper.in(SysUserEntity::getId, userIds);
        }

        wrapper.orderByDesc(SysUserEntity::getUpdateTime)
                .orderByDesc(SysUserEntity::getId);
        Page<SysUserEntity> userPage = this.page(new Page<>(current, size), wrapper);
        // 为提升查询效率，如果仅查询简单信息，直接返回
        if (Objects.nonNull(dto.getIsShowSimpleInfo()) && dto.getIsShowSimpleInfo()) {
            return userPage;
        }
        // 查询角色、岗位相关信息
        showRoleAndPostInfo(userPage.getRecords());
        return userPage;
    }

    @Override
    public SysUserEntity detailByConditions(SysUserDetailDTO dto) {
        Integer id = null;
        if (dto.getId() != null) {
            id = dto.getId();
        } else if (StringUtils.isNotBlank(dto.getUsername())) {
            SysUserEntity sysUserEntity = this.lambdaQuery().eq(SysUserEntity::getUsername, dto.getUsername()).one();
            if (sysUserEntity != null) {
                id = sysUserEntity.getId();
            }
        } else if (StringUtils.isNotBlank(dto.getNickname())) {
            SysUserEntity sysUserEntity = this.lambdaQuery().eq(SysUserEntity::getNickname, dto.getNickname()).one();
            if (sysUserEntity != null) {
                id = sysUserEntity.getId();
            }
        } else {
            ResponseException exception = ResponseException.of("用户名、用户中文名不能同时为空");
            exception.setCode(RespCodeEnum.PARAM_EXCEPTION.getMsgCode());
            throw exception;
        }
        if (id == null) {
            return null;
        }
        return selectById(id);
    }

    @Override
    public String getNicknameByUsername(String username) {
        if (StringUtils.isBlank(username)) {
            return null;
        }
        SysUserEntity sysUserEntity = this.lambdaQuery().select(SysUserEntity::getNickname).eq(SysUserEntity::getUsername, username).one();
//        SysUserEntity sysUserEntity = selectByUsername(username);
        if (!ObjectUtils.isEmpty(sysUserEntity)) {
            return sysUserEntity.getNickname();
        }
        return null;
    }

    @Override
    public Map<String, String> getUserNameNickMap(Collection<String> usernames) {
        List<SysUserEntity> sysUserEntities;
        if (CollectionUtils.isEmpty(usernames)) {
            sysUserEntities = this.selectList();
        } else {
            sysUserEntities = this.selectByUsernames(usernames.toArray(new String[1]));
        }
        return sysUserEntities.stream().collect(Collectors.toMap(SysUserEntity::getUsername, SysUserEntity::getNickname));
    }

    @Override
    public Map<String, SysUserEntity> getUserMap(Collection<String> usernames) {
        List<SysUserEntity> sysUserEntities;
        if (CollectionUtils.isEmpty(usernames)) {
            sysUserEntities = this.selectList();
        } else {
            sysUserEntities = this.selectByUsernames(usernames.toArray(new String[1]));
        }
        return sysUserEntities.stream().collect(Collectors.toMap(SysUserEntity::getUsername, v -> v));
    }

    @Override
    public Map<String, String> getUserNameNickMap2(Collection<String> usernames) {
        if(CollectionUtils.isEmpty(usernames)) {
            return Collections.emptyMap();
        }
        return getUserNameNickMap(usernames);
    }

    @Override
    public String getNicknamesByUsernames(List<String> usernames) {
        if (CollectionUtils.isEmpty(usernames)) {
            return "";
        }
        String[] usernamesArray = usernames.toArray(new String[0]);
        List<SysUserEntity> sysUserEntities = selectByUsernames(usernamesArray);
        return sysUserEntities.stream().map(SysUserEntity::getNickname).collect(Collectors.joining(Constant.SEP));
    }

    @Override
    public SysUserEntity getOneByNickname(String nickname) {
        return this.lambdaQuery().eq(SysUserEntity::getNickname, nickname).last("limit 1").one();
    }

    @Override
    public Page<SysUserEntity> selectUserListByRoleCode(String roleCode, int current, int size) {
        SysRoleEntity roleEntity = roleService.lambdaQuery().eq(SysRoleEntity::getRoleCode, roleCode).one();
        if (Objects.isNull(roleEntity)) {
            return new Page<>();
        }
        List<Integer> userIds = userRoleService.lambdaQuery()
                .eq(SysUserRoleEntity::getEnabled, EnabledEnum.ENABLE.getCode())
                .eq(SysUserRoleEntity::getRoleId, roleEntity.getId())
                .list().stream()
                .map(SysUserRoleEntity::getUserId).distinct()
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(userIds)) {
            return new Page<>();
        }
        return this.lambdaQuery().in(SysUserEntity::getId, userIds)
                .eq(SysUserEntity::getIsDelete, false)
                .page(new Page<>(current, size));

    }

    @Override
    public SysUserEntity selectUserArea(String username) {
        SysUserEntity userEntity = this.lambdaQuery().eq(SysUserEntity::getUsername, username).one();
        if (Objects.isNull(userEntity)) {
            return null;
        }
        LambdaQueryWrapper<AreaEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AreaEntity::getCid, userEntity.getCompId());
        AreaEntity areaEntity = areaMapper.selectOne(wrapper);
        userEntity.setAid(areaEntity.getAid());
        userEntity.setAname(areaEntity.getAname());
        userEntity.setAcode(areaEntity.getAcode());
        return userEntity;
    }

    @Override
    public Page<SysUserEntity> selectAllUser(String nickname, Integer current, Integer size) {
        List<SysUserEntity> list = this.lambdaQuery().like(StringUtils.isNotBlank(nickname), SysUserEntity::getNickname, nickname)
                .eq(SysUserEntity::getIsDelete, false)
                .list();
        list.forEach(o -> {
            o.setPassword(null);
            o.setKeycloakId(null);
            o.setMobile(null);
        });
        Page<SysUserEntity> page = new Page<>();
        if (current == null || size == null) {
            page.setRecords(list).setTotal(list.size()).setCurrent(1).setSize(list.size());
        } else {
            page.setRecords(list).setTotal(list.size()).setCurrent(current).setSize(size);
        }
        return page;
    }

    @Override
    public List<SysUserEntity> selectListByIdList(List<Integer> ids) {
        return this.lambdaQuery().in(!CollectionUtils.isEmpty(ids), SysUserEntity::getId, ids).list();
    }

    @Override
    public List<SysUserEntity> getListByUserNames(Collection<String> userNames) {
        if (CollectionUtils.isEmpty(userNames)) {
            return new ArrayList<>();
        }
        return this.lambdaQuery().in(SysUserEntity::getUsername, userNames).list();
    }

    @Override
    public List<SysUserEntity> getListByNickNames(List<String> nickNames) {
        if (CollectionUtils.isEmpty(nickNames)) {
            return new ArrayList<>();
        }
        return this.lambdaQuery().in(SysUserEntity::getNickname, nickNames).list();
    }

    /**
     * 查询用户列表及其角色
     *
     * @return
     */
    @Override
    public List<UserVo> getListWithRole() {
        List<UserVo> userVos = new ArrayList<>();
        // 找到系统中存在角色的启用状态的用户
        List<Integer> roleIds = userRoleService.list().stream().map(SysUserRoleEntity::getRoleId).distinct().collect(Collectors.toList());
        for (Integer roleId : roleIds) {
            SysRoleEntity roleEntity = roleService.getById(roleId);
            if (Objects.isNull(roleEntity)) {
                continue;
            }
            List<Integer> userIds = userRoleService.lambdaQuery().eq(SysUserRoleEntity::getRoleId, roleId)
                    .eq(SysUserRoleEntity::getEnabled, EnabledEnum.ENABLE.getCode())
                    .list().stream()
                    .map(SysUserRoleEntity::getUserId).distinct()
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(userIds)) {
                continue;
            }
            List<SysUserEntity> userEntities = userService.listByIds(userIds);
            userVos.add(UserVo.builder().id(roleId).nickname(roleEntity.getName()).users(userEntities).build());
        }
        return userVos;
    }

    @Override
    public SysUserEntity selectByUsername(String userName) {
        return this.lambdaQuery().eq(SysUserEntity::getUsername, userName).one();
    }

    @Override
    public void adminResetPassword(String thisUser, ResetPasswordDTO resetPasswordDTO) {
        // 通过用户找角色
        List<SysRoleEntity> thisRoleEntities = roleService.selectByUsername(thisUser);
        boolean isAdmin = thisRoleEntities.stream().anyMatch(o -> o.getId().equals(1));
        if (!isAdmin) {
            throw new ResponseException("非管理员用户，无法重置密码");
        }
        List<SysRoleEntity> userRoleEntities = roleService.selectByUsername(resetPasswordDTO.getUserAccount());
        boolean isUserAdmin = userRoleEntities.stream().anyMatch(o -> o.getId().equals(Constant.ONE));
        if (isUserAdmin) {
            throw new ResponseException("无法重置管理员用户密码");
        }
        boolean isSysAdmin = userRoleEntities.stream().filter(o -> o.getRoleCode() != null).anyMatch(o -> o.getRoleCode().equals(Constant.ONCALL_CODE));
        if (isSysAdmin) {
            throw new ResponseException("无法重置系统管理员用户密码");
        }
        BCryptPasswordEncoder bcryptPasswordEncoder = new BCryptPasswordEncoder();
        this.lambdaUpdate().eq(SysUserEntity::getUsername, resetPasswordDTO.getUserAccount()).set(SysUserEntity::getPassword, bcryptPasswordEncoder.encode(resetPasswordDTO.getNewPassword())).update();
        if (KEY_CLOAK.equals(workPropertise.getOauthType())) {
            SysUserEntity sysUserEntity = this.lambdaQuery().eq(SysUserEntity::getUsername, resetPasswordDTO.getUserAccount()).one();
            boolean success = appsService.resetPassword(resetPasswordDTO.getNewPassword(), sysUserEntity.getKeycloakId());
            // 调用精制修改密码失败 直接调用keycloak
            if (!success) {
                keyCloakService.resetPassword(sysUserEntity, resetPasswordDTO.getNewPassword());
            }
        }
    }

    @Override
    public List<SysUserEntity> listKeyword(String nickname) {
        List<SysUserEntity> result = this.lambdaQuery().like(StringUtils.isNotEmpty(nickname), SysUserEntity::getNickname, nickname)
                .eq(SysUserEntity::getIsDelete, false)
                .list();
        result.forEach(o -> {
            o.setPassword(null);
            o.setKeycloakId(null);
            o.setMobile(null);
        });
        return result;

    }

    @Override
    public PrintDTO print(SysUserPrintSelectDTO selectDTO) {
        List<PrintDTO.ParamDto> printDTOS = new ArrayList<>();
        List<String> usernames = new ArrayList<>();
        Integer current = selectDTO.getCurrent();
        Integer size = selectDTO.getSize();
        // 必填项如果未填，则直接返回，防止前端误传导致CPU飙高、以及未打印的数据显示已打印
        boolean pageParams = Objects.isNull(current) || Objects.isNull(size);
        if (StringUtils.isBlank(selectDTO.getUserNames()) && pageParams) {
            throw new ResponseException(RespCodeEnum.PRINT_IS_REQUIRE);
        }
        if (StringUtils.isNotBlank(selectDTO.getUserNames())) {
            usernames = Arrays.asList(selectDTO.getUserNames().split(Constant.SEP));
        }
        current = Objects.isNull(current) ? 1 : current;
        size = Objects.isNull(size) ? 10 : size;
        Page<SysUserEntity> page = selectPageNotIncludeYelinkOncall(selectDTO.getUsername(), selectDTO.getNickname(), selectDTO.getRoleName(), null, null, new Page<>(current, size),
                null, null, null, null, selectDTO.getDepartmentName(), usernames, null, selectDTO.getRoleNames(), null);
        List<SysUserEntity> records = page.getRecords();

        // 新版本通过前端选择的标签规则进行打印
        LabelEntity labelEntity = labelService.lambdaQuery().eq(LabelEntity::getRuleId, selectDTO.getRuleId()).one();
        if (labelEntity == null) {
            throw new ResponseException(RespCodeEnum.NOT_FIND_BAR_CODE_RULE);
        }

        LabelTypeConfigEntity labelTypeConfigEntity = labelTypeConfigService.detail(labelEntity.getCodeType());
        BarCodeAnalysisService barCodeAnalysisService = SpringUtil.getBean(BarCodeAnalysisService.class);
        // 每次打印，序号初始化为1
        int i = 1;
        // 获取打印模板, 替换打印模板里${ }的数据
        for (SysUserEntity userEntity : records) {
            PrintDTO printDTO = JSON.parseObject(labelEntity.getContent(), PrintDTO.class);
            List<PrintDTO.ParamDto.Content> printElements = printDTO.getPanels().get(0).getPrintElements();
            for (PrintDTO.ParamDto.Content content : printElements) {
                String data = content.getOptions().getTitle();
                if (StringUtils.isBlank(data)) {
                    continue;
                }
                // 替换占位符, 替换值为查询对象的某个属性值
                Matcher matcher = PATTERN.matcher(data);
                while (matcher.find()) {
                    String placeholder = matcher.group();
                    // 序号加1
                    if (placeholder.equals(LabelInfoEnum.SERIAL_NO.getPlaceholder())) {
                        data = String.valueOf(i);
                        i++;
                        continue;
                    }
                    CodeInfoSelectDTO build = CodeInfoSelectDTO.builder()
                            .ruleType(labelEntity.getCodeType())
                            .relateType(labelTypeConfigEntity.getRelateType())
                            .username(userEntity.getUsername())
                            .placeholder(placeholder)
                            .sliceDigits(content.getOptions().getSliceDigits())
                            .build();
                    // 判断该标签是否存在公式，如果存在运算公式，将对数据进行四则运算
                    // 如果公式为空，则直接进行真实值替换占位符的逻辑
                    String placeholderValue = barCodeAnalysisService.replacePlaceholder(content, build);
                    data = data.replace(placeholder, placeholderValue);
                }
                content.getOptions().setTitle(data);
                // 字体大小如果为空,则设置默认值
                if (content.getOptions().getFontSize() == null) {
                    content.getOptions().setFontSize(9.0);
                }
            }
            printDTOS.addAll(printDTO.getPanels());
        }
        List<String> userNames = records.stream().map(SysUserEntity::getUsername).collect(Collectors.toList());
        PrintDTO printDTO = PrintDTO.builder().ruleType(labelEntity.getCodeType()).ruleCode(labelEntity.getRuleCode()).panels(printDTOS).build();
        PrintDTO dto = labelService.getPrintDtoByOpenApi(printDTO);
        // 记录打印的编号
        dto.setPrintCodes(StringUtils.join(userNames, Constant.SEP));
        return dto;
    }

    @Override
    public SysUserEntity selectTempUser(String userName) {
        // 查询临时用户，查询不到就新增一个
        SysUserEntity entity = selectByUsername(userName);
        if (entity != null) {
            return entity;
        }
        SysUserEntity adminEntity = selectByUsername(Constant.ADMIN);
        SysRoleEntity sysRoleEntity = roleService.lambdaQuery().eq(SysRoleEntity::getRoleCode, Constant.TEMP_USER).one();
        String roleIds = sysRoleEntity.getId().toString();
        entity = SysUserEntity.builder().username(userName).nickname(userName)
                .roleId(roleIds).createBy(Constant.ADMIN).updateBy(Constant.ADMIN).compId(adminEntity.getCompId()).build();
        String token = ExtApiUtil.getTokenBasedOnContext();
        userService.add(entity, roleIds, Constant.ADMIN, token);
        return entity;
    }

    @Override
    public SysUserEntity getUserById(Integer userId) {
        SysUserEntity sysUserEntity = this.getById(userId);
        if (Objects.isNull(sysUserEntity)) {
            return null;
        }
        showRoleAndPostInfo(Stream.of(sysUserEntity).collect(Collectors.toList()));
        return sysUserEntity;
    }

    @Override
    public Page<SysUserEntity> selectList(UserSelectDTO selectDTO) {
        return selectPageNotIncludeYelinkOncall(selectDTO.getUsername(), selectDTO.getNickname(), selectDTO.getRoleName(), selectDTO.getKey(), selectDTO.getSort()
                , new Page<>(selectDTO.getCurrent(), selectDTO.getSize()), selectDTO.getMobile(), selectDTO.getEmail(), selectDTO.getJobNumber(),
                selectDTO.getPost(), selectDTO.getDepartmentName(), null, selectDTO.getUserState(), selectDTO.getRoleNames(), selectDTO.getUserIds());
    }

    @Override
    public void updateEntity(SysUserEntity userEntity) {
        this.lambdaUpdate().eq(SysUserEntity::getUsername, userEntity.getUsername())
                .set(SysUserEntity::getHeaderUrl, userEntity.getHeaderUrl())
                .set(SysUserEntity::getMobile, userEntity.getMobile())
                .set(SysUserEntity::getPassword, userEntity.getPassword())
                .set(SysUserEntity::getEmail, userEntity.getEmail())
                .set(SysUserEntity::getIdCard, userEntity.getIdCard())
                .set(SysUserEntity::getJobNumber, userEntity.getJobNumber())
                .set(SysUserEntity::getNickname, userEntity.getNickname())
                .set(SysUserEntity::getUpdateBy, userEntity.getUpdateBy())
                .set(SysUserEntity::getAge, userEntity.getAge())
                .set(SysUserEntity::getSex, userEntity.getSex())
                .set(SysUserEntity::getSkillLevel, userEntity.getSkillLevel())
                .set(SysUserEntity::getWechat, userEntity.getWechat())
                .set(SysUserEntity::getDepartmentId, userEntity.getDepartmentId())
                .set(SysUserEntity::getEntryDate, userEntity.getEntryDate())
                .set(SysUserEntity::getPost, userEntity.getPost())
                .set(SysUserEntity::getDepartmentName, userEntity.getDepartmentName())
                .set(SysUserEntity::getKeycloakId, userEntity.getKeycloakId())
                .set(SysUserEntity::getImUserName, userEntity.getImUserName())
                .update();
    }

    @Override
    public String getSignatureUrlByUsername(String username) {
        return userSignatureService.getSignatureUrlByUsername(username);
    }

    @Override
    public Map<String, String> getSignatureUrlMap(List<String> usernames) {
        return userSignatureService.getSignatureUrlMap(usernames);
    }

    @Override
    public SysUserEntity getUserByUsername(String inspector) {
        if (StringUtils.isEmpty(inspector)) {
            return null;
        }
        return this.lambdaQuery().eq(SysUserEntity::getUsername, inspector).one();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void upsert(SysUserEntity userEntity, String handelName) {
        setDepartmentId(userEntity);
        setRoleId(userEntity);
        SysUserEntity one = this.lambdaQuery().eq(SysUserEntity::getUsername, userEntity.getUsername()).one();
        String token = ExtApiUtil.getTokenBasedOnContext();
        if (one == null) {
            //新增
            SysUserEntity adminEntity = this.selectByUsername(Constant.ADMIN);
            userEntity.setCompId(adminEntity.getCompId());
            add(userEntity, userEntity.getRoleId(), handelName, token);
        } else {
            //更新
            FieldUtil.copyValueTo(one, userEntity);
            update(userEntity, token);
        }
    }

    @Override
    public List<UserBindProductBasicUnitVO> getRoleBindProductBasicUnitList(String username) {
        SysUserEntity userEntity = this.lambdaQuery()
                .select(SysUserEntity::getId, SysUserEntity::getUsername, SysUserEntity::getNickname)
                .eq(SysUserEntity::getUsername, username).one();
        if (Objects.isNull(userEntity)) {
            return new ArrayList<>();
        }
        // 获取用户关联的角色列表
        List<SysUserRoleEntity> userRoleEntities = userRoleService.lambdaQuery()
                .eq(SysUserRoleEntity::getUserId, userEntity.getId())
                .eq(SysUserRoleEntity::getEnabled, EnabledEnum.ENABLE.getCode())
                .list();
        List<Integer> roleIds = userRoleEntities.stream().map(SysUserRoleEntity::getRoleId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(roleIds)) {
            return new ArrayList<>();
        }
        List<UserBindProductBasicUnitVO> list = new ArrayList<>();
        // 获取角色关联的生产基本单元列表
        List<SysRoleProductionBasicUnitEntity> allBasicUnitEntities = roleProductionBasicUnitService.lambdaQuery().in(SysRoleProductionBasicUnitEntity::getRoleId, roleIds).list();
        if (CollectionUtils.isEmpty(allBasicUnitEntities)) {
            return new ArrayList<>();
        }
        List<Integer> workCenterIds = allBasicUnitEntities.stream().map(SysRoleProductionBasicUnitEntity::getWorkCenterId).collect(Collectors.toList());
        Map<Integer, WorkCenterVO> workCenterVOMap = workCenterService.listByIds(workCenterIds).stream().map(workCenterEntity -> WorkCenterVO.builder()
                .workCenterId(workCenterEntity.getId())
                .workCenterCode(workCenterEntity.getCode())
                .workCenterName(workCenterEntity.getName())
                .workCenterType(workCenterEntity.getType())
                .build()
        ).collect(Collectors.toList()).stream().collect(Collectors.toMap(WorkCenterVO::getWorkCenterId, o -> o));

        Map<Integer, List<SysRoleProductionBasicUnitEntity>> roleMap = allBasicUnitEntities.stream().collect(Collectors.groupingBy(SysRoleProductionBasicUnitEntity::getRoleId));
        for (Map.Entry<Integer, List<SysRoleProductionBasicUnitEntity>> entry : roleMap.entrySet()) {
            Integer roleId = entry.getKey();
            List<SysRoleProductionBasicUnitEntity> basicUnitEntities = entry.getValue();
            Map<Integer, List<SysRoleProductionBasicUnitEntity>> productBasicMap = basicUnitEntities.stream().collect(Collectors.groupingBy(SysRoleProductionBasicUnitEntity::getWorkCenterId));
            // 查询角色关联的工作中心列表
            List<Integer> workCentersByRoleId = basicUnitEntities.stream().map(SysRoleProductionBasicUnitEntity::getWorkCenterId).distinct().collect(Collectors.toList());
            List<WorkCenterVO> workCenterVOList = workCentersByRoleId.stream().map(workCenterVOMap::get).collect(Collectors.toList());
            // 查询工作中心关联的生产基本单元列表
            for (WorkCenterVO workCenterVO : workCenterVOList) {
                WorkCenterTypeEnum typeEnum = WorkCenterTypeEnum.getByCode(workCenterVO.getWorkCenterType());
                List<ProductBasicUnitVO> basicUnitVOList = new ArrayList<>();
                List<Integer> productBasicUnitIds = productBasicMap.get(workCenterVO.getWorkCenterId()).stream().map(SysRoleProductionBasicUnitEntity::getProductionBasicUnitId).collect(Collectors.toList());
                switch (typeEnum) {
                    case LINE:
                        basicUnitVOList = productionLineService.listByIds(productBasicUnitIds)
                                .stream().map(productionLineEntity -> ProductBasicUnitVO.builder()
                                        .productBasicUnitId(productionLineEntity.getProductionLineId())
                                        .productBasicUnitCode(productionLineEntity.getProductionLineCode())
                                        .productBasicUnitName(productionLineEntity.getName())
                                        .build()).collect(Collectors.toList());
                        break;
                    case TEAM:
                        basicUnitVOList = teamService.listByIds(productBasicUnitIds)
                                .stream().map(teamEntity -> ProductBasicUnitVO.builder()
                                        .productBasicUnitId(teamEntity.getId())
                                        .productBasicUnitCode(teamEntity.getTeamCode())
                                        .productBasicUnitName(teamEntity.getTeamName())
                                        .build()).collect(Collectors.toList());
                        break;
                    case DEVICE:
                        basicUnitVOList = deviceService.listByIds(productBasicUnitIds)
                                .stream().map(deviceEntity -> ProductBasicUnitVO.builder()
                                        .productBasicUnitId(deviceEntity.getDeviceId())
                                        .productBasicUnitCode(deviceEntity.getDeviceCode())
                                        .productBasicUnitName(deviceEntity.getDeviceName())
                                        .build()).collect(Collectors.toList());
                        break;
                    default:
                }
                workCenterVO.setProductBasicUnitList(basicUnitVOList);
            }
            list.add(UserBindProductBasicUnitVO.builder()
                    .username(username)
                    .nickname(userEntity.getNickname())
                    .roleId(roleId)
                    .workCenterVOS(workCenterVOList)
                    .build());
        }
        return list;
    }

    @Override
    public void deleteUser(String username) {
        // 不能删除内置用户(admin和yelinkoncall)
        if (username.equals(Constant.ADMIN) || username.equals(Constant.YELINK_ONCALL)) {
            throw new ResponseException(RespCodeEnum.CANNOT_DELETE_INNER_USER.getMsgDes());
        }
        // 软删除用户
        SysUserEntity userEntity = this.lambdaQuery().eq(SysUserEntity::getUsername, username).one();
        if (Objects.isNull(userEntity)) {
            return;
        }
        this.lambdaUpdate().eq(SysUserEntity::getUsername, username)
                .set(SysUserEntity::getIsDelete, true)
                .update();
        // 该用户改为禁用
        userService.updateDisableById(Constant.DISABLE, String.valueOf(userEntity.getId()), userAuthenService.getUsername());
    }

    /**
     * 通过部门编码、部门名称查询部门id，逗号连接
     *
     * @param userEntity
     */
    private void setDepartmentId(SysUserEntity userEntity) {
        String departmentCode = userEntity.getDepartmentCode();
        String departmentName = userEntity.getDepartmentName();
        if (StringUtils.isAllBlank(departmentCode, departmentName)) {
            return;
        }

        LambdaQueryWrapper<DepartmentEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.select(DepartmentEntity::getDepartmentId, DepartmentEntity::getPid);

        if (StringUtils.isNotBlank(departmentCode)) {
            wrapper.in(DepartmentEntity::getDepartmentCode, departmentCode.split(Constant.BACKSLASH));
        } else if (StringUtils.isNotBlank(departmentName)) {
            wrapper.in(DepartmentEntity::getDepartmentName, departmentName.split(Constant.BACKSLASH));
        }

        List<DepartmentEntity> list = departmentService.list(wrapper);
        String departmentIds = list.stream().map(o -> String.valueOf(o.getDepartmentId())).collect(Collectors.joining(Constant.SEP));

        if (StringUtils.isNotBlank(departmentIds)) {
            userEntity.setDepartmentId(departmentIds);
        }
    }

    /**
     * 通过权限编码、权限名称查询权限id，逗号连接
     *
     * @param userEntity
     */
    private void setRoleId(SysUserEntity userEntity) {
        String roleCode = userEntity.getRoleCode();
        String roleName = userEntity.getRoleName();
        if (StringUtils.isAllBlank(roleCode, roleName)) {
            return;
        }

        LambdaQueryWrapper<SysRoleEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.select(SysRoleEntity::getId);

        if (StringUtils.isNotBlank(roleCode)) {
            wrapper.in(SysRoleEntity::getRoleCode, roleCode.split(Constant.SEP));
        } else if (StringUtils.isNotBlank(roleName)) {
            wrapper.in(SysRoleEntity::getName, roleName.split(Constant.SEP));
        }

        String roleIds = roleService.list(wrapper)
                .stream().map(o -> String.valueOf(o.getId()))
                .collect(Collectors.joining(Constant.SEP));
        if (StringUtils.isNotBlank(roleIds)) {
            userEntity.setRoleId(roleIds);
        }
    }

    @Override
    public Page<SysUserEntity> getList2(UserQueryDTO dto) {
        String sql = SqlUtil.getSql(dto);
        return this.baseMapper.getList(sql, dto.getPage());
    }

}
