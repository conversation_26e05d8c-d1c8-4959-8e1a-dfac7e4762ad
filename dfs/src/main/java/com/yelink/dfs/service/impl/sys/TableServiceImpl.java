package com.yelink.dfs.service.impl.sys;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yelink.dfs.constant.Constant;
import com.yelink.dfs.constant.RedisKeyPrefix;
import com.yelink.dfs.constant.TableEnum;
import com.yelink.dfs.constant.sys.TableFieldTypeEnum;
import com.yelink.dfs.constant.sys.TableIndexTypeEnum;
import com.yelink.dfs.constant.target.TargetModelDataSourceTypeEnum;
import com.yelink.dfs.entity.common.TableEntity;
import com.yelink.dfs.entity.common.TableIndexEntity;
import com.yelink.dfs.entity.sys.dto.ReturnSqlDTO;
import com.yelink.dfs.entity.sys.dto.SelectSqlDTO;
import com.yelink.dfs.entity.sys.dto.TableCreateOrUpdateDTO;
import com.yelink.dfs.entity.sys.dto.TableDeleteDTO;
import com.yelink.dfs.entity.sys.dto.TableFieldDTO;
import com.yelink.dfs.entity.sys.dto.TableIndexDTO;
import com.yelink.dfs.entity.sys.dto.TableSelectDTO;
import com.yelink.dfs.entity.sys.vo.TableColumnSchemaHelper;
import com.yelink.dfs.mapper.sys.TableMapper;
import com.yelink.dfs.service.impl.common.WorkPropertise;
import com.yelink.dfs.service.sys.TableIndexService;
import com.yelink.dfs.service.sys.TableService;
import com.yelink.dfscommon.constant.Constants;
import com.yelink.dfscommon.constant.InputTypeEnum;
import com.yelink.dfscommon.constant.RespCodeEnum;
import com.yelink.dfscommon.constant.ResponseException;
import com.yelink.dfscommon.dto.CommonTableDTO;
import com.yelink.dfscommon.dto.dfs.TargetFieldOptionDTO;
import com.yelink.dfscommon.utils.DateUtil;
import com.yelink.dfscommon.utils.JacksonUtil;
import com.yelink.metrics.api.dto.TableColumnDTO;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.session.SqlSession;
import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.sql.ResultSetMetaData;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2021/4/25
 */
@Service
@AllArgsConstructor
@Slf4j
public class TableServiceImpl extends ServiceImpl<TableMapper, TableEntity> implements TableService {

    private TableMapper tableMapper;
    private WorkPropertise workPropertise;
    private TableCleanAsyncMethod tableCleanAsyncMethod;
    private RedisTemplate<String, Object> redisTemplate;
    private TableIndexService tableIndexService;
    private SqlSessionFactory sqlSessionFactory;

    @Override
    public List<CommonTableDTO> getAllDfsTable() {
        return tableMapper.getAllDfsTable(workPropertise.getTableSchema());
    }


    @Override
    public void cleanDfsData(String tableName, String time) {
        String key = RedisKeyPrefix.TABLE_DATA_CLEAN_ + tableName;
        // 不存在，则缓存到redis中
        Boolean insert = redisTemplate.opsForValue().setIfAbsent(key, 0, 10, TimeUnit.MINUTES);
        if (insert == null || !insert) {
            throw new ResponseException(RespCodeEnum.TABLE_DATA_CLEANING);
        }
        tableCleanAsyncMethod.cleanTable(tableName, time, key);
    }

    @Override
    public Page<TableEntity> getTableConfList(TableSelectDTO selectDTO) {
        Page<TableEntity> page = selectDTO.buildPage();
        LambdaQueryWrapper<TableEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StringUtils.isNotBlank(selectDTO.getTableSchema()), TableEntity::getTableSchema, selectDTO.getTableSchema())
                .eq(StringUtils.isNotBlank(selectDTO.getTableName()), TableEntity::getTableName, selectDTO.getTableName())
                .eq(StringUtils.isNotBlank(selectDTO.getFieldCode()), TableEntity::getFieldCode, selectDTO.getFieldCode())
                .eq(StringUtils.isNotBlank(selectDTO.getFieldName()), TableEntity::getFieldName, selectDTO.getFieldName())
        ;
        return this.page(page, wrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createTableStructure(TableCreateOrUpdateDTO createDto) {
        // 判断所属的表是否存在于该数据库中
        Boolean exist = tableMapper.checkTableIsExist(createDto.getTableSchema(), createDto.getTableName());
        if (exist) {
            throw new ResponseException(RespCodeEnum.TABLE_EXIST);
        }
        // 组成能够让mysql识别的建表语句
        for (TableFieldDTO field : createDto.getFields()) {
            String fieldSql = getFieldSql(field);
            field.setFieldSql(fieldSql);
        }
        // 创建表
        createDto.setTableRemark(StringUtils.isNotBlank(createDto.getTableRemark()) ? createDto.getTableRemark() : "");
        tableMapper.createTable(createDto);
    }

    /**
     * 添加表结构定义
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveTableConf(TableCreateOrUpdateDTO createDto) {
        List<TableEntity> tableEntities = createDto.getFields()
                .stream().map(field ->
                        TableEntity.builder()
                                .tableSchema(createDto.getTableSchema())
                                .tableName(createDto.getTableName())
                                .fieldCode(field.getFieldCode())
                                .fieldName(field.getFieldName())
                                .fieldType(field.getFieldType())
                                .fieldLength(field.getFieldLength())
                                .fieldPoint(field.getFieldPoint())
                                .fieldRemark(field.getFieldRemark())
                                .fieldDefaultValue(field.getFieldDefaultValue())
                                .fieldMapping(Boolean.TRUE.equals(createDto.getSmartCal()) ? field.calFieldMapping() : null)
                                .fieldDefine(field.getFieldDefine())
                                .tableRemark(createDto.getTableRemark())
                                .build())
                .collect(Collectors.toList());
        this.saveBatch(tableEntities);
    }

    @Override
    public List<LinkedHashMap<String, Object>> dataPreview(SelectSqlDTO dto) {
        String sql = dto.getSql();
        if (StringUtils.isBlank(sql)) {
            throw new ResponseException("脚本不能为空");
        }
        // 检查是否以SELECT开头
        if (!sql.trim().toUpperCase().startsWith("SELECT")) {
            throw new ResponseException(RespCodeEnum.SQL_IS_NOT_VALID);
        }
        // 正则表达式用于检测高危关键字
        String dangerousKeywordsRegex = "(?i)(?:\\bDELETE\\b|\\bINSERT\\b|\\bUPDATE\\b|\\bTRUNCATE\\b|\\bDROP\\b|\\bCREATE\\b)";
        Pattern pattern = Pattern.compile(dangerousKeywordsRegex, Pattern.CASE_INSENSITIVE);
        if (pattern.matcher(sql).find()) {
            throw new ResponseException(RespCodeEnum.SQL_IS_NOT_VALID);
        }
        // 执行查询sql
        try {
            // 校验语句合法性需要加上limit语句,考虑到语句有人会加分号，有人不加分号结尾，这里兼容这两种情况
            if (sql.endsWith(";")) {
                sql = sql.substring(0, sql.length() - 1);
            }
            // 预览数据需要加上limit语句，避免性能问题
            if (!sql.toLowerCase().contains("limit")) {
                sql += " limit 5";
            }
            sql = formatTriggerTime(sql, null);
            // 获取表头信息
            List<String> headers = new ArrayList<>();
            SqlSession session = sqlSessionFactory.openSession();
            List<LinkedHashMap<String, Object>> result = tableMapper.executeSql(sql);
            if (!result.isEmpty()) {
                formatterQueryR(result);
                return result;
            } else {
                // 如果结果为空，使用 ResultSetMetaData 获取表头
                Statement stmt = session.getConnection().createStatement();
                ResultSetMetaData metaData = stmt.executeQuery(sql).getMetaData();
                int columnCount = metaData.getColumnCount();
                for (int i = 1; i <= columnCount; i++) {
                    headers.add(metaData.getColumnLabel(i));
                }
                // headers解析成 List<Map<String, Object>> 列表
                List<LinkedHashMap<String, Object>> list = new ArrayList<>();
                LinkedHashMap<String, Object> map = new LinkedHashMap<>();
                for (String header : headers) {
                    map.put(header, null);
                }
                list.add(map);
                return list;
            }
        } catch (Exception e) {
            log.error("sql数据解析错误{}", e.getMessage());
            throw new ResponseException(RespCodeEnum.SQL_IS_NOT_VALID);
        }
    }

    @Override
    public void setFieldValue(TargetFieldOptionDTO dto) {
        // 自定义输入
        if (dto.getType().equals(InputTypeEnum.INPUT.getType())) {
            this.lambdaUpdate().eq(TableEntity::getId, dto.getId())
                    .set(TableEntity::getInputType, dto.getInputType())
                    .set(TableEntity::getOptionValuesType, dto.getType())
                    .update();
            return;
        }
        // 枚举
        // 表单选项值字段校验
        fieldJudge(dto);
        // 根据显示顺序排列数据
        List<TargetFieldOptionDTO.EnumDTO> orderEnums = dto.getEnums().stream()
                .sorted(Comparator.comparing(TargetFieldOptionDTO.EnumDTO::getShowOrder))
                .collect(Collectors.toList());

        // 如果为枚举，转换为json存入数据库
        String enums = JSON.toJSONString(orderEnums);
        this.lambdaUpdate().eq(TableEntity::getId, dto.getId())
                .set(TableEntity::getInputType, dto.getInputType())
                .set(TableEntity::getOptionValuesType, dto.getType())
                .set(TableEntity::getAllOption, enums)
                .update();
    }

    @Override
    public TargetFieldOptionDTO getFieldValueDetail(Integer id) {
        TableEntity tableEntity = this.getById(id);
        List<TargetFieldOptionDTO.EnumDTO> enums = null;
        if (StringUtils.isNotBlank(tableEntity.getOptionValuesType()) && tableEntity.getOptionValuesType().equals(Constants.TABLE)) {
            enums = JSON.parseArray(tableEntity.getAllOption(), TargetFieldOptionDTO.EnumDTO.class);
        }
        return TargetFieldOptionDTO.builder()
                .id(id)
                .type(tableEntity.getOptionValuesType())
                .inputType(tableEntity.getInputType())
                .enums(enums)
                .build();
    }

    /**
     * 表单选项值字段校验
     */
    private void fieldJudge(TargetFieldOptionDTO optionDTO) {
        // 编码和名称都不能重复
        boolean valueHasDuplicates = optionDTO.getEnums().stream()
                .collect(Collectors.groupingBy(TargetFieldOptionDTO.EnumDTO::getValue, Collectors.counting()))
                .values().stream().anyMatch(duplicatesCount -> duplicatesCount > 1);
        if (valueHasDuplicates) {
            throw new ResponseException(RespCodeEnum.OPTION_VALUE_VALUE_IS_DUPLICATES);
        }
        boolean nameHasDuplicates = optionDTO.getEnums().stream()
                .collect(Collectors.groupingBy(TargetFieldOptionDTO.EnumDTO::getLabel, Collectors.counting()))
                .values().stream().anyMatch(duplicatesCount -> duplicatesCount > 1);
        if (nameHasDuplicates) {
            throw new ResponseException(RespCodeEnum.OPTION_VALUE_LABEL_IS_DUPLICATES);
        }
        // 顺序不能为空
        long count = optionDTO.getEnums().stream().filter(o -> Objects.isNull(o.getShowOrder())).count();
        if (count > 0) {
            throw new ResponseException(RespCodeEnum.OPTION_VALUE_ORDER_NOT_NULL);
        }
        // 顺序不能相同
        boolean orderHasDuplicates = optionDTO.getEnums().stream()
                .collect(Collectors.groupingBy(TargetFieldOptionDTO.EnumDTO::getShowOrder, Collectors.counting()))
                .values().stream().anyMatch(duplicatesCount -> duplicatesCount > 1);
        if (orderHasDuplicates) {
            throw new ResponseException(RespCodeEnum.OPTION_VALUE_ORDER_IS_DUPLICATES);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateTableStructure(TableCreateOrUpdateDTO dto) {
        if (CollectionUtils.isEmpty(dto.getFields())) {
            throw new ResponseException(RespCodeEnum.TABLE_FIELD_EMPTY);
        }
        List<TableEntity> historyFieldList = this.lambdaQuery().eq(TableEntity::getTableSchema, dto.getTableSchema())
                .eq(TableEntity::getTableName, dto.getTableName())
                .list();
        // 获取需要新增、更新、删除的字段
        List<TableFieldDTO> newFields = dto.getFields().stream().filter(field -> Objects.isNull(field.getId())).collect(Collectors.toList());
        List<TableFieldDTO> updateFields = dto.getFields().stream().filter(field -> Objects.nonNull(field.getId())).collect(Collectors.toList());

        List<Integer> updateFieldIds = updateFields.stream().map(TableFieldDTO::getId).collect(Collectors.toList());
        List<String> deleteFieldCodes = historyFieldList.stream()
                .filter(field -> !updateFieldIds.contains(field.getId()))
                .map(TableEntity::getFieldCode)
                .filter(fieldCode -> dto.getFields().stream().noneMatch(field -> field.getFieldCode().equals(fieldCode))
                ).collect(Collectors.toList());

        // 更新列
        Map<Integer, String> map = historyFieldList.stream().collect(Collectors.toMap(TableEntity::getId, TableEntity::getFieldCode));
        for (TableFieldDTO updateField : updateFields) {
            try {
                String fieldSql = getFieldSql(updateField);
                // 获取旧列名
                tableMapper.updateColumn(dto.getTableSchema(), dto.getTableName(), map.get(updateField.getId()), fieldSql);
            } catch (Exception e) {
                if (e.getMessage().contains("Data truncation")) {
                    throw new ResponseException(RespCodeEnum.DATA_OUT_OF_RANGE.fmtDes(updateField.getFieldCode(), updateField.getFieldType()));
                }
            }
        }
        // 新增列
        for (TableFieldDTO newField : newFields) {
            String fieldSql = getFieldSql(newField);
            tableMapper.addColumn(dto.getTableSchema(), dto.getTableName(), fieldSql);
        }
        // 删除列
        for (String deleteFieldCode : deleteFieldCodes) {
            tableMapper.dropColumn(dto.getTableSchema(), dto.getTableName(), deleteFieldCode);
        }

        // 刷新表结构定义表
        this.lambdaUpdate().eq(TableEntity::getTableSchema, dto.getTableSchema())
                .eq(TableEntity::getTableName, dto.getTableName()).remove();
        saveTableConf(dto);
        // 关联的数据要赋值回去
        Map<String, TableEntity> historyFieldCodeMap = historyFieldList.stream().collect(Collectors.toMap(TableEntity::getFieldCode, Function.identity(), (v1, v2) -> v1));
        List<TableEntity> curFieldList = this.lambdaQuery().eq(TableEntity::getTableSchema, dto.getTableSchema())
                .eq(TableEntity::getTableName, dto.getTableName()).list();
        curFieldList.forEach(e -> {
            TableEntity old = historyFieldCodeMap.get(e.getFieldCode());
            if(old == null) {
                return;
            }
            e.setInputType(old.getInputType());
            e.setOptionValuesType(old.getOptionValuesType());
            e.setAllOption(old.getAllOption());
        });
        this.updateBatchById(curFieldList);
    }

    @Override
    public void deleteTableStructure(TableDeleteDTO deleteDTO) {
        // 删除表结构定义表
        this.lambdaUpdate().eq(TableEntity::getTableSchema, deleteDTO.getTableSchema())
                .eq(TableEntity::getTableName, deleteDTO.getTableName()).remove();
        // 删除表
        tableMapper.dropTable(deleteDTO.getTableSchema(), deleteDTO.getTableName());
    }

    @Override
    public void createTableView(String tableSchema, String tableName, String script) {
        tableMapper.createTableView(tableSchema, tableName, script);
    }

    @Override
    public void deleteTableView(String tableSchema, String tableName) {
        tableMapper.deleteTableView(tableSchema, tableName);
    }

    @Override
    public ReturnSqlDTO sqlIsValid(SelectSqlDTO dto) {
        ReturnSqlDTO returnSqlDto = ReturnSqlDTO.builder().isValid(false).build();
        String sql = dto.getSql();
        if (StringUtils.isBlank(sql)) {
            return returnSqlDto;
        }
        // 检查是否以SELECT开头
        if (!sql.trim().toUpperCase().startsWith("SELECT")) {
            returnSqlDto.setErrMsg("语句需以SELECT开头");
            return returnSqlDto;
        }
        // 正则表达式用于检测高危关键字
        String dangerousKeywordsRegex = "(?i)(?:\\bDELETE\\b|\\bINSERT\\b|\\bUPDATE\\b|\\bTRUNCATE\\b|\\bDROP\\b|\\bCREATE\\b)";
        Pattern pattern = Pattern.compile(dangerousKeywordsRegex, Pattern.CASE_INSENSITIVE);
        if (pattern.matcher(sql).find()) {
            returnSqlDto.setErrMsg("语句包含敏感词汇");
            return returnSqlDto;
        }
        // 执行查询sql
        try {
            // 校验语句合法性需要加上limit语句,考虑到语句有人会加分号，有人不加分号结尾，这里兼容这两种情况
            if (sql.endsWith(";")) {
                sql = sql.substring(0, sql.length() - 1);
            }
            if (!sql.toLowerCase().contains("limit")) {
                sql += " limit 1";
            }
            // showSql 用于前端的展示，同时校验替换的模板能不能执行
            String showSql = formatTriggerTime(sql, new Date());
            // 获取表头信息
            List<String> headers = new ArrayList<>();
            SqlSession session = sqlSessionFactory.openSession();
            List<LinkedHashMap<String, Object>> result = tableMapper.executeSql(showSql);
            if (!result.isEmpty()) {
                Map<String, Object> firstRow = result.get(0);
                headers.addAll(firstRow.keySet());
            } else {
                // 如果结果为空，使用 ResultSetMetaData 获取表头
                Statement stmt = session.getConnection().createStatement();
                ResultSetMetaData metaData = stmt.executeQuery(showSql).getMetaData();
                int columnCount = metaData.getColumnCount();
                for (int i = 1; i <= columnCount; i++) {
                    headers.add(metaData.getColumnLabel(i));
                }
            }
            // 组转最终执行的sql给用户展示
            String execSql;
            if (TargetModelDataSourceTypeEnum.VIEW.getCode().equals(dto.getDataSourceType())) {
                execSql = getViewSql(dto.getTableSchema(), dto.getTableName(), showSql);
                // 视图需要删除limit 1
                execSql = execSql.replace(" limit 1", "");
            } else {
                execSql = getSql(dto.getTableSchema(), dto.getTableName(), showSql);
            }
            returnSqlDto.setIsValid(true);
            returnSqlDto.setReturnSql(execSql);
            // 获取sql语句对应的字段(不包含id字段，因为建表时会自动加id字段)
            List<String> columnNames = headers.stream().distinct().filter(columnName -> !columnName.equals("id")).collect(Collectors.toList());
            TableColumnSchemaHelper columnHelper = new TableColumnSchemaHelper(showSql);
            returnSqlDto.setFields(columnNames.stream().map(columnName ->
                            TableFieldDTO.builder()
                                    .fieldCode(columnName)
                                    .fieldName(columnHelper.getFieldName(columnName))
                                    .fieldType(columnHelper.getFieldType(columnName))
                                    .fieldLength(columnHelper.getFieldLength(columnName))
                                    .fieldPoint(columnHelper.getFieldPoint(columnName))
                                    .fieldDefaultValue(columnHelper.getFieldDefaultValue(columnName))
                                    .build())
                    .collect(Collectors.toList()));
        } catch (Exception e) {
            log.error(e.getMessage());
            returnSqlDto.setErrMsg(e.getClass().getName()+ ": " + e.getMessage());
            return returnSqlDto;
        }
        return returnSqlDto;
    }

    @Override
    public String formatTriggerTime(String sql, Date triggerTime) {
        if (StringUtils.isBlank(sql)) {
            return sql;
        }
        String regex = "\\$\\s*\\{\\s*(.+?)\\s*}";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(sql);
        // 匹配到了
        if (matcher.find()) {
            if (triggerTime == null) {
                return sql.replaceAll(regex, " 1=1 ");
            } else {
                // 提取内部的内容
                String before = matcher.group(1);
                String after = before.replaceAll(Constant.TRIGGER_TIME, "'" + DateUtil.formatDateTime(triggerTime) + "'");
                return sql.replaceAll(regex, after);
            }
        } else {
            return sql;
        }
    }

    /**
     * 获取视图脚本
     */
    private String getViewSql(String tableSchema, String tableName, String sql) {
        return "CREATE OR REPLACE VIEW `" + tableSchema + "`.`" + tableName + "` AS " + sql;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void dropAndCreateTableIndex(TableCreateOrUpdateDTO dto) {
        List<TableIndexDTO> indexDTOs = dto.getIndexDTOs();
        // 判断更新的字段中是否不包含索引绑定的字段
        List<String> fieldCodes = dto.getFields().stream().map(TableFieldDTO::getFieldCode).collect(Collectors.toList());
        List<String> indexFieldCodes = indexDTOs.stream().map(TableIndexDTO::getIndexFields).flatMap(List::stream).collect(Collectors.toList());
        for (String indexFieldCode : indexFieldCodes) {
            if (!fieldCodes.contains(indexFieldCode)) {
                throw new ResponseException(RespCodeEnum.TABLE_INDEX_INCLUDE_NOT_EXIST_FIELD.fmtDes(indexFieldCode));
            }
        }
        if (CollectionUtils.isEmpty(indexDTOs)) {
            return;
        }
        // 索引名称不能重复
        List<String> indexNames = indexDTOs.stream().map(TableIndexDTO::getIndexName).collect(Collectors.toList());
        if (indexNames.size() != indexNames.stream().distinct().count()) {
            throw new ResponseException(RespCodeEnum.TABLE_INDEX_NAME_REPEAT);
        }

        // 删除表中已存在的索引 proc_drop_all_index
//        String dropIndexSql = "call `" + dto.getTableSchema() + "`.`proc_drop_all_index`('" + dto.getTableName() + "')";
//        tableMapper.executeSql(dropIndexSql);
        tableIndexService.lambdaUpdate().eq(TableIndexEntity::getTableSchema, dto.getTableSchema())
                .eq(TableIndexEntity::getTableName, dto.getTableName()).remove();
        // 创建表索引
        for (TableIndexDTO indexDTO : indexDTOs) {
            try {
                // indexDTO.getIndexFields()按英文逗号分隔组装成','
                String indexFields = String.join(",", indexDTO.getIndexFields());
                // 执行添加索引的存储过程
                if (indexDTO.getIndexType().equals(TableIndexTypeEnum.NORMAL.getCode())) {
                    String addIndexSql = "call `" + dto.getTableSchema() + "`.`proc_add_column_index`('" + dto.getTableName() + "','" + indexFields + "','" + indexDTO.getIndexName() + "')";
                    tableMapper.executeSql(addIndexSql);
                } else if (indexDTO.getIndexType().equals(TableIndexTypeEnum.UNIQUE.getCode())) {
                    String addIndexSql = "call `" + dto.getTableSchema() + "`.`proc_add_unique_index`('" + dto.getTableName() + "','" + indexFields + "','" + indexDTO.getIndexName() + "')";
                    tableMapper.executeSql(addIndexSql);
                }
            } catch (Exception e) {
                throw new ResponseException(RespCodeEnum.INDEX_DUPLICATE_KEY_ERROR.fmtDes(indexDTO.getIndexName()));
            }
        }
        // 记录数据
        List<TableIndexEntity> tableIndexEntities = indexDTOs.stream().map(indexDTO -> TableIndexEntity.builder()
                .tableSchema(dto.getTableSchema())
                .tableName(dto.getTableName())
                .indexName(indexDTO.getIndexName())
                .indexField(String.join(",", indexDTO.getIndexFields()))
                .indexType(indexDTO.getIndexType())
                .build()).collect(Collectors.toList());
        tableIndexService.saveBatch(tableIndexEntities);
    }

    @Override
    public void insertOrUpdateData(String tableSchema, String tableName, String sql) {
        String insertSql = getSql(tableSchema, tableName, sql);
        executeSql(tableName, insertSql);
    }

    @Override
    public void insertOrUpdateData(String tableSchema, String tableName, Collection<Map<String, Object>> collection) {
        if (CollectionUtils.isEmpty(collection)) {
            log.info("插入的数据集为空, 本次不执行");
            return;
        }
        String insertSql = getSql(tableSchema, tableName, collection);
        executeSql(tableName, insertSql);
    }

    private void executeSql(String tableName, String insertSql) {
        try {
            tableMapper.executeSql(insertSql);
            log.info("数据插入成功：执行脚本为" + insertSql);
        } catch (Exception e) {
            log.error(e.getMessage());
            throw new ResponseException(RespCodeEnum.INSERT_OR_UPDATE_DATA_ERROR.fmtDes(tableName));
        }
    }

    @Override
    public List<LinkedHashMap<String, Object>> executeQuerySql(String selectSql) {
        try {
            return tableMapper.executeSql(selectSql);
        } catch (Exception e) {
            log.error(e.getMessage());
            return Collections.emptyList();
        }
    }

    /**
     * 过滤唯一性索引字段、主键id，并获取需要更新的字段
     */
    private String getSql(String tableSchema, String tableName, String sql) {
        List<String> fieldCodes = getTableConfigs(tableSchema, tableName).stream().map(TableEntity::getFieldCode).collect(Collectors.toList());
        ;
        fieldCodes.remove("id");
        List<TableIndexEntity> tableIndexEntities = tableIndexService.lambdaQuery().eq(TableIndexEntity::getTableSchema, tableSchema)
                .eq(TableIndexEntity::getTableName, tableName)
                .list();
        List<String> uniqueFieldCodes = tableIndexEntities.stream()
                .filter(o -> o.getIndexType().equals(TableIndexTypeEnum.UNIQUE.getCode()))
                .map(o -> Arrays.asList(o.getIndexField().split(Constant.SEP)))
                .flatMap(Collection::stream).collect(Collectors.toList());
        List<String> updateFieldCodes = fieldCodes.stream()
                .filter(fieldCode -> !uniqueFieldCodes.contains(fieldCode))
                .collect(Collectors.toList());
        // 没有需要更新的字段，则直接插入
        String insertSql;
        if (CollectionUtils.isEmpty(updateFieldCodes)) {
            // 如果sql最后一个字符不是; 则增加该符号
            if (!sql.endsWith(";")) {
                sql += ";";
            }
            insertSql = "INSERT INTO `" + tableSchema + "`.`" + tableName
                    + "` (" + String.join(",", fieldCodes) + ") " + sql;
        } else {
            // 如果sql最后一个字符是; 则删除该符号
            if (sql.endsWith(";")) {
                sql = sql.substring(0, sql.length() - 1);
            }
            // 整合成批量插入的语句，如果数据存在唯一索引字段，则更新其他非唯一索引字段，否则插入新数据
            insertSql = "INSERT INTO `" + tableSchema + "`.`" + tableName
                    + "` (" + String.join(",", fieldCodes) + ") " + sql + " ON DUPLICATE KEY UPDATE ";
            insertSql += updateFieldCodes.stream().map(e -> e + "=VALUES(" + e + ")").collect(Collectors.joining(","));
        }
        return insertSql;
    }

    @Override
    public List<TableEntity> getTableConfigs(String tableSchema, String tableName) {
        return this.lambdaQuery().eq(TableEntity::getTableSchema, tableSchema)
                .eq(TableEntity::getTableName, tableName)
                .orderByAsc(TableEntity::getId)
                .list();
    }

    public String getSql(String tableSchema, String tableName, Collection<Map<String, Object>> collection) {
        // 表字段 -> 外部映射
        // 用linkMap 保证字段的插入顺序
        Map<String, String> fieldMapping = getTableConfigs(tableSchema, tableName).stream().collect(LinkedHashMap::new, (n, v) -> n.put(v.getFieldCode(), v.getFieldMapping()), HashMap::putAll);
        String preSql = collection.stream().map(node -> {
            // 每一行数据
            String row = fieldMapping.entrySet().stream().map(entry -> {
                String field = entry.getKey();
                String mapping = entry.getValue();
                // 获取插入表的值：不存在则 下划线 转 驼峰进行匹配； 否则 直接使用映射
                Object v = mapping == null ? node.get(StrUtil.toCamelCase(field)) : node.get(mapping);
                if (v == null) {
                    return "NULL";
                }
                // 字符串要加 单引号
                if (v instanceof String) {
                    return String.format("'%s'", v);
                }
                return v.toString();
                // 多列按, 分隔
            }).collect(Collectors.joining(","));
            // 每一行用()抱起来
            return String.format("(%s)", row);
            // 多行按, 分隔
        }).collect(Collectors.joining(","));
        return getSql(tableSchema, tableName, "VALUES" + preSql);
    }

    @Override
    public void dropTable(String tableSchema, String tableName) {
        tableMapper.dropTable(tableSchema, tableName);
    }


    /**
     * 获取字段sql语句
     */
    private String getFieldSql(TableFieldDTO field) {
        TableFieldTypeEnum typeEnum = TableFieldTypeEnum.getEnumByType(field.getFieldType());
        String tableRemark = StringUtils.isBlank(field.getFieldRemark()) ? "" : field.getFieldRemark();
        String fieldDefaultValue = StringUtils.isBlank(field.getFieldDefaultValue()) ? "NULL" : field.getFieldDefaultValue();
        String remarkSql = " COMMENT '" + tableRemark + "'";
        String defaultValueSql;
        switch (typeEnum) {
            case INT:
                defaultValueSql = " DEFAULT " + fieldDefaultValue;
                return "`" + field.getFieldCode() + "` " + field.getFieldType() + "(" + field.getFieldLength() + ")" + defaultValueSql + remarkSql;
            case VARCHAR:
                defaultValueSql = " DEFAULT " + "'" + fieldDefaultValue + "'";
                return "`" + field.getFieldCode() + "` " + field.getFieldType() + "(" + field.getFieldLength() + ")" + defaultValueSql + remarkSql;
            case DOUBLE:
                defaultValueSql = " DEFAULT " + fieldDefaultValue;
                return "`" + field.getFieldCode() + "` " + field.getFieldType() + "(" + field.getFieldLength() + "," + field.getFieldPoint() + ")" + defaultValueSql + remarkSql;
            case DATETIME:
                return "`" + field.getFieldCode() + "` " + field.getFieldType() + remarkSql;
            case DATE:
                return "`" + field.getFieldCode() + "` " + field.getFieldType() + remarkSql;
            default:
                return "";
        }
    }


    @Override
    public Page<CommonTableDTO> getDfsTableList(Page page, String tableName, String tableComment) {
        Page<CommonTableDTO> dfsTableList = tableMapper.getDfsTableList(page, tableName, tableComment, workPropertise.getTableSchema());
        List<CommonTableDTO> records = dfsTableList.getRecords();
        for (CommonTableDTO dto : records) {
            String name = dto.getTableName();
            String date;
            // 相关数据表：dfs_operation_log、dfs_report_count、dfs_report_fac_state、dfs_sensor_record
            if (name.equals(TableEnum.DFS_OPERATION_LOG.getTableName()) ||
                    name.equals(TableEnum.DFS_REPORT_COUNT.getTableName()) ||
                    name.equals(TableEnum.DFS_REPORT_FAC_STATE.getTableName()) ||
                    name.equals(TableEnum.DFS_SENSOR_RECORD.getTableName())) {

                date = tableMapper.getEarliestCreatTime(name);

            } else if (name.equals(TableEnum.DFS_ALARM_RECORD.getTableName())) {
                // 相关数据表：dfs_alarm_record
                date = tableMapper.getEarliestAlarmTime(name);

            } else if (name.equals(TableEnum.DFS_NOTICE_RECORD.getTableName())) {
                // 相关数据表：dfs_notice_record
                date = tableMapper.getEarliestSendTime(name);

            } else {
                // 相关数据表：dfs_record_*、dfs_quality
                date = tableMapper.getEarliestTime(name);
            }
            if (StringUtils.isNotBlank(date)) {
                dto.setCreateTime(DateUtil.parse(date, DateUtil.DATETIME_FORMAT));
            }
        }
        return dfsTableList;
    }

    @Override
    public void refreshTable(List<TableColumnDTO> refreshList) {
        if(CollectionUtils.isEmpty(refreshList)) {
            return;
        }
        List<String> tableNames = refreshList.stream().map(TableColumnDTO::getTableName).collect(Collectors.toList());
        // 获取现有配置
        List<TableEntity> existingConfigs = this.lambdaQuery()
                .in(TableEntity::getTableName, tableNames)
                .list();
        Map<String, TableEntity> existingMap = existingConfigs.stream().collect(Collectors.toMap(TableEntity::buildUniCode, Function.identity()));

        List<TableEntity> upsertList = new ArrayList<>();
        for (TableColumnDTO dto : refreshList) {
            TableEntity existConfig = existingMap.get(dto.buildUniCode());
            TableEntity upsertEntity = JacksonUtil.convertObject(dto, TableEntity.class);
            // 不相同 才需要刷新
            if(existConfig != null) {
                TableColumnDTO existDto = JacksonUtil.convertObject(existConfig, TableColumnDTO.class);
                if(dto.equalsIgnoreSome(existDto)) {
                    continue;
                }
                // 更新时需要插入id
                upsertEntity.setId(existConfig.getId());
                // 这俩不刷新
                upsertEntity.setFieldPoint(existConfig.getFieldPoint());
                upsertEntity.setFieldLength(existConfig.getFieldLength());
            }
            upsertList.add(upsertEntity);
        }
        this.saveOrUpdateBatch(upsertList);
    }
}
