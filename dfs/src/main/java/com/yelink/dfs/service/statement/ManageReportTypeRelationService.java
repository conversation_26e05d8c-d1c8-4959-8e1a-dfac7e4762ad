package com.yelink.dfs.service.statement;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yelink.dfs.entity.statement.ManageReportTypeRelationEntity;

import java.util.List;

/**
 * 报表和类型关联表(DfsManageReportTypeRelation)表服务接口
 *
 * <AUTHOR>
 * @since 2024-08-16 11:26:59
 */
public interface ManageReportTypeRelationService extends IService<ManageReportTypeRelationEntity> {

    /**
     * 关联报表和类型
     */
    void reportTypeRelate(List<ManageReportTypeRelationEntity> reportTypeRelations);

    /**
     * 移除报表和类型关联
     */
    void removeReportTypeRelate(List<ManageReportTypeRelationEntity> reportTypeRelations);

    void removeReportTypeRelate(Integer reportId);
}

