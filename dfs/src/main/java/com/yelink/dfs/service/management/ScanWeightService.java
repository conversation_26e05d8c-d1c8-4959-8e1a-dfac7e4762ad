package com.yelink.dfs.service.management;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yelink.dfs.entity.management.ScanWeightEntity;
import com.yelink.dfs.entity.sensor.SensorEntity;


/**
 * Service
 *
 * <AUTHOR>
 * @Date 2022-03-04 22:37
 */
public interface ScanWeightService extends IService<ScanWeightEntity> {

    /**
     * 包装管理处理称重消息
     *
     * @param sensorEntity
     */
    void handleWeighingMessage(SensorEntity sensorEntity);

    /**
     * 包装管理处理扫码消息
     *
     * @param sensorEntity
     */
    void handleCodeScannerMessage(SensorEntity sensorEntity);

}
