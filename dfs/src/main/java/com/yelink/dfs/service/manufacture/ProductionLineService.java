package com.yelink.dfs.service.manufacture;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yelink.dfs.entity.device.DeviceEntity;
import com.yelink.dfs.entity.manufacture.ProductionLineEntity;
import com.yelink.dfs.entity.model.vo.ExcelLogVO;
import com.yelink.dfs.entity.order.WorkOrderEntity;
import com.yelink.dfs.entity.target.record.dto.WorkOrderReportDTO;
import com.yelink.dfs.entity.user.SysUserEntity;
import com.yelink.dfs.open.v1.model.dto.ProductionLinePageDTO;
import com.yelink.dfs.open.v2.model.dto.ProductionLineQueryDTO;
import com.yelink.dfs.open.v2.model.vo.ProductionLineListVO;
import com.yelink.dfscommon.common.service.ImportDataService;
import com.yelink.dfscommon.dto.dfs.ReportDTO;
import com.yelink.dfscommon.pojo.PageResult;
import org.springframework.web.multipart.MultipartFile;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;


/**
 * Service
 *
 * <AUTHOR>
 * @Date 2020-12-03 11:15
 */
public interface ProductionLineService extends IService<ProductionLineEntity>, ImportDataService {

    /**
     * 添加产线
     *
     * @param entity
     */
    void add(ProductionLineEntity entity);

    /**
     * 删除产线
     *
     * @param id 产线id
     * @return
     */
    ProductionLineEntity deleteLine(Integer id);

    /**
     * 查询产线列表
     *
     * @return
     */
    Page<ProductionLineEntity> list(ProductionLinePageDTO dto);

    /**
     * 添加产线,并添加产线分支
     *
     * @param entity 产线实体
     * @return
     */
    void addEntity(ProductionLineEntity entity, String username);

    /**
     * 查询车间下产线列表
     *
     * @param gid     车间id
     * @param current 当前页
     * @param size    当前页条数
     * @return
     */
    Page<ProductionLineEntity> lineByGid(Integer gid, Integer current, Integer size);

    /**
     * 查询该车间下所有产线
     *
     * @param gid 产线组即车间,不传查全部
     * @return List<ProductionLineEntity> ProductionLineEntity::getProductionLineId, ProductionLineEntity::getName
     */
    List<ProductionLineEntity> listLineByGid(Integer gid);


    /**
     * 更新产线，并修改产线分支
     *
     * @param entity 产线实体
     * @return
     */
    void updateEntity(ProductionLineEntity entity);


    /**
     * 查询产线详情
     *
     * @param id
     * @return
     */
    ProductionLineEntity detail(Integer id);

    /**
     * 通过产线名称获取产线
     *
     * @param lineName
     * @return
     */
    ProductionLineEntity getEntityByLineName(String lineName);

    /**
     * 通过产线code获取产线
     *
     * @param lineCode
     * @return
     */
    ProductionLineEntity getEntityByLineCode(String lineCode);

    /**
     * 获取指定车间下的所有产线
     *
     * @param gridCodes
     * @return
     */
    List<ProductionLineEntity> getLineList(String gridCodes);

    /**
     * 点击工单完成时选择新增工单
     *
     * @param workOrderEntity
     */
    void addOrderNew(WorkOrderEntity workOrderEntity) throws Exception;

    /**
     * 通过产线编号获取产线列表
     *
     * @param lineCodes
     * @return
     */
    List<ProductionLineEntity> listByCodes(List<String> lineCodes);


    /**
     * 根据工位id查询对应的产线
     *
     * @param fid
     * @return
     */
    ProductionLineEntity getLineEntityByFid(Integer fid);


    /**
     * 工位看板--选择工单开始停止
     *
     * @param dto
     * @return
     */
    WorkOrderEntity reportAction(ReportDTO dto);

    /**
     * 获取工单绑定产线的设备编号列表
     *
     * @param workOrder 工单号
     * @return
     */
    List<DeviceEntity> getWorkOrderDeviceList(String workOrder);

    /**
     * 产线报工--获取产线列表（工单模式）
     *
     * @param name
     * @param code
     * @param modelName
     * @param currentPage
     * @param size
     * @return
     */
    Page<ProductionLineEntity> listForLineReport(Integer workCenterId, String name, String code, String modelName, Boolean isOperation, String workCenterType, Integer currentPage, Integer size, String username);

    /**
     * 查询对应产线的产线名称
     *
     * @param lineIds lineIds
     * @return Map<Integer, String>
     */
    Map<Integer, String> selectNameByLineIds(Collection<Integer> lineIds);

    void showName(List<ProductionLineEntity> records, List<String> queryAddition);


    /**
     * 根据产线模型id获取产线列表
     *
     * @param modelId
     * @return
     */
    List<ProductionLineEntity> listByWorkCenterId(Integer modelId);

    /**
     * 通过工作中心获取产线
     *
     * @param workCenterId
     * @return
     */
    List<ProductionLineEntity> getByWorkCenter(Integer workCenterId);

    /**
     * 批量更改工单状态
     *
     * @param workOrderReportDTO
     * @return
     */
    void hangOccupiedWorkOrder(WorkOrderReportDTO workOrderReportDTO, String username);

    /**
     * 查询某条产线下的所有员工
     *
     * @param productLineCode 产线编码
     * @return
     */
    List<SysUserEntity> getLineEmployees(String productLineCode);

    /**
     * 导入
     * @param file 文件
     * @return 日志
     */
    ExcelLogVO importData(MultipartFile file);

    Map<Integer, String> getIdNameMap(Collection<Integer> ids);

    Page<ProductionLineListVO> getList2(ProductionLineQueryDTO dto);
}
