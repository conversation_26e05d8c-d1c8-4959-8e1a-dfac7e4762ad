package com.yelink.dfs.service.attendance;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yelink.dfs.entity.attendance.AttendanceRecordEntity;
import com.yelink.dfs.entity.attendance.dto.AttendanceLineDTO;
import com.yelink.dfs.entity.attendance.dto.AttendanceRecordDTO;
import com.yelink.dfs.entity.attendance.dto.AttendanceUserDTO;
import com.yelink.dfs.entity.attendance.vo.AttendanceLineVO;
import com.yelink.dfs.entity.attendance.vo.AttendanceRecentVO;
import com.yelink.dfs.entity.attendance.vo.AttendanceRecordVO;
import com.yelink.dfs.entity.attendance.vo.AttendanceUserVO;

import java.util.List;


/**
 * 打卡考勤服务
 * <AUTHOR>
 */
public interface AttendanceRecordService extends IService<AttendanceRecordEntity> {

    /**
     * 获取最近一条没有下班打卡的记录
     * @param userId 用户id
     * @return 打卡记录
     */
    List<AttendanceRecordVO> getNoDownRecord(Integer userId);

    /**
     * 获取打卡记录
     * @param dto 请求入参
     * @return 打卡记录
     */
    Page<AttendanceRecordVO> recordList(AttendanceRecordDTO dto);

    /**
     * 获取最近打卡记录
     * @param lineId 产线
     * @return  最近打卡记录
     */
    AttendanceRecentVO recentCheckIn(Integer lineId);

    /**
     * 今日产线打卡详情
     * @param dto 入参
     * @return 详情列表
     */
    List<AttendanceLineVO> lineTodayCheckIn(AttendanceLineDTO dto);

    /**
     * 今日用户打卡详情
     * @param dto
     * @return
     */
    List<AttendanceUserVO> userTodayCheckIn(AttendanceUserDTO dto);
}
