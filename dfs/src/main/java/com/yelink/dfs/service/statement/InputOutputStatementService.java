package com.yelink.dfs.service.statement;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yelink.dfs.entity.statement.dto.InputOutputStatementListDTO;
import com.yelink.dfs.entity.statement.dto.InputOutputStatementSelectDTO;


/**
 * Service
 *
 * <AUTHOR>
 * @Date 2021-02-22 17:23
 */
public interface InputOutputStatementService {


    /**
     * 查询数据
     *
     * @param selectDTO
     * @return
     */
    Page<InputOutputStatementListDTO> getList(InputOutputStatementSelectDTO selectDTO);
}
