package com.yelink.dfs.service.management;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yelink.dfs.entity.management.PackageRecordEntity;
import com.yelink.dfscommon.dto.dfs.PackageRecordAddDTO;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;


/**
 * Service
 *
 * <AUTHOR>
 * @Date 2021-11-16 17:58
 */
public interface PackageRecordService extends IService<PackageRecordEntity> {
    /**
     * 查询列表
     *
     * @param workOrderNumber 工单编号
     * @param barCode         批次号
     * @param materialName    物料名称
     * @param materialCode    物料编号
     * @param reportStartTime 上报开始时间
     * @param reportEndTime   上报结束时间
     * @param current
     * @param size
     * @param packageLevel
     * @param productSn
     * @param packType
     * @param containerSn
     * @param operatorName
     * @return
     */
    Page<PackageRecordEntity> getList(String workOrderNumber, String barCode, String materialName,
                                      String materialCode, Integer deviceId, String reportStartTime,
                                      String reportEndTime, Integer current, Integer size, Integer packageLevel,
                                      String productSn, String packType, String containerSn, String operatorName);

    /**
     * 导出
     *
     * @param workOrderNumber
     * @param barCode
     * @param materialName
     * @param materialCode
     * @param deviceId
     * @param reportStartTime
     * @param reportEndTime
     * @param packageLevel
     * @param productSn
     * @param packType
     * @param containerSn
     * @param operator
     * @param response
     */
    void export(String workOrderNumber, String barCode, String materialName, String materialCode,
                Integer deviceId, String reportStartTime, String reportEndTime,
                Integer packageLevel, String productSn, String packType, String containerSn, String operator,
                HttpServletResponse response) throws IOException;


    /**
     * 添加扫码和称重匹配的数据
     *
     * @param entity
     */
    void addScanAndWeight(PackageRecordEntity entity, String weighingEui);

    /**
     * 批量新增同一工单下的包装记录,
     *
     * @param batchAdds
     */
    void batchAdd(List<PackageRecordAddDTO> batchAdds);
}
