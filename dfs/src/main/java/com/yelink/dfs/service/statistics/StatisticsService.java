package com.yelink.dfs.service.statistics;


import com.yelink.dfs.entity.model.FacilitiesEntity;

import java.util.List;

/**
 * @description:
 * @author: shuang
 * @time: 2020/6/30
 */
public interface StatisticsService {



    /**
     * 获取工位下员工及状态
     *
     * @param lineId
     * @param workOrder
     * @return
     */
    List<FacilitiesEntity> getFacUser(Integer lineId, String workOrder);

    /**
     * 上报员工状态
     *
     * @param username
     * @param fid
     * @param workOrder
     * @param state
     * @return
     */
    void reportFacUser(String username, Integer fid, String workOrder, Integer state);


}
