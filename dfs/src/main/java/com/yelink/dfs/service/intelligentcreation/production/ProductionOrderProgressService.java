package com.yelink.dfs.service.intelligentcreation.production;

import com.yelink.dfs.entity.intelligentcreation.production.ProcedureHoursVO;
import com.yelink.dfs.entity.intelligentcreation.production.ProcedureProcessVO;
import com.yelink.dfs.entity.intelligentcreation.production.ProcessDetailsVO;
import com.yelink.dfs.entity.order.RecordWorkOrderDayCountEntity;
import com.yelink.dfs.entity.order.WorkOrderEntity;
import com.yelink.dfs.entity.product.CraftProcedureEntity;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/5/26 11:35
 */
public interface ProductionOrderProgressService {

    /**
     * 获取工艺工序
     *
     * @param materialCode
     * @param workOrders
     * @return
     */
    List<CraftProcedureEntity> getCraftProcedureList(String materialCode, List<WorkOrderEntity> workOrders);

    /**
     * 生产订单进度--获取工序详情
     *
     * @param productionOrderNumber
     * @param craftProcedureId
     * @return
     */
    ProcessDetailsVO getProcedureDetail(String productionOrderNumber, Integer craftProcedureId);

    /**
     * 获取生产订单进度
     *
     * @param productionOrderNumber
     * @return
     */
    List<ProcedureProcessVO> getProcedureProgress(String productionOrderNumber);

    ProcedureHoursVO getProcedureHours(String productionOrderNumber);

    /**
     * 通过工单号获取作业工单的报工记录
     *
     * @param workOrderNumber
     * @return
     */
    List<RecordWorkOrderDayCountEntity> getOperationOrderReportList(String workOrderNumber);
}
