package com.yelink.dfs.service.customization;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yelink.dfs.entity.customization.CustomComponentConfigEntity;


/**
 * Service
 *
 * <AUTHOR>
 * @Date 2021-12-23 10:24
 */
public interface CustomComponentConfigService extends IService<CustomComponentConfigEntity> {

    /**
     * 获取组件名字
     *
     * @param locationMarkerName 位置标识名
     * @param fid 工位id
     * @return
     */
    String getComponent(String locationMarkerName,Integer fid);
}
