package com.yelink.dfs.service.impl.pack;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yelink.dfs.entity.pack.PackageBoxMacSnRelationEntity;
import com.yelink.dfs.mapper.pack.PackageBoxMacSnRelationMapper;
import com.yelink.dfs.service.code.ProductFlowCodeService;
import com.yelink.dfs.service.pack.PackageBoxMacSnRelationService;
import com.yelink.dfscommon.constant.ResponseException;
import com.yelink.dfscommon.entity.dfs.productFlowCode.CodeRelevanceEntity;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2023/3/5 17:07
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PackageBoxMacSnRelationServiceImpl extends ServiceImpl<PackageBoxMacSnRelationMapper, PackageBoxMacSnRelationEntity> implements PackageBoxMacSnRelationService {

    private final ProductFlowCodeService productFlowCodeService;

    @Override
    public void saveRelation(Integer boxId, String boxNumber, String nameplateSn) {
        // 当前产品已存在其它包装，请勿重复包装
        List<PackageBoxMacSnRelationEntity> relations = this.listRelation(null, nameplateSn);
        List<PackageBoxMacSnRelationEntity> list1 = relations.stream().filter(res -> res.getBoxId().equals(boxId)).collect(Collectors.toList());
        List<PackageBoxMacSnRelationEntity> list2 = relations.stream().filter(res -> !res.getBoxId().equals(boxId)).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(list1)) {
            log.error("当前产品已存在于当前包装,请勿重复包装, {},{},{}", nameplateSn, boxNumber, JSON.toJSONString(list1));
            throw new ResponseException("当前产品已存在于当前包装,请勿重复包装");
        }
        if (!CollectionUtils.isEmpty(list2)) {
            log.error("当前产品已存在其它包装,请勿重复包装, {},{},{}", nameplateSn, boxNumber, JSON.toJSONString(list2));
            throw new ResponseException("当前产品已存在其它包装,请勿重复包装");
        }
        // 保存关联记录
        this.save(PackageBoxMacSnRelationEntity.builder()
                .boxId(boxId)
                .boxNumber(boxNumber)
                .nameplateSn(nameplateSn)
                .createTime(new Date())
                .build());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void savePackageOrderRelation(Integer boxId, String boxNumber, String nameplateSn) {
        this.saveRelation(boxId, boxNumber, nameplateSn);
        productFlowCodeService.relevanceCode(CodeRelevanceEntity.builder()
                .code(boxNumber)
                .relevanceCode(nameplateSn)
                .build());
    }

    @Override
    public List<PackageBoxMacSnRelationEntity> listRelation(String boxNumber, String nameplateSn) {
        return this.lambdaQuery()
                .eq(StringUtils.isNotBlank(boxNumber), PackageBoxMacSnRelationEntity::getBoxNumber, boxNumber)
                .eq(StringUtils.isNotBlank(nameplateSn), PackageBoxMacSnRelationEntity::getNameplateSn, nameplateSn)
                .list();
    }
}
