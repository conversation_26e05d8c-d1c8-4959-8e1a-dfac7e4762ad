package com.yelink.dfs.service.statement;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yelink.dfs.entity.statement.ManageSourceParamEntity;

import java.util.List;

/**
 * 报表api数据源查询参数(DfsManageSourceParam)表服务接口
 *
 * <AUTHOR>
 * @since 2024-07-09 15:45:48
 */
public interface ManageSourceParamService extends IService<ManageSourceParamEntity> {

    /**
     * api自定义数据源,请求参数字段
     */
    List<ManageSourceParamEntity> listParamFields(String sourceCode);
}

