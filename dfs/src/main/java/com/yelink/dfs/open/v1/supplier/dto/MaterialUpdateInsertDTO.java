package com.yelink.dfs.open.v1.supplier.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;


/**
 * @Description:
 * @Author: zengzhengfu
 * @Date: 2024/7/18
 */
@Data
@Builder
@ToString
@AllArgsConstructor
@NoArgsConstructor
public class MaterialUpdateInsertDTO {

    /**
     * 供应商编号
     */
    @ApiModelProperty("供应商编号")
    private String supplierCode;

    /**
     * 物料编码
     */
    @ApiModelProperty("物料编码")
    private String materialCode;

    /**
     * ERP关联物料行id
     */
    @ApiModelProperty("ERP关联物料行id")
    private String externalMaterialId;

    /**
     * 供应商物料编码
     */
    @ApiModelProperty("供应商物料编码")
    private String supplierMaterialCode;

    /**
     * 供应商物料名称
     */
    @ApiModelProperty("供应商物料名称")
    private String supplierMaterialName;

    /**
     * 供应商物料规格
     */
    @ApiModelProperty("供应商物料规格")
    private String supplierMaterialStandard;

    /**
     * 状态
     */
    @ApiModelProperty(value = "state")
    private Integer state;

    @ApiModelProperty(value = "扩展字段1")
    private String supplierMaterialExtendFieldOne;
    @ApiModelProperty(value = "扩展字段2")
    private String supplierMaterialExtendFieldTwo;
    @ApiModelProperty(value = "扩展字段3")
    private String supplierMaterialExtendFieldThree;
    @ApiModelProperty(value = "扩展字段4")
    private String supplierMaterialExtendFieldFour;
    @ApiModelProperty(value = "扩展字段5")
    private String supplierMaterialExtendFieldFive;
    @ApiModelProperty(value = "扩展字段6")
    private String supplierMaterialExtendFieldSix;
    @ApiModelProperty(value = "扩展字段7")
    private String supplierMaterialExtendFieldSeven;
    @ApiModelProperty(value = "扩展字段8")
    private String supplierMaterialExtendFieldEight;
    @ApiModelProperty(value = "扩展字段9")
    private String supplierMaterialExtendFieldNine;
    @ApiModelProperty(value = "扩展字段10")
    private String supplierMaterialExtendFieldTen;
}
