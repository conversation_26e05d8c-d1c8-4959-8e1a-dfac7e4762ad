package com.yelink.dfs.open.v2.material;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yelink.dfs.common.BaseController;
import com.yelink.dfs.open.v1.material.dto.MaterialTypeInsert2DTO;
import com.yelink.dfs.open.v2.material.dto.MaterialTypeBatchDeleteDTO;
import com.yelink.dfs.open.v2.material.dto.MaterialTypeBatchInsertDTO;
import com.yelink.dfs.open.v2.material.dto.MaterialTypeBatchInsertOrUpdateDTO;
import com.yelink.dfs.open.v2.material.dto.MaterialTypeBatchUpdateDTO;
import com.yelink.dfs.open.v2.material.dto.MaterialTypeDetailQueryDTO;
import com.yelink.dfs.open.v2.material.dto.MaterialTypeQueryDTO;
import com.yelink.dfs.open.v2.material.vo.MaterialTypeBatchDeleteResultVO;
import com.yelink.dfs.open.v2.material.vo.MaterialTypeBatchInsertResultVO;
import com.yelink.dfs.open.v2.material.vo.MaterialTypeBatchUpdateResultVO;
import com.yelink.dfs.open.v2.material.vo.MaterialTypeSyncResultVO;
import com.yelink.dfs.open.v2.material.vo.MaterialTypeVO;
import com.yelink.dfs.service.common.DictService;
import com.yelink.dfscommon.constant.RespCodeEnum;
import com.yelink.dfscommon.constant.ResponseException;
import com.yelink.dfscommon.exception.BatchOperationException;
import com.yelink.dfscommon.pojo.PageResult;
import com.yelink.dfscommon.pojo.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @description 物料类型接口V2
 * @Date 2025/5/19
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/v2/open/material/types")
@Api(tags = {"配置中心/V2/物料类型接口"})
public class MaterialTypeOpenV2Controller extends BaseController {

    private DictService dictService;

    /**
     * 同步物料类型
     *
     * @param dtos 需要同步的物料类型列表
     * @return
     */
    @ApiOperation(value = "同步物料类型", notes = "唯一标识:物料类型编码，不存在新增，存在则更新。\n" +
            "返回状态码说明：\n" +
            "4503: 物料类型名称重复\n" +
            "4503: 物料类型编码重复\n" +
            "5142: 传入的参数不能为空")
    @PostMapping("/upsert")
    public Result<MaterialTypeSyncResultVO> syncMaterialType(@RequestBody @Valid List<MaterialTypeInsert2DTO> dtos) {
        if (CollectionUtils.isEmpty(dtos)) {
            throw new ResponseException(RespCodeEnum.PARAM_NOT_NULL);
        }
        MaterialTypeSyncResultVO result = dictService.syncMaterialType(dtos);
        return Result.success(result);
    }

    /**
     * 查询物料类型列表
     *
     * @param dto
     * @return
     */
    @ApiOperation(value = "查询物料类型列表", notes = "查询物料类型列表")
    @PostMapping("/list")
    public Result<PageResult<MaterialTypeVO>> list(@RequestBody MaterialTypeQueryDTO dto) {
        Page<MaterialTypeVO> page = dictService.getMaterialTypeList(dto);
        return Result.success(page);
    }

    /**
     * 批量新增物料类型
     *
     * @param dto 批量新增物料类型DTO列表
     * @return
     */
    @ApiOperation(value = "批量新增物料类型", notes = "批量新增物料类型，已存在的物料类型编码会返回失败信息。\n" +
            "批量操作保证事务一致性：要么全部成功，要么全部失败。\n" +
            "如果存在失败项，会返回详细的失败信息，包括成功和失败的记录详情。\n" +
            "返回状态码说明：\n" +
            "DFS.MATERIAL_TYPE.0001: 存在相同编码或者名称的物料类型\n" +
            "DFS.MATERIAL_TYPE.0002: 关联的工艺模板不存在\n" +
            "5142: 传入的参数不能为空")
    @PostMapping("/batch/add")
    public Result<MaterialTypeBatchInsertResultVO> batchAdd(@RequestBody @Valid MaterialTypeBatchInsertDTO dto) {
        if (CollectionUtils.isEmpty(dto.getMaterialTypes())) {
            throw new ResponseException(RespCodeEnum.PARAM_NOT_NULL);
        }
        try {
            MaterialTypeBatchInsertResultVO result = dictService.batchAddMaterialTypeV2(dto);
            return Result.success(result);
        } catch (BatchOperationException e) {
            // 批量操作失败，返回包含失败详情的结果
            MaterialTypeBatchInsertResultVO result = e.getOperationResult(MaterialTypeBatchInsertResultVO.class);
            return Result.success(result, e.getMessage());
        }
    }

    /**
     * 批量更新物料类型
     *
     * @param dto 批量更新物料类型DTO列表
     * @return
     */
    @ApiOperation(value = "批量更新物料类型", notes = "批量更新物料类型，只更新已存在的物料类型编码，不存在的物料类型编码会返回失败信息。\n" +
            "批量操作保证事务一致性：要么全部成功，要么全部失败。\n" +
            "如果存在失败项，会返回详细的失败信息，包括成功和失败的记录详情。\n" +
            "返回状态码说明：\n" +
            "DFS.MATERIAL_TYPE.0003: 存在不属于系统的物料类型\n" +
            "DFS.MATERIAL_TYPE.0002: 关联的工艺模板不存在\n" +
            "5142: 传入的参数不能为空")
    @PostMapping("/batch/update")
    public Result<MaterialTypeBatchUpdateResultVO> batchUpdate(@RequestBody @Valid MaterialTypeBatchUpdateDTO dto) {
        if (CollectionUtils.isEmpty(dto.getMaterialTypes())) {
            throw new ResponseException(RespCodeEnum.PARAM_NOT_NULL);
        }
        try {
            MaterialTypeBatchUpdateResultVO result = dictService.batchUpdateMaterialTypeV2(dto);
            return Result.success(result);
        } catch (BatchOperationException e) {
            // 批量操作失败，返回包含失败详情的结果
            MaterialTypeBatchUpdateResultVO result = e.getOperationResult(MaterialTypeBatchUpdateResultVO.class);
            return Result.success(result, e.getMessage());
        }
    }

    /**
     * 批量删除物料类型
     *
     * @param deleteDTO 批量删除物料类型DTO
     * @return
     */
    @ApiOperation(value = "批量删除物料类型", notes = "批量删除物料类型，删除前自动进行完整的业务校验，删除失败会返回失败信息。\n" +
            "批量操作保证事务一致性：要么全部成功，要么全部失败。\n" +
            "如果存在失败项，会返回详细的失败信息，包括成功和失败的记录详情。\n" +
            "返回状态码说明：\n" +
            "1160: 存在关联该物料类型的物料\n" +
            "5142: 传入的参数不能为空")
    @DeleteMapping("/batch/delete")
    public Result<MaterialTypeBatchDeleteResultVO> batchDelete(@RequestBody @Valid MaterialTypeBatchDeleteDTO deleteDTO) {
        if (CollectionUtils.isEmpty(deleteDTO.getCode())) {
            throw new ResponseException(RespCodeEnum.PARAM_NOT_NULL);
        }
        try {
            MaterialTypeBatchDeleteResultVO result = dictService.batchDeleteMaterialTypeV2(deleteDTO);
            return Result.success(result);
        } catch (BatchOperationException e) {
            // 批量操作失败，返回包含失败详情的结果
            MaterialTypeBatchDeleteResultVO result = e.getOperationResult(MaterialTypeBatchDeleteResultVO.class);
            return Result.success(result, e.getMessage());
        }
    }

    /**
     * 查询物料类型详情
     *
     * @param dto 查询条件
     * @return
     */
    @ApiOperation(value = "查询物料类型详情", notes = "查询物料类型详情")
    @PostMapping("/detail")
    public Result<MaterialTypeVO> detail(@RequestBody @Validated MaterialTypeDetailQueryDTO dto) {
        MaterialTypeVO vo = dictService.getMaterialTypeDetail(dto);
        return Result.success(vo);
    }
}
