package com.yelink.dfs.open.v1.defect;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yelink.dfs.common.BaseController;
import com.yelink.dfs.constant.defect.DefectTypeDictEnum;
import com.yelink.dfs.entity.defect.DefectDefineEntity;
import com.yelink.dfs.entity.defect.DefectSchemeEntity;
import com.yelink.dfs.entity.defect.dto.DefectRecordQueryDTO;
import com.yelink.dfs.entity.defect.dto.TypeEnumDTO;
import com.yelink.dfs.entity.order.RecordWorkOrderCountEntity;
import com.yelink.dfs.entity.order.RecordWorkOrderUnqualifiedEntity;
import com.yelink.dfs.open.v1.defect.dto.DefectDefineDTO;
import com.yelink.dfs.open.v1.defect.dto.DefectDefineInsertDTO;
import com.yelink.dfs.open.v1.defect.dto.DefectDefineDetailDTO;
import com.yelink.dfs.open.v1.defect.dto.DefectDefineSelectDTO;
import com.yelink.dfs.open.v1.defect.dto.DefectDefineUpdateDTO;
import com.yelink.dfs.open.v1.defect.dto.DefectRecordDTO;
import com.yelink.dfs.open.v1.defect.dto.DefectRecordSelectDTO;
import com.yelink.dfs.open.v1.defect.dto.DefectSchemeDTO;
import com.yelink.dfs.open.v1.defect.dto.DefectSchemeInsertDTO;
import com.yelink.dfs.open.v1.defect.dto.DefectSchemeSelectDTO;
import com.yelink.dfs.open.v1.defect.dto.DefectSchemeUpdateDTO;
import com.yelink.dfs.open.v1.defect.dto.DefectStatementDTO;
import com.yelink.dfs.open.v1.defect.dto.DefectStatementSelectDTO;
import com.yelink.dfs.open.v1.defect.dto.TypeEnumInsertDTO;
import com.yelink.dfs.open.v1.defect.dto.TypeEnumUpdateDTO;
import com.yelink.dfs.open.v2.defect.dto.RecordWorkOrderUnqualifiedBatchDeleteDTO;
import com.yelink.dfs.service.defect.BaseTypeService;
import com.yelink.dfs.service.defect.DefectDefineService;
import com.yelink.dfs.service.defect.DefectSchemeService;
import com.yelink.dfs.service.order.RecordWorkOrderCountService;
import com.yelink.dfs.service.order.RecordWorkOrderUnqualifiedService;
import com.yelink.dfscommon.dto.dfs.DefectRecordUpdateDTO;
import com.yelink.dfscommon.pojo.PageResult;
import com.yelink.dfscommon.pojo.Result;
import com.yelink.dfscommon.pojo.message.KafkaMessageResult;
import com.yelink.dfscommon.pojo.message.NewMessageEntity;
import com.yelink.dfscommon.utils.JacksonUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.Extension;
import io.swagger.annotations.ExtensionProperty;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;


/**
 * <AUTHOR>
 */
@Api(tags = {"工作中心/V1/不良定义和工位质检接口"})
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/v1/open/defect")
public class DefectOpenController extends BaseController {
    private BaseTypeService baseTypeService;
    private DefectDefineService defineService;
    private DefectSchemeService schemeService;
    private RecordWorkOrderUnqualifiedService recordWorkOrderUnqualifiedService;
    private RecordWorkOrderCountService workOrderCountService;

    /**
     * #######################不良类型##########################
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "string", name = "name", value = "名称")
    })
    @ApiOperation(value = "查询不良类型列表", httpMethod = "GET")
    @GetMapping("/type/list")
    public Result<List<TypeEnumDTO>> getDefineTypes(@RequestParam(value = "name", required = false) String name) {
        List<TypeEnumDTO> list = baseTypeService.getTypeList(DefectTypeDictEnum.DEFECT_TYPE.getCode(), name);
        return Result.success(list);
    }


    @ApiOperation(value = "新增不良类型", httpMethod = "POST")
    @PostMapping("/type/add")
    public Result<?> addDefineType(@RequestBody @Validated TypeEnumInsertDTO dto) {
        baseTypeService.addType(DefectTypeDictEnum.DEFECT_TYPE.getCode(), dto.convertToEntity(), dto.getCreateByName());
        return Result.success();
    }


    @ApiOperation(value = "更新不良类型", httpMethod = "PUT")
    @PutMapping("/type/update")
    public Result<?> updateDefineType(@RequestBody @Validated TypeEnumUpdateDTO dto) {
        baseTypeService.updateType(DefectTypeDictEnum.DEFECT_TYPE.getCode(), dto.convertToEntity(), dto.getUpdateByName());
        return Result.success();
    }


    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "path", dataType = "string", name = "code", value = "类型code")
    })
    @ApiOperation(value = "删除不良类型", httpMethod = "DELETE")
    @DeleteMapping("/define/type/remove/{code}")
    public Result<?> removeDefineType(@PathVariable String code) {
        defineService.removeDefectTypeByCode(code);
        return Result.success();
    }


    /**
     * #####################不良定义#####################
     */
    @ApiOperation(value = "分页查询不良定义", httpMethod = "POST")
    @PostMapping("/define/page")
    public Result<PageResult<DefectDefineDTO>> getDefineList(@RequestBody DefectDefineSelectDTO dto) {
        String defectName = dto.getDefectName();
        String defectType = dto.getDefectType();
        String start = dto.getStart();
        String end = dto.getEnd();
        Integer current = dto.getCurrent();
        Integer size = dto.getSize();
        Page<DefectDefineEntity> page = defineService.getList(defectName, defectType, start, end, current, size, dto.getStates(), dto.getDescription(), null, null);
        return Result.covertPageSuccess(page, DefectDefineDTO::convertToDto);
    }


    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "path", dataType = "int", name = "id", value = "定义id")
    })
    @ApiOperation(value = "查询不良定义详情", httpMethod = "GET")
    @GetMapping("/define/detail/{id}")
    public Result<DefectDefineDTO> getDefineDetail(@PathVariable Integer id) {
        DefectDefineEntity entity = defineService.getDetailById(id);
        DefectDefineDTO dto = DefectDefineDTO.convertToDto(entity);
        return Result.success(dto);
    }

    @ApiOperation(value = "查询不良定义详情", httpMethod = "POST")
    @PostMapping("/define/detail")
    public Result<DefectDefineDTO> getDefineDetail(@RequestBody DefectDefineDetailDTO param) {
        DefectDefineEntity defectDefineEntity = defineService.lambdaQuery().eq(DefectDefineEntity::getDefectCode, param.getDefectCode()).one();
        if (Objects.isNull(defectDefineEntity)) {
            return Result.success();
        }
        DefectDefineEntity entity = defineService.getDetailById(defectDefineEntity.getDefectId());
        DefectDefineDTO dto = DefectDefineDTO.convertToDto(entity);
        return Result.success(dto);
    }

    @ApiOperation(value = "新增不良定义", httpMethod = "POST")
    @PostMapping("/define/add")
    public Result<?> addDefine(@RequestBody @Validated DefectDefineInsertDTO dto) {
        DefectDefineEntity entity = DefectDefineInsertDTO.convertToEntity(dto);
        entity.setCreateTime(new Date());
        defineService.addDefine(entity);
        return Result.success();
    }


    @ApiOperation(value = "更新不良定义", httpMethod = "PUT")
    @PutMapping("/define/update")
    public Result<?> updateDefine(@RequestBody @Validated DefectDefineUpdateDTO dto) {
        DefectDefineEntity entity = DefectDefineUpdateDTO.convertToEntity(dto);
        entity.setUpdateTime(new Date());
        defineService.updateDefine(entity);
        return Result.success();
    }


    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "path", dataType = "int", name = "id", value = "定义id")
    })
    @ApiOperation(value = "删除不良定义", httpMethod = "DELETE")
    @DeleteMapping("/define/remove/{id}")
    public Result<?> removeDefine(@PathVariable Integer id) {
        defineService.removeDefineById(id);
        return Result.success();
    }


    /**
     * #####################工位质检方案#####################
     */
    @ApiOperation(value = "分页查询工位质检方案", httpMethod = "POST")
    @PostMapping("/scheme/page")
    public Result<PageResult<DefectSchemeDTO>> getSchemeList(@RequestBody DefectSchemeSelectDTO dto) {
        Page<DefectSchemeEntity> page = schemeService.getList(dto);
        return Result.covertPageSuccess(page, DefectSchemeDTO::convertToDto);
    }


    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "path", dataType = "int", name = "id", value = "方案id")
    })
    @ApiOperation(value = "查询工位质检方案详情", httpMethod = "GET")
    @GetMapping("/scheme/detail/{id}")
    public Result<DefectSchemeDTO> getSchemeDetail(@PathVariable Integer id) {
        DefectSchemeEntity entity = schemeService.getSchemeDetail(id);
        DefectSchemeDTO dto = DefectSchemeDTO.convertToDto(entity);
        return Result.success(dto);
    }


    @ApiOperation(value = "新增工位质检方案", httpMethod = "POST")
    @PostMapping("/scheme/add")
    public Result<?> addScheme(@RequestBody @Validated DefectSchemeInsertDTO dto) {
        DefectSchemeEntity entity = dto.convertToEntity();
        entity.setCreateTime(new Date());
        schemeService.addScheme(entity);
        return Result.success();
    }


    @ApiOperation(value = "更新工位质检方案", httpMethod = "PUT")
    @PutMapping("/scheme/update")
    public Result<?> updateScheme(@RequestBody @Validated DefectSchemeUpdateDTO dto) {
        DefectSchemeEntity entity = dto.convertToEntity();
        entity.setUpdateTime(new Date());
        schemeService.updateScheme(entity);
        return Result.success();
    }


    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "path", dataType = "int", name = "id", value = "方案id")
    })
    @ApiOperation(value = "删除工位质检方案", httpMethod = "DELETE")
    @DeleteMapping("/scheme/remove/{id}")
    public Result<?> removeScheme(@PathVariable Integer id) {
        schemeService.removeSchemeById(id);
        return Result.success();
    }


    /**
     * ####################工位质检记录######################
     */
    @ApiOperation(value = "分页查询工位质检记录", httpMethod = "POST")
    @PostMapping("/record/page")
    public Result<PageResult<DefectRecordDTO>> getRecordList(@RequestBody DefectRecordSelectDTO dto) {
        List<Integer> fid = Stream.of(dto.getFid()).filter(Objects::nonNull).collect(Collectors.toList());
        List<String> defectType = Stream.of(dto.getDefectType()).filter(Objects::nonNull).collect(Collectors.toList());

        DefectRecordQueryDTO build = DefectRecordQueryDTO.builder()
                .workOrder(dto.getWorkOrder())
                .workOrderNumbers(dto.getWorkOrderNumbers())
                .fid(fid)
                .defectType(defectType)
                .start(dto.getStart())
                .end(dto.getEnd())
                .current(dto.getCurrent())
                .size(dto.getSize())
                .build();
        Page<RecordWorkOrderUnqualifiedEntity> page = recordWorkOrderUnqualifiedService.getList(build);
        return Result.covertPageSuccess(page, DefectRecordDTO::convertToDto);
    }

    /**
     * #####################新增质检记录#####################
     */
    @ApiOperation(value = "新增质检记录", httpMethod = "POST")
    @PostMapping("/record/add")
    public Result addRecord(@RequestBody com.yelink.dfscommon.dto.dfs.DefectRecordDTO dto) {
        String username = getUsername();
        recordWorkOrderUnqualifiedService.saveRecord(dto, username);
        return Result.success();
    }


    /**
     * 删除质检记录l
     */
    @ApiOperation(value = "删除质检记录", httpMethod = "POST")
    @DeleteMapping("/record/delete/{recordId}")
    public Result<?> deleteTask(@PathVariable Integer recordId) {
        recordWorkOrderUnqualifiedService.deleteRecord(recordId);
        return Result.success();
    }

    @ApiOperation(value = "更新工位质检记录", httpMethod = "POST")
    @PostMapping("/record/update")
    public Result updateRecord(@RequestBody List<DefectRecordUpdateDTO> dtos) {
        List<RecordWorkOrderUnqualifiedEntity> list = JacksonUtil.convertArray(dtos, RecordWorkOrderUnqualifiedEntity.class);
        recordWorkOrderUnqualifiedService.updateBatchById(list);
        return Result.success();
    }

    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "path", dataType = "int", name = "recordId", value = "记录id")
    })
    @ApiOperation(value = "查询工位质检记录详情", httpMethod = "GET")
    @GetMapping("/record/detail/{recordId}")
    public Result<DefectRecordDTO> getRecordDetail(@PathVariable Integer recordId) {
        RecordWorkOrderUnqualifiedEntity entity = recordWorkOrderUnqualifiedService.getDetail(recordId);
        DefectRecordDTO dto = DefectRecordDTO.convertToDto(entity);
        return Result.success(dto);
    }


    /**
     * #####################工位质检报表#####################
     */
    @ApiOperation(value = "分页查询工位质检报表", httpMethod = "POST")
    @PostMapping("/statement/page")
    public Result<PageResult<DefectStatementDTO>> getStatement(@RequestBody DefectStatementSelectDTO dto) {
        String workOrder = dto.getWorkOrder();
        String materialCode = dto.getMaterialCode();
        String factoryModel = dto.getFactoryModel();
        Integer lineId = dto.getLineId();
        String start = dto.getStart();
        String end = dto.getEnd();
        Integer current = dto.getCurrent();
        Integer size = dto.getSize();
        Page<RecordWorkOrderCountEntity> page = workOrderCountService.getQualityAnalysis(workOrder, materialCode, factoryModel, lineId, start, end, current, size);
        return Result.covertPageSuccess(page, DefectStatementDTO::convertToDto);
    }


    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "path", dataType = "int", name = "id", value = "报表id")
    })
    @ApiOperation(value = "查询工位质检报表详情", httpMethod = "GET")
    @GetMapping("/statement/detail/{id}")
    public Result<DefectStatementDTO> getStatementDetail(@PathVariable Integer id) {
        RecordWorkOrderCountEntity entity = workOrderCountService.getCountDetail(id);
        DefectStatementDTO dto = DefectStatementDTO.convertToDto(entity);
        return Result.success(dto);
    }

    /**
     * kafka消息说明（返回参数说明，非接口）
     *
     * @return
     */
    @ApiOperation(value = "kafka消息说明（返回参数说明，非接口）", notes = "topic:event_main_data\n" + "kafka消息method参数说明：\n" +
            "defectScheme.add：新增质检方案\n" +
            "defectScheme.update：修改质检方案\n" +
            "defectScheme.delete：删除质检方案",
            extensions = {
                    @Extension(properties = {
                            @ExtensionProperty(name = "topic", value = "event_main_data"),
                    }),
                    @Extension(name = "messageModelId", properties = {
                            @ExtensionProperty(name = "新增质检方案", value = "defectScheme.add"),
                            @ExtensionProperty(name = "修改质检方案", value = "defectScheme.update"),
                            @ExtensionProperty(name = "删除质检方案", value = "defectScheme.delete")
                    })
            })
    @GetMapping("/kafka/message")
    public KafkaMessageResult<NewMessageEntity<DefectSchemeEntity>> message() {
        return KafkaMessageResult.success();
    }

    /**
     * kafka消息说明（返回参数说明，非接口）
     *
     * @return
     */
    @ApiOperation(value = "kafka消息说明（返回参数说明，非接口）", notes = "topic:event_main_data\n" + "kafka消息method参数说明：\n" +
            "defectDefine.add：新增质检定义\n" +
            "defectDefine.update：修改质检定义\n" +
            "defectDefine.delete：删除质检定义",
            extensions = {
                    @Extension(properties = {
                            @ExtensionProperty(name = "topic", value = "event_main_data"),
                    }),
                    @Extension(name = "messageModelId", properties = {
                            @ExtensionProperty(name = "新增质检定义", value = "defectDefine.add"),
                            @ExtensionProperty(name = "修改质检定义", value = "defectDefine.update"),
                            @ExtensionProperty(name = "删除质检定义", value = "defectDefine.delete")
                    })
            })
    @GetMapping("/kafka/message2")
    public KafkaMessageResult<NewMessageEntity<DefectDefineEntity>> message2() {
        return KafkaMessageResult.success();
    }


}







