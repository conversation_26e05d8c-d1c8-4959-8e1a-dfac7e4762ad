package com.yelink.dfs.open.v2.craft.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 工序附件
 * <AUTHOR>
 */
@Data
@ToString
public class CraftProcedureFileVO {

    @ApiModelProperty("Id")
    private Integer id;


    @ApiModelProperty("工艺工序id")
    private Integer procedureId;


    @ApiModelProperty("工艺id")
    private Integer craftId;

    /**
     * 附件url
     */
    @ApiModelProperty("附件url")
    @TableField(value = "file_url")
    private String fileUrl;

    /**
     * 附件名称
     */
    @ApiModelProperty("附件名称")
    private String name;

    /**
     * 更新人
     */
    @ApiModelProperty("更新人")
    private String updateBy;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String createBy;


    @ApiModelProperty("工序文件上传人名称")
    private String createUserName;
    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;



}
